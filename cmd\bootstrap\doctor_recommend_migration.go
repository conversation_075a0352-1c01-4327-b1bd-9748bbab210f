package bootstrap

import (
	"log"

	"yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/infra/mysql"
)

// MigrateDoctorRecommendTables 执行医生推荐相关表结构迁移
func MigrateDoctorRecommendTables() error {
	log.Println("开始执行医生推荐表结构迁移...")

	// 执行医生推荐表结构迁移
	db := mysql.Master()

	// 迁移医生推荐表
	err := db.Set("gorm:table_options", "COMMENT='医生推荐表'").AutoMigrate(&doctor.DoctorRecommend{})
	if err != nil {
		log.Printf("医生推荐表迁移失败: %v", err)
		return err
	}

	log.Println("医生推荐表结构迁移完成")
	return nil
}
