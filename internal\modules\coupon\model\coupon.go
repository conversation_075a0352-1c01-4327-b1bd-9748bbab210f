package model

import (
	"time"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// CouponType 优惠券类型枚举
type CouponType int

const (
	CouponTypeDiscount CouponType = 1 // 折扣券
	CouponTypeCash     CouponType = 2 // 满减券
	CouponTypeFixed    CouponType = 3 // 立减券
)

// CouponStatus 优惠券状态枚举
type CouponStatus int

const (
	CouponStatusDisabled CouponStatus = 0 // 禁用
	CouponStatusActive   CouponStatus = 1 // 生效中
	CouponStatusExpired  CouponStatus = 2 // 已过期
)

// UserCouponStatus 用户优惠券状态枚举
type UserCouponStatus int

const (
	UserCouponStatusUnused  UserCouponStatus = 0 // 未使用
	UserCouponStatusUsed    UserCouponStatus = 1 // 已使用
	UserCouponStatusExpired UserCouponStatus = 2 // 已过期
)

// CouponScope 可用范围枚举
type CouponScope int

const (
	CouponScopeAll         CouponScope = 1 // 全站
	CouponScopeServices    CouponScope = 2 // 指定服务
	CouponScopeGoods       CouponScope = 3 // 指定商品
	CouponScopeAllServices CouponScope = 4 // 全部服务
	CouponScopeAllGoods    CouponScope = 5 // 全部商品
)

// ValidityType 有效期类型枚举
type ValidityType int

const (
	ValidityTypePermanent ValidityType = 1 // 永久有效
	ValidityTypeDays      ValidityType = 2 // 天数有效
	ValidityTypeDate      ValidityType = 3 // 固定日期
)

// UserLevel 用户可见范围枚举
type UserLevel int

const (
	UserLevelAll  UserLevel = 1 // 全部
	UserLevelPart UserLevel = 2 // 部分等级
)

// CouponUsageRestriction 使用限制枚举
type CouponUsageRestriction int

const (
	CouponUsageRestrictionMutualExclusive CouponUsageRestriction = 1 // 互斥（不可以与其他优惠券在同一个订单中叠加使用）
	CouponUsageRestrictionStackable       CouponUsageRestriction = 2 // 同享（可以与其他优惠券在同一个订单中叠加使用）
)

// Coupon 优惠券模型
type Coupon struct {
	ID          uint       `json:"id" gorm:"primaryKey;autoIncrement;comment:优惠券ID"`
	CouponCode  string     `json:"coupon_code" gorm:"type:varchar(50);uniqueIndex;not null;comment:优惠券编码（唯一）"`
	Name        string     `json:"name" gorm:"type:varchar(120);not null;comment:优惠券名称"`
	Type        CouponType `json:"type" gorm:"not null;comment:优惠券类型:1-折扣券,2-满减券,3-立减券"`
	Description string     `json:"description" gorm:"type:text;comment:使用说明"`

	// 优惠金额/折扣相关
	Amount    float64 `json:"amount" gorm:"type:decimal(10,2);default:0;comment:优惠金额(满减券/立减券使用)"`
	Discount  float64 `json:"discount" gorm:"type:decimal(5,2);default:0;comment:折扣比例(折扣券使用,如8.5表示8.5折)"`
	MinAmount float64 `json:"min_amount" gorm:"type:decimal(10,2);default:0;comment:最低使用金额门槛"`

	// 数量相关
	TotalQuantity    int `json:"total_quantity" gorm:"not null;comment:发放数量(1-10000)"`
	ReceivedQuantity int `json:"received_quantity" gorm:"default:0;comment:已领取数量"`

	// 可用范围
	Scope CouponScope `json:"scope" gorm:"not null;comment:可用范围:1-全站,2-指定服务,3-指定商品,4-全部服务,5-全部商品"`

	// 有效期相关
	ValidityType ValidityType `json:"validity_type" gorm:"not null;comment:有效期类型:1-永久有效,2-天数有效,3-固定日期"`
	ValidDays    int          `json:"valid_days" gorm:"default:0;comment:有效天数(1-9999)"`
	ValidFrom    *time.Time   `json:"valid_from" gorm:"comment:生效开始时间"`
	ValidUntil   *time.Time   `json:"valid_until" gorm:"comment:生效结束时间"`

	// 使用限制
	UsageRestriction CouponUsageRestriction `json:"usage_restriction" gorm:"default:1;comment:使用限制:1-互斥,2-同享"`

	// 用户可见范围
	UserLevel UserLevel `json:"user_level" gorm:"default:1;comment:用户可见范围:1-全部,2-部分等级"`

	// 状态
	Status CouponStatus `json:"status" gorm:"default:1;comment:状态:0-禁用,1-生效中,2-已过期"`

	// 时间戳
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (c *Coupon) TableName() string {
	return "coupons"
}

// UserCoupon 用户优惠券模型
type UserCoupon struct {
	ID       uint             `json:"id" gorm:"primaryKey;autoIncrement;comment:用户优惠券ID"`
	UserID   uint             `json:"user_id" gorm:"index;not null;comment:用户ID"`
	CouponID uint             `json:"coupon_id" gorm:"index;not null;comment:优惠券ID"`
	Status   UserCouponStatus `json:"status" gorm:"default:0;comment:状态:0-未使用,1-已使用,2-已过期"`

	// 有效期
	ValidFrom  time.Time `json:"valid_from" gorm:"not null;comment:生效时间"`
	ValidUntil time.Time `json:"valid_until" gorm:"not null;comment:过期时间"`

	// 使用信息
	UsedAt  *time.Time `json:"used_at" gorm:"comment:使用时间"`
	OrderID *uint      `json:"order_id" gorm:"comment:使用的订单ID"`

	// 时间戳
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;comment:领取时间"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`

	// 关联
	Coupon *Coupon `json:"coupon,omitempty" gorm:"foreignKey:CouponID"`
}

// TableName 返回表名
func (uc *UserCoupon) TableName() string {
	return "user_coupons"
}

// CouponApplicableProduct 优惠券适用商品中间表
type CouponApplicableProduct struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:关联ID"`
	CouponID  uint           `json:"coupon_id" gorm:"index;not null;comment:优惠券ID"`
	ProductID uint           `json:"product_id" gorm:"index;not null;comment:商品ID"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (cap *CouponApplicableProduct) TableName() string {
	return "coupon_applicable_products"
}

// CouponApplicableService 优惠券适用服务中间表
type CouponApplicableService struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:关联ID"`
	CouponID  uint           `json:"coupon_id" gorm:"index;not null;comment:优惠券ID"`
	ServiceID uint           `json:"service_id" gorm:"index;not null;comment:服务ID"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (cas *CouponApplicableService) TableName() string {
	return "coupon_applicable_services"
}

// CouponVisibleUserLevel 优惠券可见用户等级中间表
type CouponVisibleUserLevel struct {
	ID          uint           `gorm:"primaryKey;type:int unsigned;autoIncrement;comment:关联ID" json:"id"`
	CouponID    uint           `gorm:"type:int unsigned;not null;index;comment:优惠券ID" json:"coupon_id"`
	UserLevelID uint           `gorm:"type:int unsigned;not null;index;comment:用户等级ID" json:"user_level_id"`
	CreatedAt   time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index;comment:删除时间" json:"-"`
}

// TableName 返回表名
func (cvul *CouponVisibleUserLevel) TableName() string {
	return "coupon_visible_user_levels"
}

// CouponRepository 优惠券仓库接口
type CouponRepository interface {
	// 基础CRUD
	Create(coupon *Coupon) error
	Update(coupon *Coupon) error
	Delete(id uint) error
	FindByID(id uint) (*Coupon, error)
	FindByCouponCode(couponCode string) (*Coupon, error)
	List(page, size int, couponCode, name string, couponType *CouponType, status *CouponStatus, validFrom, validUntil *time.Time) ([]*Coupon, int64, error)

	// 状态管理
	UpdateStatus(id uint, status CouponStatus) error

	// 商品关联管理
	SetApplicableProducts(couponID uint, productIDs []uint) error
	GetApplicableProducts(couponID uint) ([]uint, error)
	ClearApplicableProducts(couponID uint) error

	// 服务关联管理
	SetApplicableServices(couponID uint, serviceIDs []uint) error
	GetApplicableServices(couponID uint) ([]uint, error)
	ClearApplicableServices(couponID uint) error

	// 用户等级可见性管理
	SetVisibleUserLevels(couponID uint, userLevelIDs []uint) error
	GetVisibleUserLevels(couponID uint) ([]uint, error)
	ClearVisibleUserLevels(couponID uint) error

	// 用户优惠券管理
	IssueCoupon(userID, couponID uint) (*UserCoupon, error)
	GetUserCoupons(userID uint, status *UserCouponStatus, page, size int) ([]*UserCoupon, int64, error)
	GetAvailableUserCoupons(userID uint, productIDs []uint, totalAmount float64) ([]*UserCoupon, error)
	UseCoupon(userCouponID, orderID uint) error

	// 统计
	IncrementReceivedQuantity(couponID uint) error
	GetCouponStats(couponID uint) (*CouponStats, error)

	// 过期处理
	UpdateExpiredStatus() error
}

// CouponStats 优惠券统计信息
type CouponStats struct {
	TotalQuantity    int `json:"total_quantity"`    // 总发放数量
	ReceivedQuantity int `json:"received_quantity"` // 已领取数量
	UsedQuantity     int `json:"used_quantity"`     // 已使用数量
	ExpiredQuantity  int `json:"expired_quantity"`  // 已过期数量
}

// couponRepository 优惠券仓库实现
type couponRepository struct {
	db *gorm.DB
}

// NewCouponRepository 创建优惠券仓库
func NewCouponRepository(db *gorm.DB) CouponRepository {
	if db == nil {
		db = mysql.GetDB()
	}
	return &couponRepository{
		db: db,
	}
}

// Create 创建优惠券
func (r *couponRepository) Create(coupon *Coupon) error {
	return r.db.Create(coupon).Error
}

// Update 更新优惠券
func (r *couponRepository) Update(coupon *Coupon) error {
	return r.db.Save(coupon).Error
}

// Delete 删除优惠券
func (r *couponRepository) Delete(id uint) error {
	tx := r.db.Begin()

	// 删除优惠券
	if err := tx.Delete(&Coupon{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除关联的商品
	if err := tx.Where("coupon_id = ?", id).Delete(&CouponApplicableProduct{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除关联的服务
	if err := tx.Where("coupon_id = ?", id).Delete(&CouponApplicableService{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除关联的用户等级
	if err := tx.Where("coupon_id = ?", id).Delete(&CouponVisibleUserLevel{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// FindByID 根据ID查找优惠券
func (r *couponRepository) FindByID(id uint) (*Coupon, error) {
	var coupon Coupon
	err := r.db.First(&coupon, id).Error
	if err != nil {
		return nil, err
	}
	return &coupon, nil
}

// FindByCouponCode 根据优惠券编码查找优惠券
func (r *couponRepository) FindByCouponCode(couponCode string) (*Coupon, error) {
	var coupon Coupon
	err := r.db.Where("coupon_code = ?", couponCode).First(&coupon).Error
	if err != nil {
		return nil, err
	}
	return &coupon, nil
}

// List 获取优惠券列表
func (r *couponRepository) List(page, size int, couponCode, name string, couponType *CouponType, status *CouponStatus, validFrom, validUntil *time.Time) ([]*Coupon, int64, error) {
	var coupons []*Coupon
	var total int64

	query := r.db.Model(&Coupon{})

	// 筛选条件
	if couponCode != "" {
		query = query.Where("coupon_code LIKE ?", "%"+couponCode+"%")
	}
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if couponType != nil {
		query = query.Where("type = ?", *couponType)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	if validFrom != nil {
		query = query.Where("valid_from >= ?", *validFrom)
	}
	if validUntil != nil {
		query = query.Where("valid_until <= ?", *validUntil)
	}

	// 获取总数
	query.Count(&total)

	// 分页查询
	offset := (page - 1) * size
	err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&coupons).Error

	return coupons, total, err
}

// UpdateStatus 更新优惠券状态
func (r *couponRepository) UpdateStatus(id uint, status CouponStatus) error {
	return r.db.Model(&Coupon{}).Where("id = ?", id).Update("status", status).Error
}

// SetApplicableProducts 设置适用商品
func (r *couponRepository) SetApplicableProducts(couponID uint, productIDs []uint) error {
	tx := r.db.Begin()

	// 先删除原有关联
	if err := tx.Where("coupon_id = ?", couponID).Delete(&CouponApplicableProduct{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 批量插入新关联
	for _, productID := range productIDs {
		relation := CouponApplicableProduct{
			CouponID:  couponID,
			ProductID: productID,
		}
		if err := tx.Create(&relation).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetApplicableProducts 获取适用商品ID列表
func (r *couponRepository) GetApplicableProducts(couponID uint) ([]uint, error) {
	var relations []CouponApplicableProduct
	err := r.db.Where("coupon_id = ?", couponID).Find(&relations).Error
	if err != nil {
		return nil, err
	}

	var productIDs []uint
	for _, relation := range relations {
		productIDs = append(productIDs, relation.ProductID)
	}

	return productIDs, nil
}

// ClearApplicableProducts 清除适用商品
func (r *couponRepository) ClearApplicableProducts(couponID uint) error {
	return r.db.Where("coupon_id = ?", couponID).Delete(&CouponApplicableProduct{}).Error
}

// SetApplicableServices 设置适用服务
func (r *couponRepository) SetApplicableServices(couponID uint, serviceIDs []uint) error {
	tx := r.db.Begin()

	// 先删除原有关联
	if err := tx.Where("coupon_id = ?", couponID).Delete(&CouponApplicableService{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 批量插入新关联
	for _, serviceID := range serviceIDs {
		relation := CouponApplicableService{
			CouponID:  couponID,
			ServiceID: serviceID,
		}
		if err := tx.Create(&relation).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetApplicableServices 获取适用服务ID列表
func (r *couponRepository) GetApplicableServices(couponID uint) ([]uint, error) {
	var relations []CouponApplicableService
	err := r.db.Where("coupon_id = ?", couponID).Find(&relations).Error
	if err != nil {
		return nil, err
	}

	var serviceIDs []uint
	for _, relation := range relations {
		serviceIDs = append(serviceIDs, relation.ServiceID)
	}

	return serviceIDs, nil
}

// ClearApplicableServices 清除适用服务
func (r *couponRepository) ClearApplicableServices(couponID uint) error {
	return r.db.Where("coupon_id = ?", couponID).Delete(&CouponApplicableService{}).Error
}

// SetVisibleUserLevels 设置可见用户等级
func (r *couponRepository) SetVisibleUserLevels(couponID uint, userLevelIDs []uint) error {
	tx := r.db.Begin()

	// 先删除原有关联
	if err := tx.Where("coupon_id = ?", couponID).Delete(&CouponVisibleUserLevel{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 批量插入新关联
	for _, userLevelID := range userLevelIDs {
		relation := CouponVisibleUserLevel{
			CouponID:    couponID,
			UserLevelID: userLevelID,
		}
		if err := tx.Create(&relation).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetVisibleUserLevels 获取可见用户等级ID列表
func (r *couponRepository) GetVisibleUserLevels(couponID uint) ([]uint, error) {
	var relations []CouponVisibleUserLevel
	err := r.db.Where("coupon_id = ?", couponID).Find(&relations).Error
	if err != nil {
		return nil, err
	}

	var userLevelIDs []uint
	for _, relation := range relations {
		userLevelIDs = append(userLevelIDs, relation.UserLevelID)
	}
	return userLevelIDs, nil
}

// ClearVisibleUserLevels 清除可见用户等级
func (r *couponRepository) ClearVisibleUserLevels(couponID uint) error {
	return r.db.Where("coupon_id = ?", couponID).Delete(&CouponVisibleUserLevel{}).Error
}

// IssueCoupon 发放优惠券给用户
func (r *couponRepository) IssueCoupon(userID, couponID uint) (*UserCoupon, error) {
	// 先检查优惠券是否存在且可用
	coupon, err := r.FindByID(couponID)
	if err != nil {
		return nil, err
	}

	if coupon.Status != CouponStatusActive {
		return nil, gorm.ErrRecordNotFound
	}

	// 检查是否还有库存
	if coupon.ReceivedQuantity >= coupon.TotalQuantity {
		return nil, gorm.ErrRecordNotFound
	}

	// 注意：移除了限领数量检查，用户可以多次领取同一优惠券

	tx := r.db.Begin()

	// 创建用户优惠券记录
	userCoupon := &UserCoupon{
		UserID:   userID,
		CouponID: couponID,
		Status:   UserCouponStatusUnused,
	}

	// 计算有效期
	now := time.Now()
	switch coupon.ValidityType {
	case ValidityTypePermanent:
		userCoupon.ValidFrom = now
		userCoupon.ValidUntil = now.AddDate(10, 0, 0) // 10年后
	case ValidityTypeDays:
		userCoupon.ValidFrom = now
		userCoupon.ValidUntil = now.AddDate(0, 0, coupon.ValidDays)
	case ValidityTypeDate:
		if coupon.ValidFrom != nil {
			userCoupon.ValidFrom = *coupon.ValidFrom
		} else {
			userCoupon.ValidFrom = now
		}
		if coupon.ValidUntil != nil {
			userCoupon.ValidUntil = *coupon.ValidUntil
		} else {
			userCoupon.ValidUntil = now.AddDate(1, 0, 0) // 默认1年
		}
	}

	if err := tx.Create(userCoupon).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 更新优惠券已领取数量
	if err := tx.Model(&Coupon{}).Where("id = ?", couponID).
		Update("received_quantity", gorm.Expr("received_quantity + 1")).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return userCoupon, nil
}

// GetUserCoupons 获取用户优惠券列表
func (r *couponRepository) GetUserCoupons(userID uint, status *UserCouponStatus, page, size int) ([]*UserCoupon, int64, error) {
	var userCoupons []*UserCoupon
	var total int64

	query := r.db.Model(&UserCoupon{}).Where("user_id = ?", userID)

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 获取总数
	query.Count(&total)

	// 分页查询，预加载优惠券信息
	offset := (page - 1) * size
	err := query.Preload("Coupon").Offset(offset).Limit(size).
		Order("created_at DESC").Find(&userCoupons).Error

	return userCoupons, total, err
}

// GetAvailableUserCoupons 获取用户可用的优惠券
func (r *couponRepository) GetAvailableUserCoupons(userID uint, productIDs []uint, totalAmount float64) ([]*UserCoupon, error) {
	var userCoupons []*UserCoupon
	now := time.Now()

	query := r.db.Model(&UserCoupon{}).
		Where("user_id = ? AND status = ? AND valid_from <= ? AND valid_until > ?",
			userID, UserCouponStatusUnused, now, now).
		Preload("Coupon")

	if err := query.Find(&userCoupons).Error; err != nil {
		return nil, err
	}

	// 过滤出可用的优惠券
	var availableCoupons []*UserCoupon
	for _, userCoupon := range userCoupons {
		if r.isCouponApplicable(userCoupon.Coupon, productIDs, totalAmount) {
			availableCoupons = append(availableCoupons, userCoupon)
		}
	}

	return availableCoupons, nil
}

// isCouponApplicable 检查优惠券是否适用于当前商品和金额
func (r *couponRepository) isCouponApplicable(coupon *Coupon, productIDs []uint, totalAmount float64) bool {
	if coupon == nil {
		return false
	}

	// 检查门槛金额
	if totalAmount < coupon.MinAmount {
		return false
	}

	// 检查适用范围
	switch coupon.Scope {
	case CouponScopeAll:
		return true
	case CouponScopeGoods:
		// 检查是否有适用的商品
		applicableProducts, err := r.GetApplicableProducts(coupon.ID)
		if err != nil {
			return false
		}

		// 检查购物车商品是否包含适用商品
		for _, productID := range productIDs {
			for _, applicableID := range applicableProducts {
				if productID == applicableID {
					return true
				}
			}
		}
		return false
	case CouponScopeServices:
		// TODO: 实现服务相关逻辑
		return false
	}

	return false
}

// UseCoupon 使用优惠券
func (r *couponRepository) UseCoupon(userCouponID, orderID uint) error {
	now := time.Now()
	return r.db.Model(&UserCoupon{}).Where("id = ?", userCouponID).
		Updates(map[string]interface{}{
			"status":   UserCouponStatusUsed,
			"used_at":  &now,
			"order_id": orderID,
		}).Error
}

// IncrementReceivedQuantity 增加已领取数量
func (r *couponRepository) IncrementReceivedQuantity(couponID uint) error {
	return r.db.Model(&Coupon{}).Where("id = ?", couponID).
		Update("received_quantity", gorm.Expr("received_quantity + 1")).Error
}

// GetCouponStats 获取优惠券统计信息
func (r *couponRepository) GetCouponStats(couponID uint) (*CouponStats, error) {
	var stats CouponStats

	// 获取基本信息
	var coupon Coupon
	if err := r.db.First(&coupon, couponID).Error; err != nil {
		return nil, err
	}

	stats.TotalQuantity = coupon.TotalQuantity
	stats.ReceivedQuantity = coupon.ReceivedQuantity

	// 统计已使用数量
	var usedCount int64
	r.db.Model(&UserCoupon{}).Where("coupon_id = ? AND status = ?", couponID, UserCouponStatusUsed).Count(&usedCount)
	stats.UsedQuantity = int(usedCount)

	// 统计已过期数量
	var expiredCount int64
	r.db.Model(&UserCoupon{}).Where("coupon_id = ? AND status = ?", couponID, UserCouponStatusExpired).Count(&expiredCount)
	stats.ExpiredQuantity = int(expiredCount)

	return &stats, nil
}

// UpdateExpiredStatus 更新过期状态
func (r *couponRepository) UpdateExpiredStatus() error {
	now := time.Now()

	// 更新优惠券状态
	r.db.Model(&Coupon{}).Where("status = ? AND valid_until IS NOT NULL AND valid_until < ?",
		CouponStatusActive, now).Update("status", CouponStatusExpired)

	// 更新用户优惠券状态
	r.db.Model(&UserCoupon{}).Where("status = ? AND valid_until < ?",
		UserCouponStatusUnused, now).Update("status", UserCouponStatusExpired)

	return nil
}
