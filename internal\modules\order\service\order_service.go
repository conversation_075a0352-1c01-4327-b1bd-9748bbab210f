package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	goodsModel "yekaitai/internal/modules/goods/model"
	goodsService "yekaitai/internal/modules/goods/service"
	cartModel "yekaitai/pkg/common/model/cart"
	orderModel "yekaitai/pkg/common/model/order"
	addressModel "yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type OrderService struct {
	db              *gorm.DB
	shippingService *goodsService.ShippingService
}

func NewOrderService() *OrderService {
	return &OrderService{
		db:              mysql.GetDB(),
		shippingService: goodsService.NewShippingService(),
	}
}

// CreateOrder 创建订单
func (s *OrderService) CreateOrder(ctx context.Context, userID uint, req *orderModel.CreateOrderRequest) (*orderModel.CreateOrderResponse, error) {
	orderResp, err := s.createOrderWithTx(ctx, userID, req)
	return orderResp, err
}

func (s *OrderService) createOrderWithTx(ctx context.Context, userID uint, req *orderModel.CreateOrderRequest) (*orderModel.CreateOrderResponse, error) {
	var orderResp *orderModel.CreateOrderResponse

	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 获取购物车商品
		var cartItems []cartModel.Cart
		if err := tx.Where("user_id = ? AND id IN ?", userID, req.CartIDs).Find(&cartItems).Error; err != nil {
			return fmt.Errorf("获取购物车商品失败: %w", err)
		}

		if len(cartItems) == 0 {
			return fmt.Errorf("购物车商品不存在")
		}

		// 2. 获取收货地址
		var address addressModel.Address
		if err := tx.Where("user_id = ? AND id = ?", userID, req.AddressID).First(&address).Error; err != nil {
			return fmt.Errorf("收货地址不存在")
		}

		// 3. 计算订单金额
		var totalAmount float64
		orderItems := make([]orderModel.OrderItem, 0, len(cartItems))

		for _, cart := range cartItems {
			// 获取商品信息验证价格
			var goods struct {
				ID            uint    `json:"id"`
				GoodsName     string  `json:"goods_name"`
				TagPrice      float64 `json:"tag_price"`
				Pic           string  `json:"pic"`
				StockQuantity int     `json:"stock_quantity"`
			}
			if err := tx.Table("goods").Where("id = ?", cart.GoodsID).First(&goods).Error; err != nil {
				return fmt.Errorf("商品信息获取失败")
			}

			// 检查库存
			if goods.StockQuantity < cart.Quantity {
				return fmt.Errorf("商品[%s]库存不足", goods.GoodsName)
			}

			// 获取最终价格 - 优先使用规格价格，否则使用商品吊牌价
			var finalPrice float64 = goods.TagPrice
			var specName string
			if cart.SpecID > 0 {
				var spec struct {
					SpecName  string  `json:"spec_name"`
					SalePrice float64 `json:"sale_price"`
				}
				if err := tx.Table("goods_spec").Where("id = ?", cart.SpecID).First(&spec).Error; err == nil {
					specName = spec.SpecName
					finalPrice = spec.SalePrice // 使用规格的标准售价
				}
			}

			itemTotal := finalPrice * float64(cart.Quantity)
			totalAmount += itemTotal

			orderItems = append(orderItems, orderModel.OrderItem{
				GoodsID:    cart.GoodsID,
				GoodsName:  goods.GoodsName,
				GoodsPic:   goods.Pic,
				SpecName:   specName,
				Price:      finalPrice,
				Quantity:   cart.Quantity,
				TotalPrice: itemTotal,
			})
		}

		// 4. 计算运费
		shippingReq := &goodsModel.ShippingCalculateRequest{
			TotalAmount: totalAmount,
		}
		shippingResp, err := s.shippingService.CalculateShippingFee(ctx, shippingReq)
		if err != nil {
			return fmt.Errorf("计算运费失败: %w", err)
		}

		// 5. 计算实付金额
		payAmount := totalAmount + shippingResp.ShippingFee

		// 6. 查询地址的地区名称
		var regions []struct {
			Code string `gorm:"column:code"`
			Name string `gorm:"column:name"`
		}
		codes := []string{address.ProvinceCode, address.CityCode, address.DistrictCode}
		nameMap := make(map[string]string)

		if err := tx.Raw("SELECT code, name FROM t_regions WHERE code IN (?)", codes).Scan(&regions).Error; err == nil {
			for _, region := range regions {
				nameMap[region.Code] = region.Name
			}
		}

		// 构造收货地址JSON
		shippingAddr := orderModel.ShippingAddress{
			Name:      address.Name,
			Phone:     address.Phone,
			Province:  nameMap[address.ProvinceCode],
			City:      nameMap[address.CityCode],
			District:  nameMap[address.DistrictCode],
			Address:   address.Address,
			IsDefault: address.IsDefault == 1,
		}
		shippingAddrJSON, _ := json.Marshal(shippingAddr)

		// 7. 生成订单号
		orderNo := s.generateOrderNo()

		// 8. 创建订单
		order := &orderModel.Order{
			OrderNo:        orderNo,
			UserID:         userID,
			Status:         1, // 待付款
			PaymentStatus:  0, // 未付款
			ShippingStatus: 0, // 未发货
			TotalAmount:    totalAmount,
			ShippingFee:    shippingResp.ShippingFee,
			PayAmount:      payAmount,
			PaymentMethod:  1, // 默认微信支付
			Remark:         req.Remark,
			AddressInfo:    string(shippingAddrJSON),
		}

		if err := tx.Create(order).Error; err != nil {
			return fmt.Errorf("创建订单失败: %w", err)
		}

		// 9. 创建订单商品
		for i := range orderItems {
			orderItems[i].OrderID = order.ID
		}
		if err := tx.Create(&orderItems).Error; err != nil {
			return fmt.Errorf("创建订单商品失败: %w", err)
		}

		// 10. 减少商品库存
		for _, cart := range cartItems {
			if err := tx.Table("goods").Where("id = ?", cart.GoodsID).
				Update("stock_quantity", gorm.Expr("stock_quantity - ?", cart.Quantity)).Error; err != nil {
				return fmt.Errorf("更新商品库存失败: %w", err)
			}
		}

		// 11. 删除购物车商品
		if err := tx.Where("user_id = ? AND id IN ?", userID, req.CartIDs).Delete(&cartModel.Cart{}).Error; err != nil {
			return fmt.Errorf("清理购物车失败: %w", err)
		}

		// 12. 构造返回结果
		order.OrderItems = orderItems
		orderResp = &orderModel.CreateOrderResponse{
			OrderID:     order.ID,
			OrderNo:     order.OrderNo,
			TotalAmount: order.TotalAmount,
			PayAmount:   order.PayAmount,
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	logx.Infof("创建订单成功: %s", orderResp.OrderNo)
	return orderResp, nil
}

// UpdateOrderStatus 更新订单状态
func (s *OrderService) UpdateOrderStatus(ctx context.Context, orderID uint, req *orderModel.OrderUpdateRequest) (*orderModel.OrderResponse, error) {
	var order orderModel.Order
	if err := s.db.WithContext(ctx).First(&order, orderID).Error; err != nil {
		return nil, fmt.Errorf("订单不存在")
	}

	now := time.Now()
	updates := map[string]interface{}{
		"status": req.Status,
		"remark": req.Remark,
	}

	// 根据状态更新相应时间
	switch req.Status {
	case 2: // 已付款
		updates["payment_status"] = 1
		updates["payment_time"] = &now
	case 3: // 已发货
		updates["shipping_status"] = 1
		updates["shipping_time"] = &now
	case 4: // 已完成
		updates["receive_time"] = &now
	case 5: // 已取消
		updates["cancel_time"] = &now
		updates["cancel_reason"] = req.CancelReason
	case 6: // 已退款
		updates["payment_status"] = 2
	}

	if err := s.db.WithContext(ctx).Model(&order).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("更新订单状态失败: %w", err)
	}

	return s.GetOrderByID(ctx, orderID)
}

// GetOrderByID 获取订单详情
func (s *OrderService) GetOrderByID(ctx context.Context, orderID uint) (*orderModel.OrderResponse, error) {
	var order orderModel.Order
	if err := s.db.WithContext(ctx).Preload("OrderItems").First(&order, orderID).Error; err != nil {
		return nil, fmt.Errorf("订单不存在")
	}

	var shippingAddr orderModel.ShippingAddress
	json.Unmarshal([]byte(order.AddressInfo), &shippingAddr)

	return s.buildOrderResponse(&order, &shippingAddr), nil
}

// ListOrders 获取订单列表
func (s *OrderService) ListOrders(ctx context.Context, params *orderModel.OrderQueryParams) ([]*orderModel.OrderResponse, int64, error) {
	query := s.db.WithContext(ctx).Model(&orderModel.Order{})

	// 构建查询条件
	if params.UserID != nil {
		query = query.Where("user_id = ?", *params.UserID)
	}
	if params.Status != nil {
		query = query.Where("status = ?", *params.Status)
	}
	if params.PaymentStatus != nil {
		query = query.Where("payment_status = ?", *params.PaymentStatus)
	}
	if params.OrderNo != "" {
		query = query.Where("order_no LIKE ?", "%"+params.OrderNo+"%")
	}
	if params.StartDate != "" {
		query = query.Where("created_at >= ?", params.StartDate)
	}
	if params.EndDate != "" {
		query = query.Where("created_at <= ?", params.EndDate)
	}

	// 统计总数
	var total int64
	query.Count(&total)

	// 分页查询
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	var orders []*orderModel.Order
	if err := query.Preload("OrderItems").Order("created_at DESC").Find(&orders).Error; err != nil {
		return nil, 0, fmt.Errorf("查询订单列表失败: %w", err)
	}

	// 转换为响应格式
	responses := make([]*orderModel.OrderResponse, 0, len(orders))
	for _, order := range orders {
		var shippingAddr orderModel.ShippingAddress
		json.Unmarshal([]byte(order.AddressInfo), &shippingAddr)
		responses = append(responses, s.buildOrderResponse(order, &shippingAddr))
	}

	return responses, total, nil
}

// GetOrderStatistics 获取订单统计
func (s *OrderService) GetOrderStatistics(ctx context.Context) (*orderModel.OrderStatistics, error) {
	stats := &orderModel.OrderStatistics{}

	// 总订单数
	s.db.WithContext(ctx).Model(&orderModel.Order{}).Count(&stats.TotalOrders)

	// 各状态订单数
	s.db.WithContext(ctx).Model(&orderModel.Order{}).Where("status = ?", 1).Count(&stats.PendingOrders)
	s.db.WithContext(ctx).Model(&orderModel.Order{}).Where("status = ?", 2).Count(&stats.PaidOrders)
	s.db.WithContext(ctx).Model(&orderModel.Order{}).Where("status = ?", 3).Count(&stats.ShippedOrders)
	s.db.WithContext(ctx).Model(&orderModel.Order{}).Where("status = ?", 4).Count(&stats.CompletedOrders)
	s.db.WithContext(ctx).Model(&orderModel.Order{}).Where("status = ?", 5).Count(&stats.CancelledOrders)

	// 总销售额
	s.db.WithContext(ctx).Model(&orderModel.Order{}).Where("payment_status = ?", 1).
		Select("COALESCE(SUM(pay_amount), 0)").Scan(&stats.TotalSales)

	// 今日销售额
	today := time.Now().Format("2006-01-02")
	s.db.WithContext(ctx).Model(&orderModel.Order{}).
		Where("payment_status = ? AND DATE(payment_time) = ?", 1, today).
		Select("COALESCE(SUM(pay_amount), 0)").Scan(&stats.TodaySales)

	return stats, nil
}

// CancelOrder 取消订单
func (s *OrderService) CancelOrder(ctx context.Context, userID, orderID uint, reason string) error {
	var order orderModel.Order
	if err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", orderID, userID).First(&order).Error; err != nil {
		return fmt.Errorf("订单不存在")
	}

	if order.Status != 1 {
		return fmt.Errorf("只能取消待付款订单")
	}

	now := time.Now()
	updates := map[string]interface{}{
		"status":        5,
		"cancel_time":   &now,
		"cancel_reason": reason,
	}

	if err := s.db.WithContext(ctx).Model(&order).Updates(updates).Error; err != nil {
		return fmt.Errorf("取消订单失败: %w", err)
	}

	return nil
}

// generateOrderNo 生成订单号
func (s *OrderService) generateOrderNo() string {
	return fmt.Sprintf("YKT%s%s", time.Now().Format("20060102150405"), utils.GenerateRandomString(6))
}

// buildOrderResponse 构建订单响应
func (s *OrderService) buildOrderResponse(order *orderModel.Order, shippingAddr *orderModel.ShippingAddress) *orderModel.OrderResponse {
	return &orderModel.OrderResponse{
		ID:                 order.ID,
		OrderNo:            order.OrderNo,
		UserID:             order.UserID,
		Status:             order.Status,
		StatusText:         s.getStatusText(order.Status),
		PaymentStatus:      order.PaymentStatus,
		PaymentStatusText:  s.getPaymentStatusText(order.PaymentStatus),
		ShippingStatus:     order.ShippingStatus,
		ShippingStatusText: s.getShippingStatusText(order.ShippingStatus),
		TotalAmount:        order.TotalAmount,
		ShippingFee:        order.ShippingFee,
		DiscountAmount:     order.DiscountAmount,
		PayAmount:          order.PayAmount,
		PaymentMethod:      order.PaymentMethod,
		PaymentMethodText:  s.getPaymentMethodText(order.PaymentMethod),
		PaymentTime:        order.PaymentTime,
		ShippingTime:       order.ShippingTime,
		ReceiveTime:        order.ReceiveTime,
		CancelTime:         order.CancelTime,
		CancelReason:       order.CancelReason,
		Remark:             order.Remark,
		ShippingAddress:    shippingAddr,
		OrderItems:         order.OrderItems,
		CreatedAt:          order.CreatedAt,
		UpdatedAt:          order.UpdatedAt,
	}
}

// getStatusText 获取状态文本
func (s *OrderService) getStatusText(status int) string {
	switch status {
	case 1:
		return "待付款"
	case 2:
		return "已付款"
	case 3:
		return "已发货"
	case 4:
		return "已完成"
	case 5:
		return "已取消"
	case 6:
		return "已退款"
	default:
		return "未知状态"
	}
}

// getPaymentStatusText 获取支付状态文本
func (s *OrderService) getPaymentStatusText(status int) string {
	switch status {
	case 0:
		return "未付款"
	case 1:
		return "已付款"
	case 2:
		return "已退款"
	default:
		return "未知状态"
	}
}

// getShippingStatusText 获取配送状态文本
func (s *OrderService) getShippingStatusText(status int) string {
	switch status {
	case 0:
		return "未发货"
	case 1:
		return "已发货"
	case 2:
		return "已送达"
	default:
		return "未知状态"
	}
}

// getPaymentMethodText 获取支付方式文本
func (s *OrderService) getPaymentMethodText(method int) string {
	switch method {
	case 1:
		return "微信支付"
	case 2:
		return "支付宝"
	case 3:
		return "现金支付"
	default:
		return "未知方式"
	}
}
