package model

import (
	"time"

	"gorm.io/gorm"
)

// Verifier 核销用户模型
type Verifier struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
	UserID    uint           `gorm:"index;uniqueIndex" json:"user_id"` // 关联的用户ID
	Name      string         `gorm:"size:50" json:"name"`              // 核销员姓名
	Phone     string         `gorm:"size:20" json:"phone"`             // 联系电话
	ShopID    uint           `gorm:"index" json:"shop_id"`             // 所属门店ID
	Position  string         `gorm:"size:50" json:"position"`          // 职位
	Status    int            `gorm:"default:1" json:"status"`          // 状态 1:正常 0:禁用
}

// TableName 设置表名
func (Verifier) TableName() string {
	return "verifiers"
}

// VerifierRepository 核销用户仓库接口
type VerifierRepository interface {
	Create(verifier *Verifier) error
	Update(verifier *Verifier) error
	Delete(id uint) error
	FindByID(id uint) (*Verifier, error)
	FindByUserID(userID uint) (*Verifier, error)
	List(page, size int) ([]*Verifier, int64, error)
	ListByShop(shopID uint, page, size int) ([]*Verifier, int64, error)
	UpdateStatus(id uint, status int) error
}

// verifierRepository 核销用户仓库实现
type verifierRepository struct{}

// NewVerifierRepository 创建核销用户仓库
func NewVerifierRepository() VerifierRepository {
	return &verifierRepository{}
}
