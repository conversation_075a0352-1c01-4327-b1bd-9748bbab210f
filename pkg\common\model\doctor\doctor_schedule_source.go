package doctor

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// SourceRecord 号源记录JSON对象
type SourceRecord struct {
	HyjlID int    `json:"hyjlid"` // 号源记录ID
	HyXH   int    `json:"hyxh"`   // 号源序号
	HySJ   string `json:"hysj"`   // 号源时间
	HyRQ   string `json:"hyrq"`   // 号源日期
	HyZT   int    `json:"hyzt"`   // 号源状态 0:未预约 1:已预约 2:已取号 3:已停诊
}

// SourceRecords 号源记录JSON数组
type SourceRecords []SourceRecord

// Scan 实现 sql.Scanner 接口
func (s *SourceRecords) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("类型断言为[]byte失败")
	}
	return json.Unmarshal(bytes, s)
}

// Value 实现 driver.Valuer 接口
func (s SourceRecords) Value() (driver.Value, error) {
	if len(s) == 0 {
		return "[]", nil
	}
	return json.Marshal(s)
}

// DoctorScheduleSource 医生排班号源信息（用于解析外部API数据）
type DoctorScheduleSource struct {
	YspbID       int           `json:"yspbid"`        // 医生排班ID
	JgksID       int           `json:"jgksid"`        // 机构科室ID
	JgksMC       string        `json:"jgksmc"`        // 机构科室名称
	YsID         int           `json:"ysid"`          // 医生ID
	YsMC         string        `json:"ysmc"`          // 医生名称
	XqXH         int           `json:"xqxh"`          // 星期序号 1:周一 2:周二 3:周三 4:周四 5:周五 6:周六 7:周日
	PbLx         int           `json:"pblx"`          // 排班类型 0:普通 1:专家 2:急诊
	KsSJ         string        `json:"kssj"`          // 开始时间
	JsSJ         string        `json:"jssj"`          // 结束时间
	XhSL         int           `json:"xhsl"`          // 限号数量
	SJD          int           `json:"sjd"`           // 时间段 1:上午 2:下午
	GhF          float64       `json:"ghf"`           // 挂号费
	GhFID        int           `json:"ghfid"`         // 挂号费ID
	ZlF          float64       `json:"zlf"`           // 诊疗费
	HyJL         SourceRecords `json:"hyjl"`          // 号源记录JSON
	WsjgID       int           `json:"wsjgid"`        // 卫生机构ID
	ScheduleDate string        `json:"schedule_date"` // 排班日期
}
