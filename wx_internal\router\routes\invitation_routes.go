package routes

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/invitation/handler"
	"yekaitai/wx_internal/svc"
)

// RegisterInvitationRoutes 注册邀请相关路由
func RegisterInvitationRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	routes := []rest.Route{
		// 获取邀请中心数据
		{
			Method:  "GET",
			Path:    "/api/wx/invitation/center",
			Handler: wxAuthWrapper(handler.InvitationCenterHandler(serverCtx)),
		},
		// 创建邀请
		{
			Method:  "POST",
			Path:    "/api/wx/invitation/create",
			Handler: wxAuthWrapper(handler.CreateInvitationHandler(serverCtx)),
		},
		// 接受邀请
		{
			Method:  "POST",
			Path:    "/api/wx/invitation/accept",
			Handler: wxAuthWrapper(handler.AcceptInvitationHandler(serverCtx)),
		},
		// 获取邀请统计
		{
			Method:  "GET",
			Path:    "/api/wx/invitation/stats",
			Handler: wxAuthWrapper(handler.InvitationStatsHandler(serverCtx)),
		},
		// 获取邀请奖励规则
		{
			Method:  "GET",
			Path:    "/api/wx/invitation/rules",
			Handler: wxAuthWrapper(handler.InvitationRulesHandler(serverCtx)),
		},
	}

	server.AddRoutes(routes)
}
