package abcyun

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// SyncService 是ABC云同步服务
type SyncService struct {
	client  *AbcYunClient
	running bool
}

// NewSyncService 创建同步服务
func NewSyncService(client *AbcYunClient) *SyncService {
	return &SyncService{
		client:  client,
		running: false,
	}
}

// StartSync 启动同步服务
func (s *SyncService) StartSync() {
	if s.running {
		logx.Info("同步服务已经在运行中")
		return
	}

	s.running = true
	go s.syncLoop()
}

// StopSync 停止同步服务
func (s *SyncService) StopSync() {
	s.running = false
}

// syncLoop 同步循环
func (s *SyncService) syncLoop() {
	// 立即执行一次同步
	s.syncAll()

	// 每天凌晨2点同步一次
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		if !s.running {
			return
		}

		now := time.Now()
		if now.Hour() == 2 && now.Minute() < 10 {
			s.syncAll()
		}
	}
}

// syncAll 同步所有数据
func (s *SyncService) syncAll() {
	logx.Info("开始同步ABC云数据")

	// 同步医生数据
	if err := s.syncDoctors(); err != nil {
		logx.Errorf("同步医生数据失败: %v", err)
	}

	// 同步科室数据
	if err := s.syncDepartments(); err != nil {
		logx.Errorf("同步科室数据失败: %v", err)
	}

	logx.Info("ABC云数据同步完成")
}

// syncDoctors 同步医生数据
func (s *SyncService) syncDoctors() error {
	limit := 20
	offset := 0

	for {
		// 获取医生列表
		doctorResp, err := s.client.Get("/api/v2/open-agency/doctor/query-list", map[string]string{
			"limit":  strconv.Itoa(limit),
			"offset": strconv.Itoa(offset),
		})
		if err != nil {
			return fmt.Errorf("获取医生列表失败: %w", err)
		}

		// 解析医生列表
		var respObj map[string]interface{}
		if err := json.Unmarshal(doctorResp, &respObj); err != nil {
			return fmt.Errorf("解析医生列表响应失败: %w", err)
		}

		// 提取数据部分
		data, ok := respObj["data"].(map[string]interface{})
		if !ok {
			return fmt.Errorf("无效的医生列表响应")
		}

		// 获取行数据
		rows, ok := data["rows"].([]interface{})
		if !ok {
			return fmt.Errorf("无效的医生列表数据")
		}

		// 如果没有数据，退出循环
		if len(rows) == 0 {
			break
		}

		// 处理医生数据
		for _, row := range rows {
			doctor, ok := row.(map[string]interface{})
			if !ok {
				continue
			}

			// TODO: 保存医生数据到数据库或其他存储
			id, _ := doctor["id"].(string)
			name, _ := doctor["name"].(string)
			mobile, _ := doctor["mobile"].(string)

			logx.Infof("同步医生数据 - id: %s, name: %s, mobile: %s", id, name, mobile)
		}

		// 更新偏移量
		offset += limit

		// 检查是否有更多数据
		total, _ := data["total"].(float64)
		if offset >= int(total) {
			break
		}
	}

	return nil
}

// syncDepartments 同步科室数据
func (s *SyncService) syncDepartments() error {
	// 获取科室列表
	resp, err := s.client.Get("/api/v2/open-agency/clinics/departments", nil)
	if err != nil {
		return fmt.Errorf("获取科室列表失败: %w", err)
	}

	// 解析科室列表
	var respObj map[string]interface{}
	if err := json.Unmarshal(resp, &respObj); err != nil {
		return fmt.Errorf("解析科室列表响应失败: %w", err)
	}

	// 提取数据部分
	data, ok := respObj["data"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("无效的科室列表响应")
	}

	// 获取科室列表
	departmentList, ok := data["departmentList"].([]interface{})
	if !ok {
		return fmt.Errorf("无效的科室列表数据")
	}

	// 处理科室数据
	for _, item := range departmentList {
		department, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		id, _ := department["id"].(string)
		name, _ := department["name"].(string)

		// 同步科室详情
		if err := s.syncDepartmentDetail(id); err != nil {
			logx.Errorf("同步科室详情失败 - id: %s, name: %s, error: %v", id, name, err)
			continue
		}

		logx.Infof("同步科室数据 - id: %s, name: %s", id, name)
	}

	return nil
}

// syncDepartmentDetail 同步科室详情
func (s *SyncService) syncDepartmentDetail(departmentID string) error {
	// 获取科室详情
	path := fmt.Sprintf("/api/v2/open-agency/clinics/departments/%s", departmentID)
	resp, err := s.client.Get(path, nil)
	if err != nil {
		return fmt.Errorf("获取科室详情失败: %w", err)
	}

	// 解析科室详情
	var respObj map[string]interface{}
	if err := json.Unmarshal(resp, &respObj); err != nil {
		return fmt.Errorf("解析科室详情响应失败: %w", err)
	}

	// 提取数据部分
	data, ok := respObj["data"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("无效的科室详情响应")
	}

	// 处理科室详情
	id, _ := data["id"].(string)
	name, _ := data["name"].(string)
	principal, _ := data["principal"].(string)
	phone, _ := data["phone"].(string)

	logx.Infof("同步科室详情 - id: %s, name: %s, principal: %s, phone: %s", id, name, principal, phone)

	// 处理科室员工
	employees, ok := data["employees"].([]interface{})
	if ok {
		for _, item := range employees {
			employee, ok := item.(map[string]interface{})
			if !ok {
				continue
			}

			employeeID, _ := employee["employeeId"].(string)
			empName, _ := employee["name"].(string)
			mobile, _ := employee["mobile"].(string)

			logx.Infof("同步科室员工 - departmentID: %s, employeeID: %s, name: %s, mobile: %s", id, employeeID, empName, mobile)
		}
	}

	return nil
}
