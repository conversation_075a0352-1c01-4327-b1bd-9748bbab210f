package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	cartModel "yekaitai/pkg/common/model/cart"
	checkoutModel "yekaitai/pkg/common/model/checkout"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type CheckoutService struct {
	db *gorm.DB
}

func NewCheckoutService() *CheckoutService {
	return &CheckoutService{
		db: mysql.GetDB(),
	}
}

// Preload 预加载结算数据
func (s *CheckoutService) Preload(ctx context.Context, userID uint, req *checkoutModel.PreloadRequest) (*checkoutModel.PreloadResponse, error) {
	// 1. 获取购物车商品信息
	cartItems, err := s.getCartItems(ctx, userID, req.CartItems)
	if err != nil {
		return nil, err
	}

	if len(cartItems) == 0 {
		return nil, fmt.Errorf("购物车为空")
	}

	// 2. 转换为商品信息
	products := s.convertToProducts(cartItems)

	// 3. 计算商品总金额
	productAmount := s.calculateProductAmount(cartItems)

	// 4. 获取运费配置
	shippingConfig, err := s.getShippingConfig()
	if err != nil {
		return nil, err
	}

	// 5. 获取可用优惠券
	availableCoupons, err := s.getAvailableCoupons(ctx, userID, productAmount)
	if err != nil {
		logx.Errorf("获取可用优惠券失败: %v", err)
		availableCoupons = []checkoutModel.Coupon{}
	}

	// 6. 获取积分政策
	pointsPolicy, err := s.getPointsPolicy(ctx, userID, productAmount)
	if err != nil {
		logx.Errorf("获取积分政策失败: %v", err)
		pointsPolicy = checkoutModel.PointsPolicy{}
	}

	// 7. 获取用户会员等级折扣
	memberDiscount, err := s.getMemberDiscount(ctx, userID)
	if err != nil {
		logx.Errorf("获取会员等级折扣失败: %v", err)
		memberDiscount = 0 // 无折扣
	}

	// 8. 获取用户可用积分数
	availablePoints, err := s.getUserAvailablePoints(ctx, userID)
	if err != nil {
		logx.Errorf("获取用户可用积分失败: %v", err)
		availablePoints = 0
	}

	// 9. 计算等级优惠金额
	memberDiscountAmount := productAmount * memberDiscount

	// 10. 计算最优方案预览（包含会员折扣）
	preview := s.calculateOptimalPreview(productAmount, shippingConfig, availableCoupons, pointsPolicy, memberDiscount)

	// 11. 判断积分开关状态（需要考虑等级优惠和优惠券后的剩余金额）
	afterMemberDiscount := productAmount - memberDiscountAmount
	afterCouponAmount := afterMemberDiscount - preview.CouponDeduct
	usePointsFlag := availablePoints > 0 && afterCouponAmount > 0

	return &checkoutModel.PreloadResponse{
		Products:           products,
		DefaultShippingFee: shippingConfig.DefaultFee,
		ShippingRules:      shippingConfig.Rules,
		AvailableCoupons:   availableCoupons,
		PointsPolicy:       pointsPolicy,
		Preview:            preview,
		AvailablePoints:    availablePoints,      // 可用积分数
		MemberDiscount:     memberDiscountAmount, // 等级优惠金额
		UsePoints:          usePointsFlag,        // 积分开关状态
	}, nil
}

// Recalculate 动态重新计算
func (s *CheckoutService) Recalculate(ctx context.Context, userID uint, req *checkoutModel.RecalculateRequest) (*checkoutModel.RecalculateResponse, error) {
	// 1. 获取购物车商品信息
	cartItems, err := s.getCartItems(ctx, userID, req.CartItems)
	if err != nil {
		return nil, err
	}

	if len(cartItems) == 0 {
		return &checkoutModel.RecalculateResponse{
			Valid: false,
		}, fmt.Errorf("购物车为空")
	}

	// 2. 计算商品总金额
	productAmount := s.calculateProductAmount(cartItems)

	// 3. 获取用户会员等级折扣
	memberDiscount, err := s.getMemberDiscount(ctx, userID)
	if err != nil {
		logx.Errorf("获取会员等级折扣失败: %v", err)
		memberDiscount = 0 // 无折扣
	}

	// 4. 应用会员折扣
	memberDiscountAmount := productAmount * memberDiscount
	afterMemberDiscount := productAmount - memberDiscountAmount

	// 5. 验证并计算优惠券抵扣（基于会员折扣后的金额）
	couponDeduct, couponValid, err := s.calculateMultipleCouponsDeduct(ctx, userID, req.CouponIDs, afterMemberDiscount)
	if err != nil {
		logx.Errorf("计算优惠券抵扣失败: %v", err)
		couponValid = false
	}

	// 6. 计算运费（基于会员折扣和优惠券后的金额，积分抵扣前）
	afterCouponAmount := afterMemberDiscount - couponDeduct
	shippingFee, err := s.calculateShippingFee(afterCouponAmount)
	if err != nil {
		return nil, err
	}

	// 7. 验证并计算积分抵扣（基于会员折扣和优惠券后的金额，不包含运费）
	pointsDeduct, pointsValid, err := s.calculatePointsDeductByFlag(ctx, userID, req.UsePoints, afterCouponAmount)
	if err != nil {
		logx.Errorf("计算积分抵扣失败: %v", err)
		pointsValid = false
	}

	// 8. 获取运费配置（用于警告信息）
	shippingConfig, err := s.getShippingConfig()
	if err != nil {
		return nil, err
	}

	// 8. 计算最终金额
	afterAllDiscounts := afterCouponAmount - pointsDeduct
	totalAmount := afterAllDiscounts + shippingFee

	// 9. 获取用户可用积分数
	availablePoints, err := s.getUserAvailablePoints(ctx, userID)
	if err != nil {
		logx.Errorf("获取用户可用积分失败: %v", err)
		availablePoints = 0
	}

	// 10. 判断积分开关状态（需要考虑可用积分和剩余金额）
	usePointsFlag := req.UsePoints && availablePoints > 0 && afterCouponAmount > 0

	// 11. 生成警告信息
	totalDiscount := memberDiscountAmount + couponDeduct // 总折扣金额
	warnings := s.generateWarnings(productAmount, totalDiscount, pointsDeduct, shippingFee, shippingConfig.FreeThreshold)

	return &checkoutModel.RecalculateResponse{
		Valid:           couponValid && pointsValid,
		ShippingFee:     shippingFee,
		CouponValid:     couponValid,
		PointsValid:     pointsValid,
		AvailablePoints: availablePoints,
		UsePoints:       usePointsFlag,
		Breakdown: checkoutModel.Breakdown{
			ProductAmount:  productAmount,
			MemberDiscount: memberDiscountAmount, // 会员折扣单独显示
			CouponDeduct:   couponDeduct,         // 优惠券抵扣
			PointsDeduct:   pointsDeduct,
			TotalAmount:    totalAmount,
		},
		Warnings: warnings,
	}, nil
}

// getCartItems 获取购物车商品信息
func (s *CheckoutService) getCartItems(ctx context.Context, userID uint, cartItemIDs []string) ([]checkoutModel.CartItem, error) {
	if len(cartItemIDs) == 0 {
		return nil, fmt.Errorf("购物车商品ID列表为空")
	}

	// 转换字符串ID为uint
	var ids []uint
	for _, idStr := range cartItemIDs {
		id, err := strconv.ParseUint(idStr, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("无效的购物车ID: %s", idStr)
		}
		ids = append(ids, uint(id))
	}

	// 查询购物车商品
	var carts []cartModel.Cart
	err := s.db.WithContext(ctx).
		Where("id IN ? AND user_id = ?", ids, userID).
		Preload("Goods").
		Preload("Spec").
		Find(&carts).Error
	if err != nil {
		return nil, fmt.Errorf("查询购物车商品失败: %w", err)
	}

	// 转换为CartItem
	var cartItems []checkoutModel.CartItem
	for _, cart := range carts {
		var price float64
		var name, image, specName string

		if cart.Goods != nil {
			name = cart.Goods.GoodsName
			image = cart.Goods.Pic
			price = cart.Goods.TagPrice
		}

		if cart.Spec != nil {
			price = cart.Spec.SalePrice
			specName = cart.Spec.SpecName
			if cart.Spec.SpecName != "" {
				name += " " + cart.Spec.SpecName
			}
		}

		cartItems = append(cartItems, checkoutModel.CartItem{
			ID:       cart.ID,
			GoodsID:  cart.GoodsID,
			SpecID:   cart.SpecID,
			Quantity: cart.Quantity,
			Price:    price,
			Name:     name,
			Image:    image,
			SpecName: specName, // 添加规格名称
		})
	}

	return cartItems, nil
}

// convertToProducts 转换为商品信息
func (s *CheckoutService) convertToProducts(cartItems []checkoutModel.CartItem) []checkoutModel.Product {
	var products []checkoutModel.Product
	for _, item := range cartItems {
		products = append(products, checkoutModel.Product{
			ID:       fmt.Sprintf("p%d", item.GoodsID),
			Name:     item.Name,
			Price:    item.Price,
			Image:    item.Image,
			Quantity: item.Quantity, // 添加商品数量
			SpecName: item.SpecName, // 添加规格名称
		})
	}
	return products
}

// calculateProductAmount 计算商品总金额
func (s *CheckoutService) calculateProductAmount(cartItems []checkoutModel.CartItem) float64 {
	var total float64
	for _, item := range cartItems {
		total += item.Price * float64(item.Quantity)
	}
	return total
}

// getShippingConfig 获取运费配置（从数据库动态获取）
func (s *CheckoutService) getShippingConfig() (checkoutModel.ShippingConfig, error) {
	var config struct {
		ConfigType  int     `json:"config_type"`  // 1=全部包邮 2=满减运费
		FreeAmount  float64 `json:"free_amount"`  // 包邮门槛
		ShippingFee float64 `json:"shipping_fee"` // 运费金额
	}

	// 从shipping_config表查询运费配置（与OrderService保持一致）
	err := s.db.Table("shipping_config").
		Where("is_active = ?", 1).
		Order("id DESC").
		First(&config).Error

	if err != nil {
		logx.Errorf("查询运费配置失败: %v", err)
		return checkoutModel.ShippingConfig{}, fmt.Errorf("运费配置未设置，请联系管理员配置运费规则")
	}

	// 构建运费规则
	var rules []checkoutModel.ShippingRule
	if config.ConfigType == 1 {
		// 全部包邮
		rules = []checkoutModel.ShippingRule{
			{MinAmount: 0, Fee: 0},
		}
	} else {
		// 满减运费
		rules = []checkoutModel.ShippingRule{
			{MinAmount: config.FreeAmount, Fee: 0}, // 包邮规则
		}
	}

	return checkoutModel.ShippingConfig{
		DefaultFee:    config.ShippingFee,
		FreeThreshold: config.FreeAmount,
		Rules:         rules,
	}, nil
}

// getAvailableCoupons 获取可用优惠券（返回完整字段）
func (s *CheckoutService) getAvailableCoupons(ctx context.Context, userID uint, productAmount float64) ([]checkoutModel.Coupon, error) {
	// 查询用户可用优惠券（获取完整字段）
	var userCoupons []struct {
		ID         uint      `json:"id"`
		CouponID   uint      `json:"coupon_id"`
		Status     int       `json:"status"`
		ValidFrom  time.Time `json:"valid_from"`
		ValidUntil time.Time `json:"valid_until"`

		// 优惠券详情
		Name        string  `json:"name"`
		Description string  `json:"description"`
		Type        int     `json:"type"`       // 1=满减券 2=折扣券 3=立减券
		Amount      float64 `json:"amount"`     // 优惠金额
		Discount    float64 `json:"discount"`   // 折扣比例
		MinAmount   float64 `json:"min_amount"` // 最低使用金额
		Scope       string  `json:"scope"`      // 使用范围
	}

	err := s.db.WithContext(ctx).Table("user_coupons uc").
		Select("uc.id, uc.coupon_id, uc.status, uc.valid_from, uc.valid_until, c.name, c.description, c.type, c.amount, c.discount, c.min_amount, c.scope").
		Joins("LEFT JOIN coupons c ON uc.coupon_id = c.id").
		Where("uc.user_id = ? AND uc.status = 0 AND uc.valid_until > ? AND uc.deleted_at IS NULL AND c.deleted_at IS NULL", userID, time.Now()).
		Find(&userCoupons).Error

	if err != nil {
		return nil, fmt.Errorf("查询用户优惠券失败: %w", err)
	}

	var coupons []checkoutModel.Coupon
	var bestCouponID string
	var maxDeduct float64

	for _, uc := range userCoupons {
		// 判断是否可用（检查使用条件）
		canUse := productAmount >= uc.MinAmount

		// 计算抵扣金额
		var deduct float64
		if canUse {
			switch uc.Type {
			case 1: // 满减券
				deduct = uc.Amount
			case 2: // 折扣券
				deduct = productAmount * (1 - uc.Discount/10) // 8.5表示8.5折
			case 3: // 立减券
				deduct = uc.Amount
			}

			// 记录最优券
			if deduct > maxDeduct {
				maxDeduct = deduct
				bestCouponID = fmt.Sprintf("c%d", uc.ID)
			}
		}

		// 格式化优惠券类型文本
		var typeText string
		switch uc.Type {
		case 1:
			typeText = fmt.Sprintf("满%.0f减%.0f", uc.MinAmount, uc.Amount)
		case 2:
			typeText = fmt.Sprintf("%.1f折券", uc.Discount)
		case 3:
			typeText = fmt.Sprintf("立减%.0f元", uc.Amount)
		default:
			typeText = "优惠券"
		}

		coupons = append(coupons, checkoutModel.Coupon{
			ID:          fmt.Sprintf("c%d", uc.ID),
			Name:        uc.Name,
			Description: uc.Description,
			Type:        uc.Type,
			TypeText:    typeText,
			Amount:      uc.Amount,
			Discount:    uc.Discount,
			MinAmount:   uc.MinAmount,
			Scope:       uc.Scope,
			ValidFrom:   uc.ValidFrom.Format("2006-01-02"),
			ValidUntil:  uc.ValidUntil.Format("2006-01-02"),
			CanUse:      canUse,
			IsBest:      false,
		})
	}

	// 标记最优券
	for i := range coupons {
		if coupons[i].ID == bestCouponID {
			coupons[i].IsBest = true
			break
		}
	}

	return coupons, nil
}

// getPointsPolicy 获取积分政策
func (s *CheckoutService) getPointsPolicy(ctx context.Context, userID uint, productAmount float64) (checkoutModel.PointsPolicy, error) {
	// 查询用户叶小币余额
	var userCoins struct {
		AvailableCoins int `json:"available_coins"`
	}
	err := s.db.WithContext(ctx).Table("user_coins").
		Select("available_coins").Where("user_id = ?", userID).First(&userCoins).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户没有叶小币账户，返回默认值
			return checkoutModel.PointsPolicy{
				UsablePoints: 0,
				Ratio:        1,
				MaxDeduct:    0,
			}, nil
		}
		return checkoutModel.PointsPolicy{}, fmt.Errorf("查询用户叶小币失败: %w", err)
	}

	// 查询全局配置获取兑换比例
	var globalConfig struct {
		ExchangeRate int `json:"exchange_rate"`
	}
	err = s.db.WithContext(ctx).Table("coin_global_config").
		Select("exchange_rate").Where("enabled = 1").First(&globalConfig).Error
	if err != nil {
		// 如果查询失败或未启用，返回无积分可用
		return checkoutModel.PointsPolicy{
			UsablePoints: 0,
			Ratio:        0,
			MaxDeduct:    0,
		}, nil
	}

	// 积分政策配置
	maxDeductRate := 0.5 // 最多抵扣50%

	usablePoints := userCoins.AvailableCoins
	maxDeduct := productAmount * maxDeductRate

	// 根据叶小币数量限制最大抵扣
	pointsMaxDeduct := float64(usablePoints) / float64(globalConfig.ExchangeRate)
	if pointsMaxDeduct < maxDeduct {
		maxDeduct = pointsMaxDeduct
	}

	return checkoutModel.PointsPolicy{
		UsablePoints: usablePoints,
		Ratio:        globalConfig.ExchangeRate,
		MaxDeduct:    maxDeduct,
	}, nil
}

// getMemberDiscount 获取用户会员等级折扣
func (s *CheckoutService) getMemberDiscount(ctx context.Context, userID uint) (float64, error) {
	// 查询用户当前等级
	var userLevel struct {
		Level int `json:"level"`
	}
	err := s.db.WithContext(ctx).Table("wx_user").
		Select("user_level").Where("id = ?", userID).First(&userLevel).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil // 用户不存在，无折扣
		}
		return 0, fmt.Errorf("查询用户等级失败: %w", err)
	}

	// 如果用户等级为0或未设置，无折扣
	if userLevel.Level <= 0 {
		return 0, nil
	}

	// 查询等级规则获取商品折扣
	var levelRule struct {
		ProductDiscount float64 `json:"product_discount"`
	}
	err = s.db.WithContext(ctx).Table("user_level_rules").
		Select("product_discount").
		Where("level_order = ? AND deleted_at IS NULL", userLevel.Level).
		First(&levelRule).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil // 等级规则不存在，无折扣
		}
		return 0, fmt.Errorf("查询等级规则失败: %w", err)
	}

	// 返回折扣比例（如9.5表示9.5折，即5%的折扣）
	if levelRule.ProductDiscount > 0 && levelRule.ProductDiscount < 10 {
		return (10 - levelRule.ProductDiscount) / 10, nil // 转换为折扣率
	}

	return 0, nil // 无有效折扣
}

// calculateOptimalPreview 计算最优方案预览（包含会员折扣）
func (s *CheckoutService) calculateOptimalPreview(productAmount float64, shippingConfig checkoutModel.ShippingConfig, coupons []checkoutModel.Coupon, pointsPolicy checkoutModel.PointsPolicy, memberDiscount float64) checkoutModel.Preview {
	// 1. 先应用会员折扣
	memberDiscountAmount := productAmount * memberDiscount
	afterMemberDiscount := productAmount - memberDiscountAmount

	// 2. 找到最优优惠券并计算真实抵扣金额（基于会员折扣后的金额）
	var bestCouponDeduct float64
	for _, coupon := range coupons {
		if coupon.IsBest {
			// 根据优惠券类型计算真实抵扣金额
			if coupon.Type == 1 {
				// 满减券：直接使用减免金额
				bestCouponDeduct = coupon.Amount
			} else if coupon.Type == 2 {
				// 折扣券：基于会员折扣后的金额计算
				bestCouponDeduct = afterMemberDiscount * (1 - coupon.Discount/100)
			} else if coupon.Type == 3 {
				// 立减券：直接使用减免金额
				bestCouponDeduct = coupon.Amount
			}
			break
		}
	}

	// 3. 计算运费（基于会员折扣和优惠券后的金额，积分抵扣前）
	afterCouponAmount := afterMemberDiscount - bestCouponDeduct
	var shippingFee float64
	if afterCouponAmount >= shippingConfig.FreeThreshold {
		shippingFee = 0
	} else {
		shippingFee = shippingConfig.DefaultFee
	}

	// 4. 使用全部可用积分（基于会员折扣和优惠券后的金额，不包含运费）
	pointsDeduct := pointsPolicy.MaxDeduct
	if pointsDeduct > afterCouponAmount {
		pointsDeduct = afterCouponAmount // 积分抵扣不能超过剩余金额
	}

	// 5. 计算最终金额
	afterAllDiscounts := afterCouponAmount - pointsDeduct
	totalAmount := afterAllDiscounts + shippingFee

	return checkoutModel.Preview{
		ProductAmount:  productAmount,
		ShippingFee:    shippingFee,
		MemberDiscount: memberDiscountAmount, // 会员折扣单独显示
		CouponDeduct:   bestCouponDeduct,     // 优惠券抵扣
		PointsDeduct:   pointsDeduct,
		TotalAmount:    totalAmount,
	}
}

// calculateCouponDeduct 计算优惠券抵扣
func (s *CheckoutService) calculateCouponDeduct(ctx context.Context, userID uint, couponID string, productAmount float64) (float64, bool, error) {
	if couponID == "" {
		return 0, true, nil
	}

	// 解析优惠券ID
	if len(couponID) < 2 || couponID[0] != 'c' {
		return 0, false, fmt.Errorf("无效的优惠券ID格式")
	}

	userCouponID, err := strconv.ParseUint(couponID[1:], 10, 32)
	if err != nil {
		return 0, false, fmt.Errorf("无效的优惠券ID")
	}

	// 查询优惠券信息
	var userCoupon checkoutModel.UserCoupon
	err = s.db.WithContext(ctx).Table("user_coupons uc").
		Select("uc.id, uc.coupon_id, c.name, c.type, c.min_amount, c.discount_val, uc.expire_time, uc.is_used").
		Joins("LEFT JOIN coupons c ON uc.coupon_id = c.id").
		Where("uc.id = ? AND uc.user_id = ?", userCouponID, userID).
		First(&userCoupon).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, false, fmt.Errorf("优惠券不存在")
		}
		return 0, false, fmt.Errorf("查询优惠券失败: %w", err)
	}

	// 检查优惠券状态
	if userCoupon.IsUsed {
		return 0, false, fmt.Errorf("优惠券已使用")
	}

	if time.Now().After(userCoupon.ExpireTime) {
		return 0, false, fmt.Errorf("优惠券已过期")
	}

	// 检查使用条件
	if productAmount < userCoupon.MinAmount {
		return 0, false, fmt.Errorf("未满足优惠券使用条件")
	}

	// 计算抵扣金额
	var deduct float64
	switch userCoupon.Type {
	case 1: // 满减券
		deduct = userCoupon.DiscountVal
	case 2: // 折扣券
		deduct = productAmount * (1 - userCoupon.DiscountVal/100)
	case 3: // 无门槛券
		deduct = userCoupon.DiscountVal
	default:
		return 0, false, fmt.Errorf("不支持的优惠券类型")
	}

	// 确保抵扣金额不超过商品金额
	if deduct > productAmount {
		deduct = productAmount
	}

	return deduct, true, nil
}

// calculatePointsDeduct 计算积分抵扣
func (s *CheckoutService) calculatePointsDeduct(ctx context.Context, userID uint, usePoints int, productAmount float64) (float64, bool, error) {
	if usePoints <= 0 {
		return 0, true, nil
	}

	// 首先检查积分功能是否启用
	var globalConfig struct {
		ExchangeRate int `json:"exchange_rate"`
	}
	err := s.db.WithContext(ctx).Table("coin_global_config").
		Select("exchange_rate").Where("enabled = 1").First(&globalConfig).Error
	if err != nil {
		// 如果查询失败或未启用，不允许使用积分
		return 0, false, fmt.Errorf("叶小币功能未启用，无法使用积分抵扣")
	}

	// 查询用户积分
	var userPoints int
	err = s.db.WithContext(ctx).Table("wx_user").
		Select("points").
		Where("id = ?", userID).
		Scan(&userPoints).Error

	if err != nil {
		return 0, false, fmt.Errorf("查询用户积分失败: %w", err)
	}

	// 检查积分是否足够
	if usePoints > userPoints {
		return 0, false, fmt.Errorf("积分不足")
	}

	// 使用全局配置的兑换比例
	maxDeductRate := 0.5 // 最多抵扣50%

	// 计算抵扣金额
	pointsDeduct := float64(usePoints) / float64(globalConfig.ExchangeRate)
	maxAllowedDeduct := productAmount * maxDeductRate

	// 检查是否超过最大抵扣比例
	if pointsDeduct > maxAllowedDeduct {
		return 0, false, fmt.Errorf("积分抵扣超过最大限制")
	}

	return pointsDeduct, true, nil
}

// calculateShippingFee 计算运费（与OrderService保持一致）
func (s *CheckoutService) calculateShippingFee(amount float64) (float64, error) {
	// 查询运费配置表（与OrderService使用相同的逻辑）
	var shippingConfig struct {
		ConfigType  int     `json:"config_type"`  // 1=全部包邮 2=满减运费
		FreeAmount  float64 `json:"free_amount"`  // 包邮门槛
		ShippingFee float64 `json:"shipping_fee"` // 运费金额
	}

	err := s.db.Table("shipping_config").
		Where("is_active = ?", 1).
		Order("id DESC").
		First(&shippingConfig).Error

	if err != nil {
		logx.Errorf("查询运费配置失败: %v", err)
		return 0, fmt.Errorf("运费配置未设置，请联系管理员配置运费规则")
	}

	// 根据配置类型计算运费
	if shippingConfig.ConfigType == 1 {
		// 全部包邮
		return 0, nil
	} else if shippingConfig.ConfigType == 2 {
		// 满减运费
		if shippingConfig.FreeAmount > 0 && amount >= shippingConfig.FreeAmount {
			return 0, nil // 满足包邮条件
		} else {
			return shippingConfig.ShippingFee, nil // 需要支付运费
		}
	}

	// 默认返回配置的运费
	return shippingConfig.ShippingFee, nil
}

// generateWarnings 生成警告信息
func (s *CheckoutService) generateWarnings(productAmount, couponDeduct, pointsDeduct, shippingFee float64, freeThreshold float64) []string {
	var warnings []string

	afterDiscountAmount := productAmount - couponDeduct - pointsDeduct

	// 检查是否因为抵扣导致不满足包邮条件
	if freeThreshold > 0 && productAmount >= freeThreshold && afterDiscountAmount < freeThreshold && shippingFee > 0 {
		warnings = append(warnings, "积分抵扣后金额低于包邮门槛，运费增加")
	}

	// 检查优惠券和积分同时使用的提醒
	if couponDeduct > 0 && pointsDeduct > 0 {
		warnings = append(warnings, "已同时使用优惠券和积分，享受最大优惠")
	}

	return warnings
}

// calculateMultipleCouponsDeduct 计算多张优惠券抵扣
func (s *CheckoutService) calculateMultipleCouponsDeduct(ctx context.Context, userID uint, couponIDs []string, productAmount float64) (float64, bool, error) {
	if len(couponIDs) == 0 {
		return 0, true, nil
	}

	totalDeduct := 0.0
	remainingAmount := productAmount

	// 逐张计算优惠券抵扣
	for _, couponID := range couponIDs {
		if couponID == "" {
			continue
		}

		deduct, valid, err := s.calculateCouponDeduct(ctx, userID, couponID, remainingAmount)
		if err != nil {
			logx.Errorf("计算优惠券 %s 抵扣失败: %v", couponID, err)
			return 0, false, err
		}

		if !valid {
			return 0, false, fmt.Errorf("优惠券 %s 不可用", couponID)
		}

		totalDeduct += deduct
		remainingAmount -= deduct

		// 如果剩余金额为0，停止计算
		if remainingAmount <= 0 {
			break
		}
	}

	return totalDeduct, true, nil
}

// calculatePointsDeductByFlag 根据布尔值计算积分抵扣
func (s *CheckoutService) calculatePointsDeductByFlag(ctx context.Context, userID uint, usePoints bool, productAmount float64) (float64, bool, error) {
	if !usePoints || productAmount <= 0 {
		return 0, true, nil
	}

	// 首先检查积分功能是否启用
	var globalConfig struct {
		ExchangeRate int `json:"exchange_rate"`
	}
	err := s.db.WithContext(ctx).Table("coin_global_config").
		Select("exchange_rate").Where("enabled = 1").First(&globalConfig).Error
	if err != nil {
		// 如果查询失败或未启用，不允许使用积分
		return 0, false, fmt.Errorf("叶小币功能未启用，无法使用积分抵扣")
	}

	// 查询用户积分余额
	var userCoins struct {
		AvailableCoins int `json:"available_coins"`
	}
	err = s.db.WithContext(ctx).Table("user_coins").
		Select("available_coins").Where("user_id = ?", userID).First(&userCoins).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户没有积分账户，返回0抵扣
			return 0, true, nil
		}
		return 0, false, fmt.Errorf("查询用户积分失败: %w", err)
	}

	// 计算最大可用积分数
	maxDeductRate := 0.5 // 最多抵扣50%
	maxAllowedDeduct := productAmount * maxDeductRate

	// 根据积分余额计算最大抵扣金额
	pointsMaxDeduct := float64(userCoins.AvailableCoins) / float64(globalConfig.ExchangeRate)

	// 取较小值作为实际抵扣金额
	actualDeduct := maxAllowedDeduct
	if pointsMaxDeduct < actualDeduct {
		actualDeduct = pointsMaxDeduct
	}

	// 确保不超过商品金额
	if actualDeduct > productAmount {
		actualDeduct = productAmount
	}

	return actualDeduct, true, nil
}

// getUserAvailablePoints 获取用户可用积分数
func (s *CheckoutService) getUserAvailablePoints(ctx context.Context, userID uint) (int, error) {
	// 首先检查积分功能是否启用
	var globalConfig struct {
		Enabled bool `json:"enabled"`
	}
	err := s.db.WithContext(ctx).Table("coin_global_config").
		Select("enabled").Where("enabled = 1").First(&globalConfig).Error
	if err != nil {
		// 如果查询失败或未启用，返回0积分
		return 0, nil
	}

	// 查询用户积分余额
	var userCoins struct {
		AvailableCoins int `json:"available_coins"`
	}
	err = s.db.WithContext(ctx).Table("user_coins").
		Select("available_coins").Where("user_id = ?", userID).First(&userCoins).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户没有积分账户，返回0
			return 0, nil
		}
		return 0, fmt.Errorf("查询用户积分失败: %w", err)
	}

	return userCoins.AvailableCoins, nil
}
