package service

import (
	"context"
	"fmt"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type CategoryService struct {
	db *gorm.DB
}

func NewCategoryService() *CategoryService {
	return &CategoryService{
		db: mysql.GetDB(),
	}
}

// GetRecommendCategories 获取推荐分类
func (s *CategoryService) GetRecommendCategories(ctx context.Context, limit int) ([]*model.Category, error) {
	var categories []*model.Category
	query := s.db.WithContext(ctx).Where("is_recommended = ?", 1).
		Where("status = ?", 1).
		Order("recommend_order ASC, created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("查询推荐分类失败: %w", err)
	}

	return categories, nil
}

// GetCategoryTree 获取分类树（小程序端展示用）
func (s *CategoryService) GetCategoryTree(ctx context.Context, parentID uint) ([]*model.CategoryTree, error) {
	var categories []*model.Category
	query := s.db.WithContext(ctx).Where("parent_id = ?", parentID).
		Where("status = ?", 1).
		Order("sort_order ASC")

	if err := query.Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("查询分类失败: %w", err)
	}

	var tree []*model.CategoryTree
	for _, category := range categories {
		categoryTree := &model.CategoryTree{
			ID:             category.ID,
			ParentID:       category.ParentID,
			CategoryName:   category.CategoryName,
			CategoryIcon:   category.CategoryIcon,
			CategoryImage:  category.CategoryImage,
			Description:    category.Description,
			SortOrder:      category.SortOrder,
			IsRecommended:  category.IsRecommended,
			RecommendOrder: category.RecommendOrder,
			Status:         category.Status,
			Level:          category.Level,
			Path:           category.Path,
			GoodsCount:     category.GoodsCount,
			CreatedAt:      category.CreatedAt,
			UpdatedAt:      category.UpdatedAt,
		}

		// 递归获取子分类
		if children, err := s.GetCategoryTree(ctx, category.ID); err == nil {
			categoryTree.Children = children
		}

		tree = append(tree, categoryTree)
	}

	return tree, nil
}

// GetTopCategories 获取顶级分类
func (s *CategoryService) GetTopCategories(ctx context.Context) ([]*model.Category, error) {
	var categories []*model.Category
	if err := s.db.WithContext(ctx).Where("parent_id = ?", 0).
		Where("status = ?", 1).
		Order("sort_order ASC").Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("查询顶级分类失败: %w", err)
	}

	return categories, nil
}

// GetCategoryById 根据ID获取分类详情
func (s *CategoryService) GetCategoryById(ctx context.Context, id uint) (*model.Category, error) {
	category := &model.Category{}
	if err := s.db.WithContext(ctx).First(category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("分类不存在")
		}
		return nil, fmt.Errorf("查询分类失败: %w", err)
	}
	return category, nil
}

// GetCategoriesWithGoodsCount 获取分类及其商品数量
func (s *CategoryService) GetCategoriesWithGoodsCount(ctx context.Context, parentID uint) ([]*model.Category, error) {
	var categories []*model.Category
	if err := s.db.WithContext(ctx).Where("parent_id = ?", parentID).
		Where("status = ?", 1).
		Order("sort_order ASC").Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("查询分类失败: %w", err)
	}

	// 统计每个分类下的商品数量
	for _, category := range categories {
		var count int64
		s.db.Model(&model.Goods{}).Where("local_category_id = ?", category.ID).
			Where("status = ?", 1).Where("is_on_sale = ?", 1).Count(&count)
		category.GoodsCount = count
	}

	return categories, nil
}

// SearchCategories 搜索分类
func (s *CategoryService) SearchCategories(ctx context.Context, keyword string) ([]*model.Category, error) {
	var categories []*model.Category
	if err := s.db.WithContext(ctx).Where("name LIKE ?", "%"+keyword+"%").
		Where("status = ?", 1).
		Order("sort_order ASC").Find(&categories).Error; err != nil {
		return nil, fmt.Errorf("搜索分类失败: %w", err)
	}

	return categories, nil
}
