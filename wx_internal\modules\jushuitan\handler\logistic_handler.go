package handler

import (
	"bytes"

	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	jst "yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// LogisticHandler 聚水潭物流处理器
type LogisticHandler struct {
	ctx    *svc.WxServiceContext
	client *jst.Client
}

// NewLogisticHandler 创建聚水潭物流处理器
func NewLogisticHandler(ctx *svc.WxServiceContext) *LogisticHandler {
	return &LogisticHandler{
		ctx:    ctx,
		client: ctx.JushuitanClient,
	}
}

// QueryLogistic 查询物流信息处理
func (h *LogisticHandler) QueryLogistic(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.<PERSON>, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 物流查询原始请求: %s", reqStr)

	// 解析请求
	var req jst.LogisticQueryRequest
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 设置默认值
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	} else if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 若同时提供了修改起止时间，检查时间间隔
	if req.ModifiedBegin != "" && req.ModifiedEnd != "" {
		beginTime, err1 := time.Parse("2006-01-02 15:04:05", req.ModifiedBegin)
		endTime, err2 := time.Parse("2006-01-02 15:04:05", req.ModifiedEnd)
		if err1 == nil && err2 == nil {
			// 验证时间间隔不超过7天
			if endTime.Sub(beginTime) > 7*24*time.Hour {
				err := fmt.Errorf("修改时间间隔不能超过7天")
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
		}
	}

	// 若提供了订单号列表，验证数量
	if len(req.SoIDs) > 20 {
		err := fmt.Errorf("平台订单号最多支持20个")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.QueryLogistic(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询物流信息失败")
		return
	}

	// 记录原始响应
	respJson, _ := json.Marshal(resp)
	logx.Infof("[JST] 物流查询原始响应数据: %s", string(respJson))

	logx.Infof("[JST] 处理物流查询请求完成，耗时: %v, 结果数量: %d", time.Since(start), len(resp.Orders))

	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}
