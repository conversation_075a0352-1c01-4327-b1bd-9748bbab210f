package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"yekaitai/internal/modules/user_level/model"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// UserLevelListRequest 等级列表请求
type UserLevelListRequest struct {
	types.PageRequest
	LevelName string `form:"level_name,optional" label:"等级名称"`
	StartDate string `form:"start_date,optional" label:"开始日期,格式:2025-12-23"`
	EndDate   string `form:"end_date,optional" label:"结束日期,格式:2025-12-23"`
}

// CreateUserLevelRequest 创建等级请求
type CreateUserLevelRequest struct {
	LevelName   string `json:"level_name" validate:"required,max=50" label:"等级名称"`
	LevelOrder  int    `json:"level_order" validate:"min=0" label:"等级顺序"`
	Description string `json:"description,optional" label:"等级描述"`

	// 升级条件
	RequireRegister       bool    `json:"require_register,optional" label:"需要注册成功"`
	RequirePatientProfile bool    `json:"require_patient_profile,optional" label:"需要完善就诊人档案"`
	RequireConsumption    bool    `json:"require_consumption,optional" label:"需要消费累计"`
	ConsumptionAmount     float64 `json:"consumption_amount,optional" validate:"min=0" label:"消费累计金额"`

	// 会员权益
	FreeActivitySignup       bool    `json:"free_activity_signup,optional" label:"活动报名费免费"`
	RegistrationFeeReduction float64 `json:"registration_fee_reduction,optional" validate:"min=0" label:"挂号费减免金额(元)"`
	ExtraCoins               int     `json:"extra_coins,optional" validate:"min=0" label:"额外赠送叶小币"`
	ProductDiscount          float64 `json:"product_discount,optional" validate:"min=0,max=10" label:"商品折扣比例"`

	// 赠送内容
	GiftCoupons []GiftCouponConfig `json:"gift_coupons,optional" label:"赠送优惠券配置"`
}

// UpdateUserLevelRequest 更新等级请求
type UpdateUserLevelRequest struct {
	ID uint `json:"id" validate:"required" label:"等级ID"`
	CreateUserLevelRequest
}

// GiftCouponConfig 赠送优惠券配置
type GiftCouponConfig struct {
	CouponID uint `json:"coupon_id" validate:"required" label:"优惠券ID"`
	Quantity int  `json:"quantity" validate:"required,min=1" label:"赠送数量"`
}

// UserLevelResponse 等级响应
type UserLevelResponse struct {
	*model.UserLevelRule
	CreatorName string             `json:"creator_name"` // 创建人姓名
	GiftCoupons []GiftCouponConfig `json:"gift_coupons"` // 赠送优惠券配置
}

// UserLevelService 用户等级服务
type UserLevelService struct {
	repo model.UserLevelRepository
}

// NewUserLevelService 创建用户等级服务实例
func NewUserLevelService() *UserLevelService {
	db := mysql.GetDB()
	return &UserLevelService{
		repo: model.NewUserLevelRepository(db),
	}
}

// GetUserLevelList 获取等级列表
func (s *UserLevelService) GetUserLevelList(ctx context.Context, req *UserLevelListRequest) ([]*UserLevelResponse, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	levels, total, err := s.repo.List(req.Page, req.Size, req.LevelName, req.StartDate, req.EndDate)
	if err != nil {
		return nil, 0, fmt.Errorf("获取等级列表失败: %w", err)
	}

	var responses []*UserLevelResponse
	for _, level := range levels {
		response, err := s.buildUserLevelResponse(level)
		if err != nil {
			continue // 跳过构建失败的项
		}
		responses = append(responses, response)
	}

	return responses, total, nil
}

// CreateUserLevel 创建等级
func (s *UserLevelService) CreateUserLevel(ctx context.Context, req *CreateUserLevelRequest) (*UserLevelResponse, error) {
	// 验证等级名称唯一性
	existingLevel, err := s.repo.FindByName(req.LevelName)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查等级名称失败: %w", err)
	}
	if existingLevel != nil {
		return nil, fmt.Errorf("等级名称已存在")
	}

	// 如果没有指定排序，自动设置为最大值+1
	if req.LevelOrder == 0 {
		maxOrder, err := s.repo.GetMaxLevelOrder()
		if err != nil {
			return nil, fmt.Errorf("获取最大排序值失败: %w", err)
		}
		req.LevelOrder = maxOrder + 1
	}

	// 序列化赠送配置
	giftCouponsJson, err := json.Marshal(req.GiftCoupons)
	if err != nil {
		return nil, fmt.Errorf("序列化优惠券配置失败: %w", err)
	}

	// 构造等级模型
	level := &model.UserLevelRule{
		LevelName:                req.LevelName,
		LevelOrder:               req.LevelOrder,
		Description:              req.Description,
		RequireRegister:          req.RequireRegister,
		RequirePatientProfile:    req.RequirePatientProfile,
		RequireConsumption:       req.RequireConsumption,
		ConsumptionAmount:        req.ConsumptionAmount,
		FreeActivitySignup:       req.FreeActivitySignup,
		RegistrationFeeReduction: req.RegistrationFeeReduction,
		ExtraCoins:               req.ExtraCoins,
		ProductDiscount:          req.ProductDiscount,
		GiftCoupons:              string(giftCouponsJson),
	}

	// 从上下文获取创建人ID
	if adminID, ok := ctx.Value("admin_id").(uint); ok {
		level.CreatorID = adminID
	}

	if err := s.repo.Create(level); err != nil {
		return nil, fmt.Errorf("创建等级失败: %w", err)
	}

	return s.buildUserLevelResponse(level)
}

// UpdateUserLevel 更新等级
func (s *UserLevelService) UpdateUserLevel(ctx context.Context, req *UpdateUserLevelRequest) (*UserLevelResponse, error) {
	// 查找现有等级
	existingLevel, err := s.repo.FindByID(req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("等级不存在")
		}
		return nil, fmt.Errorf("查找等级失败: %w", err)
	}

	// 验证等级名称唯一性（排除自己）
	levelByName, err := s.repo.FindByName(req.LevelName)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("检查等级名称失败: %w", err)
	}
	if levelByName != nil && levelByName.ID != req.ID {
		return nil, fmt.Errorf("等级名称已存在")
	}

	// 序列化赠送配置
	giftCouponsJson, err := json.Marshal(req.GiftCoupons)
	if err != nil {
		return nil, fmt.Errorf("序列化优惠券配置失败: %w", err)
	}

	// 更新等级信息
	existingLevel.LevelName = req.LevelName
	existingLevel.LevelOrder = req.LevelOrder
	existingLevel.Description = req.Description
	existingLevel.RequireRegister = req.RequireRegister
	existingLevel.RequirePatientProfile = req.RequirePatientProfile
	existingLevel.RequireConsumption = req.RequireConsumption
	existingLevel.ConsumptionAmount = req.ConsumptionAmount
	existingLevel.FreeActivitySignup = req.FreeActivitySignup
	existingLevel.RegistrationFeeReduction = req.RegistrationFeeReduction
	existingLevel.ExtraCoins = req.ExtraCoins
	existingLevel.ProductDiscount = req.ProductDiscount
	existingLevel.GiftCoupons = string(giftCouponsJson)

	if err := s.repo.Update(existingLevel); err != nil {
		return nil, fmt.Errorf("更新等级失败: %w", err)
	}

	// 更新后重新计算所有用户等级
	go func() {
		if err := s.repo.BatchUpdateUserLevels(); err != nil {
			// 记录错误日志，但不影响主流程
			fmt.Printf("批量更新用户等级失败: %v\n", err)
		}
	}()

	return s.buildUserLevelResponse(existingLevel)
}

// DeleteUserLevel 删除等级
func (s *UserLevelService) DeleteUserLevel(ctx context.Context, id uint) error {
	// 检查等级是否存在
	_, err := s.repo.FindByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("等级不存在")
		}
		return fmt.Errorf("查找等级失败: %w", err)
	}

	// TODO: 检查是否有用户正在使用该等级
	// 如果有用户使用该等级，可能需要先迁移用户等级或者禁止删除

	if err := s.repo.Delete(id); err != nil {
		return fmt.Errorf("删除等级失败: %w", err)
	}

	// 删除后重新计算所有用户等级
	go func() {
		if err := s.repo.BatchUpdateUserLevels(); err != nil {
			fmt.Printf("批量更新用户等级失败: %v\n", err)
		}
	}()

	return nil
}

// GetUserLevelDetail 获取等级详情
func (s *UserLevelService) GetUserLevelDetail(ctx context.Context, id uint) (*UserLevelResponse, error) {
	level, err := s.repo.FindByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("等级不存在")
		}
		return nil, fmt.Errorf("获取等级详情失败: %w", err)
	}

	return s.buildUserLevelResponse(level)
}

// GetAvailableCoupons 获取可选择的优惠券列表
func (s *UserLevelService) GetAvailableCoupons(ctx context.Context, couponType int) (interface{}, error) {
	// 调用优惠券服务获取可用优惠券列表
	db := mysql.GetDB()

	query := db.Table("coupons").Select(`
		id, 
		name, 
		type,
		CASE 
			WHEN type = 1 THEN CONCAT('满', min_amount, '可用')
			WHEN type = 2 THEN '满减券'
			WHEN type = 3 THEN '立减券'
			ELSE '其他'
		END as usage_door,
		CASE 
			WHEN type = 1 THEN CONCAT(discount, '折')
			WHEN type = 2 THEN CONCAT(amount, '元')
			WHEN type = 3 THEN CONCAT(amount, '元')
			ELSE CONCAT(amount)
		END as amount,
		CASE 
			WHEN status = 1 THEN '生效中'
			WHEN status = 0 THEN '未生效'
			WHEN status = 2 THEN '已失效'
			ELSE '未知'
		END as coupon_status,
		CASE 
			WHEN validity_type = 1 THEN '永久有效'
			WHEN validity_type = 2 THEN CONCAT('领取后', valid_days, '天内有效')
			WHEN validity_type = 3 THEN CONCAT(DATE_FORMAT(valid_from, '%Y-%m-%d'), ' 至 ', DATE_FORMAT(valid_until, '%Y-%m-%d'))
			ELSE '未知'
		END as valid_period
	`).Where("deleted_at IS NULL")

	// 根据类型筛选
	if couponType > 0 {
		query = query.Where("type = ?", couponType)
	}

	var coupons []map[string]interface{}
	if err := query.Find(&coupons).Error; err != nil {
		return nil, fmt.Errorf("查询优惠券列表失败: %w", err)
	}

	// 计算总数
	var total int64
	countQuery := db.Table("coupons").Where("deleted_at IS NULL")
	if couponType > 0 {
		countQuery = countQuery.Where("type = ?", couponType)
	}
	countQuery.Count(&total)

	return map[string]interface{}{
		"list":  coupons,
		"total": total,
	}, nil
}

// buildUserLevelResponse 构建等级响应
func (s *UserLevelService) buildUserLevelResponse(level *model.UserLevelRule) (*UserLevelResponse, error) {
	response := &UserLevelResponse{
		UserLevelRule: level,
		CreatorName:   "系统", // 默认值
	}

	// 查询创建人姓名
	if level.CreatorID > 0 {
		if creatorName := s.getAdminUserName(level.CreatorID); creatorName != "" {
			response.CreatorName = creatorName
		} else {
			response.CreatorName = fmt.Sprintf("ID:%d", level.CreatorID)
		}
	}

	// 解析赠送优惠券配置
	if level.GiftCoupons != "" && level.GiftCoupons != "null" {
		var giftCoupons []GiftCouponConfig
		if err := json.Unmarshal([]byte(level.GiftCoupons), &giftCoupons); err == nil {
			response.GiftCoupons = giftCoupons
		}
	}

	return response, nil
}

// getAdminUserName 获取管理员姓名（优先从Redis缓存）
func (s *UserLevelService) getAdminUserName(adminID uint) string {
	adminUserMap := s.getAdminUserMap()
	if name, exists := adminUserMap[adminID]; exists {
		return name
	}
	return ""
}

// getAdminUserMap 获取管理员用户映射（带Redis缓存）
func (s *UserLevelService) getAdminUserMap() map[uint]string {
	db := mysql.GetDB()

	// 尝试从Redis获取缓存
	// redisClient := redis.GetClient()
	// cacheKey := "admin_user_cache"
	// cachedData, err := redisClient.Get(context.Background(), cacheKey).Result()
	// if err == nil && cachedData != "" {
	// 	var adminMap map[uint]string
	// 	if json.Unmarshal([]byte(cachedData), &adminMap) == nil {
	// 		return adminMap
	// 	}
	// }

	// 从数据库查询所有管理员用户
	var adminUsers []struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	}

	err := db.Table("admin_user").
		Select("id, name").
		Where("is_deleted = ?", false).
		Find(&adminUsers).Error

	adminMap := make(map[uint]string)
	if err == nil {
		for _, admin := range adminUsers {
			adminMap[admin.ID] = admin.Name
		}
	}

	// 缓存到Redis（5分钟过期）
	// if len(adminMap) > 0 {
	// 	if jsonData, err := json.Marshal(adminMap); err == nil {
	// 		redisClient.Set(context.Background(), cacheKey, string(jsonData), 5*time.Minute)
	// 	}
	// }

	return adminMap
}
