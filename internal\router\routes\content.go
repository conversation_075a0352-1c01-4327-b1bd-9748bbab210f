package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	contentHandler "yekaitai/internal/modules/content/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterContentRoutes 注册内容管理相关路由
func RegisterContentRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建内容管理处理器
	handler := contentHandler.NewContentHandler(serverCtx)

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 注册路由 - 根据原型图精简为9个核心接口
	server.AddRoutes(
		[]rest.Route{
			// 内容管理模块 (6个接口)
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/contents",
				Handler: adminAuthWrapper(handler.ListContents),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/contents/:contentId",
				Handler: adminAuth<PERSON>rapper(handler.GetContent),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/contents",
				Handler: adminAuthWrapper(handler.CreateContent),
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/contents/:contentId",
				Handler: adminAuthWrapper(handler.UpdateContent),
			},
			{
				Method:  http.MethodPatch,
				Path:    "/api/admin/contents/:contentId/status",
				Handler: adminAuthWrapper(handler.UpdateContentStatus),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/contents/:contentId",
				Handler: adminAuthWrapper(handler.DeleteContent),
			},

			// 报名管理模块 (3个接口)
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/contents/:contentId/signups",
				Handler: adminAuthWrapper(handler.GetContentSignUps),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/contents/:contentId/orders",
				Handler: adminAuthWrapper(handler.GetSignUpOrders),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/orders/:orderId",
				Handler: adminAuthWrapper(handler.GetSignUpOrderDetail),
			},
		},
	)
}
