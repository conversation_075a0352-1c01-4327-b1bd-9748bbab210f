package handler

import (
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"yekaitai/internal/svc"
	"yekaitai/internal/types"

	storeModel "yekaitai/internal/modules/store/model"
	doctorModel "yekaitai/pkg/common/model/doctor"
	userModel "yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"mime/multipart"
	"path/filepath"
	"strings"

	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 医生列表请求
type DoctorListRequest struct {
	types.PageRequest
	Name          string `form:"name,optional"`          // 医生姓名
	Mobile        string `form:"mobile,optional"`        // 医生手机号
	Hospital      string `form:"hospital,optional"`      // 医生所属医院
	Department    string `form:"department,optional"`    // 科室
	IsRecommended *bool  `form:"isRecommended,optional"` // 是否推荐
	SortBy        string `form:"sortBy,optional"`        // 排序字段，默认是created_at
	SortOrder     string `form:"sortOrder,optional"`     // 排序方式，默认是desc
}

// 医生详情请求
type DoctorDetailRequest struct {
	DoctorID uint `path:"doctorId"` // 医生ID
}

// 创建医生请求
type CreateDoctorRequest struct {
	MedicalLicense string `json:"medicalLicense,optional"` // 执业证号
	Specialty      string `json:"specialty,optional"`      // 专业领域
	Hospital       string `json:"hospital"`                // 所属医院/诊所
	Department     string `json:"department"`              // 科室
	Title          string `json:"title,optional"`          // 职称
	Introduction   string `json:"introduction,optional"`   // 简介
	StoreIDs       []uint `json:"storeIds"`                // 关联的门店ID列表
	PrimaryStoreID uint   `json:"primaryStoreId,optional"` // 主要门店ID
	Avatar         string `json:"avatar,optional"`         // 头像
	DisplayOrder   int    `json:"displayOrder,optional"`   // 展示顺序
	IsRecommended  bool   `json:"isRecommended,optional"`  // 是否推荐
	RecommendOrder int    `json:"recommendOrder,optional"` // 推荐排序
	Status         int    `json:"status,optional"`         // 状态：1-正常，0-禁用
	TagIDs         []uint `json:"tagIds,optional"`         // 标签ID列表
	// 用户基本信息
	Nickname string `json:"nickname"` // 姓名
	Mobile   string `json:"mobile"`   // 手机号
	Gender   int    `json:"gender"`   // 性别：0-未知，1-男，2-女
}

// 更新医生请求
type UpdateDoctorRequest struct {
	DoctorID       uint   `path:"doctorId"`                // 医生ID
	MedicalLicense string `json:"medicalLicense,optional"` // 执业证号
	Specialty      string `json:"specialty,optional"`      // 专业领域
	Hospital       string `json:"hospital,optional"`       // 所属医院/诊所
	Department     string `json:"department,optional"`     // 科室
	Title          string `json:"title,optional"`          // 职称
	Introduction   string `json:"introduction,optional"`   // 简介
	StoreIDs       []uint `json:"storeIds,optional"`       // 关联的门店ID列表
	PrimaryStoreID uint   `json:"primaryStoreId,optional"` // 主要门店ID
	Avatar         string `json:"avatar,optional"`         // 头像
	Nickname       string `json:"nickname,optional"`       // 姓名
	IsRecommended  *bool  `json:"isRecommended,optional"`  // 是否推荐
	RecommendOrder *int   `json:"recommendOrder,optional"` // 推荐排序
	TagIDs         []uint `json:"tagIds,optional"`         // 标签ID列表
	Status         int    `json:"status,optional"`         // 状态：1-正常，0-禁用
}

// 更新医生状态请求
type UpdateDoctorStatusRequest struct {
	DoctorID uint `path:"doctorId"` // 医生ID
	Status   int  `json:"status"`   // 状态：1-正常，0-禁用
}

// 更新医生展示顺序请求
type UpdateDoctorDisplayOrderRequest struct {
	DoctorID      uint `path:"doctorId"`      // 医生ID
	DisplayOrder  int  `json:"displayOrder"`  // 展示顺序
	IsRecommended bool `json:"isRecommended"` // 是否推荐
}

// 设置医生排班请求
type SetDoctorScheduleRequest struct {
	DoctorID  uint                   `path:"doctorId"`  // 医生ID
	StoreID   uint                   `json:"storeId"`   // 门店ID
	Schedules []DoctorScheduleDetail `json:"schedules"` // 排班详情
}

// 医生排班详情
type DoctorScheduleDetail struct {
	Date      string `json:"date"`      // 日期 yyyy-MM-dd
	StartTime string `json:"startTime"` // 开始时间 HH:MM
	EndTime   string `json:"endTime"`   // 结束时间 HH:MM
	Status    int    `json:"status"`    // 状态：1-正常，0-停诊
}

// 从HIS导入医生请求
type ImportDoctorsFromHISRequest struct {
	Source string `json:"source"` // 来源：abc, kl 等
}

// 导入医生请求
type ImportDoctorsRequest struct {
	File *multipart.FileHeader `form:"file,optional"`
}

// 导入结果的错误记录
type ImportErrorRecord struct {
	RowNum  int      `json:"rowNum"`
	Errors  []string `json:"errors"`
	RawData []string `json:"rawData"`
}

// DoctorHandler 医生处理器
type DoctorHandler struct {
	svcCtx *svc.ServiceContext
}

// NewDoctorHandler 创建医生处理器
func NewDoctorHandler(svcCtx *svc.ServiceContext) *DoctorHandler {
	return &DoctorHandler{
		svcCtx: svcCtx,
	}
}

// ListDoctors 获取医生列表
func (h *DoctorHandler) ListDoctors(w http.ResponseWriter, r *http.Request) {
	var req DoctorListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 默认页码和每页数量
	page := req.PageRequest.Page
	if page <= 0 {
		page = 1
	}
	size := req.PageRequest.Size
	if size <= 0 {
		size = 10
	}

	logx.Infof("获取医生列表: page=%d, size=%d, name=%s, mobile=%s, hospital=%s, department=%s, isRecommended=%v, sortBy=%s, sortOrder=%s",
		page, size, req.Name, req.Mobile, req.Hospital, req.Department, req.IsRecommended, req.SortBy, req.SortOrder)

	// 创建医生仓库
	doctorRepo := doctorModel.NewWxDoctorRepository(mysql.Slave())
	// 创建医生标签关系仓库
	doctorTagRepo := doctorModel.NewDoctorTagRepository(mysql.Slave())

	// 获取医生列表
	doctors, total, err := doctorRepo.List(page, size, req.Name, req.Mobile, req.Hospital, req.Department, req.IsRecommended, req.SortBy, req.SortOrder)
	if err != nil {
		logx.Errorf("获取医生列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取医生列表失败"))
		return
	}

	// 处理医生数据
	result := make([]map[string]interface{}, 0, len(doctors))
	for _, doctor := range doctors {
		// 获取医生标签
		tags, _ := doctorTagRepo.GetDoctorTags(doctor.DoctorID)
		tagIDs := make([]uint, 0, len(tags))
		tagNames := make([]string, 0, len(tags))
		for _, tag := range tags {
			tagIDs = append(tagIDs, tag.ID)
			tagNames = append(tagNames, tag.Name)
		}

		// 获取门店信息，直接从关联数据中提取
		storeInfos, _ := h.getStoreInfo(doctor.Stores)

		doctorMap := map[string]interface{}{
			"doctorId":       doctor.DoctorID,
			"userId":         doctor.UserID,
			"name":           doctor.Name,
			"mobile":         doctor.Mobile,
			"specialty":      doctor.Specialty,
			"hospital":       doctor.Hospital,
			"department":     doctor.Department,
			"title":          doctor.Title,
			"introduction":   doctor.Introduction,
			"stores":         storeInfos,
			"isRecommended":  doctor.IsRecommended,
			"recommendOrder": doctor.RecommendOrder,
			"tagIds":         tagIDs,
			"tagNames":       tagNames,
			"status":         doctor.Status,
			"createdAt":      doctor.CreatedAt,
			"updatedAt":      doctor.UpdatedAt,
		}

		result = append(result, doctorMap)
	}

	logx.Infof("获取医生列表成功: 共%d条记录", len(result))

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取医生列表成功"))
}

// GetDoctor 获取医生详情
func (h *DoctorHandler) GetDoctor(w http.ResponseWriter, r *http.Request) {
	var req DoctorDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取医生详情: doctorId=%d", req.DoctorID)

	// 创建医生仓库
	doctorRepo := doctorModel.NewWxDoctorRepository(mysql.Slave())
	// 创建医生标签关系仓库
	doctorTagRepo := doctorModel.NewDoctorTagRepository(mysql.Slave())

	// 获取医生信息
	doctor, err := doctorRepo.FindByID(req.DoctorID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "医生不存在"))
		} else {
			logx.Errorf("获取医生详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取医生详情失败"))
		}
		return
	}

	// 获取医生标签
	tags, _ := doctorTagRepo.GetDoctorTags(doctor.DoctorID)
	tagIDs := make([]uint, 0, len(tags))
	tagNames := make([]string, 0, len(tags))
	for _, tag := range tags {
		tagIDs = append(tagIDs, tag.ID)
		tagNames = append(tagNames, tag.Name)
	}

	// 获取门店信息
	storeInfos, _ := h.getStoreInfo(doctor.Stores)

	// 构建结果
	result := map[string]interface{}{
		"doctorId":       doctor.DoctorID,
		"userId":         doctor.UserID,
		"name":           doctor.Name,
		"mobile":         doctor.Mobile,
		"medicalLicense": doctor.MedicalLicense,
		"specialty":      doctor.Specialty,
		"hospital":       doctor.Hospital,
		"department":     doctor.Department,
		"title":          doctor.Title,
		"introduction":   doctor.Introduction,
		"stores":         storeInfos,
		"isRecommended":  doctor.IsRecommended,
		"recommendOrder": doctor.RecommendOrder,
		"tagIds":         tagIDs,
		"tagNames":       tagNames,
		"status":         doctor.Status,
		"createdAt":      doctor.CreatedAt,
		"updatedAt":      doctor.UpdatedAt,
	}

	logx.Infof("获取医生详情成功: ID=%d", doctor.DoctorID)

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取医生详情成功"))
}

// CreateDoctor 创建医生
func (h *DoctorHandler) CreateDoctor(w http.ResponseWriter, r *http.Request) {
	var req CreateDoctorRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		logx.Errorf("创建医生失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("创建医生请求: nickname=%s, mobile=%s, department=%s", req.Nickname, req.Mobile, req.Department)

	// 参数验证
	if req.Nickname == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "医生姓名不能为空"))
		return
	}
	if req.Mobile == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "手机号不能为空"))
		return
	}
	if req.Department == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "科室不能为空"))
		return
	}
	if req.Hospital == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "所属医院/诊所不能为空"))
		return
	}
	if len(req.StoreIDs) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "至少关联一个门店"))
		return
	}
	if len(req.StoreIDs) > 5 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "最多关联5个门店"))
		return
	}

	// 创建所需的仓库
	doctorRepo := doctorModel.NewWxDoctorRepository(mysql.Master())
	doctorTagRepo := doctorModel.NewDoctorTagRepository(mysql.Master())

	// 获取当前管理员ID作为创建人
	var creatorID uint = 0
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if adminID, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			creatorID = uint(adminID)
		}
	}

	// 用于存储创建的医生ID
	var createdDoctorID uint

	// 事务处理
	err := mysql.Master().Transaction(func(tx *gorm.DB) error {
		// 直接创建医生，不再创建WxUser
		now := time.Now()
		doctor := &doctorModel.WxDoctor{
			Name:           req.Nickname,
			Mobile:         req.Mobile,
			MedicalLicense: req.MedicalLicense,
			Specialty:      req.Specialty,
			Hospital:       req.Hospital,
			Department:     req.Department,
			Title:          req.Title,
			Introduction:   req.Introduction,
			IsRecommended:  req.IsRecommended,
			RecommendOrder: req.RecommendOrder,
			CreatorID:      creatorID,
			Status:         req.Status,
			CreatedAt:      now,
			UpdatedAt:      now,
		}

		if err := doctorRepo.Create(doctor); err != nil {
			logx.Errorf("创建医生失败: %v", err)
			return fmt.Errorf("创建医生失败")
		}

		// 保存医生ID以便事务外部使用
		createdDoctorID = doctor.DoctorID

		// 关联门店
		if len(req.StoreIDs) > 0 {
			if err := doctorRepo.SetStores(doctor, req.StoreIDs, req.PrimaryStoreID); err != nil {
				logx.Errorf("关联医生门店失败: %v", err)
				return fmt.Errorf("关联医生门店失败")
			}
		}

		// 处理医生标签关联
		if len(req.TagIDs) > 0 {
			if err := doctorTagRepo.AddDoctorTags(doctor.DoctorID, req.TagIDs); err != nil {
				logx.Errorf("关联医生标签失败: %v", err)
				return fmt.Errorf("关联医生标签失败")
			}
		}

		return nil
	})

	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	logx.Infof("医生创建成功: 姓名=%s", req.Nickname)

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// if adminID, ok := r.Context().Value("admin_id").(string); ok {
	// 	logContent := fmt.Sprintf("创建医生[%s]", req.Nickname)
	// 	h.logAdminOperation(r, adminID, "医生", "创建", createdDoctorID, "Doctor", logContent)
	// }

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"doctorId": createdDoctorID,
	}, "创建医生成功"))
}

// UpdateDoctor 更新医生
func (h *DoctorHandler) UpdateDoctor(w http.ResponseWriter, r *http.Request) {
	var req UpdateDoctorRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("更新医生信息失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("更新医生信息请求: doctorId=%d", req.DoctorID)

	// 创建所需的仓库
	doctorRepo := doctorModel.NewWxDoctorRepository(mysql.Master())
	doctorTagRepo := doctorModel.NewDoctorTagRepository(mysql.Master())

	// 检查医生是否存在
	doctor, err := doctorRepo.FindByID(req.DoctorID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "医生不存在"))
		} else {
			logx.Errorf("获取医生失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新医生信息失败"))
		}
		return
	}

	// 事务处理
	err = mysql.Master().Transaction(func(tx *gorm.DB) error {
		// 更新医生信息
		updateDoctorPerformed := false
		if req.Nickname != "" && req.Nickname != doctor.Name {
			doctor.Name = req.Nickname
			updateDoctorPerformed = true
		}
		if req.MedicalLicense != "" && req.MedicalLicense != doctor.MedicalLicense {
			doctor.MedicalLicense = req.MedicalLicense
			updateDoctorPerformed = true
		}
		if req.Specialty != "" && req.Specialty != doctor.Specialty {
			doctor.Specialty = req.Specialty
			updateDoctorPerformed = true
		}
		if req.Hospital != "" && req.Hospital != doctor.Hospital {
			doctor.Hospital = req.Hospital
			updateDoctorPerformed = true
		}
		if req.Department != "" && req.Department != doctor.Department {
			doctor.Department = req.Department
			updateDoctorPerformed = true
		}
		if req.Title != "" && req.Title != doctor.Title {
			doctor.Title = req.Title
			updateDoctorPerformed = true
		}
		if req.Introduction != "" && req.Introduction != doctor.Introduction {
			doctor.Introduction = req.Introduction
			updateDoctorPerformed = true
		}
		if req.IsRecommended != nil && *req.IsRecommended != doctor.IsRecommended {
			doctor.IsRecommended = *req.IsRecommended
			updateDoctorPerformed = true
		}
		if req.RecommendOrder != nil && *req.RecommendOrder != doctor.RecommendOrder {
			doctor.RecommendOrder = *req.RecommendOrder
			updateDoctorPerformed = true
		}
		if req.Status != 0 || doctor.Status != 0 { // 考虑零值问题
			if req.Status != doctor.Status {
				doctor.Status = req.Status
				updateDoctorPerformed = true
			}
		}

		if updateDoctorPerformed {
			doctor.UpdatedAt = time.Now()
			if err := doctorRepo.Update(doctor); err != nil {
				logx.Errorf("更新医生信息失败: %v", err)
				return fmt.Errorf("更新医生信息失败")
			}
		}

		// 更新医生门店关联
		if req.StoreIDs != nil && len(req.StoreIDs) > 0 {
			if err := doctorRepo.SetStores(doctor, req.StoreIDs, req.PrimaryStoreID); err != nil {
				logx.Errorf("更新医生门店关联失败: %v", err)
				return fmt.Errorf("更新医生门店关联失败")
			}
		}

		// 更新医生标签
		if req.TagIDs != nil {
			if err := doctorTagRepo.AddDoctorTags(doctor.DoctorID, req.TagIDs); err != nil {
				logx.Errorf("更新医生标签失败: %v", err)
				return fmt.Errorf("更新医生标签失败")
			}
		}

		return nil
	})

	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	logx.Infof("医生信息更新成功: ID=%d", doctor.DoctorID)

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// if adminID, ok := r.Context().Value("admin_id").(string); ok {
	// 	logContent := fmt.Sprintf("更新医生[%s]信息", doctor.Name)
	// 	h.logAdminOperation(r, adminID, "医生", "更新", doctor.DoctorID, "Doctor", logContent)
	// }

	httpx.OkJson(w, types.NewSuccessResponse(nil, "更新医生信息成功"))
}

// UpdateDoctorStatus 更新医生状态
func (h *DoctorHandler) UpdateDoctorStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdateDoctorStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("更新医生状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 验证状态值
	if req.Status != 0 && req.Status != 1 {
		logx.Errorf("更新医生状态失败: 无效的状态值 %d", req.Status)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "状态值无效，只能是0或1"))
		return
	}

	logx.Infof("更新医生状态请求: doctorId=%d, status=%d", req.DoctorID, req.Status)

	// 创建医生仓库
	doctorRepo := doctorModel.NewWxDoctorRepository(mysql.Master())

	// 检查医生是否存在
	doctor, err := doctorRepo.FindByID(req.DoctorID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "医生不存在"))
		} else {
			logx.Errorf("获取医生失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新医生状态失败"))
		}
		return
	}

	// 如果状态没有变化，则不需要更新
	if doctor.Status == req.Status {
		logx.Infof("医生状态已经是 %d，无需更新", req.Status)
		statusText := "启用"
		if req.Status == 0 {
			statusText = "禁用"
		}
		httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
			"message": fmt.Sprintf("医生已处于%s状态", statusText),
			"status":  req.Status,
		}, fmt.Sprintf("医生已处于%s状态", statusText)))
		return
	}

	// 更新医生状态
	doctor.Status = req.Status
	doctor.UpdatedAt = time.Now()
	if err := doctorRepo.Update(doctor); err != nil {
		logx.Errorf("更新医生状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新医生状态失败"))
		return
	}

	statusText := "启用"
	if req.Status == 0 {
		statusText = "禁用"
	}

	logx.Infof("医生状态更新成功: ID=%d, 状态=%d", req.DoctorID, req.Status)

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// if adminID, ok := r.Context().Value("admin_id").(string); ok {
	// 	userName := doctor.Name
	// 	logContent := fmt.Sprintf("%s医生: %s (ID: %d)", statusText, userName, req.DoctorID)
	// 	h.logAdminOperation(r, adminID, "医生", "更新状态", req.DoctorID, "Doctor", logContent)
	// }

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"message": fmt.Sprintf("医生%s成功", statusText),
		"status":  req.Status,
	}, fmt.Sprintf("医生%s成功", statusText)))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *DoctorHandler) logAdminOperation(r *http.Request, adminID, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 管理员ID=%s, 模块=%s, 操作=%s", adminID, module, action)
}

// getStoreInfo 获取门店信息
func (h *DoctorHandler) getStoreInfo(stores []storeModel.Store) ([]map[string]interface{}, error) {
	if len(stores) == 0 {
		return []map[string]interface{}{}, nil
	}

	// 提取门店信息
	result := make([]map[string]interface{}, 0, len(stores))
	for _, store := range stores {
		result = append(result, map[string]interface{}{
			"id":   store.ID,
			"name": store.Name,
		})
	}

	return result, nil
}

// ImportDoctors 导入医生
func (h *DoctorHandler) ImportDoctors(w http.ResponseWriter, r *http.Request) {
	// 解析表单
	if err := r.ParseMultipartForm(10 << 20); err != nil { // 最大10MB
		logx.Errorf("解析表单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "解析表单失败"))
		return
	}

	// 获取管理员信息
	adminID, err := GetAdminIDFromContext(r)
	if err != nil {
		logx.Errorf("获取管理员信息失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未登录或会话已过期"))
		return
	}

	logx.Infof("管理员 %d 正在导入医生数据", adminID)

	// 获取上传文件
	file, header, err := r.FormFile("file")
	if err != nil {
		logx.Errorf("获取上传文件失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "获取上传文件失败"))
		return
	}
	defer file.Close()

	logx.Infof("接收到文件: %s, 大小: %d字节", header.Filename, header.Size)

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(header.Filename))
	if ext != ".xlsx" && ext != ".xls" {
		logx.Errorf("不支持的文件格式: %s", ext)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "仅支持Excel文件(.xlsx, .xls)"))
		return
	}

	// 读取Excel文件
	xlsx, err := excelize.OpenReader(file)
	if err != nil {
		logx.Errorf("打开Excel文件失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "打开Excel文件失败"))
		return
	}
	defer xlsx.Close()

	// 获取工作表名称
	sheetList := xlsx.GetSheetList()
	if len(sheetList) == 0 {
		logx.Errorf("Excel文件不包含任何工作表")
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "Excel文件不包含任何工作表"))
		return
	}

	sheetName := sheetList[0]
	logx.Infof("正在处理工作表: %s", sheetName)

	// 获取所有门店，用于根据名称查找门店ID
	var stores []storeModel.Store
	if err := mysql.Slave().Find(&stores).Error; err != nil {
		logx.Errorf("获取门店列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取门店列表失败"))
		return
	}

	logx.Infof("已获取 %d 个门店信息", len(stores))

	// 创建门店名称到ID的映射
	storeNameMap := make(map[string]uint)
	for _, store := range stores {
		storeNameMap[store.Name] = store.ID
	}

	// 读取行
	rows, err := xlsx.GetRows(sheetName)
	if err != nil {
		logx.Errorf("读取Excel数据失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "读取Excel数据失败"))
		return
	}

	logx.Infof("Excel文件包含 %d 行数据", len(rows))

	if len(rows) <= 1 { // 至少需要标题行和一行数据
		logx.Errorf("Excel文件内容为空或格式不正确，行数: %d", len(rows))
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "Excel文件内容为空或格式不正确"))
		return
	}

	// 打印标题行，用于调试
	logx.Infof("标题行内容: %v", rows[0])

	// 解析标题行，确认所需字段存在
	headers := rows[0]
	nameIdx, mobileIdx, departmentIdx, titleIdx, hospitalIdx, storeIdx, avatarIdx, introIdx, specialtyIdx := -1, -1, -1, -1, -1, -1, -1, -1, -1

	for i, header := range headers {
		trimmedHeader := strings.TrimSpace(header)
		logx.Infof("检查标题[%d]: '%s'", i, trimmedHeader)

		// 移除标题中的星号
		trimmedHeader = strings.TrimSuffix(trimmedHeader, "*")

		switch trimmedHeader {
		case "医生姓名":
			nameIdx = i
			logx.Infof("找到医生姓名列: %d", i)
		case "手机号":
			mobileIdx = i
			logx.Infof("找到手机号列: %d", i)
		case "科室":
			departmentIdx = i
			logx.Infof("找到科室列: %d", i)
		case "职称":
			titleIdx = i
			logx.Infof("找到职称列: %d", i)
		case "所在医院":
			hospitalIdx = i
			logx.Infof("找到所在医院列: %d", i)
		case "所属门店":
			storeIdx = i
			logx.Infof("找到所属门店列: %d", i)
		case "医生头像":
			avatarIdx = i
			logx.Infof("找到医生头像列: %d", i)
		case "简介":
			introIdx = i
			logx.Infof("找到简介列: %d", i)
		default:
			// 特殊处理擅长字段，因为它可能有各种变体
			if strings.Contains(trimmedHeader, "擅长") {
				specialtyIdx = i
				logx.Infof("找到擅长列: %d (字段名: %s)", i, trimmedHeader)
			}
		}
	}

	// 检查必填字段索引
	requiredFields := []struct {
		name string
		idx  int
	}{
		{"医生姓名", nameIdx},
		{"手机号", mobileIdx},
		{"科室", departmentIdx},
		{"职称", titleIdx},
		{"所在医院", hospitalIdx},
		{"所属门店", storeIdx},
	}

	missingFields := []string{}
	for _, field := range requiredFields {
		if field.idx == -1 {
			missingFields = append(missingFields, field.name)
			logx.Errorf("缺少必填字段: %s", field.name)
		}
	}

	if len(missingFields) > 0 {
		errorMsg := fmt.Sprintf("Excel文件缺少必填字段: %s", strings.Join(missingFields, ", "))
		logx.Errorf(errorMsg)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, errorMsg))
		return
	}

	// 处理数据行
	successCount := 0
	errorRecords := []ImportErrorRecord{}

	for rowIdx, row := range rows {
		// 跳过标题行
		if rowIdx == 0 {
			continue
		}

		logx.Infof("处理第 %d 行数据", rowIdx+1)

		// 如果行为空或者所有单元格都为空，则跳过
		isEmpty := true
		for _, cell := range row {
			if strings.TrimSpace(cell) != "" {
				isEmpty = false
				break
			}
		}
		if isEmpty {
			logx.Infof("第 %d 行为空，跳过", rowIdx+1)
			continue
		}

		// 确保行有足够的列数
		maxIndex := maxIdx(nameIdx, mobileIdx, departmentIdx, titleIdx, hospitalIdx, storeIdx, avatarIdx, introIdx, specialtyIdx)
		if len(row) <= maxIndex {
			logx.Infof("第 %d 行列数不足，当前: %d, 需要: %d, 自动补充", rowIdx+1, len(row), maxIndex+1)
			// 补充缺少的列
			for len(row) <= maxIndex {
				row = append(row, "")
			}
		}

		// 验证必填字段
		errors := []string{}

		// 获取字段值
		name := strings.TrimSpace(row[nameIdx])
		mobile := strings.TrimSpace(row[mobileIdx])
		department := strings.TrimSpace(row[departmentIdx])
		title := strings.TrimSpace(row[titleIdx])
		hospital := strings.TrimSpace(row[hospitalIdx])
		storeName := strings.TrimSpace(row[storeIdx])

		logx.Infof("第 %d 行数据: 医生=%s, 手机=%s, 科室=%s, 职称=%s, 医院=%s, 门店=%s",
			rowIdx+1, name, mobile, department, title, hospital, storeName)

		// 验证必填字段
		if name == "" {
			errors = append(errors, "医生姓名不能为空")
		}

		if mobile == "" {
			errors = append(errors, "手机号不能为空")
		} else if !IsMobileValid(mobile) {
			errors = append(errors, "手机号格式不正确")
		}

		if department == "" {
			errors = append(errors, "科室不能为空")
		}

		if title == "" {
			errors = append(errors, "职称不能为空")
		}

		if hospital == "" {
			errors = append(errors, "所在医院不能为空")
		}

		if storeName == "" {
			errors = append(errors, "所属门店不能为空")
		}

		// 检查手机号是否已存在
		var existingDoctor doctorModel.WxDoctor
		if mobile != "" {
			err := mysql.Slave().Where("mobile = ?", mobile).First(&existingDoctor).Error
			if err == nil {
				errors = append(errors, fmt.Sprintf("手机号 %s 已存在(医生: %s)", mobile, existingDoctor.Name))
				logx.Errorf("第 %d 行: 手机号 %s 已存在(医生: %s)", rowIdx+1, mobile, existingDoctor.Name)
			}
		}

		// 验证门店是否存在
		var storeID uint
		var ok bool
		if storeName != "" {
			storeID, ok = storeNameMap[storeName]
			if !ok {
				errors = append(errors, fmt.Sprintf("门店 %s 不存在", storeName))
				logx.Errorf("第 %d 行: 门店 %s 不存在", rowIdx+1, storeName)
			}
		}

		// 如果有错误，添加到错误记录中并跳过此行
		if len(errors) > 0 {
			logx.Errorf("第 %d 行存在错误: %v", rowIdx+1, errors)
			errorRecords = append(errorRecords, ImportErrorRecord{
				RowNum:  rowIdx + 1, // Excel行号从1开始
				Errors:  errors,
				RawData: row,
			})
			continue
		}

		// 为当前行创建独立事务，每行单独处理
		err := mysql.Master().Transaction(func(tx *gorm.DB) error {
			// 创建医生仓库
			doctorRepo := doctorModel.NewWxDoctorRepository(tx)

			// 获取可选字段
			var avatarUrl, intro, specialty string
			if avatarIdx >= 0 && avatarIdx < len(row) {
				avatarUrl = strings.TrimSpace(row[avatarIdx])
			}

			if introIdx >= 0 && introIdx < len(row) {
				intro = strings.TrimSpace(row[introIdx])
			}

			if specialtyIdx >= 0 && specialtyIdx < len(row) {
				specialty = strings.TrimSpace(row[specialtyIdx])
			}

			logx.Infof("第 %d 行可选字段: 头像=%s, 简介=%s, 擅长=%s",
				rowIdx+1, avatarUrl, intro, specialty)

			// 创建医生对象
			now := time.Now()
			doctor := &doctorModel.WxDoctor{
				Name:         name,
				Mobile:       mobile,
				Department:   department,
				Title:        title,
				Hospital:     hospital,
				Introduction: intro,
				Specialty:    specialty,
				// Avatar字段不存在，暂不设置头像
				Status:    1, // 默认启用
				CreatorID: adminID,
				CreatedAt: now,
				UpdatedAt: now,
			}

			// 保存医生信息
			if err := doctorRepo.Create(doctor); err != nil {
				logx.Errorf("保存医生失败: %v", err)
				return fmt.Errorf("保存医生失败: %v", err)
			}

			logx.Infof("第 %d 行: 成功创建医生 ID=%d", rowIdx+1, doctor.DoctorID)

			// 设置医生关联的门店
			storeIDs := []uint{storeID}
			if err := doctorRepo.SetStores(doctor, storeIDs, storeID); err != nil {
				logx.Errorf("设置医生门店关联失败: %v", err)
				return fmt.Errorf("设置医生门店关联失败: %v", err)
			}

			logx.Infof("第 %d 行: 成功设置医生关联门店 storeID=%d", rowIdx+1, storeID)

			// 设置医生擅长领域为标签
			if specialty != "" {
				specialties := strings.Split(specialty, ",")
				for _, s := range specialties {
					s = strings.TrimSpace(s)
					if s == "" {
						continue
					}

					// 创建或获取标签
					tag, err := createOrGetTag(tx, s)
					if err != nil {
						logx.Errorf("创建标签失败: %v", err)
						continue
					}

					// 添加标签关联
					if err := addTagToDoctor(tx, doctor.DoctorID, tag.ID); err != nil {
						logx.Errorf("添加医生标签关联失败: %v", err)
						continue
					}

					logx.Infof("第 %d 行: 成功添加标签 '%s' (ID=%d) 到医生", rowIdx+1, s, tag.ID)
				}
			}

			return nil
		})

		if err != nil {
			// 如果这一行处理失败，将其添加到错误记录
			errorRecords = append(errorRecords, ImportErrorRecord{
				RowNum:  rowIdx + 1,
				Errors:  []string{err.Error()},
				RawData: row,
			})
			logx.Errorf("第 %d 行处理失败: %v", rowIdx+1, err)
		} else {
			// 这一行处理成功
			successCount++
			logx.Infof("第 %d 行处理成功", rowIdx+1)
		}
	}

	logx.Infof("导入完成: 成功 %d 条, 失败 %d 条", successCount, len(errorRecords))

	// 返回处理结果
	if len(errorRecords) == 0 {
		// 全部成功
		httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
			"successCount": successCount,
			"errorCount":   0,
			"errors":       []ImportErrorRecord{},
		}, "导入医生成功"))
	} else if successCount > 0 {
		// 部分成功，部分失败
		msg := fmt.Sprintf("部分导入成功: %d条成功, %d条失败", successCount, len(errorRecords))
		response := types.NewSuccessResponse(map[string]interface{}{
			"successCount": successCount,
			"errorCount":   len(errorRecords),
			"errors":       errorRecords,
		}, msg)
		httpx.OkJson(w, response)
	} else {
		// 全部失败
		errorMsg := fmt.Sprintf("导入失败，发现%d条错误记录", len(errorRecords))
		response := types.NewErrorResponse(types.CodeInvalidParams, errorMsg)
		response.Data = map[string]interface{}{
			"successCount": 0,
			"errorCount":   len(errorRecords),
			"errors":       errorRecords,
		}
		httpx.OkJson(w, response)
	}

	// 记录管理操作 - 已由中间件统一处理，无需手动记录
	// h.logAdminOperation(r, fmt.Sprintf("%d", adminID), "医生管理", "导入医生", 0, "医生",
	// 	fmt.Sprintf("导入医生，成功%d条，失败%d条", successCount, len(errorRecords)))
}

// maxIdx 获取最大索引值
func maxIdx(indices ...int) int {
	max := -1
	for _, idx := range indices {
		if idx > max {
			max = idx
		}
	}
	return max
}

// IsMobileValid 验证手机号是否有效
func IsMobileValid(mobile string) bool {
	// 简单的中国大陆手机号验证
	pattern := `^1[3-9]\d{9}$`
	matched, _ := regexp.MatchString(pattern, mobile)
	return matched
}

// GetAdminIDFromContext 从请求上下文中获取管理员ID
func GetAdminIDFromContext(r *http.Request) (uint, error) {
	adminIDStr, ok := r.Context().Value("admin_id").(string)
	if !ok || adminIDStr == "" {
		return 0, fmt.Errorf("管理员ID不存在")
	}

	adminID, err := strconv.ParseUint(adminIDStr, 10, 32)
	if err != nil {
		return 0, fmt.Errorf("解析管理员ID失败: %v", err)
	}

	return uint(adminID), nil
}

// createOrGetTag 创建或获取标签
func createOrGetTag(tx *gorm.DB, name string) (*userModel.Tag, error) {
	// 查找是否存在标签
	var tag userModel.Tag
	if err := tx.Where("name = ?", name).First(&tag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新标签
			tag = userModel.Tag{
				Name:      name,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			if err := tx.Create(&tag).Error; err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	return &tag, nil
}

// addTagToDoctor 添加标签到医生
func addTagToDoctor(tx *gorm.DB, doctorID, tagID uint) error {
	// 检查关联是否已存在
	var count int64
	if err := tx.Table("doctor_tag").Where("doctor_id = ? AND tag_id = ?", doctorID, tagID).Count(&count).Error; err != nil {
		return err
	}

	// 如果不存在则添加
	if count == 0 {
		if err := tx.Exec("INSERT INTO doctor_tag (doctor_id, tag_id) VALUES (?, ?)", doctorID, tagID).Error; err != nil {
			return err
		}
	}

	return nil
}
