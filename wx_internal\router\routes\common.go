package routes

import (
	"net/http"
	"yekaitai/wx_internal/modules/order/handler"
	"yekaitai/wx_internal/svc"
	"yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// RestServer 定义路由服务器接口
type RestServer interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
}

// RegisterCommonRoutes 注册公共路由
func RegisterCommonRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.WxServiceContext) {
	// 创建订单处理器
	orderHandlerInst := handler.NewOrderGoZeroHandler()
	if orderHandlerInst == nil {
		panic("订单处理器初始化失败")
	}

	// 创建万里牛回调处理器
	// wanliniuCallbackHandler := wanliniuHandler.NewWanLiNiuCallbackHandler()

	// 健康检查路由
	server.AddRoutes([]rest.Route{
		{
			Method:  http.MethodGet,
			Path:    "/api/health",
			Handler: HealthCheckHandler(serverCtx),
		},
		{
			Method: http.MethodGet,
			Path:   "/api/info",
			Handler: func(w http.ResponseWriter, r *http.Request) {
				httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(map[string]string{
					"name":    "叶开泰小程序API",
					"version": "1.0.0",
				}))
			},
		},

		// 微信支付回调通知（无需认证）
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/wechat/pay/notify",
			Handler: orderHandlerInst.HandleWechatPayNotify,
		},
		// 微信退款回调通知（无需认证）
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/wechat/refund/notify",
			Handler: orderHandlerInst.HandleWechatRefundNotify,
		},
		// 万里牛订单发货通知（无需认证）不使用回调了,主动查询
		// {
		// 	Method:  http.MethodPost,
		// 	Path:    "/api/erp/callback/tradeSendMsg",
		// 	Handler: wanliniuCallbackHandler.HandleTradeShipment,
		// },
		// // 万里牛库存变更通知（无需认证）
		// {
		// 	Method:  http.MethodPost,
		// 	Path:    "/api/erp/callback/itemQuantityUploadMsg",
		// 	Handler: wanliniuCallbackHandler.HandleInventoryChange,
		// },
	})
}

// HealthCheckHandler 健康检查处理器
func HealthCheckHandler(serverCtx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse("系统正常运行"))
	}
}
