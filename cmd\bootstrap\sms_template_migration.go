package bootstrap

import (
	"yekaitai/internal/modules/sms_setup/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateSmsTemplateTables 执行短信模板模块表结构迁移
func MigrateSmsTemplateTables() error {
	db := mysql.Master()

	logx.Info("开始执行短信模板模块表结构迁移...")

	// 自动迁移短信模板表
	if err := db.AutoMigrate(&model.SmsTemplate{}); err != nil {
		logx.Errorf("短信模板表迁移失败: %v", err)
		return err
	}
	logx.Info("短信模板表迁移完成")

	logx.Info("短信模板模块表结构迁移完成")
	return nil
}
