package routes

import (
	"net/http"
	"yekaitai/internal/modules/service_package_recommend/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterServicePackageRecommendRoutes 注册推荐服务套餐路由
func RegisterServicePackageRecommendRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	handler := handler.NewServicePackageRecommendHandler()

	// 推荐服务套餐管理路由
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/service-package-recommends",
				Handler: handler.GetServicePackageRecommends,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/service-package-recommends",
				Handler: handler.CreateServicePackageRecommend,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/service-package-recommends/:id",
				Handler: handler.UpdateServicePackageRecommend,
			},
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/service-package-recommends/:id",
				Handler: handler.DeleteServicePackageRecommend,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/service-package-recommends/batch-create",
				Handler: handler.BatchCreateServicePackageRecommends,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/service-package-recommends/batch-delete",
				Handler: handler.BatchDeleteServicePackageRecommends,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/service-package-recommends/:id/set-top",
				Handler: handler.SetTopServicePackageRecommend,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/service-package-recommends/:id/cancel-top",
				Handler: handler.CancelTopServicePackageRecommend,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/service-package-select",
				Handler: handler.GetServicePackageSelectList,
			},
		},
	)
}
