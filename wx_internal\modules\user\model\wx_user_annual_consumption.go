package model

import (
	"time"

	"gorm.io/gorm"
)

// WxUserAnnualConsumption 微信用户年度消费记录
type WxUserAnnualConsumption struct {
	ID                 uint           `gorm:"primaryKey;type:bigint unsigned;autoIncrement;comment:记录ID" json:"id"`
	UserID             uint           `gorm:"type:bigint unsigned;not null;index;comment:用户ID" json:"user_id"`
	Year               int            `gorm:"type:int;not null;comment:消费年份" json:"year"`
	FirstConsumeDate   *time.Time     `gorm:"type:date;comment:首次消费日期" json:"first_consume_date"`
	ProductAmount      float64        `gorm:"type:decimal(10,2);default:0;comment:商品消费累计金额" json:"product_amount"`
	ServiceAmount      float64        `gorm:"type:decimal(10,2);default:0;comment:服务消费累计金额" json:"service_amount"`
	ActivityAmount     float64        `gorm:"type:decimal(10,2);default:0;comment:活动消费累计金额" json:"activity_amount"`
	TotalAmount        float64        `gorm:"type:decimal(10,2);default:0;comment:总消费累计金额" json:"total_amount"`
	LastProductOrderNo string         `gorm:"type:varchar(64);comment:最近商品订单号" json:"last_product_order_no"`
	LastServiceOrderNo string         `gorm:"type:varchar(64);comment:最近服务订单号" json:"last_service_order_no"`
	LastActivityNo     string         `gorm:"type:varchar(64);comment:最近活动订单号" json:"last_activity_no"`
	LastConsumeTime    *time.Time     `gorm:"type:datetime;comment:最近消费时间" json:"last_consume_time"`
	CreatedAt          time.Time      `gorm:"type:datetime;comment:创建时间" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"type:datetime;comment:更新时间" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"index;type:datetime;comment:删除时间" json:"-"`
}

// TableName 指定表名
func (WxUserAnnualConsumption) TableName() string {
	return "wx_user_annual_consumption"
}

// WxUserAnnualConsumptionRepository 用户年度消费记录仓库接口
type WxUserAnnualConsumptionRepository interface {
	// 基础CRUD
	Create(record *WxUserAnnualConsumption) error
	Update(record *WxUserAnnualConsumption) error
	Delete(id uint) error
	FindByID(id uint) (*WxUserAnnualConsumption, error)
	
	// 业务方法
	FindByUserIDAndYear(userID uint, year int) (*WxUserAnnualConsumption, error)
	GetUserCurrentYearConsumption(userID uint) (*WxUserAnnualConsumption, error)
	AddProductConsumption(userID uint, amount float64, orderNo string) error
	AddServiceConsumption(userID uint, amount float64, orderNo string) error
	AddActivityConsumption(userID uint, amount float64, activityNo string) error
	GetUserTotalConsumption(userID uint) (float64, error)
}

// wxUserAnnualConsumptionRepository 用户年度消费记录仓库实现
type wxUserAnnualConsumptionRepository struct {
	db *gorm.DB
}

// NewWxUserAnnualConsumptionRepository 创建用户年度消费记录仓库
func NewWxUserAnnualConsumptionRepository(db *gorm.DB) WxUserAnnualConsumptionRepository {
	return &wxUserAnnualConsumptionRepository{db: db}
}

// Create 创建记录
func (r *wxUserAnnualConsumptionRepository) Create(record *WxUserAnnualConsumption) error {
	return r.db.Create(record).Error
}

// Update 更新记录
func (r *wxUserAnnualConsumptionRepository) Update(record *WxUserAnnualConsumption) error {
	return r.db.Save(record).Error
}

// Delete 删除记录
func (r *wxUserAnnualConsumptionRepository) Delete(id uint) error {
	return r.db.Delete(&WxUserAnnualConsumption{}, id).Error
}

// FindByID 根据ID查找记录
func (r *wxUserAnnualConsumptionRepository) FindByID(id uint) (*WxUserAnnualConsumption, error) {
	var record WxUserAnnualConsumption
	err := r.db.First(&record, id).Error
	return &record, err
}

// FindByUserIDAndYear 根据用户ID和年份查找记录
func (r *wxUserAnnualConsumptionRepository) FindByUserIDAndYear(userID uint, year int) (*WxUserAnnualConsumption, error) {
	var record WxUserAnnualConsumption
	err := r.db.Where("user_id = ? AND year = ?", userID, year).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetUserCurrentYearConsumption 获取用户当前年份消费记录
func (r *wxUserAnnualConsumptionRepository) GetUserCurrentYearConsumption(userID uint) (*WxUserAnnualConsumption, error) {
	currentYear := time.Now().Year()
	record, err := r.FindByUserIDAndYear(userID, currentYear)
	if err != nil {
		// 如果记录不存在，创建一条新记录
		if err == gorm.ErrRecordNotFound {
			newRecord := &WxUserAnnualConsumption{
				UserID:          userID,
				Year:            currentYear,
				ProductAmount:   0,
				ServiceAmount:   0,
				ActivityAmount:  0,
				TotalAmount:     0,
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
			}
			if err := r.Create(newRecord); err != nil {
				return nil, err
			}
			return newRecord, nil
		}
		return nil, err
	}
	return record, nil
}

// AddProductConsumption 添加商品消费记录
func (r *wxUserAnnualConsumptionRepository) AddProductConsumption(userID uint, amount float64, orderNo string) error {
	record, err := r.GetUserCurrentYearConsumption(userID)
	if err != nil {
		return err
	}

	now := time.Now()
	
	// 首次消费时间
	if record.FirstConsumeDate == nil {
		record.FirstConsumeDate = &now
	}
	
	// 更新消费金额
	record.ProductAmount += amount
	record.TotalAmount += amount
	record.LastProductOrderNo = orderNo
	record.LastConsumeTime = &now
	record.UpdatedAt = now
	
	return r.Update(record)
}

// AddServiceConsumption 添加服务消费记录
func (r *wxUserAnnualConsumptionRepository) AddServiceConsumption(userID uint, amount float64, orderNo string) error {
	record, err := r.GetUserCurrentYearConsumption(userID)
	if err != nil {
		return err
	}

	now := time.Now()
	
	// 首次消费时间
	if record.FirstConsumeDate == nil {
		record.FirstConsumeDate = &now
	}
	
	// 更新消费金额
	record.ServiceAmount += amount
	record.TotalAmount += amount
	record.LastServiceOrderNo = orderNo
	record.LastConsumeTime = &now
	record.UpdatedAt = now
	
	return r.Update(record)
}

// AddActivityConsumption 添加活动消费记录
func (r *wxUserAnnualConsumptionRepository) AddActivityConsumption(userID uint, amount float64, activityNo string) error {
	record, err := r.GetUserCurrentYearConsumption(userID)
	if err != nil {
		return err
	}

	now := time.Now()
	
	// 首次消费时间
	if record.FirstConsumeDate == nil {
		record.FirstConsumeDate = &now
	}
	
	// 更新消费金额
	record.ActivityAmount += amount
	record.TotalAmount += amount
	record.LastActivityNo = activityNo
	record.LastConsumeTime = &now
	record.UpdatedAt = now
	
	return r.Update(record)
}

// GetUserTotalConsumption 获取用户当年总消费金额
func (r *wxUserAnnualConsumptionRepository) GetUserTotalConsumption(userID uint) (float64, error) {
	record, err := r.GetUserCurrentYearConsumption(userID)
	if err != nil {
		return 0, err
	}
	return record.TotalAmount, nil
} 