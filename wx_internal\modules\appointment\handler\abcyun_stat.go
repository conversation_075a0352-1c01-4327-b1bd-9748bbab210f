package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"yekaitai/internal/modules/appointment/types"
	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// AbcYunStatRevenueDetailTransactionHandler 收费明细-单据
func AbcYunStatRevenueDetailTransactionHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.StatBaseQueryRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/stat/revenue/detail/transaction"
	queryParams := map[string]string{
		"date": req.Date,
	}

	if req.Limit > 0 {
		queryParams["limit"] = fmt.Sprintf("%d", req.Limit)
	}
	if req.Offset > 0 {
		queryParams["offset"] = fmt.Sprintf("%d", req.Offset)
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.StatRevenueDetailTransaction `json:"rows"`
			Total  int                                  `json:"total"`
			Offset int                                  `json:"offset"`
			Limit  int                                  `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询收费明细-单据成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunStatRevenueDetailClassifyHandler 收费明细-分类
func AbcYunStatRevenueDetailClassifyHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.StatBaseQueryRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/stat/revenue/detail/classify"
	queryParams := map[string]string{
		"date": req.Date,
	}

	if req.Limit > 0 {
		queryParams["limit"] = fmt.Sprintf("%d", req.Limit)
	}
	if req.Offset > 0 {
		queryParams["offset"] = fmt.Sprintf("%d", req.Offset)
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.StatRevenueDetailClassify `json:"rows"`
			Total  int                               `json:"total"`
			Offset int                               `json:"offset"`
			Limit  int                               `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询收费明细-分类成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunStatRevenueDetailItemsHandler 收费明细-明细
func AbcYunStatRevenueDetailItemsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.StatRevenueDetailItemsRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/stat/revenue/detail/items"
	queryParams := map[string]string{
		"date": req.Date,
	}

	if req.Dimension > 0 {
		queryParams["dimension"] = fmt.Sprintf("%d", req.Dimension)
	}
	if req.Limit > 0 {
		queryParams["limit"] = fmt.Sprintf("%d", req.Limit)
	}
	if req.Offset > 0 {
		queryParams["offset"] = fmt.Sprintf("%d", req.Offset)
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.StatRevenueDetailItem `json:"rows"`
			Total  int                           `json:"total"`
			Offset int                           `json:"offset"`
			Limit  int                           `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询收费明细-明细成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunStatExecuteDetailItemsHandler 执行业绩-明细
func AbcYunStatExecuteDetailItemsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.StatBaseQueryRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/stat/execute/detail/items"
	queryParams := map[string]string{
		"date": req.Date,
	}

	if req.Limit > 0 {
		queryParams["limit"] = fmt.Sprintf("%d", req.Limit)
	}
	if req.Offset > 0 {
		queryParams["offset"] = fmt.Sprintf("%d", req.Offset)
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.StatExecuteDetailItem `json:"rows"`
			Total  int                           `json:"total"`
			Offset int                           `json:"offset"`
			Limit  int                           `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询执行业绩-明细成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunStatCardRechargeDetailItemsHandler 开卡充值业绩-明细
func AbcYunStatCardRechargeDetailItemsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.StatCardRechargeRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}
	if req.Limit <= 0 {
		httpx.Error(w, errors.New("每页显示条数不能为空"))
		return
	}
	if req.Offset < 0 {
		httpx.Error(w, errors.New("分页起始下标不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/stat/card-recharge/detail/items"
	queryParams := map[string]string{
		"date":   req.Date,
		"limit":  fmt.Sprintf("%d", req.Limit),
		"offset": fmt.Sprintf("%d", req.Offset),
	}

	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.StatCardRechargeDetailItem `json:"rows"`
			Total  int                                `json:"total"`
			Offset int                                `json:"offset"`
			Limit  int                                `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询开卡充值业绩-明细成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunStatMemberRechargeDetailItemsHandler 会员充值业绩-明细
func AbcYunStatMemberRechargeDetailItemsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.StatMemberRechargeRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}
	if req.Limit <= 0 {
		httpx.Error(w, errors.New("每页显示条数不能为空"))
		return
	}
	if req.Offset < 0 {
		httpx.Error(w, errors.New("分页起始下标不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/stat/member-recharge/detail/items"
	queryParams := map[string]string{
		"date":   req.Date,
		"limit":  fmt.Sprintf("%d", req.Limit),
		"offset": fmt.Sprintf("%d", req.Offset),
	}

	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.StatMemberRechargeDetailItem `json:"rows"`
			Total  int                                  `json:"total"`
			Offset int                                  `json:"offset"`
			Limit  int                                  `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询会员充值业绩-明细成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}
