package upload

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"time"

	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
	"github.com/zeromicro/go-zero/core/logx"
)

// QiniuUploader 七牛云上传器
type QiniuUploader struct {
	config QiniuConfig
	mac    *qbox.Mac
	bucket string
	domain string
}

// NewQiniuUploader 创建七牛云上传器
func NewQiniuUploader(config QiniuConfig) *QiniuUploader {
	mac := qbox.NewMac(config.AccessKey, config.SecretKey)
	return &QiniuUploader{
		config: config,
		mac:    mac,
		bucket: config.Bucket,
		domain: config.Domain,
	}
}

// GetZone 获取存储区域
func (u *QiniuUploader) GetZone() (*storage.Region, error) {
	switch u.config.Zone {
	case "z0":
		return &storage.ZoneHuadong, nil
	case "z1":
		return &storage.ZoneHuabei, nil
	case "z2":
		return &storage.ZoneHuanan, nil
	case "na0":
		return &storage.ZoneBeimei, nil
	case "as0":
		return &storage.ZoneXinjiapo, nil
	default:
		return nil, fmt.Errorf("不支持的区域: %s", u.config.Zone)
	}
}

// GetUploadToken 获取上传Token
func (u *QiniuUploader) GetUploadToken() string {
	// 默认策略，覆盖上传
	putPolicy := storage.PutPolicy{
		Scope: u.bucket,
	}
	// 设置过期时间: 1小时
	putPolicy.Expires = 3600
	return putPolicy.UploadToken(u.mac)
}

// UploadFile 上传文件
func (u *QiniuUploader) UploadFile(ctx context.Context, file *multipart.FileHeader, key string) (string, error) {
	// 打开源文件
	src, err := file.Open()
	if err != nil {
		logx.Errorf("打开文件失败: %v", err)
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer src.Close()

	// 读取文件内容
	data, err := io.ReadAll(src)
	if err != nil {
		logx.Errorf("读取文件内容失败: %v", err)
		return "", fmt.Errorf("读取文件内容失败: %w", err)
	}

	return u.UploadBytes(ctx, data, key)
}

// UploadBytes 上传字节数据
func (u *QiniuUploader) UploadBytes(ctx context.Context, data []byte, key string) (string, error) {
	if key == "" {
		return "", errors.New("文件key不能为空")
	}

	// 获取区域配置
	zone, err := u.GetZone()
	if err != nil {
		logx.Errorf("获取七牛云区域配置失败: %v", err)
		return "", err
	}

	// 获取上传Token
	upToken := u.GetUploadToken()

	// 配置上传参数
	cfg := storage.Config{
		Region:        zone,
		UseCdnDomains: false,
		UseHTTPS:      true,
	}

	// 创建表单上传器
	formUploader := storage.NewFormUploader(&cfg)
	ret := storage.PutRet{}

	// 设置额外参数
	putExtra := storage.PutExtra{
		Params: map[string]string{
			"x:timestamp": fmt.Sprintf("%d", time.Now().Unix()),
		},
	}

	// 执行上传
	err = formUploader.Put(ctx, &ret, upToken, key, bytes.NewReader(data), int64(len(data)), &putExtra)
	if err != nil {
		logx.Errorf("上传文件到七牛云失败: %v", err)
		return "", fmt.Errorf("上传文件失败: %w", err)
	}

	// 生成访问URL
	url := fmt.Sprintf("https://%s/%s", u.domain, ret.Key)
	logx.Infof("文件上传成功，URL: %s", url)
	return url, nil
}

// GenerateFileKey 根据文件信息生成唯一文件路径
func GenerateFileKey(file *multipart.FileHeader, prefix string) string {
	// 获取文件扩展名
	ext := filepath.Ext(file.Filename)
	// 生成唯一文件名: 前缀/时间戳-随机数.扩展名
	key := fmt.Sprintf("%s/%d-%s%s", prefix, time.Now().UnixNano(), RandomString(8), ext)
	return key
}

// DefaultQiniuUploader 默认的七牛云上传器实例
var DefaultQiniuUploader *QiniuUploader

// InitDefaultQiniuUploader 初始化默认七牛云上传器
func InitDefaultQiniuUploader() {
	logx.Info("初始化默认七牛云上传器")
	DefaultQiniuUploader = NewQiniuUploader(GetConfig().Qiniu)
}
