package handler

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"yekaitai/internal/modules/admin/service"
	"yekaitai/internal/modules/user_points/model"
	userPointsService "yekaitai/internal/modules/user_points/service"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// UserPointsRulesHandler 叶小币规则处理器
type UserPointsRulesHandler struct {
	rulesService *userPointsService.UserPointsRulesService
	logService   *service.AdminOperationLogService
}

// NewUserPointsRulesHandler 创建叶小币规则处理器
func NewUserPointsRulesHandler() *UserPointsRulesHandler {
	return &UserPointsRulesHandler{
		rulesService: userPointsService.NewUserPointsRulesService(mysql.GetDB()),
		logService:   service.NewAdminOperationLogService(),
	}
}

// SaveRules 批量保存积分规则
func (h *UserPointsRulesHandler) SaveRules(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req model.SaveRulesRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, types.NewCodeError(400, fmt.Sprintf("参数解析失败: %v", err)))
		return
	}

	// 使用模型自带的验证方法
	if err := req.Validate(); err != nil {
		logx.Errorf("参数验证失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, types.NewCodeError(400, err.Error()))
		return
	}

	// 记录操作开始
	logx.Infof("开始批量保存规则，等级ID: %d, 规则数量: %d, 是否同步所有等级: %v",
		req.LevelID, len(req.Rules), req.SyncAllLevel)

	// 保存规则
	err := h.rulesService.SaveRules(ctx, req)
	if err != nil {
		logx.Errorf("保存规则失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, types.NewCodeError(500, fmt.Sprintf("保存规则失败: %v", err)))
		return
	}

	// 记录操作日志
	h.logAdminOperation(r, "叶小币管理", "批量保存规则", uint(req.LevelID), "UserLevelRules", "批量保存叶小币规则配置")

	// 记录操作成功
	logx.Infof("批量保存规则成功，等级ID: %d, 规则数量: %d", req.LevelID, len(req.Rules))

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "保存成功",
		Data: model.SaveRulesResponse{
			Success: true,
			Message: "规则保存成功",
		},
	})
}

// GetRuleParameters 获取规则参数映射表
func (h *UserPointsRulesHandler) GetRuleParameters(w http.ResponseWriter, r *http.Request) {
	// 获取规则参数映射
	params := h.rulesService.GetRuleParameters()

	// 转换为响应格式
	result := make(map[string]map[string]bool)
	for ruleType, config := range params {
		result[ruleType] = map[string]bool{
			"has_min_amount":  config.HasMinAmount,
			"has_extra":       config.HasExtra,
			"has_amount_step": config.HasAmountStep,
		}
	}

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    result,
	})
}

// GetRules 获取规则配置
func (h *UserPointsRulesHandler) GetRules(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req model.GetRulesRequest
	if err := httpx.ParseForm(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, types.NewCodeError(400, fmt.Sprintf("参数解析失败: %v", err)))
		return
	}

	// 尝试从查询参数中获取level_id
	if req.LevelID <= 0 {
		levelIDStr := r.URL.Query().Get("level_id")
		if levelIDStr != "" {
			var err error
			req.LevelID, err = strconv.Atoi(levelIDStr)
			if err != nil {
				logx.Errorf("解析level_id失败: %v", err)
				httpx.ErrorCtx(r.Context(), w, types.NewCodeError(400, "无效的等级ID"))
				return
			}
		}
	}

	// 参数校验
	if req.LevelID <= 0 {
		logx.Errorf("参数校验失败: 等级ID不能为空")
		httpx.ErrorCtx(r.Context(), w, types.NewCodeError(400, "等级ID不能为空"))
		return
	}

	logx.Infof("开始获取规则配置，等级ID: %d", req.LevelID)

	// 获取规则
	rules, expiryPolicy, err := h.rulesService.GetRules(ctx, req.LevelID)
	if err != nil {
		logx.Errorf("获取规则失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, types.NewCodeError(500, fmt.Sprintf("获取规则失败: %v", err)))
		return
	}

	// 构建响应
	response := model.GetRulesResponse{
		Rules: rules,
		ExpiryPolicy: model.ExpiryPolicy{
			Policy:      expiryPolicy.Policy,
			CustomYears: expiryPolicy.CustomYears,
		},
	}

	logx.Infof("获取规则配置成功，等级ID: %d, 规则数量: %d", req.LevelID, len(rules))

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    response,
	})
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *UserPointsRulesHandler) logAdminOperation(r *http.Request, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 模块=%s, 操作=%s", module, action)
}
