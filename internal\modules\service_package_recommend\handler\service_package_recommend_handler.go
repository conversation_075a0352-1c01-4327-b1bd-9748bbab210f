package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/types"
	"yekaitai/pkg/common/model/service"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// ServicePackageRecommendHandler 推荐服务套餐处理器
type ServicePackageRecommendHandler struct{}

// NewServicePackageRecommendHandler 创建推荐服务套餐处理器
func NewServicePackageRecommendHandler() *ServicePackageRecommendHandler {
	return &ServicePackageRecommendHandler{}
}

// 创建推荐服务套餐请求
type CreateServicePackageRecommendRequest struct {
	ServicePackageID uint `json:"service_package_id"` // 服务套餐ID
	Status           int  `json:"status"`             // 状态，默认1
}

// 更新推荐服务套餐请求
type UpdateServicePackageRecommendRequest struct {
	ID               uint `path:"id"`                 // 推荐ID
	ServicePackageID uint `json:"service_package_id"` // 服务套餐ID
	Status           int  `json:"status"`             // 状态
}

// 删除推荐服务套餐请求
type DeleteServicePackageRecommendRequest struct {
	ID uint `path:"id"` // 推荐ID
}

// 设置置顶请求
type SetTopRequest struct {
	ID uint `path:"id"` // 推荐ID
}

// 取消置顶请求
type CancelTopRequest struct {
	ID uint `path:"id"` // 推荐ID
}

// 批量删除请求
type BatchDeleteRequest struct {
	Ids []uint `json:"ids"` // 推荐ID列表
}

// 批量添加请求
type BatchCreateRequest struct {
	ServicePackageIds []uint `json:"service_package_ids"` // 服务套餐ID列表
	Status            int    `json:"status"`              // 状态，默认1
}

// GetServicePackageRecommends 获取推荐服务套餐列表
func (h *ServicePackageRecommendHandler) GetServicePackageRecommends(w http.ResponseWriter, r *http.Request) {
	// 解析查询参数
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	if page <= 0 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(r.URL.Query().Get("page_size"))
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	servicePackageName := r.URL.Query().Get("service_package_name")
	storeIDStr := r.URL.Query().Get("store_id")
	isTopStr := r.URL.Query().Get("is_top")
	statusStr := r.URL.Query().Get("status")

	var storeID uint
	if storeIDStr != "" {
		if id, err := strconv.ParseUint(storeIDStr, 10, 32); err == nil {
			storeID = uint(id)
		}
	}

	var isTop *bool
	if isTopStr != "" {
		if val, err := strconv.ParseBool(isTopStr); err == nil {
			isTop = &val
		}
	}

	var status *int
	if statusStr != "" {
		if val, err := strconv.Atoi(statusStr); err == nil {
			status = &val
		}
	}

	params := &service.ServicePackageRecommendQueryParams{
		Page:               page,
		PageSize:           pageSize,
		ServicePackageName: servicePackageName,
		StoreID:            storeID,
		IsTop:              isTop,
		Status:             status,
	}

	logx.Infof("获取推荐服务套餐列表请求: %+v", params)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	// 获取列表
	list, total, err := servicePackageRecommendRepo.List(params)
	if err != nil {
		logx.Errorf("获取推荐服务套餐列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取列表失败"))
		return
	}

	logx.Infof("推荐服务套餐列表获取成功: 总数=%d, 当前页数据=%d", total, len(list))

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"list":  list,
		"total": total,
		"page":  page,
		"size":  pageSize,
	}, "获取成功"))
}

// CreateServicePackageRecommend 创建推荐服务套餐
func (h *ServicePackageRecommendHandler) CreateServicePackageRecommend(w http.ResponseWriter, r *http.Request) {
	var req CreateServicePackageRecommendRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		logx.Errorf("创建推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	if req.ServicePackageID == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务套餐ID不能为空"))
		return
	}

	logx.Infof("创建推荐服务套餐请求: %+v", req)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	// 检查推荐服务套餐数量限制（最多10个）
	count, err := servicePackageRecommendRepo.CountRecommends()
	if err != nil {
		logx.Errorf("检查推荐服务套餐数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "系统错误"))
		return
	}

	if count >= 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "首页最多推荐10个套餐"))
		return
	}

	// 检查服务套餐是否已被推荐
	if servicePackageRecommendRepo.IsRecommended(req.ServicePackageID) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该服务套餐已在推荐列表中"))
		return
	}

	// 获取当前管理员ID作为创建人
	var creatorID uint = 0
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if adminID, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			creatorID = uint(adminID)
		}
	}

	// 创建推荐记录
	recommend := &service.ServicePackageRecommend{
		ServicePackageID: req.ServicePackageID,
		IsTop:            false, // 新创建默认不置顶
		SortOrder:        0,
		Status:           req.Status,
		CreatorID:        creatorID,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	if recommend.Status == 0 {
		recommend.Status = 1 // 默认启用
	}

	err = servicePackageRecommendRepo.Create(recommend)
	if err != nil {
		logx.Errorf("创建推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建失败"))
		return
	}

	logx.Infof("推荐服务套餐创建成功: ID=%d", recommend.ID)

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(recommend, "创建成功"))
}

// UpdateServicePackageRecommend 更新推荐服务套餐
func (h *ServicePackageRecommendHandler) UpdateServicePackageRecommend(w http.ResponseWriter, r *http.Request) {
	var req UpdateServicePackageRecommendRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("更新推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	if req.ServicePackageID == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务套餐ID不能为空"))
		return
	}

	logx.Infof("更新推荐服务套餐请求: ID=%d, %+v", req.ID, req)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	// 查找现有记录
	recommend, err := servicePackageRecommendRepo.FindByID(req.ID)
	if err != nil {
		logx.Errorf("查找推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "推荐记录不存在"))
		return
	}

	// 如果更换了服务套餐，需要检查新的服务套餐是否已被推荐
	if recommend.ServicePackageID != req.ServicePackageID {
		if servicePackageRecommendRepo.IsRecommended(req.ServicePackageID) {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该服务套餐已在推荐列表中"))
			return
		}
	}

	// 更新字段
	recommend.ServicePackageID = req.ServicePackageID
	recommend.Status = req.Status
	recommend.UpdatedAt = time.Now()

	err = servicePackageRecommendRepo.Update(recommend)
	if err != nil {
		logx.Errorf("更新推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新失败"))
		return
	}

	logx.Infof("推荐服务套餐更新成功: ID=%d", recommend.ID)

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(recommend, "更新成功"))
}

// DeleteServicePackageRecommend 删除推荐服务套餐
func (h *ServicePackageRecommendHandler) DeleteServicePackageRecommend(w http.ResponseWriter, r *http.Request) {
	var req DeleteServicePackageRecommendRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("删除推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("删除推荐服务套餐请求: ID=%d", req.ID)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	// 检查记录是否存在
	_, err := servicePackageRecommendRepo.FindByID(req.ID)
	if err != nil {
		logx.Errorf("查找推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "推荐记录不存在"))
		return
	}

	err = servicePackageRecommendRepo.Delete(req.ID)
	if err != nil {
		logx.Errorf("删除推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除失败"))
		return
	}

	logx.Infof("推荐服务套餐删除成功: ID=%d", req.ID)

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除成功"))
}

// BatchDeleteServicePackageRecommends 批量删除推荐服务套餐
func (h *ServicePackageRecommendHandler) BatchDeleteServicePackageRecommends(w http.ResponseWriter, r *http.Request) {
	var req BatchDeleteRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		logx.Errorf("批量删除推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	if len(req.Ids) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请选择要删除的记录"))
		return
	}

	logx.Infof("批量删除推荐服务套餐请求: ids=%v", req.Ids)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	err := servicePackageRecommendRepo.BatchDelete(req.Ids)
	if err != nil {
		logx.Errorf("批量删除推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "批量删除失败"))
		return
	}

	logx.Infof("推荐服务套餐批量删除成功: 删除%d条记录", len(req.Ids))

	// 记录操作日志
	logAdminOperation(r, "批量删除推荐服务套餐", map[string]interface{}{
		"recommend_ids": req.Ids,
		"count":         len(req.Ids),
	})

	httpx.OkJson(w, types.NewSuccessResponse(nil, "批量删除成功"))
}

// BatchCreateServicePackageRecommends 批量添加推荐服务套餐
func (h *ServicePackageRecommendHandler) BatchCreateServicePackageRecommends(w http.ResponseWriter, r *http.Request) {
	var req BatchCreateRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		logx.Errorf("批量添加推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	if len(req.ServicePackageIds) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请选择要添加的服务套餐"))
		return
	}

	if len(req.ServicePackageIds) > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "一次最多添加10个服务套餐"))
		return
	}

	logx.Infof("批量添加推荐服务套餐请求: servicePackageIds=%v", req.ServicePackageIds)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	// 检查推荐服务套餐数量限制（最多10个）
	count, err := servicePackageRecommendRepo.CountRecommends()
	if err != nil {
		logx.Errorf("检查推荐服务套餐数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "系统错误"))
		return
	}

	if count+int64(len(req.ServicePackageIds)) > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams,
			fmt.Sprintf("首页最多推荐10个套餐，当前已有%d个，最多还能添加%d个", count, 10-count)))
		return
	}

	// 检查服务套餐是否已被推荐
	var alreadyRecommended []uint
	for _, servicePackageID := range req.ServicePackageIds {
		if servicePackageRecommendRepo.IsRecommended(servicePackageID) {
			alreadyRecommended = append(alreadyRecommended, servicePackageID)
		}
	}

	if len(alreadyRecommended) > 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams,
			fmt.Sprintf("服务套餐ID %v 已在推荐列表中", alreadyRecommended)))
		return
	}

	// 获取当前管理员ID作为创建人
	var creatorID uint = 0
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if adminID, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			creatorID = uint(adminID)
		}
	}

	// 构建推荐记录
	var recommends []*service.ServicePackageRecommend
	for _, servicePackageID := range req.ServicePackageIds {
		recommend := &service.ServicePackageRecommend{
			ServicePackageID: servicePackageID,
			IsTop:            false, // 批量添加默认不置顶
			SortOrder:        0,
			Status:           req.Status,
			CreatorID:        creatorID,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}

		if recommend.Status == 0 {
			recommend.Status = 1 // 默认启用
		}

		recommends = append(recommends, recommend)
	}

	// 批量创建推荐记录
	err = servicePackageRecommendRepo.BatchCreate(recommends)
	if err != nil {
		logx.Errorf("批量创建推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "批量添加失败"))
		return
	}

	logx.Infof("推荐服务套餐批量添加成功: 添加%d个套餐", len(req.ServicePackageIds))

	// 记录操作日志
	logAdminOperation(r, "批量创建推荐服务套餐", map[string]interface{}{
		"service_package_ids": req.ServicePackageIds,
		"count":               len(req.ServicePackageIds),
		"status":              req.Status,
	})

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"count": len(req.ServicePackageIds),
	}, "批量添加成功"))
}

// SetTopServicePackageRecommend 设置置顶
func (h *ServicePackageRecommendHandler) SetTopServicePackageRecommend(w http.ResponseWriter, r *http.Request) {
	var req SetTopRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("设置推荐服务套餐置顶失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("设置推荐服务套餐置顶请求: ID=%d", req.ID)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	// 检查记录是否存在
	_, err := servicePackageRecommendRepo.FindByID(req.ID)
	if err != nil {
		logx.Errorf("查找推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "推荐记录不存在"))
		return
	}

	err = servicePackageRecommendRepo.SetTop(req.ID)
	if err != nil {
		logx.Errorf("设置推荐服务套餐置顶失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "设置置顶失败"))
		return
	}

	logx.Infof("推荐服务套餐置顶设置成功: ID=%d", req.ID)

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(nil, "置顶成功"))
}

// CancelTopServicePackageRecommend 取消置顶
func (h *ServicePackageRecommendHandler) CancelTopServicePackageRecommend(w http.ResponseWriter, r *http.Request) {
	var req CancelTopRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("取消推荐服务套餐置顶失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("取消推荐服务套餐置顶请求: ID=%d", req.ID)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	// 检查记录是否存在
	_, err := servicePackageRecommendRepo.FindByID(req.ID)
	if err != nil {
		logx.Errorf("查找推荐服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "推荐记录不存在"))
		return
	}

	err = servicePackageRecommendRepo.CancelTop(req.ID)
	if err != nil {
		logx.Errorf("取消推荐服务套餐置顶失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "取消置顶失败"))
		return
	}

	logx.Infof("推荐服务套餐置顶取消成功: ID=%d", req.ID)

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(nil, "取消置顶成功"))
}

// GetServicePackageSelectList 获取服务套餐选择列表
func (h *ServicePackageRecommendHandler) GetServicePackageSelectList(w http.ResponseWriter, r *http.Request) {
	// 解析查询参数
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	if page <= 0 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(r.URL.Query().Get("page_size"))
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	servicePackageName := r.URL.Query().Get("service_package_name")
	storeIDStr := r.URL.Query().Get("store_id")

	var storeID uint
	if storeIDStr != "" {
		if id, err := strconv.ParseUint(storeIDStr, 10, 32); err == nil {
			storeID = uint(id)
		}
	}

	params := &service.ServicePackageSelectQueryParams{
		Page:               page,
		PageSize:           pageSize,
		ServicePackageName: servicePackageName,
		StoreID:            storeID,
	}

	logx.Infof("获取服务套餐选择列表请求: %+v", params)

	// 创建推荐服务套餐仓库
	servicePackageRecommendRepo := service.NewServicePackageRecommendRepository(mysql.Master())

	// 获取列表
	list, total, err := servicePackageRecommendRepo.GetServicePackageSelectList(params)
	if err != nil {
		logx.Errorf("获取服务套餐选择列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取列表失败"))
		return
	}

	logx.Infof("服务套餐选择列表获取成功: 总数=%d, 当前页数据=%d", total, len(list))

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"list":  list,
		"total": total,
		"page":  page,
		"size":  pageSize,
	}, "获取成功"))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func logAdminOperation(r *http.Request, operation string, details map[string]interface{}) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理")
}
