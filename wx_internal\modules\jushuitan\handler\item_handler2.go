package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"
	jst "yekaitai/pkg/adapters/jushuitan"
	"yekaitai/pkg/response"
	"yekaitai/wx_internal/modules/jushuitan/logic"

	jsoniter "github.com/json-iterator/go"

	"github.com/zeromicro/go-zero/core/logx"
)

// 查询组合装商品资料
func (h *JushuitanHandler) QueryCombineSku(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageIndex, _ := strconv.Atoi(r.FormValue("page_index"))
	if pageIndex <= 0 {
		pageIndex = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	// 处理修改时间
	modifiedBegin := r.FormValue("modified_begin")
	modifiedEnd := r.FormValue("modified_end")

	// 处理商品编码
	skuIDs := r.FormValue("sku_ids")

	// 处理款式编码
	var iIDs []string
	if iIDsStr := r.FormValue("i_ids"); iIDsStr != "" {
		iIDs = strings.Split(iIDsStr, ",")
	}

	// 处理自定义字段
	var combineItemskuFlds []string
	if fldsStr := r.FormValue("combine_itemsku_flds"); fldsStr != "" {
		combineItemskuFlds = strings.Split(fldsStr, ",")
	}

	// 处理实体编码
	entySkuID := r.FormValue("enty_sku_id")

	// 记录请求参数
	req := &jst.CombineSkuQueryRequest{
		PageIndex:          pageIndex,
		PageSize:           pageSize,
		ModifiedBegin:      modifiedBegin,
		ModifiedEnd:        modifiedEnd,
		SkuIDs:             skuIDs,
		IIDs:               iIDs,
		CombineItemskuFlds: combineItemskuFlds,
		EntySkuID:          entySkuID,
	}
	LogRequest(r, req)

	// 创建商品逻辑处理
	itemLogic := logic.NewItemLogic(r.Context(), h.ctx)
	resp, err := itemLogic.QueryCombineSku(req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询组合装商品资料失败")
		return
	}

	// 记录并返回结果
	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))
	DirectJSTResponse(w, resp)
}

// 查询商品类目
func (h *JushuitanHandler) QueryCategory(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 记录请求参数
	req := &jst.CategoryQueryRequest{}
	LogRequest(r, req)

	// 创建商品逻辑处理
	itemLogic := logic.NewItemLogic(r.Context(), h.ctx)
	resp, err := itemLogic.QueryCategory(req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询商品类目失败")
		return
	}

	// 记录并返回结果
	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))
	DirectJSTResponse(w, resp)
}

// 查询BOM信息
func (h *JushuitanHandler) GetSkuBomPageList(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	currentPage, _ := strconv.Atoi(r.FormValue("current_page"))
	if currentPage <= 0 {
		currentPage = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	// 处理SKU IDs
	var skuIDs []string
	if skuIDsStr := r.FormValue("sku_ids"); skuIDsStr != "" {
		skuIDs = strings.Split(skuIDsStr, ",")
	}

	// 处理修改时间
	modifiedStart := r.FormValue("modified_start")
	modifiedEnd := r.FormValue("modified_end")

	// 记录请求参数
	req := &jst.BomQueryRequest{
		SkuIDs:        skuIDs,
		ModifiedStart: modifiedStart,
		ModifiedEnd:   modifiedEnd,
		Page: jst.BomPage{
			CurrentPage: currentPage,
			PageSize:    pageSize,
		},
	}
	LogRequest(r, req)

	// 创建商品逻辑处理
	itemLogic := logic.NewItemLogic(r.Context(), h.ctx)
	resp, err := itemLogic.GetSkuBomPageList(req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询BOM信息失败")
		return
	}

	// 记录并返回结果
	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))
	DirectJSTResponse(w, resp)
}

// 查询商品历史成本价
func (h *JushuitanHandler) GetHistoryCostPriceV2(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 处理SKU IDs
	var skuIDs []string
	if skuIDsStr := r.FormValue("sku_ids"); skuIDsStr != "" {
		skuIDs = strings.Split(skuIDsStr, ",")
	}

	// 处理仓库编码
	var wmsCoIDs []int
	if wmsCoIDsStr := r.FormValue("wms_co_ids"); wmsCoIDsStr != "" {
		wmsCoIdStrs := strings.Split(wmsCoIDsStr, ",")
		for _, idStr := range wmsCoIdStrs {
			if id, err := strconv.Atoi(idStr); err == nil {
				wmsCoIDs = append(wmsCoIDs, id)
			}
		}
	}

	// 记录请求参数
	req := &jst.HistoryCostPriceQueryRequest{
		SkuIDs:                skuIDs,
		WmsCoIDs:              wmsCoIDs,
		IsUseItemSkuCostPrice: true,
	}
	LogRequest(r, req)

	// 创建商品逻辑处理
	itemLogic := logic.NewItemLogic(r.Context(), h.ctx)
	resp, err := itemLogic.GetHistoryCostPriceV2(req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询商品历史成本价失败")
		return
	}

	// 记录并返回结果
	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))
	DirectJSTResponse(w, resp)
}

// 查询商品多供应商
func (h *JushuitanHandler) GetSupplierSkuList(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 获取商品编码参数
	skuID := r.FormValue("sku_id")
	iID := r.FormValue("i_id")

	// 如果商品编码和款式编码都为空，则至少需要一个时间范围参数
	if skuID == "" && iID == "" {
		begin := r.FormValue("begin")
		end := r.FormValue("end")
		if begin == "" || end == "" {
			err := fmt.Errorf("参数错误: 当商品编码和款式编码都为空时，需要提供时间范围参数")
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
	}

	// 获取供应商ID
	supplierID, _ := strconv.Atoi(r.FormValue("supplier_id"))

	// 获取分页参数
	currentPage, _ := strconv.Atoi(r.FormValue("current_page"))
	if currentPage <= 0 {
		currentPage = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 20
	}

	// 记录请求参数
	req := &jst.SupplierSkuQueryListRequest{
		SkuID:       skuID,
		IID:         iID,
		SupplierID:  supplierID,
		Begin:       r.FormValue("begin"),
		End:         r.FormValue("end"),
		CurrentPage: currentPage,
		PageSize:    pageSize,
	}
	LogRequest(r, req)

	// 创建商品逻辑处理
	itemLogic := logic.NewItemLogic(r.Context(), h.ctx)
	resp, err := itemLogic.GetSupplierSkuQueryList(req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询商品多供应商失败")
		return
	}

	// 记录并返回结果
	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 转换响应格式
	baseResp := &jst.BaseResp{
		Code: resp.Code,
		Msg:  resp.Msg,
		Data: resp.Data,
	}
	DirectJSTResponse(w, baseResp)
}

// DirectJSTResponse 直接返回聚水潭API的响应结果
func DirectJSTResponse(w http.ResponseWriter, resp *jst.BaseResp) {
	// 设置响应头
	w.Header().Set("Content-Type", "application/json")

	// 转换为JSON
	jsonData, err := jsoniter.Marshal(resp)
	if err != nil {
		logx.Errorf("无法序列化响应: %v", err)
		// 使用通用的错误处理函数
		errResp := &response.Response{
			Code:    http.StatusInternalServerError,
			Message: "内部服务器错误",
		}
		w.WriteHeader(http.StatusInternalServerError)
		jsoniter.NewEncoder(w).Encode(errResp)
		return
	}

	// 返回响应
	w.Write(jsonData)
}
