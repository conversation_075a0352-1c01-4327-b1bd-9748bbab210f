package user

import (
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// WxUserRepository 微信用户仓库接口
type WxUserRepository interface {
	Create(user *WxUser) error
	Update(user *WxUser) error
	Delete(id uint) error
	FindByID(id uint) (*WxUser, error)
	FindByOpenID(openid string) (*WxUser, error)
	FindByMobile(mobile string) (*WxUser, error)
	List(page, size int) ([]*WxUser, int64, error)
	UpdateStatus(id uint, status int) error
}

// wxUserRepository 微信用户仓库实现
type wxUserRepository struct {
	db *gorm.DB // 保留db字段以兼容接口，但不在方法中使用
}

// NewWxUserRepository 创建微信用户仓库
func NewWxUserRepository(db *gorm.DB) WxUserRepository {
	return &wxUserRepository{
		db: db,
	}
}

// Create 创建微信用户
func (r *wxUserRepository) Create(user *WxUser) error {
	return mysql.Master().Create(user).Error
}

// Update 更新微信用户
func (r *wxUserRepository) Update(user *WxUser) error {
	return mysql.Master().Save(user).Error
}

// Delete 删除微信用户
func (r *wxUserRepository) Delete(id uint) error {
	return mysql.Master().Delete(&WxUser{}, id).Error
}

// FindByID 根据ID查找微信用户
func (r *wxUserRepository) FindByID(id uint) (*WxUser, error) {
	var user WxUser
	err := mysql.Slave().Where("user_id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByOpenID 根据OpenID查找微信用户
func (r *wxUserRepository) FindByOpenID(openid string) (*WxUser, error) {
	var user WxUser
	err := mysql.Slave().Where("open_id = ?", openid).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindByMobile 根据手机号查找微信用户
func (r *wxUserRepository) FindByMobile(mobile string) (*WxUser, error) {
	var user WxUser
	err := mysql.Slave().Where("mobile = ?", mobile).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// List 获取微信用户列表
func (r *wxUserRepository) List(page, size int) ([]*WxUser, int64, error) {
	var users []*WxUser
	var total int64

	db := mysql.Slave()

	// 获取总数
	if err := db.Model(&WxUser{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("user_id DESC").Offset(offset).Limit(size).Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// UpdateStatus 更新微信用户状态
func (r *wxUserRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&WxUser{}).Where("user_id = ?", id).Update("status", status).Error
}
