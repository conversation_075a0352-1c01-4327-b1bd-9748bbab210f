#!/bin/bash

# 启动脚本示例
# 用于在不同环境下设置正确的日志路径

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 默认配置
APP_NAME="${APP_NAME:-yekaitai}"
ENV="${ENV:-dev}"
SERVICE="${SERVICE:-all}"

# 根据环境设置日志路径
case "$ENV" in
    "prod"|"production")
        export LOG_ROOT="${LOG_ROOT:-/var/log/medlinker}"
        export LOG_PATH="${LOG_PATH:-/var/log/medlinker/$APP_NAME}"
        CONFIG_FILE="etc/yekaitai-prod.yaml"
        echo "🚀 生产环境启动"
        ;;
    "test"|"testing")
        export LOG_ROOT="${LOG_ROOT:-/var/log/medlinker}"
        export LOG_PATH="${LOG_PATH:-/var/log/medlinker/$APP_NAME}"
        CONFIG_FILE="etc/yekaitai-test.yaml"
        echo "🧪 测试环境启动"
        ;;
    "dev"|"development"|*)
        export LOG_PATH="${LOG_PATH:-logs}"
        CONFIG_FILE="etc/yekaitai-dev.yaml"
        echo "🛠️ 开发环境启动"
        ;;
esac

# 设置其他环境变量
export APP_NAME="$APP_NAME"
export RUN_ENV="$ENV"

# 打印环境信息
echo "=================================="
echo "应用名称: $APP_NAME"
echo "运行环境: $ENV"
echo "服务类型: $SERVICE"
echo "配置文件: $CONFIG_FILE"
echo "日志路径: $LOG_PATH"
echo "=================================="

# 确保日志目录存在
if [ ! -z "$LOG_PATH" ] && [ "$LOG_PATH" != "logs" ]; then
    echo "创建日志目录: $LOG_PATH"
    mkdir -p "$LOG_PATH"
    chmod 755 "$LOG_PATH"
fi

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 检查可执行文件是否存在
BINARY_NAME="yekaitai_admin"
if [ ! -f "$BINARY_NAME" ]; then
    echo "❌ 可执行文件不存在: $BINARY_NAME"
    echo "请先编译项目: go build -o $BINARY_NAME main.go"
    exit 1
fi

# 启动应用
echo "🚀 启动应用..."
exec ./"$BINARY_NAME" -f "$CONFIG_FILE" -service "$SERVICE"
