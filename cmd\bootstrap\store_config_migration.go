package bootstrap

import (
	"yekaitai/internal/modules/base_setup/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateStoreConfigTables 执行店铺配置模块表结构迁移
func MigrateStoreConfigTables() error {
	db := mysql.Master()

	logx.Info("开始执行店铺配置模块表结构迁移...")

	// 自动迁移店铺配置表
	if err := db.AutoMigrate(&model.StoreConfig{}); err != nil {
		logx.Errorf("店铺配置表迁移失败: %v", err)
		return err
	}
	logx.Info("店铺配置表迁移完成")

	logx.Info("店铺配置模块表结构迁移完成")
	return nil
}
