package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/zeromicro/go-zero/rest/httpx"

	repo_registration "yekaitai/pkg/common/repository/registration"
	qrmodel "yekaitai/wx_internal/modules/qrcode-management/model"
	"yekaitai/wx_internal/modules/qrcode-management/service"
)

// 二维码请求
type QRCodeRequest struct {
	OrderID uint `json:"order_id" form:"order_id"`
}

// 核销请求
type VerifyRequest struct {
	OrderNo          string `json:"order_no" form:"order_no"`                   // 订单编号
	VerificationCode string `json:"verification_code" form:"verification_code"` // 核销码
	Sign             string `json:"sign" form:"sign"`                           // 签名
	Timestamp        string `json:"timestamp" form:"timestamp"`                 // 时间戳
	VerifierID       uint   `json:"verifier_id" form:"verifier_id"`             // 核销人ID
	VerifierName     string `json:"verifier_name" form:"verifier_name"`         // 核销人姓名
}

// 核销记录请求
type VerificationRecordsRequest struct {
	StoreID   string `json:"store_id" form:"store_id" query:"store_id"`
	StartDate string `json:"start_date" form:"start_date" query:"start_date"`
	EndDate   string `json:"end_date" form:"end_date" query:"end_date"`
	Page      int    `json:"page" form:"page" query:"page"`
	PageSize  int    `json:"page_size" form:"page_size" query:"page_size"`
}

// 基础响应
type BaseResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// 生成二维码响应
type QRCodeResponse struct {
	URL string `json:"url"`
}

// 核销响应
type VerifyResponse struct {
	Success bool `json:"success"`
}

// 核销记录列表响应
type VerificationRecordsResponse struct {
	Records []*qrmodel.VerificationRecord `json:"records"`
	Total   int                           `json:"total"`
}

// 核销记录详情响应
type VerificationRecordResponse struct {
	Record *qrmodel.VerificationRecord `json:"record"`
}

// 生成二维码处理器
func GenerateQRCodeHandler(w http.ResponseWriter, r *http.Request) {
	var req QRCodeRequest

	// 解析JSON请求体
	if r.Header.Get("Content-Type") == "application/json" {
		// 使用json.NewDecoder解析JSON请求体
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			httpx.Error(w, fmt.Errorf("解析JSON请求体失败: %v", err))
			return
		}
	} else {
		// 使用httpx.Parse解析表单请求
		if err := httpx.Parse(r, &req); err != nil {
			httpx.Error(w, err)
			return
		}
	}

	// 验证请求参数
	if req.OrderID == 0 {
		httpx.Error(w, fmt.Errorf("order_id不能为空"))
		return
	}

	// 创建二维码服务
	qrCodeService := qrmodel.NewQRCodeService()

	// 生成二维码
	url, err := qrCodeService.GenerateQRCode(r.Context(), req.OrderID)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 返回响应
	httpx.OkJson(w, &BaseResponse{
		Code: 0,
		Msg:  "success",
		Data: &QRCodeResponse{
			URL: url,
		},
	})
}

// 获取二维码处理器
func GetQRCodeHandler(w http.ResponseWriter, r *http.Request) {
	// 从URL路径中获取订单ID
	path := r.URL.Path
	segments := strings.Split(path, "/")
	if len(segments) < 1 {
		http.Error(w, "无效的请求路径", http.StatusBadRequest)
		return
	}

	// 解析订单ID
	idStr := segments[len(segments)-1]
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		http.Error(w, "无效的订单ID", http.StatusBadRequest)
		return
	}

	// 创建二维码服务
	qrCodeService := qrmodel.NewQRCodeService()

	// 获取二维码
	qrCodeURL, err := qrCodeService.GetQRCode(r.Context(), uint(id))
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 获取订单信息
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	order, err := orderRepo.GetAppointmentOrderByID(r.Context(), uint(id))
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 如果二维码不存在，则生成并上传二维码
	if order.QRCodeURL == "" {
		// 生成并上传二维码
		qrCodeURL, err = service.GenerateAppointmentQRCode(
			r.Context(),
			order.ID,
			order.OrderNo,
			order.VerificationCode,
		)
		if err != nil {
			httpx.Error(w, err)
			return
		}

		// 更新订单二维码URL
		order.QRCodeURL = qrCodeURL
		if err := orderRepo.UpdateAppointmentOrder(r.Context(), order); err != nil {
			httpx.Error(w, err)
			return
		}
	}

	// 返回二维码图片
	http.Redirect(w, r, qrCodeURL, http.StatusFound)
}

// 验证核销码处理器
func VerifyCodeHandler(w http.ResponseWriter, r *http.Request) {
	var req VerifyRequest

	// 解析JSON请求体
	if r.Header.Get("Content-Type") == "application/json" {
		// 使用json.NewDecoder解析JSON请求体
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			httpx.Error(w, fmt.Errorf("解析JSON请求体失败: %v", err))
			return
		}
	} else {
		// 使用httpx.Parse解析表单请求
		if err := httpx.Parse(r, &req); err != nil {
			httpx.Error(w, err)
			return
		}
	}

	// 验证必填参数
	if req.OrderNo == "" {
		httpx.Error(w, fmt.Errorf("order_no不能为空"))
		return
	}
	if req.VerificationCode == "" {
		httpx.Error(w, fmt.Errorf("verification_code不能为空"))
		return
	}
	if req.Sign == "" {
		httpx.Error(w, fmt.Errorf("sign不能为空"))
		return
	}
	if req.Timestamp == "" {
		httpx.Error(w, fmt.Errorf("timestamp不能为空"))
		return
	}
	if req.VerifierID == 0 {
		httpx.Error(w, fmt.Errorf("verifier_id不能为空"))
		return
	}
	if req.VerifierName == "" {
		httpx.Error(w, fmt.Errorf("verifier_name不能为空"))
		return
	}

	// 创建二维码服务
	qrCodeService := qrmodel.NewQRCodeService()

	// 验证核销码（包含所有验证逻辑）
	verifyData := &qrmodel.VerifyRequestData{
		OrderNo:          req.OrderNo,
		VerificationCode: req.VerificationCode,
		Sign:             req.Sign,
		Timestamp:        req.Timestamp,
		VerifierID:       req.VerifierID,
		VerifierName:     req.VerifierName,
	}
	err := qrCodeService.VerifyCodeWithValidation(r.Context(), verifyData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 返回响应
	httpx.OkJson(w, &BaseResponse{
		Code: 0,
		Msg:  "success",
		Data: &VerifyResponse{
			Success: true,
		},
	})
}

// 获取核销记录列表处理器
func GetVerificationRecordsHandler(w http.ResponseWriter, r *http.Request) {
	var req VerificationRecordsRequest

	// 根据请求方法和Content-Type选择不同的解析方式
	if r.Method == "GET" {
		// 直接从URL查询参数获取数据
		storeID := r.URL.Query().Get("store_id")
		startDate := r.URL.Query().Get("start_date")
		endDate := r.URL.Query().Get("end_date")

		pageStr := r.URL.Query().Get("page")
		pageSizeStr := r.URL.Query().Get("page_size")

		// 设置请求参数
		req.StoreID = storeID
		req.StartDate = startDate
		req.EndDate = endDate

		// 转换页码和每页大小
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		} else {
			req.Page = 1 // 默认值
		}

		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 {
			req.PageSize = pageSize
		} else {
			req.PageSize = 10 // 默认值
		}
	} else if r.Header.Get("Content-Type") == "application/json" {
		// 使用json.NewDecoder解析JSON请求体
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			httpx.Error(w, fmt.Errorf("解析JSON请求体失败: %v", err))
			return
		}
	} else {
		// 使用httpx.Parse解析表单请求
		if err := httpx.Parse(r, &req); err != nil {
			httpx.Error(w, err)
			return
		}
	}

	// 创建二维码服务
	qrCodeService := qrmodel.NewQRCodeService()

	// 获取核销记录列表
	records, total, err := qrCodeService.GetVerificationRecords(
		r.Context(),
		req.StoreID,
		req.StartDate,
		req.EndDate,
		req.Page,
		req.PageSize,
	)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 返回响应
	httpx.OkJson(w, &BaseResponse{
		Code: 0,
		Msg:  "success",
		Data: &VerificationRecordsResponse{
			Records: records,
			Total:   total,
		},
	})
}

// 获取核销记录详情处理器
func GetVerificationRecordHandler(w http.ResponseWriter, r *http.Request) {
	// 从URL路径中获取记录ID
	path := r.URL.Path
	segments := strings.Split(path, "/")
	if len(segments) < 1 {
		http.Error(w, "无效的请求路径", http.StatusBadRequest)
		return
	}

	// 解析记录ID
	idStr := segments[len(segments)-1]
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		http.Error(w, "无效的记录ID", http.StatusBadRequest)
		return
	}

	// 创建二维码服务
	qrCodeService := qrmodel.NewQRCodeService()

	// 获取核销记录详情
	record, err := qrCodeService.GetVerificationRecord(r.Context(), uint(id))
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 返回响应
	httpx.OkJson(w, &BaseResponse{
		Code: 0,
		Msg:  "success",
		Data: &VerificationRecordResponse{
			Record: record,
		},
	})
}
