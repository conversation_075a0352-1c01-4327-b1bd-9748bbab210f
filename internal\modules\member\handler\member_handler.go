package handler

import (
	"net/http"
	"strconv"
	"strings"
	"time"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 会员列表请求
type MemberListRequest struct {
	types.PageRequest
	UnionID             string    `form:"unionId,optional"`             // 微信UnionID
	Mobile              string    `form:"mobile,optional"`              // 手机号
	Nickname            string    `form:"nickname,optional"`            // 昵称
	Status              int       `form:"status,optional"`              // 状态：1-正常，0-禁用
	UserLevelID         uint      `form:"userLevelId,optional"`         // 用户等级ID
	MinTotalConsumption float64   `form:"minTotalConsumption,optional"` // 最小累计消费金额
	MaxTotalConsumption float64   `form:"maxTotalConsumption,optional"` // 最大累计消费金额
	MinDailyConsumption float64   `form:"minDailyConsumption,optional"` // 最小单日消费金额
	MaxDailyConsumption float64   `form:"maxDailyConsumption,optional"` // 最大单日消费金额
	TagID               string    `form:"tagId,optional"`               // 标签ID
	StartDate           time.Time `form:"startDate,optional"`           // 注册开始日期
	EndDate             time.Time `form:"endDate,optional"`             // 注册结束日期
}

// 会员详情请求
type MemberDetailRequest struct {
	ID uint `path:"id"` // 会员ID
}

// 更新会员状态请求
type UpdateMemberStatusRequest struct {
	ID     uint `path:"id"`     // 会员ID
	Status int  `json:"status"` // 状态：1-正常，0-禁用
}

// 更新会员标签请求
type UpdateMemberTagsRequest struct {
	ID     uint   `path:"id"`     // 会员ID
	TagIDs string `json:"tagIds"` // 标签ID，多个ID用逗号分隔
}

// MemberHandler 会员管理处理器
type MemberHandler struct {
	svcCtx *svc.ServiceContext
}

// NewMemberHandler 创建会员管理处理器
func NewMemberHandler(svcCtx *svc.ServiceContext) *MemberHandler {
	return &MemberHandler{
		svcCtx: svcCtx,
	}
}

// ListMembers 获取会员列表
func (h *MemberHandler) ListMembers(w http.ResponseWriter, r *http.Request) {
	var req MemberListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 构建查询条件
	query := make(map[string]interface{})
	if req.UnionID != "" {
		query["union_id"] = req.UnionID
	}
	if req.Mobile != "" {
		query["mobile"] = req.Mobile
	}
	if req.Nickname != "" {
		query["nickname"] = req.Nickname
	}
	if req.Status > 0 {
		query["status"] = req.Status
	}
	if req.UserLevelID > 0 {
		query["user_level_id"] = req.UserLevelID
	}

	if req.TagID != "" {
		query["tag_id"] = req.TagID
	}
	if !req.StartDate.IsZero() {
		query["start_date"] = req.StartDate
	}
	if !req.EndDate.IsZero() {
		query["end_date"] = req.EndDate
	}

	// 默认按注册时间降序排序
	query["order_by"] = "register_date DESC"

	logx.Infof("获取会员列表: page=%d, size=%d, query=%s", req.Page, req.Size, req.Query)

	// 调用服务层获取会员列表
	members, total, err := h.svcCtx.MemberService.ListMembers(req.Page, req.Size, query)
	if err != nil {
		logx.Errorf("获取会员列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取会员列表失败"))
		return
	}

	// 使用统一的分页响应
	pageResponse := types.NewPageResponse(members, total, &req.PageRequest)

	logx.Infof("获取会员列表成功: 共%d条记录", total)

	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取会员列表成功"))
}

// GetMember 获取会员详情
func (h *MemberHandler) GetMember(w http.ResponseWriter, r *http.Request) {
	var req MemberDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 调用服务层获取会员详情
	member, err := h.svcCtx.MemberService.GetMember(req.ID)
	if err != nil {
		logx.Errorf("获取会员详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取会员详情失败"))
		return
	}

	// 获取会员的就诊人列表
	patients, err := h.svcCtx.MemberService.GetMemberPatients(req.ID)
	if err != nil {
		logx.Errorf("获取就诊人列表失败: %v", err)
		// 不影响整体响应，继续执行
	}

	// 构建响应数据
	data := map[string]interface{}{
		"member":   member,
		"patients": patients,
	}

	httpx.OkJson(w, types.NewSuccessResponse(data, "获取会员详情成功"))
}

// UpdateMemberStatus 更新会员状态
func (h *MemberHandler) UpdateMemberStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdateMemberStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 校验状态值
	if req.Status != 0 && req.Status != 1 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "状态值无效，只能是0或1"))
		return
	}

	// 调用服务层更新会员状态
	err := h.svcCtx.MemberService.UpdateMemberStatus(req.ID, req.Status)
	if err != nil {
		logx.Errorf("更新会员状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新会员状态失败"))
		return
	}

	// 构建响应数据
	statusText := "启用"
	if req.Status == 0 {
		statusText = "禁用"
	}

	httpx.OkJson(w, types.NewSuccessResponse(nil, "会员"+statusText+"成功"))
}

// UpdateMemberTags 更新会员标签
func (h *MemberHandler) UpdateMemberTags(w http.ResponseWriter, r *http.Request) {
	var req UpdateMemberTagsRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("更新会员标签: memberId=%d, tagIds=%s", req.ID, req.TagIDs)

	// 校验请求中的标签ID是否合法
	if req.TagIDs != "" {
		// 验证标签ID格式
		tagIDs := strings.Split(req.TagIDs, ",")
		for _, tagIDStr := range tagIDs {
			_, err := strconv.ParseUint(tagIDStr, 10, 32)
			if err != nil {
				logx.Errorf("标签ID格式错误: %s", tagIDStr)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签ID格式错误，应为数字"))
				return
			}
		}
	}

	// 调用服务层更新会员标签
	err := h.svcCtx.MemberService.UpdateMemberTags(req.ID, req.TagIDs)
	if err != nil {
		logx.Errorf("更新会员标签失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新会员标签失败"))
		return
	}

	// 构建响应数据
	httpx.OkJson(w, types.NewSuccessResponse(nil, "更新会员标签成功"))
}
