package service

import (
	"fmt"

	"github.com/robfig/cron/v3"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/cmd/cron/tasks"
	"yekaitai/internal/modules/store/model"
	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/adapters/hangzhou"
)

// CronService 定时任务服务
type CronService struct {
	cron           *cron.Cron
	db             *gorm.DB
	active         bool
	hangzhouClient *hangzhou.Client
	abcYunClient   *abcyun.AbcYunClient
}

// NewCronService 创建定时任务服务
func NewCronService(db *gorm.DB, clients ...interface{}) *CronService {
	service := &CronService{
		cron:   cron.New(cron.WithSeconds()),
		db:     db,
		active: false,
	}

	// 处理可选的客户端参数
	for _, client := range clients {
		if hangzhouClient, ok := client.(*hangzhou.Client); ok {
			service.hangzhouClient = hangzhouClient
		} else if abcClient, ok := client.(*abcyun.AbcYunClient); ok {
			service.abcYunClient = abcClient
		}
	}

	return service
}

// Start 启动所有定时任务
func (s *CronService) Start() {
	if s.active {
		logx.Info("定时任务服务已在运行中")
		return
	}

	logx.Info("开始启动定时任务服务...")

	// 初始化门诊收费信息同步服务
	chargeSyncService := tasks.NewChargeSyncService(s.db)

	// 先执行一次门诊收费信息同步（立即执行）
	go func() {
		logx.Info("正在执行门诊收费信息首次同步...")
		chargeSyncService.SyncCharges()
		logx.Info("门诊收费信息首次同步完成")
	}()

	// 添加每小时执行门诊收费信息同步（同步最新数据）
	_, err := s.cron.AddFunc("0 30 * * * ?", func() {
		logx.Info("定时任务：开始每小时门诊收费信息同步...")
		chargeSyncService.SyncCharges() // 使用相同的逻辑，会自动判断需要同步的日期范围
		logx.Info("定时任务：每小时门诊收费信息同步完成")
	})
	if err != nil {
		logx.Errorf("添加门诊收费信息同步任务失败: %v", err)
	} else {
		logx.Info("已添加门诊收费信息同步任务，每小时执行一次")
	}

	// 初始化医生同步服务
	doctorSyncService := tasks.NewDoctorSyncService(s.db)

	// 添加每小时执行医生同步
	_, err = s.cron.AddFunc("0 10 * * * ?", doctorSyncService.RunSync)
	if err != nil {
		logx.Errorf("添加医生同步任务失败: %v", err)
	} else {
		logx.Info("已添加医生同步任务，每小时执行一次")
	}

	// 初始化患者同步服务
	patientSyncService := tasks.NewPatientSyncService(s.db)

	// 添加每小时执行患者同步
	_, err = s.cron.AddFunc("0 20 * * * ?", patientSyncService.RunSync)
	if err != nil {
		logx.Errorf("添加患者同步任务失败: %v", err)
	} else {
		logx.Info("已添加患者同步任务，每小时执行一次")
	}

	// 初始化机构同步服务（如果杭州HIS客户端已初始化）
	if s.hangzhouClient != nil {
		storeRepo := model.NewStoreRepository(s.db)
		orgSyncService := tasks.NewOrgSyncService(s.hangzhouClient, storeRepo, true, 1)

		// 启动机构同步服务（它会自动执行一次同步）
		// 因为syncOrganizations是未导出方法，这里直接调用Start
		go orgSyncService.Start()
		logx.Info("已启动机构同步服务")
	} else {
		logx.Info("杭州HIS客户端未初始化，跳过机构同步服务")
	}

	// 添加ABC云同步任务（如果客户端已初始化）
	if s.abcYunClient != nil {
		// ABC云门店同步 - 每小时执行一次
		abcYunStoreSyncService := tasks.NewAbcYunStoreSyncService(s.abcYunClient)
		_, err = s.cron.AddFunc("0 0 * * * ?", func() {
			if syncErr := abcYunStoreSyncService.SyncStores(); syncErr != nil {
				logx.Errorf("ABC云门店同步失败: %v", syncErr)
			}
		})
		if err != nil {
			logx.Errorf("添加ABC云门店同步任务失败: %v", err)
		} else {
			logx.Info("已添加ABC云门店同步任务，每小时执行一次")
		}

		// ABC云科室同步 - 每小时执行一次
		abcYunDeptSyncService := tasks.NewAbcYunDepartmentSyncService(s.abcYunClient)
		_, err = s.cron.AddFunc("0 5 * * * ?", func() {
			if syncErr := abcYunDeptSyncService.SyncDepartments(); syncErr != nil {
				logx.Errorf("ABC云科室同步失败: %v", syncErr)
			}
		})
		if err != nil {
			logx.Errorf("添加ABC云科室同步任务失败: %v", err)
		} else {
			logx.Info("已添加ABC云科室同步任务，每小时执行一次")
		}

		// ABC云医生同步 - 每小时执行一次
		abcYunDoctorSyncService := tasks.NewAbcYunDoctorSyncService(s.abcYunClient)
		_, err = s.cron.AddFunc("0 10 * * * ?", func() {
			if syncErr := abcYunDoctorSyncService.SyncDoctors(); syncErr != nil {
				logx.Errorf("ABC云医生同步失败: %v", syncErr)
			}
		})
		if err != nil {
			logx.Errorf("添加ABC云医生同步任务失败: %v", err)
		} else {
			logx.Info("已添加ABC云医生同步任务，每小时执行一次")
		}

		// ABC云患者同步 - 每小时执行一次
		abcYunPatientSyncService := tasks.NewAbcYunPatientSyncSimpleService(s.abcYunClient)
		_, err = s.cron.AddFunc("0 15 * * * ?", func() {
			if syncErr := abcYunPatientSyncService.SyncPatients(); syncErr != nil {
				logx.Errorf("ABC云患者同步失败: %v", syncErr)
			}
		})
		if err != nil {
			logx.Errorf("添加ABC云患者同步任务失败: %v", err)
		} else {
			logx.Info("已添加ABC云患者同步任务，每小时执行一次")
		}

		logx.Info("已添加ABC云同步任务")
	} else {
		logx.Info("ABC云客户端未初始化，跳过ABC云同步任务")
	}

	// 添加万里牛商品同步任务
	wanLiNiuGoodsSyncService := tasks.NewWanLiNiuGoodsSyncService()
	if wanLiNiuGoodsSyncService != nil {
		// 万里牛商品同步 - 每小时执行一次
		_, err = s.cron.AddFunc("0 25 * * * ?", func() {
			logx.Info("定时任务：开始万里牛商品同步...")
			if syncErr := wanLiNiuGoodsSyncService.SyncGoods(); syncErr != nil {
				logx.Errorf("万里牛商品同步失败: %v", syncErr)
			} else {
				logx.Info("定时任务：万里牛商品同步完成")
			}
		})
		if err != nil {
			logx.Errorf("添加万里牛商品同步任务失败: %v", err)
		} else {
			logx.Info("已添加万里牛商品同步任务，每小时执行一次")
		}

		// 万里牛商品分类同步 - 每小时执行一次（比商品同步提前执行）
		wanLiNiuCategorySyncService := tasks.NewWanLiNiuCategorySyncService()
		if wanLiNiuCategorySyncService != nil {
			_, err = s.cron.AddFunc("0 20 * * * ?", func() {
				logx.Info("定时任务：开始万里牛商品分类同步...")
				if syncErr := wanLiNiuCategorySyncService.SyncCategories(); syncErr != nil {
					logx.Errorf("万里牛商品分类同步失败: %v", syncErr)
				} else {
					logx.Info("定时任务：万里牛商品分类同步完成")
				}
			})
			if err != nil {
				logx.Errorf("添加万里牛商品分类同步任务失败: %v", err)
			} else {
				logx.Info("已添加万里牛商品分类同步任务，每小时执行一次")
			}
		}

		// 万里牛库存同步 - 每小时执行一次（在商品同步后执行）
		wanLiNiuInventorySyncService := tasks.NewWanLiNiuInventorySyncService()
		if wanLiNiuInventorySyncService != nil {
			_, err = s.cron.AddFunc("0 35 * * * ?", func() {
				logx.Info("定时任务：开始万里牛库存同步...")
				if syncErr := wanLiNiuInventorySyncService.SyncInventory(); syncErr != nil {
					logx.Errorf("万里牛库存同步失败: %v", syncErr)
				} else {
					logx.Info("定时任务：万里牛库存同步完成")
				}
			})
			if err != nil {
				logx.Errorf("添加万里牛库存同步任务失败: %v", err)
			} else {
				logx.Info("已添加万里牛库存同步任务，每小时执行一次")
			}
		}

		// 万里牛发货状态同步 - 每20分钟执行一次
		wanLiNiuShippingSyncService := tasks.NewWanLiNiuShippingSyncService()
		if wanLiNiuShippingSyncService != nil {
			_, err = s.cron.AddFunc("0 */20 * * * ?", func() {
				logx.Info("定时任务：开始万里牛发货状态同步...")
				if syncErr := wanLiNiuShippingSyncService.SyncShippingStatus(); syncErr != nil {
					logx.Errorf("万里牛发货状态同步失败: %v", syncErr)
				} else {
					logx.Info("定时任务：万里牛发货状态同步完成")
				}
			})
			if err != nil {
				logx.Errorf("添加万里牛发货状态同步任务失败: %v", err)
			} else {
				logx.Info("已添加万里牛发货状态同步任务，每20分钟执行一次")
			}
		}

		// 万里牛失败订单推送任务 - 临时改为每2分钟执行一次（用于快速验证签名修正）
		wanLiNiuFailedPushService := tasks.NewWanLiNiuFailedPushService()
		if wanLiNiuFailedPushService != nil {
			_, err = s.cron.AddFunc("0 */2 * * * ?", func() {
				logx.Info("定时任务：开始万里牛失败订单推送...")
				if pushErr := wanLiNiuFailedPushService.PushFailedOrders(); pushErr != nil {
					logx.Errorf("万里牛失败订单推送失败: %v", pushErr)
				} else {
					logx.Info("定时任务：万里牛失败订单推送完成")
				}
			})
			if err != nil {
				logx.Errorf("添加万里牛失败订单推送任务失败: %v", err)
			} else {
				logx.Info("已添加万里牛失败订单推送任务，每2分钟执行一次（临时调整）")
			}
		}
	} else {
		logx.Info("万里牛客户端未初始化，跳过万里牛商品同步任务")
	}

	// 添加优惠券发放任务处理器
	couponIssueTaskProcessor := tasks.NewCouponIssueTaskProcessor()
	if couponIssueTaskProcessor != nil {
		// 优惠券发放任务处理 - 每2分钟执行一次
		_, err = s.cron.AddFunc("0 */2 * * * ?", func() {
			logx.Info("定时任务：开始处理优惠券发放任务...")
			couponIssueTaskProcessor.ProcessTasks()
			logx.Info("定时任务：优惠券发放任务处理完成")
		})
		if err != nil {
			logx.Errorf("添加优惠券发放任务处理器失败: %v", err)
		} else {
			logx.Info("已添加优惠券发放任务处理器，每2分钟执行一次")
		}
	}

	// 添加订单自动关闭任务
	orderAutoCloseService := tasks.NewOrderAutoCloseService()
	if orderAutoCloseService != nil {
		// 订单自动关闭任务 - 每2分钟执行一次
		_, err = s.cron.AddFunc("0 */2 * * * ?", func() {
			logx.Info("定时任务：开始检查并关闭过期订单...")
			if closeErr := orderAutoCloseService.CloseExpiredOrders(); closeErr != nil {
				logx.Errorf("订单自动关闭任务失败: %v", closeErr)
			} else {
				logx.Info("定时任务：订单自动关闭任务完成")
			}
		})
		if err != nil {
			logx.Errorf("添加订单自动关闭任务失败: %v", err)
		} else {
			logx.Info("已添加订单自动关闭任务，每2分钟执行一次")
		}
	}

	// 添加用户等级升级任务
	userLevelUpgradeService := tasks.NewUserLevelUpgradeService()
	if userLevelUpgradeService != nil {
		// 用户等级升级任务 - 每小时执行一次
		_, err = s.cron.AddFunc("0 0 * * * ?", func() {
			logx.Info("定时任务：开始用户等级升级检查...")
			userLevelUpgradeService.RunUpgradeTask()
			logx.Info("定时任务：用户等级升级检查完成")
		})
		if err != nil {
			logx.Errorf("添加用户等级升级任务失败: %v", err)
		} else {
			logx.Info("已添加用户等级升级任务，每小时执行一次")
		}
	}

	// 添加门店地理编码同步任务（如果配置了腾讯地图）
	// 注意：这里需要从配置中获取腾讯地图配置，暂时跳过
	// TODO: 添加门店地理编码同步定时任务

	// 添加科室数据同步任务（如果杭州HIS客户端已初始化）
	if s.hangzhouClient != nil {
		departmentSyncService := tasks.NewDepartmentSyncService(s.hangzhouClient, true, 1)
		// 科室数据同步 - 每天凌晨2点执行一次
		_, err = s.cron.AddFunc("0 0 2 * * ?", func() {
			logx.Info("定时任务：开始科室数据同步...")
			departmentSyncService.SyncDepartments()
			logx.Info("定时任务：科室数据同步完成")
		})
		if err != nil {
			logx.Errorf("添加科室数据同步任务失败: %v", err)
		} else {
			logx.Info("已添加科室数据同步任务，每天凌晨2点执行一次")
		}
	}

	// 叶小币奖励任务
	coinRewardTaskService := tasks.NewCoinRewardTaskService()
	if coinRewardTaskService != nil {
		// 处理待处理的消费奖励 - 每10分钟执行
		_, err = s.cron.AddFunc("0 */10 * * * ?", func() {
			logx.Info("定时任务：开始处理待处理的消费奖励...")
			if err := coinRewardTaskService.ProcessPendingConsumptionRewards(); err != nil {
				logx.Errorf("处理待处理消费奖励失败: %v", err)
			} else {
				logx.Info("定时任务：处理待处理的消费奖励完成")
			}
		})
		if err != nil {
			logx.Errorf("添加消费奖励处理任务失败: %v", err)
		} else {
			logx.Info("已添加消费奖励处理任务，每10分钟执行")
		}

		// 处理过期积分 - 每天凌晨4点执行
		_, err = s.cron.AddFunc("0 0 4 * * ?", func() {
			logx.Info("定时任务：开始处理过期积分...")
			if err := coinRewardTaskService.ProcessExpiredCoins(); err != nil {
				logx.Errorf("处理过期积分失败: %v", err)
			} else {
				logx.Info("定时任务：处理过期积分完成")
			}
		})
		if err != nil {
			logx.Errorf("添加过期积分处理任务失败: %v", err)
		} else {
			logx.Info("已添加过期积分处理任务，每天凌晨4点执行")
		}

		// 处理每日打卡奖励补偿 - 每天凌晨5点执行
		_, err = s.cron.AddFunc("0 0 5 * * ?", func() {
			logx.Info("定时任务：开始处理每日打卡奖励补偿...")
			if err := coinRewardTaskService.ProcessDailyCheckinRewards(); err != nil {
				logx.Errorf("处理每日打卡奖励补偿失败: %v", err)
			} else {
				logx.Info("定时任务：处理每日打卡奖励补偿完成")
			}
		})
		if err != nil {
			logx.Errorf("添加每日打卡奖励补偿任务失败: %v", err)
		} else {
			logx.Info("已添加每日打卡奖励补偿任务，每天凌晨5点执行")
		}

		// 处理邀请奖励补偿 - 每天凌晨6点执行
		_, err = s.cron.AddFunc("0 0 6 * * ?", func() {
			logx.Info("定时任务：开始处理邀请奖励补偿...")
			if err := coinRewardTaskService.ProcessInvitationRewards(); err != nil {
				logx.Errorf("处理邀请奖励补偿失败: %v", err)
			} else {
				logx.Info("定时任务：处理邀请奖励补偿完成")
			}
		})
		if err != nil {
			logx.Errorf("添加邀请奖励补偿任务失败: %v", err)
		} else {
			logx.Info("已添加邀请奖励补偿任务，每天凌晨6点执行")
		}
	}

	// 添加活动相关定时任务
	s.addActivityTasks()

	// 启动定时任务
	s.cron.Start()
	s.active = true

	logx.Info("定时任务服务启动成功")
}

// addActivityTasks 添加活动相关定时任务
func (s *CronService) addActivityTasks() {
	logx.Info("开始添加活动相关定时任务...")

	// 初始化活动任务服务
	activityTaskService := tasks.NewActivityTaskService()

	// 添加过期活动检查任务 - 每小时执行一次
	_, err := s.cron.AddFunc("0 0 * * * ?", func() {
		logx.Info("定时任务：开始过期活动检查...")
		if err := activityTaskService.ProcessExpiredActivities(); err != nil {
			logx.Errorf("定时任务：过期活动检查失败: %v", err)
		} else {
			logx.Info("定时任务：过期活动检查完成")
		}
	})
	if err != nil {
		logx.Errorf("添加过期活动检查任务失败: %v", err)
	} else {
		logx.Info("已添加过期活动检查任务，每小时执行一次")
	}

	// 添加活动报名人数更新任务 - 每10分钟执行一次
	_, err = s.cron.AddFunc("0 */10 * * * ?", func() {
		logx.Info("定时任务：开始活动报名人数更新...")
		if err := activityTaskService.UpdateActivitySignUpCounts(); err != nil {
			logx.Errorf("定时任务：活动报名人数更新失败: %v", err)
		} else {
			logx.Info("定时任务：活动报名人数更新完成")
		}
	})
	if err != nil {
		logx.Errorf("添加活动报名人数更新任务失败: %v", err)
	} else {
		logx.Info("已添加活动报名人数更新任务，每10分钟执行一次")
	}

	// 添加过期二维码清理任务 - 每天凌晨2点执行一次
	_, err = s.cron.AddFunc("0 0 2 * * ?", func() {
		logx.Info("定时任务：开始过期二维码清理...")
		if err := activityTaskService.CleanupExpiredQRCodes(); err != nil {
			logx.Errorf("定时任务：过期二维码清理失败: %v", err)
		} else {
			logx.Info("定时任务：过期二维码清理完成")
		}
	})
	if err != nil {
		logx.Errorf("添加过期二维码清理任务失败: %v", err)
	} else {
		logx.Info("已添加过期二维码清理任务，每天凌晨2点执行一次")
	}

	logx.Info("活动相关定时任务添加完成")
}

// Stop 停止所有定时任务
func (s *CronService) Stop() {
	if !s.active {
		return
	}

	logx.Info("停止定时任务服务...")
	s.cron.Stop()
	s.active = false
	logx.Info("定时任务服务已停止")
}

// RunAllTasksOnce 手动执行所有定时任务一次
func (s *CronService) RunAllTasksOnce() error {
	logx.Info("开始手动执行所有定时任务...")

	var errors []error

	// 1. 执行门诊收费信息同步
	logx.Info("手动执行：门诊收费信息同步...")
	chargeSyncService := tasks.NewChargeSyncService(s.db)
	chargeSyncService.SyncCharges()
	logx.Info("手动执行：门诊收费信息同步完成")

	// 2. 执行医生数据同步
	logx.Info("手动执行：医生数据同步...")
	doctorSyncService := tasks.NewDoctorSyncService(s.db)
	if err := doctorSyncService.SyncDoctors(); err != nil {
		logx.Errorf("医生数据同步失败: %v", err)
		errors = append(errors, fmt.Errorf("医生数据同步失败: %w", err))
	} else {
		logx.Info("手动执行：医生数据同步完成")
	}

	// 3. 执行患者数据同步
	logx.Info("手动执行：患者数据同步...")
	patientSyncService := tasks.NewPatientSyncService(s.db)
	if err := patientSyncService.SyncPatients(); err != nil {
		logx.Errorf("患者数据同步失败: %v", err)
		errors = append(errors, fmt.Errorf("患者数据同步失败: %w", err))
	} else {
		logx.Info("手动执行：患者数据同步完成")
	}

	// 4. 执行ABC云同步任务（如果客户端已初始化）
	if s.abcYunClient != nil {
		// ABC云门店同步
		logx.Info("手动执行：ABC云门店同步...")
		abcYunStoreSyncService := tasks.NewAbcYunStoreSyncService(s.abcYunClient)
		if err := abcYunStoreSyncService.SyncStores(); err != nil {
			logx.Errorf("ABC云门店同步失败: %v", err)
			errors = append(errors, fmt.Errorf("ABC云门店同步失败: %w", err))
		} else {
			logx.Info("手动执行：ABC云门店同步完成")
		}

		// ABC云科室同步
		logx.Info("手动执行：ABC云科室同步...")
		abcYunDeptSyncService := tasks.NewAbcYunDepartmentSyncService(s.abcYunClient)
		if err := abcYunDeptSyncService.SyncDepartments(); err != nil {
			logx.Errorf("ABC云科室同步失败: %v", err)
			errors = append(errors, fmt.Errorf("ABC云科室同步失败: %w", err))
		} else {
			logx.Info("手动执行：ABC云科室同步完成")
		}

		// ABC云医生同步
		logx.Info("手动执行：ABC云医生同步...")
		abcYunDoctorSyncService := tasks.NewAbcYunDoctorSyncService(s.abcYunClient)
		if err := abcYunDoctorSyncService.SyncDoctors(); err != nil {
			logx.Errorf("ABC云医生同步失败: %v", err)
			errors = append(errors, fmt.Errorf("ABC云医生同步失败: %w", err))
		} else {
			logx.Info("手动执行：ABC云医生同步完成")
		}

		// ABC云患者同步
		logx.Info("手动执行：ABC云患者同步...")
		abcYunPatientSyncService := tasks.NewAbcYunPatientSyncSimpleService(s.abcYunClient)
		if err := abcYunPatientSyncService.SyncPatients(); err != nil {
			logx.Errorf("ABC云患者同步失败: %v", err)
			errors = append(errors, fmt.Errorf("ABC云患者同步失败: %w", err))
		} else {
			logx.Info("手动执行：ABC云患者同步完成")
		}
	}

	// 5. 执行万里牛ERP同步任务
	// 万里牛商品分类同步
	logx.Info("手动执行：万里牛商品分类同步...")
	wanLiNiuCategorySyncService := tasks.NewWanLiNiuCategorySyncService()
	if wanLiNiuCategorySyncService != nil {
		if err := wanLiNiuCategorySyncService.SyncCategories(); err != nil {
			logx.Errorf("万里牛商品分类同步失败: %v", err)
			errors = append(errors, fmt.Errorf("万里牛商品分类同步失败: %w", err))
		} else {
			logx.Info("手动执行：万里牛商品分类同步完成")
		}
	}

	// 万里牛商品同步
	logx.Info("手动执行：万里牛商品同步...")
	wanLiNiuGoodsSyncService := tasks.NewWanLiNiuGoodsSyncService()
	if wanLiNiuGoodsSyncService != nil {
		if err := wanLiNiuGoodsSyncService.SyncGoods(); err != nil {
			logx.Errorf("万里牛商品同步失败: %v", err)
			errors = append(errors, fmt.Errorf("万里牛商品同步失败: %w", err))
		} else {
			logx.Info("手动执行：万里牛商品同步完成")
		}
	}

	// 万里牛库存同步
	logx.Info("手动执行：万里牛库存同步...")
	wanLiNiuInventorySyncService := tasks.NewWanLiNiuInventorySyncService()
	if wanLiNiuInventorySyncService != nil {
		if err := wanLiNiuInventorySyncService.SyncInventory(); err != nil {
			logx.Errorf("万里牛库存同步失败: %v", err)
			errors = append(errors, fmt.Errorf("万里牛库存同步失败: %w", err))
		} else {
			logx.Info("手动执行：万里牛库存同步完成")
		}
	}

	// 万里牛发货状态同步
	logx.Info("手动执行：万里牛发货状态同步...")
	wanLiNiuShippingSyncService := tasks.NewWanLiNiuShippingSyncService()
	if wanLiNiuShippingSyncService != nil {
		if err := wanLiNiuShippingSyncService.SyncShippingStatus(); err != nil {
			logx.Errorf("万里牛发货状态同步失败: %v", err)
			errors = append(errors, fmt.Errorf("万里牛发货状态同步失败: %w", err))
		} else {
			logx.Info("手动执行：万里牛发货状态同步完成")
		}
	}

	// 万里牛失败订单推送
	logx.Info("手动执行：万里牛失败订单推送...")
	wanLiNiuFailedPushService := tasks.NewWanLiNiuFailedPushService()
	if wanLiNiuFailedPushService != nil {
		if err := wanLiNiuFailedPushService.PushFailedOrders(); err != nil {
			logx.Errorf("万里牛失败订单推送失败: %v", err)
			errors = append(errors, fmt.Errorf("万里牛失败订单推送失败: %w", err))
		} else {
			logx.Info("手动执行：万里牛失败订单推送完成")
		}
	}

	// 6. 执行业务处理任务
	// 优惠券发放任务处理
	logx.Info("手动执行：优惠券发放任务处理...")
	couponIssueTaskProcessor := tasks.NewCouponIssueTaskProcessor()
	if couponIssueTaskProcessor != nil {
		couponIssueTaskProcessor.ProcessTasks()
		logx.Info("手动执行：优惠券发放任务处理完成")
	}

	// 订单自动关闭任务
	logx.Info("手动执行：订单自动关闭任务...")
	orderAutoCloseService := tasks.NewOrderAutoCloseService()
	if orderAutoCloseService != nil {
		if err := orderAutoCloseService.CloseExpiredOrders(); err != nil {
			logx.Errorf("订单自动关闭任务失败: %v", err)
			errors = append(errors, fmt.Errorf("订单自动关闭任务失败: %w", err))
		} else {
			logx.Info("手动执行：订单自动关闭任务完成")
		}
	}

	// 用户等级升级任务
	logx.Info("手动执行：用户等级升级任务...")
	userLevelUpgradeService := tasks.NewUserLevelUpgradeService()
	if userLevelUpgradeService != nil {
		userLevelUpgradeService.RunUpgradeTask()
		logx.Info("手动执行：用户等级升级任务完成")
	}

	// 7. 执行叶小币奖励任务
	// 叶小币奖励任务处理
	logx.Info("手动执行：叶小币奖励任务...")
	coinRewardTaskService := tasks.NewCoinRewardTaskService()
	if coinRewardTaskService != nil {
		coinRewardTaskService.RunAllCoinRewardTasks()
		logx.Info("手动执行：叶小币奖励任务完成")
	}

	// 汇总执行结果
	if len(errors) > 0 {
		logx.Errorf("手动执行定时任务完成，但有 %d 个任务失败", len(errors))
		for i, err := range errors {
			logx.Errorf("错误 %d: %v", i+1, err)
		}
		return fmt.Errorf("有 %d 个任务执行失败", len(errors))
	}

	logx.Info("所有定时任务手动执行成功完成")
	return nil
}
