package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"yekaitai/pkg/adapters/wanliniu"
	orderModel "yekaitai/pkg/common/model/order"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// OrderPushService 万里牛订单推送服务
type OrderPushService struct {
	db              *gorm.DB
	wanliniuService *wanliniu.Service
}

// NewOrderPushService 创建万里牛订单推送服务
func NewOrderPushService() *OrderPushService {
	return &OrderPushService{
		db:              mysql.GetDB(),
		wanliniuService: wanliniu.GetService(), // 使用全局的万里牛服务实例
	}
}

// PushOrderToERP 推送订单到万里牛ERP
func (s *OrderPushService) PushOrderToERP(ctx context.Context, orderNo string) error {
	// 检查万里牛服务是否已初始化
	if s.wanliniuService == nil {
		return fmt.Errorf("万里牛客户端未初始化")
	}

	// 获取订单详情
	var order orderModel.Order
	err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error
	if err != nil {
		return fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查订单状态，只推送已支付的订单
	if order.Status < 2 { // 2=已支付
		logx.Infof("[WanLiNiu] 订单状态为%d（未支付），跳过推送: 订单号=%s", order.Status, orderNo)
		return fmt.Errorf("订单状态为%d（未支付），不允许推送到万里牛", order.Status)
	}

	logx.Infof("[WanLiNiu] 订单状态检查通过: 订单号=%s, 状态=%d, 支付时间=%v",
		orderNo, order.Status, order.PayTime)

	// 获取订单商品项
	var items []orderModel.OrderItem
	err = s.db.WithContext(ctx).Where("order_id = ?", order.ID).Find(&items).Error
	if err != nil {
		return fmt.Errorf("获取订单商品项失败: %w", err)
	}

	// 构建万里牛订单数据
	trade := s.buildWanLiNiuTrade(&order, items)

	// 打印构建的订单数据
	logx.Infof("[WanLiNiu] 构建的订单数据: 订单号=%s, 店铺昵称=%s, 创建时间=%s, 状态=%d",
		trade.TradeID, trade.ShopNick, trade.CreateTime, trade.Status)
	logx.Infof("[WanLiNiu] 收件人信息: 姓名=%s, 手机=%s, 省=%s, 市=%s, 地址=%s",
		trade.ReceiverName, trade.ReceiverMobile, trade.ReceiverProvince, trade.ReceiverCity, trade.ReceiverAddress)
	logx.Infof("[WanLiNiu] 订单明细数量: %d", len(trade.Orders))
	for i, item := range trade.Orders {
		logx.Infof("[WanLiNiu] 明细[%d]: ItemID=%s, SkuID=%s, 标题=%s, 数量=%d",
			i, item.ItemID, item.SkuID, item.ItemTitle, item.Size)
	}

	// 推送到万里牛ERP
	logx.Infof("[WanLiNiu] 开始推送订单到万里牛ERP: 订单号=%s", orderNo)
	logx.Infof("[WanLiNiu] 推送的订单数据详情:")
	logx.Infof("[WanLiNiu] - 订单号: %s", trade.TradeID)
	logx.Infof("[WanLiNiu] - 订单状态: %d", trade.Status)
	logx.Infof("[WanLiNiu] - 支付时间: %s", trade.PayTime)
	logx.Infof("[WanLiNiu] - 支付金额: %.2f", trade.Payment)
	logx.Infof("[WanLiNiu] - 本地订单状态: %d", order.Status)
	logx.Infof("[WanLiNiu] - 本地支付时间: %v", order.PayTime)

	err = s.wanliniuService.PushTrades(ctx, []wanliniu.Trade{trade})

	// 更新推送状态
	now := time.Now()
	updates := map[string]interface{}{
		"wanliniu_last_push_time": &now,
	}

	if err != nil {
		// 推送失败
		logx.Errorf("[WanLiNiu] 推送订单失败: 订单号=%s, 错误=%v", orderNo, err)
		updates["wanliniu_order_push_status"] = orderModel.WanLiNiuPushStatusFailed
		updates["wanliniu_order_push_count"] = gorm.Expr("wanliniu_order_push_count + 1")
		updates["wanliniu_push_error"] = err.Error()

		// 更新数据库状态
		if updateErr := s.db.Model(&order).Updates(updates).Error; updateErr != nil {
			logx.Errorf("[WanLiNiu] 更新订单推送失败状态失败: %s, 错误: %v", orderNo, updateErr)
		}

		return fmt.Errorf("推送订单到万里牛ERP失败: %w", err)
	}

	// 推送成功
	updates["wanliniu_order_push_status"] = orderModel.WanLiNiuPushStatusSuccess
	updates["wanliniu_push_error"] = ""

	// 更新数据库状态
	if updateErr := s.db.Model(&order).Updates(updates).Error; updateErr != nil {
		logx.Errorf("[WanLiNiu] 更新订单推送成功状态失败: %s, 错误: %v", orderNo, updateErr)
	}

	logx.Infof("订单推送到万里牛ERP成功: 订单号=%s", orderNo)
	return nil
}

// PushRefundToERP 推送售后单到万里牛ERP
func (s *OrderPushService) PushRefundToERP(ctx context.Context, orderNo, refundNo, refundReason string, refundAmount float64) error {
	// 检查万里牛服务是否已初始化
	if s.wanliniuService == nil {
		return fmt.Errorf("万里牛客户端未初始化")
	}

	// 获取订单详情
	var order orderModel.Order
	err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error
	if err != nil {
		return fmt.Errorf("获取订单失败: %w", err)
	}

	// 构建万里牛售后单数据
	refund := s.buildWanLiNiuRefund(&order, refundNo, refundReason, refundAmount)

	// 推送到万里牛ERP
	err = s.wanliniuService.PushRefund(ctx, refund)

	// 更新推送状态
	now := time.Now()
	updates := map[string]interface{}{
		"wanliniu_last_push_time": &now,
	}

	if err != nil {
		// 推送失败
		logx.Errorf("[WanLiNiu] 推送售后单失败: 订单号=%s, 退款单号=%s, 错误=%v", orderNo, refundNo, err)
		updates["wanliniu_refund_push_status"] = orderModel.WanLiNiuPushStatusFailed
		updates["wanliniu_refund_push_count"] = gorm.Expr("wanliniu_refund_push_count + 1")
		updates["wanliniu_push_error"] = err.Error()

		// 更新数据库状态
		if updateErr := s.db.Model(&order).Updates(updates).Error; updateErr != nil {
			logx.Errorf("[WanLiNiu] 更新售后单推送失败状态失败: 订单号=%s, 错误: %v", orderNo, updateErr)
		}

		return fmt.Errorf("推送售后单到万里牛ERP失败: %w", err)
	}

	// 推送成功
	updates["wanliniu_refund_push_status"] = orderModel.WanLiNiuPushStatusSuccess
	updates["wanliniu_push_error"] = ""

	// 更新数据库状态
	if updateErr := s.db.Model(&order).Updates(updates).Error; updateErr != nil {
		logx.Errorf("[WanLiNiu] 更新售后单推送成功状态失败: 订单号=%s, 错误: %v", orderNo, updateErr)
	}

	logx.Infof("售后单推送到万里牛ERP成功: 订单号=%s, 退款单号=%s", orderNo, refundNo)
	return nil
}

// buildWanLiNiuTrade 构建万里牛订单数据 - 使用真实数据
func (s *OrderPushService) buildWanLiNiuTrade(order *orderModel.Order, items []orderModel.OrderItem) wanliniu.Trade {
	// 构建订单明细 - 填充必要字段
	tradeOrders := make([]wanliniu.TradeOrder, 0, len(items))
	for _, item := range items {
		tradeOrder := wanliniu.TradeOrder{
			// 必填字段
			ItemID:    fmt.Sprintf("GOODS_%d", item.GoodsID), // 使用商品ID构建万里牛商品ID（必填）
			SkuID:     fmt.Sprintf("SPEC_%d", item.SpecID),   // 使用规格ID构建万里牛规格ID（必填）
			ItemTitle: item.GoodsName,                        // 商品标题（必填）
			Size:      item.Quantity,                         // 商品数量（必填）

			// 可选但重要字段
			Price:     item.Price,                            // 商品单价
			Payment:   item.TotalPrice,                       // 商品实付
			OrderID:   fmt.Sprintf("ORDER_ITEM_%d", item.ID), // 子订单ID
			Status:    2,                                     // 明细状态：2-待发货
			HasRefund: 0,                                     // 无退款
		}

		tradeOrders = append(tradeOrders, tradeOrder)
	}

	// 获取万里牛客户端配置
	client := wanliniu.GetClient()
	shopNick := "叶开泰商城" // 默认店铺昵称
	if client != nil {
		config := client.GetConfig()
		if config.ShopNick != "" {
			shopNick = config.ShopNick
		}
	}

	// 计算订单金额
	var totalFee float64
	for _, item := range items {
		totalFee += item.TotalPrice
	}

	// 获取真实的收货地址信息
	receiverInfo := s.getReceiverInfo(order)

	// 构建万里牛订单 - 使用真实数据
	trade := wanliniu.Trade{
		// 必填字段
		TradeID:          order.OrderNo,                                 // 第三方交易号（必填）
		CreateTime:       order.CreatedAt.Format("2006-01-02 15:04:05"), // 交易创建时间（必填）
		ModifyTime:       order.UpdatedAt.Format("2006-01-02 15:04:05"), // 交易最新修改时间（必填）
		ShopNick:         shopNick,                                      // 店铺昵称（必填）
		Status:           s.getWanLiNiuOrderStatus(order.Status),        // 线上交易状态（必填）
		ReceiverName:     receiverInfo.Name,                             // 收件人姓名（真实数据）
		ReceiverMobile:   receiverInfo.Phone,                            // 收件人手机（真实数据）
		ReceiverProvince: receiverInfo.Province,                         // 收件人省份（真实数据）
		ReceiverCity:     receiverInfo.City,                             // 收件人城市（真实数据）
		ReceiverAddress:  receiverInfo.Address,                          // 收件人详细地址（真实数据）
		Orders:           tradeOrders,                                   // 交易明细集（必填）
		Buyer:            fmt.Sprintf("USER_%d", order.UserID),          // 买家（必填）

		// 常用可选字段
		BuyerMobile: receiverInfo.Phone,   // 订购人手机（使用收件人手机）
		Payment:     totalFee,             // 应收款（真实数据）
		TotalFee:    totalFee,             // 总金额（真实数据）
		PostFee:     order.ShippingAmount, // 邮费（真实数据）
		PayTime:     s.getPayTime(order),  // 付款时间（真实数据）
		PayType:     s.getPayType(order),  // 支付类型（真实数据）
		TradeType:   1,                    // 订单类型：1-一口价

		// 其他有用字段
		HasRefund:    s.getHasRefund(order), // 退款状态（真实数据）
		DiscountFee:  order.DiscountAmount,  // 优惠金额（真实数据）
		BuyerMessage: order.Remark,          // 买家留言（真实数据）
		SellerMemo:   "",                    // 卖家备注
		ShippingType: 0,                     // 发货类型：0-快递
	}

	return trade
}

// buildWanLiNiuRefund 构建万里牛售后单数据
func (s *OrderPushService) buildWanLiNiuRefund(order *orderModel.Order, refundNo, refundReason string, refundAmount float64) wanliniu.RefundInfo {
	// 获取万里牛客户端配置
	client := wanliniu.GetClient()
	shopNick := "叶开泰商城" // 默认店铺昵称
	if client != nil {
		config := client.GetConfig()
		if config.ShopNick != "" {
			shopNick = config.ShopNick
		}
	}

	// 获取订单商品项
	var items []orderModel.OrderItem
	err := s.db.Where("order_id = ?", order.ID).Find(&items).Error
	if err != nil {
		logx.Errorf("[WanLiNiu] 获取订单商品项失败: %v", err)
		items = []orderModel.OrderItem{} // 使用空数组
	}

	// 构建退款明细
	refundItems := make([]wanliniu.RefundItem, 0, len(items))
	for _, item := range items {
		refundItem := wanliniu.RefundItem{
			OrderID:      fmt.Sprintf("ORDER_ITEM_%d", item.ID), // 子订单ID
			ChangeItemID: fmt.Sprintf("GOODS_%d", item.GoodsID), // 换货商品ID（对于退款，填入原商品ID）
			ChangeSkuID:  fmt.Sprintf("SPEC_%d", item.SpecID),   // 换货规格ID（对于退款，填入原规格ID）
			ChangeNum:    item.Quantity,                         // 换货数量（对于退款，填入原数量）
		}
		refundItems = append(refundItems, refundItem)
	}

	// 构建收件人信息（退货收件人信息）- 使用真实的店铺配置
	receiver := s.getRefundReceiver()

	refund := wanliniu.RefundInfo{
		// 必填字段
		RefundID: refundNo,      // 售后单号（不能为空）
		TradeID:  order.OrderNo, // 第三方交易号（不能为空）
		ShopNick: shopNick,      // 在ERP中注册的卖家（店铺）昵称（不能为空）
		Status:   1,             // 售后单状态：1-换货待处理（不能为空）
		Type:     1,             // 售后类型：1-仅退款（不能为空）

		// 重要字段
		Buyer:     fmt.Sprintf("USER_%d", order.UserID),     // 买家昵称
		Create:    time.Now().Format("2006-01-02 15:04:05"), // 创建时间，格式化为万里牛要求的格式
		Modified:  time.Now().Format("2006-01-02 15:04:05"), // 修改时间
		Reason:    refundReason,                             // 售后原因
		RefundFee: refundAmount,                             // 售后金额
		Items:     refundItems,                              // 退换货明细（必需！）
		Receiver:  receiver,                                 // 收件人信息

		// 可选字段
		EndTime:       "", // 完成时间（可选）
		ExpressCode:   "", // 退货快递单号（可选）
		LogisticsName: "", // 物流公司（可选）
	}

	// 打印构建的退款数据用于调试
	logx.Infof("[WanLiNiu] 构建的退款数据: 退款单号=%s, 订单号=%s, 店铺昵称=%s, 退款类型=%d, 状态=%d",
		refund.RefundID, refund.TradeID, refund.ShopNick, refund.Type, refund.Status)
	logx.Infof("[WanLiNiu] 退款金额=%.2f, 买家=%s, 创建时间=%s, 原因=%s",
		refund.RefundFee, refund.Buyer, refund.Create, refund.Reason)
	logx.Infof("[WanLiNiu] 退款明细数量: %d", len(refund.Items))
	for i, item := range refund.Items {
		logx.Infof("[WanLiNiu] 退款明细[%d]: OrderID=%s, ChangeItemID=%s, ChangeSkuID=%s, ChangeNum=%d",
			i, item.OrderID, item.ChangeItemID, item.ChangeSkuID, item.ChangeNum)
	}
	if refund.Receiver != nil {
		logx.Infof("[WanLiNiu] 收件人: 姓名=%s, 手机=%s, 省=%s, 市=%s, 地址=%s",
			refund.Receiver.ReceiverName, refund.Receiver.ReceiverMobile,
			refund.Receiver.ReceiverProvince, refund.Receiver.ReceiverCity, refund.Receiver.ReceiverAddress)
	}

	return refund
}

// getWanLiNiuOrderStatus 获取万里牛订单状态（修正状态映射）
// 本地订单状态：1=待支付,2=已支付,3=已发货,4=已完成,5=已取消,6=已退款
// 万里牛状态定义：0=未创建交易,1=待付款,2=待发货,3=已完成,4=已关闭,5=已发货待确认,6=已签收
func (s *OrderPushService) getWanLiNiuOrderStatus(status int) int {
	switch status {
	case 1: // 本地：待支付 (OrderStatusPending)
		return 1 // 万里牛：待付款
	case 2: // 本地：已支付 (OrderStatusPaid)
		return 2 // 万里牛：待发货（修正：已支付应该是待发货状态）
	case 3: // 本地：已发货 (OrderStatusShipped)
		return 5 // 万里牛：已发货待确认（修正：已发货应该是待确认状态）
	case 4: // 本地：已完成 (OrderStatusCompleted)
		return 6 // 万里牛：已签收（修正：已完成应该是已签收状态）
	case 5: // 本地：已取消 (OrderStatusCancelled)
		return 4 // 万里牛：已关闭
	case 6: // 本地：已退款 (OrderStatusRefunded)
		return 4 // 万里牛：已关闭（退款订单在万里牛中应该是已关闭状态）
	default:
		return 1 // 默认：待付款
	}
}

// ReceiverInfo 收件人信息结构
type ReceiverInfo struct {
	Name     string
	Phone    string
	Province string
	City     string
	District string
	Address  string
}

// getReceiverInfo 获取真实的收货地址信息
func (s *OrderPushService) getReceiverInfo(order *orderModel.Order) ReceiverInfo {
	// 默认收件人信息（作为兜底）
	defaultReceiver := ReceiverInfo{
		Name:     "收件人",
		Phone:    "13800138000",
		Province: "湖北省",
		City:     "武汉市",
		District: "洪山区",
		Address:  "详细地址",
	}

	// 如果订单中有地址信息快照，优先使用
	if order.AddressInfo != "" {
		// 尝试解析地址信息JSON
		var addressSnapshot map[string]interface{}
		if err := json.Unmarshal([]byte(order.AddressInfo), &addressSnapshot); err == nil {
			receiver := ReceiverInfo{}

			// 安全地获取各个字段
			if name, ok := addressSnapshot["name"].(string); ok && name != "" {
				receiver.Name = name
			} else {
				receiver.Name = defaultReceiver.Name
			}

			if phone, ok := addressSnapshot["phone"].(string); ok && phone != "" {
				receiver.Phone = phone
			} else {
				receiver.Phone = defaultReceiver.Phone
			}

			if province, ok := addressSnapshot["province_name"].(string); ok && province != "" {
				receiver.Province = province
			} else if province, ok := addressSnapshot["province"].(string); ok && province != "" {
				receiver.Province = province
			} else {
				receiver.Province = defaultReceiver.Province
			}

			if city, ok := addressSnapshot["city_name"].(string); ok && city != "" {
				receiver.City = city
			} else if city, ok := addressSnapshot["city"].(string); ok && city != "" {
				receiver.City = city
			} else {
				receiver.City = defaultReceiver.City
			}

			if district, ok := addressSnapshot["district_name"].(string); ok && district != "" {
				receiver.District = district
			} else if district, ok := addressSnapshot["district"].(string); ok && district != "" {
				receiver.District = district
			} else {
				receiver.District = defaultReceiver.District
			}

			if address, ok := addressSnapshot["address"].(string); ok && address != "" {
				// 组合完整地址：区县 + 详细地址
				receiver.Address = receiver.District + address
			} else {
				receiver.Address = defaultReceiver.Address
			}

			logx.Infof("[WanLiNiu] 使用地址快照: 姓名=%s, 手机=%s, 省=%s, 市=%s, 地址=%s",
				receiver.Name, receiver.Phone, receiver.Province, receiver.City, receiver.Address)
			return receiver
		}
	}

	// 如果没有地址快照或解析失败，尝试从地址表查询
	if order.AddressID > 0 {
		var address struct {
			Name         string `gorm:"column:name"`
			Phone        string `gorm:"column:phone"`
			ProvinceCode string `gorm:"column:province_code"`
			CityCode     string `gorm:"column:city_code"`
			DistrictCode string `gorm:"column:district_code"`
			Address      string `gorm:"column:address"`
		}

		err := s.db.Table("addresses").
			Where("id = ?", order.AddressID).
			First(&address).Error

		if err == nil {
			// 查询地区名称
			var regions []struct {
				Code string `gorm:"column:code"`
				Name string `gorm:"column:name"`
			}

			codes := []string{address.ProvinceCode, address.CityCode, address.DistrictCode}
			s.db.Raw("SELECT code, name FROM t_regions WHERE code IN (?)", codes).Scan(&regions)

			// 建立编码到名称的映射
			nameMap := make(map[string]string)
			for _, region := range regions {
				nameMap[region.Code] = region.Name
			}

			receiver := ReceiverInfo{
				Name:     address.Name,
				Phone:    address.Phone,
				Province: nameMap[address.ProvinceCode],
				City:     nameMap[address.CityCode],
				District: nameMap[address.DistrictCode],
				Address:  nameMap[address.DistrictCode] + address.Address,
			}

			// 如果地区名称为空，使用默认值
			if receiver.Province == "" {
				receiver.Province = defaultReceiver.Province
			}
			if receiver.City == "" {
				receiver.City = defaultReceiver.City
			}
			if receiver.District == "" {
				receiver.District = defaultReceiver.District
			}

			logx.Infof("[WanLiNiu] 使用地址表数据: 姓名=%s, 手机=%s, 省=%s, 市=%s, 地址=%s",
				receiver.Name, receiver.Phone, receiver.Province, receiver.City, receiver.Address)
			return receiver
		} else {
			logx.Errorf("[WanLiNiu] 查询地址表失败: %v", err)
		}
	}

	// 兜底：使用默认地址
	logx.Infof("[WanLiNiu] 无法获取收货地址，使用默认地址")
	return defaultReceiver
}

// getPayTime 获取支付时间（修正支付时间填写逻辑）
func (s *OrderPushService) getPayTime(order *orderModel.Order) string {
	// 根据万里牛文档：付款后必填pay_time字段
	// 只有当订单状态>=已支付时，才填写支付时间
	if order.Status >= 2 && order.PayTime != nil { // 2=已支付
		return order.PayTime.Format("2006-01-02 15:04:05")
	}

	// 未支付或支付时间为空时，返回空字符串
	return ""
}

// getPayType 获取支付类型
func (s *OrderPushService) getPayType(order *orderModel.Order) string {
	if order.PayMethod != "" {
		return order.PayMethod
	}

	// 根据支付方式字段判断
	switch order.PaymentMethod {
	case 1:
		return "微信支付"
	case 2:
		return "支付宝"
	case 3:
		return "现金支付"
	default:
		return "微信支付" // 默认微信支付
	}
}

// getHasRefund 获取是否有退款
func (s *OrderPushService) getHasRefund(order *orderModel.Order) int {
	if order.RefundStatus > 0 {
		return 1 // 有退款
	}
	return 0 // 无退款
}

// getRefundReceiver 获取退货收件人信息（店铺信息）
func (s *OrderPushService) getRefundReceiver() *wanliniu.RefundReceiver {
	// 默认退货收件人信息
	defaultReceiver := &wanliniu.RefundReceiver{
		ReceiverName:     "叶开泰商城客服",
		ReceiverMobile:   "************",
		ReceiverProvince: "湖北省",
		ReceiverCity:     "武汉市",
		ReceiverArea:     "洪山区",
		ReceiverAddress:  "珞喻路1037号华中科技大学",
	}

	// 尝试从店铺配置表获取真实信息
	var storeConfig struct {
		StoreName    string `gorm:"column:store_name"`
		StorePhone   string `gorm:"column:store_phone"`
		StoreAddress string `gorm:"column:store_address"`
		ProvinceID   string `gorm:"column:province_id"`
		CityID       string `gorm:"column:city_id"`
		AreaID       string `gorm:"column:area_id"`
	}

	err := s.db.Table("store_configs").
		Select("store_name, store_phone, store_address, province_id, city_id, area_id").
		First(&storeConfig).Error

	if err == nil && storeConfig.StoreName != "" {
		// 查询地区名称
		var regions []struct {
			Code string `gorm:"column:code"`
			Name string `gorm:"column:name"`
		}

		codes := []string{storeConfig.ProvinceID, storeConfig.CityID, storeConfig.AreaID}
		s.db.Raw("SELECT code, name FROM t_regions WHERE code IN (?)", codes).Scan(&regions)

		// 建立编码到名称的映射
		nameMap := make(map[string]string)
		for _, region := range regions {
			nameMap[region.Code] = region.Name
		}

		receiver := &wanliniu.RefundReceiver{
			ReceiverName:     storeConfig.StoreName + "客服",
			ReceiverMobile:   storeConfig.StorePhone,
			ReceiverProvince: nameMap[storeConfig.ProvinceID],
			ReceiverCity:     nameMap[storeConfig.CityID],
			ReceiverArea:     nameMap[storeConfig.AreaID],
			ReceiverAddress:  storeConfig.StoreAddress,
		}

		// 如果某些字段为空，使用默认值
		if receiver.ReceiverName == "客服" || receiver.ReceiverName == "" {
			receiver.ReceiverName = defaultReceiver.ReceiverName
		}
		if receiver.ReceiverMobile == "" {
			receiver.ReceiverMobile = defaultReceiver.ReceiverMobile
		}
		if receiver.ReceiverProvince == "" {
			receiver.ReceiverProvince = defaultReceiver.ReceiverProvince
		}
		if receiver.ReceiverCity == "" {
			receiver.ReceiverCity = defaultReceiver.ReceiverCity
		}
		if receiver.ReceiverArea == "" {
			receiver.ReceiverArea = defaultReceiver.ReceiverArea
		}
		if receiver.ReceiverAddress == "" {
			receiver.ReceiverAddress = defaultReceiver.ReceiverAddress
		}

		logx.Infof("[WanLiNiu] 使用店铺配置退货地址: 名称=%s, 手机=%s, 省=%s, 市=%s, 地址=%s",
			receiver.ReceiverName, receiver.ReceiverMobile, receiver.ReceiverProvince, receiver.ReceiverCity, receiver.ReceiverAddress)
		return receiver
	} else {
		logx.Infof("[WanLiNiu] 未找到店铺配置，使用默认退货地址")
	}

	return defaultReceiver
}
