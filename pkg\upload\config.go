package upload

// QiniuConfig 七牛云对象存储配置
type QiniuConfig struct {
	AccessKey string // 七牛云 AccessKey
	SecretKey string // 七牛云 SecretKey
	Bucket    string // 七牛云存储空间名称
	Domain    string // 七牛云CDN域名
	Zone      string // 七牛云存储区域
}

// DefaultQiniuConfig 默认七牛云配置
var DefaultQiniuConfig = QiniuConfig{
	AccessKey: "-BftVKiSPGF4Aznszuynn8NqTlx5xzYEr9hNIRUP",
	SecretKey: "dEBEjShOwfqJYPSnn4jocunXwkU2oCsMOZIO1Ck0",
	Bucket:    "pub-med-yekaitai",
	Domain:    "pub-med-yekaitai.medlinker.com",
	Zone:      "z2", // 华南-广东 对应的区域代码为 z2
}

// UploadConfig 上传相关配置
type UploadConfig struct {
	MaxImageSize    int64    // 最大图片大小 默认10MB
	MaxDocumentSize int64    // 最大文档大小 默认50MB
	MaxVideoSize    int64    // 最大视频大小 默认1.5GB
	MaxGeneralSize  int64    // 最大通用文件大小 默认100MB
	AllowedDomains  []string // 允许的跨域来源
}

// DefaultUploadConfig 默认上传配置
var DefaultUploadConfig = UploadConfig{
	MaxImageSize:    10 << 20,   // 10MB
	MaxDocumentSize: 50 << 20,   // 50MB
	MaxVideoSize:    1536 << 20, // 1.5GB
	MaxGeneralSize:  100 << 20,  // 100MB
	AllowedDomains:  []string{"*"},
}

// Config 全局上传配置结构
type Config struct {
	Qiniu  QiniuConfig  // 七牛云配置
	Upload UploadConfig // 上传相关配置
}

// DefaultConfig 默认配置
var DefaultConfig = Config{
	Qiniu:  DefaultQiniuConfig,
	Upload: DefaultUploadConfig,
}

// globalConfig 全局配置实例
var globalConfig = DefaultConfig

// SetConfig 设置全局配置
func SetConfig(cfg Config) {
	globalConfig = cfg
}

// GetConfig 获取全局配置
func GetConfig() Config {
	return globalConfig
}

// GetUploadConfig 获取上传配置
func GetUploadConfig() UploadConfig {
	return globalConfig.Upload
}

// GetQiniuConfig 获取七牛云配置
func GetQiniuConfig() QiniuConfig {
	return globalConfig.Qiniu
}
