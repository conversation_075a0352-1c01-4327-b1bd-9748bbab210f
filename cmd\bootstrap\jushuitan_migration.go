package bootstrap

import (
	"fmt"
	"log"
	jushuitanModel "yekaitai/pkg/adapters/jushuitan"
	"yekaitai/pkg/infra/mysql"
)

// 需要迁移的聚水潭模型
var jushuitanModels = []interface{}{
	&jushuitanModel.ShopInfo{},
	&jushuitanModel.LogisticsCompany{},
	&jushuitanModel.Warehouse{},
	&jushuitanModel.UserInfo{},
	&jushuitanModel.SupplierInfo{},
	&jushuitanModel.DistributorInfo{},
	// 商品相关模型
	&jushuitanModel.ItemSku{},
	&jushuitanModel.MallItem{},
	&jushuitanModel.SkuMap{},
	&jushuitanModel.CombineSku{},
	&jushuitanModel.CombineSkuItem{},
	&jushuitanModel.CategoryQueryItem{},
	&jushuitanModel.BomItem{},
	&jushuitanModel.BomMinorItem{},
	&jushuitanModel.SkuBinDB{},

	// 订单相关表
	&jushuitanModel.OrderInfoDB{},
	&jushuitanModel.OrderItemDB{},
	&jushuitanModel.OrderPayDB{},
	&jushuitanModel.OrderActionDB{},
	// 分销商和用户相关表
	&jushuitanModel.MerchantUserInfo{},
	// 库存相关数据库表
	&jushuitanModel.InventoryDB{},
	&jushuitanModel.PackDB{},
	&jushuitanModel.PackBatchDB{},
	&jushuitanModel.InventoryCountDB{},
	&jushuitanModel.InventoryCountItemDB{},
	&jushuitanModel.InventoryBatchDB{},
	&jushuitanModel.InventorySerialNumberDB{},
	// 售后相关数据库表
	&jushuitanModel.AfterSaleDB{},
	&jushuitanModel.AfterSaleItemDB{},
	&jushuitanModel.AfterSaleReceivedDB{},
	&jushuitanModel.AfterSaleReceivedItemDB{},
	&jushuitanModel.AfterSaleReceivedBatchDB{},
	&jushuitanModel.AfterSaleReceivedSNDB{},
	&jushuitanModel.RefundDB{},
	&jushuitanModel.AfterSaleActionDB{},
	// 物流相关数据库表
	&jushuitanModel.LogisticDB{},
	&jushuitanModel.LogisticItemDB{},
}

// MigrateJushuitanTables 迁移聚水潭表结构
func MigrateJushuitanTables() error {
	fmt.Println("开始迁移聚水潭表结构...")

	// 使用 GORM 的表注释功能
	db := mysql.Master()

	// 移除外键和关联关系的表需要单独迁移，以防止自动创建外键
	// 商品相关表
	db.Set("gorm:table_options", "COMMENT='普通商品资料表'").AutoMigrate(&jushuitanModel.ItemSku{})
	db.Set("gorm:table_options", "COMMENT='款信息表'").AutoMigrate(&jushuitanModel.MallItem{})
	db.Set("gorm:table_options", "COMMENT='商品映射表'").AutoMigrate(&jushuitanModel.SkuMap{})
	db.Set("gorm:table_options", "COMMENT='组合装商品信息表'").AutoMigrate(&jushuitanModel.CombineSku{})
	db.Set("gorm:table_options", "COMMENT='组合装商品子项表'").AutoMigrate(&jushuitanModel.CombineSkuItem{})
	db.Set("gorm:table_options", "COMMENT='类目项表'").AutoMigrate(&jushuitanModel.CategoryQueryItem{})
	db.Set("gorm:table_options", "COMMENT='主料项表'").AutoMigrate(&jushuitanModel.BomItem{})
	db.Set("gorm:table_options", "COMMENT='辅料项表'").AutoMigrate(&jushuitanModel.BomMinorItem{})
	db.Set("gorm:table_options", "COMMENT='商品仓位表'").AutoMigrate(&jushuitanModel.SkuBinDB{})
	// 订单相关表
	db.Set("gorm:table_options", "COMMENT='订单信息表'").AutoMigrate(&jushuitanModel.OrderInfoDB{})
	db.Set("gorm:table_options", "COMMENT='订单商品明细表'").AutoMigrate(&jushuitanModel.OrderItemDB{})
	db.Set("gorm:table_options", "COMMENT='订单支付信息表'").AutoMigrate(&jushuitanModel.OrderPayDB{})
	db.Set("gorm:table_options", "COMMENT='订单操作日志表'").AutoMigrate(&jushuitanModel.OrderActionDB{})

	// 分销商和用户相关数据库表
	db.Set("gorm:table_options", "COMMENT='分销商信息表'").AutoMigrate(&jushuitanModel.DistributorInfo{})
	db.Set("gorm:table_options", "COMMENT='商家用户信息表'").AutoMigrate(&jushuitanModel.MerchantUserInfo{})

	// 库存相关数据库表
	db.Set("gorm:table_options", "COMMENT='商品库存表'").AutoMigrate(&jushuitanModel.InventoryDB{})
	db.Set("gorm:table_options", "COMMENT='包装表'").AutoMigrate(&jushuitanModel.PackDB{})
	db.Set("gorm:table_options", "COMMENT='包装批次表'").AutoMigrate(&jushuitanModel.PackBatchDB{})
	db.Set("gorm:table_options", "COMMENT='盘点单表'").AutoMigrate(&jushuitanModel.InventoryCountDB{})
	db.Set("gorm:table_options", "COMMENT='盘点单商品表'").AutoMigrate(&jushuitanModel.InventoryCountItemDB{})
	db.Set("gorm:table_options", "COMMENT='库存批次表'").AutoMigrate(&jushuitanModel.InventoryBatchDB{})
	db.Set("gorm:table_options", "COMMENT='库存序列号表'").AutoMigrate(&jushuitanModel.InventorySerialNumberDB{})

	// 售后相关数据库表
	db.Set("gorm:table_options", "COMMENT='售后单表'").AutoMigrate(&jushuitanModel.AfterSaleDB{})
	db.Set("gorm:table_options", "COMMENT='售后单商品表'").AutoMigrate(&jushuitanModel.AfterSaleItemDB{})
	db.Set("gorm:table_options", "COMMENT='售后入库单表'").AutoMigrate(&jushuitanModel.AfterSaleReceivedDB{})
	db.Set("gorm:table_options", "COMMENT='售后入库商品表'").AutoMigrate(&jushuitanModel.AfterSaleReceivedItemDB{})
	db.Set("gorm:table_options", "COMMENT='售后入库批次表'").AutoMigrate(&jushuitanModel.AfterSaleReceivedBatchDB{})
	db.Set("gorm:table_options", "COMMENT='售后入库序列号表'").AutoMigrate(&jushuitanModel.AfterSaleReceivedSNDB{})
	db.Set("gorm:table_options", "COMMENT='退款单表'").AutoMigrate(&jushuitanModel.RefundDB{})
	db.Set("gorm:table_options", "COMMENT='售后操作日志表'").AutoMigrate(&jushuitanModel.AfterSaleActionDB{})

	// 物流相关数据库表
	db.Set("gorm:table_options", "COMMENT='物流信息表'").AutoMigrate(&jushuitanModel.LogisticDB{})
	db.Set("gorm:table_options", "COMMENT='物流商品明细表'").AutoMigrate(&jushuitanModel.LogisticItemDB{})

	// 其他模型通过批量迁移
	if err := mysql.AutoMigrate(jushuitanModels...); err != nil {
		log.Printf("迁移聚水潭表结构失败: %v", err)
		return err
	}

	fmt.Println("聚水潭表结构迁移完成")
	return nil
}
