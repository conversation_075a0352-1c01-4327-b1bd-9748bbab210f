package service

import (
	"context"
	"fmt"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type PointsService struct {
	db *gorm.DB
}

func NewPointsService() *PointsService {
	return &PointsService{
		db: mysql.GetDB(),
	}
}

// PointsRuleInfo 积分规则信息
type PointsRuleInfo struct {
	Available    bool    `json:"available"`     // 是否可用积分
	UserPoints   int     `json:"user_points"`   // 用户积分余额
	ExchangeRate float64 `json:"exchange_rate"` // 积分兑换比例（如100积分=1元）
	MaxUse       int     `json:"max_use"`       // 最大可用积分
	MaxAmount    float64 `json:"max_amount"`    // 最大抵扣金额
	MinPoints    int     `json:"min_points"`    // 最少使用积分
	Description  string  `json:"description"`   // 规则说明
}

// PointsDeductionResult 积分抵扣结果
type PointsDeductionResult struct {
	CanUse       bool    `json:"can_use"`       // 是否可以使用
	UsePoints    int     `json:"use_points"`    // 实际使用积分
	DeductAmount float64 `json:"deduct_amount"` // 抵扣金额
	FinalAmount  float64 `json:"final_amount"`  // 最终金额
	Message      string  `json:"message"`       // 提示信息
}

// GetPointsRule 获取积分使用规则
func (s *PointsService) GetPointsRule(ctx context.Context, userID uint, totalAmount float64) (*PointsRuleInfo, error) {
	// 获取用户积分余额
	userPoints, err := s.GetUserPoints(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 获取积分规则配置 - 这里先使用默认配置
	// TODO: 从系统配置表读取实际配置
	exchangeRate := float64(100) // 100积分=1元
	minPoints := 100             // 最少使用100积分
	maxRatio := 0.5              // 最多抵扣50%

	// 计算最大可用积分和金额
	maxAmount := totalAmount * maxRatio
	maxUseByAmount := int(maxAmount * exchangeRate)
	maxUse := userPoints
	if maxUseByAmount < maxUse {
		maxUse = maxUseByAmount
	}

	// 检查是否可用
	available := userPoints >= minPoints && totalAmount > 0

	rule := &PointsRuleInfo{
		Available:    available,
		UserPoints:   userPoints,
		ExchangeRate: exchangeRate,
		MaxUse:       maxUse,
		MaxAmount:    maxAmount,
		MinPoints:    minPoints,
		Description:  fmt.Sprintf("每%.0f积分可抵扣1元，最多可抵扣订单金额的%.0f%%", exchangeRate, maxRatio*100),
	}

	return rule, nil
}

// CalculateDeduction 计算积分抵扣
func (s *PointsService) CalculateDeduction(ctx context.Context, userID uint, usePoints int, totalAmount float64) (*PointsDeductionResult, error) {
	// 获取积分规则
	rule, err := s.GetPointsRule(ctx, userID, totalAmount)
	if err != nil {
		return nil, err
	}

	result := &PointsDeductionResult{
		CanUse:       false,
		UsePoints:    0,
		DeductAmount: 0,
		FinalAmount:  totalAmount,
		Message:      "",
	}

	// 检查是否可用积分
	if !rule.Available {
		result.Message = fmt.Sprintf("积分余额不足，至少需要%d积分", rule.MinPoints)
		return result, nil
	}

	// 检查使用积分是否超过余额
	if usePoints > rule.UserPoints {
		result.Message = "积分余额不足"
		return result, nil
	}

	// 检查使用积分是否超过最大限制
	if usePoints > rule.MaxUse {
		result.Message = fmt.Sprintf("最多只能使用%d积分", rule.MaxUse)
		return result, nil
	}

	// 检查是否低于最少使用积分
	if usePoints < rule.MinPoints {
		result.Message = fmt.Sprintf("至少需要使用%d积分", rule.MinPoints)
		return result, nil
	}

	// 计算抵扣金额
	deductAmount := float64(usePoints) / rule.ExchangeRate
	finalAmount := totalAmount - deductAmount

	result.CanUse = true
	result.UsePoints = usePoints
	result.DeductAmount = deductAmount
	result.FinalAmount = finalAmount
	result.Message = fmt.Sprintf("使用%d积分，抵扣%.2f元", usePoints, deductAmount)

	return result, nil
}

// GetUserPoints 获取用户积分余额
func (s *PointsService) GetUserPoints(ctx context.Context, userID uint) (int, error) {
	// TODO: 从用户积分表查询实际积分
	// 暂时返回默认积分，待实现积分表后完善
	var points int

	// 模拟查询用户积分 - 实际应该从 user_points 表查询
	err := s.db.WithContext(ctx).Raw(`
		SELECT COALESCE(points, 0) as points 
		FROM (SELECT 1000 as points) as default_points
	`).Scan(&points).Error

	if err != nil {
		return 0, fmt.Errorf("查询用户积分失败: %w", err)
	}

	return points, nil
}
