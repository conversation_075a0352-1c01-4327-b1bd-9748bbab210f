package service

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
)

// TencentGeocodingResponse 腾讯地图地理编码响应
type TencentGeocodingResponse struct {
	Status    int    `json:"status"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Result    struct {
		Location struct {
			Lat float64 `json:"lat"`
			Lng float64 `json:"lng"`
		} `json:"location"`
		AddressComponents struct {
			Province     string `json:"province"`
			City         string `json:"city"`
			District     string `json:"district"`
			Street       string `json:"street"`
			StreetNumber string `json:"street_number"`
		} `json:"address_components"`
		AdInfo struct {
			Adcode string `json:"adcode"`
		} `json:"ad_info"`
		Reliability int `json:"reliability"`
		Level       int `json:"level"`
	} `json:"result"`
}

// TencentDistanceMatrixResponse 腾讯地图距离矩阵API响应
type TencentDistanceMatrixResponse struct {
	Status    int    `json:"status"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Result    struct {
		Rows []struct {
			Elements []struct {
				Distance int `json:"distance"` // 距离，单位：米
				Duration int `json:"duration"` // 耗时，单位：秒
				Status   int `json:"status,omitempty"`
			} `json:"elements"`
		} `json:"rows"`
	} `json:"result"`
}

// DistanceResult 距离计算结果
type DistanceResult struct {
	StoreID  uint // 门店ID
	Distance int  // 距离，单位：米
	Duration int  // 耗时，单位：秒
}

// LocationService 位置服务接口
type LocationService interface {
	// Geocode 地理编码
	Geocode(address string) (latitude, longitude float64, err error)
	// BatchCalculateDistance 批量计算距离
	BatchCalculateDistance(userLat, userLng float64, destinations []struct {
		ID        uint
		Latitude  float64
		Longitude float64
	}) ([]DistanceResult, error)
}

type locationService struct {
	apiKey string
}

// NewLocationService 创建位置服务实例
func NewLocationService(apiKey string) LocationService {
	return &locationService{
		apiKey: apiKey,
	}
}

// Geocode 将地址转换为经纬度
func (s *locationService) Geocode(address string) (latitude, longitude float64, err error) {
	// 腾讯地图地理编码API地址
	baseURL := "https://apis.map.qq.com/ws/geocoder/v1/"

	// 构建请求参数
	params := url.Values{}
	params.Set("key", s.apiKey)
	params.Set("address", address)
	params.Set("output", "json")

	// 发送请求
	resp, err := http.Get(baseURL + "?" + params.Encode())
	if err != nil {
		return 0, 0, fmt.Errorf("请求腾讯地图API失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var result TencentGeocodingResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return 0, 0, fmt.Errorf("解析API响应失败: %v", err)
	}

	// 检查响应状态
	if result.Status != 0 {
		return 0, 0, fmt.Errorf("地理编码失败: %s", result.Message)
	}

	// 获取经纬度
	latitude = result.Result.Location.Lat
	longitude = result.Result.Location.Lng

	// 验证经纬度有效性
	if latitude == 0 && longitude == 0 {
		return 0, 0, fmt.Errorf("获取到的经纬度无效")
	}

	return latitude, longitude, nil
}

// BatchCalculateDistance 批量计算距离
func (s *locationService) BatchCalculateDistance(userLat, userLng float64, destinations []struct {
	ID        uint
	Latitude  float64
	Longitude float64
}) ([]DistanceResult, error) {
	// 检查API密钥是否为空
	if s.apiKey == "" {
		return nil, fmt.Errorf("腾讯地图API密钥为空")
	}

	fmt.Printf("用户位置: 纬度=%f, 经度=%f\n", userLat, userLng)
	fmt.Printf("计算距离的目标点数量: %d\n", len(destinations))
	fmt.Printf("使用腾讯地图API密钥: [%s]\n", s.apiKey)

	// 打印所有目标位置
	for i, dest := range destinations {
		fmt.Printf("目标门店[%d]: ID=%d, 纬度=%f, 经度=%f\n", i+1, dest.ID, dest.Latitude, dest.Longitude)
	}

	// 检查目的地列表是否为空
	if len(destinations) == 0 {
		return []DistanceResult{}, nil
	}

	// 腾讯地图距离矩阵API地址
	baseURL := "https://apis.map.qq.com/ws/distance/v1/matrix"

	// 构建目的地坐标字符串
	var destinationCoords []string
	for _, dest := range destinations {
		if dest.Latitude != 0 && dest.Longitude != 0 {
			destinationCoords = append(destinationCoords, fmt.Sprintf("%f,%f", dest.Latitude, dest.Longitude))
		}
	}

	// 如果没有有效的目的地坐标，返回空结果
	if len(destinationCoords) == 0 {
		return []DistanceResult{}, nil
	}

	// 构建请求参数
	params := url.Values{}
	params.Set("key", s.apiKey)
	params.Set("mode", "driving") // 驾车模式
	params.Set("from", fmt.Sprintf("%f,%f", userLat, userLng))
	params.Set("to", strings.Join(destinationCoords, ";"))
	params.Set("output", "json")

	// 发送请求
	resp, err := http.Get(baseURL + "?" + params.Encode())
	if err != nil {
		return nil, fmt.Errorf("请求腾讯地图距离矩阵API失败: %v", err)
	}
	defer resp.Body.Close()

	// 记录请求URL
	fmt.Println("腾讯地图API请求URL:", baseURL+"?"+params.Encode())

	// 解析响应
	var result TencentDistanceMatrixResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %v", err)
	}

	// 记录API响应
	respBytes, _ := json.MarshalIndent(result, "", "  ")
	fmt.Println("腾讯地图API响应:", string(respBytes))

	// 打印具体的距离结果
	if result.Status == 0 && len(result.Result.Rows) > 0 && len(result.Result.Rows[0].Elements) > 0 {
		fmt.Println("腾讯地图距离计算结果详情:")
		for i, element := range result.Result.Rows[0].Elements {
			// 确保有与之对应的目的地
			if i < len(destinations) {
				fmt.Printf("  目标[%d]: ID=%d, 纬度=%f, 经度=%f, 距离=%d米, 用时=%d秒\n",
					i+1, destinations[i].ID, destinations[i].Latitude, destinations[i].Longitude,
					element.Distance, element.Duration)
			}
		}
	}

	// 检查响应状态
	if result.Status != 0 {
		return nil, fmt.Errorf("距离计算失败: %s", result.Message)
	}

	// 处理响应结果
	var distanceResults []DistanceResult

	// 确保返回了结果行
	if len(result.Result.Rows) > 0 {
		elements := result.Result.Rows[0].Elements

		// 将API结果映射到数据结构
		destIndex := 0
		for i, element := range elements {
			// 如果有目的地被跳过(因为无效坐标)，需要调整索引
			for destIndex < len(destinations) && (destinations[destIndex].Latitude == 0 || destinations[destIndex].Longitude == 0) {
				destIndex++
			}

			// 确保索引在范围内
			if destIndex >= len(destinations) {
				break
			}

			// 如果元素索引超出范围，终止处理
			if i >= len(elements) {
				break
			}

			distanceResults = append(distanceResults, DistanceResult{
				StoreID:  destinations[destIndex].ID,
				Distance: element.Distance,
				Duration: element.Duration,
			})

			// 记录映射的距离结果
			fmt.Printf("门店ID: %d, 纬度: %f, 经度: %f, 距离: %d米, 时间: %d秒\n",
				destinations[destIndex].ID,
				destinations[destIndex].Latitude,
				destinations[destIndex].Longitude,
				element.Distance,
				element.Duration)

			destIndex++
		}
	}

	fmt.Printf("共计算 %d 个门店的距离\n", len(distanceResults))

	return distanceResults, nil
}
