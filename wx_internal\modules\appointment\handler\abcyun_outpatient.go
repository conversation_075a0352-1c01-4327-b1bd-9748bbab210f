package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/modules/appointment/types"
	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// OutpatientQueryByPatientRequest 按患者查询门诊单请求
type OutpatientQueryByPatientRequest struct {
	PatientID string `path:"patientId"`                 // 患者ID
	BeginDate string `form:"beginDate,optional"`        // 开始日期，可选，默认为3个月前
	EndDate   string `form:"endDate,optional"`          // 结束日期，可选，默认为当前日期
	Limit     int    `form:"limit,optional,default=20"` // 每页显示条数，可选，默认20
	Offset    int    `form:"offset,optional,default=0"` // 分页起始下标，可选，默认0
}

// OutpatientDetailRequest 获取门诊单详情请求
type OutpatientDetailRequest struct {
	ID string `path:"id"` // 门诊单ID
}

// OutpatientQueryByDateRequest 按天查询门诊单请求
type OutpatientQueryByDateRequest struct {
	Date   string `form:"date"`                      // 日期（格式yyyy-MM-dd）
	Limit  int    `form:"limit,optional,default=20"` // 每页显示条数，可选，默认20，最大100
	Offset int    `form:"offset,optional,default=0"` // 分页起始下标，可选，默认0
}

// AbcYunOutpatientQueryByPatientHandler 按患者查询门诊单
func AbcYunOutpatientQueryByPatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req OutpatientQueryByPatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PatientID == "" {
		httpx.Error(w, errors.New("患者ID不能为空"))
		return
	}

	// 设置默认值
	if req.BeginDate == "" {
		// 默认3个月前
		req.BeginDate = time.Now().AddDate(0, -3, 0).Format("2006-01-02")
	}
	if req.EndDate == "" {
		// 默认当前日期
		req.EndDate = time.Now().Format("2006-01-02")
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/outpatient/query-by-patient/%s", req.PatientID)

	queryParams := map[string]string{
		"beginDate": req.BeginDate,
		"endDate":   req.EndDate,
		"limit":     strconv.Itoa(req.Limit),
		"offset":    strconv.Itoa(req.Offset),
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.OutpatientSummary `json:"rows"`
			Total  int                       `json:"total"`
			Offset int                       `json:"offset"`
			Limit  int                       `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "按患者查询门诊单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunOutpatientDetailHandler 获取门诊单详情
func AbcYunOutpatientDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req OutpatientDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("门诊单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/outpatient/%s", req.ID)

	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    types.OutpatientDetail `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取门诊单详情成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunCreateOutpatientHandler 创建门诊单
func AbcYunCreateOutpatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.OutpatientCreateReq
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.DepartmentID == "" {
		httpx.Error(w, errors.New("科室ID不能为空"))
		return
	}
	if req.DoctorID == "" {
		httpx.Error(w, errors.New("医生ID不能为空"))
		return
	}
	if req.PatientID == "" {
		httpx.Error(w, errors.New("患者ID不能为空"))
		return
	}
	if req.OperatorID == "" {
		httpx.Error(w, errors.New("操作人ID不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/outpatient"

	respBody, err := client.Post(path, req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    types.OutpatientDetail `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "创建门诊单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunOutpatientQueryByDateHandler 按天查询门诊单
func AbcYunOutpatientQueryByDateHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req OutpatientQueryByDateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}

	// 设置最大条数限制
	if req.Limit > 100 {
		req.Limit = 100
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/outpatient/query-by-date"

	queryParams := map[string]string{
		"date":   req.Date,
		"limit":  strconv.Itoa(req.Limit),
		"offset": strconv.Itoa(req.Offset),
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.OutpatientSummary `json:"rows"`
			Total  int                       `json:"total"`
			Offset int                       `json:"offset"`
			Limit  int                       `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "按天查询门诊单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}
