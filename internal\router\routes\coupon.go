package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	couponHandler "yekaitai/internal/modules/coupon/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterCouponRoutes 注册优惠券管理相关路由
func RegisterCouponRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 创建优惠券处理器实例
	couponHandlerInst := couponHandler.NewCouponGoZeroHandler()

	// 优惠券管理路由
	server.AddRoutes(
		[]rest.Route{
			// 获取优惠券列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/coupons",
				Handler: adminAuthWrapper(couponHandlerInst.GetCouponList),
			},
			// 创建优惠券
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/coupons",
				Handler: adminAuthWrapper(couponHandlerInst.CreateCoupon),
			},
			// 获取优惠券详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/coupons/:id",
				Handler: adminAuthWrapper(couponHandlerInst.GetCouponDetail),
			},
			// 更新优惠券
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/coupons/:id",
				Handler: adminAuthWrapper(couponHandlerInst.UpdateCoupon),
			},
			// 删除优惠券
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/coupons/:id",
				Handler: adminAuthWrapper(couponHandlerInst.DeleteCoupon),
			},
			// 更新优惠券状态
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/coupons/:id/status",
				Handler: adminAuthWrapper(couponHandlerInst.UpdateCouponStatus),
			},
			// 批量发放优惠券（同步）
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/coupons/:id/issue",
				Handler: adminAuthWrapper(couponHandlerInst.IssueCoupons),
			},
			// 创建发放任务（异步）
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/coupons/:id/issue-task",
				Handler: adminAuthWrapper(couponHandlerInst.CreateIssueTask),
			},
			// 获取发放任务列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/coupon-issue-tasks",
				Handler: adminAuthWrapper(couponHandlerInst.GetIssueTaskList),
			},
			// 获取发放任务详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/coupon-issue-tasks/:id",
				Handler: adminAuthWrapper(couponHandlerInst.GetIssueTaskDetail),
			},
			// 手动执行发放任务
			// {
			// 	Method:  http.MethodPost,
			// 	Path:    "/api/admin/coupon-issue-tasks/:id/execute",
			// 	Handler: adminAuthWrapper(couponHandlerInst.ExecuteIssueTask),
			// },
			// 获取商品列表（用于选择）
			// {
			// 	Method:  http.MethodGet,
			// 	Path:    "/api/admin/coupons/products",
			// 	Handler: adminAuthWrapper(couponHandlerInst.GetProductList),
			// },
			// // 获取服务列表（用于选择）
			// {
			// 	Method:  http.MethodGet,
			// 	Path:    "/api/admin/coupons/services",
			// 	Handler: adminAuthWrapper(couponHandlerInst.GetServiceList),
			// },
		},
	)
}
