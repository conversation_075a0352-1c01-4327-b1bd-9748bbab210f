package handler

import (
	"net/http"
	"strconv"

	"yekaitai/internal/modules/wx_redeemer/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type RedeemerHandler struct {
	redeemerService *service.RedeemerService
}

func NewRedeemerHandler() *RedeemerHandler {
	return &RedeemerHandler{
		redeemerService: service.NewRedeemerService(),
	}
}

// RedeemerListRequest 核销员列表请求
type RedeemerListRequest struct {
	Page      int    `form:"page,default=1"`      // 页码
	Size      int    `form:"size,default=10"`     // 每页数量
	Mobile    string `form:"mobile,optional"`     // 手机号筛选
	Name      string `form:"name,optional"`       // 名称筛选
	StoreName string `form:"store_name,optional"` // 门店名称筛选
}

// RedeemerCreateRequest 创建核销员请求
type RedeemerCreateRequest struct {
	Mobile  string `json:"mobile" validate:"required"`   // 手机号
	Name    string `json:"name" validate:"required"`     // 核销员名称
	StoreID uint   `json:"store_id" validate:"required"` // 门店ID
	Avatar  string `json:"avatar"`                       // 头像
}

// RedeemerUpdateRequest 更新核销员请求
type RedeemerUpdateRequest struct {
	Name    string `json:"name" validate:"required"`     // 核销员名称
	StoreID uint   `json:"store_id" validate:"required"` // 门店ID
	Avatar  string `json:"avatar"`                       // 头像
}

// RedeemerStatusRequest 更新核销员状态请求
type RedeemerStatusRequest struct {
	Status int `json:"status" validate:"required,oneof=0 1"` // 状态(1-正常，0-禁用)
}

// GetRedeemerList 获取核销员列表
func (h *RedeemerHandler) GetRedeemerList(w http.ResponseWriter, r *http.Request) {
	var req RedeemerListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}
	if req.Size > 100 {
		req.Size = 100
	}

	// 转换为service请求
	serviceReq := &service.RedeemerListRequest{
		Page:      req.Page,
		Size:      req.Size,
		Mobile:    req.Mobile,
		Name:      req.Name,
		StoreName: req.StoreName,
	}

	redeemers, total, err := h.redeemerService.GetRedeemerList(r.Context(), serviceReq)
	if err != nil {
		logx.Errorf("获取核销员列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取核销员列表失败"))
		return
	}

	result := map[string]interface{}{
		"list":  redeemers,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// CreateRedeemer 创建核销员
func (h *RedeemerHandler) CreateRedeemer(w http.ResponseWriter, r *http.Request) {
	var req RedeemerCreateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 验证必填字段
	if req.Mobile == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "手机号不能为空"))
		return
	}
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "核销员名称不能为空"))
		return
	}
	if req.StoreID == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "门店ID不能为空"))
		return
	}

	// 转换为service请求
	serviceReq := &service.RedeemerCreateRequest{
		Mobile:  req.Mobile,
		Name:    req.Name,
		StoreID: req.StoreID,
		Avatar:  req.Avatar,
	}

	err := h.redeemerService.CreateRedeemer(r.Context(), serviceReq)
	if err != nil {
		logx.Errorf("创建核销员失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// UpdateRedeemer 更新核销员
func (h *RedeemerHandler) UpdateRedeemer(w http.ResponseWriter, r *http.Request) {
	// 从路径参数获取核销员ID
	redeemerIDStr := r.URL.Query().Get("id")
	if redeemerIDStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "核销员ID不能为空"))
		return
	}

	redeemerID, err := strconv.ParseUint(redeemerIDStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "核销员ID格式错误"))
		return
	}

	var req RedeemerUpdateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 验证必填字段
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "核销员名称不能为空"))
		return
	}
	if req.StoreID == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "门店ID不能为空"))
		return
	}

	// 转换为service请求
	serviceReq := &service.RedeemerUpdateRequest{
		RedeemerID: uint(redeemerID),
		Name:       req.Name,
		StoreID:    req.StoreID,
		Avatar:     req.Avatar,
	}

	err = h.redeemerService.UpdateRedeemer(r.Context(), serviceReq)
	if err != nil {
		logx.Errorf("更新核销员失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// UpdateRedeemerStatus 更新核销员状态
func (h *RedeemerHandler) UpdateRedeemerStatus(w http.ResponseWriter, r *http.Request) {
	// 从路径参数获取核销员ID
	redeemerIDStr := r.URL.Query().Get("id")
	if redeemerIDStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "核销员ID不能为空"))
		return
	}

	redeemerID, err := strconv.ParseUint(redeemerIDStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "核销员ID格式错误"))
		return
	}

	var req RedeemerStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	err = h.redeemerService.UpdateRedeemerStatus(r.Context(), uint(redeemerID), req.Status)
	if err != nil {
		logx.Errorf("更新核销员状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// DeleteRedeemer 删除核销员
func (h *RedeemerHandler) DeleteRedeemer(w http.ResponseWriter, r *http.Request) {
	// 从路径参数获取核销员ID
	redeemerIDStr := r.URL.Query().Get("id")
	if redeemerIDStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "核销员ID不能为空"))
		return
	}

	redeemerID, err := strconv.ParseUint(redeemerIDStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "核销员ID格式错误"))
		return
	}

	err = h.redeemerService.DeleteRedeemer(r.Context(), uint(redeemerID))
	if err != nil {
		logx.Errorf("删除核销员失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}
