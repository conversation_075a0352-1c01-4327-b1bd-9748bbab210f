package middleware

import (
	"context"
	"net/http"
	"strings"
	"yekaitai/internal/types"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/svc"
	wxtypes "yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// WxAuthMiddleware 小程序端用户认证中间件
func WxAuthMiddleware(serverCtx *svc.WxServiceContext) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 从请求头获取Token
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权，请先登录"))
				return
			}

			// 验证Token格式
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
				return
			}

			// 检查Token类型
			tokenType := strings.ToLower(parts[0])
			tokenString := parts[1]

			// 检查令牌类型，只接受bearer
			if tokenType != "bearer" {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证类型错误，请使用Bearer认证"))
				return
			}

			// 使用专用的小程序Token解析函数
			claims, err := ParseWxToken(serverCtx, tokenString)
			if err != nil {
				logx.Errorf("解析小程序Token失败: %v", err)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证失败: "+err.Error()))
				return
			}

			// 从Token获取OpenID
			openid := claims.OpenID

			// 通过OpenID查询用户信息（只查询一次）
			db := mysql.GetDB()
			var wxUser user.WxUser
			if err := db.Where("open_id = ?", openid).First(&wxUser).Error; err != nil {
				if err == gorm.ErrRecordNotFound {
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户不存在"))
				} else {
					logx.Errorf("查询用户失败: %v", err)
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询用户失败"))
				}
				return
			}

			// 检查用户状态
			if wxUser.Status != 1 {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(wxtypes.CodeUserForbidden, "用户已被禁用"))
				return
			}

			// 将用户信息添加到请求上下文
			r = r.WithContext(NewContext(r.Context(), "wx_openid", openid))
			r = r.WithContext(NewContext(r.Context(), OpenIDKey, openid))
			// 重要：直接将userID添加到context，使用标准的context.WithValue
			r = r.WithContext(context.WithValue(r.Context(), types.UserIDKey, wxUser.UserID))

			next.ServeHTTP(w, r)
		})
	}
}
