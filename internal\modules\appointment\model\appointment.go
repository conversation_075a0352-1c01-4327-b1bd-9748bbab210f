package model

import (
	"time"
	"yekaitai/pkg/infra/mysql"
)

// 预约状态
const (
	AppointmentStatusPending   = 1 // 待支付
	AppointmentStatusPaid      = 2 // 已支付
	AppointmentStatusCompleted = 3 // 已完成
	AppointmentStatusCancelled = 4 // 已取消
	AppointmentStatusRefunded  = 5 // 已退款
)

// 就诊类型
const (
	VisitTypeOffline = 1 // 线下就诊
	VisitTypeOnline  = 2 // 线上问诊
)

// Appointment 预约挂号
type Appointment struct {
	ID             uint       `gorm:"primaryKey;column:id" json:"id"`
	AppointmentNo  string     `gorm:"column:appointment_no;type:varchar(64);not null;uniqueIndex" json:"appointment_no"` // 预约编号
	UserID         uint       `gorm:"column:user_id;not null;index" json:"user_id"`                                      // 用户ID
	PatientID      uint       `gorm:"column:patient_id;not null;index" json:"patient_id"`                                // 就诊人ID
	DoctorID       uint       `gorm:"column:doctor_id;not null;index" json:"doctor_id"`                                  // 医生ID
	DepartmentID   uint       `gorm:"column:department_id;not null;index" json:"department_id"`                          // 科室ID
	ClinicID       string     `gorm:"column:clinic_id;type:varchar(64);index" json:"clinic_id"`                          // 诊所ID
	ScheduleTimeID uint       `gorm:"column:schedule_time_id;not null" json:"schedule_time_id"`                          // 排班时间ID
	AppointTime    time.Time  `gorm:"column:appoint_time;not null" json:"appoint_time"`                                  // 预约时间
	VisitType      int        `gorm:"column:visit_type;not null;default:1" json:"visit_type"`                            // 就诊类型：1-线下就诊，2-线上问诊
	RegisterFee    float64    `gorm:"column:register_fee;not null;type:decimal(10,2)" json:"register_fee"`               // 挂号费
	Status         int        `gorm:"column:status;not null;default:1" json:"status"`                                    // 预约状态
	OrderNo        string     `gorm:"column:order_no;type:varchar(64);index" json:"order_no"`                            // 关联的订单编号
	Symptoms       string     `gorm:"column:symptoms;type:text" json:"symptoms"`                                         // 症状描述
	MedicalHistory string     `gorm:"column:medical_history;type:text" json:"medical_history"`                           // 病史
	CancelReason   string     `gorm:"column:cancel_reason;type:varchar(255)" json:"cancel_reason"`                       // 取消原因
	CreatedAt      time.Time  `gorm:"column:created_at;not null" json:"created_at"`                                      // 创建时间
	UpdatedAt      time.Time  `gorm:"column:updated_at;not null" json:"updated_at"`                                      // 更新时间
	DeletedAt      *time.Time `gorm:"column:deleted_at;index" json:"deleted_at"`                                         // 删除时间
	ExternalID     string     `gorm:"column:external_id;type:varchar(50);index" json:"external_id"`                      // 外部预约ID
	ExternalKey    string     `gorm:"column:external_key;type:varchar(20);index" json:"external_key"`                    // 外部系统标识
}

// TableName 表名
func (Appointment) TableName() string {
	return "appointments"
}

// ScheduleTime 排班时间
type ScheduleTime struct {
	ID           uint       `gorm:"primaryKey;column:id" json:"id"`
	DoctorID     uint       `gorm:"column:doctor_id;not null;index" json:"doctor_id"`                    // 医生ID
	DepartmentID uint       `gorm:"column:department_id;not null;index" json:"department_id"`            // 科室ID
	ClinicID     string     `gorm:"column:clinic_id;type:varchar(64);not null;index" json:"clinic_id"`   // 诊所ID
	Date         time.Time  `gorm:"column:date;not null;index" json:"date"`                              // 日期
	StartTime    time.Time  `gorm:"column:start_time;not null" json:"start_time"`                        // 开始时间
	EndTime      time.Time  `gorm:"column:end_time;not null" json:"end_time"`                            // 结束时间
	Quota        int        `gorm:"column:quota;not null;default:1" json:"quota"`                        // 可预约人数
	Reserved     int        `gorm:"column:reserved;not null;default:0" json:"reserved"`                  // 已预约人数
	RegisterFee  float64    `gorm:"column:register_fee;not null;type:decimal(10,2)" json:"register_fee"` // 挂号费
	Status       int        `gorm:"column:status;not null;default:1" json:"status"`                      // 状态：1-正常，2-已满，3-停诊
	CreatedAt    time.Time  `gorm:"column:created_at;not null" json:"created_at"`                        // 创建时间
	UpdatedAt    time.Time  `gorm:"column:updated_at;not null" json:"updated_at"`                        // 更新时间
	DeletedAt    *time.Time `gorm:"column:deleted_at;index" json:"deleted_at"`                           // 删除时间
	ExternalID   string     `gorm:"column:external_id;type:varchar(50);index" json:"external_id"`        // 外部排班ID
	ExternalKey  string     `gorm:"column:external_key;type:varchar(20);index" json:"external_key"`      // 外部系统标识
}

// TableName 表名
func (ScheduleTime) TableName() string {
	return "schedule_times"
}

// AppointmentRepository 预约仓库接口
type AppointmentRepository interface {
	// 创建预约
	CreateAppointment(appointment *Appointment) error
	// 查询预约
	FindAppointmentByNo(appointmentNo string) (*Appointment, error)
	// 更新预约状态
	UpdateAppointmentStatus(appointmentNo string, status int) error
	// 查询用户预约列表
	FindUserAppointments(userID uint, status int, page, pageSize int) ([]*Appointment, int64, error)
	// 查询医生预约列表
	FindDoctorAppointments(doctorID uint, date time.Time) ([]*Appointment, error)
	// 取消预约
	CancelAppointment(appointmentNo string, reason string) error
	// 根据外部ID查询预约
	FindByExternalID(externalID string) (*Appointment, error)
}

// appointmentRepository 预约仓库实现
type appointmentRepository struct{}

// NewAppointmentRepository 创建预约仓库
func NewAppointmentRepository() AppointmentRepository {
	return &appointmentRepository{}
}

// CreateAppointment 创建预约
func (r *appointmentRepository) CreateAppointment(appointment *Appointment) error {
	return mysql.Master().Create(appointment).Error
}

// FindAppointmentByNo 查询预约
func (r *appointmentRepository) FindAppointmentByNo(appointmentNo string) (*Appointment, error) {
	var appointment Appointment
	err := mysql.Slave().Where("appointment_no = ?", appointmentNo).First(&appointment).Error
	if err != nil {
		return nil, err
	}
	return &appointment, nil
}

// UpdateAppointmentStatus 更新预约状态
func (r *appointmentRepository) UpdateAppointmentStatus(appointmentNo string, status int) error {
	return mysql.Master().Model(&Appointment{}).Where("appointment_no = ?", appointmentNo).Update("status", status).Error
}

// FindUserAppointments 查询用户预约列表
func (r *appointmentRepository) FindUserAppointments(userID uint, status int, page, pageSize int) ([]*Appointment, int64, error) {
	var appointments []*Appointment
	var total int64

	query := mysql.Slave().Model(&Appointment{}).Where("user_id = ?", userID)

	if status > 0 {
		query = query.Where("status = ?", status)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&appointments).Error
	if err != nil {
		return nil, 0, err
	}

	return appointments, total, nil
}

// FindDoctorAppointments 查询医生预约列表
func (r *appointmentRepository) FindDoctorAppointments(doctorID uint, date time.Time) ([]*Appointment, error) {
	var appointments []*Appointment

	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	err := mysql.Slave().Where("doctor_id = ? AND appoint_time >= ? AND appoint_time < ?",
		doctorID, startOfDay, endOfDay).Find(&appointments).Error

	return appointments, err
}

// CancelAppointment 取消预约
func (r *appointmentRepository) CancelAppointment(appointmentNo string, reason string) error {
	return mysql.Master().Model(&Appointment{}).Where("appointment_no = ?", appointmentNo).
		Updates(map[string]interface{}{
			"status":        AppointmentStatusCancelled,
			"cancel_reason": reason,
			"updated_at":    time.Now(),
		}).Error
}

// FindByExternalID 根据外部ID查询预约
func (r *appointmentRepository) FindByExternalID(externalID string) (*Appointment, error) {
	var appointment Appointment
	err := mysql.Slave().Where("external_id = ?", externalID).First(&appointment).Error
	if err != nil {
		return nil, err
	}
	return &appointment, nil
}

// ScheduleTimeRepository 排班时间仓库接口
type ScheduleTimeRepository interface {
	// 创建排班时间
	CreateScheduleTime(scheduleTime *ScheduleTime) error
	// 查询排班时间
	FindScheduleTimeByID(id uint) (*ScheduleTime, error)
	// 查询医生排班时间
	FindDoctorScheduleTimes(doctorID uint, date time.Time) ([]*ScheduleTime, error)
	// 查询可用排班时间
	FindAvailableScheduleTimes(doctorID uint, date time.Time) ([]*ScheduleTime, error)
	// 预约排班时间
	ReserveScheduleTime(id uint) error
	// 取消预约排班时间
	CancelReserveScheduleTime(id uint) error
	// 批量创建排班时间
	BatchCreateScheduleTimes(scheduleTimes []*ScheduleTime) error
	// 根据外部ID查询排班时间
	FindByExternalID(externalID string) (*ScheduleTime, error)
	// 更新排班时间
	Update(scheduleTime *ScheduleTime) error
}

// scheduleTimeRepository 排班时间仓库实现
type scheduleTimeRepository struct{}

// NewScheduleTimeRepository 创建排班时间仓库
func NewScheduleTimeRepository() ScheduleTimeRepository {
	return &scheduleTimeRepository{}
}

// CreateScheduleTime 创建排班时间
func (r *scheduleTimeRepository) CreateScheduleTime(scheduleTime *ScheduleTime) error {
	return mysql.Master().Create(scheduleTime).Error
}

// FindScheduleTimeByID 查询排班时间
func (r *scheduleTimeRepository) FindScheduleTimeByID(id uint) (*ScheduleTime, error) {
	var scheduleTime ScheduleTime
	err := mysql.Slave().Where("id = ?", id).First(&scheduleTime).Error
	if err != nil {
		return nil, err
	}
	return &scheduleTime, nil
}

// FindDoctorScheduleTimes 查询医生排班时间
func (r *scheduleTimeRepository) FindDoctorScheduleTimes(doctorID uint, date time.Time) ([]*ScheduleTime, error) {
	var scheduleTimes []*ScheduleTime

	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	err := mysql.Slave().Where("doctor_id = ? AND date >= ? AND date < ?",
		doctorID, startOfDay, endOfDay).Find(&scheduleTimes).Error

	return scheduleTimes, err
}

// FindAvailableScheduleTimes 查询可用排班时间
func (r *scheduleTimeRepository) FindAvailableScheduleTimes(doctorID uint, date time.Time) ([]*ScheduleTime, error) {
	var scheduleTimes []*ScheduleTime

	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	err := mysql.Slave().Where("doctor_id = ? AND date >= ? AND date < ? AND status = 1 AND reserved < quota",
		doctorID, startOfDay, endOfDay).Find(&scheduleTimes).Error

	return scheduleTimes, err
}

// ReserveScheduleTime 预约排班时间
func (r *scheduleTimeRepository) ReserveScheduleTime(id uint) error {
	tx := mysql.Master().Begin()

	var scheduleTime ScheduleTime
	err := tx.Set("gorm:query_option", "FOR UPDATE").Where("id = ?", id).First(&scheduleTime).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	if scheduleTime.Reserved >= scheduleTime.Quota {
		tx.Rollback()
		return mysql.ErrExhausted
	}

	// 计算新的状态
	var newStatus int
	if scheduleTime.Reserved+1 >= scheduleTime.Quota {
		newStatus = 2 // 已满
	} else {
		newStatus = 1 // 正常
	}

	err = tx.Model(&ScheduleTime{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"reserved":   scheduleTime.Reserved + 1,
			"status":     newStatus,
			"updated_at": time.Now(),
		}).Error

	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// CancelReserveScheduleTime 取消预约排班时间
func (r *scheduleTimeRepository) CancelReserveScheduleTime(id uint) error {
	tx := mysql.Master().Begin()

	var scheduleTime ScheduleTime
	err := tx.Set("gorm:query_option", "FOR UPDATE").Where("id = ?", id).First(&scheduleTime).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	if scheduleTime.Reserved <= 0 {
		tx.Rollback()
		return nil
	}

	err = tx.Model(&ScheduleTime{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"reserved":   scheduleTime.Reserved - 1,
			"status":     1, // 如果之前是已满状态，现在取消一个后应该变回正常状态
			"updated_at": time.Now(),
		}).Error

	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// BatchCreateScheduleTimes 批量创建排班时间
func (r *scheduleTimeRepository) BatchCreateScheduleTimes(scheduleTimes []*ScheduleTime) error {
	return mysql.Master().Create(&scheduleTimes).Error
}

// FindByExternalID 根据外部ID查询排班时间
func (r *scheduleTimeRepository) FindByExternalID(externalID string) (*ScheduleTime, error) {
	var scheduleTime ScheduleTime
	err := mysql.Slave().Where("external_id = ?", externalID).First(&scheduleTime).Error
	if err != nil {
		return nil, err
	}
	return &scheduleTime, nil
}

// Update 更新排班时间
func (r *scheduleTimeRepository) Update(scheduleTime *ScheduleTime) error {
	return mysql.Master().Save(scheduleTime).Error
}
