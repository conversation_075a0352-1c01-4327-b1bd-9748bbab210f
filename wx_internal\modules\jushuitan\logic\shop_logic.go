package logic

import (
	"context"
	"yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// ShopLogic 店铺查询逻辑
type ShopLogic struct {
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

// NewShopLogic 创建店铺查询逻辑
func NewShopLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *ShopLogic {
	return &ShopLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// QueryShops 查询店铺列表
func (l *ShopLogic) QueryShops(pageIndex, pageSize int, shopIDs []int) (*jushuitan.BaseResp, error) {
	// 调用聚水潭API
	req := &jushuitan.ShopQueryRequest{
		PageIndex: pageIndex,
		PageSize:  pageSize,
		ShopIDs:   shopIDs,
	}

	return l.svcCtx.JushuitanClient.QueryShops(l.ctx, req)
}
