package routes

import (
	"net/http"

	"yekaitai/wx_internal/modules/qrcode-management/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterQrcodeHandlers 注册二维码和核销相关接口
func RegisterQrcodeHandlers(server RestServer, serverCtx *svc.WxServiceContext) {

	// 生成二维码接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodPost,
			Path:    "/api/wx/registration/appointment/generate-qrcode",
			Handler: handler.GenerateQRCodeHandler,
		},
	)

	// 获取二维码接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/registration/appointment/qrcode/:id",
			Handler: handler.GetQRCodeHandler,
		},
	)

	// 验证核销码接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodPost,
			Path:    "/api/wx/registration/appointment/verify",
			Handler: handler.VerifyCodeHandler,
		},
	)

	// 获取核销记录列表接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/registration/verification/list",
			Handler: handler.GetVerificationRecordsHandler,
		},
	)

	// 获取核销记录详情接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/registration/verification/detail/:id",
			Handler: handler.GetVerificationRecordHandler,
		},
	)
}
