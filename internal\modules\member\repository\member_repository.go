package repository

import (
	"strconv"
	"time"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// MemberRepository 会员仓库接口
type MemberRepository interface {
	// List 获取会员列表，根据注册时间排序
	List(page, size int, query map[string]interface{}) ([]*user.WxUser, int64, error)
	// UpdateStatus 更新会员状态（启用/禁用）
	UpdateStatus(id uint, status int) error
	// FindByID 根据ID查找会员
	FindByID(id uint) (*user.WxUser, error)
	// FindByUnionID 根据UnionID查找会员
	FindByUnionID(unionID string) (*user.WxUser, error)
	// FindPatients 获取会员的就诊人列表
	FindPatients(userID uint) ([]*patient.WxPatient, error)
	// UpdateMemberTags 更新会员标签
	UpdateMemberTags(id uint, tagIDs []uint) error
	// GetMemberTags 获取会员标签
	GetMemberTags(id uint) ([]user.Tag, error)
}

// memberRepository 会员仓库实现
type memberRepository struct {
	db *gorm.DB
}

// NewMemberRepository 创建会员仓库
func NewMemberRepository(db *gorm.DB) MemberRepository {
	if db == nil {
		db = mysql.Master()
	}
	return &memberRepository{db: db}
}

// List 获取会员列表，根据注册时间排序
func (r *memberRepository) List(page, size int, query map[string]interface{}) ([]*user.WxUser, int64, error) {
	var members []*user.WxUser
	var total int64

	db := r.db.Model(&user.WxUser{}).Where("deleted_at IS NULL")

	// 添加查询条件
	if unionID, ok := query["union_id"].(string); ok && unionID != "" {
		db = db.Where("union_id = ?", unionID)
	}

	if mobile, ok := query["mobile"].(string); ok && mobile != "" {
		db = db.Where("mobile LIKE ?", "%"+mobile+"%")
	}

	if nickname, ok := query["nickname"].(string); ok && nickname != "" {
		db = db.Where("nickname LIKE ?", "%"+nickname+"%")
	}

	if status, ok := query["status"].(int); ok && status > 0 {
		db = db.Where("status = ?", status)
	}

	if userLevelID, ok := query["user_level_id"].(uint); ok && userLevelID > 0 {
		db = db.Where("user_level_id = ?", userLevelID)
	}

	if startDate, ok := query["start_date"].(time.Time); ok {
		db = db.Where("register_date >= ?", startDate)
	}

	if endDate, ok := query["end_date"].(time.Time); ok {
		db = db.Where("register_date <= ?", endDate)
	}

	if tagID, ok := query["tag_id"].(string); ok && tagID != "" {
		// 使用关联查询查找指定标签ID的会员
		tagIDUint, err := strconv.ParseUint(tagID, 10, 32)
		if err == nil {
			db = db.Joins("JOIN user_tag ON wx_user.user_id = user_tag.user_id").
				Where("user_tag.tag_id = ?", uint(tagIDUint))
		}
	}

	// 计算总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	db = db.Offset(offset).Limit(size)

	// 默认按注册时间降序排序
	orderBy, ok := query["order_by"].(string)
	if !ok || orderBy == "" {
		orderBy = "register_date DESC"
	}

	// 预加载标签
	err = db.Preload("Tags").Order(orderBy).Find(&members).Error
	if err != nil {
		return nil, 0, err
	}

	return members, total, nil
}

// UpdateStatus 更新会员状态（启用/禁用）
func (r *memberRepository) UpdateStatus(id uint, status int) error {
	return r.db.Model(&user.WxUser{}).Where("user_id = ? AND deleted_at IS NULL", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error
}

// FindByID 根据ID查找会员
func (r *memberRepository) FindByID(id uint) (*user.WxUser, error) {
	var member user.WxUser
	err := r.db.Preload("Tags").Where("user_id = ? AND deleted_at IS NULL", id).First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

// FindByUnionID 根据UnionID查找会员
func (r *memberRepository) FindByUnionID(unionID string) (*user.WxUser, error) {
	var member user.WxUser
	err := r.db.Where("union_id = ? AND deleted_at IS NULL", unionID).First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

// FindPatients 获取会员的就诊人列表
func (r *memberRepository) FindPatients(userID uint) ([]*patient.WxPatient, error) {
	var patients []*patient.WxPatient
	err := r.db.Where("user_id = ? AND deleted_at IS NULL", userID).Find(&patients).Error
	if err != nil {
		return nil, err
	}
	return patients, nil
}

// UpdateMemberTags 更新会员标签
func (r *memberRepository) UpdateMemberTags(id uint, tagIDs []uint) error {
	// 开启事务
	tx := r.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 查找会员
	var member user.WxUser
	if err := tx.Where("user_id = ? AND deleted_at IS NULL", id).First(&member).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 先清除当前的标签关联
	if err := tx.Model(&member).Association("Tags").Clear(); err != nil {
		tx.Rollback()
		return err
	}

	// 如果没有新的标签ID，则直接提交事务
	if len(tagIDs) == 0 {
		return tx.Commit().Error
	}

	// 查找所有标签
	var tags []user.Tag
	if err := tx.Where("id IN ?", tagIDs).Find(&tags).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 添加新的标签关联
	if err := tx.Model(&member).Association("Tags").Append(&tags); err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit().Error
}

// GetMemberTags 获取会员标签
func (r *memberRepository) GetMemberTags(id uint) ([]user.Tag, error) {
	var member user.WxUser
	err := r.db.Preload("Tags").Where("user_id = ? AND deleted_at IS NULL", id).First(&member).Error
	if err != nil {
		return nil, err
	}
	return member.Tags, nil
}
