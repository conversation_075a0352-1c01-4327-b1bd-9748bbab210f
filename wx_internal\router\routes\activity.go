package routes

import (
	"net/http"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/activity/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterActivityRoutes 注册活动相关路由
func RegisterActivityRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用微信认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	activityHandler := handler.NewActivityHandler(serverCtx.ServiceContext)

	// 活动相关路由
	server.AddRoutes(
		[]rest.Route{
			// 获取活动列表
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/activity/list",
				Handler: wxAuthWrapper(activityHandler.GetActivityList),
			},
			// 获取活动详情
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/activity/:activityId",
				Handler: wxAuthWrapper(activityHandler.GetActivityDetail),
			},
			// 获取报名信息
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/activity/:activityId/signup-info",
				Handler: wxAuthWrapper(activityHandler.GetSignUpInfo),
			},
			// 立即报名
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/activity/signup",
				Handler: wxAuthWrapper(activityHandler.SignUp),
			},
			// 取消报名
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/activity/cancel-signup",
				Handler: wxAuthWrapper(activityHandler.CancelSignUp),
			},
			// 获取用户报名列表
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/activity/my-signups",
				Handler: wxAuthWrapper(activityHandler.GetUserSignUpList),
			},
			// 获取已报名活动详情
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/activity/signed/:orderId",
				Handler: wxAuthWrapper(activityHandler.GetSignedActivityDetail),
			},
			// 获取最新活动弹框
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/activity/latest-popup",
				Handler: wxAuthWrapper(activityHandler.GetLatestActivityPopup),
			},
			// 生成二维码
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/activity/generate-qrcode",
				Handler: wxAuthWrapper(activityHandler.GenerateQRCode),
			},
			// 核销活动
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/activity/verify",
				Handler: wxAuthWrapper(activityHandler.VerifyActivity),
			},

			// 获取活动结算页信息
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/activity/checkout",
				Handler: wxAuthWrapper(activityHandler.GetActivityCheckout),
			},
		},
	)
}
