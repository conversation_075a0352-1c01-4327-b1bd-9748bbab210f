package bootstrap

import (
	"fmt"
	"yekaitai/internal/modules/coupon/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// CouponIssueTaskMigration 优惠券批量发放任务迁移
func CouponIssueTaskMigration() error {
	db := mysql.GetDB()

	logx.Info("开始执行优惠券发放任务表结构迁移...")

	// 自动迁移优惠券发放任务表
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券批量发放任务表'").
		AutoMigrate(&model.CouponIssueTask{}); err != nil {
		logx.Errorf("创建优惠券发放任务表失败: %v", err)
		return fmt.Errorf("创建优惠券发放任务表失败: %w", err)
	}

	logx.Info("优惠券发放任务表创建成功")
	return nil
}
