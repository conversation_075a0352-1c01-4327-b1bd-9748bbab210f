package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
	"yekaitai/internal/config"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/user/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type UserService struct {
	logx.Logger
	conn   sqlx.SqlConn
	config config.Config
}

func NewUserService(conn sqlx.SqlConn, c config.Config) *UserService {
	return &UserService{
		Logger: logx.WithContext(context.Background()),
		conn:   conn,
		config: c,
	}
}

// GetWxUserByOpenId 根据OpenID获取微信用户信息
func (s *UserService) GetWxUserByOpenId(ctx context.Context, openId string) (*types.WxUser, error) {
	var user types.WxUser
	err := s.conn.QueryRowCtx(ctx, &user, "SELECT * FROM wx_user WHERE open_id = ?", openId)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetWxUserByID 根据ID获取微信用户信息
func (s *UserService) GetWxUserByID(ctx context.Context, userID uint) (*types.WxUser, error) {
	var user types.WxUser
	err := s.conn.QueryRowCtx(ctx, &user, "SELECT * FROM wx_user WHERE user_id = ?", userID)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// CreateWxUser 创建微信用户
func (s *UserService) CreateWxUser(ctx context.Context, openId, phoneNumber string) error {
	now := time.Now()
	_, err := s.conn.ExecCtx(ctx, "INSERT INTO wx_user (open_id, mobile, register_date, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
		openId, phoneNumber, now, now, now)
	return err
}

// UpdateUserProfile 更新用户头像和昵称
func (s *UserService) UpdateUserProfile(ctx context.Context, openId string, avatar, nickName string) error {
	// 参数验证
	if avatar == "" && nickName == "" {
		return fmt.Errorf("avatar and nickName cannot both be empty")
	}

	// 构建SQL语句
	sql := "UPDATE wx_user SET updated_at = ?"
	args := []interface{}{time.Now()}

	if avatar != "" {
		sql += ", avatar = ?"
		args = append(args, avatar)
	}

	if nickName != "" {
		sql += ", nickname = ?"
		args = append(args, nickName)
	}

	sql += " WHERE open_id = ?"
	args = append(args, openId)

	// 执行更新
	_, err := s.conn.ExecCtx(ctx, sql, args...)
	if err != nil {
		return fmt.Errorf("update profile failed: %v", err)
	}

	return nil
}

// UpdatePhoneNumber 更新用户手机号
func (s *UserService) UpdatePhoneNumber(ctx context.Context, openId, code string) (*types.PhoneInfo, error) {
	if openId == "" {
		logx.Error("更新手机号失败: openId为空")
		return nil, fmt.Errorf("openId不能为空")
	}

	if code == "" {
		logx.Error("更新手机号失败: code为空")
		return nil, fmt.Errorf("code不能为空")
	}

	logx.Infof("开始更新用户手机号, openId: %s, code长度: %d", openId, len(code))

	// 1. 获取access token
	logx.Info("开始获取微信access_token")
	accessToken, err := s.getAccessToken()
	if err != nil {
		logx.Errorf("获取access_token失败: %v", err)
		return nil, fmt.Errorf("获取access_token失败: %v", err)
	}
	logx.Infof("获取access_token成功: %s", accessToken[:10]+"...")

	// 2. 调用微信接口获取手机号
	logx.Info("开始获取用户手机号")
	phoneInfo, err := s.getPhoneNumber(accessToken, code)
	if err != nil {
		logx.Errorf("获取手机号失败: %v", err)
		return nil, fmt.Errorf("获取手机号失败: %v", err)
	}
	logx.Infof("获取手机号成功: %s", phoneInfo.PhoneNumber)

	// 3. 查询用户是否存在（使用gorm确保数据一致性）
	logx.Infof("查询用户是否存在, openId: %s", openId)
	var userCount int64
	err = mysql.Master().Table("wx_user").Where("open_id = ?", openId).Count(&userCount).Error
	if err != nil {
		logx.Errorf("查询用户失败: %v", err)
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}
	exists := userCount > 0
	logx.Infof("用户存在性检查结果: openId=%s, exists=%v, count=%d", openId, exists, userCount)

	if !exists {
		// 用户不存在，创建新用户
		logx.Infof("用户不存在，创建新用户, openId: %s, 手机号: %s", openId, phoneInfo.PhoneNumber)
		err = s.CreateWxUser(ctx, openId, phoneInfo.PhoneNumber)
		if err != nil {
			logx.Errorf("创建用户失败: %v", err)
			return nil, fmt.Errorf("创建用户失败: %v", err)
		}
		logx.Infof("创建用户成功, openId: %s", openId)

		// 新用户注册完成，处理邀请码（如果有）
		s.processInvitationAfterRegistration(ctx, openId)
	} else {
		// 用户存在，更新手机号（使用gorm确保数据一致性）
		logx.Infof("用户存在，更新手机号, openId: %s, 手机号: %s", openId, phoneInfo.PhoneNumber)
		now := time.Now()
		err = mysql.Master().Table("wx_user").
			Where("open_id = ?", openId).
			Updates(map[string]interface{}{
				"mobile":     phoneInfo.PhoneNumber,
				"updated_at": now,
			}).Error
		if err != nil {
			logx.Errorf("更新手机号失败: %v", err)
			return nil, fmt.Errorf("更新手机号失败: %v", err)
		}
		logx.Infof("更新手机号成功, openId: %s", openId)
	}

	return phoneInfo, nil
}

// getAccessToken 获取微信access token
func (s *UserService) getAccessToken() (string, error) {
	// 检查 s.config 是否为 nil
	if s.config.Wechat.AppID == "" || s.config.Wechat.AppSecret == "" {
		logx.Error("获取access_token失败: 微信应用配置不完整，缺少AppID或AppSecret")
		return "", fmt.Errorf("微信应用配置不完整，缺少AppID或AppSecret")
	}

	// 从配置中获取appid和secret
	appid := s.config.Wechat.AppID      // 从微信小程序配置中获取AppID
	secret := s.config.Wechat.AppSecret // 从微信小程序配置中获取AppSecret

	logx.Infof("正在获取微信access_token，appID: %s", appid)

	// 调用微信接口获取access token
	url := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
		appid, secret)

	resp, err := http.Get(url)
	if err != nil {
		logx.Errorf("请求微信access_token接口失败: %v", err)
		return "", fmt.Errorf("请求微信access_token接口失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("读取微信access_token响应失败: %v", err)
		return "", fmt.Errorf("读取微信access_token响应失败: %v", err)
	}

	logx.Infof("微信access_token响应: %s", string(body))

	var result struct {
		AccessToken string `json:"access_token"`
		ExpiresIn   int    `json:"expires_in"`
		ErrCode     int    `json:"errcode"`
		ErrMsg      string `json:"errmsg"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		logx.Errorf("解析微信access_token响应失败: %v", err)
		return "", fmt.Errorf("解析微信access_token响应失败: %v", err)
	}

	if result.ErrCode != 0 {
		logx.Errorf("获取access_token失败: [%d] %s", result.ErrCode, result.ErrMsg)
		return "", fmt.Errorf("获取access_token失败: [%d] %s", result.ErrCode, result.ErrMsg)
	}

	logx.Infof("获取access_token成功，将在%d秒后过期", result.ExpiresIn)
	return result.AccessToken, nil
}

// getPhoneNumber 获取用户手机号
func (s *UserService) getPhoneNumber(accessToken, code string) (*types.PhoneInfo, error) {
	if accessToken == "" {
		logx.Error("获取手机号失败: access_token为空")
		return nil, fmt.Errorf("获取手机号失败: access_token为空")
	}

	if code == "" {
		logx.Error("获取手机号失败: code为空")
		return nil, fmt.Errorf("获取手机号失败: code为空")
	}

	url := fmt.Sprintf("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s", accessToken)
	logx.Infof("请求获取手机号接口，URL: %s", url)

	reqBody := map[string]string{
		"code": code,
	}
	reqData, err := json.Marshal(reqBody)
	if err != nil {
		logx.Errorf("序列化请求数据失败: %v", err)
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, strings.NewReader(string(reqData)))
	if err != nil {
		logx.Errorf("创建HTTP请求失败: %v", err)
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logx.Errorf("发送HTTP请求失败: %v", err)
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("读取响应内容失败: %v", err)
		return nil, fmt.Errorf("读取响应内容失败: %v", err)
	}

	logx.Infof("获取手机号接口响应: %s", string(body))

	var result struct {
		ErrCode   int    `json:"errcode"`
		ErrMsg    string `json:"errmsg"`
		PhoneInfo struct {
			PhoneNumber     string `json:"phoneNumber"`
			PurePhoneNumber string `json:"purePhoneNumber"`
			CountryCode     string `json:"countryCode"`
		} `json:"phone_info"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		logx.Errorf("解析响应数据失败: %v", err)
		return nil, fmt.Errorf("解析响应数据失败: %v", err)
	}

	if result.ErrCode != 0 {
		logx.Errorf("获取手机号失败: [%d] %s", result.ErrCode, result.ErrMsg)
		return nil, fmt.Errorf("获取手机号失败: [%d] %s", result.ErrCode, result.ErrMsg)
	}

	logx.Infof("获取手机号成功: %s", result.PhoneInfo.PhoneNumber)
	return &types.PhoneInfo{
		PhoneNumber:     result.PhoneInfo.PhoneNumber,
		PurePhoneNumber: result.PhoneInfo.PurePhoneNumber,
		CountryCode:     result.PhoneInfo.CountryCode,
	}, nil
}

// processInvitationAfterRegistration 处理用户注册后的邀请逻辑
func (s *UserService) processInvitationAfterRegistration(ctx context.Context, openId string) {
	// 这里可以从缓存或其他地方获取邀请码
	// 由于当前架构限制，这里暂时留空，实际处理可以在前端完成注册后调用邀请接受接口
	logx.Infof("用户注册完成，可以处理邀请逻辑: openId=%s", openId)

	// TODO: 实际项目中可以：
	// 1. 从Redis缓存中获取该用户的邀请码（如果有）
	// 2. 调用邀请服务处理邀请接受
	// 3. 触发用户等级升级检查
	// 4. 发放注册奖励等
}
