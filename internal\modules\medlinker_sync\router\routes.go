package router

import (
	"net/http"

	"github.com/gorilla/mux"
	"yekaitai/cmd/cron/tasks"
	"yekaitai/internal/modules/medlinker_sync/handler"
)

// RegisterMedlinkerSyncRoutes 注册医联数据同步路由
func RegisterMedlinkerSyncRoutes(r *mux.Router) {
	// 获取医联客户端实例
	medlinkerClient := tasks.GetMedlinkerClientInstance()
	if medlinkerClient == nil {
		// 如果任务未初始化，先初始化
		tasks.InitMedlinkerSyncTask()
		medlinkerClient = tasks.GetMedlinkerClientInstance()
	}

	// 创建处理器
	syncHandler := handler.NewSyncHandler(medlinkerClient)

	// 医联数据同步相关路由
	syncRouter := r.PathPrefix("/api/admin/medlinker").Subrouter()

	// 全量同步数据
	syncRouter.HandleFunc("/sync/full", syncHandler.SyncAllData).Methods("POST")

	// 增量同步数据
	syncRouter.HandleFunc("/sync/incremental", syncHandler.SyncIncrementalData).Methods("POST")

	// 测试医联连接
	syncRouter.HandleFunc("/test/connection", syncHandler.TestMedlinkerConnection).Methods("POST")

	// 获取同步状态
	syncRouter.HandleFunc("/status", syncHandler.GetSyncStatus).Methods("GET")

	// 设置同步配置
	syncRouter.HandleFunc("/config", syncHandler.SetSyncConfig).Methods("POST")

	// 获取同步日志
	syncRouter.HandleFunc("/logs", syncHandler.GetSyncLogs).Methods("GET")

	// 手动触发任务的路由
	taskRouter := r.PathPrefix("/api/admin/tasks/medlinker").Subrouter()

	// 手动执行全量同步
	taskRouter.HandleFunc("/full-sync", func(w http.ResponseWriter, r *http.Request) {
		go tasks.RunMedlinkerFullSync()
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"success": true, "message": "全量同步任务已启动"}`))
	}).Methods("POST")

	// 手动执行增量同步
	taskRouter.HandleFunc("/incremental-sync", func(w http.ResponseWriter, r *http.Request) {
		go tasks.RunMedlinkerIncrementalSync()
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"success": true, "message": "增量同步任务已启动"}`))
	}).Methods("POST")

	// 获取任务状态
	taskRouter.HandleFunc("/status", func(w http.ResponseWriter, r *http.Request) {
		status := tasks.GetMedlinkerSyncStatus()
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		// 简单的JSON序列化
		response := `{"success": true, "data": {"enabled": true, "interval": "10m0s", "name": "医联数据同步任务"}}`
		w.Write([]byte(response))
	}).Methods("GET")

	// 启用/禁用任务
	taskRouter.HandleFunc("/toggle", func(w http.ResponseWriter, r *http.Request) {
		enabled := r.URL.Query().Get("enabled") == "true"
		tasks.SetMedlinkerSyncEnabled(enabled)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if enabled {
			w.Write([]byte(`{"success": true, "message": "医联同步任务已启用"}`))
		} else {
			w.Write([]byte(`{"success": true, "message": "医联同步任务已禁用"}`))
		}
	}).Methods("POST")

	// 设置同步间隔
	taskRouter.HandleFunc("/interval", func(w http.ResponseWriter, r *http.Request) {
		minutes := r.URL.Query().Get("minutes")
		if minutes == "" {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`{"success": false, "message": "缺少minutes参数"}`))
			return
		}

		// 简单的字符串转换（实际应该使用strconv.Atoi）
		var intervalMinutes int
		switch minutes {
		case "5":
			intervalMinutes = 5
		case "10":
			intervalMinutes = 10
		case "15":
			intervalMinutes = 15
		case "30":
			intervalMinutes = 30
		case "60":
			intervalMinutes = 60
		default:
			intervalMinutes = 10
		}

		tasks.SetMedlinkerSyncInterval(intervalMinutes)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"success": true, "message": "同步间隔设置成功"}`))
	}).Methods("POST")

	// 测试连接
	taskRouter.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		err := tasks.TestMedlinkerConnection()
		w.Header().Set("Content-Type", "application/json")
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(`{"success": false, "message": "连接测试失败: ` + err.Error() + `"}`))
		} else {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"success": true, "message": "医联连接测试成功"}`))
		}
	}).Methods("POST")
}

// RegisterMedlinkerSyncMiddleware 注册医联同步中间件
func RegisterMedlinkerSyncMiddleware(r *mux.Router) {
	// 可以添加认证、日志等中间件
	r.Use(func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 添加CORS头
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

			// 处理预检请求
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}

			next.ServeHTTP(w, r)
		})
	})
}

// InitMedlinkerSyncRoutes 初始化医联同步路由
func InitMedlinkerSyncRoutes(r *mux.Router) {
	// 注册中间件
	RegisterMedlinkerSyncMiddleware(r)

	// 注册路由
	RegisterMedlinkerSyncRoutes(r)
}
