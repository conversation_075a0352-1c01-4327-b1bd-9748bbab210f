package bootstrap

import (
	"fmt"
	"log"

	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/consultation/model"
)

// 需要迁移的医疗聊天模型
var medchatModels = []interface{}{
	&model.ChatStream{},
	&model.ChatSession{},
}

// MigrateMedChat 医疗聊天表迁移
func MigrateMedChat() error {
	log.Println("开始执行医疗聊天模块表结构迁移...")

	// 执行医疗聊天表结构迁移
	db := mysql.Master()

	db.Set("gorm:table_options", "COMMENT='聊天流记录表'").AutoMigrate(&model.ChatStream{})
	db.Set("gorm:table_options", "COMMENT='聊天会话表'").AutoMigrate(&model.ChatSession{})

	// 其他模型通过批量迁移
	if err := mysql.AutoMigrate(medchatModels...); err != nil {
		log.Printf("医疗聊天模块表结构迁移失败: %v", err)
		return fmt.Errorf("医疗聊天模块表结构迁移失败: %w", err)
	}

	log.Println("医疗聊天模块表结构迁移完成")
	return nil
}
