package types

import (
	"time"

	"yekaitai/wx_internal/types"
)

// WxDoctorInfo 医生信息
type WxDoctorInfo struct {
	DoctorID       uint      `json:"doctor_id"`       // 医生ID
	UserID         uint      `json:"user_id"`         // 用户ID
	OpenID         string    `json:"open_id"`         // 微信 OpenID
	Mobile         string    `json:"mobile"`          // 手机号
	Nickname       string    `json:"nickname"`        // 昵称
	Avatar         string    `json:"avatar"`          // 头像
	Gender         int       `json:"gender"`          // 性别 0-未知 1-男 2-女
	MedicalLicense string    `json:"medical_license"` // 执业证号
	Specialty      string    `json:"specialty"`       // 专业领域
	Hospital       string    `json:"hospital"`        // 所属医院
	Department     string    `json:"department"`      // 科室
	Title          string    `json:"title"`           // 职称
	Introduction   string    `json:"introduction"`    // 简介
	Status         int       `json:"status"`          // 状态 1-正常 0-禁用
	CreatedAt      time.Time `json:"created_at"`      // 创建时间
	UpdatedAt      time.Time `json:"updated_at"`      // 更新时间
}

// CreateDoctorRequest 创建医生请求
type CreateDoctorRequest struct {
	UserID         uint   `json:"user_id" binding:"required"` // 用户ID
	MedicalLicense string `json:"medical_license,omitempty"`  // 执业证号(可选)
	Specialty      string `json:"specialty,omitempty"`        // 专业领域(可选)
	Hospital       string `json:"hospital,omitempty"`         // 所属医院(可选)
	Department     string `json:"department,omitempty"`       // 科室(可选)
	Title          string `json:"title,omitempty"`            // 职称(可选)
	Introduction   string `json:"introduction,omitempty"`     // 简介(可选)
}

// UpdateDoctorRequest 更新医生请求
type UpdateDoctorRequest struct {
	MedicalLicense string `json:"medical_license,omitempty"` // 执业证号(可选)
	Specialty      string `json:"specialty,omitempty"`       // 专业领域(可选)
	Hospital       string `json:"hospital,omitempty"`        // 所属医院(可选)
	Department     string `json:"department,omitempty"`      // 科室(可选)
	Title          string `json:"title,omitempty"`           // 职称(可选)
	Introduction   string `json:"introduction,omitempty"`    // 简介(可选)
	Status         *int   `json:"status,omitempty"`          // 状态(可选) 1-正常 0-禁用
}

// GetDoctorRequest 获取医生请求
type GetDoctorRequest struct {
	DoctorID uint `path:"doctor_id" binding:"required"` // 医生ID
}

// GetDoctorByUserIDRequest 通过用户ID获取医生请求
type GetDoctorByUserIDRequest struct {
	UserID uint `path:"user_id" binding:"required"` // 用户ID
}

// ListDoctorsRequest 医生列表请求
type ListDoctorsRequest struct {
	types.PaginationRequest
	Nickname  string `form:"nickname,optional"`  // 昵称，模糊查询
	Mobile    string `form:"mobile,optional"`    // 手机号，精确查询
	Hospital  string `form:"hospital,optional"`  // 医院，模糊查询
	Specialty string `form:"specialty,optional"` // 专业领域，模糊查询
	Status    *int   `form:"status,optional"`    // 状态，可选 1-正常 0-禁用
}

// ChangeDoctorStatusRequest 变更医生状态请求
type ChangeDoctorStatusRequest struct {
	DoctorID uint `json:"doctor_id" binding:"required"` // 医生ID
	Status   int  `json:"status" binding:"required"`    // 状态 1-正常 0-禁用
}

// BatchImportDoctorsRequest 批量导入医生请求
type BatchImportDoctorsRequest struct {
	Doctors []CreateDoctorRequest `json:"doctors" binding:"required"` // 医生列表
}

// DoctorIDResponse 医生ID响应
type DoctorIDResponse struct {
	DoctorID uint `json:"doctor_id"` // 医生ID
}
