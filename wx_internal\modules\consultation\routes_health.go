package consultation

import (
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/consultation/handler"
	"yekaitai/wx_internal/queue"
	"yekaitai/wx_internal/svc"
)

// RestServer 定义路由服务器接口
type RestServer interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
}

// RegisterHealthConsultRoutes 注册健康咨询模块路由
func RegisterHealthConsultRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 创建Kafka生产者适配器
	kafkaProducer := queue.NewKafkaProducerAdapter()

	// 创建处理器，传入服务上下文
	healthConsultHandler := handler.NewHealthConsultHandler(serverCtx, serverCtx.MedlinkerClient, kafkaProducer)

	// 注册HTTP路由
	server.AddRoutes(
		[]rest.Route{
			// 初始化健康咨询会话
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/consultation/health/init",
				Handler: wxAuthWrapper(healthConsultHandler.InitHealthConsult),
			},
			// 获取会话历史记录
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/consultation/health/history",
				Handler: wxAuthWrapper(healthConsultHandler.GetSessionHistory),
			},
			// 获取会话详情
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/consultation/health/detail",
				Handler: wxAuthWrapper(healthConsultHandler.GetSessionDetail),
			},
		},
	)

	logx.Info("已注册健康咨询HTTP路由")

	// WebSocket路由
	logx.Info("开始注册健康咨询WebSocket路由...")

	// 创建WebSocket专用路由组
	wsGroup := []rest.Route{
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/consultation/ws/health",
			Handler: wxAuthWrapper(healthConsultHandler.HandleWebSocket),
		},
	}

	// 注册WebSocket路由
	server.AddRoutes(wsGroup)

	logx.Info("已注册健康咨询WebSocket路由: /api/wx/consultation/ws/health，支持所有AI模型（健康咨询、预问诊、报告解读、智能分导诊、智能客服）")
}

// RegisterAllConsultationRoutes 注册所有咨询模块路由
func RegisterAllConsultationRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 注册预问诊路由（从routes.go）
	// RegisterConsultationRoutes(server, serverCtx)

	// 注册健康咨询路由
	RegisterHealthConsultRoutes(server, serverCtx)

	logx.Info("所有咨询模块路由注册完成")
}
