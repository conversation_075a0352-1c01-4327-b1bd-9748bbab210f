package model

import (
	"database/sql"
	"errors"
	"time"

	"gorm.io/gorm"
)

// EnabledRegion 已开通地区模型
type EnabledRegion struct {
	ID         int64        `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	RegionCode string       `json:"region_code" gorm:"type:varchar(20);uniqueIndex;comment:行政区划代码"` // 行政区划代码
	RegionName string       `json:"region_name" gorm:"type:varchar(50);not null;comment:地区名称"`      // 地区名称
	Level      int          `json:"level" gorm:"type:tinyint;not null;comment:级别:1-省级 2-地级 3-县级"`   // 级别: 1-省级 2-地级 3-县级
	CreatedBy  uint         `json:"created_by" gorm:"type:int unsigned;not null;comment:创建人ID"`     // 创建人ID
	CreatedAt  time.Time    `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt  time.Time    `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt  sql.NullTime `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (r *EnabledRegion) TableName() string {
	return "t_enabled_regions"
}

// EnabledRegionList 带分页的已开通地区列表
type EnabledRegionList struct {
	Total int64            `json:"total"`
	List  []*EnabledRegion `json:"list"`
}

// EnabledRegionRepository 已开通地区仓库接口
type EnabledRegionRepository interface {
	// 创建已开通地区
	Create(region *EnabledRegion) error
	// 批量创建已开通地区
	BatchCreate(regions []*EnabledRegion) error
	// 根据编码查找已开通地区
	FindByCode(code string) (*EnabledRegion, error)
	// 获取已开通地区列表（分页）
	List(page, size int, query string) ([]*EnabledRegion, int64, error)
	// 获取所有已开通省份
	GetProvinces() ([]*EnabledRegion, error)
	// 获取指定已开通省份的所有城市
	GetCities(provinceCode string) ([]*EnabledRegion, error)
	// 获取指定已开通城市的所有区县
	GetAreas(cityCode string) ([]*EnabledRegion, error)
	// 根据编码删除已开通地区
	DeleteByCode(code string) error
	// 检查地区是否已开通
	IsEnabled(code string) (bool, error)
}

// enabledRegionRepository 实现了EnabledRegionRepository接口
type enabledRegionRepository struct {
	db *gorm.DB
}

// NewEnabledRegionRepository 创建已开通地区仓库实例
func NewEnabledRegionRepository(db *gorm.DB) EnabledRegionRepository {
	return &enabledRegionRepository{db: db}
}

// Create 创建已开通地区
func (r *enabledRegionRepository) Create(region *EnabledRegion) error {
	// 检查是否已存在
	exists, err := r.IsEnabled(region.RegionCode)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if exists {
		return errors.New("地区已开通")
	}
	return r.db.Create(region).Error
}

// BatchCreate 批量创建已开通地区
func (r *enabledRegionRepository) BatchCreate(regions []*EnabledRegion) error {
	if len(regions) == 0 {
		return nil
	}
	return r.db.CreateInBatches(regions, 100).Error
}

// FindByCode 根据编码查找已开通地区
func (r *enabledRegionRepository) FindByCode(code string) (*EnabledRegion, error) {
	var region EnabledRegion
	err := r.db.Where("region_code = ?", code).First(&region).Error
	if err != nil {
		return nil, err
	}
	return &region, nil
}

// List 获取已开通地区列表（分页）
func (r *enabledRegionRepository) List(page, size int, query string) ([]*EnabledRegion, int64, error) {
	var regions []*EnabledRegion
	var total int64

	db := r.db.Model(&EnabledRegion{})

	if query != "" {
		db = db.Where("region_name LIKE ? OR region_code LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if page > 0 && size > 0 {
		offset := (page - 1) * size
		db = db.Offset(offset).Limit(size)
	}

	// 按级别和名称排序
	err = db.Order("level ASC, region_name ASC").Find(&regions).Error
	return regions, total, err
}

// GetProvinces 获取所有已开通省份
func (r *enabledRegionRepository) GetProvinces() ([]*EnabledRegion, error) {
	var provinces []*EnabledRegion
	err := r.db.Where("level = ?", 1).Find(&provinces).Error
	return provinces, err
}

// GetCities 获取指定已开通省份的所有城市
func (r *enabledRegionRepository) GetCities(provinceCode string) ([]*EnabledRegion, error) {
	// 查找省份对应的城市编码前缀（前2位）
	prefix := provinceCode[:2]
	var cities []*EnabledRegion
	// 查找已开通的城市，城市编码前2位为省份编码，且level为2
	err := r.db.Where("region_code LIKE ? AND level = ?", prefix+"%", 2).Find(&cities).Error
	return cities, err
}

// GetAreas 获取指定已开通城市的所有区县
func (r *enabledRegionRepository) GetAreas(cityCode string) ([]*EnabledRegion, error) {
	// 查找城市对应的区县编码前缀（前4位）
	prefix := cityCode[:4]
	var areas []*EnabledRegion
	// 查找已开通的区县，区县编码前4位为城市编码，且level为3
	err := r.db.Where("region_code LIKE ? AND level = ?", prefix+"%", 3).Find(&areas).Error
	return areas, err
}

// DeleteByCode 根据编码删除已开通地区
func (r *enabledRegionRepository) DeleteByCode(code string) error {
	return r.db.Where("region_code = ?", code).Delete(&EnabledRegion{}).Error
}

// IsEnabled 检查地区是否已开通
func (r *enabledRegionRepository) IsEnabled(code string) (bool, error) {
	var count int64
	err := r.db.Model(&EnabledRegion{}).Where("region_code = ?", code).Count(&count).Error
	return count > 0, err
}
