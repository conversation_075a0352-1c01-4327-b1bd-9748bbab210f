package handler

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"yekaitai/internal/modules/content/model"
	refundService "yekaitai/internal/modules/refund/service"
	"yekaitai/internal/types"
	orderModel "yekaitai/pkg/common/model/order"
	"yekaitai/pkg/common/wechatpay"
	"yekaitai/pkg/infra/mysql"
	activityHandler "yekaitai/wx_internal/modules/activity/handler"
	"yekaitai/wx_internal/modules/order/service"
	"yekaitai/wx_internal/utils"

	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type OrderGoZeroHandler struct {
	orderService *service.OrderService
}

func NewOrderGoZeroHandler() *OrderGoZeroHandler {
	orderService, err := service.NewOrderService()
	if err != nil {
		logx.Errorf("初始化订单服务失败: %v", err)
		// 即使初始化失败，也要返回一个处理器，避免 nil 指针错误
		return &OrderGoZeroHandler{
			orderService: nil,
		}
	}

	return &OrderGoZeroHandler{
		orderService: orderService,
	}
}

// CreateOrder 创建订单接口（核心支付流程）
func (h *OrderGoZeroHandler) CreateOrder(w http.ResponseWriter, r *http.Request) {
	logx.Infof("开始创建订单，请求路径: %s", r.URL.Path)

	// 检查订单服务是否初始化成功
	if h.orderService == nil {
		logx.Error("订单服务未初始化成功")
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "订单服务暂时不可用，请稍后重试"))
		return
	}

	var req orderModel.CreateOrderRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析创建订单请求参数失败: %v, 请求体: %s", err, r.Body)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数格式错误: "+err.Error()))
		return
	}

	logx.Infof("创建订单请求参数: 购物车IDs=%v, 地址ID=%d, 优惠券IDs=%v, 使用积分=%t, 备注=%s",
		req.CartIDs, req.AddressID, req.CouponIDs, req.UsePoints, req.Remark)

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		logx.Error("获取用户ID失败")
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户身份验证失败"))
		return
	}

	logx.Infof("用户ID: %d", userID)

	// 获取用户OpenID (从认证中间件设置的context中获取)
	userOpenID, err := utils.GetOpenIDFromContext(r.Context())
	if err != nil {
		logx.Errorf("获取用户OpenID失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户身份验证失败"))
		return
	}

	logx.Infof("用户OpenID: %s", userOpenID)

	// 创建订单并生成支付参数
	resp, err := h.orderService.CreateOrder(r.Context(), userID, userOpenID, &req)
	if err != nil {
		logx.Errorf("创建订单业务逻辑失败: userID=%d, cartIDs=%v, addressID=%d, error=%v",
			userID, req.CartIDs, req.AddressID, err)

		// 根据不同错误类型返回更具体的错误信息
		var errorCode int
		var errorMessage string

		switch {
		case err.Error() == "微信支付服务未初始化":
			errorCode = types.CodeInternalError
			errorMessage = "支付服务暂时不可用，请稍后重试"
		case err.Error() == "用户身份验证失败":
			errorCode = types.CodeUnauthorized
			errorMessage = "用户身份验证失败"
		default:
			errorCode = types.CodeInternalError
			errorMessage = err.Error()
		}

		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(errorCode, errorMessage))
		return
	}

	logx.Infof("创建订单成功: orderID=%d, orderNo=%s, totalAmount=%.2f",
		resp.OrderID, resp.OrderNo, resp.TotalAmount)

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// HandleWechatPayNotify 处理微信支付回调通知
func (h *OrderGoZeroHandler) HandleWechatPayNotify(w http.ResponseWriter, r *http.Request) {
	// 获取回调服务实例
	notifyService := wechatpay.GetGlobalNotifyService()
	if notifyService == nil {
		logx.Error("回调服务未初始化")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	// 设置支付回调处理器
	notifyService.SetPaymentHandler(h.handlePaymentSuccess)

	// 处理支付回调
	notifyService.HandlePaymentNotify(w, r)
}

// HandleWechatRefundNotify 处理微信退款回调通知
func (h *OrderGoZeroHandler) HandleWechatRefundNotify(w http.ResponseWriter, r *http.Request) {
	logx.Info("===== 收到微信退款回调请求 =====")

	// 获取回调服务实例
	notifyService := wechatpay.GetGlobalNotifyService()
	if notifyService == nil {
		logx.Error("微信回调服务未初始化")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	// 设置退款回调处理器
	notifyService.SetRefundHandler(h.handleRefundSuccess)

	// 处理真实的微信退款回调
	logx.Info("开始处理微信退款回调...")
	notifyService.HandleRefundNotify(w, r)
	logx.Info("微信退款回调处理完成")
}

// handlePaymentSuccess 处理支付成功的业务逻辑
func (h *OrderGoZeroHandler) handlePaymentSuccess(ctx context.Context, transaction *payments.Transaction) error {
	// 获取商户订单号
	outTradeNo := *transaction.OutTradeNo

	logx.Infof("处理支付成功回调: 订单号=%s, 微信订单号=%s", outTradeNo, *transaction.TransactionId)

	// 根据订单号前缀路由到不同的业务模块
	bizType := getBizTypeFromOrderNo(outTradeNo)
	switch bizType {
	case "activity":
		return h.handleActivityPaymentSuccess(ctx, transaction)
	case "service":
		return h.handleServicePaymentSuccess(ctx, transaction)
	case "mall":
		// 商城订单，使用原有逻辑
		return h.handleMallPaymentSuccess(ctx, transaction)
	default:
		// 默认使用商城订单逻辑（兼容旧订单）
		return h.handleMallPaymentSuccess(ctx, transaction)
	}
}

// getBizTypeFromOrderNo 根据订单号获取业务类型
func getBizTypeFromOrderNo(orderNo string) string {
	if len(orderNo) < 3 {
		return "mall"
	}

	prefix := orderNo[:3]
	switch prefix {
	case "ACT":
		return "activity"
	case "SRV":
		return "service"
	default:
		return "mall"
	}
}

// handleActivityPaymentSuccess 处理活动支付成功
func (h *OrderGoZeroHandler) handleActivityPaymentSuccess(ctx context.Context, transaction *payments.Transaction) error {
	orderNo := *transaction.OutTradeNo
	logx.Infof("处理活动支付成功: orderNo=%s", orderNo)

	// 调用活动模块的支付成功处理逻辑
	err := h.processActivityPaymentSuccess(ctx, orderNo, *transaction.TransactionId)
	if err != nil {
		logx.Errorf("处理活动支付成功失败: orderNo=%s, error=%v", orderNo, err)
		return err
	}

	logx.Infof("活动支付回调处理完成: orderNo=%s", orderNo)
	return nil
}

// handleServicePaymentSuccess 处理服务支付成功
func (h *OrderGoZeroHandler) handleServicePaymentSuccess(ctx context.Context, transaction *payments.Transaction) error {
	orderNo := *transaction.OutTradeNo
	logx.Infof("处理服务支付成功: orderNo=%s", orderNo)

	// TODO: 调用服务模块的支付成功处理逻辑

	logx.Infof("服务支付回调处理完成: orderNo=%s", orderNo)
	return nil
}

// handleMallPaymentSuccess 处理商城支付成功
func (h *OrderGoZeroHandler) handleMallPaymentSuccess(ctx context.Context, transaction *payments.Transaction) error {
	outTradeNo := *transaction.OutTradeNo
	logx.Infof("处理商城支付成功: orderNo=%s", outTradeNo)

	// 调用订单服务更新订单状态
	err := h.orderService.UpdateOrderStatusByPayment(ctx, outTradeNo, *transaction.TransactionId, string(*transaction.TradeState))
	if err != nil {
		logx.Errorf("更新订单支付状态失败: %v", err)
		return err
	}

	logx.Infof("商城支付回调处理完成: orderNo=%s", outTradeNo)
	return nil
}

// processActivityPaymentSuccess 处理活动支付成功的具体业务逻辑
func (h *OrderGoZeroHandler) processActivityPaymentSuccess(ctx context.Context, orderNo, transactionID string) error {
	// 更新活动订单状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":                1, // 已报名
		"payment_time":          &now,
		"wechat_transaction_id": transactionID,
	}

	// 查询订单信息
	var order model.ContentSignUpOrder
	err := mysql.Master().Where("order_no = ?", orderNo).First(&order).Error
	if err != nil {
		logx.Errorf("查询活动订单失败: orderNo=%s, error=%v", orderNo, err)
		return fmt.Errorf("查询活动订单失败: %w", err)
	}

	// 更新订单状态
	err = mysql.Master().Model(&order).Updates(updates).Error
	if err != nil {
		logx.Errorf("更新活动订单状态失败: orderNo=%s, error=%v", orderNo, err)
		return fmt.Errorf("更新活动订单状态失败: %w", err)
	}

	// 根据活动类型处理积分奖励
	h.processActivitySignUpRewardWithType(order.UserID, order.ContentID, orderNo, "付费活动报名")

	logx.Infof("活动支付成功处理完成: orderNo=%s, userID=%d", orderNo, order.UserID)
	return nil
}

// processActivitySignUpReward 处理活动报名积分奖励
func (h *OrderGoZeroHandler) processActivitySignUpReward(userID uint, orderNo, description string) {
	// 使用积分奖励助手
	coinHelper := activityHandler.NewCoinRewardHelper()
	coinHelper.ProcessActivitySignUpReward(userID, orderNo, description)
}

// processActivitySignUpRewardWithType 根据活动类型和后台配置处理活动报名积分奖励
func (h *OrderGoZeroHandler) processActivitySignUpRewardWithType(userID uint, activityID uint, orderNo, description string) {
	// 查询活动信息，判断是否为专项活动
	var activity struct {
		IsSpecialActivity bool `json:"is_special_activity"`
	}

	err := mysql.Slave().Table("t_contents").
		Select("is_special_activity").
		Where("id = ? AND type = 'activity'", activityID).
		First(&activity).Error

	if err != nil {
		logx.Errorf("查询活动信息失败: activityID=%d, error=%v", activityID, err)
		return
	}

	// 检查后台积分奖励配置
	normalActivityEnabled := h.isActivityRewardEnabled("ACTIVITY_SIGNUP")
	specialActivityEnabled := h.isActivityRewardEnabled("SPECIAL_ACTIVITY_SIGNUP")

	// 使用积分奖励助手
	coinHelper := activityHandler.NewCoinRewardHelper()

	if activity.IsSpecialActivity {
		// 专项活动：根据配置给予奖励
		if normalActivityEnabled {
			coinHelper.ProcessActivitySignUpReward(userID, orderNo, description)
			logx.Infof("专项活动报名，给予普通活动奖励: userID=%d, activityID=%d", userID, activityID)
		}
		if specialActivityEnabled {
			coinHelper.ProcessSpecialActivitySignUpReward(userID, orderNo, description+" - 专项活动额外奖励")
			logx.Infof("专项活动报名，给予专项活动额外奖励: userID=%d, activityID=%d", userID, activityID)
		}
		if !normalActivityEnabled && !specialActivityEnabled {
			logx.Infof("专项活动报名，但积分奖励未启用: userID=%d, activityID=%d", userID, activityID)
		}
	} else {
		// 普通活动：只检查普通活动奖励配置
		if normalActivityEnabled {
			coinHelper.ProcessActivitySignUpReward(userID, orderNo, description)
			logx.Infof("普通活动报名，给予基础积分奖励: userID=%d, activityID=%d", userID, activityID)
		} else {
			logx.Infof("普通活动报名，但积分奖励未启用: userID=%d, activityID=%d", userID, activityID)
		}
	}
}

// isActivityRewardEnabled 检查指定类型的活动积分奖励是否启用
func (h *OrderGoZeroHandler) isActivityRewardEnabled(activityType string) bool {
	var count int64
	err := mysql.Slave().Table("coin_rules").
		Where("activity_type = ? AND enabled = 1", activityType).
		Count(&count).Error

	if err != nil {
		logx.Errorf("检查积分奖励配置失败: activityType=%s, error=%v", activityType, err)
		return false
	}

	return count > 0
}

// handleRefundSuccess 处理退款成功的业务逻辑
func (h *OrderGoZeroHandler) handleRefundSuccess(ctx context.Context, refund *refunddomestic.Refund) error {
	logx.Info("===== 开始处理退款回调业务逻辑 =====")

	// 安全地获取商户订单号和退款单号，避免空指针异常
	var outTradeNo, outRefundNo, refundStatus string
	if refund.OutTradeNo != nil {
		outTradeNo = *refund.OutTradeNo
	}
	if refund.OutRefundNo != nil {
		outRefundNo = *refund.OutRefundNo
	}
	if refund.Status != nil {
		refundStatus = string(*refund.Status)
	}

	logx.Infof("[退款回调] 订单号: %s", outTradeNo)
	logx.Infof("[退款回调] 退款单号: %s", outRefundNo)
	logx.Infof("[退款回调] 退款状态: %s", refundStatus)

	// 验证必需字段（临时放宽状态验证，用于调试）
	if outTradeNo == "" || outRefundNo == "" {
		return fmt.Errorf("退款回调数据不完整: 订单号=%s, 退款单号=%s, 状态=%s", outTradeNo, outRefundNo, refundStatus)
	}

	// 如果状态为空，设置默认值
	if refundStatus == "" {
		logx.Infof("⚠️ 退款状态为空，设置默认值为SUCCESS")
		refundStatus = "SUCCESS"
	}

	// 记录微信退款的详细信息
	if refund.RefundId != nil {
		logx.Infof("[退款回调] 微信退款单号: %s", *refund.RefundId)
	}
	if refund.TransactionId != nil {
		logx.Infof("[退款回调] 微信支付订单号: %s", *refund.TransactionId)
	}
	if refund.Amount != nil && refund.Amount.Refund != nil {
		logx.Infof("[退款回调] 退款金额: %d分", *refund.Amount.Refund)
	}
	if refund.SuccessTime != nil {
		logx.Infof("[退款回调] 退款成功时间: %s", *refund.SuccessTime)
	}

	// 使用通用退款服务处理回调
	logx.Info("[退款回调] 开始处理退款回调...")
	refundSvc := refundService.NewRefundService()

	// 构建回调数据
	callbackData := map[string]interface{}{
		"out_trade_no":  outTradeNo,
		"out_refund_no": outRefundNo,
		"refund_status": refundStatus,
	}

	if refund.RefundId != nil {
		callbackData["refund_id"] = *refund.RefundId
	}
	if refund.Amount != nil && refund.Amount.Refund != nil {
		callbackData["refund_amount"] = *refund.Amount.Refund
	}

	// 调用通用退款服务处理回调
	err := refundSvc.ProcessRefundCallback(ctx, callbackData)
	if err != nil {
		logx.Errorf("[退款回调] 通用退款服务处理失败: %v", err)

		// 如果是商城订单，回退到原有逻辑
		bizType := getBizTypeFromOrderNo(outTradeNo)
		if bizType == "mall" {
			logx.Info("[退款回调] 回退到商城订单退款处理...")
			err = h.orderService.UpdateOrderRefundStatus(ctx, outTradeNo, outRefundNo, refundStatus)
			if err != nil {
				logx.Errorf("[退款回调] 更新商城订单退款状态失败: %v", err)
				return err
			}
		} else {
			return err
		}
	}

	logx.Info("[退款回调] 退款回调处理成功")
	logx.Infof("===== 退款回调业务处理完成: 订单号=%s =====", outTradeNo)
	return nil
}

// GetOrderList 获取订单列表
func (h *OrderGoZeroHandler) GetOrderList(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 获取查询参数
	status := r.URL.Query().Get("status")
	page := r.URL.Query().Get("page")
	limit := r.URL.Query().Get("limit")

	// 调用服务获取订单列表
	orders, err := h.orderService.GetOrderList(r.Context(), userID, status, page, limit)
	if err != nil {
		logx.Errorf("获取订单列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(orders))
}

// GetOrderDetail 获取订单详情
func (h *OrderGoZeroHandler) GetOrderDetail(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 从URL路径中获取订单ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderIDStr := parts[len(parts)-1]
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil || orderID <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	// 调用服务获取订单详情
	order, err := h.orderService.GetOrderDetail(r.Context(), userID, orderIDStr)
	if err != nil {
		logx.Errorf("获取订单详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(order))
}

// CancelOrder 取消订单
func (h *OrderGoZeroHandler) CancelOrder(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 从URL路径中获取订单ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderIDStr := parts[len(parts)-2] // 因为路径是 /orders/:id/cancel，所以ID在倒数第二个位置
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil || orderID <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	// 调用服务取消订单
	err = h.orderService.CancelOrder(r.Context(), userID, orderIDStr)
	if err != nil {
		logx.Errorf("取消订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse("订单已取消"))
}

// ConfirmReceive 确认收货
func (h *OrderGoZeroHandler) ConfirmReceive(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 从URL路径中获取订单ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderIDStr := parts[len(parts)-2] // 因为路径是 /orders/:id/receive，所以ID在倒数第二个位置
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil || orderID <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	// 调用服务确认收货
	err = h.orderService.ConfirmReceive(r.Context(), userID, orderIDStr)
	if err != nil {
		logx.Errorf("确认收货失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse("已确认收货"))
}

// GetOrderDetailWithRefund 获取包含退款信息的订单详情
func (h *OrderGoZeroHandler) GetOrderDetailWithRefund(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 从URL路径中获取订单ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderIDStr := parts[len(parts)-1]
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil || orderID <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	// 调用服务获取订单详情
	order, err := h.orderService.GetOrderDetailWithRefundInfo(r.Context(), userID, orderIDStr)
	if err != nil {
		logx.Errorf("获取订单详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(order))
}

// RepayOrder 重新支付订单（支付参数过期时使用）
func (h *OrderGoZeroHandler) RepayOrder(w http.ResponseWriter, r *http.Request) {
	// 从URL路径中获取订单ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderIDStr := parts[len(parts)-2] // 因为路径是 /orders/:id/repay，所以ID在倒数第二个位置
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil || orderID <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 获取用户OpenID
	userOpenID, err := utils.GetOpenIDFromContext(r.Context())
	if err != nil {
		logx.Errorf("获取用户OpenID失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户身份验证失败"))
		return
	}

	// 验证订单归属
	order, err := h.orderService.GetOrderByID(r.Context(), uint(orderID))
	if err != nil {
		logx.Errorf("查询订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "订单不存在"))
		return
	}

	if order.UserID != userID {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "无权限操作此订单"))
		return
	}

	// 检查订单状态
	if order.Status != 1 { // 只有待支付订单才能重新支付
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单状态不支持重新支付"))
		return
	}

	// 重新创建微信支付订单
	resp, err := h.orderService.CreateWechatPayOrder(r.Context(), uint(orderID), userOpenID)
	if err != nil {
		logx.Errorf("重新创建支付订单失败: %v", err)
		httpx.WriteJson(w, http.StatusInternalServerError, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	logx.Infof("订单重新支付成功: orderID=%d, orderNo=%s", orderID, order.OrderNo)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// QueryPaymentOrder 查询支付订单状态
func (h *OrderGoZeroHandler) QueryPaymentOrder(w http.ResponseWriter, r *http.Request) {
	orderNo := r.URL.Query().Get("order_no")
	if orderNo == "" {
		httpx.WriteJson(w, http.StatusBadRequest, types.NewErrorResponse(types.CodeInvalidParams, "订单号不能为空"))
		return
	}

	resp, err := h.orderService.QueryWechatPayOrder(r.Context(), orderNo)
	if err != nil {
		logx.Errorf("查询支付订单失败: %v", err)
		httpx.WriteJson(w, http.StatusInternalServerError, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// CreateRefund 创建退款
func (h *OrderGoZeroHandler) CreateRefund(w http.ResponseWriter, r *http.Request) {
	var req struct {
		OrderID      uint    `json:"order_id" validate:"required"`
		RefundAmount float64 `json:"refund_amount" validate:"required,min=0.01"`
		Reason       string  `json:"reason" validate:"required"`
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusBadRequest, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	// 创建微信退款
	resp, err := h.orderService.CreateWechatRefund(r.Context(), req.OrderID, req.RefundAmount, req.Reason)
	if err != nil {
		logx.Errorf("创建退款失败: %v", err)
		httpx.WriteJson(w, http.StatusInternalServerError, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// QueryRefund 查询退款状态
func (h *OrderGoZeroHandler) QueryRefund(w http.ResponseWriter, r *http.Request) {
	refundNo := r.URL.Query().Get("refund_no")
	if refundNo == "" {
		httpx.WriteJson(w, http.StatusBadRequest, types.NewErrorResponse(types.CodeInvalidParams, "退款单号不能为空"))
		return
	}

	resp, err := h.orderService.QueryWechatRefund(r.Context(), refundNo)
	if err != nil {
		logx.Errorf("查询退款失败: %v", err)
		httpx.WriteJson(w, http.StatusInternalServerError, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// ClosePaymentOrder 关闭支付订单
func (h *OrderGoZeroHandler) ClosePaymentOrder(w http.ResponseWriter, r *http.Request) {
	var req struct {
		OrderNo string `json:"order_no" validate:"required"`
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusBadRequest, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	err := h.orderService.CloseWechatPayOrder(r.Context(), req.OrderNo)
	if err != nil {
		logx.Errorf("关闭支付订单失败: %v", err)
		httpx.WriteJson(w, http.StatusInternalServerError, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse("支付订单已关闭"))
}

// GetRefundButtonStatus 获取订单退款按钮状态
func (h *OrderGoZeroHandler) GetRefundButtonStatus(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID（暂时不用，但保留权限检查）
	_, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 获取订单ID
	orderIDStr := r.URL.Query().Get("order_id")
	if orderIDStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID格式错误"))
		return
	}

	// 调用服务获取退款按钮状态
	buttonStatus, err := h.orderService.GetOrderRefundButtonStatus(r.Context(), uint(orderID))
	if err != nil {
		logx.Errorf("获取退款按钮状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(buttonStatus))
}

// GetOrderRefundStatus 查询订单退款状态
func (h *OrderGoZeroHandler) GetOrderRefundStatus(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 从URL路径中获取订单ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderIDStr := parts[len(parts)-2] // 因为路径是 /orders/:id/refund-status，所以ID在倒数第二个位置
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil || orderID <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	// 调用服务查询退款状态
	refundStatus, err := h.orderService.GetOrderRefundStatus(r.Context(), userID, orderIDStr)
	if err != nil {
		logx.Errorf("查询订单退款状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(refundStatus))
}
