package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"yekaitai/pkg/adapters/abcyun"
	patientModel "yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 患者相关请求结构体
// PatientListRequest 获取患者分页列表请求
type PatientListRequest struct {
	Date   string `form:"date"`                      // 日期，格式yyyy-MM-dd，必填
	Limit  int    `form:"limit,optional,default=20"` // 每页显示条数，默认20
	Offset int    `form:"offset,optional,default=0"` // 分页起始下标，默认0
}

// PatientQueryRequest 查询患者请求
type PatientQueryRequest struct {
	Mobile string `form:"mobile,optional"` // 手机号，可选
	Name   string `form:"name,optional"`   // 姓名，可选
	SN     string `form:"sn,optional"`     // 档案号，可选
}

// PatientDetailRequest 患者详情请求
type PatientDetailRequest struct {
	ID string `path:"id"` // 患者ID
}

// PatientAttachmentListRequest 患者附件列表请求
type PatientAttachmentListRequest struct {
	PatientID string `path:"patientId"`                 // 患者ID
	Limit     int    `form:"limit,optional,default=10"` // 每页显示条数，默认10
	Offset    int    `form:"offset,optional,default=0"` // 分页起始下标，默认0
}

// PatientAttachmentRequest 患者附件请求
type PatientAttachmentRequest struct {
	PatientID    string `path:"patientId"`    // 患者ID
	AttachmentID string `path:"attachmentId"` // 附件ID
}

// PatientFamilyMemberRequest 患者家庭成员请求
type PatientFamilyMemberRequest struct {
	PatientID string `path:"patientId"` // 患者ID
}

// MemberCardPayRequest 会员卡支付请求
type MemberCardPayRequest struct {
	MemberCardID         string  `path:"memberCardId"`         // 会员卡ID
	TransactionPatientID string  `json:"transactionPatientId"` // 交易患者ID
	Amount               float64 `json:"amount"`               // 金额
	BusinessID           string  `json:"businessId"`           // 业务ID
	Password             string  `json:"password"`             // 密码
	OperatorID           string  `json:"operatorId"`           // 操作员ID
}

// MemberCardRefundRequest 会员卡退款请求
type MemberCardRefundRequest struct {
	MemberCardID   string   `path:"memberCardId"`   // 会员卡ID
	Amount         float64  `json:"amount"`         // 金额
	TransactionIDs []string `json:"transactionIds"` // 交易ID列表
	BusinessID     string   `json:"businessId"`     // 业务ID
	OperatorID     string   `json:"operatorId"`     // 操作员ID
}

// CreatePatientRequest 创建患者请求
type CreatePatientRequest struct {
	Name         string                `json:"name"`         // 姓名
	Mobile       string                `json:"mobile"`       // 手机号
	Sex          string                `json:"sex"`          // 性别
	Birthday     string                `json:"birthday"`     // 生日
	SourceID     string                `json:"sourceId"`     // 来源ID
	SourceFromID string                `json:"sourceFromId"` // 来源FromID
	IDCard       string                `json:"idCard"`       // 身份证号
	PastHistory  string                `json:"pastHistory"`  // 既往病史
	Address      abcyun.PatientAddress `json:"address"`      // 地址
	SN           string                `json:"sn"`           // 档案号
	Remark       string                `json:"remark"`       // 备注
	Profession   string                `json:"profession"`   // 职业
	Company      string                `json:"company"`      // 公司
	Marital      string                `json:"marital"`      // 婚姻状况
	Weight       string                `json:"weight"`       // 体重
	// 新增本地患者ID参数
	LocalPatientID uint                `json:"localPatientId,optional" form:"localPatientId,optional"` // 本地患者ID
}

// UpdatePatientRequest 更新患者请求
type UpdatePatientRequest struct {
	ID           string                `path:"id"`           // 患者ID
	Name         string                `json:"name"`         // 姓名
	Mobile       string                `json:"mobile"`       // 手机号
	Sex          string                `json:"sex"`          // 性别
	Birthday     string                `json:"birthday"`     // 生日
	SourceID     string                `json:"sourceId"`     // 来源ID
	SourceFromID string                `json:"sourceFromId"` // 来源FromID
	IDCard       string                `json:"idCard"`       // 身份证号
	PastHistory  string                `json:"pastHistory"`  // 既往病史
	Address      abcyun.PatientAddress `json:"address"`      // 地址
	SN           string                `json:"sn"`           // 档案号
	Remark       string                `json:"remark"`       // 备注
	Profession   string                `json:"profession"`   // 职业
	Company      string                `json:"company"`      // 公司
	Marital      string                `json:"marital"`      // 婚姻状况
	Weight       string                `json:"weight"`       // 体重
}

// AddPatientAttachmentsRequest 添加患者附件请求
type AddPatientAttachmentsRequest struct {
	PatientID   string                               `path:"patientId"`   // 患者ID
	Attachments []abcyun.AddPatientAttachmentRequest `json:"attachments"` // 附件列表
}

// 4.1.1. 获取患者分页列表
func AbcYunPatientListHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientListRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.Date == "" {
		httpx.Error(w, errors.New("缺少日期参数"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/patient"
	queryParams := map[string]string{
		"date":   req.Date,
		"limit":  fmt.Sprintf("%d", req.Limit),
		"offset": fmt.Sprintf("%d", req.Offset),
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者分页列表成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 4.1.2. 查询患者
func AbcYunPatientQueryHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientQueryRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 至少需要一个查询参数
	if req.Mobile == "" && req.Name == "" && req.SN == "" {
		httpx.Error(w, errors.New("至少需要提供一个查询参数（手机号、姓名或档案号）"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/patient/query"
	queryParams := map[string]string{}

	if req.Mobile != "" {
		queryParams["mobile"] = req.Mobile
	}
	if req.Name != "" {
		queryParams["name"] = req.Name
	}
	if req.SN != "" {
		queryParams["sn"] = req.SN
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询患者成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 4.1.3. 创建患者
func AbcYunCreatePatientHandler(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[ABC云创建患者] 开始处理请求: %s %s", r.Method, r.URL.String())

	client := abcyun.GetGlobalClient()
	if client == nil {
		errMsg := "[ABC云创建患者] ABC云客户端未初始化"
		logx.Errorf(errMsg)
		httpx.Error(w, errors.New("ABC云客户端未初始化，请联系系统管理员"))
		return
	}

	var req CreatePatientRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		errMsg := fmt.Sprintf("[ABC云创建患者] 请求参数解析失败: %v", err)
		logx.Errorf(errMsg)
		httpx.Error(w, fmt.Errorf("请求参数解析失败: %v", err))
		return
	}

	// 记录请求参数
	reqJSON, _ := json.Marshal(req)
	logx.Infof("[ABC云创建患者] 请求参数: %s", string(reqJSON))

	// 验证必填参数
	if req.Name == "" {
		errMsg := "[ABC云创建患者] 参数验证失败: 缺少姓名参数"
		logx.Errorf(errMsg)
		httpx.Error(w, errors.New("缺少姓名参数"))
		return
	}
	if req.Mobile == "" {
		errMsg := "[ABC云创建患者] 参数验证失败: 缺少手机号参数"
		logx.Errorf(errMsg)
		httpx.Error(w, errors.New("缺少手机号参数"))
		return
	}

	// 验证本地患者ID（如果提供）
	var localPatient *patientModel.WxPatient
	if req.LocalPatientID > 0 {
		patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())
		localPatient, err = patientRepo.FindByID(req.LocalPatientID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				errMsg := fmt.Sprintf("[ABC云创建患者] 本地患者ID %d 不存在", req.LocalPatientID)
				logx.Errorf(errMsg)
				httpx.Error(w, errors.New("指定的本地患者不存在"))
			} else {
				errMsg := fmt.Sprintf("[ABC云创建患者] 查询本地患者失败: %v", err)
				logx.Errorf(errMsg)
				httpx.Error(w, fmt.Errorf("查询本地患者失败: %v", err))
			}
			return
		}
		logx.Infof("[ABC云创建患者] 关联本地患者: ID=%d, 姓名=%s", localPatient.PatientID, localPatient.Name)
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/patient"
	requestData := map[string]interface{}{
		"name":         req.Name,
		"mobile":       req.Mobile,
		"sex":          req.Sex,
		"birthday":     req.Birthday,
		"sourceId":     req.SourceID,
		"sourceFromId": req.SourceFromID,
		"idCard":       req.IDCard,
		"pastHistory":  req.PastHistory,
		"address":      req.Address,
		"sn":           req.SN,
		"remark":       req.Remark,
		"profession":   req.Profession,
		"company":      req.Company,
		"marital":      req.Marital,
		"weight":       req.Weight,
	}

	respBody, err := client.Post(path, requestData)
	if err != nil {
		// 详细记录错误信息
		errMsg := fmt.Sprintf("[ABC云创建患者] 创建患者失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			httpx.Error(w, fmt.Errorf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			httpx.Error(w, fmt.Errorf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			httpx.Error(w, fmt.Errorf("请求超时，请稍后重试: %v", err))
		} else {
			httpx.Error(w, fmt.Errorf("创建患者失败: %v", err))
		}
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		errMsg := fmt.Sprintf("[ABC云创建患者] 解析响应失败: %v", err)
		logx.Errorf(errMsg)
		httpx.Error(w, fmt.Errorf("解析响应失败: %v", err))
		return
	}

	if result.Code != 0 {
		errMsg := fmt.Sprintf("[ABC云创建患者] API返回错误: %s", result.Message)
		logx.Errorf(errMsg)
		httpx.Error(w, errors.New(result.Message))
		return
	}

	// 记录成功结果
	logx.Infof("[ABC云创建患者] 创建成功")

	// 如果提供了本地患者ID，更新本地患者的ABC云患者ID
		var abcyunPatientID string
	var responseData interface{} = result.Data

	// 尝试从响应中提取患者ID
		if dataMap, ok := result.Data.(map[string]interface{}); ok {
		if id, exists := dataMap["id"]; exists {
				abcyunPatientID = fmt.Sprintf("%v", id)
			}
		}

	// 更新本地患者表
	if localPatient != nil && abcyunPatientID != "" {
			// 更新本地患者的ABC云患者ID
			localPatient.AbcyunPatientID = abcyunPatientID

			err = mysql.Master().Save(localPatient).Error
			if err != nil {
				logx.Errorf("[ABC云创建患者] 更新本地患者ABC云ID失败: %v", err)
				// 不影响主流程，只记录错误
			} else {
				logx.Infof("[ABC云创建患者] 成功更新本地患者ABC云ID: 本地ID=%d, ABC云ID=%s", localPatient.PatientID, abcyunPatientID)
			}

		// 在响应中添加本地患者ID
			responseData = map[string]interface{}{
			"patientInfo":    result.Data,
				"localPatientId": req.LocalPatientID,
		}
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "创建患者成功",
		"data":    responseData,
	}
	httpx.OkJson(w, response)
}

// 4.1.4. 获取患者详情
func AbcYunPatientDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientDetailRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.ID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s", req.ID)
	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者详情成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 4.1.5. 更新患者
func AbcYunUpdatePatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req UpdatePatientRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.ID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s", req.ID)
	requestData := map[string]interface{}{
		"name":         req.Name,
		"mobile":       req.Mobile,
		"sex":          req.Sex,
		"birthday":     req.Birthday,
		"sourceId":     req.SourceID,
		"sourceFromId": req.SourceFromID,
		"idCard":       req.IDCard,
		"pastHistory":  req.PastHistory,
		"address":      req.Address,
		"sn":           req.SN,
		"remark":       req.Remark,
		"profession":   req.Profession,
		"company":      req.Company,
		"marital":      req.Marital,
		"weight":       req.Weight,
	}

	requestBytes, err := json.Marshal(requestData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	respBody, err := client.Put(path, requestBytes)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "更新患者成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 4.1.6. 获取患者附件列表
func AbcYunPatientAttachmentListHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientAttachmentListRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s/attachment", req.PatientID)
	queryParams := map[string]string{
		"limit":  fmt.Sprintf("%d", req.Limit),
		"offset": fmt.Sprintf("%d", req.Offset),
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者附件列表成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 4.1.7. 获取患者附件
func AbcYunPatientAttachmentHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientAttachmentRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}
	if req.AttachmentID == "" {
		httpx.Error(w, errors.New("缺少附件ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s/attachment/%s", req.PatientID, req.AttachmentID)
	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者附件成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 4.1.8. 添加患者附件
func AbcYunAddPatientAttachmentsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req AddPatientAttachmentsRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s/attachment", req.PatientID)
	requestData := map[string]interface{}{
		"attachments": req.Attachments,
	}

	respBody, err := client.Post(path, requestData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "添加患者附件成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 4.1.9. 获取患者家庭成员
func AbcYunPatientFamilyMemberHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientFamilyMemberRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s/family-member", req.PatientID)
	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者家庭成员成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}
