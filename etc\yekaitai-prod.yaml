Name: yekaitai  # 应用名称

# 共享日志配置 (使用go-zero标准参数)
x-log-config: &log-config
  Mode: file                    # 文件模式
  Level: warn                   # 生产环境只记录警告和错误
  Path: "${LOG_PATH:-logs}"     # 日志目录，优先使用环境变量LOG_PATH，默认为logs
  Encoding: json                # JSON格式
  Compress: false               # 不压缩日志
  KeepDays: 30                  # 生产环境保留30天
  Rotation: daily               # 按天轮转
  MaxSize: 0                    # 不限制文件大小
  MaxBackups: 0                 # 不限制备份文件数量
  Stat: false                   # 生产环境关闭统计日志
  StackCooldownMillis: 100      # 堆栈冷却时间

# 小程序服务配置
Mini:
  Name: yekaitai-mini
  Host: 0.0.0.0
  Port: 8888
  Timeout: 60000  # 设置为合理的超时时间：60秒
  Log: *log-config  # 引用统一日志配置

# 后台管理服务配置
Backend:
  Name: yekaitai-admin
  Host: 0.0.0.0
  Port: 8889
  Log: *log-config  # 引用统一日志配置

# 以下是共享配置
RestConf:
  Timeout: 10000
  ServiceName: yekaitai

# 全局日志配置 (统一使用go-zero的logx)
LogConf:
  ServiceName: yekaitai
  <<: *log-config  # 引用统一日志配置

# 数据库配置
Mysql:
  Master:
    Addr: yekaitai:wnbBB7sYSwfY+8xu@tcp(*************:3306)/yekaitai?charset=utf8mb4&parseTime=True&loc=Local
    MaxIdleConns: 20
    MaxOpenConns: 200
    ConnMaxLifetime: 3600
  Slave:
    Addr: yekaitai:wnbBB7sYSwfY+8xu@tcp(*************:3306)/yekaitai?charset=utf8mb4&parseTime=True&loc=Local
    MaxIdleConns: 20
    MaxOpenConns: 200
    ConnMaxLifetime: 3600

# Redis配置
Redis:
  Addr: ************:6379
  Password: "cKxc6BoR0aNyJYAP"
  DB: 1
  PoolSize: 200

# JWT配置
JWT:
  AccessSecret: "yekaitai_prod_access_secret"
  AccessExpire: 86400     # 24小时
  RefreshSecret: "yekaitai_prod_refresh_secret"
  RefreshExpire: 604800   # 7天
  RefreshTokenKey: "refresh_token:"
  BlacklistPrefKey: "jwt_blacklist:"

  # 管理系统专用密钥
  AdminAccessSecret: "admin_prod_xxxxxxxx"
  AdminAccessExpire: 86400
  AdminRefreshSecret: "admin_refresh_prod_xxxxxxxx"
  AdminRefreshExpire: 604800

  # 小程序专用密钥
  WxAccessSecret: "wx-access-secret-key-prod"
  WxAccessExpire: 7200000     # 2000小时
  WxRefreshSecret: "wx-refresh-secret-key-prod"
  WxRefreshExpire: 6048000  # 70天

# 微信小程序配置 生产环境
Wechat:
  AppID: "wx7131ff9883c5e05a"      # 小程序AppID
  AppSecret: "1c9b7cdb8ce2697e190d1029bfd3e313"  # 小程序AppSecret

AbcYun:
  BaseURL: https://open-region2.abcyun.cn
  AppID: "3815942476742656000"
  AppSecret: "7afd38ebd88dd437894a765a8d85943c"
  ClinicID: ""  # 武汉叶开泰中医诊所永清街店

Medlinker:
  BaseURL: https://medgptv2-tob.medlinker.com
  AppID: 0dcd6090978f408ba4ba3c6f287dd69f
  AppSecret: 6ef3e87e65214faea344fbb05230876c
  ModelID: 400
  Phone: "13800138000"  # 添加默认医联认证电话号码
  DailyCallLimit: 10000  # 每日调用次数限制，0表示无限制

# Kafka配置（可选，用于医疗聊天流数据处理）
# 如果不需要医疗AI聊天功能，可以注释掉以下配置
kafka:
  brokers:
    - 10.10.107.129:9092        # 直接使用9092端口（无认证）
  topic: medical_chat_stream    # 确保与运维确认的Topic名称一致
  group: consultation_group
  # 移除所有认证相关配置 - 根据运维反馈无需认证
  enableTLS: false              # 禁用TLS/SSL
  autoCreateTopic: true         # 启用自动创建Topic
  username: "ckafka-k9m5dm9x#yekaitai"
  password: "nAPwzeXE3PtX5t"

# 聚水潭配置
Jushuitan:
  BaseURL: https://openapi.jushuitan.com
  AppKey: 5a93e2857ea94297bbf3e3305bb0eeb2
  AppSecret: abaf6bde64ae4dcfb520247af1277b9c
  AccessToken: 784c443add144e40871ba0eb011bb600  # 企业版
  IsEnterprise: true  # 默认使用企业版

# 杭州HIS系统配置
HangzhouHIS:
  BaseURL: https://4djjb6138395.vicp.fun  # 需替换为实际的HIS系统地址
  Port: 443          # 需替换为实际的端口
  ClientSecret: system
  UserName: "yktjt"        # His提供的用户账号(yhzh)
  Password: "eb141b9f505fb48f338606d174e8ec54"        # His提供的用户密码(yhmm)
  SyncEnabled: true        # 是否启用卫生机构同步功能
  SyncInterval: 1          # 同步间隔（小时），默认1小时
  CurrentJsdj: "5"     # His提供的参数
  CurrentJtid: "0"     # His提供的参数
  CurrentWsjgid: "5424"   # His提供的参数
  CurrentYhjsid: "4480"   # His提供的参数

# 七牛云对象存储配置
Upload:
  Qiniu:
    AccessKey: "-BftVKiSPGF4Aznszuynn8NqTlx5xzYEr9hNIRUP"
    SecretKey: "dEBEjShOwfqJYPSnn4jocunXwkU2oCsMOZIO1Ck0"
    Bucket: "pub-med-yekaitai"
    Domain: "pub-med-yekaitai.medlinker.com"
    Zone: "z2"  # 华南-广东对应的区域代码为z2
  Config:
    MaxImageSize: 10485760     # 10MB
    MaxDocumentSize: 52428800  # 50MB
    MaxVideoSize: 1610612736   # 1.5GB
    MaxGeneralSize: 104857600  # 100MB
    AllowedDomains:
      - "*"

tencentMap:
  key: "OAOBZ-4UUNB-HAEUL-NJRRR-52B43-MGBRL"  # 腾讯地图API密钥，后面需要替换TODO

cron:
  enable: true  # 是否启用定时任务
  storeGeoSync:
    enable: true  # 是否启用门店地理编码同步
    cron: "0 0 3 * * ?"  # 每天凌晨3点执行
    batchSize: 100  # 每批处理数量

wanliniu:
  BaseURL: https://open-api.hupun.com/api
  AppKey: "T3068632154"
  AppSecret: "34f6c67bb800d97295d5e919cc056363"
  shop_nick: "叶小医"
  shop_type: 100

# 微信支付配置（生产环境）
wechatPay:
  appID: "wx7131ff9883c5e05a"      # 小程序AppID（与微信小程序配置保持一致）
  appSecret: "1c9b7cdb8ce2697e190d1029bfd3e313"  # 小程序AppSecret（与微信小程序配置保持一致）
  mchID: "1508380841"             # 微信支付商户号
  apiV2Key: "9949583C3210BB76B8E53AC37259F20F"  # APIv2密钥（用于部分旧版接口，如退款查询等）
  apiV3Key: "9949583C3210BB76B8E53AC37259F20F"  # APIv3密钥（用于新版支付接口，如统一下单、查询订单等）
  certificate: |                  # 微信支付平台证书（用于验证微信支付回调请求的签名）
    -----BEGIN CERTIFICATE-----
    MIIEFDCCAvygAwIBAgIUe4+C2FVKvQLPi4o5hVltvvFFQ2gwDQYJKoZIhvcNAQEL
    BQAwXjELMAkGA1UEBhMCQ04xEzARBgNVBAoTClRlbnBheS5jb20xHTAbBgNVBAsT
    FFRlbnBheS5jb20gQ0EgQ2VudGVyMRswGQYDVQQDExJUZW5wYXkuY29tIFJvb3Qg
    Q0EwHhcNMjUwNjExMDcxMTAyWhcNMzAwNjEwMDcxMTAyWjBuMRgwFgYDVQQDDA9U
    ZW5wYXkuY29tIHNpZ24xEzARBgNVBAoMClRlbnBheS5jb20xHTAbBgNVBAsMFFRl
    bnBheS5jb20gQ0EgQ2VudGVyMQswCQYDVQQGEwJDTjERMA8GA1UEBwwIU2hlblpo
    ZW4wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDPeefxdwFFt6nOr9V1
    7t+W+CuU4rDva4FXzhEB9yWH7cud6Qgr6OreGST/xyv3eD16jC1x1OEXVLq8+Uaw
    EIvOn4Cxmde0/86eizxeRbID+5wGhIPpJYwBQALleOO3ADlUFa5Z9rh4nMdCBiZ1
    73hBvYDWfdsvNurorUBgNc4k8u6FzAiQiqKpISCThCilzR1kAEZIkPiw00St/6FF
    DYeGdnAaBt3+tsmLTY1qZVv9D/nk4crNzT27hCweYZ+L7dLztaGM/j1enN06BB0D
    BvOXrZmMkTQ6ogX3AZq+jI3L3OIhqJ8wE0r8vZWhA8A68DldPVEDdoYP3mE3A92A
    fcunAgMBAAGjgbkwgbYwCQYDVR0TBAIwADALBgNVHQ8EBAMCA/gwgZsGA1UdHwSB
    kzCBkDCBjaCBiqCBh4aBhGh0dHA6Ly9ldmNhLml0cnVzLmNvbS5jbi9wdWJsaWMv
    aXRydXNjcmw/Q0E9MUJENDIyMEU1MERCQzA0QjA2QUQzOTc1NDk4NDZDMDFDM0U4
    RUJEMiZzZz1IQUNDNDcxQjY1NDIyRTEyQjI3QTlEMzNBODdBRDFDREY1OTI2RTE0
    MDM3MTANBgkqhkiG9w0BAQsFAAOCAQEAnO1mZYXcv5eXIJrSvBzNOaNv84VwliHq
    egUsQ0gRhWuLckYsFTX2L8m/7NOaRWrkArpg/OTBy54kABXsakYJ6CMcmHSsvut/
    igtcGL8i8MBjtxBpL/lgUzOEC8a0gqaM7w2r3Lg9LcugfgIZtbX2vftqX+yJyE2p
    weNVHLOb7f6s9LEPNgJ+/WYYRUB5Go28mgbrXlD8w+sDgN8Rs07HN/qtbAI/vhPr
    IWh3EHPqfCOfKwO3BWz3TUB6vy1+Ez91ia3yGqmZoL9zKaLCneCMlsp6k26PVxms
    TxBIwjTCX0Pz8i8zA4kNoV/nN0CPK6qjV1u3X+365YfCszX3PGE4jg==
    -----END CERTIFICATE-----
  serialNo: "2D4A4170B4EA887FF255EEBD7660441283488736"  # 商户API证书序列号（用于标识商户证书）
  notifyURL: "https://ykt-api.whykt.com/yekaitai-mini-api/api/wx/wechat/pay/notify"  # 支付结果通知回调URL（生产环境域名）
  refundNotifyURL: "https://ykt-api.whykt.com/yekaitai-mini-api/api/wx/wechat/refund/notify"  # 退款结果通知回调URL（生产环境域名）
  privateKey: |                   # 商户API私钥（用于请求签名，从apiclient_key.pem文件获取）
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# 腾讯云短信配置
TencentSms:
  SecretId: "AKIDAD8732zxnfcTuDJEzZxMdmlYClCi6JME"     # 腾讯云API密钥ID
  SecretKey: "jJjy8FNODsMgnslRyISTIzZxBF9sRwIV"        # 腾讯云API密钥Key
  SdkAppId: "1400991359"                              # 短信应用ID
  SignName: "武汉叶开泰药业"                            # 短信签名
  TemplateId: "2452756"                               # 短信模板ID
  Region: "ap-guangzhou"                              # 地域参数
  # 短信模板内容：您的验证码为：{1}，请勿泄露给他人，如非本人操作，请忽略本短信！
  # 注意：App Key (214e7a8182b967717e286795c4427755) 在新版腾讯云短信API中不需要使用

# 邮件配置
Email:
  Host: "smtp.163.com"
  Username: "<EMAIL>"
  Password: "qwe123"
  EmailPort: 465
  RecipientEmail: "<EMAIL>"