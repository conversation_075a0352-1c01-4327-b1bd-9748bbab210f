package kafka

// Producer Kafka消息生产者接口
type Producer interface {
	// Send 发送消息到指定主题
	Send(topic string, message []byte) error
}

// MockProducer 模拟的Kafka生产者，用于测试或开发环境
type MockProducer struct{}

// NewMockProducer 创建模拟Kafka生产者
func NewMockProducer() Producer {
	return &MockProducer{}
}

// Send 发送消息（模拟实现）
func (p *MockProducer) Send(topic string, message []byte) error {
	// 在实际项目中，这里应该实现真正的Kafka消息发送
	// 目前仅返回成功，用于模拟场景
	return nil
}
