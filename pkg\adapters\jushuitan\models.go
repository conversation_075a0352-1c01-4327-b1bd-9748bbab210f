package jushuitan

import (
	"time"

	"gorm.io/gorm"
)

// 以下是基础信息相关的数据模型

// ShopQueryRequest 店铺查询请求
type ShopQueryRequest struct {
	PageIndex int   `json:"page_index,omitempty"` // 第几页，默认第一页
	PageSize  int   `json:"page_size,omitempty"`  // 每页多少条；默认100条，最大100条
	ShopIDs   []int `json:"shop_ids,omitempty"`   // 店铺编码
}

// ShopQueryResponse 店铺查询响应
type ShopQueryResponse struct {
	PageIndex int        `json:"page_index"` // 第几页
	PageSize  int        `json:"page_size"`  // 每页多少条
	HasNext   bool       `json:"has_next"`   // 是否有下一页
	Datas     []ShopInfo `json:"datas"`      // 店铺列表
}

// ShopInfo 店铺信息
type ShopInfo struct {
	ShopID         int    `json:"shop_id"`         // 店铺编号
	ShopName       string `json:"shop_name"`       // 店铺名称
	CoID           int    `json:"co_id"`           // 公司编号
	ShopSite       string `json:"shop_site"`       // 店铺站点
	ShopURL        string `json:"shop_url"`        // 店铺网址
	Created        string `json:"created"`         // 创建时间
	Nick           string `json:"nick"`            // 主账号
	SessionExpired string `json:"session_expired"` // 授权过期时间
	SessionUID     string `json:"session_uid"`     // 会话用户编号
	ShortName      string `json:"short_name"`      // 店铺简称
	GroupID        int    `json:"group_id"`        // 分组id
	GroupName      string `json:"group_name"`      // 分组名称
}

// ShopInfoDB 店铺信息数据库模型
type ShopInfoDB struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ShopID         int       `gorm:"column:shop_id;index;comment:店铺编号" json:"shop_id"`             // 店铺编号
	ShopName       string    `gorm:"column:shop_name;comment:店铺名称" json:"shop_name"`               // 店铺名称
	CoID           int       `gorm:"column:co_id;comment:公司编号" json:"co_id"`                       // 公司编号
	ShopSite       string    `gorm:"column:shop_site;comment:店铺站点" json:"shop_site"`               // 店铺站点
	ShopURL        string    `gorm:"column:shop_url;comment:店铺网址" json:"shop_url"`                 // 店铺网址
	Created        string    `gorm:"column:created;comment:创建时间" json:"created"`                   // 创建时间
	Nick           string    `gorm:"column:nick;comment:主账号" json:"nick"`                          // 主账号
	SessionExpired string    `gorm:"column:session_expired;comment:授权过期时间" json:"session_expired"` // 授权过期时间
	SessionUID     string    `gorm:"column:session_uid;comment:会话用户编号" json:"session_uid"`         // 会话用户编号
	ShortName      string    `gorm:"column:short_name;comment:店铺简称" json:"short_name"`             // 店铺简称
	GroupID        int       `gorm:"column:group_id;comment:分组id" json:"group_id"`                 // 分组id
	GroupName      string    `gorm:"column:group_name;comment:分组名称" json:"group_name"`             // 分组名称
	CreatedAt      time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`           // 记录创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`           // 记录更新时间
}

// TableName 指定表名
func (ShopInfoDB) TableName() string {
	return "jst_shop_info"
}

// LogisticsCompanyQueryRequest 物流公司查询请求
type LogisticsCompanyQueryRequest struct {
	PageIndex     int    `json:"page_index,omitempty"`     // 第几页，默认第一页开始
	PageSize      int    `json:"page_size,omitempty"`      // 每页多少条，默认30条，最大50条
	ModifiedBegin string `json:"modified_begin,omitempty"` // 修改起始时间
	ModifiedEnd   string `json:"modified_end,omitempty"`   // 修改结束时间
	WmsCoID       int    `json:"wms_co_id,omitempty"`      // 公司编号
}

// LogisticsCompanyQueryResponse 物流公司查询响应
type LogisticsCompanyQueryResponse struct {
	PageIndex int                `json:"page_index"` // 第几页
	PageSize  int                `json:"page_size"`  // 每页多少条
	HasNext   bool               `json:"has_next"`   // 是否有下一页
	DataCount int                `json:"data_count"` // 总条数
	PageCount int                `json:"page_count"` // 总页数
	Datas     []LogisticsCompany `json:"datas"`      // 物流公司列表
}

// LogisticsCompany 物流公司信息
type LogisticsCompany struct {
	LcID     string `json:"lc_id"`    // 快递公司编码
	LcName   string `json:"lc_name"`  // 快递公司
	Modified string `json:"modified"` // 修改时间
}

// LogisticsCompanyDB 物流公司数据库模型
type LogisticsCompanyDB struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	LcID      string    `gorm:"column:lc_id;index;comment:快递公司编码" json:"lc_id"`     // 快递公司编码
	LcName    string    `gorm:"column:lc_name;comment:快递公司" json:"lc_name"`         // 快递公司
	Modified  string    `gorm:"column:modified;comment:修改时间" json:"modified"`       // 修改时间
	CreatedAt time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"` // 记录创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"` // 记录更新时间
}

// TableName 指定表名
func (LogisticsCompanyDB) TableName() string {
	return "jst_logistics_company"
}

// WarehouseQueryRequest 仓库查询请求
type WarehouseQueryRequest struct {
	PageIndex int `json:"page_index,omitempty"` // 第几页，默认第一页开始
	PageSize  int `json:"page_size,omitempty"`  // 每页多少条，非必填项，默认30条
}

// WarehouseQueryResponse 仓库查询响应
type WarehouseQueryResponse struct {
	PageIndex int         `json:"page_index"` // 第几页
	PageSize  int         `json:"page_size"`  // 每页多少条
	HasNext   bool        `json:"has_next"`   // 是否有下一页
	DataCount int         `json:"data_count"` // 总条数
	PageCount int         `json:"page_count"` // 总页数
	Datas     []Warehouse `json:"datas"`      // 仓库列表
}

// Warehouse 仓库信息
type Warehouse struct {
	Name    string `json:"name"`      // 分仓名称
	CoID    int    `json:"co_id"`     // 主仓公司编号
	WmsCoID int    `json:"wms_co_id"` // 分仓编号
	IsMain  bool   `json:"is_main"`   // 是否为主仓，true=主仓
	Status  string `json:"status"`    // 状态
	Remark1 string `json:"remark1"`   // 对方备注
	Remark2 string `json:"remark2"`   // 我方备注
}

// WarehouseDB 仓库信息数据库模型
type WarehouseDB struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name      string    `gorm:"column:name;comment:分仓名称" json:"name"`                 // 分仓名称
	CoID      int       `gorm:"column:co_id;comment:主仓公司编号" json:"co_id"`             // 主仓公司编号
	WmsCoID   int       `gorm:"column:wms_co_id;index;comment:分仓编号" json:"wms_co_id"` // 分仓编号
	IsMain    bool      `gorm:"column:is_main;comment:是否为主仓" json:"is_main"`          // 是否为主仓，true=主仓
	Status    string    `gorm:"column:status;comment:状态" json:"status"`               // 状态
	Remark1   string    `gorm:"column:remark1;comment:对方备注" json:"remark1"`           // 对方备注
	Remark2   string    `gorm:"column:remark2;comment:我方备注" json:"remark2"`           // 我方备注
	CreatedAt time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`   // 记录创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`   // 记录更新时间
}

// TableName 指定表名
func (WarehouseDB) TableName() string {
	return "jst_warehouse"
}

// UserQueryRequest 用户查询请求
type UserQueryRequest struct {
	CurrentPage  int    `json:"current_page"`            // 当前页
	PageSize     int    `json:"page_size"`               // 每页数
	PageAction   int    `json:"page_action,omitempty"`   // 查询类型
	Enabled      *bool  `json:"enabled,omitempty"`       // 用户状态
	Version      int    `json:"version,omitempty"`       // 版本号
	LoginID      string `json:"loginId,omitempty"`       // 查询的登录账号
	CreatedBegin string `json:"created_begin,omitempty"` // 创建开始时间
	CreatedEnd   string `json:"created_end,omitempty"`   // 创建结束时间
}

// UserQueryResponse 用户查询响应
type UserQueryResponse struct {
	CurrentPage int        `json:"current_page"` // 当前页
	PageSize    int        `json:"page_size"`    // 每页数
	Count       int        `json:"count"`        // 总条数
	Pages       int        `json:"pages"`        // 总页数
	Datas       []UserInfo `json:"datas"`        // 用户数据
}

// UserInfo 用户信息
// type UserInfo struct {
// 	UID         int    `json:"u_id"`     // 用户编码
// 	Name        string `json:"name"`     // 用户名称
// 	Enabled     bool   `json:"enabled"`  // 状态
// 	Created     string `json:"created"`  // 创建时间
// 	Modified    string `json:"modified"` // 修改时间
// 	UgNames     string `json:"ug_names"` // 所属分组名称，已转为string类型
// 	UgIDs       string `json:"ug_ids,omitempty"`
// 	Roles       string `json:"roles,omitempty"`
// 	RoleIDs     string `json:"roleIds,omitempty"`
// 	PwdModified string `json:"pwd_modified,omitempty"`
// 	Remark      string `json:"remark,omitempty"`
// 	EmpID       string `json:"empId,omitempty"`
// 	Creator     string `json:"creator,omitempty"`
// 	Modifier    string `json:"modifier,omitempty"`
// }

// UserInfoDB 用户信息数据库模型
type UserInfo struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UID         int       `gorm:"column:u_id;index;comment:用户编码" json:"u_id"`             // 用户编码
	Name        string    `gorm:"column:name;comment:用户名称" json:"name"`                   // 用户名称
	Enabled     bool      `gorm:"column:enabled;comment:状态" json:"enabled"`               // 状态
	Created     string    `gorm:"column:created;comment:创建时间" json:"created"`             // 创建时间
	Modified    string    `gorm:"column:modified;comment:修改时间" json:"modified"`           // 修改时间
	UgNames     string    `gorm:"column:ug_names;comment:所属分组名称" json:"ug_names"`         // 所属分组名称，已转为string类型
	UgIDs       string    `gorm:"column:ug_ids;comment:分组ID" json:"ug_ids"`               // 分组ID
	Roles       string    `gorm:"column:roles;comment:角色" json:"roles"`                   // 角色
	RoleIDs     string    `gorm:"column:role_ids;comment:角色ID" json:"role_ids"`           // 角色ID
	PwdModified string    `gorm:"column:pwd_modified;comment:密码修改时间" json:"pwd_modified"` // 密码修改时间
	Remark      string    `gorm:"column:remark;comment:备注" json:"remark"`                 // 备注
	EmpID       string    `gorm:"column:emp_id;comment:员工ID" json:"emp_id"`               // 员工ID
	Creator     string    `gorm:"column:creator;comment:创建人" json:"creator"`              // 创建人
	Modifier    string    `gorm:"column:modifier;comment:修改人" json:"modifier"`            // 修改人
	CreatedAt   time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`     // 记录创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`     // 记录更新时间
}

// TableName 指定表名
func (UserInfo) TableName() string {
	return "jst_user_info"
}

// SupplierQueryRequest 供应商查询请求
type SupplierQueryRequest struct {
	Status       int    `json:"status,omitempty"`         // 合作状态
	CoName       string `json:"co_name,omitempty"`        // 供应商名称
	SupplierCoID string `json:"supplier_co_id,omitempty"` // 供应商编号
	PageNum      int    `json:"page_num"`                 // 页数
	PageSize     int    `json:"page_size"`                // 每页数量，最大100
}

// SupplierQueryResponse 供应商查询响应
type SupplierQueryResponse struct {
	Total       int            `json:"total"`        // 总数
	SupplierVos []SupplierInfo `json:"supplier_vos"` // 供应商信息
}

// SupplierInfo 供应商信息
// type SupplierInfo struct {
// 	Status       int    `json:"status"`         // 合作状态
// 	CoName       string `json:"co_name"`        // 供应商公司名
// 	SupplierCoID string `json:"supplier_co_id"` // 供应商编号
// }

// SupplierInfoDB 供应商信息数据库模型
type SupplierInfo struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Status       int       `gorm:"column:status;comment:合作状态" json:"status"`                        // 合作状态
	CoName       string    `gorm:"column:co_name;comment:供应商公司名" json:"co_name"`                    // 供应商公司名
	SupplierCoID string    `gorm:"column:supplier_co_id;index;comment:供应商编号" json:"supplier_co_id"` // 供应商编号
	CreatedAt    time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`              // 记录创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`              // 记录更新时间
}

// TableName 指定表名
func (SupplierInfo) TableName() string {
	return "jst_supplier_info"
}

// DistributorQueryRequest 分销商查询请求
type DistributorQueryRequest struct {
	Status       int    `json:"status,omitempty"`        // 合作状态
	PageNum      int    `json:"page_num"`                // 页数
	PageSize     int    `json:"page_size"`               // 每页数量，最大100
	UpdatedStart string `json:"updated_start,omitempty"` // 修改时间开始
	UpdatedEnd   string `json:"updated_end,omitempty"`   // 修改时间结束
}

// DistributorQueryResponse 分销商查询响应
type DistributorQueryResponse struct {
	Total      int               `json:"total"`       // 总数
	ChannelVos []DistributorInfo `json:"channel_vos"` // 分销商信息
}

// DistributorInfo 分销商信息
// type DistributorInfo struct {
// 	Status               int    `json:"status"`                 // 合作状态
// 	CoName               string `json:"co_name"`                // 分销商商公司名
// 	ChannelCoID          string `json:"channel_co_id"`          // 分销商编号
// 	ApplyTime            string `json:"apply_time"`             // 申请时间
// 	ConfirmTime          string `json:"confirm_time"`           // 合作时间
// 	BillName             string `json:"bill_name"`              // 开单名称
// 	ContactName          string `json:"contact_name"`           // 联系人名称
// 	ContactPhone         string `json:"contact_phone"`          // 联系人手机号
// 	SupplierRemark       string `json:"supplier_remark"`        // 供应商备注
// 	SupplierSalesmanName string `json:"supplier_salesman_name"` // 供应商业务员名称
// 	DisLevel             int    `json:"dis_level"`              // 分销商等级
// 	Updated              string `json:"updated"`                // 修改时间
// }

// DistributorInfoDB 分销商信息数据库模型
type DistributorInfo struct {
	ID              int64          `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	DistributorID   string         `gorm:"column:distributor_id;index;type:varchar(50);not null;comment:分销商ID" json:"distributor_id"` // 分销商ID
	DistributorName string         `gorm:"column:distributor_name;type:varchar(100);not null;comment:分销商名称" json:"distributor_name"`  // 分销商名称
	Nick            string         `gorm:"column:nick;type:varchar(100);comment:分销商昵称" json:"nick"`                                   // 分销商昵称
	MobilePhone     string         `gorm:"column:mobile_phone;type:varchar(20);comment:手机号" json:"mobile_phone"`                      // 手机号
	Phone           string         `gorm:"column:phone;type:varchar(20);comment:电话" json:"phone"`                                     // 电话
	Email           string         `gorm:"column:email;type:varchar(100);comment:邮箱" json:"email"`                                    // 邮箱
	QQ              string         `gorm:"column:qq;type:varchar(20);comment:QQ" json:"qq"`                                           // QQ
	WX              string         `gorm:"column:wx;type:varchar(50);comment:微信" json:"wx"`                                           // 微信
	Status          int            `gorm:"column:status;default:0;comment:状态,0:无效,1:有效" json:"status"`                                // 状态,0:无效,1:有效
	Modified        string         `gorm:"column:modified;type:varchar(50);comment:修改时间" json:"modified"`                             // 修改时间
	CreatedAt       time.Time      `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`                                        // 记录创建时间
	UpdatedAt       time.Time      `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`                                        // 记录更新时间
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;index;comment:记录删除时间" json:"deleted_at"`                                  // 记录删除时间
}

// TableName 指定表名
func (DistributorInfo) TableName() string {
	return "jst_distributor_info_db"
}

// MerchantUserInfoDB 商家用户信息数据库模型
type MerchantUserInfo struct {
	ID           int64          `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID       string         `gorm:"column:user_id;index;type:varchar(50);not null;comment:用户ID" json:"user_id"` // 用户ID
	NickName     string         `gorm:"column:nick_name;type:varchar(100);comment:昵称" json:"nick_name"`             // 昵称
	RealName     string         `gorm:"column:real_name;type:varchar(100);comment:真实姓名" json:"real_name"`           // 真实姓名
	MobilePhone  string         `gorm:"column:mobile_phone;type:varchar(20);comment:手机号" json:"mobile_phone"`       // 手机号
	Email        string         `gorm:"column:email;type:varchar(100);comment:邮箱" json:"email"`                     // 邮箱
	Status       int            `gorm:"column:status;default:0;comment:状态,0:无效,1:有效" json:"status"`                 // 状态,0:无效,1:有效
	Birthday     string         `gorm:"column:birthday;type:varchar(20);comment:生日" json:"birthday"`                // 生日
	Gender       int            `gorm:"column:gender;default:0;comment:性别,0:未知,1:男,2:女" json:"gender"`              // 性别,0:未知,1:男,2:女
	UserLevel    int            `gorm:"column:user_level;default:0;comment:用户等级" json:"user_level"`                 // 用户等级
	Points       int            `gorm:"column:points;default:0;comment:积分" json:"points"`                           // 积分
	RegisterTime string         `gorm:"column:register_time;type:varchar(50);comment:注册时间" json:"register_time"`    // 注册时间
	Modified     string         `gorm:"column:modified;type:varchar(50);comment:修改时间" json:"modified"`              // 修改时间
	CreatedAt    time.Time      `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`                         // 记录创建时间
	UpdatedAt    time.Time      `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`                         // 记录更新时间
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;index;comment:记录删除时间" json:"deleted_at"`                   // 记录删除时间
}

// TableName 指定表名
func (MerchantUserInfo) TableName() string {
	return "jst_merchant_user_info_db"
}

// AfterSaleDB 售后单数据库模型
type AfterSaleDB struct {
	ID               int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AsID             int       `gorm:"column:as_id;index;comment:售后单号" json:"as_id"`                   // 售后单号
	OuterAsID        string    `gorm:"column:outer_as_id;index;comment:外部售后单号" json:"outer_as_id"`     // 外部售后单号
	Type             string    `gorm:"column:type;comment:售后类型" json:"type"`                           // 售后类型，普通退货，其它，拒收退货,仅退款,投诉,补发,维修,换货
	Status           string    `gorm:"column:status;comment:状态" json:"status"`                         // 状态
	Modified         string    `gorm:"column:modified;comment:修改时间" json:"modified"`                   // 修改时间
	ShopID           int       `gorm:"column:shop_id;index;comment:店铺ID" json:"shop_id"`               // 店铺ID
	ShopName         string    `gorm:"column:shop_name;comment:店铺名称" json:"shop_name"`                 // 店铺名称
	ShopStatus       string    `gorm:"column:shop_status;comment:平台状态" json:"shop_status"`             // 平台状态
	Remark           string    `gorm:"column:remark;comment:备注" json:"remark"`                         // 备注
	GoodStatus       string    `gorm:"column:good_status;comment:货物状态" json:"good_status"`             // 货物状态
	QuestionType     string    `gorm:"column:question_type;comment:问题类型" json:"question_type"`         // 问题类型
	Refund           float64   `gorm:"column:refund;comment:卖家应退金额" json:"refund"`                     // 卖家应退金额
	Payment          float64   `gorm:"column:payment;comment:买家应补偿金额" json:"payment"`                  // 买家应补偿金额
	Freight          float64   `gorm:"column:freight;comment:运费" json:"freight"`                       // 运费
	AsDate           string    `gorm:"column:as_date;comment:售后日期" json:"as_date"`                     // 售后日期
	OID              int       `gorm:"column:o_id;index;comment:内部订单号" json:"o_id"`                    // 内部订单号
	SoID             string    `gorm:"column:so_id;index;comment:线上订单号" json:"so_id"`                  // 线上订单号
	LogisticsCompany string    `gorm:"column:logistics_company;comment:物流公司" json:"logistics_company"` // 物流公司
	LID              string    `gorm:"column:l_id;comment:物流单号" json:"l_id"`                           // 物流单号
	ReceiverName     string    `gorm:"column:receiver_name;comment:收货人" json:"receiver_name"`          // 收货人
	ReceiverMobile   string    `gorm:"column:receiver_mobile;comment:手机" json:"receiver_mobile"`       // 手机
	ReceiverAddress  string    `gorm:"column:receiver_address;comment:收货地址" json:"receiver_address"`   // 收货地址
	ReceiverState    string    `gorm:"column:receiver_state;comment:省份" json:"receiver_state"`         // 省份
	ReceiverCity     string    `gorm:"column:receiver_city;comment:城市" json:"receiver_city"`           // 城市
	ReceiverDistrict string    `gorm:"column:receiver_district;comment:区" json:"receiver_district"`    // 区
	WmsCoID          int       `gorm:"column:wms_co_id;comment:收货仓编码" json:"wms_co_id"`                // 收货仓编码
	SendLcID         string    `gorm:"column:send_lc_id;comment:寄出快递编码" json:"send_lc_id"`             // 寄出快递编码
	SendLcName       string    `gorm:"column:send_lc_name;comment:寄出快递名称" json:"send_lc_name"`         // 寄出快递名称
	CreatedAt        time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`             // 记录创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`             // 记录更新时间
}

// TableName 指定表名
func (AfterSaleDB) TableName() string {
	return "jst_after_sale"
}

// AfterSaleItemDB 售后商品明细数据库模型
type AfterSaleItemDB struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AsID            int       `gorm:"column:as_id;index;comment:售后单号" json:"as_id"`               // 售后单号
	SkuID           string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`             // 商品编码
	Name            string    `gorm:"column:name;comment:商品名称" json:"name"`                       // 商品名称
	Pic             string    `gorm:"column:pic;comment:图片" json:"pic"`                           // 图片
	Qty             int       `gorm:"column:qty;comment:数量" json:"qty"`                           // 数量
	Amount          float64   `gorm:"column:amount;comment:金额" json:"amount"`                     // 金额
	PropertiesValue string    `gorm:"column:properties_value;comment:规格" json:"properties_value"` // 规格
	Type            string    `gorm:"column:type;comment:类型" json:"type"`                         // 类型，可选:退货，换货，其它，补发
	Des             string    `gorm:"column:des;comment:备注" json:"des"`                           // 备注
	OuterOiID       string    `gorm:"column:outer_oi_id;comment:平台订单明细编号" json:"outer_oi_id"`     // 平台订单明细编号
	BatchID         string    `gorm:"column:batch_id;comment:生产批次号" json:"batch_id"`              // 生产批次号
	ProducedDate    string    `gorm:"column:produced_date;comment:生产日期" json:"produced_date"`     // 生产日期
	ExpirationDate  string    `gorm:"column:expiration_date;comment:到期日" json:"expiration_date"`  // 到期日
	CreatedAt       time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`         // 记录创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`         // 记录更新时间
}

// TableName 指定表名
func (AfterSaleItemDB) TableName() string {
	return "jst_after_sale_item"
}

// AfterSaleReceivedDB 售后入库单数据库模型
type AfterSaleReceivedDB struct {
	ID               int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	IoID             int       `gorm:"column:io_id;index;comment:退仓单号" json:"io_id"`                     // 退仓单号（唯一值）
	OID              int       `gorm:"column:o_id;index;comment:内部单号" json:"o_id"`                       // 内部单号
	SoID             string    `gorm:"column:so_id;index;comment:线上单号" json:"so_id"`                     // 线上单号
	AsID             int       `gorm:"column:as_id;index;comment:售后订单号" json:"as_id"`                    // 售后订单号
	LID              string    `gorm:"column:l_id;comment:物流单号" json:"l_id"`                             // 物流单号
	LogisticsCompany string    `gorm:"column:logistics_company;comment:物流公司名称" json:"logistics_company"` // 物流公司名称
	Creator          int       `gorm:"column:creator;comment:创建人编码" json:"creator"`                      // 创建人编码
	CreatorName      string    `gorm:"column:creator_name;comment:创建人名称" json:"creator_name"`            // 创建人名称
	IoDate           string    `gorm:"column:io_date;comment:入库日期" json:"io_date"`                       // 入库日期
	Warehouse        string    `gorm:"column:warehouse;comment:仓库名称" json:"warehouse"`                   // 仓库名称
	Modified         string    `gorm:"column:modified;comment:修改时间" json:"modified"`                     // 修改时间
	LcID             string    `gorm:"column:lc_id;comment:物流公司编码" json:"lc_id"`                         // 物流公司编码
	ShopID           int       `gorm:"column:shop_id;comment:店铺编号" json:"shop_id"`                       // 店铺编号
	AftersaleRemark  string    `gorm:"column:aftersale_remark;comment:售后备注" json:"aftersale_remark"`     // 售后备注
	Ts               int       `gorm:"column:ts;comment:时间戳" json:"ts"`                                  // 时间戳
	Status           string    `gorm:"column:status;comment:单据状态" json:"status"`                         // 单据状态
	WhID             int       `gorm:"column:wh_id;comment:仓库代码" json:"wh_id"`                           // 仓库代码（1主仓，2销退仓，3进货仓，4次品仓）
	WmsCoID          int       `gorm:"column:wms_co_id;comment:分仓编号" json:"wms_co_id"`                   // 分仓编号
	DrpCoName        string    `gorm:"column:drp_co_name;comment:分销商名称" json:"drp_co_name"`              // 分销商名称
	DrpCoIDTo        int       `gorm:"column:drp_co_id_to;comment:分销商编号" json:"drp_co_id_to"`            // 分销商编号
	Type             string    `gorm:"column:type;comment:售后类型" json:"type"`                             // 售后类型
	Currency         string    `gorm:"column:currency;comment:币种" json:"currency"`                       // 币种
	DrpCoIDFrom      int       `gorm:"column:drp_co_id_from;comment:分销商编号" json:"drp_co_id_from"`        // 分销商编号
	Labels           string    `gorm:"column:labels;comment:标签" json:"labels"`                           // 标签
	CreatedAt        time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`               // 记录创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`               // 记录更新时间
}

// TableName 指定表名
func (AfterSaleReceivedDB) TableName() string {
	return "jst_after_sale_received"
}

// AfterSaleReceivedItemDB 售后入库单商品明细数据库模型
type AfterSaleReceivedItemDB struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	IoID            int       `gorm:"column:io_id;index;comment:退仓单号" json:"io_id"`                // 退仓单号
	SkuID           string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`              // 商品编码
	IID             string    `gorm:"column:i_id;index;comment:款式编码" json:"i_id"`                  // 款式编码
	Unit            string    `gorm:"column:unit;comment:单位" json:"unit"`                          // 单位
	Qty             int       `gorm:"column:qty;comment:商品数量" json:"qty"`                          // 商品数量
	Name            string    `gorm:"column:name;comment:商品名称" json:"name"`                        // 商品名称
	PropertiesValue string    `gorm:"column:properties_value;comment:属性值" json:"properties_value"` // 属性值
	SalePrice       float64   `gorm:"column:sale_price;comment:销售价格" json:"sale_price"`            // 销售价格
	SaleAmount      float64   `gorm:"column:sale_amount;comment:销售总金额" json:"sale_amount"`         // 销售总金额
	IoiID           int       `gorm:"column:ioi_id;index;comment:退仓子单号" json:"ioi_id"`             // 退仓子单号
	CombineSkuID    string    `gorm:"column:combine_sku_id;comment:组合商品编码" json:"combine_sku_id"`  // 组合商品编码
	RawSoID         string    `gorm:"column:raw_so_id;comment:原始线上单号" json:"raw_so_id"`            // 原始线上单号
	CreatedAt       time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`          // 记录创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`          // 记录更新时间
}

// TableName 指定表名
func (AfterSaleReceivedItemDB) TableName() string {
	return "jst_after_sale_received_item"
}

// AfterSaleReceivedBatchDB 售后入库单批次数据库模型
type AfterSaleReceivedBatchDB struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	IoID           int       `gorm:"column:io_id;index;comment:退仓单号" json:"io_id"`               // 退仓单号
	IoiID          int       `gorm:"column:ioi_id;index;comment:退仓子单号" json:"ioi_id"`            // 退仓子单号
	BatchNo        string    `gorm:"column:batch_no;comment:批次号" json:"batch_no"`                // 批次号
	SkuID          string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`             // 商品编码
	Qty            int       `gorm:"column:qty;comment:商品数量" json:"qty"`                         // 商品数量
	ProductDate    string    `gorm:"column:product_date;comment:批次日期" json:"product_date"`       // 批次日期
	SupplierID     string    `gorm:"column:supplier_id;comment:供应商编号" json:"supplier_id"`        // 供应商编号
	SupplierName   string    `gorm:"column:supplier_name;comment:供应商名称" json:"supplier_name"`    // 供应商名称
	ExpirationDate string    `gorm:"column:expiration_date;comment:有效期至" json:"expiration_date"` // 有效期至
	CreatedAt      time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`         // 记录创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`         // 记录更新时间
}

// TableName 指定表名
func (AfterSaleReceivedBatchDB) TableName() string {
	return "jst_after_sale_received_batch"
}

// AfterSaleReceivedSNDB 售后入库单SN码数据库模型
type AfterSaleReceivedSNDB struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	IoID      int       `gorm:"column:io_id;index;comment:退仓单号" json:"io_id"`       // 退仓单号
	SkuID     string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`     // 商品编码
	SN        string    `gorm:"column:sn;comment:SN码" json:"sn"`                    // SN码
	CreatedAt time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"` // 记录创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"` // 记录更新时间
}

// TableName 指定表名
func (AfterSaleReceivedSNDB) TableName() string {
	return "jst_after_sale_received_sn"
}

// RefundDB 退款单数据库模型
type RefundDB struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ShopID      int       `gorm:"column:shop_id;index;comment:店铺编码" json:"shop_id"`          // 店铺编码
	AsID        int       `gorm:"column:as_id;index;comment:售后单" json:"as_id"`               // 售后单
	OID         int       `gorm:"column:o_id;index;comment:内部订单号" json:"o_id"`               // 内部订单号
	PayID       int       `gorm:"column:pay_id;index;comment:退款单号" json:"pay_id"`            // 退款单号
	PayDate     string    `gorm:"column:pay_date;comment:退款时间" json:"pay_date"`              // 退款时间
	ConfirmDate string    `gorm:"column:confirm_date;comment:审核时间" json:"confirm_date"`      // 审核时间
	Modified    string    `gorm:"column:modified;comment:修改时间" json:"modified"`              // 修改时间
	Amount      float64   `gorm:"column:amount;comment:原订单已付金额" json:"amount"`               // 原订单已付金额
	Status      string    `gorm:"column:status;comment:状态" json:"status"`                    // 状态
	Payment     string    `gorm:"column:payment;comment:退款方式" json:"payment"`                // 退款方式
	DrpCoIDFrom int       `gorm:"column:drp_co_id_from;comment:分销商id" json:"drp_co_id_from"` // 分销商id
	Ts          int       `gorm:"column:ts;comment:分页用时间戳" json:"ts"`                        // 分页用时间戳
	CreatedAt   time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`        // 记录创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`        // 记录更新时间
}

// TableName 指定表名
func (RefundDB) TableName() string {
	return "jst_refund"
}

// AfterSaleActionDB 售后操作日志数据库模型
type AfterSaleActionDB struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	AsID        int       `gorm:"column:as_id;index;comment:售后单号" json:"as_id"`        // 售后单号
	Name        string    `gorm:"column:name;comment:操作名称" json:"name"`                // 操作名称
	Remark      string    `gorm:"column:remark;comment:备注" json:"remark"`              // 备注
	Created     string    `gorm:"column:created;comment:操作时间" json:"created"`          // 操作时间
	CreatorName string    `gorm:"column:creator_name;comment:创建人" json:"creator_name"` // 创建人
	CreatedAt   time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`  // 记录创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`  // 记录更新时间
}

// TableName 指定表名
func (AfterSaleActionDB) TableName() string {
	return "jst_after_sale_action"
}
