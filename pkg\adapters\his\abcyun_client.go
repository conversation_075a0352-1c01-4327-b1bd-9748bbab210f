package his

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"
)

// ABCClient ABC云诊所管家客户端适配器
type ABCClient struct {
	client *abcyun.AbcYunClient
	config struct {
		ClinicIDs []string
	}
}

// NewABCClient 创建ABC云诊所管家客户端适配器
func NewABCClient(client *abcyun.AbcYunClient) *ABCClient {
	return &ABCClient{
		client: client,
		config: struct {
			ClinicIDs []string
		}{
			ClinicIDs: []string{"clinic1"}, // 默认诊所ID，可以根据实际情况修改
		},
	}
}

// GetProviderType 获取提供商类型
func (c *ABCClient) GetProviderType() ProviderType {
	return ProviderABCYun
}

// GetClinicList 获取诊所列表
func (c *ABCClient) GetClinicList(ctx context.Context) ([]string, error) {
	// 从配置获取诊所列表
	return c.config.ClinicIDs, nil
}

// GetDepartments 获取科室列表
func (c *ABCClient) GetDepartments(ctx context.Context, clinicID string) ([]DepartmentInfo, error) {
	// 查询科室列表
	respBytes, err := abcyun.ClientGetDepartmentList(clinicID)
	if err != nil {
		logx.Error("获取ABC云科室列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取科室列表失败: %w", err)
	}

	// 解析响应
	var resp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Items []struct {
				Id          string `json:"id"`
				Name        string `json:"name"`
				Description string `json:"description"`
			} `json:"items"`
		} `json:"data"`
	}

	if err := json.Unmarshal(respBytes, &resp); err != nil {
		logx.Error("解析ABC云科室列表响应失败", zap.Error(err))
		return nil, fmt.Errorf("解析科室列表响应失败: %w", err)
	}

	// 检查响应状态码
	if resp.Code != 200 {
		logx.Error("ABC云科室列表请求失败", zap.String("message", resp.Message))
		return nil, fmt.Errorf("获取科室列表失败: %s", resp.Message)
	}

	// 转换为通用科室信息
	result := make([]DepartmentInfo, 0, len(resp.Data.Items))
	for _, dept := range resp.Data.Items {
		result = append(result, DepartmentInfo{
			ID:          dept.Id,
			Name:        dept.Name,
			Description: dept.Description,
			Status:      1, // 默认为正常状态
			ExternalID:  dept.Id,
			ExternalKey: string(ProviderABCYun),
		})
	}

	return result, nil
}

// GetDoctors 获取医生列表
func (c *ABCClient) GetDoctors(ctx context.Context, clinicID, departmentID string, date time.Time) ([]DoctorInfo, error) {
	// 由于原始API不支持根据科室ID过滤，这里先获取所有医生
	respBytes, err := abcyun.ClientGetDoctorList(clinicID, 100, 0)
	if err != nil {
		logx.Error("获取ABC云医生列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取医生列表失败: %w", err)
	}

	// 解析响应
	var resp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Items []struct {
				Id             string `json:"id"`
				Name           string `json:"name"`
				Title          string `json:"title"`
				DepartmentName string `json:"departmentName"`
				Introduction   string `json:"introduction"`
			} `json:"items"`
		} `json:"data"`
	}

	if err := json.Unmarshal(respBytes, &resp); err != nil {
		logx.Error("解析ABC云医生列表响应失败", zap.Error(err))
		return nil, fmt.Errorf("解析医生列表响应失败: %w", err)
	}

	// 检查响应状态码
	if resp.Code != 200 {
		logx.Error("ABC云医生列表请求失败", zap.String("message", resp.Message))
		return nil, fmt.Errorf("获取医生列表失败: %s", resp.Message)
	}

	// 转换为通用医生信息
	result := make([]DoctorInfo, 0, len(resp.Data.Items))
	for _, doc := range resp.Data.Items {
		result = append(result, DoctorInfo{
			ID:             doc.Id,
			Name:           doc.Name,
			Title:          doc.Title,
			DepartmentID:   departmentID,
			DepartmentName: doc.DepartmentName,
			Introduction:   doc.Introduction,
			Status:         1, // 默认为正常状态
			ExternalID:     doc.Id,
			ExternalKey:    string(ProviderABCYun),
		})
	}

	return result, nil
}

// GetSchedules 获取排班信息
func (c *ABCClient) GetSchedules(ctx context.Context, clinicID, departmentID, doctorID string, date time.Time) ([]ScheduleInfo, error) {
	// 日期格式化
	dateStr := date.Format("2006-01-02")

	// 模拟排班数据，因为ABC云API未提供直接调用方法
	// 在实际项目中，应使用真实的API调用
	// 这部分仅示例代码，实际应根据API进行适配

	// 模拟响应数据
	scheduleItems := []struct {
		Id             string
		DoctorName     string
		DepartmentName string
		StartTime      string
		EndTime        string
		Quota          int
		Reserved       int
		RegisterFee    string
		Status         string
	}{
		{
			Id:             "schedule_" + doctorID + "_" + dateStr + "_1",
			DoctorName:     "Doctor " + doctorID,
			DepartmentName: "Department " + departmentID,
			StartTime:      dateStr + " 09:00:00",
			EndTime:        dateStr + " 10:00:00",
			Quota:          5,
			Reserved:       2,
			RegisterFee:    "50.00",
			Status:         "enabled",
		},
		{
			Id:             "schedule_" + doctorID + "_" + dateStr + "_2",
			DoctorName:     "Doctor " + doctorID,
			DepartmentName: "Department " + departmentID,
			StartTime:      dateStr + " 14:00:00",
			EndTime:        dateStr + " 15:00:00",
			Quota:          5,
			Reserved:       1,
			RegisterFee:    "50.00",
			Status:         "enabled",
		},
	}

	// 转换为通用排班信息
	result := make([]ScheduleInfo, 0, len(scheduleItems))
	for _, sch := range scheduleItems {
		// 解析时间
		startTime, err := time.Parse("2006-01-02 15:04:05", sch.StartTime)
		if err != nil {
			logx.Error("解析开始时间失败", zap.String("time", sch.StartTime), zap.Error(err))
			continue
		}

		endTime, err := time.Parse("2006-01-02 15:04:05", sch.EndTime)
		if err != nil {
			logx.Error("解析结束时间失败", zap.String("time", sch.EndTime), zap.Error(err))
			continue
		}

		// 转换状态
		status := 1 // 默认为正常
		if sch.Status == "disabled" {
			status = 3 // 停诊
		} else if sch.Reserved >= sch.Quota {
			status = 2 // 已满
		}

		// 解析挂号费
		registerFee, err := strconv.ParseFloat(sch.RegisterFee, 64)
		if err != nil {
			registerFee = 0
		}

		result = append(result, ScheduleInfo{
			ID:             sch.Id,
			DoctorID:       doctorID,
			DoctorName:     sch.DoctorName,
			DepartmentID:   departmentID,
			DepartmentName: sch.DepartmentName,
			Date:           startTime.Truncate(24 * time.Hour),
			StartTime:      startTime,
			EndTime:        endTime,
			Quota:          sch.Quota,
			Reserved:       sch.Reserved,
			RegisterFee:    registerFee,
			Status:         status,
			ExternalID:     sch.Id,
			ExternalKey:    string(ProviderABCYun),
		})
	}

	return result, nil
}

// CreatePatient 创建患者信息
func (c *ABCClient) CreatePatient(ctx context.Context, clinicID string, patient *PatientInfo) (*PatientInfo, error) {
	// 模拟创建患者
	// 在实际项目中，应使用真实的API调用
	patientResp := struct {
		Id string
	}{
		Id: "patient_" + strconv.FormatInt(time.Now().UnixNano(), 10),
	}

	// 更新患者ID
	patient.ExternalID = patientResp.Id
	patient.ExternalKey = string(ProviderABCYun)
	patient.ID = patient.ExternalID

	return patient, nil
}

// GetPatients 获取患者列表
func (c *ABCClient) GetPatients(ctx context.Context, clinicID, nameQuery string) ([]PatientInfo, error) {
	// 模拟获取患者列表
	// 在实际项目中，应使用真实的API调用
	patientItems := []struct {
		Id         string
		Name       string
		Gender     int
		Birthday   string
		IDCardType int
		IDCardNo   string
		Phone      string
		Address    string
	}{
		{
			Id:         "patient_1",
			Name:       "张三",
			Gender:     1,
			Birthday:   "1990-01-01",
			IDCardType: 1,
			IDCardNo:   "110101199001010011",
			Phone:      "13800138000",
			Address:    "北京市",
		},
	}

	// 转换为通用患者信息
	result := make([]PatientInfo, 0, len(patientItems))
	for _, pat := range patientItems {
		// 解析出生日期
		var birthday time.Time
		if pat.Birthday != "" {
			var err error
			birthday, err = time.Parse("2006-01-02", pat.Birthday)
			if err != nil {
				logx.Error("解析出生日期失败", zap.String("date", pat.Birthday), zap.Error(err))
				birthday = time.Time{} // 使用零值
			}
		}

		result = append(result, PatientInfo{
			ID:          pat.Id,
			Name:        pat.Name,
			Gender:      pat.Gender,
			Birthday:    birthday,
			IDCardType:  pat.IDCardType,
			IDCardNo:    pat.IDCardNo,
			Phone:       pat.Phone,
			Address:     pat.Address,
			ExternalID:  pat.Id,
			ExternalKey: string(ProviderABCYun),
		})
	}

	return result, nil
}

// GetPatient 获取患者信息
func (c *ABCClient) GetPatient(ctx context.Context, clinicID, patientID string) (*PatientInfo, error) {
	// 模拟获取患者信息
	// 在实际项目中，应使用真实的API调用
	pat := struct {
		Id         string
		Name       string
		Gender     int
		Birthday   string
		IDCardType int
		IDCardNo   string
		Phone      string
		Address    string
	}{
		Id:         patientID,
		Name:       "张三",
		Gender:     1,
		Birthday:   "1990-01-01",
		IDCardType: 1,
		IDCardNo:   "110101199001010011",
		Phone:      "13800138000",
		Address:    "北京市",
	}

	// 解析出生日期
	var birthday time.Time
	if pat.Birthday != "" {
		var err error
		birthday, err = time.Parse("2006-01-02", pat.Birthday)
		if err != nil {
			logx.Error("解析出生日期失败", zap.String("date", pat.Birthday), zap.Error(err))
			birthday = time.Time{} // 使用零值
		}
	}

	// 转换为通用患者信息
	return &PatientInfo{
		ID:          pat.Id,
		Name:        pat.Name,
		Gender:      pat.Gender,
		Birthday:    birthday,
		IDCardType:  pat.IDCardType,
		IDCardNo:    pat.IDCardNo,
		Phone:       pat.Phone,
		Address:     pat.Address,
		ExternalID:  pat.Id,
		ExternalKey: string(ProviderABCYun),
	}, nil
}

// CreateAppointment 创建预约
func (c *ABCClient) CreateAppointment(ctx context.Context, clinicID string, appointment *AppointmentInfo) (*AppointmentInfo, error) {
	// 模拟创建预约
	// 在实际项目中，应使用真实的API调用
	appointResp := struct {
		Id     string
		Status string
	}{
		Id:     "appointment_" + strconv.FormatInt(time.Now().UnixNano(), 10),
		Status: "pending",
	}

	// 更新预约ID和状态
	appointment.ExternalID = appointResp.Id
	appointment.ExternalKey = string(ProviderABCYun)
	appointment.ID = appointment.ExternalID
	appointment.Status = convertABCStatus(appointResp.Status)

	return appointment, nil
}

// 转换ABC云状态到内部状态
func convertABCStatus(abcStatus string) int {
	switch abcStatus {
	case "pending":
		return 1 // 待支付
	case "paid":
		return 2 // 已支付
	case "completed":
		return 3 // 已完成
	case "cancelled":
		return 4 // 已取消
	default:
		return 1 // 默认为待支付
	}
}

// CancelAppointment 取消预约
func (c *ABCClient) CancelAppointment(ctx context.Context, clinicID, appointmentID, reason string) error {
	// 模拟取消预约
	// 在实际项目中，应使用真实的API调用
	return nil
}

// GetAppointments 获取预约列表
func (c *ABCClient) GetAppointments(ctx context.Context, clinicID, patientID string, startDate, endDate time.Time, status int) ([]AppointmentInfo, error) {
	// 日期格式化
	startDateStr := startDate.Format("2006-01-02")
	// 转换状态为ABC云状态
	statusStr := ""
	switch status {
	case 1:
		statusStr = "pending"
	case 2:
		statusStr = "paid"
	case 3:
		statusStr = "completed"
	case 4:
		statusStr = "cancelled"
	}

	// 模拟获取预约列表
	// 在实际项目中，应使用真实的API调用
	appointmentItems := []struct {
		Id             string
		PatientID      string
		PatientName    string
		DoctorID       string
		DoctorName     string
		DepartmentID   string
		DepartmentName string
		ScheduleID     string
		AppointTime    string
		RegisterFee    string
		Status         string
	}{
		{
			Id:             "appointment_1",
			PatientID:      patientID,
			PatientName:    "张三",
			DoctorID:       "doctor_1",
			DoctorName:     "李医生",
			DepartmentID:   "dept_1",
			DepartmentName: "内科",
			ScheduleID:     "schedule_1",
			AppointTime:    startDateStr + " 09:00:00",
			RegisterFee:    "50.00",
			Status:         statusStr,
		},
	}

	// 转换为通用预约信息
	result := make([]AppointmentInfo, 0, len(appointmentItems))
	for _, appt := range appointmentItems {
		// 解析预约时间
		var appointTime time.Time
		if appt.AppointTime != "" {
			var err error
			appointTime, err = time.Parse("2006-01-02 15:04:05", appt.AppointTime)
			if err != nil {
				logx.Error("解析预约时间失败", zap.String("time", appt.AppointTime), zap.Error(err))
				appointTime = time.Now() // 使用当前时间作为默认值
			}
		}

		// 解析挂号费
		registerFee, err := strconv.ParseFloat(appt.RegisterFee, 64)
		if err != nil {
			registerFee = 0
		}

		result = append(result, AppointmentInfo{
			ID:             appt.Id,
			PatientID:      appt.PatientID,
			PatientName:    appt.PatientName,
			DoctorID:       appt.DoctorID,
			DoctorName:     appt.DoctorName,
			DepartmentID:   appt.DepartmentID,
			DepartmentName: appt.DepartmentName,
			ScheduleID:     appt.ScheduleID,
			AppointTime:    appointTime,
			RegisterFee:    registerFee,
			Status:         convertABCStatus(appt.Status),
			ExternalID:     appt.Id,
			ExternalKey:    string(ProviderABCYun),
		})
	}

	return result, nil
}

// SyncDepartments 同步科室信息
func (c *ABCClient) SyncDepartments(ctx context.Context, clinicID string) error {
	// 获取科室列表并同步到内部系统
	// 这部分逻辑应该由服务层实现，这里只是接口定义
	_, err := c.GetDepartments(ctx, clinicID)
	if err != nil {
		return err
	}
	return nil
}

// SyncDoctors 同步医生信息
func (c *ABCClient) SyncDoctors(ctx context.Context, clinicID string, date time.Time) error {
	// 获取科室列表
	departments, err := c.GetDepartments(ctx, clinicID)
	if err != nil {
		return err
	}

	// 遍历科室，同步医生信息
	for _, dept := range departments {
		_, err := c.GetDoctors(ctx, clinicID, dept.ID, date)
		if err != nil {
			logx.Error("同步医生信息失败",
				zap.String("clinicID", clinicID),
				zap.String("departmentID", dept.ID),
				zap.Error(err))
			continue
		}
	}

	return nil
}

// SyncSchedules 同步排班信息
func (c *ABCClient) SyncSchedules(ctx context.Context, clinicID string, startDate, endDate time.Time) error {
	// 获取科室列表
	departments, err := c.GetDepartments(ctx, clinicID)
	if err != nil {
		return err
	}

	// 遍历日期范围
	for date := startDate; !date.After(endDate); date = date.AddDate(0, 0, 1) {
		// 遍历科室
		for _, dept := range departments {
			// 获取医生列表
			doctors, err := c.GetDoctors(ctx, clinicID, dept.ID, date)
			if err != nil {
				logx.Error("获取医生列表失败",
					zap.String("clinicID", clinicID),
					zap.String("departmentID", dept.ID),
					zap.Time("date", date),
					zap.Error(err))
				continue
			}

			// 遍历医生，同步排班信息
			for _, doctor := range doctors {
				_, err := c.GetSchedules(ctx, clinicID, dept.ID, doctor.ID, date)
				if err != nil {
					logx.Error("同步排班信息失败",
						zap.String("clinicID", clinicID),
						zap.String("departmentID", dept.ID),
						zap.String("doctorID", doctor.ID),
						zap.Time("date", date),
						zap.Error(err))
					continue
				}
			}
		}
	}

	return nil
}

// SyncAppointments 同步预约信息
func (c *ABCClient) SyncAppointments(ctx context.Context, clinicID string, startDate, endDate time.Time) error {
	// 同步预约信息
	_, err := c.GetAppointments(ctx, clinicID, "", startDate, endDate, 0)
	if err != nil {
		logx.Error("同步预约信息失败",
			zap.String("clinicID", clinicID),
			zap.Time("startDate", startDate),
			zap.Time("endDate", endDate),
			zap.Error(err))
		return err
	}

	return nil
}
