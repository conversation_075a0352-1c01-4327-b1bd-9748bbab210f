package routes

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/checkin/handler"
	"yekaitai/wx_internal/svc"
)

// RegisterCheckinRoutes 注册签到相关路由
func RegisterCheckinRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	routes := []rest.Route{
		// 获取签到日历
		{
			Method:  "GET",
			Path:    "/api/wx/checkin/calendar",
			Handler: wxAuthWrapper(handler.CheckinCalendarHandler(serverCtx)),
		},
		// 用户签到
		{
			Method:  "POST",
			Path:    "/api/wx/checkin",
			Handler: wxAuthWrapper(handler.<PERSON><PERSON><PERSON><PERSON><PERSON>(serverCtx)),
		},
		// 分享签到
		{
			Method:  "POST",
			Path:    "/api/wx/checkin/share",
			Handler: wxAuthWrapper(handler.ShareCheckinHandler(serverCtx)),
		},
		// 获取签到弹窗信息
		{
			Method:  "GET",
			Path:    "/api/wx/checkin/popup",
			Handler: wxAuthWrapper(handler.CheckinPopupHandler(serverCtx)),
		},
		// 获取签到统计
		{
			Method:  "GET",
			Path:    "/api/wx/checkin/stats",
			Handler: wxAuthWrapper(handler.CheckinStatsHandler(serverCtx)),
		},
		// 获取签到奖励规则
		{
			Method:  "GET",
			Path:    "/api/wx/checkin/rules",
			Handler: wxAuthWrapper(handler.CheckinRewardRulesHandler(serverCtx)),
		},
	}

	server.AddRoutes(routes)
}
