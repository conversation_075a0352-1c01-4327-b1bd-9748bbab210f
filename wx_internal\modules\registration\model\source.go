package model

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/common/model/doctor"
)

// SourceResponse 号源响应结构
type SourceResponse struct {
	YspbID      int     `json:"yspbid"`       // 医生排班ID
	JgksID      int     `json:"jgksid"`       // 机构科室ID
	JgksMC      string  `json:"jgksmc"`       // 机构科室名称
	YsID        int     `json:"ysid"`         // 医生ID
	YsMC        string  `json:"ysmc"`         // 医生名称
	XqXH        int     `json:"xqxh"`         // 星期序号 1:周一 2:周二 3:周三 4:周四 5:周五 6:周六 7:周日
	PbLx        int     `json:"pblx"`         // 排班类型 0:普通 1:专家 2:急诊
	KsSJ        string  `json:"kssj"`         // 开始时间
	JsSJ        string  `json:"jssj"`         // 结束时间
	XhSL        int     `json:"xhsl"`         // 限号数量
	SJD         int     `json:"sjd"`          // 时间段 1:上午 2:下午
	GhF         float64 `json:"ghf"`          // 挂号费
	GhFID       int     `json:"ghfid"`        // 挂号费ID
	ZlF         float64 `json:"zlf"`          // 诊疗费
	WsjgID      int     `json:"wsjgid"`       // 卫生机构ID
	AmRemain    int     `json:"am_remain"`    // 上午剩余号源数
	PmRemain    int     `json:"pm_remain"`    // 下午剩余号源数
	NightRemain int     `json:"night_remain"` // 夜班剩余号源数
	TotalSlot   int     `json:"total_slot"`   // 总号源数
	HyRq        string  `json:"hyrq"`         // 号源日期
}

// HyjlResponse 号源记录响应结构
type HyjlResponse struct {
	HyjlID int    `json:"hyjlid"` // 号源记录ID
	YspbID int    `json:"yspbid"` // 医生排班ID
	HyXH   int    `json:"hyxh"`   // 号源序号
	HySJ   string `json:"hysj"`   // 号源时间
	HyRQ   string `json:"hyrq"`   // 号源日期
	HyZT   int    `json:"hyzt"`   // 号源状态 0:未预约 1:已预约 2:已取号 3:已停诊
}

// DetailedHyjlRecord 详细号源记录结构（用于hyjl数组中的项）
type DetailedHyjlRecord struct {
	CzrID  int    `json:"czrid"`  // 操作人ID
	CzSJ   string `json:"czsj"`   // 操作时间
	XgrID  int    `json:"xgrid"`  // 修改人ID
	XgSJ   string `json:"xgsj"`   // 修改时间
	ZfrID  int    `json:"zfrid"`  // 作废人ID
	ZfSJ   string `json:"zfsj"`   // 作废时间
	ZfBz   int    `json:"zfbz"`   // 作废标志
	ZfYY   string `json:"zfyy"`   // 作废原因
	Bz     string `json:"bz"`     // 备注
	XM     string `json:"xm"`     // 姓名
	XbDM   string `json:"xbdm"`   // 性别代码
	XbMC   string `json:"xbmc"`   // 性别名称
	NL     string `json:"nl"`     // 年龄
	HyjlID int    `json:"hyjlid"` // 号源记录ID
	YspbID int    `json:"yspbid"` // 医生排班ID
	HyXH   int    `json:"hyxh"`   // 号源序号
	HySJ   string `json:"hysj"`   // 号源时间
	HyRQ   string `json:"hyrq"`   // 号源日期
	HyZT   string `json:"hyzt"`   // 号源状态
	YyLx   string `json:"yylx"`   // 预约类型
	QhMM   string `json:"qhmm"`   // 取号密码
	YyghID int    `json:"yyghid"` // 预约挂号ID
	GrxxID int    `json:"grxxid"` // 个人信息ID
	JsSJ   string `json:"jssj"`   // 结束时间
	HySF   string `json:"hysf"`   // 号源时分
}

// CompleteSourceRecordResponse 完整号源记录响应结构
type CompleteSourceRecordResponse struct {
	// 基本信息
	PYM   string `json:"pym"`   // 拼音码
	WBM   string `json:"wbm"`   // 五笔码
	CzrID int    `json:"czrid"` // 操作人ID
	CzSJ  string `json:"czsj"`  // 操作时间
	XgrID int    `json:"xgrid"` // 修改人ID
	XgSJ  string `json:"xgsj"`  // 修改时间
	ZfrID int    `json:"zfrid"` // 作废人ID
	ZfSJ  string `json:"zfsj"`  // 作废时间
	ZfBz  int    `json:"zfbz"`  // 作废标志
	ZfYY  string `json:"zfyy"`  // 作废原因
	Bz    string `json:"bz"`    // 备注
	CzrMC string `json:"czrmc"` // 操作人名称
	XgrMC string `json:"xgrmc"` // 修改人名称
	ZfrMC string `json:"zfrmc"` // 作废人名称

	// 医生排班信息
	YspbID int    `json:"yspbid"` // 医生排班ID
	WsjgID int    `json:"wsjgid"` // 卫生机构ID
	WsjgMC string `json:"wsjgmc"` // 卫生机构名称
	JgksID int    `json:"jgksid"` // 机构科室ID
	JgksMC string `json:"jgksmc"` // 机构科室名称
	YsID   int    `json:"ysid"`   // 医生ID
	YsMC   string `json:"ysmc"`   // 医生名称

	// 排班时间信息
	PbksRQ string `json:"pbksrq"` // 排班开始日期
	PbZQ   string `json:"pbzq"`   // 排班周期
	XqXH   string `json:"xqxh"`   // 星期序号
	RqXH   int    `json:"rqxh"`   // 日期序号
	PbLx   string `json:"pblx"`   // 排班类型
	KsSJ   string `json:"kssj"`   // 开始时间
	JsSJ   string `json:"jssj"`   // 结束时间
	XhSL   int    `json:"xhsl"`   // 限号数量

	// 费用信息
	GhFID int     `json:"ghfid"` // 挂号费ID
	GhF   float64 `json:"ghf"`   // 挂号费
	ZlFID int     `json:"zlfid"` // 诊疗费ID
	ZlF   float64 `json:"zlf"`   // 诊疗费

	// 停诊信息
	TzBz int    `json:"tzbz"` // 停诊标志
	TzSJ string `json:"tzsj"` // 停诊时间
	TzYY string `json:"tzyy"` // 停诊原因

	// 其他信息
	Zs   string `json:"zs"`   // 主诉
	SJD  string `json:"sjd"`  // 时间段
	PbRQ string `json:"pbrq"` // 排班日期
	HyRQ string `json:"hyrq"` // 号源日期

	// 统计信息
	SwyhSL int `json:"swyhsl"` // 上午预约号数量
	XwyhSL int `json:"xwyhsl"` // 下午预约号数量
	SwzSL  int `json:"swzsl"`  // 上午总数量
	XwzSL  int `json:"xwzsl"`  // 下午总数量

	// 医生详细信息
	YsZC   string `json:"yszc"`   // 医生职称
	GrZP   string `json:"grzp"`   // 个人照片
	GrJJ   string `json:"grjj"`   // 个人简介
	ZyTC   string `json:"zytc"`   // 专业特长
	XbMC   string `json:"xbmc"`   // 性别名称
	WsryID string `json:"wsryid"` // 卫生人员ID
	PxSX   string `json:"pxsx"`   // 排序顺序
	YsgjBM string `json:"ysgjbm"` // 医生国籍编码
	Count  string `json:"count"`  // 计数

	// 号源记录数组
	HyJL []DetailedHyjlRecord `json:"hyjl"` // 号源记录数组
}

// SourceRepository 号源查询仓库接口
type SourceRepository interface {
	GetSources(ctx context.Context, pbrq string, wsjgID, jgksID int, ysID string) ([]SourceResponse, error)
	GetSourceRecordsByPeriod(ctx context.Context, pbrq string, wsjgID, jgksID int, ysID string, period int) ([]HyjlResponse, error)
	GetCompleteSourceRecords(ctx context.Context, pbrq string, wsjgID, jgksID int, ysID string, period int) ([]CompleteSourceRecordResponse, error)
}

// sourceRepository 号源查询仓库实现
type sourceRepository struct{}

// NewSourceRepository 创建号源查询仓库
func NewSourceRepository() SourceRepository {
	return &sourceRepository{}
}

// GetSources 查询号源
func (r *sourceRepository) GetSources(ctx context.Context, pbrq string, wsjgID, jgksID int, ysID string) ([]SourceResponse, error) {
	// 调用杭州API
	resp, err := hangzhou.GetScheduleSources(ctx, pbrq, wsjgID, jgksID, ysID)
	if err != nil {
		logx.Errorf("获取排班号源失败: %v", err)
		return nil, err
	}

	// 解析响应
	var sources []doctor.DoctorScheduleSource

	// 检查响应类型
	switch data := resp.Data.(type) {
	case string:
		// 如果是字符串，需要预处理将字符串形式的数值转为数值
		var rawData []map[string]interface{}
		if err := json.Unmarshal([]byte(data), &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据，将字符串形式的数值转为实际数值
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	case []byte:
		// 同样需要进行预处理
		var rawData []map[string]interface{}
		if err := json.Unmarshal(data, &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	default:
		// 先转换为JSON字符串再处理
		dataBytes, err := json.Marshal(resp.Data)
		if err != nil {
			logx.Errorf("Marshal排班号源数据失败: %v", err)
			return nil, err
		}

		// 采用与字符串相同的处理方式
		var rawData []map[string]interface{}
		if err := json.Unmarshal(dataBytes, &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	}

	// 构建响应
	var result []SourceResponse
	for _, source := range sources {
		// 计算上午、下午和夜班的剩余号源数
		amRemain, pmRemain, nightRemain := 0, 0, 0
		totalSlot := len(source.HyJL)

		// 获取号源日期
		hyRq := ""
		if len(source.HyJL) > 0 {
			hyRq = source.HyJL[0].HyRQ
		}

		for _, record := range source.HyJL {
			// 如果号源未预约
			if record.HyZT == 0 {
				// 解析时间判断上午下午夜班
				hysj, err := time.Parse("15:04:05", record.HySJ)
				if err != nil {
					continue
				}

				// 根据时间段判断
				hour := hysj.Hour()
				minute := hysj.Minute()

				// 将时间转换为分钟，方便比较
				timeInMinutes := hour*60 + minute

				// 午夜12点前为上午
				if hour < 12 {
					amRemain++
				} else if timeInMinutes < 17*60+30 { // 17:30前(但在12:00后)为下午
					pmRemain++
				} else { // 17:30及以后为夜班
					nightRemain++
				}
			}
		}

		result = append(result, SourceResponse{
			YspbID:      source.YspbID,
			JgksID:      source.JgksID,
			JgksMC:      source.JgksMC,
			YsID:        source.YsID,
			YsMC:        source.YsMC,
			XqXH:        source.XqXH,
			PbLx:        source.PbLx,
			KsSJ:        source.KsSJ,
			JsSJ:        source.JsSJ,
			XhSL:        source.XhSL,
			SJD:         source.SJD,
			GhF:         source.GhF,
			GhFID:       source.GhFID,
			ZlF:         source.ZlF,
			WsjgID:      source.WsjgID,
			AmRemain:    amRemain,
			PmRemain:    pmRemain,
			NightRemain: nightRemain,
			TotalSlot:   totalSlot,
			HyRq:        hyRq,
		})
	}

	return result, nil
}

// 添加辅助函数，将字符串形式的数值转为实际数值类型
func convertStringsToNumbers(data map[string]interface{}) {
	for k, v := range data {
		if strVal, ok := v.(string); ok {
			// 尝试解析为整数
			if intVal, err := strconv.Atoi(strVal); err == nil {
				data[k] = intVal
				continue
			}

			// 尝试解析为浮点数
			if floatVal, err := strconv.ParseFloat(strVal, 64); err == nil {
				data[k] = floatVal
				continue
			}
		}
	}
}

// GetSourceRecordsByPeriod 根据时间段查询号源记录
func (r *sourceRepository) GetSourceRecordsByPeriod(ctx context.Context, pbrq string, wsjgID, jgksID int, ysID string, period int) ([]HyjlResponse, error) {
	// 调用杭州API
	resp, err := hangzhou.GetScheduleSources(ctx, pbrq, wsjgID, jgksID, ysID)
	if err != nil {
		logx.Errorf("获取排班号源失败: %v", err)
		return nil, err
	}

	// 解析响应
	var sources []doctor.DoctorScheduleSource

	// 检查响应类型
	switch data := resp.Data.(type) {
	case string:
		// 如果是字符串，需要预处理将字符串形式的数值转为数值
		var rawData []map[string]interface{}
		if err := json.Unmarshal([]byte(data), &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据，将字符串形式的数值转为实际数值
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	case []byte:
		// 采用与字符串相同的处理方式
		var rawData []map[string]interface{}
		if err := json.Unmarshal(data, &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	default:
		// 先转换为JSON字符串再处理
		dataBytes, err := json.Marshal(resp.Data)
		if err != nil {
			logx.Errorf("Marshal排班号源数据失败: %v", err)
			return nil, err
		}

		// 采用与字符串相同的处理方式
		var rawData []map[string]interface{}
		if err := json.Unmarshal(dataBytes, &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	}

	// 构建响应
	var result []HyjlResponse

	for _, source := range sources {
		for _, record := range source.HyJL {
			// 如果号源未预约
			if record.HyZT == 0 {
				// 解析时间判断上午下午夜班
				hysj, err := time.Parse("15:04:05", record.HySJ)
				if err != nil {
					continue
				}

				// 根据时间段判断
				hour := hysj.Hour()
				minute := hysj.Minute()

				// 将时间转换为分钟，方便比较
				timeInMinutes := hour*60 + minute

				// 根据时间段过滤
				// period: 1-上午(0:00-12:00), 2-下午(12:00-17:30), 3-夜班(17:30-24:00)
				shouldInclude := false

				if period == 1 && hour < 12 {
					// 上午
					shouldInclude = true
				} else if period == 2 && hour >= 12 && timeInMinutes < 17*60+30 {
					// 下午
					shouldInclude = true
				} else if period == 3 && timeInMinutes >= 17*60+30 {
					// 夜班
					shouldInclude = true
				} else if period == 0 {
					// 不限时间段
					shouldInclude = true
				}

				if shouldInclude {
					result = append(result, HyjlResponse{
						HyjlID: record.HyjlID,
						YspbID: source.YspbID,
						HyXH:   record.HyXH,
						HySJ:   record.HySJ,
						HyRQ:   record.HyRQ,
						HyZT:   record.HyZT,
					})
				}
			}
		}
	}

	return result, nil
}

// GetCompleteSourceRecords 获取完整的号源记录信息（包含排班详情和号源记录数组）
func (r *sourceRepository) GetCompleteSourceRecords(ctx context.Context, pbrq string, wsjgID, jgksID int, ysID string, period int) ([]CompleteSourceRecordResponse, error) {
	// 调用杭州API
	resp, err := hangzhou.GetScheduleSources(ctx, pbrq, wsjgID, jgksID, ysID)
	if err != nil {
		logx.Errorf("获取排班号源失败: %v", err)
		return nil, err
	}

	// 解析响应
	var sources []doctor.DoctorScheduleSource

	// 检查响应类型，与现有方法保持一致的处理逻辑
	switch data := resp.Data.(type) {
	case string:
		// 如果是字符串，需要预处理将字符串形式的数值转为数值
		var rawData []map[string]interface{}
		if err := json.Unmarshal([]byte(data), &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据，将字符串形式的数值转为实际数值
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	case []byte:
		// 采用与字符串相同的处理方式
		var rawData []map[string]interface{}
		if err := json.Unmarshal(data, &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	default:
		// 先转换为JSON字符串再处理
		dataBytes, err := json.Marshal(resp.Data)
		if err != nil {
			logx.Errorf("Marshal排班号源数据失败: %v", err)
			return nil, err
		}

		// 采用与字符串相同的处理方式
		var rawData []map[string]interface{}
		if err := json.Unmarshal(dataBytes, &rawData); err != nil {
			logx.Errorf("解析排班号源原始数据失败: %v", err)
			return nil, err
		}

		// 预处理数据
		for i := range rawData {
			// 处理顶层字段
			convertStringsToNumbers(rawData[i])

			// 处理hyjl数组
			if hyjlData, ok := rawData[i]["hyjl"].([]interface{}); ok {
				for j := range hyjlData {
					if hyjlItem, ok := hyjlData[j].(map[string]interface{}); ok {
						convertStringsToNumbers(hyjlItem)
					}
				}
			}
		}

		// 重新序列化为JSON
		processedData, err := json.Marshal(rawData)
		if err != nil {
			logx.Errorf("重新序列化排班号源数据失败: %v", err)
			return nil, err
		}

		// 反序列化为目标结构体
		if err := json.Unmarshal(processedData, &sources); err != nil {
			logx.Errorf("解析排班号源数据失败: %v", err)
			return nil, err
		}
	}

	// 构建完整响应结构
	var result []CompleteSourceRecordResponse

	for _, source := range sources {
		// 构建详细号源记录数组
		var detailedRecords []DetailedHyjlRecord
		for _, record := range source.HyJL {
			// 根据时间段过滤号源记录
			shouldInclude := false

			// 解析时间判断上午下午夜班
			if hysj, err := time.Parse("15:04:05", record.HySJ); err == nil {
				hour := hysj.Hour()
				minute := hysj.Minute()
				timeInMinutes := hour*60 + minute

				// 根据时间段过滤
				// period: 0-全部, 1-上午(0:00-12:00), 2-下午(12:00-17:30), 3-夜班(17:30-24:00)
				if period == 0 {
					// 不限时间段，包含所有
					shouldInclude = true
				} else if period == 1 && hour < 12 {
					// 上午
					shouldInclude = true
				} else if period == 2 && hour >= 12 && timeInMinutes < 17*60+30 {
					// 下午
					shouldInclude = true
				} else if period == 3 && timeInMinutes >= 17*60+30 {
					// 夜班
					shouldInclude = true
				}
			} else {
				// 时间解析失败的情况，如果是period=0（全部），仍然包含
				if period == 0 {
					shouldInclude = true
				}
			}

			if shouldInclude {
				detailedRecord := DetailedHyjlRecord{
					HyjlID: record.HyjlID,
					YspbID: source.YspbID,
					HyXH:   record.HyXH,
					HySJ:   record.HySJ,
					HyRQ:   record.HyRQ,
					HyZT:   strconv.Itoa(record.HyZT), // 转换为字符串类型
					JsSJ:   source.JsSJ,
					// 其他字段根据实际API响应进行填充，暂时设置默认值
					CzrID:  0,
					CzSJ:   "",
					XgrID:  0,
					XgSJ:   "",
					ZfrID:  0,
					ZfSJ:   "1970-01-01 00:00:00",
					ZfBz:   0,
					ZfYY:   "",
					Bz:     "",
					XM:     "",
					XbDM:   "",
					XbMC:   "",
					NL:     "",
					YyLx:   "",
					QhMM:   "",
					YyghID: 0,
					GrxxID: 0,
					HySF:   record.HySJ[:5], // 提取时分
				}
				detailedRecords = append(detailedRecords, detailedRecord)
			}
		}

		// 只有当存在符合时间段的号源记录时，才添加到结果中
		if len(detailedRecords) > 0 {
			// 构建完整响应结构
			completeResponse := CompleteSourceRecordResponse{
				// 基本信息（需要根据实际API响应进行调整）
				PYM:   "",
				WBM:   "",
				CzrID: 0,
				CzSJ:  "",
				XgrID: 0,
				XgSJ:  "",
				ZfrID: 0,
				ZfSJ:  "1970-01-01 00:00:00",
				ZfBz:  0,
				ZfYY:  "",
				Bz:    "",
				CzrMC: "",
				XgrMC: "",
				ZfrMC: "",

				// 医生排班信息
				YspbID: source.YspbID,
				WsjgID: source.WsjgID,
				WsjgMC: "", // 需要从API响应中获取或者为空
				JgksID: source.JgksID,
				JgksMC: source.JgksMC,
				YsID:   source.YsID,
				YsMC:   source.YsMC,

				// 排班时间信息
				PbksRQ: pbrq,
				PbZQ:   "1",
				XqXH:   strconv.Itoa(source.XqXH),
				RqXH:   0,
				PbLx:   strconv.Itoa(source.PbLx),
				KsSJ:   source.KsSJ,
				JsSJ:   source.JsSJ,
				XhSL:   source.XhSL,

				// 费用信息
				GhFID: source.GhFID,
				GhF:   source.GhF,
				ZlFID: 0, // 需要从API响应中获取
				ZlF:   source.ZlF,

				// 停诊信息
				TzBz: 0,
				TzSJ: "1970-01-01 00:00:00",
				TzYY: "",

				// 其他信息
				Zs:   "",
				SJD:  strconv.Itoa(source.SJD),
				PbRQ: pbrq,
				HyRQ: "",

				// 统计信息（需要根据实际需求计算）
				SwyhSL: 0,
				XwyhSL: 0,
				SwzSL:  0,
				XwzSL:  0,

				// 医生详细信息
				YsZC:   "",
				GrZP:   "",
				GrJJ:   "",
				ZyTC:   "",
				XbMC:   "",
				WsryID: "",
				PxSX:   "",
				YsgjBM: "",
				Count:  "",

				// 号源记录数组
				HyJL: detailedRecords,
			}

			result = append(result, completeResponse)
		}
	}

	return result, nil
}
