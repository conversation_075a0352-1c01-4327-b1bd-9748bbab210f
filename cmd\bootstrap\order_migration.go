package bootstrap

import (
	orderModel "yekaitai/pkg/common/model/order"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateOrderTables 执行订单模块表结构迁移
func MigrateOrderTables() error {
	db := mysql.Master()

	logx.Info("开始执行订单模块表结构迁移...")

	// 自动迁移订单表
	if err := db.AutoMigrate(&orderModel.Order{}); err != nil {
		logx.Errorf("订单表迁移失败: %v", err)
		return err
	}
	logx.Info("订单表迁移完成")

	// 自动迁移订单商品表
	if err := db.AutoMigrate(&orderModel.OrderItem{}); err != nil {
		logx.Errorf("订单商品表迁移失败: %v", err)
		return err
	}
	logx.Info("订单商品表迁移完成")

	logx.Info("订单模块表结构迁移完成")
	return nil
}
