package bootstrap

import (
	"fmt"
	"log"

	contentModel "yekaitai/internal/modules/content/model"
	medicalModel "yekaitai/internal/modules/pharmacy/model"
	"yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/common/model/redeemer"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
	departmentModel "yekaitai/wx_internal/modules/department/model"
)

// 需要迁移的模型
var wxModelsToMigrate = []interface{}{
	&medicalModel.MedicineCategory{},
	&medicalModel.Medicine{},
	&contentModel.Content{},
	&contentModel.ContentStoreRelation{},
	&contentModel.ContentSignUp{},

	// 用户相关表
	&user.WxUser{},
	&patient.WxPatient{},
	&doctor.WxDoctor{},
	&redeemer.WxRedeemer{},

	// 机构科室表
	&departmentModel.Department{},
}

// MigrateWxAll 执行所有微信小程序相关表的迁移
func MigrateWxAll() error {
	fmt.Println("开始迁移所有微信小程序相关表...")

	if err := MigrateUser(); err != nil {
		return err
	}

	// 执行科室表结构迁移
	if err := MigrateDepartmentTables(); err != nil {
		return err
	}

	// 其他模型通过批量迁移
	if err := mysql.AutoMigrate(wxModelsToMigrate...); err != nil {
		log.Printf("批量迁移数据库失败: %v", err)
		return err
	}

	fmt.Println("所有微信小程序相关表结构迁移完成")
	return nil
}
