package hangzhou

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"
)

// 创建json-iterator实例
var jsoniter_ = jsoniter.ConfigCompatibleWithStandardLibrary

// GetJSON 获取JSON处理对象
func GetJSON() jsoniter.API {
	return jsoniter_
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	AccessToken string `json:"access_token"`
	LoginUser   struct {
		YhID int    `json:"yhid"` // 用户id
		YhMC string `json:"yhmc"` // 用户名称
	} `json:"loginUser"`
}

// YhjsItem 用户角色条目
type YhjsItem struct {
	YhjsID int    `json:"yhjsid"` // 用户角色id
	JsDj   string `json:"jsdj"`   // 角色等级
	WsjgID int    `json:"wsjgid"` // 卫生机构id
	WsjgMC string `json:"wsjgmc"` // 卫生机构名称
	JsBm   string `json:"jsbm"`   // 角色名称
}

// Client 杭州HIS系统API客户端
type Client struct {
	config     Config
	httpClient *http.Client
	mu         sync.RWMutex           // 用于保护accessToken
	tokenData  map[string]interface{} // 存储完整的token响应数据
}

// NewClient 创建杭州HIS系统API客户端
func NewClient(config Config) *Client {
	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SetHISAuthHeaders 设置HIS系统特定的认证头
// 根据token响应中的yhjs数组匹配wsjgid来设置对应的jsdj和yhjsid
func (c *Client) SetHISAuthHeaders(req *http.Request) {
	// 获取保存的token数据
	c.mu.RLock()
	tokenInfo := c.tokenData
	c.mu.RUnlock()
	// 使用配置中的集团ID(固定值)
	if c.config.CurrentJtid != "" {
		req.Header.Set("currentJtid", c.config.CurrentJtid)
	}

	// 使用配置中的卫生机构ID
	if c.config.CurrentWsjgid != "" {
		req.Header.Set("currentWsjgid", c.config.CurrentWsjgid)
	}

	req.Header.Set("currentJsdj", c.config.CurrentJsdj)
	req.Header.Set("currentYhjsid", c.config.CurrentYhjsid)

	// 默认使用配置中的角色等级和角色ID
	currentJsdj := c.config.CurrentJsdj
	currentYhjsid := c.config.CurrentYhjsid

	// 尝试从token信息中找到匹配当前wsjgid的用户角色
	if loginUser, ok := tokenInfo["loginUser"].(map[string]interface{}); ok {
		if yhjs, ok := loginUser["yhjs"].([]interface{}); ok && len(yhjs) > 0 {
			// 查找匹配当前wsjgid的角色
			targetWsjgid := c.config.CurrentWsjgid

			for _, role := range yhjs {
				if roleMap, ok := role.(map[string]interface{}); ok {
					// 获取角色中的wsjgid
					if roleWsjgid, ok := roleMap["wsjgid"].(float64); ok {
						// 将字符串类型的targetWsjgid转换为float64进行比较
						configWsjgid, _ := strconv.ParseFloat(targetWsjgid, 64)

						if roleWsjgid == configWsjgid {
							logx.Infof("[HangzhouHIS] 当前wsjgid: %s", targetWsjgid)
							logx.Infof("[HangzhouHIS] 找到匹配角色: %v", roleMap)
							// 找到匹配的角色，使用其jsdj和yhjsid
							if jsdj, ok := roleMap["jsdj"].(string); ok && jsdj != "" {
								currentJsdj = jsdj
								logx.Infof("[HangzhouHIS] 使用token中匹配的jsdj值: %s", jsdj)
							}

							if yhjsid, ok := roleMap["yhjsid"].(float64); ok {
								currentYhjsid = strconv.FormatFloat(yhjsid, 'f', 0, 64)
								logx.Infof("[HangzhouHIS] 使用token中匹配的yhjsid值: %s", currentYhjsid)
							}
							break
						}
					}
				}
			}
		}
	}

	// 设置角色等级
	if currentJsdj != "" {
		req.Header.Set("currentJsdj", currentJsdj)
	}

	// 设置用户角色ID
	if currentYhjsid != "" {
		req.Header.Set("currentYhjsid", currentYhjsid)
	}

	// 打印最终使用的头信息
	logx.Infof("[HangzhouHIS] 最终使用的认证头: currentJsdj=%s, currentJtid=%s, currentWsjgid=%s, currentYhjsid=%s",
		req.Header.Get("currentJsdj"), req.Header.Get("currentJtid"),
		req.Header.Get("currentWsjgid"), req.Header.Get("currentYhjsid"))
}

// GetConfig 获取客户端配置
func (c *Client) GetConfig() Config {
	return c.config
}

// BaseResponse 基础API响应结构
type BaseResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// GetAccessToken 获取访问令牌
func (c *Client) GetAccessToken(ctx context.Context) (string, error) {
	// 强制每次都重新获取token
	c.mu.Lock()
	// 清空旧token
	c.config.AccessToken = ""
	c.mu.Unlock()

	// 构建认证URL
	authURL := fmt.Sprintf("%s/api/v1/api-gateway/oauth/token?clientSecret=%s&yhzh=%s&yhmm=%s",
		c.config.BaseURL,
		c.config.ClientSecret,
		c.config.UserName,
		c.config.Password)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", authURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建认证请求失败: %w", err)
	}

	// 执行请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(req)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 认证请求失败: %s, 耗时: %v, 错误: %v", authURL, requestDuration, err)
		return "", fmt.Errorf("发送认证请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 读取认证响应失败: %s, 耗时: %v, 错误: %v", authURL, requestDuration, err)
		return "", fmt.Errorf("读取认证响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[HangzhouHIS] 收到认证响应: %s, 状态码: %d, 耗时: %v", authURL, resp.StatusCode, requestDuration)
	logx.Infof("[HangzhouHIS] 响应内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[HangzhouHIS] 认证请求失败: %s, 状态码: %d, 响应: %s", authURL, resp.StatusCode, string(body))
		return "", fmt.Errorf("认证请求失败: %s", string(body))
	}

	// 解析认证响应
	var loginResp LoginResponse
	if err := jsoniter_.Unmarshal(body, &loginResp); err != nil {
		logx.Errorf("[HangzhouHIS] 解析认证响应失败: %s, 错误: %v", authURL, err)
		return "", fmt.Errorf("解析认证响应失败: %w", err)
	}

	// 输出完整的登录响应，便于调试
	debugRespJson, _ := jsoniter_.MarshalToString(loginResp)
	logx.Infof("[HangzhouHIS] 登录响应详情: %s", debugRespJson)

	// 保存token
	c.mu.Lock()
	c.config.AccessToken = loginResp.AccessToken
	c.mu.Unlock()

	// 同时解析并保存完整的token响应数据
	var tokenData map[string]interface{}
	if err := jsoniter_.Unmarshal(body, &tokenData); err != nil {
		logx.Errorf("[HangzhouHIS] 解析完整token响应数据失败: %v", err)
	} else {
		// 保存token数据
		c.mu.Lock()
		c.tokenData = tokenData
		c.mu.Unlock()
	}

	// 检查登录响应和用户角色信息
	logx.Infof("[HangzhouHIS] 登录用户: %+v", loginResp.LoginUser)

	// 处理登录响应后的token信息，为使用SetHISAuthHeaders做准备
	var fullLoginResp map[string]interface{}
	if err := jsoniter_.Unmarshal(body, &fullLoginResp); err != nil {
		logx.Errorf("[HangzhouHIS] 解析完整登录响应失败: %v", err)
	} else {
		// 查看loginUser中的yhjs
		c.SetHISAuthHeaders(req)
	}

	logx.Infof("[HangzhouHIS] 继续执行，使用默认配置")

	// 保存登录用户信息
	c.config.UserID = loginResp.LoginUser.YhID
	c.config.LoginUserName = loginResp.LoginUser.YhMC

	// 记录登录用户信息
	logx.Infof("[HangzhouHIS] 登录用户信息: 用户ID=%d, 用户名称=%s",
		loginResp.LoginUser.YhID, loginResp.LoginUser.YhMC)

	return loginResp.AccessToken, nil
}

// Request 发送GET请求到HIS系统API，自动处理认证
func (c *Client) Request(ctx context.Context, path string, queryParams map[string]string) ([]byte, error) {
	// 获取访问令牌
	token, err := c.GetAccessToken(ctx)
	if err != nil {
		return nil, err
	}

	// 构建完整URL
	fullURL := fmt.Sprintf("%s%s", c.config.BaseURL, path)

	// 添加查询参数
	if len(queryParams) > 0 {
		params := url.Values{}
		for k, v := range queryParams {
			params.Add(k, v)
		}
		fullURL = fmt.Sprintf("%s?%s", fullURL, params.Encode())
	}

	logx.Infof("[HangzhouHIS] 发送请求: %s", fullURL)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("Authorization", "Bearer "+token)

	// 添加HIS系统特定的认证头
	c.SetHISAuthHeaders(req)

	// 打印发送的headers，便于调试
	headersMap := map[string]string{
		"Content-Type":  req.Header.Get("Content-Type"),
		"Authorization": "Bearer " + token[:10] + "...",
		"currentJsdj":   req.Header.Get("currentJsdj"),
		"currentJtid":   req.Header.Get("currentJtid"),
		"currentWsjgid": req.Header.Get("currentWsjgid"),
		"currentYhjsid": req.Header.Get("currentYhjsid"),
	}

	headersJson, _ := jsoniter_.MarshalToString(headersMap)
	logx.Infof("[HangzhouHIS] 完整请求头: %s", headersJson)
	logx.Infof("[HangzhouHIS] 请求头: Authorization=Bearer %s..., currentJsdj=%s, currentJtid=%s, currentWsjgid=%s, currentYhjsid=%s",
		token[:10], req.Header.Get("currentJsdj"), req.Header.Get("currentJtid"),
		req.Header.Get("currentWsjgid"), req.Header.Get("currentYhjsid"))

	// 执行请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(req)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 读取响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[HangzhouHIS] 收到响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)
	logx.Infof("[HangzhouHIS] 响应内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[HangzhouHIS] 请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return nil, fmt.Errorf("API请求失败: %s", string(body))
	}

	return body, nil
}

// 全局默认客户端实例
var DefaultClient *Client

// Init 初始化默认客户端
func Init(config Config) {
	DefaultClient = NewClient(config)
}
