package handler

import (
	"context"
	"net/http"

	"yekaitai/internal/modules/admin/service"
	userPointsModel "yekaitai/internal/modules/user_points/model"
	userPointsService "yekaitai/internal/modules/user_points/service"
	"yekaitai/internal/types"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// UserPointsDetailRequest 获取规则详情请求
type UserPointsDetailRequest struct {
	ID uint `path:"id"` // 规则ID
}

// UpdateUserPointsRequest 更新规则请求（handler层使用）
type UpdateUserPointsRequest struct {
	ID uint `path:"id"` // 规则ID
	userPointsModel.CreateCoinRuleRequest
}

// DeleteUserPointsRequest 删除规则请求
type DeleteUserPointsRequest struct {
	ID uint `path:"id"` // 规则ID
}

// UserPointsHandler 叶小币处理器
type UserPointsHandler struct {
	userPointsService *userPointsService.UserPointsService
	logService        *service.AdminOperationLogService
}

// NewUserPointsHandler 创建叶小币处理器
func NewUserPointsHandler() *UserPointsHandler {
	return &UserPointsHandler{
		userPointsService: userPointsService.NewUserPointsService(),
		logService:        service.NewAdminOperationLogService(),
	}
}

// GetGlobalConfig 获取全局配置
func (h *UserPointsHandler) GetGlobalConfig(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	config, err := h.userPointsService.GetGlobalConfig(ctx)
	if err != nil {
		logx.Errorf("获取全局配置失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 转换为响应格式
	response := &userPointsModel.CoinGlobalConfigResponse{
		ID:           config.ID,
		Enabled:      config.Enabled,
		ExchangeRate: config.ExchangeRate,
		CreatedAt:    config.CreatedAt,
		UpdatedAt:    config.UpdatedAt,
	}

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    response,
	})
}

// CreateGlobalConfig 创建全局配置
func (h *UserPointsHandler) CreateGlobalConfig(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req userPointsModel.CreateCoinGlobalConfigRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 验证参数
	if err := utils.ValidateStruct(&req); err != nil {
		logx.Errorf("参数验证失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	config, err := h.userPointsService.CreateGlobalConfig(ctx, &req)
	if err != nil {
		logx.Errorf("创建全局配置失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	// 转换为响应格式
	response := &userPointsModel.CoinGlobalConfigResponse{
		ID:           config.ID,
		Enabled:      config.Enabled,
		ExchangeRate: config.ExchangeRate,
		CreatedAt:    config.CreatedAt,
		UpdatedAt:    config.UpdatedAt,
	}

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "创建成功",
		Data:    response,
	})
}

// UpdateGlobalConfig 更新全局配置
func (h *UserPointsHandler) UpdateGlobalConfig(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req userPointsModel.UpdateCoinGlobalConfigRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 验证参数
	if err := utils.ValidateStruct(&req); err != nil {
		logx.Errorf("参数验证失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	err := h.userPointsService.UpdateGlobalConfig(ctx, &req)
	if err != nil {
		logx.Errorf("更新全局配置失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "更新成功",
	})
}

// CreateCoinRule 创建叶小币规则
func (h *UserPointsHandler) CreateCoinRule(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req userPointsModel.CreateCoinRuleRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 验证参数
	if err := utils.ValidateStruct(&req); err != nil {
		logx.Errorf("参数验证失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	rule, err := h.userPointsService.CreateCoinRule(ctx, &req)
	if err != nil {
		logx.Errorf("创建规则失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	// 转换为响应格式
	response := &userPointsModel.CoinRuleResponse{
		ID:               rule.ID,
		UserLevelID:      rule.UserLevelID,
		RuleType:         rule.RuleType,
		RuleName:         rule.RuleName,
		Description:      rule.Description,
		Enabled:          rule.Enabled,
		CoinsAwarded:     rule.CoinsAwarded,
		MinAmount:        rule.MinAmount,
		AmountThreshold:  rule.AmountThreshold,
		IsOneTime:        rule.IsOneTime,
		RequireShare:     rule.RequireShare,
		ActivityType:     rule.ActivityType,
		ExpiryPolicyType: rule.ExpiryPolicyType,
		CustomYears:      rule.CustomYears,
		CreatedAt:        rule.CreatedAt,
		UpdatedAt:        rule.UpdatedAt,
	}

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "创建成功",
		Data:    response,
	})
}

// UpdateCoinRule 更新叶小币规则
func (h *UserPointsHandler) UpdateCoinRule(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req UpdateUserPointsRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 构建更新请求
	updateReq := &userPointsModel.UpdateCoinRuleRequest{
		ID:               req.ID,
		UserLevelID:      req.UserLevelID,
		RuleType:         req.RuleType,
		RuleName:         req.RuleName,
		Description:      req.Description,
		Enabled:          req.Enabled,
		CoinsAwarded:     req.CoinsAwarded,
		MinAmount:        req.MinAmount,
		AmountThreshold:  req.AmountThreshold,
		IsOneTime:        req.IsOneTime,
		RequireShare:     req.RequireShare,
		ActivityType:     req.ActivityType,
		ExpiryPolicyType: req.ExpiryPolicyType,
		CustomYears:      req.CustomYears,
	}

	// 验证参数
	if err := utils.ValidateStruct(updateReq); err != nil {
		logx.Errorf("参数验证失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	err := h.userPointsService.UpdateCoinRule(ctx, updateReq)
	if err != nil {
		logx.Errorf("更新规则失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "更新成功",
	})
}

// DeleteCoinRule 删除叶小币规则
func (h *UserPointsHandler) DeleteCoinRule(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req DeleteUserPointsRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 删除规则
	err := h.userPointsService.DeleteCoinRule(ctx, req.ID)
	if err != nil {
		logx.Errorf("删除规则失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "删除成功",
	})
}

// GetCoinRule 获取叶小币规则详情
func (h *UserPointsHandler) GetCoinRule(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req UserPointsDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	rule, err := h.userPointsService.GetCoinRule(ctx, req.ID)
	if err != nil {
		logx.Errorf("获取规则失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 转换为响应格式
	response := &userPointsModel.CoinRuleResponse{
		ID:              rule.ID,
		UserLevelID:     rule.UserLevelID,
		RuleType:        rule.RuleType,
		RuleName:        rule.RuleName,
		Description:     rule.Description,
		Enabled:         rule.Enabled,
		CoinsAwarded:    rule.CoinsAwarded,
		MinAmount:       rule.MinAmount,
		AmountThreshold: rule.AmountThreshold,
		IsOneTime:       rule.IsOneTime,
		RequireShare:    rule.RequireShare,
		ActivityType:    rule.ActivityType,
		CreatedAt:       rule.CreatedAt,
		UpdatedAt:       rule.UpdatedAt,
	}

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    response,
	})
}

// GetCoinRuleList 获取叶小币规则列表
func (h *UserPointsHandler) GetCoinRuleList(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req userPointsModel.CoinRuleListRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	rules, total, err := h.userPointsService.GetCoinRuleList(ctx, &req)
	if err != nil {
		logx.Errorf("获取规则列表失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 转换为响应格式
	var responseList []*userPointsModel.CoinRuleResponse
	for _, rule := range rules {
		responseList = append(responseList, &userPointsModel.CoinRuleResponse{
			ID:              rule.ID,
			UserLevelID:     rule.UserLevelID,
			RuleType:        rule.RuleType,
			RuleName:        rule.RuleName,
			Description:     rule.Description,
			Enabled:         rule.Enabled,
			CoinsAwarded:    rule.CoinsAwarded,
			MinAmount:       rule.MinAmount,
			AmountThreshold: rule.AmountThreshold,
			IsOneTime:       rule.IsOneTime,
			RequireShare:    rule.RequireShare,
			ActivityType:    rule.ActivityType,
			CreatedAt:       rule.CreatedAt,
			UpdatedAt:       rule.UpdatedAt,
		})
	}

	response := map[string]interface{}{
		"list":  responseList,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    response,
	})
}

// SyncRulesToAllLevels 同步规则到所有等级
func (h *UserPointsHandler) SyncRulesToAllLevels(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	var req userPointsModel.SyncToAllLevelsRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("参数解析失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 验证参数
	if err := utils.ValidateStruct(&req); err != nil {
		logx.Errorf("参数验证失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	err := h.userPointsService.SyncRulesToAllLevels(ctx, &req)
	if err != nil {
		logx.Errorf("同步规则失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "同步成功",
	})
}

// GetUserLevels 获取所有用户等级
func (h *UserPointsHandler) GetUserLevels(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	levels, err := h.userPointsService.GetUserLevels(ctx)
	if err != nil {
		logx.Errorf("获取用户等级失败: %v", err)
		httpx.ErrorCtx(r.Context(), w, err)
		return
	}

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    levels,
	})
}

// GetRuleTypes 获取所有规则类型
func (h *UserPointsHandler) GetRuleTypes(w http.ResponseWriter, r *http.Request) {
	ruleTypes := h.userPointsService.GetRuleTypes()

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    ruleTypes,
	})
}

// GetExpiryTypes 获取所有期限类型
func (h *UserPointsHandler) GetExpiryTypes(w http.ResponseWriter, r *http.Request) {
	expiryTypes := h.userPointsService.GetExpiryTypes()

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    expiryTypes,
	})
}

// GetActivityTypes 获取所有活动类型
func (h *UserPointsHandler) GetActivityTypes(w http.ResponseWriter, r *http.Request) {
	activityTypes := h.userPointsService.GetActivityTypes()

	httpx.OkJsonCtx(r.Context(), w, types.Response{
		Code:    200,
		Message: "获取成功",
		Data:    activityTypes,
	})
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *UserPointsHandler) logAdminOperation(r *http.Request, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 模块=%s, 操作=%s", module, action)
}
