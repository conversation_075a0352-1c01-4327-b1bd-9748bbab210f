package migration

import (
	"fmt"
	"log"

	"yekaitai/internal/modules/area/model"
	"yekaitai/pkg/infra/mysql"
)

// 需要迁移的已开通地区模型
var enabledAreaModels = []interface{}{
	&model.EnabledArea{},
}

// MigrateEnabledAreaTables 执行已开通地区表结构迁移
func MigrateEnabledAreaTables() error {
	log.Println("开始执行已开通地区表结构迁移...")

	// 执行已开通地区表结构迁移
	db := mysql.Master()
	db.Set("gorm:table_options", "COMMENT='已开通地区表'").AutoMigrate(&model.EnabledArea{})

	// 其他模型通过批量迁移
	if err := mysql.AutoMigrate(enabledAreaModels...); err != nil {
		log.Printf("已开通地区表结构迁移失败: %v", err)
		return fmt.Errorf("已开通地区表结构迁移失败: %w", err)
	}

	log.Println("已开通地区表结构迁移完成")
	return nil
}
