package utils

import (
	"context"
	"regexp"
	"strconv"
)

// GetAdminIDFromContext 从上下文获取管理员ID
func GetAdminIDFromContext(ctx context.Context) string {
	if adminID, ok := ctx.Value("admin_id").(string); ok {
		return adminID
	}
	return "0" // 默认返回0
}

// GetAdminUintIDFromContext 从上下文获取管理员ID并转换为uint类型
func GetAdminUintIDFromContext(ctx context.Context) uint {
	adminIDStr := GetAdminIDFromContext(ctx)
	adminID, err := strconv.ParseUint(adminIDStr, 10, 32)
	if err != nil {
		return 0
	}
	return uint(adminID)
}

// IsValidPhoneNumber 验证手机号码格式
func IsValidPhoneNumber(phone string) bool {
	// 中国大陆手机号验证正则表达式
	pattern := `^1[3-9]\d{9}$`
	matched, _ := regexp.MatchString(pattern, phone)
	return matched
}

// IsValidIDCard 验证身份证号码格式
func IsValidIDCard(idCard string) bool {
	// 18位身份证号码验证
	pattern := `^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$`
	matched, _ := regexp.MatchString(pattern, idCard)
	return matched
}
