package types

import (
	"encoding/json"
	"fmt"
)

// FlexibleValue 灵活值类型，用于处理多种类型的JSON值
type FlexibleValue struct {
	RawData json.RawMessage // 原始JSON数据
	Value   interface{}     // 解析后的值
}

// UnmarshalJSON 自定义JSON解析方法
func (fv *FlexibleValue) UnmarshalJSON(data []byte) error {
	// 保存原始数据
	fv.RawData = make([]byte, len(data))
	copy(fv.RawData, data)

	// 尝试解析，保持原始类型
	var dynamic interface{}
	if err := json.Unmarshal(data, &dynamic); err != nil {
		return err
	}
	fv.Value = dynamic
	return nil
}

// MarshalJSON 自定义JSON序列化方法
func (fv FlexibleValue) MarshalJSON() ([]byte, error) {
	// 如果有原始数据，直接返回原始数据，保持类型不变
	if fv.RawData != nil {
		return fv.RawData, nil
	}

	// 如果没有原始数据，序列化Value字段
	if fv.Value == nil {
		return []byte("null"), nil
	}

	return json.Marshal(fv.Value)
}

// String 返回值的字符串表示
func (fv FlexibleValue) String() string {
	if fv.Value == nil {
		return ""
	}

	switch v := fv.Value.(type) {
	case string:
		return v
	case float64, int, int64:
		return fmt.Sprintf("%v", v)
	case bool:
		return fmt.Sprintf("%v", v)
	default:
		// 对于复杂类型，尝试序列化为JSON字符串
		data, err := json.Marshal(v)
		if err != nil {
			return fmt.Sprintf("%v", v)
		}
		return string(data)
	}
}

// AsString 将值作为字符串返回
func (fv FlexibleValue) AsString() (string, bool) {
	if fv.Value == nil {
		return "", false
	}

	if s, ok := fv.Value.(string); ok {
		return s, true
	}

	return fv.String(), false
}

// AsFloat 将值作为浮点数返回
func (fv FlexibleValue) AsFloat() (float64, bool) {
	if fv.Value == nil {
		return 0, false
	}

	switch v := fv.Value.(type) {
	case float64:
		return v, true
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case string:
		// 尝试将字符串解析为浮点数
		var f float64
		if _, err := fmt.Sscanf(v, "%f", &f); err == nil {
			return f, true
		}
	}

	return 0, false
}

// AsBool 将值作为布尔值返回
func (fv FlexibleValue) AsBool() (bool, bool) {
	if fv.Value == nil {
		return false, false
	}

	if b, ok := fv.Value.(bool); ok {
		return b, true
	}

	return false, false
}

// AsObject 将值作为对象/映射返回
func (fv FlexibleValue) AsObject() (map[string]interface{}, bool) {
	if fv.Value == nil {
		return nil, false
	}

	if obj, ok := fv.Value.(map[string]interface{}); ok {
		return obj, true
	}

	return nil, false
}

// IsNull 检查值是否为null
func (fv FlexibleValue) IsNull() bool {
	return fv.Value == nil
}

// Type 返回值的类型名称
func (fv FlexibleValue) Type() string {
	if fv.Value == nil {
		return "null"
	}

	switch fv.Value.(type) {
	case string:
		return "string"
	case float64, int, int64:
		return "number"
	case bool:
		return "boolean"
	case map[string]interface{}:
		return "object"
	case []interface{}:
		return "array"
	default:
		return fmt.Sprintf("%T", fv.Value)
	}
}
