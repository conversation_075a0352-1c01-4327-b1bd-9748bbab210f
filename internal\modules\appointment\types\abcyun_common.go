package types

// OperationResult 操作响应结果
type OperationResult struct {
	Code    int    `json:"code"`    // 状态码
	Message string `json:"message"` // 提示信息
}

// Department 部门信息
type Department struct {
	ID                string `json:"id"`                // ID
	Name              string `json:"name"`              // 名称
	Type              int    `json:"type"`              // 类型
	MainMedicalName   string `json:"mainMedicalName"`   // 主要医疗名称
	SecondMedicalName string `json:"secondMedicalName"` // 次要医疗名称
}

// PatientAge 患者年龄
type PatientAge struct {
	Year  int `json:"year"`  // 年
	Month int `json:"month"` // 月
	Day   int `json:"day"`   // 日
}

// AbcYunRequest 基础请求结构体
type AbcYunRequest struct {
	ClinicID string `form:"clinicId,optional"` // 诊所ID，可选
}

// AbcYunResponse 通用响应结构体
type AbcYunResponse struct {
	Code    int         `json:"code"`    // 状态码
	Message string      `json:"message"` // 提示信息
	Data    interface{} `json:"data"`    // 响应数据
}

// Supplier 供应商信息
type Supplier struct {
	ID   string `json:"id"`   // 供应商ID
	Name string `json:"name"` // 供应商名称
}
