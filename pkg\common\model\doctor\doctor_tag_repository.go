package doctor

import (
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// DoctorTagRepository 医生标签关系仓库接口
type DoctorTagRepository interface {
	// 批量添加医生标签关系
	AddDoctorTags(doctorID uint, tagIDs []uint) error
	// 删除医生的所有标签关系
	DeleteDoctorTags(doctorID uint) error
	// 获取医生的标签列表
	GetDoctorTags(doctorID uint) ([]user.Tag, error)
	// 根据标签ID查找相关医生
	FindDoctorsByTagID(tagID uint) ([]uint, error)
}

// doctorTagRepository 医生标签关系仓库实现
type doctorTagRepository struct {
	db *gorm.DB // 保留db字段以兼容接口，但不在方法中使用
}

// NewDoctorTagRepository 创建医生标签关系仓库
func NewDoctorTagRepository(db *gorm.DB) DoctorTagRepository {
	return &doctorTagRepository{
		db: db,
	}
}

// AddDoctorTags 批量添加医生标签关系
func (r *doctorTagRepository) AddDoctorTags(doctorID uint, tagIDs []uint) error {
	// 开启事务
	tx := mysql.Master().Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 查找医生
	var doctor WxDoctor
	if err := tx.Where("doctor_id = ?", doctorID).First(&doctor).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 先清除当前的标签关联
	if err := tx.Model(&doctor).Association("Tags").Clear(); err != nil {
		tx.Rollback()
		return err
	}

	// 如果没有新的标签ID，则直接提交事务
	if len(tagIDs) == 0 {
		return tx.Commit().Error
	}

	// 查找所有标签
	var tags []user.Tag
	if err := tx.Where("id IN ?", tagIDs).Find(&tags).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 添加新的标签关联
	if err := tx.Model(&doctor).Association("Tags").Append(&tags); err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit().Error
}

// DeleteDoctorTags 删除医生的所有标签关系
func (r *doctorTagRepository) DeleteDoctorTags(doctorID uint) error {
	var doctor WxDoctor
	if err := mysql.Master().Where("doctor_id = ?", doctorID).First(&doctor).Error; err != nil {
		return err
	}
	return mysql.Master().Model(&doctor).Association("Tags").Clear()
}

// GetDoctorTags 获取医生的标签列表
func (r *doctorTagRepository) GetDoctorTags(doctorID uint) ([]user.Tag, error) {
	var doctor WxDoctor
	err := mysql.Slave().Preload("Tags").Where("doctor_id = ?", doctorID).First(&doctor).Error
	if err != nil {
		return nil, err
	}
	return doctor.Tags, nil
}

// FindDoctorsByTagID 根据标签ID查找相关医生
func (r *doctorTagRepository) FindDoctorsByTagID(tagID uint) ([]uint, error) {
	var doctorIDs []uint
	err := mysql.Slave().Model(&DoctorTag{}).Where("tag_id = ?", tagID).Pluck("doctor_id", &doctorIDs).Error
	return doctorIDs, err
}
