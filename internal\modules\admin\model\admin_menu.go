package model

import (
	"time"
)

// AdminMenu 管理菜单表
type AdminMenu struct {
	MenuID    uint      `json:"menu_id" gorm:"primaryKey;autoIncrement;comment:菜单ID"`
	ParentID  uint      `json:"parent_id" gorm:"not null;default:0;comment:父菜单ID"`
	Title     string    `json:"title" gorm:"type:varchar(50);not null;comment:菜单名称"`
	Icon      string    `json:"icon" gorm:"type:varchar(50);comment:菜单图标"`
	Path      string    `json:"path" gorm:"type:varchar(100);comment:前端路由路径"`
	Component string    `json:"component" gorm:"type:varchar(100);comment:前端组件路径"`
	PermID    int       `json:"perm_id" gorm:"not null;default:0;comment:权限ID"`
	Sort      int       `json:"sort" gorm:"not null;default:0;comment:排序"`
	IsShow    bool      `json:"is_show" gorm:"not null;default:1;comment:是否显示：0-隐藏，1-显示"`
	Status    int       `json:"status" gorm:"not null;default:1;comment:状态：0-禁用，1-启用"`
	CreatedAt time.Time `json:"created_at" gorm:"not null;comment:创建时间"`
	UpdatedAt time.Time `json:"updated_at" gorm:"not null;comment:更新时间"`
}

// TableName 设置AdminMenu表名
func (AdminMenu) TableName() string {
	return "admin_menu"
}
