package email

import (
	"fmt"
	"time"
)

// EmailService 邮件服务
type EmailService struct {
	client *EmailClient
}

// NewEmailService 创建邮件服务
func NewEmailService(config *EmailConfig) *EmailService {
	return &EmailService{
		client: NewEmailClient(config),
	}
}

// AppointmentInfo 预约信息
type AppointmentInfo struct {
	Name        string `json:"name"`         // 姓名
	Gender      string `json:"gender"`       // 性别
	BirthDate   string `json:"birth_date"`   // 出生年月日
	Phone       string `json:"phone"`        // 电话号码
	Email       string `json:"email"`        // 邮箱
	SubmitTime  string `json:"submit_time"`  // 提交时间
}

// SendAppointmentEmail 发送预约邮件
func (s *EmailService) SendAppointmentEmail(info *AppointmentInfo, recipientEmail string) error {
	// 验证邮箱地址
	if !IsValidEmail(recipientEmail) {
		return fmt.Errorf("收件人邮箱地址无效: %s", recipientEmail)
	}

	// 构建邮件内容
	subject := "填写预约 - 新的预约信息"
	content := s.buildAppointmentEmailContent(info)

	// 创建邮件消息
	message := &EmailMessage{
		To:      []string{recipientEmail},
		Subject: subject,
		Body:    content,
		IsHTML:  true,
	}

	// 发送邮件
	return s.client.SendEmail(message)
}

// buildAppointmentEmailContent 构建预约邮件内容
func (s *EmailService) buildAppointmentEmailContent(info *AppointmentInfo) string {
	// 格式化性别显示
	genderText := info.Gender
	if genderText == "1" || genderText == "male" {
		genderText = "男"
	} else if genderText == "2" || genderText == "female" {
		genderText = "女"
	}

	// 构建邮件内容
	contentHTML := fmt.Sprintf(`
		<h2>新的预约信息</h2>
		<table style="border-collapse: collapse; width: 100%%; margin: 20px 0;">
			<tr style="background-color: #f9f9f9;">
				<td style="border: 1px solid #ddd; padding: 12px; font-weight: bold; width: 120px;">姓名</td>
				<td style="border: 1px solid #ddd; padding: 12px;">%s</td>
			</tr>
			<tr>
				<td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">性别</td>
				<td style="border: 1px solid #ddd; padding: 12px;">%s</td>
			</tr>
			<tr style="background-color: #f9f9f9;">
				<td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">出生年月日</td>
				<td style="border: 1px solid #ddd; padding: 12px;">%s</td>
			</tr>
			<tr>
				<td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">电话号码</td>
				<td style="border: 1px solid #ddd; padding: 12px;">%s</td>
			</tr>
			<tr style="background-color: #f9f9f9;">
				<td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">邮箱</td>
				<td style="border: 1px solid #ddd; padding: 12px;">%s</td>
			</tr>
			<tr>
				<td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">提交时间</td>
				<td style="border: 1px solid #ddd; padding: 12px;">%s</td>
			</tr>
		</table>
		<p style="margin-top: 20px; color: #666; font-size: 14px;">
			请及时处理该预约信息，如有疑问请联系相关负责人。
		</p>
	`, info.Name, genderText, info.BirthDate, info.Phone, info.Email, info.SubmitTime)

	return BuildHTMLTemplate("填写预约", contentHTML)
}

// SendTestEmail 发送测试邮件
func (s *EmailService) SendTestEmail(recipientEmail string) error {
	// 验证邮箱地址
	if !IsValidEmail(recipientEmail) {
		return fmt.Errorf("收件人邮箱地址无效: %s", recipientEmail)
	}

	// 构建测试邮件内容
	subject := "邮件服务测试"
	content := BuildHTMLTemplate("邮件服务测试", `
		<h2>邮件服务测试成功</h2>
		<p>这是一封测试邮件，用于验证邮件服务是否正常工作。</p>
		<p><strong>发送时间：</strong>` + time.Now().Format("2006-01-02 15:04:05") + `</p>
		<p>如果您收到这封邮件，说明邮件服务配置正确。</p>
	`)

	// 创建邮件消息
	message := &EmailMessage{
		To:      []string{recipientEmail},
		Subject: subject,
		Body:    content,
		IsHTML:  true,
	}

	// 发送邮件
	return s.client.SendEmail(message)
}

// ValidateService 验证邮件服务
func (s *EmailService) ValidateService() error {
	return s.client.ValidateConfig()
}

// TestConnection 测试邮件服务器连接
func (s *EmailService) TestConnection() error {
	return s.client.TestConnection()
}

// SendPlainTextEmail 发送纯文本邮件
func (s *EmailService) SendPlainTextEmail(to []string, subject, content string) error {
	// 验证收件人邮箱地址
	for _, email := range to {
		if !IsValidEmail(email) {
			return fmt.Errorf("收件人邮箱地址无效: %s", email)
		}
	}

	// 创建邮件消息
	message := &EmailMessage{
		To:      to,
		Subject: subject,
		Body:    content,
		IsHTML:  false,
	}

	// 发送邮件
	return s.client.SendEmail(message)
}

// SendHTMLEmail 发送HTML邮件
func (s *EmailService) SendHTMLEmail(to []string, subject, htmlContent string) error {
	// 验证收件人邮箱地址
	for _, email := range to {
		if !IsValidEmail(email) {
			return fmt.Errorf("收件人邮箱地址无效: %s", email)
		}
	}

	// 创建邮件消息
	message := &EmailMessage{
		To:      to,
		Subject: subject,
		Body:    htmlContent,
		IsHTML:  true,
	}

	// 发送邮件
	return s.client.SendEmail(message)
}
