package wanliniu

import (
	"time"

	jsoniter "github.com/json-iterator/go"
)

var jsonLib = jsoniter.ConfigCompatibleWithStandardLibrary

// WanLiNiuTime 万里牛时间类型，处理时间戳转换
type WanLiNiuTime struct {
	time.Time
}

// UnmarshalJSON 自定义JSON反序列化
func (wt *WanLiNiuTime) UnmarshalJSON(data []byte) error {
	// 尝试解析为时间戳（毫秒）
	var timestamp int64
	if err := jsonLib.Unmarshal(data, &timestamp); err == nil {
		if timestamp > 0 {
			wt.Time = time.Unix(timestamp/1000, (timestamp%1000)*1000000)
		} else {
			wt.Time = time.Time{}
		}
		return nil
	}

	// 尝试解析为字符串格式
	var timeStr string
	if err := jsonLib.Unmarshal(data, &timeStr); err == nil {
		if timeStr == "" {
			wt.Time = time.Time{}
			return nil
		}

		// 万里牛时间格式: "2023-12-10 13:45:17"
		t, err := time.Parse("2006-01-02 15:04:05", timeStr)
		if err != nil {
			wt.Time = time.Time{}
			return nil // 忽略解析错误，使用零值
		}
		wt.Time = t
		return nil
	}

	// 如果都失败，使用零值
	wt.Time = time.Time{}
	return nil
}

// MarshalJSON 自定义JSON序列化
func (wt WanLiNiuTime) MarshalJSON() ([]byte, error) {
	if wt.Time.IsZero() {
		return jsonLib.Marshal(0)
	}
	return jsonLib.Marshal(wt.Time.Unix() * 1000) // 返回毫秒时间戳
}

// String 返回字符串表示
func (wt WanLiNiuTime) String() string {
	if wt.Time.IsZero() {
		return ""
	}
	return wt.Time.Format("2006-01-02 15:04:05")
}

// ToTime 转换为标准time.Time
func (wt WanLiNiuTime) ToTime() time.Time {
	return wt.Time
}

// BaseResponse 万里牛API基础响应结构
type BaseResponse struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg,omitempty"`
}

// TradesPushResponse 订单推送响应结构
type TradesPushResponse struct {
	Code int                  `json:"code"`
	Data TradesPushDataResult `json:"data"`
	Msg  string               `json:"msg,omitempty"`
}

// TradesPushDataResult 订单推送数据结果
type TradesPushDataResult struct {
	Success   bool   `json:"success"`    // 业务执行是否成功
	ErrorCode string `json:"error_code"` // 错误代码
	ErrorMsg  string `json:"error_msg"`  // 错误信息
}

// GoodsListResponse 商品列表响应
type GoodsListResponse struct {
	Code int           `json:"code"`
	Data []GoodsDetail `json:"data"`
}

// GoodsDetail 商品详细信息
type GoodsDetail struct {
	Abbreviation                 string            `json:"abbreviation"`                    // 简称
	Adult                        bool              `json:"adult"`                           // 是否成人用品
	ArticleNumber                string            `json:"article_number"`                  // 货号
	BatteryType                  string            `json:"battery_type"`                    // 电池类型
	BrandName                    string            `json:"brand_name"`                      // 品牌
	CatagoryID                   string            `json:"catagory_id"`                     // 分类ID
	CatagoryName                 string            `json:"catagory_name"`                   // 分类名称
	ContainsLiquid               bool              `json:"contains_liquid"`                 // 是否包含液体
	DefaultSuplyCode             string            `json:"default_suply_code"`              // 默认供应商编码
	DefaultSuplyName             string            `json:"default_suply_name"`              // 默认供应商名称
	ElectronicCigarette          bool              `json:"electronic_cigarette"`            // 是否包含电子烟
	ElectronicCigaretteType      string            `json:"electronic_cigarette_type"`       // 电子烟类型
	ErpSuppliers                 []ErpSupplier     `json:"erp_suppliers"`                   // 供应商列表
	Expiration                   int               `json:"expiration"`                      // 保质期
	General                      bool              `json:"general"`                         // 是否普货
	GoodsChineseName             string            `json:"goods_chinese_name"`              // 报关中文名
	GoodsCode                    string            `json:"goods_code"`                      // 商品编码
	GoodsCustomCode              string            `json:"goods_custom_code"`               // 报关编码
	GoodsCustomPrice             float64           `json:"goods_custom_price"`              // 报关价格(美元)
	GoodsCustomWeight            float64           `json:"goods_custom_weight"`             // 报关重量(g)
	GoodsEnglishName             string            `json:"goods_english_name"`              // 报关英文名
	GoodsMark                    string            `json:"goods_mark"`                      // 商品标记
	GoodsName                    string            `json:"goods_name"`                      // 商品名称
	InputTaxRatio                string            `json:"input_tax_ratio"`                 // 销项税
	IsBattery                    bool              `json:"is_battery"`                      // 是否为纯电池
	IsContainsBattery            bool              `json:"is_contains_battery"`             // 是否包含电池
	IsContainsNonliquidCosmetics bool              `json:"is_contains_nonliquid_cosmetics"` // 是否含非液体化妆品
	IsFragile                    bool              `json:"is_fragile"`                      // 是否易碎
	IsOpenSn                     bool              `json:"is_open_sn"`                      // 是否开启序列号管理
	LengthUnitName               string            `json:"length_unit_name"`                // 长度单位
	LiquidType                   string            `json:"liquid_type"`                     // 液体类型
	ManufacturerName             string            `json:"manufacturer_name"`               // 生产商
	ModifyTime                   WanLiNiuTime      `json:"modify_time"`                     // 修改时间，使用自定义时间类型
	OpenBatch                    bool              `json:"open_batch"`                      // 是否开启批次
	OpenBatchDate                bool              `json:"open_batch_date"`                 // 是否开启批次效期管理
	OpenSn                       bool              `json:"open_sn"`                         // 是否启用序列号
	Pic                          string            `json:"pic"`                             // 商品图片
	Pic2                         string            `json:"pic2"`                            // 商品图片2
	Pic3                         string            `json:"pic3"`                            // 商品图片3
	Pic4                         string            `json:"pic4"`                            // 商品图片4
	Properties                   map[string]string `json:"properties"`                      // 自定义商品属性
	PurchaseNum                  float64           `json:"purchase_num"`                    // 采购数量
	PurchaseTypeName             string            `json:"purchase_type_name"`              // 采购类型
	Remark                       string            `json:"remark"`                          // 备注
	Specs                        []GoodsSpec       `json:"specs"`                           // 规格集
	Status                       int               `json:"status"`                          // 商品状态 0停用 1启用
	Stive                        bool              `json:"stive"`                           // 是否包含粉末
	StiveType                    string            `json:"stive_type"`                      // 粉末类型
	SysGoodsUID                  string            `json:"sys_goods_uid"`                   // 系统商品uid
	TagPrice                     float64           `json:"tag_price"`                       // 吊牌价
	TaxRatio                     string            `json:"tax_ratio"`                       // 进项税
	UnitName                     string            `json:"unit_name"`                       // 单位
	VolumeUnitName               string            `json:"volume_unit_name"`                // 体积单位
	WeightUnitName               string            `json:"weight_unit_name"`                // 重量单位
}

// ErpSupplier 供应商信息
type ErpSupplier struct {
	Address         string           `json:"address"`           // 地址
	Area            string           `json:"area"`              // 区域
	City            string           `json:"city"`              // 城市
	Contact         string           `json:"contact"`           // 联系人
	Email           string           `json:"email"`             // 邮箱
	ErpSupplierExts []ErpSupplierExt `json:"erp_supplier_exts"` // 供应商扩展信息
	IsDefault       int              `json:"is_default"`        // 是否默认
	IsInvalid       int              `json:"is_invalid"`        // 是否无效
	Mobile          string           `json:"mobile"`            // 手机
	Province        string           `json:"province"`          // 省份
	Remark          string           `json:"remark"`            // 备注
	Status          int              `json:"status"`            // 状态
	SupplierCode    string           `json:"supplier_code"`     // 供应商编码
	SupplierName    string           `json:"supplier_name"`     // 供应商名称
	SupplierUID     string           `json:"supplier_uid"`      // 供应商UID
}

// ErpSupplierExt 供应商扩展信息
type ErpSupplierExt struct {
	ProCode string `json:"pro_code"` // 属性编码
	ProName string `json:"pro_name"` // 属性名称
	Value   string `json:"value"`    // 属性值
}

// GoodsSpec 商品规格（根据实际响应调整）
type GoodsSpec struct {
	BarCode        string        `json:"barcode"`         // 条码（注意字段名）
	BarCodes       string        `json:"barcodes"`        // 条码集合
	Height         float64       `json:"height"`          // 高度
	Length         float64       `json:"length"`          // 长度
	Pic            string        `json:"pic"`             // 规格图片
	PrimePrice     float64       `json:"prime_price"`     // 成本价
	Props          []interface{} `json:"props"`           // 属性集合
	SalePrice      float64       `json:"sale_price"`      // 销售价
	Spec1          string        `json:"spec1"`           // 规格1
	Spec2          string        `json:"spec2"`           // 规格2
	SpecCode       string        `json:"spec_code"`       // 规格编码
	Status         int           `json:"status"`          // 状态
	SysSpecUID     string        `json:"sys_spec_uid"`    // 系统规格UID
	Weight         float64       `json:"weight"`          // 重量
	WholesalePrice float64       `json:"wholesale_price"` // 批发价
	Width          float64       `json:"width"`           // 宽度
}

// SpecDetailValue 规格明细值
type SpecDetailValue struct {
	PropID    string `json:"prop_id"`    // 属性ID
	PropName  string `json:"prop_name"`  // 属性名称
	PropValue string `json:"prop_value"` // 属性值
}

// GoodsQueryParams 商品查询参数
type GoodsQueryParams struct {
	Page           int        `json:"page"`            // 页码
	Limit          int        `json:"limit"`           // 每页大小
	SpecCode       string     `json:"spec_code"`       // 规格编码
	ItemCode       string     `json:"item_code"`       // 商品编码
	ModifyTime     *time.Time `json:"modify_time"`     // 修改时间
	BarCode        string     `json:"bar_code"`        // 条码
	AllStatus      bool       `json:"all_status"`      // 是否查询所有状态
	EndTime        *time.Time `json:"end_time"`        // 修改结束时间
	NeedProperties bool       `json:"need_properties"` // 是否需要查询商品自定义属性
}

// ===================== 类目推送相关模型 =====================

// CategoryPushRequest 类目推送请求
type CategoryPushRequest struct {
	Categories []Category `json:"categories"`
}

// Category 类目信息
type Category struct {
	CategoryID string `json:"category_id"` // 类目编号（不能为空）
	Name       string `json:"name"`        // 类目名称（不能为空）
	ParentID   string `json:"parent_id"`   // 父类类别编号，如果是子类必填
	ShopNick   string `json:"shop_nick"`   // 在ERP中注册的卖家（店铺）昵称（不能为空）
	SortOrder  int    `json:"sort_order"`  // 索引
	Status     int    `json:"status"`      // 状态: 1：使用中；0：已删除
}

// ===================== 订单推送相关模型 =====================

// TradesPushRequest 订单推送请求
type TradesPushRequest struct {
	Trades []Trade `json:"trades"`
}

// Trade 订单信息 - 只保留万里牛ERP必须的字段
type Trade struct {
	// 必填字段
	CreateTime       string       `json:"create_time"`       // 交易创建时间（不能为空）格式: yyyy-MM-dd HH:mm:ss
	ModifyTime       string       `json:"modify_time"`       // 交易最新修改时间，格式: yyyy-MM-dd HH:mm:ss
	Orders           []TradeOrder `json:"orders"`            // 交易明细集（不能为空）
	ReceiverAddress  string       `json:"receiver_address"`  // 收件人详细地址（不能为空）
	ReceiverName     string       `json:"receiver_name"`     // 收件人姓名（不能为空）
	ReceiverCity     string       `json:"receiver_city"`     // 收件人市（不能为空）
	ReceiverMobile   string       `json:"receiver_mobile"`   // 收件人手机（不能为空）
	ReceiverProvince string       `json:"receiver_province"` // 收件人省（不能为空）
	ShopNick         string       `json:"shop_nick"`         // 在ERP中注册的卖家（店铺）昵称（不能为空）
	Status           int          `json:"status"`            // 线上交易状态（不能为空）
	TradeID          string       `json:"trade_id"`          // 第三方交易号（不能为空）

	// 根据错误信息，buyer字段也是必填的
	Buyer string `json:"buyer"` // 买家（必填）

	// 可选但常用字段
	BuyerMobile string  `json:"buyer_mobile,omitempty"` // 订购人手机
	Payment     float64 `json:"payment,omitempty"`      // 应收款
	TotalFee    float64 `json:"total_fee,omitempty"`    // 总金额
	PostFee     float64 `json:"post_fee,omitempty"`     // 邮费
	PayTime     string  `json:"pay_time,omitempty"`     // 交易付款时间
	PayType     string  `json:"pay_type,omitempty"`     // 支付类型
	TradeType   int     `json:"trade_type,omitempty"`   // 订单类型

	// 其他可选字段
	BackImg               string  `json:"back_img,omitempty"`                // 身份证背面图片链接
	BuyerEmail            string  `json:"buyer_email,omitempty"`             // 买家邮件
	BuyerMessage          string  `json:"buyer_message,omitempty"`           // 买家留言
	CheckStorage          int     `json:"check_storage,omitempty"`           // 校验仓库标识
	ClearanceCode         string  `json:"clearance_code,omitempty"`          // 清关单号
	ClearanceDiscount     float64 `json:"clearance_discount,omitempty"`      // 优惠-非现金抵扣金额
	ClearanceItemAmount   float64 `json:"clearance_item_amount,omitempty"`   // 商品总金额
	CpfNo                 string  `json:"cpf_no,omitempty"`                  // 个人税号
	CurrencyCode          string  `json:"currency_code,omitempty"`           // 金额币种
	DeliveryCode          string  `json:"delivery_code,omitempty"`           // 外贸承运商编码
	DiscountFee           float64 `json:"discount_fee,omitempty"`            // 优惠金额
	EncryptAddress        string  `json:"encrypt_address,omitempty"`         // 收件人地址密文信息
	EncryptConsignee      string  `json:"encrypt_consignee,omitempty"`       // 收件人密文信息
	EncryptConsigneePhone string  `json:"encrypt_consignee_phone,omitempty"` // 收件人手机号密文信息
	EncryptFlag           int     `json:"encrypt_flag,omitempty"`            // 是否加密标识
	EndTime               string  `json:"end_time,omitempty"`                // 交易结束时间
	ExpressCode           string  `json:"express_code,omitempty"`            // 快递单号
	Extend                string  `json:"extend,omitempty"`                  // 扩展数据
	FaceImg               string  `json:"face_img,omitempty"`                // 身份证正面图片链接
	HasRefund             int     `json:"has_refund,omitempty"`              // 退款退货标记
	IdentityName          string  `json:"identity_name,omitempty"`           // 身份证上的名称
	IdentityNum           string  `json:"identity_num,omitempty"`            // 身份证号
	Invoice               string  `json:"invoice,omitempty"`                 // 发票信息
	InvoiceAddress        string  `json:"invoice_address,omitempty"`         // 发票地址
	InvoiceBank           string  `json:"invoice_bank,omitempty"`            // 发票开户行
	InvoiceBankAccount    string  `json:"invoice_bank_account,omitempty"`    // 发票银行账号
	InvoiceEmail          string  `json:"invoice_email,omitempty"`           // 发票邮箱
	InvoiceMobile         string  `json:"invoice_mobile,omitempty"`          // 发票电话
	InvoiceRemark         string  `json:"invoice_remark,omitempty"`          // 发票备注
	InvoiceTax            string  `json:"invoice_tax,omitempty"`             // 发票税号
	InvoiceType           int     `json:"invoice_type,omitempty"`            // 发票类型
	InvoicingType         int     `json:"invoicing_type,omitempty"`          // 开票形式
	LastShipTime          string  `json:"last_ship_time,omitempty"`          // 预计发货时间
	LogisticsCode         string  `json:"logistics_code,omitempty"`          // 快递公司编码
	OriginSourceCode      string  `json:"origin_source_code,omitempty"`      // 订单来源平台编码
	OriginSourceOrderID   string  `json:"origin_source_order_id,omitempty"`  // 原始平台单号
	OriginSourceShopNick  string  `json:"origin_source_shop_nick,omitempty"` // 原始店铺昵称
	PackageName           string  `json:"package_name,omitempty"`            // 集包地名称
	PayDeclareAmount      float64 `json:"pay_declare_amount,omitempty"`      // 欠款金额
	PayNo                 string  `json:"pay_no,omitempty"`                  // 支付交易号
	PdfURL                string  `json:"pdf_url,omitempty"`                 // 面单链接
	PrintConfig           string  `json:"print_config,omitempty"`            // 订单类型
	ReceiverArea          string  `json:"receiver_area,omitempty"`           // 收件人区/县
	ReceiverCountry       string  `json:"receiver_country,omitempty"`        // 收件人国家
	ReceiverPhone         string  `json:"receiver_phone,omitempty"`          // 收件人电话
	ReceiverZip           string  `json:"receiver_zip,omitempty"`            // 收件人邮编
	RouteCode             string  `json:"route_code,omitempty"`              // 三段码
	SalesMobile           string  `json:"sales_mobile,omitempty"`            // 业务员手机号
	SellerMemo            string  `json:"seller_memo,omitempty"`             // 卖家备注
	ServiceFee            float64 `json:"service_fee,omitempty"`             // 服务费
	ShippingTime          string  `json:"shipping_time,omitempty"`           // 交易发货时间
	ShippingType          int     `json:"shipping_type,omitempty"`           // 发货类型
	StoreID               string  `json:"store_id,omitempty"`                // 门店ID
}

// TradeOrder 订单明细 - 只保留万里牛ERP必须的字段
type TradeOrder struct {
	// 必填字段
	ItemID    string `json:"item_id"`    // 商品编号（不能为空）
	ItemTitle string `json:"item_title"` // 商品标题（不能为空）
	Size      int    `json:"size"`       // 商品数量（不能为空）
	SkuID     string `json:"sku_id"`     // 规格编号（不能为空）

	// 可选字段
	BatchDate     string  `json:"batch_date,omitempty"`     // 批次生产日期
	BatchNo       string  `json:"batch_no,omitempty"`       // 批次信息
	ExpiredDate   string  `json:"expired_date,omitempty"`   // 批次失效日期
	Extend        string  `json:"extend,omitempty"`         // 扩展数据
	GxPayment     float64 `json:"gx_payment,omitempty"`     // 供销payment
	GxPrice       float64 `json:"gx_price,omitempty"`       // 供销price
	HasRefund     int     `json:"has_refund,omitempty"`     // 明细是否退款
	ImageURL      string  `json:"image_url,omitempty"`      // 商品图片地址
	ItemCode      string  `json:"item_code,omitempty"`      // 外部商家编码
	LogisticsCode string  `json:"logistics_code,omitempty"` // 买家自选物流编码
	LogisticsName string  `json:"logistics_name,omitempty"` // 买家所选物流昵称
	OrderAttr     string  `json:"order_attr,omitempty"`     // 明细备注
	OrderID       string  `json:"order_id,omitempty"`       // 子交易单号
	Payment       float64 `json:"payment,omitempty"`        // 商品实付
	Price         float64 `json:"price,omitempty"`          // 商品现价
	SkuCode       string  `json:"sku_code,omitempty"`       // 外部规格编码
	SkuTitle      string  `json:"sku_title,omitempty"`      // 规格标题
	Snapshot      string  `json:"snapshot,omitempty"`       // 商品交易快照地址
	Status        int     `json:"status,omitempty"`         // 明细线上状态
	TradeID       string  `json:"trade_id,omitempty"`       // 第三方交易号
}

// ===================== 库存查询相关模型 =====================

// InventoryBatchRequest 批量库存查询请求
type InventoryBatchRequest struct {
	Page        int        `json:"page"`                   // 当前页，默认为1
	Limit       int        `json:"limit"`                  // 每页条数，最大值：200
	Start       *time.Time `json:"start"`                  // 库存修改的开始时间
	End         *time.Time `json:"end"`                    // 库存修改的结束时间
	StorageCode string     `json:"storage_code,omitempty"` // ERP中的仓库编码
}

// InventorySingleRequest 单笔库存查询请求
type InventorySingleRequest struct {
	ShopType    int    `json:"shop_type"`              // 店铺类型，B2C平台：100
	ShopNick    string `json:"shop_nick"`              // 万里牛ERP中B2C店铺昵称
	ItemID      string `json:"item_id"`                // 商品编号
	SkuID       string `json:"sku_id,omitempty"`       // 规格ID（如果商品含规格，则必填）
	StorageCode string `json:"storage_code,omitempty"` // ERP中的仓库编码
}

// InventoryResponse 库存查询响应
type InventoryResponse struct {
	Code int             `json:"code"`
	Data InventoryResult `json:"data"`
}

// InventoryResult 库存查询结果
type InventoryResult struct {
	ErrorCode string `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
	Response  string `json:"response"`
	Success   bool   `json:"success"`
}

// ===================== 订单状态查询相关模型 =====================

// TradeStatusRequest 订单状态查询请求
type TradeStatusRequest struct {
	ShopType int    `json:"shop_type"` // 店铺类型，B2C 平台店铺类型：100
	ShopNick string `json:"shop_nick"` // 万里牛B2C店铺昵称
	TradeIDs string `json:"trade_ids"` // 订单号，多个订单以半角逗号相隔
}

// ===================== 售后单推送相关模型 =====================

// RefundPushRequest 售后单推送请求
type RefundPushRequest struct {
	Refund RefundInfo `json:"refund"`
}

// RefundInfo 售后单信息
type RefundInfo struct {
	Buyer         string          `json:"buyer,omitempty"`          // 买家昵称
	Create        string          `json:"create"`                   // 创建时间，格式: yyyy-MM-dd HH:mm:ss
	EndTime       string          `json:"end_time,omitempty"`       // 完成时间，格式: yyyy-MM-dd HH:mm:ss
	ExpressCode   string          `json:"express_code,omitempty"`   // 退货快递单号
	Items         []RefundItem    `json:"items,omitempty"`          // 退换货明细
	LogisticsName string          `json:"logistics_name,omitempty"` // 物流公司
	Modified      string          `json:"modified,omitempty"`       // 修改时间，格式: yyyy-MM-dd HH:mm:ss
	Reason        string          `json:"reason,omitempty"`         // 售后原因
	Receiver      *RefundReceiver `json:"receiver,omitempty"`       // 收件人信息
	RefundFee     float64         `json:"refund_fee,omitempty"`     // 售后金额
	RefundID      string          `json:"refund_id"`                // 售后单号（不能为空）
	ShopNick      string          `json:"shop_nick"`                // 在ERP中注册的卖家（店铺）昵称（不能为空）
	Status        int             `json:"status"`                   // 售后单状态（不能为空）
	TradeID       string          `json:"trade_id"`                 // 第三方交易号（不能为空）
	Type          int             `json:"type"`                     // 售后类型（不能为空）
}

// RefundItem 退换货明细
type RefundItem struct {
	ChangeItemID string `json:"change_item_id,omitempty"` // 换货商品ID
	ChangeNum    int    `json:"change_num,omitempty"`     // 换货数量
	ChangeSkuID  string `json:"change_sku_id,omitempty"`  // 换货规格ID
	OrderID      string `json:"order_id,omitempty"`       // 订单ID
}

// RefundReceiver 收件人信息
type RefundReceiver struct {
	ReceiverAddress  string `json:"receiver_address,omitempty"`  // 收件人详细地址
	ReceiverArea     string `json:"receiver_area,omitempty"`     // 收件人区/县
	ReceiverCity     string `json:"receiver_city,omitempty"`     // 收件人市
	ReceiverMobile   string `json:"receiver_mobile,omitempty"`   // 收件人手机
	ReceiverName     string `json:"receiver_name,omitempty"`     // 收件人姓名
	ReceiverProvince string `json:"receiver_province,omitempty"` // 收件人省
}

// ===================== 回调接口相关模型 =====================

// TradeShipmentNotification 订单发货通知
type TradeShipmentNotification struct {
	TradeID      string            `json:"trade_id"`      // 订单ID
	DeliveryCode string            `json:"delivery_code"` // 万里牛ERP承运商编码
	DeliveryName string            `json:"delivery_name"` // 物流公司名称
	Waybill      string            `json:"waybill"`       // 物流单号
	ShopNick     string            `json:"shop_nick"`     // 店铺昵称
	Packages     []ShipmentPackage `json:"packages"`      // 多包裹信息
}

// ShipmentPackage 包裹信息
type ShipmentPackage struct {
	DeliveryName string          `json:"deliveryName"` // 物流公司名称
	DeliveryCode string          `json:"deliveryCode"` // 承运商编码
	Waybill      string          `json:"waybill"`      // 物流单号
	OrderInfos   []ShipmentOrder `json:"orderInfos"`   // 订单信息
}

// ShipmentOrder 发货订单信息
type ShipmentOrder struct {
	OrderID string `json:"orderID"` // 订单ID
	Num     int    `json:"num"`     // 数量
}

// InventoryChangeNotification 库存变更通知
type InventoryChangeNotification struct {
	ItemID   string  `json:"item_id"`   // 商品ID
	SkuID    string  `json:"sku_id"`    // 规格ID
	ShopNick string  `json:"shop_nick"` // 店铺昵称
	Quantity float64 `json:"quantity"`  // 数量
}

// ===================== 通用响应模型 =====================

// ERPResponse ERP通用响应
type ERPResponse struct {
	ErrorCode string `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
	Response  string `json:"response"`
	Success   bool   `json:"success"`
}

// CallbackResponse 回调接口响应
type CallbackResponse struct {
	ErrorMsg string `json:"error_msg"` // 错误信息，成功则为 null
	Success  bool   `json:"success"`   // 调用是否成功
}

// CategoryQueryParams 商品分类查询参数
type CategoryQueryParams struct {
	Page  int `json:"page"`  // 当前页码，从1开始
	Limit int `json:"limit"` // 每页大小，最大50
}

// CategoryListResponse 商品分类列表响应
type CategoryListResponse struct {
	Code int                `json:"code"`
	Data []CategoryResponse `json:"data"`
}

// CategoryResponse 商品分类响应
type CategoryResponse struct {
	CatagoryName string `json:"catagory_name"` // 分类名称
	CatagoryID   string `json:"catagoryid"`    // 分类id
	HasChild     int    `json:"has_child"`     // 是否有子节点：1有，0无
	ParentID     string `json:"parentid"`      // 父节点id
}

// ===================== 库存查询V2接口 =====================

// InventoryQueryV2Request 库存查询V2请求
type InventoryQueryV2Request struct {
	PageNo        int     `json:"page_no"`                   // 分页号
	PageSize      int     `json:"page_size"`                 // 分页大小，最大200
	ArticleNumber string  `json:"article_number,omitempty"`  // 货号
	BarCode       string  `json:"bar_code,omitempty"`        // 商品条码，多个用逗号分隔，最多20个
	ModifyTime    *string `json:"modify_time,omitempty"`     // 修改时间
	ModifyTimeEnd *string `json:"modify_time_end,omitempty"` // 修改结束时间
	SkuCode       string  `json:"sku_code,omitempty"`        // 规格编码，多个用逗号分隔，最多20个
	Storage       string  `json:"storage,omitempty"`         // 目标仓库编码，多个用逗号分隔
}

// BatchInfo 批次信息
type BatchInfo struct {
	BatchNo       string  `json:"batch_no"`        // 批次号
	ExpiredDate   int64   `json:"expired_date"`    // 过期日期（Unix时间戳，毫秒）
	Num           float64 `json:"num"`             // 批次数量
	ProduceDate   int64   `json:"produce_date"`    // 生产日期（Unix时间戳，毫秒）
	LockSize      float64 `json:"lock_size"`       // 锁定库存
	DefectNum     float64 `json:"defect_num"`      // 次品数量
	LockDefectNum float64 `json:"lock_defect_num"` // 锁定次品数量
}

// InventoryItemV2 库存商品信息V2
type InventoryItemV2 struct {
	ArticleNumber     string      `json:"article_number"`      // 货号
	BarCode           string      `json:"bar_code"`            // 商品条码
	Batchs            []BatchInfo `json:"batchs"`              // 批次信息
	Cost              float64     `json:"cost"`                // 总价
	DefectNum         float64     `json:"defect_num"`          // 次品数量
	GoodsCode         string      `json:"goods_code"`          // 商品编码
	LastStock         float64     `json:"last_stock"`          // 最后采购金额
	LockDefectNum     float64     `json:"lock_defect_num"`     // 锁定次品数量
	LockSize          float64     `json:"lock_size"`           // 锁定库存
	Quantity          float64     `json:"quantity"`            // 实际库存
	SkuCode           string      `json:"sku_code"`            // 规格编码
	SpecName          string      `json:"spec_name"`           // 规格名称
	StorageCode       string      `json:"storage_code"`        // 仓库编码
	Underway          float64     `json:"underway"`            // 在途库存
	UnderwayDefectNum float64     `json:"underway_defect_num"` // 在途次品数量
}

// InventoryQueryV2Response 库存查询V2响应
type InventoryQueryV2Response struct {
	Code int               `json:"code"` // 响应代码，0表示成功
	Data []InventoryItemV2 `json:"data"` // 库存数据
}

// ===================== 发货状态查询相关模型 =====================

// TradeStatusResponse 发货状态查询响应
type TradeStatusResponse struct {
	Statuses []TradeStatus `json:"statuses"`
}

// TradeStatus 订单发货状态
type TradeStatus struct {
	SaleTaxSn    string             `json:"sale_tax_sn"`   // 销项发票号
	ExpressCode  string             `json:"express_code"`  // 快递公司编码
	ExpressLocal string             `json:"express_local"` // 快递网点编码
	Waybill      string             `json:"waybill"`       // 物流单号(快递单号)
	TradeNo      string             `json:"tradeNo"`       // 万里牛内部交易号
	ShippingType string             `json:"shippingType"`  // 发货类型
	PackingInfos []interface{}      `json:"packingInfos"`  // 包装信息
	Express      string             `json:"express"`       // 快递公司名称
	Orders       []TradeOrderStatus `json:"orders"`        // 订单明细状态
	TID          string             `json:"tid"`           // 第三方交易号（本地订单号）
	Status       string             `json:"status"`        // 订单状态
}

// TradeOrderStatus 订单明细状态
type TradeOrderStatus struct {
	ItemID      string `json:"itemID"`      // 商品ID
	OrderStatus string `json:"orderstatus"` // 明细状态
	OrderID     string `json:"orderID"`     // 子订单ID
	SkuID       string `json:"skuID"`       // 规格ID
}
