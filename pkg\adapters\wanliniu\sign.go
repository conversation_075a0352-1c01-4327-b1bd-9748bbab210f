package wanliniu

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// javaURLEncode 仿Java的URL编码
func javaURLEncode(str string) string {
	encodedStr := url.QueryEscape(str)
	encodedStr = strings.ReplaceAll(encodedStr, "!", "%21")
	encodedStr = strings.ReplaceAll(encodedStr, "'", "%27")
	encodedStr = strings.ReplaceAll(encodedStr, "(", "%28")
	encodedStr = strings.ReplaceAll(encodedStr, ")", "%29")
	encodedStr = strings.ReplaceAll(encodedStr, "~", "%7E")
	encodedStr = strings.ReplaceAll(encodedStr, "%20", "+")
	return encodedStr
}

// SignParameters 对参数进行签名
func SignParameters(parameters map[string]string, appKey, secret string) map[string]string {
	logx.Infof("[WanLiNiu] ===== 开始签名处理 =====")
	logx.Infof("[Wan<PERSON>iN<PERSON>] 输入参数: %+v", parameters)
	logx.Infof("[WanLiNiu] AppKey: %s", appKey)
	logx.Infof("[WanLiNiu] Secret: %s***", secret[:4]) // 只显示前4位

	params := make(map[string]string)
	for k, v := range parameters {
		params[k] = v
	}

	// 添加系统参数
	timestamp := time.Now().Unix() // 使用秒级时间戳，符合万里牛文档要求
	params["_app"] = appKey
	params["_t"] = fmt.Sprintf("%d", timestamp)
	params["_s"] = "" // 授权码，默认为空

	logx.Infof("[WanLiNiu] 添加系统参数后: _app=%s, _t=%d, _s=\"\"", appKey, timestamp)
	logx.Infof("[WanLiNiu] 完整参数集: %+v", params)

	// 对参数进行排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	logx.Infof("[WanLiNiu] 排序后的参数键: %v", keys)

	// 拼接参数串
	var content strings.Builder
	var encodedPairs []string
	for _, k := range keys {
		encodedValue := javaURLEncode(params[k])
		pair := k + "=" + encodedValue
		encodedPairs = append(encodedPairs, pair)
		content.WriteString(pair)
		content.WriteString("&")
	}
	logx.Infof("[WanLiNiu] URL编码后的参数对: %v", encodedPairs)

	// 去掉最后一个 '&' 符号
	queryString := strings.TrimSuffix(content.String(), "&")
	logx.Infof("[WanLiNiu] 查询字符串: %s", queryString)

	// 生成签名
	signContent := secret + queryString + secret
	logx.Infof("[WanLiNiu] 签名内容: %s", signContent)

	md5Hash := md5.Sum([]byte(signContent))
	sign := strings.ToUpper(hex.EncodeToString(md5Hash[:])) // 使用大写MD5，与成功的库存查询保持一致
	logx.Infof("[WanLiNiu] 生成的签名: %s", sign)

	// 添加签名到参数
	params["_sign"] = sign

	logx.Infof("[WanLiNiu] 最终签名参数: %+v", params)
	logx.Infof("[WanLiNiu] ===== 签名处理完成 =====")
	return params
}
