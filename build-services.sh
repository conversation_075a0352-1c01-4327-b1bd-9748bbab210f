#!/bin/bash

# 构建脚本 - 使用方案1：通过构建参数区分服务

set -e

# 获取版本号
VERSION=${1:-latest}
REGISTRY=${REGISTRY:-registry.xrxr.xyz}

echo "开始构建叶开泰服务镜像，版本: $VERSION"

# 构建应用
echo "构建应用..."
go build -o yekaitai_admin main.go

# 构建小程序服务镜像
echo "构建小程序服务镜像..."
docker build --build-arg SERVICE_NAME=yekaitai-mini -t $REGISTRY/yekaitai/yekaitai-mini:$VERSION .

# 构建后台管理服务镜像
echo "构建后台管理服务镜像..."
docker build --build-arg SERVICE_NAME=yekaitai-admin -t $REGISTRY/yekaitai/yekaitai-admin:$VERSION .

echo "镜像构建完成:"
echo "  小程序服务: $REGISTRY/yekaitai/yekaitai-mini:$VERSION"
echo "    - 日志路径: /var/log/medlinker/yekaitai-mini/"
echo "  后台管理服务: $REGISTRY/yekaitai/yekaitai-admin:$VERSION"
echo "    - 日志路径: /var/log/medlinker/yekaitai-admin/"

# 推送镜像（可选）
if [ "$2" = "push" ]; then
    echo "推送镜像到仓库..."
    docker push $REGISTRY/yekaitai/yekaitai-mini:$VERSION
    docker push $REGISTRY/yekaitai/yekaitai-admin:$VERSION
    echo "镜像推送完成"
fi

echo "构建完成！"
