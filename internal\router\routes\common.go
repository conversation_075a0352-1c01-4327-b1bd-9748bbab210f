package routes

import (
	"net/http"

	commonHandler "yekaitai/internal/handler"
	handler "yekaitai/internal/modules/appointment/handler"
	patientHandler "yekaitai/internal/modules/patient/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterCommonRoutes 注册公共路由
func RegisterCommonRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.ServiceContext) {
	// 创建枚举处理器
	enumHandler := patientHandler.NewEnumHandler(serverCtx)

	// 无需认证的路由组
	server.AddRoutes([]rest.Route{
		{
			Method:  http.MethodGet,
			Path:    "/health",
			Handler: handler.HealthHandler(serverCtx),
		},
	})

	// 需要JWT认证的文件上传路由
	server.AddRoutes(
		[]rest.Route{
			// 文件上传
			{
				Method:  http.MethodPost,
				Path:    "/api/files/upload",
				Handler: commonHandler.UploadFileHandler(serverCtx),
			},
			// 获取民族和与本人关系选项
			{
				Method:  http.MethodGet,
				Path:    "/api/enum/ethnicities",
				Handler: enumHandler.GetEthnicities,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/enum/relationships",
				Handler: enumHandler.GetRelationships,
			},
		},
		rest.WithJwt(serverCtx.Config.JWT.AccessSecret),
	)
}
