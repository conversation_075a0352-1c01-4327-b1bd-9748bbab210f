package checkout

import "time"

// PreloadRequest 预加载请求
type PreloadRequest struct {
	CartItems []string `json:"cartItems" validate:"required,min=1"` // 商品ID列表
}

// RecalculateRequest 动态计算请求
type RecalculateRequest struct {
	CartItems []string `json:"cartItems" validate:"required,min=1"` // 商品ID列表
	CouponIDs []string `json:"couponIds,omitempty"`                 // 优惠券ID列表（支持多张）
	UsePoints bool     `json:"usePoints,omitempty"`                 // 是否使用积分
}

// Product 商品信息
type Product struct {
	ID       string  `json:"id"`
	Name     string  `json:"name"`
	Price    float64 `json:"price"`
	Image    string  `json:"image"`
	Quantity int     `json:"quantity"` // 商品数量
	SpecName string  `json:"specName"` // 商品规格名称
}

// ShippingRule 运费规则
type ShippingRule struct {
	MinAmount float64 `json:"minAmount"` // 最小金额
	Fee       float64 `json:"fee"`       // 运费
}

// Coupon 优惠券信息（完整字段）
type Coupon struct {
	ID          string  `json:"id"`          // 优惠券ID
	Name        string  `json:"name"`        // 优惠券名称
	Description string  `json:"description"` // 使用说明
	Type        int     `json:"type"`        // 优惠券类型：1=满减券 2=折扣券 3=立减券
	TypeText    string  `json:"typeText"`    // 类型文本（如"满100减20"）
	Amount      float64 `json:"amount"`      // 优惠金额
	Discount    float64 `json:"discount"`    // 折扣比例
	MinAmount   float64 `json:"minAmount"`   // 最低使用金额
	Scope       string  `json:"scope"`       // 使用范围
	ValidFrom   string  `json:"validFrom"`   // 生效时间
	ValidUntil  string  `json:"validUntil"`  // 过期时间
	CanUse      bool    `json:"canUse"`      // 是否可用
	IsBest      bool    `json:"isBest"`      // 是否为最优券
}

// PointsPolicy 积分政策
type PointsPolicy struct {
	UsablePoints int     `json:"usablePoints"` // 可用积分
	Ratio        int     `json:"ratio"`        // 积分比例 (100积分=1元)
	MaxDeduct    float64 `json:"maxDeduct"`    // 最大抵扣金额
}

// Preview 预览信息
type Preview struct {
	ProductAmount  float64 `json:"productAmount"`  // 商品金额
	ShippingFee    float64 `json:"shippingFee"`    // 运费
	MemberDiscount float64 `json:"memberDiscount"` // 会员折扣
	CouponDeduct   float64 `json:"couponDeduct"`   // 优惠券抵扣
	PointsDeduct   float64 `json:"pointsDeduct"`   // 积分抵扣
	TotalAmount    float64 `json:"totalAmount"`    // 最终金额
}

// PreloadResponse 预加载响应
type PreloadResponse struct {
	Products           []Product      `json:"products"`
	DefaultShippingFee float64        `json:"defaultShippingFee"`
	ShippingRules      []ShippingRule `json:"shippingRules"`
	AvailableCoupons   []Coupon       `json:"availableCoupons"`
	PointsPolicy       PointsPolicy   `json:"pointsPolicy"`
	Preview            Preview        `json:"preview"`
	AvailablePoints    int            `json:"availablePoints"` // 可用积分数
	MemberDiscount     float64        `json:"memberDiscount"`  // 等级优惠金额
	UsePoints          bool           `json:"usePoints"`       // 积分开关状态
}

// Breakdown 费用明细
type Breakdown struct {
	ProductAmount  float64 `json:"productAmount"`  // 商品金额
	MemberDiscount float64 `json:"memberDiscount"` // 会员折扣
	CouponDeduct   float64 `json:"couponDeduct"`   // 优惠券抵扣
	PointsDeduct   float64 `json:"pointsDeduct"`   // 积分抵扣
	TotalAmount    float64 `json:"totalAmount"`    // 最终金额
}

// RecalculateResponse 动态计算响应
type RecalculateResponse struct {
	Valid           bool      `json:"valid"`           // 计算是否有效
	ShippingFee     float64   `json:"shippingFee"`     // 运费
	CouponValid     bool      `json:"couponValid"`     // 优惠券是否有效
	PointsValid     bool      `json:"pointsValid"`     // 积分是否有效
	Breakdown       Breakdown `json:"breakdown"`       // 费用明细
	Warnings        []string  `json:"warnings"`        // 警告信息
	AvailablePoints int       `json:"availablePoints"` // 可用积分数
	UsePoints       bool      `json:"usePoints"`       // 积分开关状态
}

// CartItem 购物车商品项
type CartItem struct {
	ID       uint    `json:"id"`
	GoodsID  uint    `json:"goods_id"`
	SpecID   uint    `json:"spec_id"`
	Quantity int     `json:"quantity"`
	Price    float64 `json:"price"`
	Name     string  `json:"name"`
	Image    string  `json:"image"`
	SpecName string  `json:"spec_name"` // 规格名称
}

// UserCoupon 用户优惠券
type UserCoupon struct {
	ID          uint      `json:"id"`
	CouponID    uint      `json:"coupon_id"`
	Name        string    `json:"name"`
	Type        int       `json:"type"`         // 1=满减券 2=折扣券 3=无门槛券
	MinAmount   float64   `json:"min_amount"`   // 最小使用金额
	DiscountVal float64   `json:"discount_val"` // 折扣值
	ExpireTime  time.Time `json:"expire_time"`  // 过期时间
	IsUsed      bool      `json:"is_used"`      // 是否已使用
}

// UserPoints 用户积分信息
type UserPoints struct {
	TotalPoints   int     `json:"total_points"`    // 总积分
	UsablePoints  int     `json:"usable_points"`   // 可用积分
	ExchangeRatio int     `json:"exchange_ratio"`  // 兑换比例
	MaxDeductRate float64 `json:"max_deduct_rate"` // 最大抵扣比例
}

// ShippingConfig 运费配置
type ShippingConfig struct {
	DefaultFee    float64        `json:"default_fee"`    // 默认运费
	FreeThreshold float64        `json:"free_threshold"` // 包邮门槛
	Rules         []ShippingRule `json:"rules"`          // 运费规则
}
