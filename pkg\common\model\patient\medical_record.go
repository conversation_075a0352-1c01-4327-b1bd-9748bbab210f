package patient

import (
	"time"

	"gorm.io/gorm"
)

// AbcYunMedicalRecord ABC云病历表
type AbcYunMedicalRecord struct {
	ID                     uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	PatientID              uint           `gorm:"column:patient_id;index;comment:患者ID(关联wx_patient表)" json:"patient_id"`
	ExternalID             string         `gorm:"column:external_id;index;comment:外部病历ID" json:"external_id"`
	OutpatientSheetID      string         `gorm:"column:outpatient_sheet_id;index;comment:门诊单ID" json:"outpatient_sheet_id"`
	ChiefComplaint         string         `gorm:"column:chief_complaint;type:text;comment:主述/现病史" json:"chief_complaint"`
	PastHistory            string         `gorm:"column:past_history;type:text;comment:既往史" json:"past_history"`
	FamilyHistory          string         `gorm:"column:family_history;type:text;comment:家族史" json:"family_history"`
	PresentHistory         string         `gorm:"column:present_history;type:text;comment:现病史" json:"present_history"`
	PhysicalExamination    string         `gorm:"column:physical_examination;type:text;comment:体格检查" json:"physical_examination"`
	Diagnosis              string         `gorm:"column:diagnosis;type:text;comment:诊断" json:"diagnosis"`
	DoctorAdvice           string         `gorm:"column:doctor_advice;type:text;comment:医嘱" json:"doctor_advice"`
	Syndrome               string         `gorm:"column:syndrome;type:text;comment:辨症" json:"syndrome"`
	Therapy                string         `gorm:"column:therapy;type:text;comment:治法" json:"therapy"`
	ChineseExamination     string         `gorm:"column:chinese_examination;type:text;comment:望闻切诊" json:"chinese_examination"`
	WearGlassesHistory     string         `gorm:"column:wear_glasses_history;comment:戴镜史" json:"wear_glasses_history"`
	EpidemiologicalHistory string         `gorm:"column:epidemiological_history;type:text;comment:流行病学史" json:"epidemiological_history"`
	ObstetricalHistory     string         `gorm:"column:obstetrical_history;type:text;comment:妇科月经婚育史" json:"obstetrical_history"`
	EyeExamination         string         `gorm:"column:eye_examination;type:text;comment:眼科检查(JSON)" json:"eye_examination"`
	AllergicHistory        string         `gorm:"column:allergic_history;type:text;comment:过敏史" json:"allergic_history"`
	AuxiliaryExamination   string         `gorm:"column:auxiliary_examination;type:text;comment:辅助检查" json:"auxiliary_examination"`
	Attachments            string         `gorm:"column:attachments;type:text;comment:附件列表(JSON)" json:"attachments"`
	CreatedAt              time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt              time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt              gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (AbcYunMedicalRecord) TableName() string {
	return "abcyun_medical_records"
}
