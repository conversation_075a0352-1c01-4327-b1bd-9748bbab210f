package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// CouponIssueTaskStatus 任务状态
type CouponIssueTaskStatus int

const (
	TaskStatusPending    CouponIssueTaskStatus = 0 // 待处理
	TaskStatusProcessing CouponIssueTaskStatus = 1 // 处理中
	TaskStatusCompleted  CouponIssueTaskStatus = 2 // 已完成
	TaskStatusFailed     CouponIssueTaskStatus = 3 // 已失败
)

// CouponIssueTask 优惠券批量发放任务
type CouponIssueTask struct {
	ID           uint                  `gorm:"primaryKey;type:bigint unsigned;autoIncrement;comment:任务ID" json:"id"`
	CouponID     uint                  `gorm:"type:bigint unsigned;not null;index;comment:优惠券ID" json:"coupon_id"`
	TaskName     string                `gorm:"type:varchar(100);not null;comment:任务名称" json:"task_name"`
	UserLevels   string                `gorm:"type:text;not null;comment:目标用户等级列表" json:"user_levels"`
	IssueCount   int                   `gorm:"type:int;not null;default:0;comment:计划发放数量" json:"issue_count"`
	IssuedCount  int                   `gorm:"type:int;not null;default:0;comment:已发放数量" json:"issued_count"`
	FailedCount  int                   `gorm:"type:int;not null;default:0;comment:发放失败数量" json:"failed_count"`
	TargetCount  int                   `gorm:"type:int;not null;default:0;comment:目标用户总数" json:"target_count"`
	IssueReason  string                `gorm:"type:varchar(200);comment:发放原因" json:"issue_reason"`
	Status       CouponIssueTaskStatus `gorm:"type:tinyint;not null;default:0;index;comment:任务状态：0-待处理，1-处理中，2-已完成，3-已失败" json:"status"`
	ErrorMessage string                `gorm:"type:text;comment:错误信息" json:"error_message"`
	CreatorID    uint                  `gorm:"type:bigint unsigned;default:0;index;comment:创建人ID" json:"creator_id"`
	CreatedAt    time.Time             `gorm:"type:datetime(3);comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time             `gorm:"type:datetime(3);comment:更新时间" json:"updated_at"`
	CompletedAt  *time.Time            `gorm:"type:datetime(3);comment:完成时间" json:"completed_at"`
}

// TableName 表名
func (CouponIssueTask) TableName() string {
	return "coupon_issue_tasks"
}

// GetUserLevelsSlice 获取用户等级切片
func (t *CouponIssueTask) GetUserLevelsSlice() ([]int, error) {
	var levels []int
	if t.UserLevels == "" {
		return levels, nil
	}

	err := json.Unmarshal([]byte(t.UserLevels), &levels)
	return levels, err
}

// SetUserLevelsSlice 设置用户等级切片
func (t *CouponIssueTask) SetUserLevelsSlice(levels []int) error {
	data, err := json.Marshal(levels)
	if err != nil {
		return err
	}
	t.UserLevels = string(data)
	return nil
}

// CouponIssueTaskRepository 优惠券发放任务仓库接口
type CouponIssueTaskRepository interface {
	Create(task *CouponIssueTask) error
	Update(task *CouponIssueTask) error
	FindByID(id uint) (*CouponIssueTask, error)
	FindPendingTasks(limit int) ([]*CouponIssueTask, error)
	UpdateStatus(id uint, status CouponIssueTaskStatus, errorMessage string) error
	UpdateProgress(id uint, issuedCount, failedCount int) error
	CompleteTask(id uint, issuedCount, failedCount int) error
	List(page, size int, status *CouponIssueTaskStatus) ([]*CouponIssueTask, int64, error)
}

// couponIssueTaskRepository 优惠券发放任务仓库实现
type couponIssueTaskRepository struct {
	db *gorm.DB
}

// NewCouponIssueTaskRepository 创建优惠券发放任务仓库
func NewCouponIssueTaskRepository(db *gorm.DB) CouponIssueTaskRepository {
	return &couponIssueTaskRepository{db: db}
}

// Create 创建任务
func (r *couponIssueTaskRepository) Create(task *CouponIssueTask) error {
	return r.db.Create(task).Error
}

// Update 更新任务
func (r *couponIssueTaskRepository) Update(task *CouponIssueTask) error {
	return r.db.Save(task).Error
}

// FindByID 根据ID查找任务
func (r *couponIssueTaskRepository) FindByID(id uint) (*CouponIssueTask, error) {
	var task CouponIssueTask
	err := r.db.First(&task, id).Error
	return &task, err
}

// FindPendingTasks 查找待处理的任务
func (r *couponIssueTaskRepository) FindPendingTasks(limit int) ([]*CouponIssueTask, error) {
	var tasks []*CouponIssueTask
	err := r.db.Where("status = ?", TaskStatusPending).
		Order("created_at ASC").
		Limit(limit).
		Find(&tasks).Error
	return tasks, err
}

// UpdateStatus 更新任务状态
func (r *couponIssueTaskRepository) UpdateStatus(id uint, status CouponIssueTaskStatus, errorMessage string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if errorMessage != "" {
		updates["error_message"] = errorMessage
	}

	if status == TaskStatusCompleted || status == TaskStatusFailed {
		now := time.Now()
		updates["completed_at"] = &now
	}

	return r.db.Model(&CouponIssueTask{}).Where("id = ?", id).Updates(updates).Error
}

// UpdateProgress 更新任务进度
func (r *couponIssueTaskRepository) UpdateProgress(id uint, issuedCount, failedCount int) error {
	return r.db.Model(&CouponIssueTask{}).Where("id = ?", id).Updates(map[string]interface{}{
		"issued_count": issuedCount,
		"failed_count": failedCount,
		"updated_at":   time.Now(),
	}).Error
}

// CompleteTask 完成任务
func (r *couponIssueTaskRepository) CompleteTask(id uint, issuedCount, failedCount int) error {
	now := time.Now()
	return r.db.Model(&CouponIssueTask{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":       TaskStatusCompleted,
		"issued_count": issuedCount,
		"failed_count": failedCount,
		"completed_at": &now,
		"updated_at":   now,
	}).Error
}

// List 获取任务列表
func (r *couponIssueTaskRepository) List(page, size int, status *CouponIssueTaskStatus) ([]*CouponIssueTask, int64, error) {
	var tasks []*CouponIssueTask
	var total int64

	query := r.db.Model(&CouponIssueTask{})

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	err := query.Order("created_at DESC").Offset(offset).Limit(size).Find(&tasks).Error

	return tasks, total, err
}
