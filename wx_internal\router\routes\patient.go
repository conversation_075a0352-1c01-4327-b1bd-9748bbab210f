package routes

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/middleware"
	patientHandler "yekaitai/wx_internal/modules/patient/handler"
	registrationHandler "yekaitai/wx_internal/modules/registration/handler"
	"yekaitai/wx_internal/svc"
)

// RegisterPatientRoutes 注册患者相关路由
func RegisterPatientRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 创建患者处理器
	diagnosisHandler := patientHandler.NewDiagnosisHandler()
	registrationPatientHandler := registrationHandler.NewPatientHandler()
	patientManagementHandler := patientHandler.NewPatientHandler(serverCtx)

	// 使用微信认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 注册患者信息新增接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodPost,
			Path:    "/api/wx/consumer-jcfw/jcfw/grxx",
			Handler: wxAuthWrapper(registrationPatientHandler.Create),
		},
	)

	// 注册患者诊断记录路由
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/patients/diagnosis/list",
				Handler: diagnosisHandler.List,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/patients/diagnosis/:id",
				Handler: diagnosisHandler.Detail,
			},
		},
		rest.WithJwt(serverCtx.Config.JWT.WxAccessSecret),
	)

	// 新增：患者管理相关路由
	server.AddRoutes(
		[]rest.Route{
			// 获取患者列表
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/patients",
				Handler: wxAuthWrapper(patientManagementHandler.ListPatients),
			},
			// 获取患者详情
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/patients/:patientId",
				Handler: wxAuthWrapper(patientManagementHandler.GetPatient),
			},
			// 创建患者
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/patients",
				Handler: wxAuthWrapper(patientManagementHandler.CreatePatient),
			},
			// 更新患者信息
			{
				Method:  http.MethodPut,
				Path:    "/api/wx/patients/:patientId",
				Handler: wxAuthWrapper(patientManagementHandler.UpdatePatient),
			},
			// 删除患者
			{
				Method:  http.MethodDelete,
				Path:    "/api/wx/patients/:patientId",
				Handler: wxAuthWrapper(patientManagementHandler.DeletePatient),
			},
			// 设置默认就诊人
			{
				Method:  http.MethodPut,
				Path:    "/api/wx/patients/:patientId/default",
				Handler: wxAuthWrapper(patientManagementHandler.SetDefaultPatient),
			},
		},
	)
}
