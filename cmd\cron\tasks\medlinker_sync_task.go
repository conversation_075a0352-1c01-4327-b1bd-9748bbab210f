package tasks

import (
	"fmt"
	"time"

	"yekaitai/internal/modules/medlinker_sync/service"
	"yekaitai/pkg/adapters/medlinker"

	"github.com/zeromicro/go-zero/core/logx"
)

// MedlinkerSyncTask 医联数据同步定时任务
type MedlinkerSyncTask struct {
	syncService *service.SyncService
	enabled     bool
	interval    time.Duration
}

// NewMedlinkerSyncTask 创建医联同步任务
func NewMedlinkerSyncTask() *MedlinkerSyncTask {
	// 创建医联客户端配置
	config := medlinker.Config{
		BaseURL:        "https://api.medlinker.com", // 医联API地址
		AppID:          "yekaitai_app",              // 应用ID
		AppSecret:      "your_app_secret",           // 应用密钥
		ModelID:        403,                         // 智能导诊模型ID
		DailyCallLimit: 1000,                        // 每日调用限制
	}

	// 创建医联客户端
	medlinkerClient := medlinker.NewMedlinkerClient(config)

	// 创建同步服务
	syncService := service.NewSyncService(medlinkerClient)

	return &MedlinkerSyncTask{
		syncService: syncService,
		enabled:     true,             // 默认启用
		interval:    10 * time.Minute, // 默认10分钟间隔
	}
}

// Run 运行增量同步任务
func (t *MedlinkerSyncTask) Run() {
	if !t.enabled {
		logx.Info("医联数据同步任务已禁用，跳过执行")
		return
	}

	logx.Info("开始执行医联数据增量同步任务")

	startTime := time.Now()
	err := t.syncService.SyncIncrementalData()
	duration := time.Since(startTime)

	if err != nil {
		logx.Errorf("医联数据增量同步任务执行失败: %v, 耗时: %v", err, duration)
		// 这里可以添加告警通知
		return
	}

	logx.Infof("医联数据增量同步任务执行成功, 耗时: %v", duration)
}

// RunFullSync 运行全量同步任务
func (t *MedlinkerSyncTask) RunFullSync() {
	logx.Info("开始执行医联数据全量同步任务")

	startTime := time.Now()
	err := t.syncService.SyncAllData()
	duration := time.Since(startTime)

	if err != nil {
		logx.Errorf("医联数据全量同步任务执行失败: %v, 耗时: %v", err, duration)
		// 这里可以添加告警通知
		return
	}

	logx.Infof("医联数据全量同步任务执行成功, 耗时: %v", duration)
}

// SetEnabled 设置任务启用状态
func (t *MedlinkerSyncTask) SetEnabled(enabled bool) {
	t.enabled = enabled
	logx.Infof("医联数据同步任务状态设置为: %v", enabled)
}

// SetInterval 设置同步间隔
func (t *MedlinkerSyncTask) SetInterval(interval time.Duration) {
	t.interval = interval
	logx.Infof("医联数据同步间隔设置为: %v", interval)
}

// GetStatus 获取任务状态
func (t *MedlinkerSyncTask) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"enabled":  t.enabled,
		"interval": t.interval.String(),
		"name":     "医联数据同步任务",
	}
}

// StartScheduler 启动定时调度器
func (t *MedlinkerSyncTask) StartScheduler() {
	logx.Infof("启动医联数据同步定时调度器, 间隔: %v", t.interval)

	ticker := time.NewTicker(t.interval)
	defer ticker.Stop()

	// 立即执行一次
	go t.Run()

	for {
		select {
		case <-ticker.C:
			go t.Run()
		}
	}
}

// TestConnection 测试医联连接
func (t *MedlinkerSyncTask) TestConnection() error {
	logx.Info("测试医联连接")

	// 使用默认电话进行登录测试
	err := t.syncService.GetMedlinkerClient().Login("18888888888")
	if err != nil {
		logx.Errorf("医联连接测试失败: %v", err)
		return err
	}

	logx.Info("医联连接测试成功")
	return nil
}

// GetMedlinkerClient 获取医联客户端（用于外部调用）
func (t *MedlinkerSyncTask) GetMedlinkerClient() *medlinker.MedlinkerClient {
	return t.syncService.GetMedlinkerClient()
}

// 全局任务实例
var MedlinkerSyncTaskInstance *MedlinkerSyncTask

// InitMedlinkerSyncTask 初始化医联同步任务
func InitMedlinkerSyncTask() {
	MedlinkerSyncTaskInstance = NewMedlinkerSyncTask()
	logx.Info("医联数据同步任务初始化完成")
}

// RunMedlinkerIncrementalSync 运行增量同步（供外部调用）
func RunMedlinkerIncrementalSync() {
	if MedlinkerSyncTaskInstance == nil {
		logx.Error("医联同步任务未初始化")
		return
	}
	MedlinkerSyncTaskInstance.Run()
}

// RunMedlinkerFullSync 运行全量同步（供外部调用）
func RunMedlinkerFullSync() {
	if MedlinkerSyncTaskInstance == nil {
		logx.Error("医联同步任务未初始化")
		return
	}
	MedlinkerSyncTaskInstance.RunFullSync()
}

// GetMedlinkerSyncStatus 获取同步状态（供外部调用）
func GetMedlinkerSyncStatus() map[string]interface{} {
	if MedlinkerSyncTaskInstance == nil {
		return map[string]interface{}{
			"error": "医联同步任务未初始化",
		}
	}
	return MedlinkerSyncTaskInstance.GetStatus()
}

// SetMedlinkerSyncEnabled 设置同步启用状态（供外部调用）
func SetMedlinkerSyncEnabled(enabled bool) {
	if MedlinkerSyncTaskInstance == nil {
		logx.Error("医联同步任务未初始化")
		return
	}
	MedlinkerSyncTaskInstance.SetEnabled(enabled)
}

// SetMedlinkerSyncInterval 设置同步间隔（供外部调用）
func SetMedlinkerSyncInterval(minutes int) {
	if MedlinkerSyncTaskInstance == nil {
		logx.Error("医联同步任务未初始化")
		return
	}
	interval := time.Duration(minutes) * time.Minute
	MedlinkerSyncTaskInstance.SetInterval(interval)
}

// TestMedlinkerConnection 测试医联连接（供外部调用）
func TestMedlinkerConnection() error {
	if MedlinkerSyncTaskInstance == nil {
		logx.Error("医联同步任务未初始化")
		return fmt.Errorf("医联同步任务未初始化")
	}
	return MedlinkerSyncTaskInstance.TestConnection()
}

// GetMedlinkerClientInstance 获取医联客户端实例（供外部调用）
func GetMedlinkerClientInstance() *medlinker.MedlinkerClient {
	if MedlinkerSyncTaskInstance == nil {
		logx.Error("医联同步任务未初始化")
		return nil
	}
	return MedlinkerSyncTaskInstance.GetMedlinkerClient()
}
