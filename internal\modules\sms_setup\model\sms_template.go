package model

import (
	"time"

	"gorm.io/gorm"
)

// SmsTemplate 短信模板模型
type SmsTemplate struct {
	ID           uint           `gorm:"primaryKey" json:"id"`                   // 模板ID
	TemplateName string         `gorm:"size:100;not null" json:"template_name"` // 模板名称
	Content      string         `gorm:"type:text;not null" json:"content"`      // 模板内容
	Creator      string         `gorm:"size:50" json:"creator"`                 // 创建人
	CreatedAt    time.Time      `json:"created_at"`                             // 创建时间
	UpdatedAt    time.Time      `json:"updated_at"`                             // 更新时间
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`                         // 删除时间
}

// TableName 设置表名
func (SmsTemplate) TableName() string {
	return "sms_templates"
}

// SmsTemplateCreateRequest 创建短信模板请求
type SmsTemplateCreateRequest struct {
	TemplateName string `json:"template_name" validate:"required,max=100"` // 模板名称，必填，最大100字符
	Content      string `json:"content" validate:"required"`               // 模板内容，必填
	Creator      string `json:"creator" validate:"max=50"`                 // 创建人，最大50字符
}

// SmsTemplateUpdateRequest 更新短信模板请求
type SmsTemplateUpdateRequest struct {
	ID           uint   `json:"id" validate:"required"`                    // 模板ID，必填
	TemplateName string `json:"template_name" validate:"required,max=100"` // 模板名称，必填，最大100字符
	Content      string `json:"content" validate:"required"`               // 模板内容，必填
	Creator      string `json:"creator" validate:"max=50"`                 // 创建人，最大50字符
}

// SmsTemplateListRequest 短信模板列表请求
type SmsTemplateListRequest struct {
	Page         int    `form:"page,optional,default=1"`  // 页码，默认1
	Size         int    `form:"size,optional,default=10"` // 每页数量，默认10
	TemplateName string `form:"template_name,optional"`   // 模板名称筛选
}

// SmsTemplateResponse 短信模板响应
type SmsTemplateResponse struct {
	ID           uint      `json:"id"`            // 模板ID
	TemplateName string    `json:"template_name"` // 模板名称
	Content      string    `json:"content"`       // 模板内容
	Creator      string    `json:"creator"`       // 创建人
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
}

// SmsTemplateListResponse 短信模板列表响应
type SmsTemplateListResponse struct {
	List  []*SmsTemplateResponse `json:"list"`  // 模板列表
	Total int64                  `json:"total"` // 总数
	Page  int                    `json:"page"`  // 当前页码
	Size  int                    `json:"size"`  // 每页数量
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	IDs []uint `json:"ids" validate:"required,min=1"` // 要删除的ID列表，必填，至少1个
}

// SmsTemplateRepository 短信模板仓库接口
type SmsTemplateRepository interface {
	Create(template *SmsTemplate) error                                      // 创建模板
	Update(template *SmsTemplate) error                                      // 更新模板
	Delete(id uint) error                                                    // 删除模板
	BatchDelete(ids []uint) error                                            // 批量删除模板
	FindByID(id uint) (*SmsTemplate, error)                                  // 根据ID查找模板
	List(page, size int, templateName string) ([]*SmsTemplate, int64, error) // 获取模板列表
	CheckNameExists(name string, excludeID uint) (bool, error)               // 检查模板名称是否存在
}

// smsTemplateRepository 短信模板仓库实现
type smsTemplateRepository struct {
	db *gorm.DB
}

// NewSmsTemplateRepository 创建短信模板仓库
func NewSmsTemplateRepository(db *gorm.DB) SmsTemplateRepository {
	return &smsTemplateRepository{db: db}
}

// Create 创建模板
func (r *smsTemplateRepository) Create(template *SmsTemplate) error {
	return r.db.Create(template).Error
}

// Update 更新模板
func (r *smsTemplateRepository) Update(template *SmsTemplate) error {
	return r.db.Save(template).Error
}

// Delete 删除模板
func (r *smsTemplateRepository) Delete(id uint) error {
	return r.db.Delete(&SmsTemplate{}, id).Error
}

// BatchDelete 批量删除模板
func (r *smsTemplateRepository) BatchDelete(ids []uint) error {
	return r.db.Delete(&SmsTemplate{}, ids).Error
}

// FindByID 根据ID查找模板
func (r *smsTemplateRepository) FindByID(id uint) (*SmsTemplate, error) {
	var template SmsTemplate
	err := r.db.First(&template, id).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// List 获取模板列表
func (r *smsTemplateRepository) List(page, size int, templateName string) ([]*SmsTemplate, int64, error) {
	var templates []*SmsTemplate
	var total int64

	query := r.db.Model(&SmsTemplate{})

	// 根据模板名称筛选
	if templateName != "" {
		query = query.Where("template_name LIKE ?", "%"+templateName+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&templates).Error; err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

// CheckNameExists 检查模板名称是否存在
func (r *smsTemplateRepository) CheckNameExists(name string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.Model(&SmsTemplate{}).Where("template_name = ?", name)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}
