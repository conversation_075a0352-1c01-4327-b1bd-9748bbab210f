package bootstrap

import (
	"fmt"
	"log"

	refundModel "yekaitai/internal/modules/refund/model"
	"yekaitai/pkg/infra/mysql"
)

// CreateRefundRecordsTable 创建退款记录表
func CreateRefundRecordsTable() {
	fmt.Println("开始创建退款记录表...")

	db := mysql.Master()
	if db == nil {
		log.Fatal("数据库连接失败")
	}

	// 创建退款记录表
	if err := db.AutoMigrate(&refundModel.RefundRecord{}); err != nil {
		log.Fatalf("创建退款记录表失败: %v", err)
	}

	fmt.Println("退款记录表创建成功")
}

// DropRefundRecordsTable 删除退款记录表（用于回滚）
func DropRefundRecordsTable() {
	fmt.Println("开始删除退款记录表...")

	db := mysql.Master()
	if db == nil {
		log.Fatal("数据库连接失败")
	}

	// 删除表
	if err := db.Migrator().DropTable(&refundModel.RefundRecord{}); err != nil {
		log.Fatalf("删除退款记录表失败: %v", err)
	}

	fmt.Println("退款记录表删除成功")
}
