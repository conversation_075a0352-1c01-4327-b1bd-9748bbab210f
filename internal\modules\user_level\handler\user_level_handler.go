package handler

import (
	"context"
	"net/http"
	"strconv"

	"yekaitai/internal/modules/admin/service"
	userService "yekaitai/internal/modules/user_level/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// UserLevelDetailRequest 获取等级详情请求
type UserLevelDetailRequest struct {
	ID uint `path:"id"` // 等级ID
}

// GetUserLevelDetailRequest 获取等级详情请求
type GetUserLevelDetailRequest struct {
	ID uint `path:"id"` // 等级ID
}

// UpdateUserLevelRequest 更新等级请求（handler层使用）
type UpdateUserLevelRequest struct {
	ID uint `path:"id"` // 等级ID
	userService.CreateUserLevelRequest
}

// DeleteUserLevelRequest 删除等级请求
type DeleteUserLevelRequest struct {
	ID uint `path:"id"` // 等级ID
}

// GetAvailableCouponsRequest 获取可选优惠券请求
type GetAvailableCouponsRequest struct {
	CouponType int `form:"coupon_type,optional"` // 优惠券类型筛选:1-折扣券,2-满减券,3-立减券
	Page       int `form:"page,default=1"`       // 页码
	Size       int `form:"size,default=10"`      // 每页数量
}

// UserLevelHandler 用户等级处理器
type UserLevelHandler struct {
	userLevelService *userService.UserLevelService
	logService       *service.AdminOperationLogService
}

// NewUserLevelHandler 创建用户等级处理器
func NewUserLevelHandler() *UserLevelHandler {
	return &UserLevelHandler{
		userLevelService: userService.NewUserLevelService(),
		logService:       service.NewAdminOperationLogService(),
	}
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *UserLevelHandler) logAdminOperation(r *http.Request, adminID, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 管理员ID=%s, 模块=%s, 操作=%s", adminID, module, action)
}

// GetUserLevelList 获取等级列表
func (h *UserLevelHandler) GetUserLevelList(w http.ResponseWriter, r *http.Request) {
	var req userService.UserLevelListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	levels, total, err := h.userLevelService.GetUserLevelList(context.Background(), &req)
	if err != nil {
		logx.Errorf("获取等级列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取等级列表失败"))
		return
	}

	response := map[string]interface{}{
		"list":  levels,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(response, "获取等级列表成功"))
}

// CreateUserLevel 创建等级
func (h *UserLevelHandler) CreateUserLevel(w http.ResponseWriter, r *http.Request) {
	var req userService.CreateUserLevelRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析创建等级请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取管理员ID
	ctx := context.Background()
	var adminID uint
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if adminIDUint, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			adminID = uint(adminIDUint)
			ctx = context.WithValue(ctx, "admin_id", adminID)
		}
	}

	result, err := h.userLevelService.CreateUserLevel(ctx, &req)
	if err != nil {
		logx.Errorf("创建等级失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "创建等级成功"))
}

// GetUserLevelDetail 获取等级详情
func (h *UserLevelHandler) GetUserLevelDetail(w http.ResponseWriter, r *http.Request) {
	var req GetUserLevelDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	level, err := h.userLevelService.GetUserLevelDetail(context.Background(), req.ID)
	if err != nil {
		logx.Errorf("获取等级详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(level, "获取等级详情成功"))
}

// UpdateUserLevel 更新等级
func (h *UserLevelHandler) UpdateUserLevel(w http.ResponseWriter, r *http.Request) {
	var req UpdateUserLevelRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析更新等级请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取管理员ID
	ctx := context.Background()
	var adminID uint
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if adminIDUint, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			adminID = uint(adminIDUint)
			ctx = context.WithValue(ctx, "admin_id", adminID)
		}
	}

	// 转换为service层的请求结构体
	serviceReq := &userService.UpdateUserLevelRequest{
		ID:                     req.ID,
		CreateUserLevelRequest: req.CreateUserLevelRequest,
	}

	result, err := h.userLevelService.UpdateUserLevel(ctx, serviceReq)
	if err != nil {
		logx.Errorf("更新等级失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "更新等级成功"))
}

// DeleteUserLevel 删除等级
func (h *UserLevelHandler) DeleteUserLevel(w http.ResponseWriter, r *http.Request) {
	var req UserLevelDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取管理员ID
	ctx := context.Background()
	var adminID uint
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if adminIDUint, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			adminID = uint(adminIDUint)
			ctx = context.WithValue(ctx, "admin_id", adminID)
		}
	}

	// 先获取等级信息，用于日志记录
	_, err := h.userLevelService.GetUserLevelDetail(ctx, req.ID)
	if err != nil {
		logx.Errorf("获取等级详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "等级不存在"))
		return
	}

	err = h.userLevelService.DeleteUserLevel(ctx, req.ID)
	if err != nil {
		logx.Errorf("删除等级失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "删除等级成功"))
}

// GetAvailableCoupons 获取可选优惠券列表
func (h *UserLevelHandler) GetAvailableCoupons(w http.ResponseWriter, r *http.Request) {
	var req GetAvailableCouponsRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	result, err := h.userLevelService.GetAvailableCoupons(context.Background(), req.CouponType)
	if err != nil {
		logx.Errorf("获取可选优惠券列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取优惠券列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取优惠券列表成功"))
}
