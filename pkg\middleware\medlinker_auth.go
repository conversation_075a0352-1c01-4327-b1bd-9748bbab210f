package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/internal/modules/hospital_sync/types"
	"yekaitai/pkg/adapters/medlinker"
)

// MedlinkerTokenAuth 医联token认证中间件
func MedlinkerTokenAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取token
		token := c.GetHeader("token")
		if token == "" {
			logx.Errorf("医联token认证失败: token为空")
			c.JSON(http.StatusUnauthorized, types.MedlinkerResponse{
				Code: 401,
				Msg:  "认证失败，缺少token",
				Data: nil,
			})
			c.Abort()
			return
		}

		// 验证token格式（简单验证）
		if len(token) < 10 {
			logx.Errorf("医联token认证失败: token格式无效")
			c.JSON(http.StatusUnauthorized, types.MedlinkerResponse{
				Code: 401,
				Msg:  "认证失败，token格式无效",
				Data: nil,
			})
			c.Abort()
			return
		}

		// 验证token是否为医联有效token
		// 这里可以调用医联的token验证接口，或者检查token是否在有效列表中
		if !isValidMedlinkerToken(token) {
			logx.Errorf("医联token认证失败: token无效 - %s", token[:10]+"...")
			c.JSON(http.StatusUnauthorized, types.MedlinkerResponse{
				Code: 401,
				Msg:  "认证失败，token无效",
				Data: nil,
			})
			c.Abort()
			return
		}

		logx.Infof("医联token认证成功: %s", token[:10]+"...")
		
		// 将token存储到上下文中，供后续使用
		c.Set("medlinker_token", token)
		c.Next()
	}
}

// isValidMedlinkerToken 验证医联token是否有效
func isValidMedlinkerToken(token string) bool {
	// 简单验证：检查token是否以特定前缀开头
	// 在实际环境中，这里应该调用医联的token验证接口
	
	// 1. 检查token长度
	if len(token) < 20 {
		return false
	}

	// 2. 检查token是否包含非法字符
	if strings.Contains(token, " ") || strings.Contains(token, "\n") || strings.Contains(token, "\t") {
		return false
	}

	// 3. 这里可以添加更复杂的验证逻辑
	// 例如：调用医联API验证token，或者检查token是否在Redis缓存中等
	
	// 4. 临时实现：检查是否为已知的测试token
	validTestTokens := []string{
		"eafde38ec19fdd9ed07b06b0011e7efd", // 测试token
	}
	
	for _, validToken := range validTestTokens {
		if token == validToken {
			return true
		}
	}

	// 5. 如果有医联客户端，可以尝试验证token
	client := medlinker.NewClient()
	if client != nil {
		// 这里可以调用医联的token验证接口
		// 暂时返回true，表示通过验证
		return true
	}

	// 默认情况下，如果无法验证，则认为token有效
	// 在生产环境中，应该更严格地验证token
	return true
}

// GetMedlinkerToken 从上下文中获取医联token
func GetMedlinkerToken(c *gin.Context) string {
	if token, exists := c.Get("medlinker_token"); exists {
		if tokenStr, ok := token.(string); ok {
			return tokenStr
		}
	}
	return ""
}
