package service

import (
	"context"
	"fmt"

	"yekaitai/pkg/common/model/redeemer"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type RedeemerService struct {
	db *gorm.DB
}

func NewRedeemerService() *RedeemerService {
	return &RedeemerService{
		db: mysql.GetDB(),
	}
}

// RedeemerListRequest 核销员列表请求
type RedeemerListRequest struct {
	Page      int    `form:"page" validate:"min=1"`         // 页码
	Size      int    `form:"size" validate:"min=1,max=100"` // 每页数量
	Mobile    string `form:"mobile"`                        // 手机号筛选
	Name      string `form:"name"`                          // 名称筛选
	StoreName string `form:"store_name"`                    // 门店名称筛选
}

// RedeemerCreateRequest 创建核销员请求
type RedeemerCreateRequest struct {
	Mobile  string `json:"mobile" validate:"required"`   // 手机号
	Name    string `json:"name" validate:"required"`     // 核销员名称
	StoreID uint   `json:"store_id" validate:"required"` // 门店ID
	Avatar  string `json:"avatar"`                       // 头像
}

// RedeemerUpdateRequest 更新核销员请求
type RedeemerUpdateRequest struct {
	RedeemerID uint   `json:"redeemer_id" validate:"required"` // 核销员ID
	Name       string `json:"name" validate:"required"`        // 核销员名称
	StoreID    uint   `json:"store_id" validate:"required"`    // 门店ID
	Avatar     string `json:"avatar"`                          // 头像
}

// RedeemerResponse 核销员响应
type RedeemerResponse struct {
	RedeemerID uint   `json:"redeemer_id"` // 核销员ID
	UserID     uint   `json:"user_id"`     // 用户ID
	Mobile     string `json:"mobile"`      // 手机号
	Name       string `json:"name"`        // 核销员名称
	Avatar     string `json:"avatar"`      // 头像
	StoreID    uint   `json:"store_id"`    // 门店ID
	StoreName  string `json:"store_name"`  // 门店名称
	Status     int    `json:"status"`      // 状态(1-正常，0-禁用)
	CreatedAt  string `json:"created_at"`  // 创建时间
	UpdatedAt  string `json:"updated_at"`  // 更新时间
}

// GetRedeemerList 获取核销员列表
func (s *RedeemerService) GetRedeemerList(ctx context.Context, req *RedeemerListRequest) ([]*RedeemerResponse, int64, error) {
	var total int64
	var redeemers []*RedeemerResponse

	// 构建查询
	query := s.db.WithContext(ctx).Table("wx_redeemer r").
		Select(`r.redeemer_id, r.user_id, r.store_id, r.status, r.created_at, r.updated_at,
				u.mobile, u.nickname as name, u.avatar,
				s.name as store_name`).
		Joins("LEFT JOIN wx_user u ON r.user_id = u.user_id").
		Joins("LEFT JOIN t_stores s ON r.store_id = s.id").
		Where("r.deleted_at IS NULL")

	// 添加筛选条件
	if req.Mobile != "" {
		query = query.Where("u.mobile LIKE ?", "%"+req.Mobile+"%")
	}
	if req.Name != "" {
		query = query.Where("u.nickname LIKE ?", "%"+req.Name+"%")
	}
	if req.StoreName != "" {
		query = query.Where("s.name LIKE ?", "%"+req.StoreName+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取核销员总数失败: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Size
	if err := query.Order("r.created_at DESC").
		Offset(offset).Limit(req.Size).
		Find(&redeemers).Error; err != nil {
		return nil, 0, fmt.Errorf("查询核销员列表失败: %w", err)
	}

	// 格式化时间和名称
	for _, item := range redeemers {
		// 限制名称长度为10个字符
		if len([]rune(item.Name)) > 10 {
			item.Name = string([]rune(item.Name)[:10])
		}
	}

	return redeemers, total, nil
}

// CreateRedeemer 创建核销员
func (s *RedeemerService) CreateRedeemer(ctx context.Context, req *RedeemerCreateRequest) error {
	// 验证名称长度和字符
	if len([]rune(req.Name)) > 10 {
		return fmt.Errorf("名称不能超过10个字符")
	}
	if !isValidName(req.Name) {
		return fmt.Errorf("名称只能包含中英文字符，不支持符号")
	}

	// 检查手机号是否已存在
	var existingUser user.WxUser
	err := s.db.WithContext(ctx).Where("mobile = ?", req.Mobile).First(&existingUser).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询用户失败: %w", err)
	}

	var userID uint
	if err == gorm.ErrRecordNotFound {
		// 创建新用户
		newUser := &user.WxUser{
			Mobile:   req.Mobile,
			Nickname: req.Name,
			Avatar:   req.Avatar,
			Status:   1,
		}
		if err := s.db.WithContext(ctx).Create(newUser).Error; err != nil {
			return fmt.Errorf("创建用户失败: %w", err)
		}
		userID = newUser.UserID
	} else {
		userID = existingUser.UserID
		// 检查是否已经是核销员
		var existingRedeemer redeemer.WxRedeemer
		err = s.db.WithContext(ctx).Where("user_id = ? AND deleted_at IS NULL", userID).First(&existingRedeemer).Error
		if err == nil {
			return fmt.Errorf("该用户已经是核销员")
		}
		if err != gorm.ErrRecordNotFound {
			return fmt.Errorf("查询核销员失败: %w", err)
		}
	}

	// 获取门店名称
	var storeName string
	err = s.db.WithContext(ctx).Table("t_stores").
		Where("id = ? AND deleted_at IS NULL", req.StoreID).
		Pluck("name", &storeName).Error
	if err != nil {
		return fmt.Errorf("门店不存在: %w", err)
	}

	// 创建核销员记录
	newRedeemer := &redeemer.WxRedeemer{
		UserID:    userID,
		StoreID:   req.StoreID,
		StoreName: storeName,
		Status:    1,
	}

	if err := s.db.WithContext(ctx).Create(newRedeemer).Error; err != nil {
		return fmt.Errorf("创建核销员失败: %w", err)
	}

	return nil
}

// UpdateRedeemer 更新核销员
func (s *RedeemerService) UpdateRedeemer(ctx context.Context, req *RedeemerUpdateRequest) error {
	// 验证名称长度和字符
	if len([]rune(req.Name)) > 10 {
		return fmt.Errorf("名称不能超过10个字符")
	}
	if !isValidName(req.Name) {
		return fmt.Errorf("名称只能包含中英文字符，不支持符号")
	}

	// 查询核销员记录
	var redeemerRecord redeemer.WxRedeemer
	err := s.db.WithContext(ctx).Where("redeemer_id = ? AND deleted_at IS NULL", req.RedeemerID).First(&redeemerRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("核销员不存在")
		}
		return fmt.Errorf("查询核销员失败: %w", err)
	}

	// 获取门店名称
	var storeName string
	err = s.db.WithContext(ctx).Table("t_stores").
		Where("id = ? AND deleted_at IS NULL", req.StoreID).
		Pluck("name", &storeName).Error
	if err != nil {
		return fmt.Errorf("门店不存在: %w", err)
	}

	// 开启事务
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新用户信息
		if err := tx.Model(&user.WxUser{}).
			Where("user_id = ?", redeemerRecord.UserID).
			Updates(map[string]interface{}{
				"nickname": req.Name,
				"avatar":   req.Avatar,
			}).Error; err != nil {
			return fmt.Errorf("更新用户信息失败: %w", err)
		}

		// 更新核销员信息
		if err := tx.Model(&redeemer.WxRedeemer{}).
			Where("redeemer_id = ?", req.RedeemerID).
			Updates(map[string]interface{}{
				"store_id":   req.StoreID,
				"store_name": storeName,
			}).Error; err != nil {
			return fmt.Errorf("更新核销员信息失败: %w", err)
		}

		return nil
	})
}

// UpdateRedeemerStatus 更新核销员状态
func (s *RedeemerService) UpdateRedeemerStatus(ctx context.Context, redeemerID uint, status int) error {
	result := s.db.WithContext(ctx).Model(&redeemer.WxRedeemer{}).
		Where("redeemer_id = ? AND deleted_at IS NULL", redeemerID).
		Update("status", status)

	if result.Error != nil {
		return fmt.Errorf("更新核销员状态失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("核销员不存在")
	}

	return nil
}

// DeleteRedeemer 删除核销员
func (s *RedeemerService) DeleteRedeemer(ctx context.Context, redeemerID uint) error {
	result := s.db.WithContext(ctx).Where("redeemer_id = ?", redeemerID).Delete(&redeemer.WxRedeemer{})
	if result.Error != nil {
		return fmt.Errorf("删除核销员失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("核销员不存在")
	}

	return nil
}

// isValidName 验证名称是否只包含中英文字符
func isValidName(name string) bool {
	for _, r := range name {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '\u4e00' && r <= '\u9fff')) {
			return false
		}
	}
	return true
}
