package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"yekaitai/internal/modules/admin/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/infra/redis"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// AdminAuthMiddleware 后台管理系统认证中间件
func AdminAuthMiddleware(serverCtx *svc.ServiceContext) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 从请求头获取Token
			authHeader := r.Header.Get("Authorization")
			if authHeader == "" {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权，请先登录"))
				return
			}

			// 验证Token格式
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
				return
			}

			// 检查Token类型
			tokenType := strings.ToLower(parts[0])
			tokenString := parts[1]

			// 检查令牌类型，只接受bearer
			if tokenType != "bearer" {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证类型错误"))
				return
			}

			// 使用专用的管理员Token解析函数
			claims, err := ParseAdminToken(serverCtx, tokenString)
			if err != nil {
				logx.Errorf("解析管理员Token失败: %v", err)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证失败: "+err.Error()))
				return
			}

			// 从Token获取管理员ID
			adminID, err := strconv.ParseUint(claims.Subject, 10, 64)
			if err != nil {
				logx.Errorf("解析管理员ID失败: %v", err)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "无效的管理员标识"))
				return
			}

			// 将管理员ID添加到请求上下文
			r = r.WithContext(NewContext(r.Context(), "admin_id", strconv.FormatUint(adminID, 10)))

			// 将管理员权限添加到上下文
			if len(claims.Perms) > 0 {
				r = r.WithContext(NewContext(r.Context(), "admin_perms", strings.Join(claims.Perms, ",")))
			}

			// 将管理员角色添加到上下文
			if len(claims.Roles) > 0 {
				r = r.WithContext(NewContext(r.Context(), "admin_roles", strings.Join(claims.Roles, ",")))
			}

			next.ServeHTTP(w, r)
		})
	}
}

// AdminPermissionMiddleware 后台管理系统权限检查中间件
func AdminPermissionMiddleware(module, action string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 从请求上下文获取管理员ID
			adminIDStr := FromContext(r.Context(), "admin_id")
			if adminIDStr == "" {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权，请先登录"))
				return
			}

			adminID, err := strconv.ParseUint(adminIDStr, 10, 64)
			if err != nil {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "无效的管理员标识"))
				return
			}

			// 直接从上下文检查权限
			adminPerms := FromContext(r.Context(), "admin_perms")
			permCode := module + ":" + action

			// 如果上下文中已经有权限，直接检查
			if adminPerms != "" {
				permList := strings.Split(adminPerms, ",")
				for _, perm := range permList {
					if perm == permCode || perm == "*" {
						next.ServeHTTP(w, r)
						return
					}
				}
			}

			// 如果上下文中没有权限或权限不匹配，查询数据库
			rbacRepo := model.NewAdminRBACRepository(mysql.Master())
			hasPermission, err := rbacRepo.CheckAdminPermission(uint(adminID), module, action)
			if err != nil || !hasPermission {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "没有操作权限"))
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// GetAdminIDFromToken 从Token获取管理员ID
func GetAdminIDFromToken(token string, serverCtx *svc.ServiceContext) (uint, error) {
	// 使用ParseAdminToken函数解析token
	claims, err := ParseAdminToken(serverCtx, token)
	if err != nil {
		return 0, err
	}

	// UserID字段直接包含管理员ID
	return claims.UserID, nil
}

// AdminLogoutHandler 处理管理员登出请求
func AdminLogoutHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var logoutReq LogoutRequest
		if err := httpx.Parse(r, &logoutReq); err != nil {
			httpx.Error(w, err)
			return
		}

		// 从请求头获取token
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			logx.Error("退出失败：未提供认证令牌")
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未提供认证令牌"))
			return
		}

		// 解析token
		var userIDStr string

		// 解析token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 {
			logx.Error("退出失败：认证格式错误")
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
			return
		}

		tokenType := strings.ToLower(parts[0])
		tokenString := parts[1]

		// 解析token
		if tokenType == "bearer" {
			// 尝试解析为管理员token
			claims, err := ParseAdminToken(svcCtx, tokenString)
			if err != nil {
				logx.Errorf("退出失败：解析管理员令牌失败 %s", err.Error())
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, fmt.Sprintf("解析令牌失败: %v", err)))
				return
			}

			// 成功解析
			userIDStr = strconv.FormatUint(uint64(claims.UserID), 10)
		} else {
			logx.Error("退出失败：不支持的认证类型")
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "不支持的认证类型"))
			return
		}

		// 删除刷新令牌
		redisCli := redis.GetClient()
		_ = redisCli.Del(context.Background(), "admin_refresh_token:"+userIDStr).Err()

		// 返回成功
		logx.Infof("管理员登出成功: %s", userIDStr)
		httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse("登出成功"))
	}
}

// LogoutRequest 登出请求
type LogoutRequest struct {
	// 可以为空，仅用于保持API一致性
}
