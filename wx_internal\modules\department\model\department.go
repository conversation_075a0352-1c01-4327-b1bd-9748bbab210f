package model

import (
	"time"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// Department 机构科室模型
type Department struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:本地ID"`
	JgksID    string         `json:"jgksid" gorm:"column:jgks_id;type:varchar(50);index;comment:机构科室ID"`
	JgksMC    string         `json:"jgksmc" gorm:"column:jgks_mc;type:varchar(100);comment:机构科室名称"`
	PYM       string         `json:"pym" gorm:"column:pym;type:varchar(50);comment:拼音码"`
	ZfBz      int            `json:"zfbz" gorm:"column:zf_bz;default:0;comment:作废标志 0:未作废,1:作废"`
	FwFw      string         `json:"fwfw" gorm:"column:fw_fw;type:varchar(20);comment:服务范围"`
	FwFwMC    string         `json:"fwfwmc" gorm:"column:fw_fw_mc;type:varchar(50);comment:服务范围名称"`
	GlLb      string         `json:"gllb" gorm:"column:gl_lb;type:varchar(20);comment:管理类别"`
	GlLbMC    string         `json:"gllbmc" gorm:"column:gl_lb_mc;type:varchar(50);comment:管理类别名称"`
	KsFLDM    string         `json:"ksfldm" gorm:"column:ks_fldm;type:varchar(20);comment:科室分类代码"`
	KsFLMC    string         `json:"ksflmc" gorm:"column:ks_flmc;type:varchar(50);comment:科室分类名称"`
	KsLx      string         `json:"kslx" gorm:"column:ks_lx;type:varchar(10);comment:科室类型 1:挂号科室,2:行政科室,3:药房,4:药库,5:物资库,6:医技科室"`
	KsType    int            `json:"ks_type" gorm:"column:ks_type;default:1;comment:科室类型 0:普通科室,1:门诊科室"`
	WsjgID    string         `json:"wsjgid" gorm:"column:wsjg_id;type:varchar(50);index;comment:卫生机构ID"`
	Hospital  string         `json:"hospital" gorm:"column:hospital;type:varchar(100);comment:所属医院/门店名称"`
	CreatedAt time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (d *Department) TableName() string {
	return "t_departments"
}

// DepartmentRepository 机构科室仓库接口
type DepartmentRepository interface {
	// 创建机构科室
	Create(dept *Department) error
	// 批量创建机构科室
	BatchCreate(depts []*Department) error
	// 更新机构科室
	Update(dept *Department) error
	// 根据ID查找机构科室
	FindByID(id uint) (*Department, error)
	// 根据机构科室ID查找
	FindByJgksID(jgksID int) (*Department, error)
	// 根据字符串类型机构科室ID查找
	FindByJgksIDStr(jgksID string) (*Department, error)
	// 根据机构科室ID和卫生机构ID查找
	FindByJgksIDAndWsjgID(jgksID int, wsjgID string) (*Department, error)
	// 获取机构科室列表
	List(page, size int, wsjgID string, search string) ([]*Department, int64, error)
	// 根据卫生机构ID获取机构科室列表
	ListByWsjgID(wsjgID string) ([]*Department, error)
}

// departmentRepository 机构科室仓库实现
type departmentRepository struct{}

// NewDepartmentRepository 创建机构科室仓库
func NewDepartmentRepository() DepartmentRepository {
	return &departmentRepository{}
}

// Create 创建机构科室
func (r *departmentRepository) Create(dept *Department) error {
	return mysql.Master().Create(dept).Error
}

// BatchCreate 批量创建机构科室
func (r *departmentRepository) BatchCreate(depts []*Department) error {
	if len(depts) == 0 {
		return nil
	}
	return mysql.Master().Create(depts).Error
}

// Update 更新机构科室
func (r *departmentRepository) Update(dept *Department) error {
	return mysql.Master().Save(dept).Error
}

// FindByID 根据ID查找机构科室
func (r *departmentRepository) FindByID(id uint) (*Department, error) {
	var dept Department
	err := mysql.Slave().Where("id = ?", id).First(&dept).Error
	if err != nil {
		return nil, err
	}
	return &dept, nil
}

// FindByJgksID 根据机构科室ID查找
func (r *departmentRepository) FindByJgksID(jgksID int) (*Department, error) {
	var dept Department
	err := mysql.Slave().Where("jgks_id = ?", jgksID).First(&dept).Error
	if err != nil {
		return nil, err
	}
	return &dept, nil
}

// FindByJgksIDStr 根据字符串类型机构科室ID查找
func (r *departmentRepository) FindByJgksIDStr(jgksID string) (*Department, error) {
	var dept Department
	err := mysql.Slave().Where("jgks_id = ?", jgksID).First(&dept).Error
	if err != nil {
		return nil, err
	}
	return &dept, nil
}

// FindByJgksIDAndWsjgID 根据机构科室ID和卫生机构ID查找
func (r *departmentRepository) FindByJgksIDAndWsjgID(jgksID int, wsjgID string) (*Department, error) {
	var dept Department
	err := mysql.Slave().Where("jgks_id = ? AND wsjg_id = ?", jgksID, wsjgID).First(&dept).Error
	if err != nil {
		return nil, err
	}
	return &dept, nil
}

// List 获取机构科室列表
func (r *departmentRepository) List(page, size int, wsjgID string, search string) ([]*Department, int64, error) {
	var depts []*Department
	var total int64

	db := mysql.Slave().Model(&Department{})

	// 添加查询条件
	if wsjgID != "" {
		db = db.Where("wsjg_id = ?", wsjgID)
	}

	if search != "" {
		db = db.Where("jgks_mc LIKE ?", "%"+search+"%")
	}

	// 仅查询未作废的科室
	db = db.Where("zf_bz = '0'")

	// 计算总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && size > 0 {
		offset := (page - 1) * size
		db = db.Offset(offset).Limit(size)
	}

	// 执行查询
	err = db.Order("id DESC").Find(&depts).Error
	if err != nil {
		return nil, 0, err
	}

	return depts, total, nil
}

// ListByWsjgID 根据卫生机构ID获取机构科室列表
func (r *departmentRepository) ListByWsjgID(wsjgID string) ([]*Department, error) {
	var depts []*Department
	err := mysql.Slave().Where("wsjg_id = ? AND zf_bz = '0'", wsjgID).Find(&depts).Error
	if err != nil {
		return nil, err
	}
	return depts, nil
}

// ListByWsjgIDAndZfbz 根据卫生机构ID和作废标志获取机构科室列表
func (r *departmentRepository) ListByWsjgIDAndZfbz(wsjgID string, zfbz string) ([]*Department, error) {
	var depts []*Department
	err := mysql.Slave().Where("wsjg_id = ? AND zf_bz = ?", wsjgID, zfbz).Find(&depts).Error
	if err != nil {
		return nil, err
	}
	return depts, nil
}
