package service

import (
	"context"
	"fmt"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type GoodsService struct {
	db *gorm.DB
}

func NewGoodsService() *GoodsService {
	return &GoodsService{
		db: mysql.GetDB(),
	}
}

// CreateGoods 创建商品
func (s *GoodsService) CreateGoods(ctx context.Context, req *model.GoodsCreateRequest) (*model.Goods, error) {
	goods := &model.Goods{
		GoodsCode:          req.GoodsCode,
		GoodsName:          req.GoodsName,
		BrandName:          req.BrandName,
		CategoryID:         req.CategoryID,
		CategoryName:       req.CategoryName,
		Status:             req.Status,
		IsOnSale:           req.IsOnSale,
		IsRecommended:      req.IsRecommended,
		RecommendOrder:     req.RecommendOrder,
		TagPrice:           req.TagPrice,
		Pic:                req.<PERSON>,
		Pics:               req.Pics,
		Description:        req.Description,
		Origin:             req.Origin,
		Manufacturer:       req.Manufacturer,
		IsIntegralExchange: req.IsIntegralExchange,
		IntegralPrice:      req.IntegralPrice,
		SortOrder:          req.SortOrder,
	}

	if err := s.db.WithContext(ctx).Create(goods).Error; err != nil {
		return nil, fmt.Errorf("创建商品失败: %w", err)
	}

	return goods, nil
}

// UpdateGoods 更新商品
func (s *GoodsService) UpdateGoods(ctx context.Context, id uint, req *model.GoodsUpdateRequest) (*model.Goods, error) {
	goods := &model.Goods{}
	if err := s.db.WithContext(ctx).First(goods, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商品不存在")
		}
		return nil, fmt.Errorf("查询商品失败: %w", err)
	}

	// 更新商品信息 - 只更新允许修改的字段
	if req.CategoryID != "" {
		goods.CategoryID = req.CategoryID
	}
	goods.IsRecommended = req.IsRecommended
	goods.RecommendOrder = req.RecommendOrder

	// 更新新增的字段（使用指针判断是否需要更新）
	if req.Status != nil {
		goods.Status = *req.Status
	}
	if req.IsOnSale != nil {
		goods.IsOnSale = *req.IsOnSale
	}
	if req.IsIntegralExchange != nil {
		goods.IsIntegralExchange = *req.IsIntegralExchange
	}
	if req.IntegralPrice != nil {
		goods.IntegralPrice = *req.IntegralPrice
	}
	if req.SortOrder != nil {
		goods.SortOrder = *req.SortOrder
	}

	if err := s.db.WithContext(ctx).Save(goods).Error; err != nil {
		return nil, fmt.Errorf("更新商品失败: %w", err)
	}

	return goods, nil
}

// DeleteGoods 删除商品
func (s *GoodsService) DeleteGoods(ctx context.Context, id uint) error {
	result := s.db.WithContext(ctx).Delete(&model.Goods{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除商品失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("商品不存在")
	}
	return nil
}

// GetGoods 获取商品详情
func (s *GoodsService) GetGoods(ctx context.Context, id uint) (*model.Goods, error) {
	goods := &model.Goods{}
	if err := s.db.WithContext(ctx).Preload("Category").Preload("Specs").First(goods, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商品不存在")
		}
		return nil, fmt.Errorf("查询商品失败: %w", err)
	}
	return goods, nil
}

// ListGoods 获取商品列表
func (s *GoodsService) ListGoods(ctx context.Context, params *model.GoodsQueryParams) ([]*model.Goods, int64, error) {
	var goods []*model.Goods
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Goods{})

	// 条件筛选
	if params.GoodsName != "" {
		query = query.Where("goods_name LIKE ?", "%"+params.GoodsName+"%")
	}
	if params.CategoryID != "" {
		query = query.Where("category_id = ?", params.CategoryID)
	}
	if params.Status != nil {
		query = query.Where("status = ?", *params.Status)
	}
	if params.IsOnSale != nil {
		query = query.Where("is_on_sale = ?", *params.IsOnSale)
	}
	if params.IsRecommended != nil {
		query = query.Where("is_recommended = ?", *params.IsRecommended)
	}
	if params.IsIntegralExchange != nil {
		query = query.Where("is_integral_exchange = ?", *params.IsIntegralExchange)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计商品数量失败: %w", err)
	}

	// 分页查询
	offset := (params.Page - 1) * params.PageSize
	if err := query.Preload("Category").Preload("Specs").
		Order("sort_order ASC, recommend_order ASC, created_at DESC").
		Offset(offset).Limit(params.PageSize).Find(&goods).Error; err != nil {
		return nil, 0, fmt.Errorf("查询商品列表失败: %w", err)
	}

	return goods, total, nil
}

// GetRecommendGoods 获取推荐商品
func (s *GoodsService) GetRecommendGoods(ctx context.Context, limit int) ([]*model.Goods, error) {
	var goods []*model.Goods
	query := s.db.WithContext(ctx).Where("is_recommended = ?", 1).
		Where("status = ?", 1).
		Where("is_on_sale = ?", 1).
		Order("recommend_order ASC, created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Preload("Category").Preload("Specs").Find(&goods).Error; err != nil {
		return nil, fmt.Errorf("查询推荐商品失败: %w", err)
	}

	return goods, nil
}

// UpdateGoodsRecommend 更新商品推荐状态
func (s *GoodsService) UpdateGoodsRecommend(ctx context.Context, id uint, isRecommended, recommendOrder int) error {
	result := s.db.WithContext(ctx).Model(&model.Goods{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_recommended":  isRecommended,
			"recommend_order": recommendOrder,
		})

	if result.Error != nil {
		return fmt.Errorf("更新商品推荐状态失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("商品不存在")
	}
	return nil
}

// UpdateGoodsCategory 更新商品分类
func (s *GoodsService) UpdateGoodsCategory(ctx context.Context, id uint, categoryID string) error {
	result := s.db.WithContext(ctx).Model(&model.Goods{}).Where("id = ?", id).
		Update("category_id", categoryID)

	if result.Error != nil {
		return fmt.Errorf("更新商品分类失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("商品不存在")
	}
	return nil
}

// GetGoodsByCategory 根据分类获取商品
func (s *GoodsService) GetGoodsByCategory(ctx context.Context, categoryID string, params *model.GoodsQueryParams) ([]*model.Goods, int64, error) {
	params.CategoryID = categoryID
	return s.ListGoods(ctx, params)
}

// IncrementViewCount 增加商品浏览次数
func (s *GoodsService) IncrementViewCount(ctx context.Context, goodsID uint) error {
	if err := s.db.WithContext(ctx).Model(&model.Goods{}).Where("id = ?", goodsID).
		UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error; err != nil {
		return fmt.Errorf("更新商品浏览次数失败: %w", err)
	}
	return nil
}

// SearchGoods 搜索商品（模糊匹配商品名称）
func (s *GoodsService) SearchGoods(ctx context.Context, keyword string, params *model.GoodsQueryParams) ([]*model.Goods, int64, error) {
	params.GoodsName = keyword
	return s.ListGoods(ctx, params)
}

// UpdateSalesCount 更新商品销售单数（支付成功时调用）
func (s *GoodsService) UpdateSalesCount(ctx context.Context, goodsID uint, increment int) error {
	// 使用原子操作更新销售数量
	result := s.db.WithContext(ctx).Model(&model.Goods{}).Where("id = ?", goodsID).
		UpdateColumn("sales_count", gorm.Expr("sales_count + ?", increment))

	if result.Error != nil {
		return fmt.Errorf("更新商品销售单数失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("商品不存在")
	}
	return nil
}

// BatchUpdateSalesCount 批量更新商品销售单数（用于订单包含多个商品时）
func (s *GoodsService) BatchUpdateSalesCount(ctx context.Context, goodsIDs []uint, increment int) error {
	if len(goodsIDs) == 0 {
		return nil
	}

	result := s.db.WithContext(ctx).Model(&model.Goods{}).Where("id IN ?", goodsIDs).
		UpdateColumn("sales_count", gorm.Expr("sales_count + ?", increment))

	if result.Error != nil {
		return fmt.Errorf("批量更新商品销售单数失败: %w", result.Error)
	}

	return nil
}
