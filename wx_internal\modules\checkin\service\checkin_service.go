package service

import (
	"context"
	"fmt"
	"time"

	"yekaitai/internal/service"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/checkin/model"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type CheckinService struct {
	db                *gorm.DB
	coinRewardService *service.CoinRewardService
}

func NewCheckinService() *CheckinService {
	return &CheckinService{
		db:                mysql.GetDB(),
		coinRewardService: service.NewCoinRewardService(),
	}
}

// Checkin 用户签到
func (s *CheckinService) Checkin(ctx context.Context, userID uint, date string) (*model.CheckinResponse, error) {
	// 1. 验证签到日期
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	checkinDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("签到日期格式错误")
	}

	// 只能签到今天或之前的日期
	today := time.Now().Format("2006-01-02")
	if date > today {
		return nil, fmt.Errorf("不能签到未来的日期")
	}

	// 2. 检查是否已经签到
	var existingRecord model.CheckinRecord
	err = s.db.WithContext(ctx).Where("user_id = ? AND checkin_date = ?", userID, date).First(&existingRecord).Error
	if err == nil {
		return &model.CheckinResponse{
			Success:        false,
			Message:        "今日已签到",
			ContinuousDays: existingRecord.ContinuousDays,
			CoinsAwarded:   0,
			TotalCoins:     0,
			CanShare:       !existingRecord.IsShared,
			ShareCoins:     s.calculateShareReward(),
		}, nil
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询签到记录失败: %w", err)
	}

	// 3. 计算连续签到天数
	continuousDays := s.calculateContinuousDays(ctx, userID, checkinDate)

	// 4. 计算签到奖励
	coinsAwarded := s.calculateCheckinReward(continuousDays)

	// 5. 开始事务处理签到
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 6. 创建签到记录
	checkinRecord := &model.CheckinRecord{
		UserID:         userID,
		CheckinDate:    date,
		CheckinTime:    time.Now(),
		ContinuousDays: continuousDays,
		CoinsAwarded:   coinsAwarded,
		IsShared:       false,
		ShareCoins:     0,
	}

	if err := tx.Create(checkinRecord).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建签到记录失败: %w", err)
	}

	// 7. 发放签到奖励积分
	if coinsAwarded > 0 {
		err = s.coinRewardService.ProcessCheckinReward(ctx, userID, date, false)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("发放签到奖励失败: %w", err)
		}
	}

	// 8. 获取用户当前叶小币总数
	var userCoins struct {
		TotalCoins int `json:"total_coins"`
	}
	err = tx.Table("user_coins").Select("COALESCE(total_coins, 0) as total_coins").
		Where("user_id = ?", userID).First(&userCoins).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		tx.Rollback()
		return nil, fmt.Errorf("查询用户叶小币失败: %w", err)
	}

	// 9. 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交签到事务失败: %w", err)
	}

	logx.Infof("用户签到成功: userID=%d, date=%s, continuousDays=%d, coinsAwarded=%d",
		userID, date, continuousDays, coinsAwarded)

	return &model.CheckinResponse{
		Success:        true,
		Message:        fmt.Sprintf("签到成功！连续签到%d天，获得%d叶小币", continuousDays, coinsAwarded),
		ContinuousDays: continuousDays,
		CoinsAwarded:   coinsAwarded,
		TotalCoins:     userCoins.TotalCoins,
		CanShare:       true,
		ShareCoins:     s.calculateShareReward(),
	}, nil
}

// GetCheckinCalendar 获取签到日历
func (s *CheckinService) GetCheckinCalendar(ctx context.Context, userID uint, year, month int) (*model.CheckinCalendarResponse, error) {
	// 1. 获取指定月份的签到记录
	startDate := fmt.Sprintf("%04d-%02d-01", year, month)
	endDate := fmt.Sprintf("%04d-%02d-31", year, month)

	var checkinRecords []model.CheckinRecord
	err := s.db.WithContext(ctx).Where("user_id = ? AND checkin_date >= ? AND checkin_date <= ?",
		userID, startDate, endDate).Find(&checkinRecords).Error
	if err != nil {
		return nil, fmt.Errorf("查询签到记录失败: %w", err)
	}

	// 2. 构建签到日历数据
	checkinMap := make(map[string]*model.CheckinRecord)
	for i := range checkinRecords {
		checkinMap[checkinRecords[i].CheckinDate] = &checkinRecords[i]
	}

	// 3. 获取用户当前叶小币总数
	var userCoins struct {
		TotalCoins int `json:"total_coins"`
	}
	err = s.db.WithContext(ctx).Table("user_coins").Select("COALESCE(total_coins, 0) as total_coins").
		Where("user_id = ?", userID).First(&userCoins).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询用户叶小币失败: %w", err)
	}

	// 4. 获取连续签到天数
	continuousDays, err := s.getUserContinuousDays(ctx, userID)
	if err != nil {
		logx.Errorf("获取连续签到天数失败: %v", err)
		continuousDays = 0
	}

	// 5. 构建日历数据
	today := time.Now().Format("2006-01-02")
	todayChecked := false

	// 获取当月天数
	firstDay := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	lastDay := firstDay.AddDate(0, 1, -1).Day()

	var checkinDays []model.CheckinDayInfo
	for day := 1; day <= lastDay; day++ {
		dateStr := fmt.Sprintf("%04d-%02d-%02d", year, month, day)

		isToday := dateStr == today
		isChecked := checkinMap[dateStr] != nil
		canCheckin := isToday && !isChecked

		if isToday && isChecked {
			todayChecked = true
		}

		// 获取签到奖励
		coinsReward := 0
		if record, exists := checkinMap[dateStr]; exists {
			coinsReward = record.CoinsAwarded
		} else if canCheckin {
			// 计算今天签到可获得的奖励
			coinsReward = s.calculateCheckinReward(continuousDays + 1)
		}

		checkinDays = append(checkinDays, model.CheckinDayInfo{
			Day:         day,
			IsChecked:   isChecked,
			IsToday:     isToday,
			CanCheckin:  canCheckin,
			CoinsReward: coinsReward,
		})
	}

	// 6. 获取奖励规则
	rewardRules, err := s.getCheckinRewardRules(ctx)
	if err != nil {
		logx.Errorf("获取签到奖励规则失败: %v", err)
		rewardRules = []model.CheckinRewardRule{}
	}

	// 7. 计算本月签到次数
	monthlyCheckins := len(checkinRecords)

	return &model.CheckinCalendarResponse{
		Year:            year,
		Month:           month,
		TotalCoins:      userCoins.TotalCoins,
		ContinuousDays:  continuousDays,
		MonthlyCheckins: monthlyCheckins,
		TodayChecked:    todayChecked,
		CanCheckin:      !todayChecked,
		CheckinDays:     checkinDays,
		RewardRules:     rewardRules,
	}, nil
}

// ShareCheckin 分享签到
func (s *CheckinService) ShareCheckin(ctx context.Context, userID uint, date string) (*model.ShareCheckinResponse, error) {
	// 1. 查询签到记录
	var checkinRecord model.CheckinRecord
	err := s.db.WithContext(ctx).Where("user_id = ? AND checkin_date = ?", userID, date).First(&checkinRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("未找到该日期的签到记录")
		}
		return nil, fmt.Errorf("查询签到记录失败: %w", err)
	}

	// 2. 检查是否已经分享
	if checkinRecord.IsShared {
		return &model.ShareCheckinResponse{
			Success:      false,
			Message:      "该签到已分享过",
			CoinsAwarded: 0,
			TotalCoins:   0,
		}, nil
	}

	// 3. 计算分享奖励
	shareCoins := s.calculateShareReward()

	// 4. 开始事务处理分享
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 5. 更新签到记录
	err = tx.Model(&checkinRecord).Updates(map[string]interface{}{
		"is_shared":   true,
		"share_coins": shareCoins,
		"updated_at":  time.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新签到记录失败: %w", err)
	}

	// 6. 发放分享奖励积分
	if shareCoins > 0 {
		err = s.coinRewardService.ProcessActivityReward(ctx, userID, "SHARE_CHECKIN", date)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("发放分享奖励失败: %w", err)
		}
	}

	// 7. 获取用户当前叶小币总数
	var userCoins struct {
		TotalCoins int `json:"total_coins"`
	}
	err = tx.Table("user_coins").Select("COALESCE(total_coins, 0) as total_coins").
		Where("user_id = ?", userID).First(&userCoins).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		tx.Rollback()
		return nil, fmt.Errorf("查询用户叶小币失败: %w", err)
	}

	// 8. 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交分享事务失败: %w", err)
	}

	logx.Infof("用户分享签到成功: userID=%d, date=%s, shareCoins=%d", userID, date, shareCoins)

	return &model.ShareCheckinResponse{
		Success:      true,
		Message:      fmt.Sprintf("分享成功！获得%d叶小币", shareCoins),
		CoinsAwarded: shareCoins,
		TotalCoins:   userCoins.TotalCoins,
	}, nil
}

// GetCheckinPopupInfo 获取签到弹窗信息
func (s *CheckinService) GetCheckinPopupInfo(ctx context.Context, userID uint) (*model.CheckinPopupResponse, error) {
	today := time.Now().Format("2006-01-02")

	// 1. 检查今天是否已签到
	var checkinRecord model.CheckinRecord
	err := s.db.WithContext(ctx).Where("user_id = ? AND checkin_date = ?", userID, today).First(&checkinRecord).Error
	todayChecked := err == nil

	// 2. 获取连续签到天数
	continuousDays, err := s.getUserContinuousDays(ctx, userID)
	if err != nil {
		logx.Errorf("获取用户连续签到天数失败: %v", err)
		continuousDays = 0
	}

	// 3. 计算今日签到奖励
	todayReward := 0
	if !todayChecked {
		todayReward = s.calculateCheckinReward(continuousDays + 1)
	}

	// 4. 判断是否显示弹窗（今天未签到且是首次进入）
	showPopup := !todayChecked

	message := ""
	if showPopup {
		message = fmt.Sprintf("签到可获得%d叶小币，连续签到奖励更丰富！", todayReward)
	}

	return &model.CheckinPopupResponse{
		ShowPopup:      showPopup,
		Message:        message,
		TodayChecked:   todayChecked,
		ContinuousDays: continuousDays,
		TodayReward:    todayReward,
		CanCheckin:     !todayChecked,
	}, nil
}

// GetCheckinStats 获取签到统计
func (s *CheckinService) GetCheckinStats(ctx context.Context, userID uint) (*model.CheckinStatsResponse, error) {
	// 1. 获取总签到次数
	var totalCheckins int64
	err := s.db.WithContext(ctx).Model(&model.CheckinRecord{}).
		Where("user_id = ?", userID).Count(&totalCheckins).Error
	if err != nil {
		return nil, fmt.Errorf("查询总签到次数失败: %w", err)
	}

	// 2. 获取连续签到天数
	continuousDays, err := s.getUserContinuousDays(ctx, userID)
	if err != nil {
		logx.Errorf("获取连续签到天数失败: %v", err)
		continuousDays = 0
	}

	// 3. 获取总获得叶小币
	var totalCoins struct {
		TotalCoins int `json:"total_coins"`
	}
	err = s.db.WithContext(ctx).Table("checkin_records").
		Select("COALESCE(SUM(coins_awarded + share_coins), 0) as total_coins").
		Where("user_id = ?", userID).First(&totalCoins).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询总获得叶小币失败: %w", err)
	}

	// 4. 获取本月签到次数
	now := time.Now()
	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, -1)

	var monthlyCheckins int64
	err = s.db.WithContext(ctx).Model(&model.CheckinRecord{}).
		Where("user_id = ? AND checkin_date >= ? AND checkin_date <= ?",
			userID, startOfMonth.Format("2006-01-02"), endOfMonth.Format("2006-01-02")).
		Count(&monthlyCheckins).Error
	if err != nil {
		return nil, fmt.Errorf("查询本月签到次数失败: %w", err)
	}

	return &model.CheckinStatsResponse{
		TotalCheckins:   int(totalCheckins),
		ContinuousDays:  continuousDays,
		TotalCoins:      totalCoins.TotalCoins,
		MonthlyCheckins: int(monthlyCheckins),
	}, nil
}

// GetCheckinRewardRules 获取签到奖励规则
func (s *CheckinService) GetCheckinRewardRules(ctx context.Context) ([]model.CheckinRewardRule, error) {
	return s.getCheckinRewardRules(ctx)
}

// getUserContinuousDays 获取用户连续签到天数
func (s *CheckinService) getUserContinuousDays(ctx context.Context, userID uint) (int, error) {
	// 获取用户最近的签到记录，按日期倒序
	var records []model.CheckinRecord
	err := s.db.WithContext(ctx).Where("user_id = ?", userID).
		Order("checkin_date DESC").Limit(100).Find(&records).Error
	if err != nil {
		return 0, fmt.Errorf("查询签到记录失败: %w", err)
	}

	if len(records) == 0 {
		return 0, nil
	}

	// 从最近的签到日期开始计算连续天数
	today := time.Now()
	continuousDays := 0

	for i, record := range records {
		expectedDate := today.AddDate(0, 0, -i)
		expectedDateStr := expectedDate.Format("2006-01-02")

		if record.CheckinDate == expectedDateStr {
			continuousDays++
		} else {
			// 如果不是连续的，检查是否是昨天的记录
			if i == 0 {
				yesterday := today.AddDate(0, 0, -1).Format("2006-01-02")
				if record.CheckinDate == yesterday {
					continuousDays++
					continue
				}
			}
			break
		}
	}

	return continuousDays, nil
}

// calculateContinuousDays 计算连续签到天数（签到时使用）
func (s *CheckinService) calculateContinuousDays(ctx context.Context, userID uint, checkinDate time.Time) int {
	// 获取昨天的日期
	yesterday := checkinDate.AddDate(0, 0, -1).Format("2006-01-02")

	// 查询昨天是否有签到记录
	var yesterdayRecord model.CheckinRecord
	err := s.db.WithContext(ctx).Where("user_id = ? AND checkin_date = ?", userID, yesterday).First(&yesterdayRecord).Error

	if err == gorm.ErrRecordNotFound {
		// 昨天没有签到，连续天数重置为1
		return 1
	} else if err != nil {
		// 查询出错，默认为1
		logx.Errorf("查询昨天签到记录失败: %v", err)
		return 1
	}

	// 昨天有签到，连续天数+1
	return yesterdayRecord.ContinuousDays + 1
}

// calculateCheckinReward 计算签到奖励
func (s *CheckinService) calculateCheckinReward(continuousDays int) int {
	// 根据 coin_rules 表中的签到规则计算奖励
	// 这里先使用简单的规则，后续可以从数据库读取

	// 基础奖励
	baseReward := 1

	// 连续签到奖励
	if continuousDays >= 7 {
		return baseReward + 2 // 连续7天额外+2
	} else if continuousDays >= 3 {
		return baseReward + 1 // 连续3天额外+1
	}

	return baseReward
}

// calculateShareReward 计算分享奖励
func (s *CheckinService) calculateShareReward() int {
	// 分享奖励固定为1叶小币
	return 1
}

// getCheckinRewardRules 获取签到奖励规则
func (s *CheckinService) getCheckinRewardRules(ctx context.Context) ([]model.CheckinRewardRule, error) {
	// 从 coin_rules 表查询签到相关规则
	var rules []struct {
		CoinsAwarded int    `json:"coins_awarded"`
		Description  string `json:"description"`
	}

	err := s.db.WithContext(ctx).Table("coin_rules").
		Select("coins_awarded, description").
		Where("rule_type = ? AND enabled = ?", "CHECKIN", true).
		Find(&rules).Error

	if err != nil {
		// 如果查询失败，返回默认规则
		return []model.CheckinRewardRule{
			{Day: 1, CoinsReward: 1, ShareCoinsReward: 1},
			{Day: 3, CoinsReward: 2, ShareCoinsReward: 1},
			{Day: 7, CoinsReward: 3, ShareCoinsReward: 1},
		}, nil
	}

	// 转换为响应格式
	var rewardRules []model.CheckinRewardRule
	for i, rule := range rules {
		rewardRules = append(rewardRules, model.CheckinRewardRule{
			Day:              i + 1,
			CoinsReward:      rule.CoinsAwarded,
			ShareCoinsReward: 1, // 分享奖励固定为1
		})
	}

	// 如果没有规则，返回默认规则
	if len(rewardRules) == 0 {
		return []model.CheckinRewardRule{
			{Day: 1, CoinsReward: 1, ShareCoinsReward: 1},
			{Day: 3, CoinsReward: 2, ShareCoinsReward: 1},
			{Day: 7, CoinsReward: 3, ShareCoinsReward: 1},
		}, nil
	}

	return rewardRules, nil
}
