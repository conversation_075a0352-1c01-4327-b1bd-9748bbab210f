package bootstrap

import (
	"log"
	"yekaitai/pkg/common/model/registration"
	"yekaitai/pkg/infra/mysql"
)

// MigrateRegistrationTables 执行预约挂号相关表结构迁移
func MigrateRegistrationTables() error {
	log.Println("开始执行预约挂号相关表结构迁移...")

	// 执行预约挂号订单表结构迁移
	db := mysql.Master()
	db.Set("gorm:table_options", "COMMENT='预约挂号订单表'").AutoMigrate(&registration.AppointmentOrder{})

	log.Println("预约挂号相关表结构迁移完成")
	return nil
}
