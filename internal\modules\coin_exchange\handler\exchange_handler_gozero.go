package handler

import (
	"net/http"
	"time"

	"yekaitai/internal/modules/coin_exchange/model"
	"yekaitai/internal/modules/coin_exchange/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// ExchangeGoZeroHandler 兑换配置处理器
type ExchangeGoZeroHandler struct {
	exchangeService *service.ExchangeService
}

// NewExchangeGoZeroHandler 创建兑换配置处理器
func NewExchangeGoZeroHandler() *ExchangeGoZeroHandler {
	return &ExchangeGoZeroHandler{
		exchangeService: service.NewExchangeService(),
	}
}

// ExchangeConfigListRequest 兑换配置列表请求
type ExchangeConfigListRequest struct {
	types.PageRequest
	ItemName  string `form:"name,optional"`
	StartDate string `form:"start_date,optional"`
	EndDate   string `form:"end_date,optional"`
}

// ExchangeConfigDetailRequest 兑换配置详情请求
type ExchangeConfigDetailRequest struct {
	ConfigID uint `path:"id"`
}

// ListConfigs 获取兑换配置列表
func (h *ExchangeGoZeroHandler) ListConfigs(w http.ResponseWriter, r *http.Request) {
	var req ExchangeConfigListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 转换为service需要的参数
	params := &model.ExchangeConfigQueryParams{
		ItemName: req.ItemName,
		Page:     req.Page,
		PageSize: req.Size,
	}

	// 解析日期
	if req.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", req.StartDate)
		if err == nil {
			params.StartDate = startDate
		}
	}
	if req.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", req.EndDate)
		if err == nil {
			params.EndDate = endDate
		}
	}

	configs, total, err := h.exchangeService.ListConfigs(r.Context(), params)
	if err != nil {
		logx.Errorf("获取兑换配置列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	resp := types.NewPageResponse(configs, total, &req.PageRequest)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// CreateConfig 创建兑换配置
func (h *ExchangeGoZeroHandler) CreateConfig(w http.ResponseWriter, r *http.Request) {
	var req model.ExchangeConfigCreateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 根据类型验证必填字段
	if req.Type == model.ExchangeTypeProduct || req.Type == model.ExchangeTypeService {
		if req.ItemID == 0 {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "商品/服务ID不能为空"))
			return
		}
	} else if req.Type == model.ExchangeTypeAppointment {
		if req.TimeSlot == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "挂号时段不能为空"))
			return
		}
	}

	config := &model.ExchangeConfig{
		Type:     req.Type,
		ItemID:   req.ItemID,
		TimeSlot: req.TimeSlot,
		Operator: "admin", // 实际应该从请求上下文中获取当前登录用户
	}

	if err := h.exchangeService.CreateConfig(r.Context(), config); err != nil {
		logx.Errorf("创建兑换配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(config))
}

// DeleteConfig 删除兑换配置
func (h *ExchangeGoZeroHandler) DeleteConfig(w http.ResponseWriter, r *http.Request) {
	var req ExchangeConfigDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的兑换配置ID"))
		return
	}

	if err := h.exchangeService.DeleteConfig(r.Context(), req.ConfigID); err != nil {
		logx.Errorf("删除兑换配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// GetAvailableServicePackages 获取可用的服务套餐列表
func (h *ExchangeGoZeroHandler) GetAvailableServicePackages(w http.ResponseWriter, r *http.Request) {
	servicePackages, err := h.exchangeService.GetAvailableServicePackages(r.Context())
	if err != nil {
		logx.Errorf("获取可用服务套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取服务套餐失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(servicePackages))
}
