package wechatpay

import (
	"context"
	"fmt"
	"os"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

// WechatPayConfig 微信支付配置
type WechatPayConfig struct {
	AppID             string // 小程序AppID
	MchID             string // 商户号
	CertSerialNo      string // 商户证书序列号
	APIv3Key          string // APIv3密钥
	PrivateKeyContent string // 商户私钥内容（PEM格式字符串）
	NotifyURL         string // 支付回调URL
	RefundNotifyURL   string // 退款回调URL
}

// GetDefaultConfig 获取默认配置（从环境变量读取）
func GetDefaultConfig() *WechatPayConfig {
	return &WechatPayConfig{
		AppID:             getEnvOrDefault("WECHAT_APP_ID", "wx1234567890abcdef"),
		MchID:             getEnvOrDefault("WECHAT_MCH_ID", "1900000001"),
		CertSerialNo:      getEnvOrDefault("WECHAT_CERT_SERIAL_NO", ""),
		APIv3Key:          getEnvOrDefault("WECHAT_APIV3_KEY", ""),
		PrivateKeyContent: getEnvOrDefault("WECHAT_PRIVATE_KEY_CONTENT", ""),
		NotifyURL:         getEnvOrDefault("WECHAT_NOTIFY_URL", "https://yourdomain.com/api/wx/payment/notify"),
	}
}

// getEnvOrDefault 获取环境变量，如果不存在则返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// CreateClient 创建微信支付客户端
func CreateClient(config *WechatPayConfig) (*core.Client, error) {
	// 从配置字符串解析私钥（而不是从文件读取）
	privateKey, err := utils.LoadPrivateKey(config.PrivateKeyContent)
	if err != nil {
		return nil, fmt.Errorf("解析商户私钥失败: %w", err)
	}

	// 创建客户端
	ctx := context.Background()
	client, err := core.NewClient(
		ctx,
		option.WithWechatPayAutoAuthCipher(
			config.MchID,
			config.CertSerialNo,
			privateKey,
			config.APIv3Key,
		),
	)
	if err != nil {
		return nil, fmt.Errorf("创建微信支付客户端失败: %w", err)
	}

	return client, nil
}

// WechatPayService 微信支付服务
type WechatPayService struct {
	Client *core.Client
	Config *WechatPayConfig
}

// NewWechatPayService 创建微信支付服务
func NewWechatPayService() (*WechatPayService, error) {
	config := GetDefaultConfig()
	client, err := CreateClient(config)
	if err != nil {
		return nil, err
	}

	return &WechatPayService{
		Client: client,
		Config: config,
	}, nil
}
