package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	goodsModel "yekaitai/internal/modules/goods/model"
	"yekaitai/internal/service"
	"yekaitai/pkg/adapters/wanliniu"
	cartModel "yekaitai/pkg/common/model/cart"
	orderModel "yekaitai/pkg/common/model/order"
	userModel "yekaitai/pkg/common/model/user"
	"yekaitai/pkg/common/wechatpay"
	"yekaitai/pkg/infra/mysql"
	cartService "yekaitai/wx_internal/modules/goods/service"
	invitationService "yekaitai/wx_internal/modules/invitation/service"
	userService "yekaitai/wx_internal/modules/user/service"
	wanliniuOrderService "yekaitai/wx_internal/modules/wanliniu/service"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type OrderService struct {
	db                   *gorm.DB
	cartService          *cartService.CartService
	wanliniuOrderService *wanliniuOrderService.OrderPushService
	wanliniuService      *wanliniu.Service
}

func NewOrderService() (*OrderService, error) {
	return &OrderService{
		db:                   mysql.GetDB(),
		cartService:          cartService.NewCartService(),
		wanliniuOrderService: wanliniuOrderService.NewOrderPushService(),
		wanliniuService:      wanliniu.GetService(),
	}, nil
}

// CreateOrder 创建订单并生成支付参数
func (s *OrderService) CreateOrder(ctx context.Context, userID uint, userOpenID string, req *orderModel.CreateOrderRequest) (*orderModel.CreateOrderResponse, error) {
	// 严格验证请求参数
	if err := s.validateCreateOrderRequest(ctx, userID, req); err != nil {
		return nil, err
	}

	// 开启数据库事务
	return s.withTransaction(ctx, func(tx *gorm.DB) (*orderModel.CreateOrderResponse, error) {
		// 1. 验证购物车商品和计算金额
		orderItems, goodsAmount, err := s.validateCartAndCalculateAmount(ctx, tx, userID, req.CartIDs)
		if err != nil {
			return nil, err
		}

		// 2. 获取并验证地址信息
		addressInfo, err := s.validateAndGetAddressInfo(ctx, req.AddressID, userID)
		if err != nil {
			return nil, err
		}

		// 3. 获取用户会员等级折扣
		memberDiscount, err := s.getMemberDiscount(ctx, userID)
		if err != nil {
			logx.Errorf("获取会员等级折扣失败: %v", err)
			memberDiscount = 0 // 无折扣
		}

		// 4. 应用会员折扣
		memberDiscountAmount := goodsAmount * memberDiscount
		afterMemberDiscount := goodsAmount - memberDiscountAmount

		// 5. 验证并计算优惠券抵扣（基于会员折扣后的金额）
		couponAmount, err := s.validateAndCalculateMultipleCoupons(ctx, userID, req.CouponIDs, afterMemberDiscount)
		if err != nil {
			return nil, err
		}

		// 6. 计算运费（基于会员折扣和优惠券后的金额，积分抵扣前）
		afterCouponAmount := afterMemberDiscount - couponAmount
		shippingAmount, err := s.calculateShipping(ctx, afterCouponAmount)
		if err != nil {
			return nil, err
		}

		// 7. 验证并计算积分抵扣（基于会员折扣和优惠券后的金额，不包含运费）
		pointsAmount, err := s.validateAndCalculatePointsByFlag(ctx, userID, req.UsePoints, afterCouponAmount)
		if err != nil {
			return nil, err
		}

		// 8. 计算最终支付金额
		afterAllDiscounts := afterCouponAmount - pointsAmount
		totalAmount := afterAllDiscounts + shippingAmount
		payAmount := totalAmount // 使用真实的计算金额

		// 7. 最终验证支付金额
		if err := s.validateFinalAmount(totalAmount, payAmount); err != nil {
			return nil, err
		}

		// 7. 生成订单号
		orderNo := s.generateOrderNo()

		// 8.1. 处理优惠券ID（取第一张优惠券的ID用于记录）
		var couponID uint
		if len(req.CouponIDs) > 0 && req.CouponIDs[0] != "" {
			if len(req.CouponIDs[0]) > 1 && req.CouponIDs[0][0] == 'c' {
				if id, err := strconv.ParseUint(req.CouponIDs[0][1:], 10, 32); err == nil {
					couponID = uint(id)
				}
			}
		}

		// 8.2. 处理积分使用量（根据实际抵扣金额计算）
		var usePointsAmount int
		if req.UsePoints && pointsAmount > 0 {
			// 根据全局配置的兑换比例计算使用的积分数
			var globalConfig struct {
				ExchangeRate int `json:"exchange_rate"`
			}
			if err := s.db.WithContext(ctx).Table("coin_global_config").
				Select("exchange_rate").Where("enabled = 1").First(&globalConfig).Error; err == nil {
				usePointsAmount = int(pointsAmount * float64(globalConfig.ExchangeRate))
			}
		}

		// 9. 创建订单记录
		order := &orderModel.Order{
			OrderNo:        orderNo,
			UserID:         userID,
			Status:         orderModel.OrderStatusPending,
			TotalAmount:    totalAmount,
			GoodsAmount:    goodsAmount,
			ShippingAmount: shippingAmount,
			DiscountAmount: memberDiscountAmount, // 会员折扣金额
			CouponAmount:   couponAmount,
			PointsAmount:   pointsAmount,
			PayAmount:      payAmount,
			PayMethod:      "wechat",
			AddressID:      req.AddressID,
			AddressInfo:    addressInfo,
			CouponID:       couponID,
			UsePoints:      usePointsAmount,
			Remark:         req.Remark,
		}

		if err := tx.Create(order).Error; err != nil {
			return nil, fmt.Errorf("创建订单失败: %w", err)
		}

		// 9. 创建订单商品记录
		for _, item := range orderItems {
			item.OrderID = order.ID
			if err := tx.Create(item).Error; err != nil {
				return nil, fmt.Errorf("创建订单商品失败: %w", err)
			}
		}

		// 10. 根据支付金额决定是否调起微信支付
		var paymentParams map[string]string
		needPayment := payAmount > 0

		if needPayment {
			// 需要支付，调用微信支付接口创建支付订单
			var err error
			paymentParams, err = s.createWechatPayment(ctx, order, userOpenID)
			if err != nil {
				return nil, fmt.Errorf("创建微信支付失败: %w", err)
			}
		} else {
			// 0元订单，直接标记为已支付
			order.Status = orderModel.OrderStatusPaid
			order.PayTime = &time.Time{}
			*order.PayTime = time.Now()
			if err := tx.Save(order).Error; err != nil {
				return nil, fmt.Errorf("更新订单状态失败: %w", err)
			}
			paymentParams = map[string]string{
				"needPayment": "false",
				"message":     "订单金额为0，无需支付",
			}
		}

		// 11. 删除购物车商品
		if err := s.removeCartItems(ctx, tx, userID, req.CartIDs); err != nil {
			logx.Errorf("删除购物车商品失败: %v", err)
			// 不阻断订单创建流程
		}

		// 注意：万里牛推送将在支付成功回调时进行，此处不推送

		return &orderModel.CreateOrderResponse{
			OrderID:       order.ID,
			OrderNo:       order.OrderNo,
			TotalAmount:   totalAmount,
			PayAmount:     payAmount,
			PaymentParams: paymentParams,
			NeedPayment:   needPayment, // 新增字段，标识是否需要支付
		}, nil
	})
}

// generateOrderNo 生成订单号
func (s *OrderService) generateOrderNo() string {
	return fmt.Sprintf("ORDER%d%06d", time.Now().Unix(), time.Now().Nanosecond()%1000000)
}

// validateCartAndCalculateAmount 验证购物车商品并计算金额
func (s *OrderService) validateCartAndCalculateAmount(ctx context.Context, tx *gorm.DB, userID uint, cartIDs []uint) ([]*orderModel.OrderItem, float64, error) {
	cartList, err := s.cartService.GetCartList(ctx, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取购物车失败: %w", err)
	}

	var orderItems []*orderModel.OrderItem
	var totalAmount float64

	for _, cartID := range cartIDs {
		var foundCart *cartModel.CartResponse
		for _, cart := range cartList {
			if cart.ID == cartID {
				foundCart = cart
				break
			}
		}

		if foundCart == nil {
			return nil, 0, fmt.Errorf("购物车商品不存在: %d", cartID)
		}

		// 验证商品状态
		if foundCart.Goods == nil || foundCart.Goods.Status != 1 || foundCart.Goods.IsOnSale != 1 {
			return nil, 0, fmt.Errorf("商品已下架")
		}

		// 校验ERP库存
		if err := s.validateERPInventory(ctx, foundCart); err != nil {
			return nil, 0, err
		}

		// 构建订单商品
		var price float64
		var specName string
		if foundCart.Spec != nil {
			price = foundCart.Spec.SalePrice
			specName = foundCart.Spec.SpecName
		} else {
			price = foundCart.Goods.TagPrice
		}

		orderItem := &orderModel.OrderItem{
			GoodsID:    foundCart.GoodsID,
			SpecID:     foundCart.SpecID,
			GoodsName:  foundCart.Goods.GoodsName,
			SpecName:   specName,
			GoodsPic:   foundCart.Goods.Pic,
			Price:      price,
			Quantity:   foundCart.Quantity,
			TotalPrice: price * float64(foundCart.Quantity),
		}

		orderItems = append(orderItems, orderItem)
		totalAmount += orderItem.TotalPrice
	}

	return orderItems, totalAmount, nil
}

// getAddressInfo 获取地址信息
func (s *OrderService) getAddressInfo(ctx context.Context, addressID, userID uint) (string, error) {
	var address userModel.Address
	if err := s.db.WithContext(ctx).Table("addresses").Where("id = ? AND user_id = ?", addressID, userID).First(&address).Error; err != nil {
		return "", fmt.Errorf("收货地址不存在")
	}

	addressInfo, _ := json.Marshal(address)
	return string(addressInfo), nil
}

// calculateShipping 计算运费（增强版）
func (s *OrderService) calculateShipping(ctx context.Context, goodsAmount float64) (float64, error) {
	// 查询运费配置表
	var shippingConfig goodsModel.ShippingConfig
	err := s.db.WithContext(ctx).
		Table("shipping_config").
		Where("is_active = ?", 1).
		Order("id DESC").
		First(&shippingConfig).Error

	if err != nil {
		logx.Errorf("查询运费配置失败: %v", err)
		return 0, fmt.Errorf("运费配置未设置，请联系管理员配置运费规则")
	}

	// 根据配置类型计算运费
	if shippingConfig.ConfigType == 1 {
		// 全部包邮
		logx.Infof("运费计算：全部包邮，运费=0")
		return 0, nil
	} else if shippingConfig.ConfigType == 2 {
		// 满减运费
		if shippingConfig.FreeAmount > 0 && goodsAmount >= shippingConfig.FreeAmount {
			logx.Infof("运费计算：满%.2f元免运费，商品金额%.2f，运费=0", shippingConfig.FreeAmount, goodsAmount)
			return 0, nil
		} else {
			logx.Infof("运费计算：商品金额%.2f，未达到免运费门槛%.2f，运费=%.2f", goodsAmount, shippingConfig.FreeAmount, shippingConfig.ShippingFee)
			return shippingConfig.ShippingFee, nil
		}
	}

	// 默认返回配置的运费
	return shippingConfig.ShippingFee, nil
}

// createWechatPayment 创建微信支付
func (s *OrderService) createWechatPayment(ctx context.Context, order *orderModel.Order, userOpenID string) (map[string]string, error) {
	amountInCents := int64(order.PayAmount * 100)

	req := &wechatpay.CreateJSAPIPaymentRequest{
		OrderNo:     order.OrderNo,
		Amount:      amountInCents,
		Description: fmt.Sprintf("商城订单-%s", order.OrderNo),
		OpenID:      userOpenID,
		Attach:      fmt.Sprintf("order_id:%d", order.ID),
		TimeExpire:  time.Now().Add(15 * time.Minute), // 设置15分钟有效期
	}

	paymentService := wechatpay.GetGlobalPaymentService()
	if paymentService == nil {
		return nil, fmt.Errorf("微信支付服务未初始化")
	}

	resp, err := paymentService.CreateJSAPIPayment(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp.PaymentParams, nil
}

// removeCartItems 删除购物车商品
func (s *OrderService) removeCartItems(ctx context.Context, tx *gorm.DB, userID uint, cartIDs []uint) error {
	return s.cartService.BatchRemoveFromCart(ctx, cartIDs, userID)
}

// withTransaction 执行事务
func (s *OrderService) withTransaction(ctx context.Context, fn func(tx *gorm.DB) (*orderModel.CreateOrderResponse, error)) (*orderModel.CreateOrderResponse, error) {
	return fn(s.db.WithContext(ctx))
}

// UpdateOrderPaymentSuccess 更新订单支付成功状态
func (s *OrderService) UpdateOrderPaymentSuccess(ctx context.Context, orderNo, wechatOrderID string) error {
	now := time.Now()
	return s.db.WithContext(ctx).Model(&orderModel.Order{}).
		Where("order_no = ?", orderNo).
		Updates(map[string]interface{}{
			"status":              orderModel.OrderStatusPaid,
			"wechat_pay_order_id": wechatOrderID,
			"pay_time":            &now,
		}).Error
}

// GetOrderList 获取订单列表
func (s *OrderService) GetOrderList(ctx context.Context, userID uint, status, page, limit string) (interface{}, error) {
	// TODO: 实现分页和状态筛选逻辑
	var orders []orderModel.Order
	query := s.db.WithContext(ctx).Where("user_id = ?", userID)

	// 状态筛选
	if status != "" && status != "all" {
		query = query.Where("status = ?", status)
	}

	err := query.Order("created_at DESC").Find(&orders).Error
	if err != nil {
		return nil, fmt.Errorf("获取订单列表失败: %w", err)
	}

	return orders, nil
}

// GetOrderDetail 获取订单详情（包含退款信息）
func (s *OrderService) GetOrderDetail(ctx context.Context, userID uint, orderID string) (*orderModel.OrderDetailResponse, error) {
	var order orderModel.Order
	err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", orderID, userID).
		First(&order).Error
	if err != nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 获取订单商品
	var orderItems []orderModel.OrderItem
	err = s.db.WithContext(ctx).
		Where("order_id = ?", order.ID).
		Find(&orderItems).Error
	if err != nil {
		return nil, fmt.Errorf("获取订单商品失败: %w", err)
	}

	// 根据订单状态判断发货状态（不再调用外部接口）
	isShipped := order.Status >= orderModel.OrderStatusShipped

	// 计算各种操作权限
	canRefund := !isShipped && order.Status == orderModel.OrderStatusPaid && order.RefundStatus == orderModel.RefundStatusNone
	canCancel := (order.Status == orderModel.OrderStatusPending || order.Status == orderModel.OrderStatusPaid) && !isShipped
	canViewLogistics := order.Status >= orderModel.OrderStatusShipped && order.ExpressNo != ""
	canConfirmReceive := order.Status == orderModel.OrderStatusShipped

	// 获取退款按钮状态
	refundButtonStatus := s.getRefundButtonStatus(&order, isShipped)

	// 构建详情响应
	result := &orderModel.OrderDetailResponse{
		Order:              order,
		Items:              orderItems,
		StatusText:         orderModel.GetOrderStatusText(order.Status),
		RefundStatusText:   orderModel.GetRefundStatusText(order.RefundStatus),
		CanRefund:          canRefund,
		CanCancel:          canCancel,
		CanViewLogistics:   canViewLogistics,
		CanConfirmReceive:  canConfirmReceive,
		RefundButtonStatus: &refundButtonStatus,
	}

	return result, nil
}

// getRefundButtonStatus 获取退款按钮状态
func (s *OrderService) getRefundButtonStatus(order *orderModel.Order, isShipped bool) orderModel.RefundButtonStatus {
	switch order.RefundStatus {
	case orderModel.RefundStatusNone:
		if isShipped {
			return orderModel.RefundButtonStatus{
				CanRefund:     false,
				ButtonText:    "不可退款",
				DisableReason: "商品已发货，不支持退款",
				RefundStatus:  order.RefundStatus,
			}
		}
		if order.Status == orderModel.OrderStatusPaid {
			return orderModel.RefundButtonStatus{
				CanRefund:     true,
				ButtonText:    "申请退款",
				DisableReason: "",
				RefundStatus:  order.RefundStatus,
			}
		}
		return orderModel.RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "不可退款",
			DisableReason: "订单状态不支持退款",
			RefundStatus:  order.RefundStatus,
		}
	case orderModel.RefundStatusApplying:
		return orderModel.RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "退款申请中",
			DisableReason: "退款申请已提交，请等待处理",
			RefundStatus:  order.RefundStatus,
		}
	case orderModel.RefundStatusProcessing:
		return orderModel.RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "退款处理中",
			DisableReason: "退款正在处理中，请耐心等待",
			RefundStatus:  order.RefundStatus,
		}
	case orderModel.RefundStatusCompleted:
		return orderModel.RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "已退款",
			DisableReason: "退款已完成",
			RefundStatus:  order.RefundStatus,
		}
	default:
		return orderModel.RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "不可退款",
			DisableReason: "未知状态",
			RefundStatus:  order.RefundStatus,
		}
	}
}

// CancelOrder 取消订单
func (s *OrderService) CancelOrder(ctx context.Context, userID uint, orderID string) error {
	// 查询订单
	var order orderModel.Order
	err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", orderID, userID).
		First(&order).Error
	if err != nil {
		return fmt.Errorf("订单不存在")
	}

	// 检查订单状态是否可以取消
	switch order.Status {
	case orderModel.OrderStatusPending:
		// 待支付订单：直接关闭
		return s.cancelUnpaidOrder(ctx, &order)
	case orderModel.OrderStatusPaid:
		// 已支付待发货订单：自动创建微信退款
		return s.cancelPaidOrder(ctx, &order)
	case orderModel.OrderStatusShipped, orderModel.OrderStatusCompleted:
		// 已发货的订单不能取消
		return fmt.Errorf("订单已发货，不支持取消，请联系客服处理售后")
	case orderModel.OrderStatusCancelled:
		return fmt.Errorf("订单已取消")
	case orderModel.OrderStatusRefunded:
		return fmt.Errorf("订单已退款")
	default:
		return fmt.Errorf("当前订单状态不支持取消")
	}
}

// cancelUnpaidOrder 取消未支付订单
func (s *OrderService) cancelUnpaidOrder(ctx context.Context, order *orderModel.Order) error {
	logx.Infof("取消未支付订单: 订单号=%s", order.OrderNo)

	// 开启事务
	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 更新订单状态为已取消
	err := tx.Model(order).Updates(map[string]interface{}{
		"status":        orderModel.OrderStatusCancelled,
		"cancel_time":   time.Now(),
		"cancel_reason": "用户主动取消订单",
		"updated_at":    time.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 2. 恢复商品库存
	err = s.restoreGoodsStockForOrder(ctx, tx, order)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("恢复商品库存失败: %w", err)
	}

	// 3. 恢复优惠券和叶小币（修正：取消订单时需要恢复）
	err = s.restoreCouponAndPoints(ctx, tx, order)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("恢复优惠券和叶小币失败: %w", err)
	}

	// 3. 关闭微信支付订单
	go func() {
		if closeErr := s.CloseWechatPayOrder(ctx, order.OrderNo); closeErr != nil {
			logx.Errorf("关闭微信支付订单失败: 订单号=%s, 错误=%v", order.OrderNo, closeErr)
		}
	}()

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	logx.Infof("未支付订单取消成功: 订单号=%s", order.OrderNo)
	return nil
}

// cancelPaidOrder 取消已支付订单（自动创建退款）
func (s *OrderService) cancelPaidOrder(ctx context.Context, order *orderModel.Order) error {
	logx.Infof("取消已支付订单，自动创建退款: 订单号=%s", order.OrderNo)

	// 1. 更新订单状态为已取消，同时设置退款状态为申请退款
	err := s.db.WithContext(ctx).Model(order).Updates(map[string]interface{}{
		"status":        orderModel.OrderStatusCancelled,
		"refund_status": 1, // 申请退款
		"refund_reason": "用户主动取消订单",
		"refund_amount": order.PayAmount, // 使用真实的支付金额
		"cancel_time":   time.Now(),
		"cancel_reason": "用户主动取消订单",
		"updated_at":    time.Now(),
	}).Error
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 2. 自动创建微信退款（使用订单实际支付金额）
	_, err = s.CreateWechatRefund(ctx, order.ID, order.PayAmount, "用户主动取消订单")
	if err != nil {
		// 如果退款创建失败，回滚订单状态
		s.db.WithContext(ctx).Model(order).Update("status", orderModel.OrderStatusPaid)
		return fmt.Errorf("创建退款失败: %w", err)
	}

	logx.Infof("已支付订单取消成功，退款已创建: 订单号=%s", order.OrderNo)
	return nil
}

// ConfirmReceive 确认收货
func (s *OrderService) ConfirmReceive(ctx context.Context, userID uint, orderID string) error {
	// 查询订单
	var order orderModel.Order
	err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", orderID, userID).
		First(&order).Error
	if err != nil {
		return fmt.Errorf("订单不存在")
	}

	// 检查订单状态是否可以确认收货（已发货）
	if order.Status != orderModel.OrderStatusShipped {
		return fmt.Errorf("当前订单状态不支持确认收货")
	}

	// 更新订单状态为已完成
	err = s.db.WithContext(ctx).Model(&order).Updates(map[string]interface{}{
		"status":       orderModel.OrderStatusCompleted,
		"receive_time": time.Now(),
	}).Error
	if err != nil {
		return fmt.Errorf("确认收货失败: %w", err)
	}

	return nil
}

// RefundOrder 申请订单退款
func (s *OrderService) RefundOrder(ctx context.Context, userID uint, req *orderModel.RefundOrderRequest) (*orderModel.RefundOrderResponse, error) {
	// 查询订单
	var order orderModel.Order
	err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", req.OrderID, userID).
		First(&order).Error
	if err != nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 检查订单状态是否支持退款
	if order.Status != orderModel.OrderStatusPaid {
		return nil, fmt.Errorf("订单状态不支持退款")
	}

	// 检查是否已经申请过退款
	if order.RefundStatus != orderModel.RefundStatusNone {
		return nil, fmt.Errorf("订单已申请退款")
	}

	// 检查发货状态（查询万里牛ERP）
	shippingStatus, err := s.CheckOrderShippingStatus(ctx, order.OrderNo)
	if err != nil {
		logx.Errorf("检查订单发货状态失败: %v", err)
		// 检查失败时，保守处理：如果本地状态>=已发货，认为已发货
		if order.Status >= orderModel.OrderStatusShipped {
			return nil, fmt.Errorf("订单已发货，不支持退款")
		}
	} else if shippingStatus.IsShipped {
		return nil, fmt.Errorf("订单已发货，不支持退款")
	}

	// 检查退款金额是否合理
	if req.RefundAmount > order.PayAmount {
		return nil, fmt.Errorf("退款金额不能超过支付金额")
	}

	// 生成退款单号
	refundNo := s.generateRefundNo()

	// 更新订单退款状态
	now := time.Now()
	err = s.db.WithContext(ctx).Model(&order).Updates(map[string]interface{}{
		"refund_status": orderModel.RefundStatusApplying,
		"refund_amount": req.RefundAmount,
		"refund_reason": req.RefundReason,
		"refund_no":     refundNo,
		"refund_time":   &now,
	}).Error
	if err != nil {
		return nil, fmt.Errorf("更新订单退款状态失败: %w", err)
	}

	// TODO: 调用微信支付退款接口
	// 这里应该调用微信支付的退款API

	// 暂时直接设置为退款中状态
	err = s.db.WithContext(ctx).Model(&order).Update("refund_status", orderModel.RefundStatusProcessing).Error
	if err != nil {
		logx.Errorf("更新退款状态为处理中失败: %v", err)
	}

	return &orderModel.RefundOrderResponse{
		RefundNo: refundNo,
		Status:   "applying",
		Message:  "退款申请已提交",
	}, nil
}

// generateRefundNo 生成退款单号
func (s *OrderService) generateRefundNo() string {
	return fmt.Sprintf("REFUND%d%06d", time.Now().Unix(), time.Now().Nanosecond()%1000000)
}

// CheckOrderRefundability 检查订单是否可以退款和查看物流
func (s *OrderService) CheckOrderRefundability(ctx context.Context, userID uint, orderID string) (bool, bool, error) {
	var order orderModel.Order
	err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", orderID, userID).
		First(&order).Error
	if err != nil {
		return false, false, fmt.Errorf("订单不存在")
	}

	// 可以退款的条件：已支付且未发货且未申请过退款
	canRefund := order.Status == orderModel.OrderStatusPaid &&
		order.ShipTime == nil &&
		order.RefundStatus == orderModel.RefundStatusNone

	// 可以查看物流的条件：已发货且有快递单号
	canViewLogistics := order.Status >= orderModel.OrderStatusShipped &&
		order.ExpressNo != ""

	return canRefund, canViewLogistics, nil
}

// GetOrderDetailWithRefundInfo 获取包含退款信息的订单详情
func (s *OrderService) GetOrderDetailWithRefundInfo(ctx context.Context, userID uint, orderID string) (*orderModel.OrderDetailResponse, error) {
	var order orderModel.Order
	err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", orderID, userID).
		First(&order).Error
	if err != nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 获取订单商品
	var orderItems []orderModel.OrderItem
	err = s.db.WithContext(ctx).
		Where("order_id = ?", order.ID).
		Find(&orderItems).Error
	if err != nil {
		return nil, fmt.Errorf("获取订单商品失败: %w", err)
	}

	// 检查发货状态
	shippingStatus, err := s.CheckOrderShippingStatus(ctx, order.OrderNo)
	isShipped := false
	if err != nil {
		logx.Errorf("检查订单发货状态失败: %v", err)
		// 检查失败时，根据本地状态判断
		isShipped = order.Status >= orderModel.OrderStatusShipped
	} else {
		isShipped = shippingStatus.IsShipped
	}

	// 检查退款和物流查看权限
	canRefund := !isShipped && order.Status == orderModel.OrderStatusPaid && order.RefundStatus == orderModel.RefundStatusNone
	canViewLogistics := order.Status >= orderModel.OrderStatusShipped && order.ExpressNo != ""

	// 获取退款按钮状态
	refundButtonStatus := order.GetRefundButtonStatus(isShipped)

	// 构建详情响应
	result := &orderModel.OrderDetailResponse{
		Order:              order,
		StatusText:         orderModel.GetOrderStatusText(order.Status),
		RefundStatusText:   orderModel.GetRefundStatusText(order.RefundStatus),
		CanRefund:          canRefund,
		CanViewLogistics:   canViewLogistics,
		RefundButtonStatus: &refundButtonStatus,
	}

	// 设置订单商品
	result.Items = orderItems

	return result, nil
}

// UpdateOrderStatusByPayment 根据支付结果更新订单状态
func (s *OrderService) UpdateOrderStatusByPayment(ctx context.Context, outTradeNo, transactionID, tradeState string) error {
	// 根据微信支付状态更新本地订单状态
	var status int
	switch tradeState {
	case "SUCCESS":
		status = orderModel.OrderStatusPaid
	case "REFUND":
		status = orderModel.OrderStatusRefunded
	case "CLOSED":
		status = orderModel.OrderStatusCancelled
	default:
		return fmt.Errorf("不支持的支付状态: %s", tradeState)
	}

	// 使用事务处理订单状态更新和商品销售数量更新
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 查询订单信息，用于后续记录用户消费
		var order orderModel.Order
		if err := tx.Where("order_no = ?", outTradeNo).First(&order).Error; err != nil {
			logx.Errorf("查询订单失败: orderNo=%s, error=%v", outTradeNo, err)
			// 继续处理，不影响支付流程
		}

		// 更新订单状态
		result := tx.Model(&orderModel.Order{}).
			Where("order_no = ?", outTradeNo).
			Updates(map[string]interface{}{
				"status":                status,
				"wechat_transaction_id": transactionID, // 微信交易号
				"pay_time":              time.Now(),    // 同时更新支付时间
				"updated_at":            time.Now(),
			})

		if result.Error != nil {
			return fmt.Errorf("更新订单支付状态失败: %w", result.Error)
		}

		logx.Infof("订单状态更新成功: %s -> %d", outTradeNo, status)

		// 如果支付成功，更新商品销售单数、扣除优惠券和叶小币、记录用户消费
		if tradeState == "SUCCESS" {
			if err := s.updateGoodsSalesCount(ctx, tx, outTradeNo); err != nil {
				logx.Errorf("更新商品销售单数失败: 订单号=%s, 错误=%v", outTradeNo, err)
				// 不阻断支付流程，只记录错误
			}

			// 扣除优惠券和叶小币（修正：支付成功时才实际扣除）
			if err := s.deductCouponAndPoints(ctx, tx, &order); err != nil {
				logx.Errorf("扣除优惠券和叶小币失败: 订单号=%s, 错误=%v", outTradeNo, err)
				// 不阻断支付流程，只记录错误
			}

			// 异步处理消费积分奖励和等级升级
			go func() {
				// 记录用户消费并触发等级升级
				userConsumptionService := userService.NewUserConsumptionService()
				if err := userConsumptionService.UpdateUserConsumption(order.UserID, order.PayAmount, "商品订单", order.OrderNo); err != nil {
					logx.Errorf("记录用户消费失败: userID=%d, amount=%.2f, orderNo=%s, error=%v", order.UserID, order.PayAmount, order.OrderNo, err)
				} else {
					logx.Infof("记录用户消费成功: userID=%d, amount=%.2f, orderNo=%s", order.UserID, order.PayAmount, order.OrderNo)
				}

				// 处理消费积分奖励（使用新的叶小币奖励系统）
				coinRewardService := service.NewCoinRewardService()
				if err := coinRewardService.ProcessConsumptionReward(context.Background(), order.UserID, order.PayAmount, order.OrderNo); err != nil {
					logx.Errorf("消费积分奖励失败: userID=%d, amount=%.2f, orderNo=%s, error=%v", order.UserID, order.PayAmount, order.OrderNo, err)
				} else {
					logx.Infof("消费积分奖励成功: userID=%d, amount=%.2f, orderNo=%s", order.UserID, order.PayAmount, order.OrderNo)
				}

				// 处理邀请首单奖励
				invitationSvc := invitationService.NewInvitationService()
				if err := invitationSvc.ProcessInvitationOrderReward(context.Background(), order.UserID, order.OrderNo); err != nil {
					logx.Errorf("邀请首单奖励失败: userID=%d, amount=%.2f, orderNo=%s, error=%v", order.UserID, order.PayAmount, order.OrderNo, err)
				} else {
					logx.Infof("邀请首单奖励处理完成: userID=%d, amount=%.2f, orderNo=%s", order.UserID, order.PayAmount, order.OrderNo)
				}
			}()

			// 推送订单到万里牛ERP（异步处理，延迟1秒确保订单状态更新完成）
			go func() {
				time.Sleep(1 * time.Second) // 延迟1秒，确保订单状态更新完成
				logx.Infof("开始推送订单到万里牛ERP: 订单号=%s", outTradeNo)

				err := s.wanliniuOrderService.PushOrderToERP(context.Background(), outTradeNo)
				if err != nil {
					logx.Errorf("推送订单到万里牛ERP失败: 订单号=%s, 错误=%v", outTradeNo, err)
				} else {
					logx.Infof("订单支付成功，已推送到万里牛ERP: %s", outTradeNo)
				}
			}()

			// 记录用户消费记录（异步处理）
			if order.ID > 0 && order.UserID > 0 {
				go func(o orderModel.Order) {
					// 导入用户消费服务，使用别名避免冲突
					userSvc := userService.NewUserConsumptionService()

					// 确定订单类型
					// 从订单号或其他特征判断订单类型
					// 这里假设用OrderNo前缀判断类型
					orderType := "goods" // 默认为商品订单
					if len(o.OrderNo) > 2 {
						prefix := o.OrderNo[:2]
						switch prefix {
						case "GD": // 商品订单
							orderType = "goods"
						case "SV": // 服务订单
							orderType = "service"
						case "AC": // 活动订单
							orderType = "activity"
						}
					}

					// 计算实际支付金额
					amount := o.PayAmount
					if amount <= 0 {
						amount = o.TotalAmount - o.DiscountAmount
					}

					// 使用UpdateUserConsumption方法，会自动触发等级检查
					recordErr := userSvc.UpdateUserConsumption(o.UserID, amount, orderType, o.OrderNo)

					if recordErr != nil {
						logx.Errorf("记录用户消费失败: type=%s, userID=%d, amount=%.2f, orderNo=%s, error=%v",
							orderType, o.UserID, amount, o.OrderNo, recordErr)
					} else {
						logx.Infof("记录用户消费成功: type=%s, userID=%d, amount=%.2f, orderNo=%s",
							orderType, o.UserID, amount, o.OrderNo)
					}
				}(order)
			}
		}

		return nil
	})
}

// UpdateOrderRefundStatus 更新订单退款状态
func (s *OrderService) UpdateOrderRefundStatus(ctx context.Context, outTradeNo, outRefundNo, refundStatus string) error {
	logx.Info("===== 开始更新订单退款状态 =====")
	logx.Infof("[退款状态更新] 订单号: %s", outTradeNo)
	logx.Infof("[退款状态更新] 退款单号: %s", outRefundNo)
	logx.Infof("[退款状态更新] 微信退款状态: %s", refundStatus)

	// 根据退款状态更新订单状态和退款状态
	var status int
	var refundStatusCode int
	switch refundStatus {
	case "SUCCESS":
		status = orderModel.OrderStatusRefunded
		refundStatusCode = 3 // 已退款
		logx.Info("[退款状态更新] 退款成功，将订单状态设置为已退款，退款状态设置为已退款")
	case "PROCESSING":
		// 退款处理中，订单状态设为已取消，退款状态设为退款中
		status = orderModel.OrderStatusCancelled
		refundStatusCode = 2 // 退款中
		logx.Info("[退款状态更新] 退款处理中，将订单状态设置为已取消，退款状态设置为退款中")
	case "CLOSED":
		// 退款关闭，恢复到已支付状态，退款状态设为无退款
		status = orderModel.OrderStatusPaid
		refundStatusCode = 0 // 无退款
		logx.Info("[退款状态更新] 退款关闭，恢复订单状态为已支付，退款状态设置为无退款")
	default:
		logx.Errorf("[退款状态更新] 不支持的退款状态: %s", refundStatus)
		return fmt.Errorf("不支持的退款状态: %s", refundStatus)
	}

	// 查询订单信息（用于推送万里牛售后单）
	logx.Info("[退款状态更新] 查询订单信息...")
	var order orderModel.Order
	err := s.db.WithContext(ctx).Where("order_no = ?", outTradeNo).First(&order).Error
	if err != nil {
		logx.Errorf("[退款状态更新] 查询订单失败: %v", err)
		return fmt.Errorf("查询订单失败: %w", err)
	}
	logx.Infof("[退款状态更新] 订单查询成功: ID=%d, 当前状态=%d, 退款原因=%s, 退款金额=%.2f",
		order.ID, order.Status, order.RefundReason, order.RefundAmount)

	// 使用GORM更新订单状态和退款状态
	logx.Info("[退款状态更新] 更新订单状态到数据库...")
	logx.Infof("[退款状态更新] 更新参数: status=%d, refund_status=%d, refund_no=%s", status, refundStatusCode, outRefundNo)

	result := s.db.WithContext(ctx).
		Model(&orderModel.Order{}).
		Where("order_no = ?", outTradeNo).
		Updates(map[string]interface{}{
			"status":        status,
			"refund_status": refundStatusCode,
			"refund_no":     outRefundNo,
			"updated_at":    time.Now(),
		})

	if result.Error != nil {
		logx.Errorf("[退款状态更新] 更新订单状态失败: %v", result.Error)
		return fmt.Errorf("更新订单退款状态失败: %w", result.Error)
	}
	logx.Infof("[退款状态更新] 订单状态更新成功: %s -> status=%d, refund_status=%d (影响行数: %d)",
		outTradeNo, status, refundStatusCode, result.RowsAffected)

	// 只有在退款成功时才推送万里牛售后单和恢复优惠券叶小币（修正推送时机）
	if refundStatus == "SUCCESS" {
		logx.Info("[退款状态更新] 退款成功，开始后续处理...")

		// 异步推送售后单到万里牛ERP
		if s.wanliniuOrderService != nil {
			logx.Info("[退款状态更新] 异步推送售后单到万里牛ERP...")
			go func() {
				pushCtx := context.Background()
				logx.Infof("[万里牛推送] 开始推送售后单: 订单号=%s, 退款单号=%s, 退款原因=%s, 退款金额=%.2f",
					order.OrderNo, outRefundNo, order.RefundReason, order.RefundAmount)

				err := s.wanliniuOrderService.PushRefundToERP(pushCtx, order.OrderNo, outRefundNo, order.RefundReason, order.RefundAmount)
				if err != nil {
					logx.Errorf("[万里牛推送] 推送售后单到万里牛ERP失败: 订单号=%s, 退款单号=%s, 错误=%v", order.OrderNo, outRefundNo, err)
				} else {
					logx.Infof("[万里牛推送] 微信退款成功，已推送售后单到万里牛ERP: 订单号=%s, 退款单号=%s", order.OrderNo, outRefundNo)
				}
			}()
		} else {
			logx.Infof("[退款状态更新] 万里牛订单服务未初始化，跳过售后单推送")
		}

		// 恢复优惠券和叶小币（修正：退款成功时需要恢复）
		logx.Info("[退款状态更新] 开始恢复优惠券和叶小币...")
		err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			return s.restoreCouponAndPoints(ctx, tx, &order)
		})
		if err != nil {
			logx.Errorf("[退款状态更新] 退款成功但恢复优惠券和叶小币失败: 订单号=%s, 错误=%v", order.OrderNo, err)
			// 不阻断退款流程，只记录错误
		} else {
			logx.Infof("[退款状态更新] 退款成功，已恢复优惠券和叶小币: 订单号=%s", order.OrderNo)
		}
	} else {
		logx.Infof("[退款状态更新] 退款状态为%s，跳过万里牛推送和优惠券恢复", refundStatus)
	}

	logx.Info("===== 订单退款状态更新完成 =====")
	return nil
}

// GetOrderByID 根据订单ID获取订单信息
func (s *OrderService) GetOrderByID(ctx context.Context, orderID uint) (*orderModel.Order, error) {
	var order orderModel.Order
	if err := s.db.WithContext(ctx).Where("id = ?", orderID).First(&order).Error; err != nil {
		return nil, fmt.Errorf("订单不存在")
	}
	return &order, nil
}

// UpdateOrderStatus 更新订单状态
func (s *OrderService) UpdateOrderStatus(ctx context.Context, orderID uint, status string) error {
	return s.db.WithContext(ctx).Model(&orderModel.Order{}).Where("id = ?", orderID).Update("status", status).Error
}

// CreateWechatPayOrder 创建微信支付订单
func (s *OrderService) CreateWechatPayOrder(ctx context.Context, orderID uint, userOpenID string) (*wechatpay.CreateJSAPIPaymentResponse, error) {
	// 查询订单
	order, err := s.GetOrderByID(ctx, orderID)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	if order.Status != orderModel.OrderStatusPending {
		return nil, fmt.Errorf("订单状态不允许支付: %d", order.Status)
	}

	// 构建支付请求
	paymentService := wechatpay.GetGlobalPaymentService()
	if paymentService == nil {
		return nil, fmt.Errorf("微信支付服务未初始化")
	}

	req := &wechatpay.CreateJSAPIPaymentRequest{
		OrderNo:     order.OrderNo,
		Description: fmt.Sprintf("药库泰订单-%s", order.OrderNo),
		Amount:      int64(order.PayAmount * 100), // 使用实际支付金额，转换为分
		OpenID:      userOpenID,
		TimeExpire:  time.Now().Add(15 * time.Minute), // 15分钟后过期
	}

	resp, err := paymentService.CreateJSAPIPayment(ctx, req)
	if err != nil {
		logx.Errorf("创建微信支付订单失败: orderID=%d, error=%v", orderID, err)
		return nil, fmt.Errorf("创建微信支付订单失败: %w", err)
	}

	logx.Infof("创建微信支付订单成功: orderID=%d, orderNo=%s, prepayID=%s",
		orderID, order.OrderNo, resp.PrepayID)

	return resp, nil
}

// QueryWechatPayOrder 查询微信支付订单状态
func (s *OrderService) QueryWechatPayOrder(ctx context.Context, orderNo string) (*wechatpay.QueryOrderResponse, error) {
	paymentService := wechatpay.GetGlobalPaymentService()
	if paymentService == nil {
		return nil, fmt.Errorf("微信支付服务未初始化")
	}

	req := &wechatpay.QueryOrderRequest{
		OutTradeNo: orderNo,
	}

	resp, err := paymentService.QueryOrder(ctx, req)
	if err != nil {
		logx.Errorf("查询微信支付订单失败: orderNo=%s, error=%v", orderNo, err)
		return nil, fmt.Errorf("查询微信支付订单失败: %w", err)
	}

	logx.Infof("查询微信支付订单成功: orderNo=%s, status=%s", orderNo, resp.TradeState)
	return resp, nil
}

// CreateWechatRefund 创建微信退款
func (s *OrderService) CreateWechatRefund(ctx context.Context, orderID uint, refundAmount float64, reason string) (*wechatpay.CreateRefundResponse, error) {
	// 查询订单
	order, err := s.GetOrderByID(ctx, orderID)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	// if order.Status != orderModel.OrderStatusPaid {
	// 	return nil, fmt.Errorf("订单状态不允许退款: %s", order.Status)
	// }

	// 生成退款单号
	refundNo := fmt.Sprintf("R%s%d", order.OrderNo, time.Now().Unix())

	// 启用真实的微信支付退款
	refundService := wechatpay.GetGlobalRefundService()
	if refundService == nil {
		return nil, fmt.Errorf("微信退款服务未初始化")
	}

	req := &wechatpay.CreateRefundRequest{
		OutRefundNo:  refundNo,
		OutTradeNo:   order.OrderNo,
		Reason:       reason,
		RefundAmount: int64(refundAmount * 100),    // 转换为分（使用真实退款金额）
		TotalAmount:  int64(order.PayAmount * 100), // 原订单金额，转换为分（使用真实订单金额）
		// NotifyURL 不需要设置，退款服务内部会使用配置中的 RefundNotifyURL
	}

	resp, err := refundService.CreateRefund(ctx, req)
	if err != nil {
		logx.Errorf("创建微信退款失败: orderID=%d, error=%v", orderID, err)
		return nil, fmt.Errorf("创建微信退款失败: %w", err)
	}

	// 更新订单状态为退款中
	err = s.UpdateOrderRefundStatus(ctx, order.OrderNo, resp.OutRefundNo, "PROCESSING")
	if err != nil {
		logx.Errorf("更新订单退款状态失败: orderID=%d, error=%v", orderID, err)
	}

	logx.Infof("创建微信退款成功: orderID=%d, refundNo=%s, refundID=%s",
		orderID, refundNo, resp.RefundID)

	// 注意：万里牛售后单推送已移至微信退款成功回调中（UpdateOrderRefundStatus方法）
	// 这样确保只有在退款真正成功后才推送售后单到万里牛ERP

	return resp, nil
}

// QueryWechatRefund 查询微信退款状态
func (s *OrderService) QueryWechatRefund(ctx context.Context, refundNo string) (*wechatpay.QueryRefundResponse, error) {
	refundService := wechatpay.GetGlobalRefundService()
	if refundService == nil {
		return nil, fmt.Errorf("微信退款服务未初始化")
	}

	req := &wechatpay.QueryRefundRequest{
		OutRefundNo: refundNo,
	}

	resp, err := refundService.QueryRefund(ctx, req)
	if err != nil {
		logx.Errorf("查询微信退款失败: refundNo=%s, error=%v", refundNo, err)
		return nil, fmt.Errorf("查询微信退款失败: %w", err)
	}

	logx.Infof("查询微信退款成功: refundNo=%s, status=%s", refundNo, resp.Status)
	return resp, nil
}

// CloseWechatPayOrder 关闭微信支付订单
func (s *OrderService) CloseWechatPayOrder(ctx context.Context, orderNo string) error {
	paymentService := wechatpay.GetGlobalPaymentService()
	if paymentService == nil {
		return fmt.Errorf("微信支付服务未初始化")
	}

	req := &wechatpay.CloseOrderRequest{
		OutTradeNo: orderNo,
	}

	err := paymentService.CloseOrder(ctx, req)
	if err != nil {
		logx.Errorf("关闭微信支付订单失败: orderNo=%s, error=%v", orderNo, err)
		return fmt.Errorf("关闭微信支付订单失败: %w", err)
	}

	logx.Infof("关闭微信支付订单成功: orderNo=%s", orderNo)
	return nil
}

// CheckOrderShippingStatus 检查订单发货状态
func (s *OrderService) CheckOrderShippingStatus(ctx context.Context, orderNo string) (*orderModel.ShippingStatusResponse, error) {
	// 首先检查本地订单状态
	var order orderModel.Order
	if err := s.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 如果本地已有发货信息，直接返回
	if order.Status >= orderModel.OrderStatusShipped && order.ShipTime != nil {
		return &orderModel.ShippingStatusResponse{
			IsShipped:      true,
			ShipTime:       order.ShipTime.Format("2006-01-02 15:04:05"),
			ExpressCompany: order.ExpressCompany,
			ExpressNo:      order.ExpressNo,
			CanRefund:      false,
		}, nil
	}

	// 查询万里牛ERP订单状态
	wanliniuService := wanliniu.GetService()
	if wanliniuService == nil {
		logx.Errorf("万里牛客户端未初始化")
		// 查询失败时，根据本地状态判断
		return &orderModel.ShippingStatusResponse{
			IsShipped:      order.Status >= orderModel.OrderStatusShipped,
			ShipTime:       "",
			ExpressCompany: order.ExpressCompany,
			ExpressNo:      order.ExpressNo,
			CanRefund:      order.Status == orderModel.OrderStatusPaid,
		}, nil
	}
	tradeIDs := []string{orderNo}
	response, err := wanliniuService.QueryTradeStatus(ctx, "叶开泰商城", tradeIDs)
	if err != nil {
		logx.Errorf("查询万里牛订单状态失败: %v", err)
		// 查询失败时，根据本地状态判断
		return &orderModel.ShippingStatusResponse{
			IsShipped:      order.Status >= orderModel.OrderStatusShipped,
			ShipTime:       "",
			ExpressCompany: order.ExpressCompany,
			ExpressNo:      order.ExpressNo,
			CanRefund:      order.Status == orderModel.OrderStatusPaid,
		}, nil
	}

	// 解析万里牛返回的状态（这里需要根据实际API返回格式调整）
	isShipped := false
	shipTime := ""
	expressCompany := order.ExpressCompany
	expressNo := order.ExpressNo

	// TODO: 根据万里牛API实际返回格式解析发货状态
	// 临时逻辑：如果万里牛返回成功，认为可能已发货
	if response != nil {
		// 这里应该解析response中的发货状态
		logx.Infof("万里牛订单状态查询成功: %+v", response)
	}

	return &orderModel.ShippingStatusResponse{
		IsShipped:      isShipped,
		ShipTime:       shipTime,
		ExpressCompany: expressCompany,
		ExpressNo:      expressNo,
		CanRefund:      !isShipped && order.Status == orderModel.OrderStatusPaid,
	}, nil
}

// GetOrderRefundButtonStatus 获取订单退款按钮状态
func (s *OrderService) GetOrderRefundButtonStatus(ctx context.Context, orderID uint) (*orderModel.RefundButtonStatus, error) {
	// 获取订单信息
	order, err := s.GetOrderByID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	// 检查发货状态
	shippingStatus, err := s.CheckOrderShippingStatus(ctx, order.OrderNo)
	if err != nil {
		logx.Errorf("检查订单发货状态失败: %v", err)
		// 检查失败时，保守处理：如果本地状态>=已发货，认为已发货
		isShipped := order.Status >= orderModel.OrderStatusShipped
		buttonStatus := order.GetRefundButtonStatus(isShipped)
		return &buttonStatus, nil
	}

	// 根据发货状态生成按钮状态
	buttonStatus := order.GetRefundButtonStatus(shippingStatus.IsShipped)
	return &buttonStatus, nil
}

// updateGoodsSalesCount 更新商品销售单数（支付成功时调用）
func (s *OrderService) updateGoodsSalesCount(ctx context.Context, tx *gorm.DB, orderNo string) error {
	// 查询订单信息
	var order orderModel.Order
	if err := tx.Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		return fmt.Errorf("查询订单失败: %w", err)
	}

	// 查询订单商品列表
	var orderItems []orderModel.OrderItem
	if err := tx.Where("order_id = ?", order.ID).Find(&orderItems).Error; err != nil {
		return fmt.Errorf("查询订单商品失败: %w", err)
	}

	// 获取所有商品ID
	goodsIDs := make([]uint, 0, len(orderItems))
	for _, item := range orderItems {
		goodsIDs = append(goodsIDs, item.GoodsID)
	}

	if len(goodsIDs) == 0 {
		logx.Infof("订单 %s 没有商品，跳过销售数量更新", orderNo)
		return nil
	}

	// 批量更新商品销售单数（每个商品+1，不是按数量累加）
	result := tx.Table("goods").Where("id IN ?", goodsIDs).
		UpdateColumn("sales_count", gorm.Expr("sales_count + 1"))

	if result.Error != nil {
		return fmt.Errorf("更新商品销售单数失败: %w", result.Error)
	}

	logx.Infof("订单 %s 支付成功，已更新 %d 个商品的销售单数", orderNo, len(goodsIDs))
	return nil
}

// validateERPInventory 校验万里牛ERP库存
func (s *OrderService) validateERPInventory(ctx context.Context, cartItem *cartModel.CartResponse) error {
	// 如果万里牛服务未初始化，跳过ERP库存校验
	if s.wanliniuService == nil {
		logx.Infof("万里牛服务未初始化，跳过ERP库存校验")
		return nil
	}

	// 构建查询参数，按优先级：规格编码>条码>货号
	var skuCode string
	var articleNumber string

	if cartItem.SpecID > 0 {
		// 有规格时，使用规格编码查询（优先级最高）
		skuCode = fmt.Sprintf("SPEC_%d", cartItem.SpecID)
	} else {
		// 无规格时，使用货号查询（货号可以从商品ID生成）
		articleNumber = fmt.Sprintf("GOODS_%d", cartItem.GoodsID)
	}

	var response *wanliniu.InventoryQueryV2Response
	var err error

	// 根据优先级查询库存：规格编码>条码>货号
	if skuCode != "" {
		// 使用规格编码查询（优先级最高）
		response, err = s.wanliniuService.QueryInventoryV2BySkuCode(ctx, skuCode, "")
	} else if articleNumber != "" {
		// 使用货号查询
		response, err = s.wanliniuService.QueryInventoryV2ByArticleNumber(ctx, articleNumber, "")
	}

	if err != nil {
		logx.Errorf("查询万里牛ERP库存V2失败: 商品ID=%d, 规格ID=%d, 错误=%v", cartItem.GoodsID, cartItem.SpecID, err)
		// ERP库存查询失败时，不阻断下单流程，只记录日志
		return nil
	}

	// 检查是否有库存数据
	if len(response.Data) == 0 {
		logx.Infof("万里牛ERP中未找到库存数据: 商品ID=%d, 规格ID=%d", cartItem.GoodsID, cartItem.SpecID)
		// 没有库存数据时，不阻断下单流程
		return nil
	}

	// 统计总可用库存
	totalAvailableStock := 0.0
	for _, item := range response.Data {
		// 可用库存 = 实际库存 - 锁定库存
		availableStock := item.Quantity - item.LockSize
		if availableStock > 0 {
			totalAvailableStock += availableStock
		}
		logx.Infof("万里牛ERP库存详情: 商品编码=%s, 规格编码=%s, 仓库=%s, 实际库存=%.2f, 锁定库存=%.2f, 可用库存=%.2f",
			item.GoodsCode, item.SkuCode, item.StorageCode, item.Quantity, item.LockSize, availableStock)
	}

	// 检查库存是否充足
	requiredQuantity := float64(cartItem.Quantity)
	if totalAvailableStock < requiredQuantity {
		logx.Errorf("万里牛ERP库存不足: 商品ID=%d, 规格ID=%d, 需要数量=%.2f, 可用库存=%.2f",
			cartItem.GoodsID, cartItem.SpecID, requiredQuantity, totalAvailableStock)
		return fmt.Errorf("库存不足，当前可用库存: %.0f，需要数量: %.0f", totalAvailableStock, requiredQuantity)
	}

	logx.Infof("万里牛ERP库存校验通过: 商品ID=%d, 规格ID=%d, 需要数量=%.2f, 可用库存=%.2f",
		cartItem.GoodsID, cartItem.SpecID, requiredQuantity, totalAvailableStock)

	return nil
}

// restoreGoodsStockForOrder 恢复订单商品库存
func (s *OrderService) restoreGoodsStockForOrder(ctx context.Context, tx *gorm.DB, order *orderModel.Order) error {
	// 查询订单商品
	var orderItems []orderModel.OrderItem
	err := tx.Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return err
	}

	// 恢复每个商品的库存
	for _, item := range orderItems {
		// 如果有规格，恢复规格库存
		if item.SpecID > 0 {
			err = tx.Table("goods_spec").
				Where("id = ?", item.SpecID).
				UpdateColumn("stock", gorm.Expr("stock + ?", item.Quantity)).Error
			if err != nil {
				logx.Errorf("恢复规格库存失败: 规格ID=%d, 数量=%d, 错误=%v", item.SpecID, item.Quantity, err)
				return err
			}
			logx.Infof("恢复规格库存成功: 规格ID=%d, 数量=%d", item.SpecID, item.Quantity)
		} else {
			// 恢复商品库存
			err = tx.Table("goods").
				Where("id = ?", item.GoodsID).
				UpdateColumn("stock", gorm.Expr("stock + ?", item.Quantity)).Error
			if err != nil {
				logx.Errorf("恢复商品库存失败: 商品ID=%d, 数量=%d, 错误=%v", item.GoodsID, item.Quantity, err)
				return err
			}
			logx.Infof("恢复商品库存成功: 商品ID=%d, 数量=%d", item.GoodsID, item.Quantity)
		}
	}

	return nil
}

// GetOrderRefundStatus 查询订单退款状态
func (s *OrderService) GetOrderRefundStatus(ctx context.Context, userID uint, orderID string) (*orderModel.RefundStatusResponse, error) {
	var order orderModel.Order
	err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", orderID, userID).
		First(&order).Error
	if err != nil {
		return nil, fmt.Errorf("订单不存在")
	}

	return &orderModel.RefundStatusResponse{
		OrderID:          order.ID,
		OrderNo:          order.OrderNo,
		RefundStatus:     order.RefundStatus,
		RefundStatusText: orderModel.GetRefundStatusText(order.RefundStatus),
		RefundAmount:     order.RefundAmount,
		RefundReason:     order.RefundReason,
		RefundNo:         order.RefundNo,
		RefundTime:       order.RefundTime,
	}, nil
}

// validateCreateOrderRequest 严格验证创建订单请求
func (s *OrderService) validateCreateOrderRequest(ctx context.Context, userID uint, req *orderModel.CreateOrderRequest) error {
	// 1. 验证购物车ID列表
	if len(req.CartIDs) == 0 {
		return fmt.Errorf("购物车商品不能为空")
	}
	if len(req.CartIDs) > 20 {
		return fmt.Errorf("单次下单商品数量不能超过20个")
	}

	// 2. 验证地址ID
	if req.AddressID == 0 {
		return fmt.Errorf("收货地址不能为空")
	}

	// 3. 验证用户状态
	var userStatus int
	err := s.db.WithContext(ctx).Table("wx_user").
		Select("status").Where("user_id = ?", userID).Scan(&userStatus).Error
	if err != nil {
		return fmt.Errorf("用户不存在")
	}
	if userStatus != 1 {
		return fmt.Errorf("用户状态异常，无法下单")
	}

	// 4. 验证优惠券ID（如果提供）
	for _, couponID := range req.CouponIDs {
		if couponID != "" {
			// 解析优惠券ID
			if len(couponID) < 2 || couponID[0] != 'c' {
				return fmt.Errorf("无效的优惠券ID格式: %s", couponID)
			}
			userCouponID, err := strconv.ParseUint(couponID[1:], 10, 32)
			if err != nil {
				return fmt.Errorf("无效的优惠券ID: %s", couponID)
			}

			var couponCount int64
			err = s.db.WithContext(ctx).Table("user_coupons").
				Where("id = ? AND user_id = ? AND is_used = 0 AND expire_time > ?", userCouponID, userID, time.Now()).
				Count(&couponCount).Error
			if err != nil || couponCount == 0 {
				return fmt.Errorf("优惠券 %s 无效或已过期", couponID)
			}
		}
	}

	// 5. 验证积分使用（布尔值，无需验证具体数量）
	if req.UsePoints {
		// 检查积分功能是否启用
		var globalConfig struct {
			Enabled bool `json:"enabled"`
		}
		err := s.db.WithContext(ctx).Table("coin_global_config").
			Select("enabled").Where("enabled = 1").First(&globalConfig).Error
		if err != nil {
			return fmt.Errorf("叶小币功能未启用")
		}
	}

	// 6. 验证备注长度
	if len(req.Remark) > 200 {
		return fmt.Errorf("订单备注不能超过200个字符")
	}

	return nil
}

// validateAndGetAddressInfo 验证并获取地址信息
func (s *OrderService) validateAndGetAddressInfo(ctx context.Context, addressID, userID uint) (string, error) {
	var address userModel.Address
	err := s.db.WithContext(ctx).Table("addresses").
		Where("id = ? AND user_id = ?", addressID, userID).
		First(&address).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", fmt.Errorf("收货地址不存在")
		}
		return "", fmt.Errorf("查询收货地址失败: %w", err)
	}

	// 验证地址信息完整性
	if address.Name == "" || address.Phone == "" || address.Address == "" {
		return "", fmt.Errorf("收货地址信息不完整")
	}

	// 验证手机号格式
	if len(address.Phone) != 11 {
		return "", fmt.Errorf("收货地址手机号格式不正确")
	}

	addressInfo, _ := json.Marshal(address)
	return string(addressInfo), nil
}

// validateAndCalculateCoupon 验证并计算优惠券抵扣
func (s *OrderService) validateAndCalculateCoupon(ctx context.Context, userID, couponID uint, goodsAmount float64) (float64, error) {
	if couponID == 0 {
		return 0, nil
	}

	// 查询用户优惠券信息（使用实际的表结构）
	var userCoupon struct {
		ID         uint       `json:"id"`
		CouponID   uint       `json:"coupon_id"`
		Status     int        `json:"status"` // 0-未使用,1-已使用,2-已过期
		ValidFrom  time.Time  `json:"valid_from"`
		ValidUntil time.Time  `json:"valid_until"`
		UsedAt     *time.Time `json:"used_at"`

		// 优惠券详情
		Name      string  `json:"name"`
		Type      int     `json:"type"`       // 1=满减券 2=折扣券 3=立减券
		Amount    float64 `json:"amount"`     // 优惠金额
		Discount  float64 `json:"discount"`   // 折扣比例
		MinAmount float64 `json:"min_amount"` // 最低使用金额
	}

	err := s.db.WithContext(ctx).Table("user_coupons uc").
		Select("uc.id, uc.coupon_id, uc.status, uc.valid_from, uc.valid_until, uc.used_at, c.name, c.type, c.amount, c.discount, c.min_amount").
		Joins("LEFT JOIN coupons c ON uc.coupon_id = c.id").
		Where("uc.id = ? AND uc.user_id = ? AND uc.deleted_at IS NULL AND c.deleted_at IS NULL", couponID, userID).
		First(&userCoupon).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, fmt.Errorf("优惠券不存在或已删除")
		}
		return 0, fmt.Errorf("查询优惠券失败: %w", err)
	}

	// 验证优惠券状态
	if userCoupon.Status == 1 {
		return 0, fmt.Errorf("优惠券已使用")
	}
	if userCoupon.Status == 2 {
		return 0, fmt.Errorf("优惠券已过期")
	}

	// 验证有效期
	now := time.Now()
	if now.Before(userCoupon.ValidFrom) {
		return 0, fmt.Errorf("优惠券尚未生效")
	}
	if now.After(userCoupon.ValidUntil) {
		return 0, fmt.Errorf("优惠券已过期")
	}

	// 验证使用条件
	if goodsAmount < userCoupon.MinAmount {
		return 0, fmt.Errorf("未满足优惠券使用条件，需满%.2f元", userCoupon.MinAmount)
	}

	// 计算抵扣金额
	var deduct float64
	switch userCoupon.Type {
	case 1: // 满减券
		deduct = userCoupon.Amount
	case 2: // 折扣券
		deduct = goodsAmount * (1 - userCoupon.Discount/10) // 8.5表示8.5折
	case 3: // 立减券
		deduct = userCoupon.Amount
	default:
		return 0, fmt.Errorf("不支持的优惠券类型")
	}

	// 确保抵扣金额不超过商品金额
	if deduct > goodsAmount {
		deduct = goodsAmount
	}

	return deduct, nil
}

// validateAndCalculatePoints 验证并计算积分抵扣
func (s *OrderService) validateAndCalculatePoints(ctx context.Context, userID uint, usePoints int, goodsAmount float64) (float64, error) {
	if usePoints <= 0 {
		return 0, nil
	}

	// 查询用户叶小币余额（从user_coins表）
	var userCoins struct {
		AvailableCoins int `json:"available_coins"`
	}
	err := s.db.WithContext(ctx).Table("user_coins").
		Select("available_coins").Where("user_id = ?", userID).First(&userCoins).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, fmt.Errorf("用户叶小币账户不存在")
		}
		return 0, fmt.Errorf("查询用户叶小币失败: %w", err)
	}

	// 验证叶小币余额
	if usePoints > userCoins.AvailableCoins {
		return 0, fmt.Errorf("叶小币余额不足，当前可用余额：%d", userCoins.AvailableCoins)
	}

	// 查询全局配置获取兑换比例
	var globalConfig struct {
		ExchangeRate int `json:"exchange_rate"`
	}
	err = s.db.WithContext(ctx).Table("coin_global_config").
		Select("exchange_rate").Where("enabled = 1").First(&globalConfig).Error
	if err != nil {
		// 如果查询失败或未启用，不允许使用积分
		return 0, fmt.Errorf("叶小币功能未启用，无法使用积分抵扣")
	}

	// 查询用户等级权益
	var userLevel struct {
		ExtraCoins      int     `json:"extra_coins"`
		ProductDiscount float64 `json:"product_discount"`
	}
	err = s.db.WithContext(ctx).Table("wx_user u").
		Select("ulr.extra_coins, ulr.product_discount").
		Joins("LEFT JOIN user_level_rules ulr ON u.user_level_id = ulr.id").
		Where("u.id = ?", userID).First(&userLevel).Error
	if err != nil {
		// 如果查询失败，使用默认值
		userLevel.ExtraCoins = 0
		userLevel.ProductDiscount = 0
	}

	// 计算抵扣金额（1叶小币=1元）
	pointsDeduct := float64(usePoints) / float64(globalConfig.ExchangeRate)

	// 获取最大抵扣比例限制（默认50%）
	maxDeductRate := 0.5
	maxAllowedDeduct := goodsAmount * maxDeductRate

	// 验证是否超过最大抵扣比例
	if pointsDeduct > maxAllowedDeduct {
		return 0, fmt.Errorf("叶小币抵扣超过最大限制，最多可抵扣%.2f元", maxAllowedDeduct)
	}

	return pointsDeduct, nil
}

// getMemberDiscount 获取用户会员等级折扣
func (s *OrderService) getMemberDiscount(ctx context.Context, userID uint) (float64, error) {
	// 查询用户当前等级
	var userLevel struct {
		Level int `json:"level"`
	}
	err := s.db.WithContext(ctx).Table("wx_user").
		Select("user_level").Where("id = ?", userID).First(&userLevel).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil // 用户不存在，无折扣
		}
		return 0, fmt.Errorf("查询用户等级失败: %w", err)
	}

	// 如果用户等级为0或未设置，无折扣
	if userLevel.Level <= 0 {
		return 0, nil
	}

	// 查询等级规则获取商品折扣
	var levelRule struct {
		ProductDiscount float64 `json:"product_discount"`
	}
	err = s.db.WithContext(ctx).Table("user_level_rules").
		Select("product_discount").
		Where("level_order = ? AND deleted_at IS NULL", userLevel.Level).
		First(&levelRule).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil // 等级规则不存在，无折扣
		}
		return 0, fmt.Errorf("查询等级规则失败: %w", err)
	}

	// 返回折扣比例（如9.5表示9.5折，即5%的折扣）
	if levelRule.ProductDiscount > 0 && levelRule.ProductDiscount < 10 {
		return (10 - levelRule.ProductDiscount) / 10, nil // 转换为折扣率
	}

	return 0, nil // 无有效折扣
}

// validateFinalAmount 验证最终支付金额
func (s *OrderService) validateFinalAmount(totalAmount, payAmount float64) error {
	// 验证金额合理性
	if totalAmount < 0 {
		return fmt.Errorf("订单总金额不能为负数")
	}
	if payAmount < 0 {
		return fmt.Errorf("支付金额不能为负数")
	}
	if payAmount > totalAmount {
		return fmt.Errorf("支付金额不能超过订单总金额")
	}

	// 验证最小支付金额（微信支付最小1分钱）
	if payAmount > 0 && payAmount < 0.01 {
		return fmt.Errorf("支付金额不能少于0.01元")
	}

	// 验证最大支付金额（防止异常大额订单）
	if payAmount > 50000 {
		return fmt.Errorf("单笔订单支付金额不能超过50000元")
	}

	return nil
}

// deductCouponAndPoints 扣除优惠券和叶小币（支付成功时调用）
func (s *OrderService) deductCouponAndPoints(ctx context.Context, tx *gorm.DB, order *orderModel.Order) error {
	// 1. 扣除优惠券
	if order.CouponID > 0 {
		err := tx.Table("user_coupons").
			Where("id = ? AND user_id = ? AND status = 0", order.CouponID, order.UserID).
			Updates(map[string]interface{}{
				"status":     1,          // 设置为已使用
				"used_at":    time.Now(), // 使用时间
				"order_id":   order.ID,   // 关联订单ID
				"updated_at": time.Now(),
			}).Error
		if err != nil {
			logx.Errorf("扣除优惠券失败: 订单号=%s, 优惠券ID=%d, 错误=%v", order.OrderNo, order.CouponID, err)
			return fmt.Errorf("扣除优惠券失败: %w", err)
		}
		logx.Infof("扣除优惠券成功: 订单号=%s, 优惠券ID=%d", order.OrderNo, order.CouponID)
	}

	// 2. 扣除叶小币
	if order.UsePoints > 0 {
		err := tx.Table("user_coins").
			Where("user_id = ? AND available_coins >= ?", order.UserID, order.UsePoints).
			Updates(map[string]interface{}{
				"available_coins": gorm.Expr("available_coins - ?", order.UsePoints),
				"used_coins":      gorm.Expr("used_coins + ?", order.UsePoints),
				"updated_at":      time.Now(),
			}).Error
		if err != nil {
			logx.Errorf("扣除叶小币失败: 订单号=%s, 用户ID=%d, 使用积分=%d, 错误=%v", order.OrderNo, order.UserID, order.UsePoints, err)
			return fmt.Errorf("扣除叶小币失败: %w", err)
		}
		logx.Infof("扣除叶小币成功: 订单号=%s, 用户ID=%d, 使用积分=%d", order.OrderNo, order.UserID, order.UsePoints)
	}

	return nil
}

// restoreCouponAndPoints 恢复优惠券和叶小币（取消/退款时调用）
func (s *OrderService) restoreCouponAndPoints(ctx context.Context, tx *gorm.DB, order *orderModel.Order) error {
	// 1. 恢复优惠券
	if order.CouponID > 0 {
		err := tx.Table("user_coupons").
			Where("id = ? AND user_id = ? AND status = 1 AND order_id = ?", order.CouponID, order.UserID, order.ID).
			Updates(map[string]interface{}{
				"status":     0,   // 恢复为未使用
				"used_at":    nil, // 清空使用时间
				"order_id":   nil, // 清空订单关联
				"updated_at": time.Now(),
			}).Error
		if err != nil {
			logx.Errorf("恢复优惠券失败: 订单号=%s, 优惠券ID=%d, 错误=%v", order.OrderNo, order.CouponID, err)
			return fmt.Errorf("恢复优惠券失败: %w", err)
		}
		logx.Infof("恢复优惠券成功: 订单号=%s, 优惠券ID=%d", order.OrderNo, order.CouponID)
	}

	// 2. 恢复叶小币（用户使用的积分）
	if order.UsePoints > 0 {
		err := tx.Table("user_coins").
			Where("user_id = ?", order.UserID).
			Updates(map[string]interface{}{
				"available_coins": gorm.Expr("available_coins + ?", order.UsePoints),
				"used_coins":      gorm.Expr("used_coins - ?", order.UsePoints),
				"updated_at":      time.Now(),
			}).Error
		if err != nil {
			logx.Errorf("恢复叶小币失败: 订单号=%s, 用户ID=%d, 使用积分=%d, 错误=%v", order.OrderNo, order.UserID, order.UsePoints, err)
			return fmt.Errorf("恢复叶小币失败: %w", err)
		}
		logx.Infof("恢复叶小币成功: 订单号=%s, 用户ID=%d, 使用积分=%d", order.OrderNo, order.UserID, order.UsePoints)
	}

	// 3. 扣回消费奖励积分（使用新的叶小币奖励服务）
	coinRewardService := service.NewCoinRewardService()
	if err := coinRewardService.RefundCoinsForOrder(ctx, order.UserID, order.OrderNo); err != nil {
		logx.Errorf("扣回消费奖励积分失败: 订单号=%s, 用户ID=%d, 错误=%v", order.OrderNo, order.UserID, err)
		// 不阻断退款流程，只记录错误
	} else {
		logx.Infof("扣回消费奖励积分成功: 订单号=%s, 用户ID=%d", order.OrderNo, order.UserID)
	}

	return nil
}

// handleConsumptionPointsReward 处理消费积分奖励
func (s *OrderService) handleConsumptionPointsReward(ctx context.Context, userID uint, amount float64, orderNo string) error {
	// 查询用户等级规则中的消费积分奖励配置
	var levelRule struct {
		ExtraCoins int `json:"extra_coins"`
	}

	err := s.db.WithContext(ctx).Table("wx_user u").
		Select("COALESCE(ulr.extra_coins, 0) as extra_coins").
		Joins("LEFT JOIN user_level_rules ulr ON u.user_level = ulr.level_order").
		Where("u.user_id = ?", userID).
		First(&levelRule).Error

	if err != nil {
		logx.Errorf("查询用户等级规则失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("查询用户等级规则失败: %w", err)
	}

	// 计算消费积分奖励（按消费金额的一定比例，这里假设1元=1积分）
	consumptionPoints := int(amount)

	// 如果用户等级有额外积分奖励，则加上额外积分
	totalPoints := consumptionPoints + levelRule.ExtraCoins

	// 如果没有积分奖励，直接返回
	if totalPoints <= 0 {
		logx.Infof("无消费积分奖励: userID=%d, amount=%.2f", userID, amount)
		return nil
	}

	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 创建或更新用户积分账户
	err = tx.Exec(`
		INSERT INTO user_coins (user_id, total_coins, available_coins, frozen_coins, used_coins, created_at, updated_at)
		VALUES (?, ?, ?, 0, 0, NOW(), NOW())
		ON DUPLICATE KEY UPDATE
		total_coins = total_coins + VALUES(total_coins),
		available_coins = available_coins + VALUES(available_coins),
		updated_at = NOW()
	`, userID, totalPoints, totalPoints).Error

	if err != nil {
		tx.Rollback()
		logx.Errorf("更新用户积分账户失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("更新用户积分账户失败: %w", err)
	}

	// 2. 记录积分交易流水
	err = tx.Exec(`
		INSERT INTO coin_transactions (user_id, transaction_type, amount, balance, order_id, description, created_at, updated_at)
		VALUES (?, 'EARN', ?, (SELECT available_coins FROM user_coins WHERE user_id = ?), ?, ?, NOW(), NOW())
	`, userID, totalPoints, userID, orderNo, fmt.Sprintf("消费奖励(订单:%s,消费:%.2f元)", orderNo, amount)).Error

	if err != nil {
		tx.Rollback()
		logx.Errorf("记录积分交易流水失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("记录积分交易流水失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logx.Errorf("提交消费积分奖励事务失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("提交消费积分奖励事务失败: %w", err)
	}

	logx.Infof("消费积分奖励成功: userID=%d, 消费金额=%.2f, 奖励积分=%d(消费积分:%d+等级奖励:%d)",
		userID, amount, totalPoints, consumptionPoints, levelRule.ExtraCoins)
	return nil
}

// validateAndCalculateMultipleCoupons 验证并计算多张优惠券抵扣
func (s *OrderService) validateAndCalculateMultipleCoupons(ctx context.Context, userID uint, couponIDs []string, goodsAmount float64) (float64, error) {
	if len(couponIDs) == 0 {
		return 0, nil
	}

	totalDeduct := 0.0
	remainingAmount := goodsAmount

	// 逐张计算优惠券抵扣
	for _, couponID := range couponIDs {
		if couponID == "" {
			continue
		}

		// 解析优惠券ID
		if len(couponID) < 2 || couponID[0] != 'c' {
			return 0, fmt.Errorf("无效的优惠券ID格式: %s", couponID)
		}

		userCouponID, err := strconv.ParseUint(couponID[1:], 10, 32)
		if err != nil {
			return 0, fmt.Errorf("无效的优惠券ID: %s", couponID)
		}

		deduct, err := s.validateAndCalculateCoupon(ctx, userID, uint(userCouponID), remainingAmount)
		if err != nil {
			return 0, fmt.Errorf("优惠券 %s 验证失败: %w", couponID, err)
		}

		totalDeduct += deduct
		remainingAmount -= deduct

		// 如果剩余金额为0，停止计算
		if remainingAmount <= 0 {
			break
		}
	}

	return totalDeduct, nil
}

// validateAndCalculatePointsByFlag 根据布尔值验证并计算积分抵扣
func (s *OrderService) validateAndCalculatePointsByFlag(ctx context.Context, userID uint, usePoints bool, goodsAmount float64) (float64, error) {
	if !usePoints || goodsAmount <= 0 {
		return 0, nil
	}

	// 查询全局配置获取兑换比例
	var globalConfig struct {
		ExchangeRate int `json:"exchange_rate"`
	}
	err := s.db.WithContext(ctx).Table("coin_global_config").
		Select("exchange_rate").Where("enabled = 1").First(&globalConfig).Error
	if err != nil {
		// 如果查询失败或未启用，不允许使用积分
		return 0, fmt.Errorf("叶小币功能未启用，无法使用积分抵扣")
	}

	// 查询用户积分余额
	var userCoins struct {
		AvailableCoins int `json:"available_coins"`
	}
	err = s.db.WithContext(ctx).Table("user_coins").
		Select("available_coins").Where("user_id = ?", userID).First(&userCoins).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户没有积分账户，返回0抵扣
			return 0, nil
		}
		return 0, fmt.Errorf("查询用户积分失败: %w", err)
	}

	// 计算最大可用积分数
	maxDeductRate := 0.5 // 最多抵扣50%
	maxAllowedDeduct := goodsAmount * maxDeductRate

	// 根据积分余额计算最大抵扣金额
	pointsMaxDeduct := float64(userCoins.AvailableCoins) / float64(globalConfig.ExchangeRate)

	// 取较小值作为实际抵扣金额
	actualDeduct := maxAllowedDeduct
	if pointsMaxDeduct < actualDeduct {
		actualDeduct = pointsMaxDeduct
	}

	// 确保不超过商品金额
	if actualDeduct > goodsAmount {
		actualDeduct = goodsAmount
	}

	return actualDeduct, nil
}
