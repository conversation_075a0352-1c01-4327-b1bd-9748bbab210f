package bootstrap

import (
	"fmt"
	"log"

	adminModel "yekaitai/internal/modules/admin/model"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"
)

// MigrateRBACTables 执行RBAC表结构迁移
func MigrateRBACTables() error {
	log.Println("开始执行RBAC表结构迁移...")

	// 执行管理员RBAC表结构迁移
	if err := migrateAdminRBACTables(); err != nil {
		return fmt.Errorf("管理员RBAC表结构迁移失败: %w", err)
	}

	log.Println("RBAC表结构迁移完成")
	return nil
}

// 执行管理员RBAC表结构迁移
func migrateAdminRBACTables() error {
	db := mysql.Master()

	// 为每个表单独设置表名注释
	db.Set("gorm:table_options", "COMMENT='管理员用户表'").AutoMigrate(&adminModel.AdminUser{})
	db.Set("gorm:table_options", "COMMENT='管理员角色表'").AutoMigrate(&adminModel.AdminRole{})
	db.Set("gorm:table_options", "COMMENT='管理员权限表'").AutoMigrate(&adminModel.AdminPermission{})
	db.Set("gorm:table_options", "COMMENT='角色权限关联表'").AutoMigrate(&adminModel.AdminRolePermission{})
	db.Set("gorm:table_options", "COMMENT='用户角色关联表'").AutoMigrate(&adminModel.AdminUserRole{})
	db.Set("gorm:table_options", "COMMENT='管理员操作日志表'").AutoMigrate(&adminModel.AdminOperationLog{})
	db.Set("gorm:table_options", "COMMENT='管理菜单表'").AutoMigrate(&adminModel.AdminMenu{})

	return nil
}

// 加载RBAC初始化SQL文件
func loadRBACInitData() error {
	db := mysql.Master()
	// 加载RBAC完整初始化数据
	if err := utils.ExecuteSQL(db, "scripts/sql/admin_rbac_init.sql"); err != nil {
		return err
	}
	return nil
}

// InitRBACData 初始化RBAC基础数据
func InitRBACData() error {
	log.Println("开始初始化RBAC数据...")

	// 执行SQL初始化数据文件
	if err := loadRBACInitData(); err != nil {
		return fmt.Errorf("RBAC数据初始化失败: %w", err)
	}

	log.Println("RBAC数据初始化完成")
	return nil
}

// MigrateRegionTables 迁移地区表结构
func MigrateRegionTables() error {
	log.Println("开始执行地区表结构迁移...")

	db := mysql.Master()
	db.Set("gorm:table_options", "COMMENT='地区表'").AutoMigrate(&adminModel.Region{})

	log.Println("地区表结构迁移完成")
	return nil
}
