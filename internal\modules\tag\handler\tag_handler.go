package handler

import (
	"net/http"
	"strconv"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/common/model/user"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 标签列表请求
type TagListRequest struct {
	types.PageRequest
	IDs []uint `form:"ids,optional"` // 标签ID列表，用于批量查询，可选
}

// 标签详情请求
type TagDetailRequest struct {
	ID uint `path:"id"` // 标签ID
}

// 创建标签请求
type CreateTagRequest struct {
	Name string `json:"name"` // 标签名称
}

// 更新标签请求
type UpdateTagRequest struct {
	ID   uint   `path:"id"`   // 标签ID
	Name string `json:"name"` // 标签名称
}

// TagHandler 标签管理处理器
type TagHandler struct {
	svcCtx *svc.ServiceContext
}

// NewTagHandler 创建标签管理处理器
func NewTagHandler(svcCtx *svc.ServiceContext) *TagHandler {
	return &TagHandler{
		svcCtx: svcCtx,
	}
}

// ListTags 获取标签列表
func (h *TagHandler) ListTags(w http.ResponseWriter, r *http.Request) {
	var req TagListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	var tags []*user.Tag
	var total int64
	var err error

	// 根据是否提供IDs参数决定查询方式
	if len(req.IDs) > 0 {
		// 如果提供了IDs，则按ID批量查询
		tags, err = h.svcCtx.TagService.FindByIDs(req.IDs)
		if err != nil {
			logx.Errorf("按ID批量查询标签失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取标签列表失败"))
			return
		}
		total = int64(len(tags))
	} else {
		// 否则使用常规分页查询
		tags, total, err = h.svcCtx.TagService.ListTags(req.Page, req.Size, req.Query)
		if err != nil {
			logx.Errorf("获取标签列表失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取标签列表失败"))
			return
		}
	}

	// 使用统一的分页响应
	pageResponse := types.NewPageResponse(tags, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取标签列表成功"))
}

// GetTag 获取标签详情
func (h *TagHandler) GetTag(w http.ResponseWriter, r *http.Request) {
	var req TagDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 调用服务层获取标签详情
	tag, err := h.svcCtx.TagService.GetTag(req.ID)
	if err != nil {
		logx.Errorf("获取标签详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取标签详情失败"))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(tag, "获取标签详情成功"))
}

// CreateTag 创建标签
func (h *TagHandler) CreateTag(w http.ResponseWriter, r *http.Request) {
	var req CreateTagRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 校验标签名称
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签名称不能为空"))
		return
	}

	// 调用服务层创建标签
	tag, err := h.svcCtx.TagService.CreateTag(req.Name)
	if err != nil {
		logx.Errorf("创建标签失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建标签失败"))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(tag, "创建标签成功"))
}

// UpdateTag 更新标签
func (h *TagHandler) UpdateTag(w http.ResponseWriter, r *http.Request) {
	var req UpdateTagRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 校验标签名称
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签名称不能为空"))
		return
	}

	// 调用服务层更新标签
	err := h.svcCtx.TagService.UpdateTag(req.ID, req.Name)
	if err != nil {
		logx.Errorf("更新标签失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新标签失败"))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(nil, "更新标签成功"))
}

// DeleteTag 删除标签
func (h *TagHandler) DeleteTag(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Path[len("/api/admin/tags/"):]
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的标签ID"))
		return
	}

	// 调用服务层删除标签
	err = h.svcCtx.TagService.DeleteTag(uint(id))
	if err != nil {
		logx.Errorf("删除标签失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除标签失败"))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除标签成功"))
}
