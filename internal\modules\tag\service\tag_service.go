package service

import (
	"time"
	"yekaitai/internal/modules/tag/repository"
	"yekaitai/pkg/common/model/user"
)

// TagService 标签服务接口
type TagService interface {
	// CreateTag 创建标签
	CreateTag(name string) (*user.Tag, error)
	// UpdateTag 更新标签
	UpdateTag(id uint, name string) error
	// DeleteTag 删除标签
	DeleteTag(id uint) error
	// GetTag 获取标签详情
	GetTag(id uint) (*user.Tag, error)
	// ListTags 获取标签列表
	ListTags(page, size int, query string) ([]*user.Tag, int64, error)
	// FindByIDs 根据ID列表查找标签
	FindByIDs(ids []uint) ([]*user.Tag, error)
}

// tagService 标签服务实现
type tagService struct {
	repo repository.TagRepository
}

// NewTagService 创建标签服务
func NewTagService(repo repository.TagRepository) TagService {
	return &tagService{repo: repo}
}

// CreateTag 创建标签
func (s *tagService) CreateTag(name string) (*user.Tag, error) {
	// 检查标签名称是否已存在
	existingTag, err := s.repo.FindByName(name)
	if err == nil && existingTag != nil {
		return existingTag, nil
	}

	// 创建新标签
	tag := &user.Tag{
		Name:      name,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = s.repo.Create(tag)
	if err != nil {
		return nil, err
	}

	return tag, nil
}

// UpdateTag 更新标签
func (s *tagService) UpdateTag(id uint, name string) error {
	// 检查标签是否存在
	tag, err := s.repo.FindByID(id)
	if err != nil {
		return err
	}

	// 检查新名称是否与其他标签重复
	if name != tag.Name {
		existingTag, err := s.repo.FindByName(name)
		if err == nil && existingTag != nil && existingTag.ID != id {
			return err
		}
	}

	// 更新标签
	tag.Name = name
	tag.UpdatedAt = time.Now()

	return s.repo.Update(tag)
}

// DeleteTag 删除标签
func (s *tagService) DeleteTag(id uint) error {
	return s.repo.Delete(id)
}

// GetTag 获取标签详情
func (s *tagService) GetTag(id uint) (*user.Tag, error) {
	return s.repo.FindByID(id)
}

// ListTags 获取标签列表
func (s *tagService) ListTags(page, size int, query string) ([]*user.Tag, int64, error) {
	return s.repo.List(page, size, query)
}

// FindByIDs 根据ID列表查找标签
func (s *tagService) FindByIDs(ids []uint) ([]*user.Tag, error) {
	return s.repo.FindByIDs(ids)
}
