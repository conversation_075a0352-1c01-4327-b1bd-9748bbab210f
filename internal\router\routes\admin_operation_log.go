package routes

import (
	"net/http"

	"yekaitai/internal/modules/admin/handler"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterAdminOperationLogRoutes 注册操作日志路由
func RegisterAdminOperationLogRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}) {
	handler := handler.NewAdminOperationLogHandler()

	server.AddRoutes([]rest.Route{
		{
			Method:  http.MethodGet,
			Path:    "/api/admin/operation-logs",
			Handler: handler.GetOperationLogList,
		},
	})
}
