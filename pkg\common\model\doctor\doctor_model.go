package doctor

import (
	"time"

	"gorm.io/gorm"
)

// DoctorDisplay 医生展示信息
type DoctorDisplay struct {
	ID            uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:展示ID"`
	DoctorID      uint           `json:"doctor_id" gorm:"uniqueIndex;not null;comment:医生ID"`
	DisplayOrder  int            `json:"display_order" gorm:"default:0;comment:展示顺序，数字越小越靠前"`
	IsRecommended bool           `json:"is_recommended" gorm:"default:false;comment:是否推荐"`
	CreatedAt     time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt     time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 设置DoctorDisplay表名
func (DoctorDisplay) TableName() string {
	return "doctor_display"
}

// DoctorStoreRelation 医生门店关联
type DoctorStoreRelation struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:关联ID"`
	DoctorID  uint      `json:"doctor_id" gorm:"index;not null;comment:医生ID"`
	StoreID   uint      `json:"store_id" gorm:"index;not null;comment:门店ID"`
	IsPrimary bool      `json:"is_primary" gorm:"default:false;comment:是否为主要门店"`
	CreatedAt time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time `json:"updated_at" gorm:"comment:更新时间"`
}

// TableName 设置DoctorStoreRelation表名
func (DoctorStoreRelation) TableName() string {
	return "doctor_store_relation"
}
