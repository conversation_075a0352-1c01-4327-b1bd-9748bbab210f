# 叶开泰医疗预约系统开发指南

本文档提供了项目开发的关键信息，包括项目结构、组件使用方法、代码规范等。在开发新功能时，请先阅读本文档，并参考已有功能的实现方式。

## 项目结构

```
├── cmd/                  # 命令行工具和启动入口
│   ├── bootstrap/        # 系统初始化和引导代码
│   │   ├── bootstrap.go  # 主引导程序
│   │   ├── migration.go  # 数据库迁移
│   │   └── *_migration.go # 各模块数据库迁移
│   └── tools/            # 工具类命令
├── docs/                 # 文档目录
├── internal/             # 后台管理系统内部代码，不对外暴露
│   ├── middleware/       # 中间件
│   ├── modules/          # 业务模块
│   │   ├── admin/        # 管理员模块
│   │   ├── area/         # 地区管理模块
│   │   ├── store/        # 门店管理模块
│   │   └── ...
│   ├── router/           # 路由相关
│   │   ├── routes/       # 各模块路由定义
│   │   └── router.go     # 主路由注册
│   ├── svc/              # 服务上下文
│   └── types/            # 通用类型定义
├── wx_internal/          # 小程序端内部代码，不对外暴露
│   ├── middleware/       # 小程序中间件
│   ├── modules/          # 小程序业务模块
│   │   ├── consultation/ # 咨询模块
│   │   ├── user/         # 用户模块
│   │   └── ...
│   ├── router/           # 小程序路由相关
│   ├── svc/              # 小程序服务上下文
│   └── types/            # 小程序通用类型定义
├── pkg/                  # 公共包
│   ├── adapters/         # 第三方系统适配器
│   │   ├── abcyun/       # ABC云对接
│   │   ├── medlinker/    # 医联对接
│   │   └── ...
│   ├── infra/            # 基础设施组件
│   │   ├── logger/       # 日志工具
│   │   ├── kafka/        # kafka工具
│   │   ├── mysql/        # MySQL工具
│   │   └── redis/        # Redis工具
│   └── utils/            # 通用工具函数
├── etc/                  # 配置文件
└── main.go               # 主程序入口
```

## 系统架构说明

本系统采用单体架构，但在代码组织上采用了明确的模块化设计：

1. **后台管理系统**：
   - 代码位于 `internal/` 目录
   - 使用端口 8889
   - 提供管理员登录、门店管理、会员管理等功能

2. **小程序服务**：
   - 代码位于 `wx_internal/` 目录
   - 使用端口 8888
   - 提供用户认证、咨询服务、预约服务等功能

3. **公共组件**：
   - 代码位于 `pkg/` 目录
   - 被两个子系统共同使用
   - 包括第三方系统适配器、基础设施组件等

两个子系统通过共享数据库实现数据交互。

## 模块开发规范

项目采用模块化设计，每个业务模块应包含以下组件：

### 1. 模型层 (model)

模型层定义数据结构和数据访问层接口：

```
modules/{模块名}/model/
  ├── {实体名}.go      # 实体定义、表结构、仓库接口
```

**示例：** `internal/modules/area/model/enabled_area.go`

关键点：
- 定义结构体时使用标准字段（ID、CreatedAt、UpdatedAt、DeletedAt等）
- 实现TableName方法返回表名（格式为t_xxx）
- 定义Repository接口
- 实现Repository接口的具体类型

### 2. 处理器层 (handler)

处理器负责处理HTTP请求，调用模型层接口：

```
modules/{模块名}/handler/
  ├── {实体名}_handler.go  # 处理器定义和实现
```

**示例：** `internal/modules/area/handler/area_handler.go`

关键点：
- 每个处理器都应该遵循相同的模式
- 使用http.ResponseWriter和http.Request处理请求
- 使用httpx.Error和httpx.OkJson返回响应
- 请求参数解析和验证
- 错误处理和日志记录

### 3. 数据库迁移 (migration)

每个模块需要定义自己的数据库迁移文件：

```
modules/{模块名}/migration/
  ├── {实体名}_migration.go  # 迁移定义
```

**示例：** `internal/modules/area/migration/enabled_area_migration.go`

关键点：
- 使用GORM的AutoMigrate创建表
- 在cmd/bootstrap下创建对应的迁移入口文件
- 在bootstrap.go中注册迁移函数

### 4. 路由注册

每个模块需要在router/routes目录下创建路由注册文件：

```
router/routes/
  ├── {模块名}.go  # 路由注册
```

**示例：** `internal/router/routes/area.go` 或 `wx_internal/router/routes/consultation.go`

关键点：
- 使用RegisterXxxRoutes函数注册模块路由
- 使用adminAuthWrapper包装需要权限的接口（后台管理系统）
- 使用wxAuthWrapper包装需要微信认证的接口（小程序端）
- 后台路由路径格式应为`/api/admin/{模块名}/{资源}`
- 小程序路由路径格式应为`/api/wx/{模块名}/{资源}`

## WebSocket实现指南

本项目在小程序端实现了WebSocket连接处理，主要用于预问诊功能：

### 1. WebSocket连接流程

```
前端 -> InitPreDiagnosis接口 -> 生成WebSocket URL -> 前端建立WebSocket连接 -> HandleWebSocket处理
```

1. **初始化阶段**
   - 通过HTTP接口 `/api/wx/consultation/prediagnosis` 获取WebSocket连接URL
   - 返回格式为 `ws://host:port/api/wx/consultation/ws/prediagnosis?token=xxx&sessionid=xxx`

2. **认证阶段**
   - 使用URL参数中的token进行用户认证，必须是有效的JWT令牌
   - 认证在 `wx_internal/router/router.go` 中的 `wxAuthServer` 实现
   - **重要改进**: 认证中间件对WebSocket请求特殊处理，确保返回正确的HTTP响应而非JSON

3. **连接阶段**
   - 升级HTTP连接为WebSocket连接
   - 使用 `gorilla/websocket` 包的 `Upgrader` 进行升级
   - 设置合理的超时时间和缓冲区大小

4. **消息处理阶段**
   - 启动单独的协程处理消息发送，避免并发写入问题
   - 使用心跳机制（Ping-Pong）保持连接活跃
   - 实现优雅关闭流程，确保连接正确终止

5. **关闭阶段（重要改进）**
   - 实现标准的WebSocket关闭握手（发送Close帧后等待响应）
   - 给客户端充足时间处理关闭消息（3秒超时）
   - 详细日志记录整个关闭过程
   - 防止状态码1006错误（异常关闭）

### 2. WebSocket关键代码

Upgrader配置示例：
```go
upgrader := websocket.Upgrader{
    CheckOrigin: func(r *http.Request) bool {
        return true  // 开发环境允许所有来源，生产环境应该更严格
    },
    HandshakeTimeout: 10 * time.Second,  // 握手超时10秒
    ReadBufferSize:   4096,
    WriteBufferSize:  4096,
}

conn, err := upgrader.Upgrade(w, r, nil)
if err != nil {
    logx.Errorf("升级WebSocket连接失败: %v", err)
    return
}
```

心跳机制示例：
```go
// 设置ping处理器
conn.SetPingHandler(func(data string) error {
    conn.SetWriteDeadline(time.Now().Add(5 * time.Second))
    return conn.WriteControl(websocket.PongMessage, []byte{}, time.Now().Add(5*time.Second))
})

// 定期发送ping
ticker := time.NewTicker(30 * time.Second)
defer ticker.Stop()

go func() {
    for {
        select {
        case <-ticker.C:
            conn.WriteControl(websocket.PingMessage, []byte("ping"), time.Now().Add(5*time.Second))
        case <-done:
            return
        }
    }
}()
```

单协程消息发送示例：
```go
// 创建消息通道
sendChan := make(chan Message, 10)

// 启动消息发送协程
go func() {
    for msg := range sendChan {
        conn.SetWriteDeadline(time.Now().Add(2 * time.Minute))
        if err := conn.WriteJSON(msg); err != nil {
            logx.Errorf("发送消息失败: %v", err)
            return
        }
    }
}()

// 发送消息示例
sendChan <- Message{Type: "text", Content: "Hello"}
```

优雅关闭示例（改进版）：
```go
func gracefulClose(conn *websocket.Conn, sessionID string) {
    if conn == nil {
        return
    }
    
    // 设置较短的写超时
    conn.SetWriteDeadline(time.Now().Add(3 * time.Second))

    // 发送关闭消息
    closeMsg := websocket.FormatCloseMessage(
        websocket.CloseNormalClosure,
        "会话结束")
    
    err := conn.WriteMessage(websocket.CloseMessage, closeMsg)
    if err != nil {
        logx.Errorf("发送关闭消息失败: %v", err)
    } else {
        logx.Info("成功发送WebSocket关闭消息")
        
        // 等待客户端响应的Close帧
        conn.SetReadDeadline(time.Now().Add(3 * time.Second))
        messageType, closePayload, readErr := conn.ReadMessage()
        if readErr != nil {
            logx.Infof("等待客户端关闭响应时发生错误(这通常是正常的): %v", readErr)
        } else if messageType == websocket.CloseMessage {
            logx.Infof("收到客户端关闭帧: %v", closePayload)
        }
    }
    
    // 关闭底层连接
    conn.Close()
}
```

### 3. WebSocket最佳实践

- **超时设置**：
  - 握手超时：10秒
  - 读超时：2-5分钟（可重置）
  - 写超时：30秒-2分钟（每次写前重置）
  - 服务全局超时：60秒（适用于HTTP请求，WebSocket连接应另行处理）

- **心跳机制**：
  - 每30秒发送一次Ping
  - 正确响应客户端的Ping消息
  - 收到Pong时重置读超时

- **错误处理**：
  - 捕获并记录所有错误
  - 发送错误时给客户端发送Close帧
  - 避免多协程并发写入导致的错误

- **并发控制**：
  - 单独的写协程处理所有消息发送
  - 使用通道在协程间传递消息
  - 通过done通道同步协程退出

- **认证处理（重要改进）**：
  - 为WebSocket请求特殊处理认证错误
  - 返回普通HTTP错误响应而非JSON格式
  - 在URL参数中传递token而非请求头

- **关闭处理（重要改进）**：
  - 遵循WebSocket标准关闭流程
  - 发送Close帧后等待客户端响应
  - 增加超时以确保关闭消息能被处理

## 常用组件和工具

### 1. GORM (ORM框架)

项目使用GORM作为ORM框架，连接MySQL数据库：

```go
import (
    "gorm.io/gorm"
)

// 定义模型
type YourModel struct {
    ID        uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:ID"`
    Name      string         `json:"name" gorm:"type:varchar(50);not null;comment:名称"`
    CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
    UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
    DeletedAt sql.NullTime   `json:"-" gorm:"index;comment:删除时间"`
}

// 查询示例
db.Where("name = ?", name).First(&result)
```

### 2. go-zero (Web框架)

项目使用go-zero作为Web框架：

```go
import (
    "github.com/zeromicro/go-zero/rest/httpx"
)

// 成功响应
httpx.OkJson(w, data)

// 错误响应
httpx.Error(w, err)
```

### 3. 认证中间件

项目使用不同的中间件分别处理后台管理系统和小程序的认证：

```go
// 后台管理系统认证
adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
    return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
}

// 小程序认证
wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
    return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
}

// 添加受保护的路由
server.AddRoutes(
    []rest.Route{
        {
            Method:  http.MethodGet,
            Path:    "/api/admin/your-path", // 后台路径
            Handler: adminAuthWrapper(yourHandler.YourMethod),
        },
        {
            Method:  http.MethodGet,
            Path:    "/api/wx/your-path", // 小程序路径
            Handler: wxAuthWrapper(yourHandler.YourMethod),
        },
    },
)
```

## 小程序端关键功能

### 1. 会员标签管理 (多对多关系)

会员标签使用多对多关系实现（已优化）：

- **相关模型**：
  - `WxUser`：小程序用户模型，有 `Tags []Tag` 关联
  - `Tag`：标签模型，有 `Users []WxUser` 反向关联
  - `UserTag`：中间表模型实现多对多关系

- **重要改进**：
  - 从使用逗号分隔字符串方式（例如：`1,2,3,4`）改为标准多对多关系表
  - 优势：支持更复杂查询、提高数据一致性、简化标签管理逻辑
  - 中间表（`user_tags`）存储 `user_id` 和 `tag_id` 的对应关系
  - 通过GORM关联实现高效的多对多操作

- **标签操作**：
  - 添加标签：使用 `Association("Tags").Append()` 方法
  - 删除标签：使用 `Association("Tags").Delete()` 方法
  - 替换标签：使用 `Association("Tags").Replace()` 方法

- **使用示例**：
```go
  // 更新用户标签
  db.Model(&user).Association("Tags").Replace(tags)
    
  // 查询带有特定标签的用户
  db.Joins("JOIN user_tags ON user_tags.user_id = wx_users.id").
     Where("user_tags.tag_id IN ?", tagIDs).
     Find(&users)
  ```

- **数据库结构**：
  ```sql
  -- 标签表
  CREATE TABLE `tags` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(32) NOT NULL COMMENT '标签名称',
    `sort` int(11) DEFAULT 0 COMMENT '排序',
    `created_at` datetime DEFAULT NULL,
    `updated_at` datetime DEFAULT NULL,
    `deleted_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

  -- 用户标签关联表
  CREATE TABLE `user_tags` (
    `user_id` bigint(20) unsigned NOT NULL,
    `tag_id` bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`user_id`,`tag_id`),
    KEY `idx_tag_id` (`tag_id`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
  ```

### 2. WebSocket预问诊服务

预问诊服务使用WebSocket实现实时对话：

- **流程概述**：
  1. 初始化：调用`/api/wx/consultation/prediagnosis`获取连接URL
  2. 连接：前端建立WebSocket连接
  3. 发送消息：发送问题内容
  4. 接收回复：收到AI生成的回答块
  5. 结束：收到最终消息

- **消息格式**：
  ```json
  // 发送到服务端的消息
  {
    "model_id": 400,
    "content": "我最近头晕，有点发热...",
    "sessionid": "pd_20240610_123456"
  }
  
  // 从服务端接收的消息
  {
    "type": "chunk", // 消息类型: chunk(分块), full(完整), error(错误)
    "content": "您好，根据您描述的症状...",
    "sessionid": "pd_20240610_123456",
    "is_final": false // 是否为最后一条消息
  }
  ```

## 注意事项

1. 后台管理系统和小程序服务共用同一数据库，但代码分开管理
2. 添加新功能时，请根据功能归属放入正确的目录（internal 或 wx_internal）
3. 统一的认证、日志、错误处理机制确保系统一致性
4. WebSocket连接需要特别注意超时设置和错误处理
5. 尊重现有代码风格和设计模式

## 常用命令

```bash
# 启动服务
go run main.go

# 使用特定配置启动
go run main.go -f etc/yekaitai-dev.yaml

# 创建数据库迁移（示例）
go run cmd/tools/migration/main.go
``` 