package utils

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"

	"gorm.io/gorm"
)

// ExecuteSQL 执行SQL脚本文件
func ExecuteSQL(db *gorm.DB, sqlFile string) error {
	// 读取SQL文件
	content, err := ioutil.ReadFile(sqlFile)
	if err != nil {
		return fmt.Errorf("读取SQL文件失败: %w", err)
	}

	// 分割SQL语句
	statements := strings.Split(string(content), ";")

	// 执行每个SQL语句
	for _, statement := range statements {
		statement = strings.TrimSpace(statement)
		if statement == "" {
			continue
		}

		if err := db.Exec(statement).Error; err != nil {
			return fmt.Errorf("执行SQL语句失败: %w", err)
		}
	}

	return nil
}

// AutoMigrateModels 使用GORM的AutoMigrate功能自动创建和更新表结构
func AutoMigrateModels(db *gorm.DB, models ...interface{}) error {
	log.Println("开始自动迁移数据库表结构...")

	// 执行自动迁移
	if err := db.AutoMigrate(models...); err != nil {
		return fmt.Errorf("自动迁移数据库失败: %w", err)
	}

	log.Println("数据库表结构迁移完成")
	return nil
}

// InitDatabaseWithSQLFile 使用SQL脚本初始化数据库（旧方法，保留兼容）
func InitDatabaseWithSQLFile(db *gorm.DB, schemaPath string) error {
	// 检查schemaPath是否存在
	if _, err := os.Stat(schemaPath); os.IsNotExist(err) {
		return fmt.Errorf("架构文件不存在: %s", schemaPath)
	}

	// 执行SQL脚本
	log.Printf("正在执行数据库架构脚本: %s", schemaPath)
	if err := ExecuteSQL(db, schemaPath); err != nil {
		return fmt.Errorf("初始化数据库失败: %w", err)
	}

	log.Println("数据库架构初始化成功")
	return nil
}

// InitDatabase 仅通过模型自动迁移初始化数据库
func InitDatabase(db *gorm.DB, models ...interface{}) error {
	return AutoMigrateModels(db, models...)
}

// MigrateDatabase 根据指定路径的所有SQL文件进行数据库迁移
func MigrateDatabase(db *gorm.DB, migrationsPath string) error {
	// 检查迁移路径是否存在
	if _, err := os.Stat(migrationsPath); os.IsNotExist(err) {
		return fmt.Errorf("迁移文件路径不存在: %s", migrationsPath)
	}

	// 获取所有SQL文件
	files, err := filepath.Glob(filepath.Join(migrationsPath, "*.sql"))
	if err != nil {
		return fmt.Errorf("获取迁移文件失败: %w", err)
	}

	// 按文件名排序执行
	for _, file := range files {
		log.Printf("正在执行迁移文件: %s", file)
		if err := ExecuteSQL(db, file); err != nil {
			return fmt.Errorf("执行迁移文件 %s 失败: %w", file, err)
		}
	}

	log.Println("数据库迁移完成")
	return nil
}
