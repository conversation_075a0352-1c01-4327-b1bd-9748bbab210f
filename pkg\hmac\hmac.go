package hmac

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
)

// 签名密钥
const secretKey = "yekaitai_qrcode_verification_secret_key"

// GenerateHMAC 生成HMAC签名
func GenerateHMAC(data string) string {
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// ValidateHMAC 验证HMAC签名
func ValidateHMAC(data, signature string) bool {
	expectedSignature := GenerateHMAC(data)
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}
