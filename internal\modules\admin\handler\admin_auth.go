package handler

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"

	"yekaitai/internal/middleware"
	adminModel "yekaitai/internal/modules/admin/model"
	smsModel "yekaitai/internal/modules/sms/model"
	"yekaitai/internal/modules/sms/service"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// AdminLoginRequest 管理员登录请求参数
type AdminLoginRequest struct {
	Username  string `json:"username"`  // 用户名（兼容旧接口）
	Mobile    string `json:"mobile"`    // 手机号
	Password  string `json:"password"`  // 密码
	Code      string `json:"code"`      // 短信验证码
	LoginType string `json:"loginType"` // 登录类型：pwd-密码登录，sms-短信登录
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken"`
}

// LogoutRequest 登出请求
type LogoutRequest struct {
	UserID string `json:"userId,optional"`
}

// AdminLoginHandler 处理管理员登录
func AdminLoginHandler(svcCtx *svc.ServiceContext, jwtMiddleware *middleware.JWTMiddleware) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var (
			req AdminLoginRequest
		)

		// 读取请求体
		body, err := io.ReadAll(r.Body)
		if err != nil {
			logx.Errorf("读取请求体失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 解析JSON
		err = json.Unmarshal(body, &req)
		if err != nil {
			logx.Errorf("解析JSON失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 记录原始请求参数
		logx.Infof("管理员登录请求参数: %+v", req)

		// 根据登录类型验证参数
		if req.LoginType == "" || (req.LoginType != "pwd" && req.LoginType != "sms") {
			req.LoginType = "pwd" // 默认使用密码登录
		}

		// 创建管理员仓库
		adminRepo := adminModel.NewAdminUserRepository()
		var admin *adminModel.AdminUser

		if req.LoginType == "pwd" {
			// 密码登录：支持用户名或手机号 + 密码
			if req.Username != "" {
				// 查找管理员用户（通过用户名）
				logx.Infof("管理员密码登录请求: 用户名=%s", req.Username)
				admin, err = adminRepo.FindByUsername(req.Username)
			} else if req.Mobile != "" {
				// 查找管理员用户（通过手机号）
				logx.Infof("管理员密码登录请求: 手机号=%s", req.Mobile)
				admin, err = adminRepo.FindByMobile(req.Mobile)
			} else {
				logx.Error("管理员登录请求参数错误: 用户名和手机号不能同时为空")
				httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "请输入用户名或手机号"))
				return
			}

			if err != nil {
				if err == gorm.ErrRecordNotFound {
					logx.Errorf("管理员用户不存在: %s", req.Username)
					httpx.OkJson(w, types.NewErrorResponse(types.CodeUserNotFound, "用户名或密码错误"))
				} else {
					logx.Errorf("查询管理员用户失败: %v", err)
					httpx.OkJson(w, types.NewErrorResponse(types.CodeInternalError, "内部错误"))
				}
				return
			}

			// 检查管理员状态
			if admin.Status != 1 {
				logx.Errorf("管理员账号已被禁用")
				httpx.OkJson(w, types.NewErrorResponse(types.CodeUserNotFound, "账号已被禁用"))
				return
			}

			// 验证密码
			if !utils.VerifyPassword(admin.Password, req.Password) {
				logx.Errorf("管理员密码验证失败")
				httpx.OkJson(w, types.NewErrorResponse(types.CodePasswordError, "用户名或密码错误"))
				return
			}
		} else if req.LoginType == "sms" {
			// 短信验证码登录：手机号 + 验证码
			if req.Mobile == "" {
				logx.Error("管理员短信登录请求参数错误: 手机号不能为空")
				httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "请输入手机号"))
				return
			}

			if req.Code == "" {
				logx.Error("管理员短信登录请求参数错误: 验证码不能为空")
				httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "请输入验证码"))
				return
			}

			// 查找管理员用户（通过手机号）
			logx.Infof("管理员短信登录请求: 手机号=%s", req.Mobile)
			admin, err = adminRepo.FindByMobile(req.Mobile)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					logx.Errorf("管理员用户不存在: 手机号=%s", req.Mobile)
					httpx.OkJson(w, types.NewErrorResponse(types.CodeUserNotFound, "用户不存在"))
				} else {
					logx.Errorf("查询管理员用户失败: %v", err)
					httpx.OkJson(w, types.NewErrorResponse(types.CodeInternalError, "内部错误"))
				}
				return
			}

			// 检查管理员状态
			if admin.Status != 1 {
				logx.Errorf("管理员账号已被禁用: 手机号=%s", req.Mobile)
				httpx.OkJson(w, types.NewErrorResponse(types.CodeUserNotFound, "账号已被禁用"))
				return
			}

			// 验证短信验证码
			smsService := service.CreateSmsServiceWithConfig(svcCtx.Config.TencentSms)
			valid, err := smsService.VerifySmsCode(req.Mobile, req.Code, smsModel.SmsTypeLogin)
			if err != nil || !valid {
				logx.Errorf("短信验证码验证失败: %v", err)
				httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "验证码错误或已过期"))
				return
			}
		}

		// 生成令牌 - 使用管理员ID作为用户ID
		tokenInfo, err := jwtMiddleware.GenerateToken(uint(admin.AdminID), admin.Username, "admin")
		if err != nil {
			logx.Errorf("生成管理员令牌失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInternalError, "登录失败"))
			return
		}

		// 获取客户端IP地址
		clientIP := utils.GetClientIP(r)

		// 更新最后登录时间和IP地址
		if err := adminRepo.UpdateLastLogin(admin.AdminID, clientIP); err != nil {
			logx.Errorf("更新管理员最后登录信息失败: %v", err)
			// 不返回错误，继续处理
		}

		// 获取管理员角色
		var roles []adminModel.AdminRole
		db := mysql.Slave()
		db.Joins("JOIN admin_user_role ON admin_role.role_id = admin_user_role.role_id").
			Where("admin_user_role.admin_id = ?", admin.AdminID).
			Find(&roles)

		// 构建角色列表
		roleNames := make([]string, len(roles))
		for i, role := range roles {
			roleNames[i] = role.RoleName
		}

		// 准备用户信息
		adminInfo := map[string]interface{}{
			"admin_id": admin.AdminID,
			"username": admin.Username,
			"email":    admin.Email,
			"mobile":   admin.Mobile,
			"roles":    roleNames,
		}

		// 组装完整响应
		response := map[string]interface{}{
			"token": tokenInfo,
			"admin": adminInfo,
		}

		logx.Infof("管理员登录成功: ID=%d, Username=%s, Mobile=%s", admin.AdminID, admin.Username, admin.Mobile)
		httpx.OkJson(w, types.NewSuccessResponse(response))
	}
}

// AdminRefreshTokenHandler 处理管理员令牌刷新
func AdminRefreshTokenHandler(svcCtx *svc.ServiceContext, jwtMiddleware *middleware.JWTMiddleware) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req RefreshTokenRequest
		if err := httpx.ParseJsonBody(r, &req); err != nil {
			logx.Errorf("解析管理员刷新令牌请求参数失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 使用专门的管理员刷新Token函数
		tokenInfo, err := middleware.RefreshAdminToken(svcCtx, req.RefreshToken)
		if err != nil {
			logx.Errorf("刷新管理员令牌失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInternalError, "刷新令牌失败"))
			return
		}

		// 返回新令牌
		httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
			"token": map[string]interface{}{
				"accessToken":  tokenInfo.AccessToken,
				"refreshToken": tokenInfo.RefreshToken,
				"expiresAt":    tokenInfo.ExpiresAt,
			},
		}))
	}
}

// AdminLogoutHandler 处理管理员登出
func AdminLogoutHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从Authorization头获取令牌
		authHeader := r.Header.Get("Authorization")
		logx.Infof("管理员登出请求，Authorization头: %s", authHeader)

		if authHeader == "" {
			logx.Error("登出请求没有提供Authorization头")
			httpx.OkJson(w, types.NewErrorResponse(types.CodeUnauthorized, "未提供认证令牌"))
			return
		}

		// 检查令牌格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 {
			logx.Errorf("登出请求Authorization格式错误: %s", authHeader)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
			return
		}

		tokenType := strings.ToLower(parts[0])
		tokenString := parts[1]

		// 支持bearer前缀
		if tokenType != "bearer" {
			logx.Errorf("登出请求Authorization类型错误: %s", tokenType)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeUnauthorized, "认证类型错误"))
			return
		}

		// 解析令牌获取管理员ID
		claims, err := middleware.ParseAdminToken(svcCtx, tokenString)
		if err != nil {
			logx.Errorf("解析管理员登出令牌失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeUnauthorized, "无效的认证令牌"))
			return
		}

		userID := claims.UserID
		userIDStr := strconv.FormatUint(uint64(userID), 10)
		logx.Infof("管理员登出请求，管理员ID: %d", userID)

		// 撤销令牌
		if err := middleware.RevokeAdminToken(svcCtx, userIDStr); err != nil {
			logx.Errorf("撤销管理员令牌失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInternalError, "登出失败"))
			return
		}

		logx.Infof("管理员 %d 登出成功", userID)
		httpx.OkJson(w, types.NewSuccessResponse("管理员登出成功"))
	}
}
