package types

import (
	"time"
	"yekaitai/pkg/common/model/user"
)

// UpdateProfileReq 更新用户资料请求
type UpdateProfileReq struct {
	Avatar   string `json:"avatar,omitempty"`   // 用户头像，可选
	NickName string `json:"nickName,omitempty"` // 用户昵称，可选
}

// UpdateProfileResp 更新用户资料响应
type UpdateProfileResp struct {
	Success bool `json:"success"` // 是否成功
}

// GetPhoneNumberReq 获取手机号请求
type GetPhoneNumberReq struct {
	Code string `json:"code"` // 手机号获取凭证
}

// GetPhoneNumberResp 获取手机号响应
type GetPhoneNumberResp struct {
	Mobile   string                 `json:"mobile"`   // 用户手机号
	UserID   uint                   `json:"userId"`   // 用户ID
	Nickname string                 `json:"nickname"` // 用户昵称
	Avatar   string                 `json:"avatar"`   // 用户头像
	Roles    []string               `json:"roles"`    // 用户角色列表
	Identity map[string]interface{} `json:"identity"` // 用户身份信息
}

// Tag 标签结构体
type Tag struct {
	Id        uint      `json:"id" db:"ID"`
	Name      string    `json:"name" db:"Name"`
	CreatedAt time.Time `json:"created_at" db:"CreatedAt"`
	UpdatedAt time.Time `json:"updated_at" db:"UpdatedAt"`
}

// WxUser 微信用户信息，直接引用数据库模型
type WxUser = user.WxUser

// PhoneInfo 手机号信息
type PhoneInfo struct {
	PhoneNumber     string `json:"phoneNumber"`     // 用户手机号
	PurePhoneNumber string `json:"purePhoneNumber"` // 不带区号的手机号
	CountryCode     string `json:"countryCode"`     // 国际区号
}
