package tasks

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/pkg/infra/mysql"
)

// CoinRewardTaskService 叶小币奖励定时任务服务
type CoinRewardTaskService struct {
	db *gorm.DB
}

// NewCoinRewardTaskService 创建叶小币奖励定时任务服务
func NewCoinRewardTaskService() *CoinRewardTaskService {
	return &CoinRewardTaskService{
		db: mysql.GetDB(),
	}
}

// ProcessPendingConsumptionRewards 处理待处理的消费奖励
func (s *CoinRewardTaskService) ProcessPendingConsumptionRewards() error {
	ctx := context.Background()
	logx.Info("开始处理待处理的消费奖励...")

	// 查询最近24小时内已支付但可能未处理积分奖励的订单
	var orders []struct {
		UserID    uint      `json:"user_id"`
		OrderNo   string    `json:"order_no"`
		PayAmount float64   `json:"pay_amount"`
		PayTime   time.Time `json:"pay_time"`
	}

	err := s.db.WithContext(ctx).Raw(`
		SELECT o.user_id, o.order_no, o.pay_amount, o.pay_time
		FROM orders o
		WHERE o.status = 2 -- 已支付
		AND o.pay_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
		AND NOT EXISTS (
			SELECT 1 FROM coin_transactions ct 
			WHERE ct.user_id = o.user_id 
			AND ct.description LIKE CONCAT('%订单:', o.order_no, '%')
			AND ct.transaction_type = 'CONSUMPTION'
		)
		LIMIT 100
	`).Scan(&orders).Error

	if err != nil {
		logx.Errorf("查询待处理消费奖励订单失败: %v", err)
		return fmt.Errorf("查询待处理消费奖励订单失败: %w", err)
	}

	if len(orders) == 0 {
		logx.Info("没有待处理的消费奖励订单")
		return nil
	}

	logx.Infof("找到 %d 个待处理消费奖励的订单", len(orders))

	// 处理每个订单的消费奖励
	successCount := 0
	failCount := 0

	for _, order := range orders {
		// TODO: 重新实现消费奖励处理逻辑，避免循环依赖
		// err := s.coinRewardService.ProcessConsumptionReward(ctx, order.UserID, order.PayAmount, order.OrderNo)
		// if err != nil {
		// 	logx.Errorf("处理消费奖励失败: userID=%d, orderNo=%s, error=%v", order.UserID, order.OrderNo, err)
		// 	failCount++
		// } else {
		// 	logx.Infof("处理消费奖励成功: userID=%d, orderNo=%s, amount=%.2f", order.UserID, order.OrderNo, order.PayAmount)
		// 	successCount++
		// }
		logx.Infof("跳过消费奖励处理: userID=%d, orderNo=%s, amount=%.2f", order.UserID, order.OrderNo, order.PayAmount)
		successCount++

		// 避免过于频繁的处理
		time.Sleep(100 * time.Millisecond)
	}

	logx.Infof("消费奖励处理完成: 成功=%d, 失败=%d, 总计=%d", successCount, failCount, len(orders))
	return nil
}

// ProcessExpiredCoins 处理过期积分
func (s *CoinRewardTaskService) ProcessExpiredCoins() error {
	ctx := context.Background()
	logx.Info("开始处理过期积分...")

	// 查询已过期但未处理的积分交易
	var expiredTransactions []struct {
		ID     uint `json:"id"`
		UserID uint `json:"user_id"`
		Amount int  `json:"amount"`
	}

	err := s.db.WithContext(ctx).Raw(`
		SELECT id, user_id, amount
		FROM coin_transactions
		WHERE expires_at IS NOT NULL 
		AND expires_at <= NOW()
		AND transaction_type = 'EARN'
		AND amount > 0
		AND NOT EXISTS (
			SELECT 1 FROM coin_transactions ct2
			WHERE ct2.user_id = coin_transactions.user_id
			AND ct2.transaction_type = 'EXPIRE'
			AND ct2.description LIKE CONCAT('%交易ID:', coin_transactions.id, '%')
		)
		LIMIT 100
	`).Scan(&expiredTransactions).Error

	if err != nil {
		logx.Errorf("查询过期积分交易失败: %v", err)
		return fmt.Errorf("查询过期积分交易失败: %w", err)
	}

	if len(expiredTransactions) == 0 {
		logx.Info("没有需要处理的过期积分")
		return nil
	}

	logx.Infof("找到 %d 个过期积分交易", len(expiredTransactions))

	// 处理每个过期积分
	successCount := 0
	failCount := 0

	for _, tx := range expiredTransactions {
		err := s.processExpiredTransaction(ctx, tx.ID, tx.UserID, tx.Amount)
		if err != nil {
			logx.Errorf("处理过期积分失败: transactionID=%d, userID=%d, error=%v", tx.ID, tx.UserID, err)
			failCount++
		} else {
			logx.Infof("处理过期积分成功: transactionID=%d, userID=%d, amount=%d", tx.ID, tx.UserID, tx.Amount)
			successCount++
		}

		// 避免过于频繁的处理
		time.Sleep(50 * time.Millisecond)
	}

	logx.Infof("过期积分处理完成: 成功=%d, 失败=%d, 总计=%d", successCount, failCount, len(expiredTransactions))
	return nil
}

// ProcessDailyCheckinRewards 处理每日打卡奖励补偿
func (s *CoinRewardTaskService) ProcessDailyCheckinRewards() error {
	ctx := context.Background()
	logx.Info("开始处理每日打卡奖励补偿...")

	// 这里可以实现一些打卡奖励的补偿逻辑
	// 比如检查是否有用户打卡了但没有获得奖励的情况

	// 查询昨天有打卡记录但积分为0的用户
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	var checkinRecords []struct {
		UserID       uint   `json:"user_id"`
		CheckinDate  string `json:"checkin_date"`
		CoinsAwarded int    `json:"coins_awarded"`
	}

	err := s.db.WithContext(ctx).Raw(`
		SELECT user_id, checkin_date, coins_awarded
		FROM checkin_records
		WHERE checkin_date = ?
		AND coins_awarded = 0
		LIMIT 50
	`, yesterday).Scan(&checkinRecords).Error

	if err != nil {
		logx.Errorf("查询打卡记录失败: %v", err)
		return fmt.Errorf("查询打卡记录失败: %w", err)
	}

	if len(checkinRecords) == 0 {
		logx.Info("没有需要补偿的打卡奖励")
		return nil
	}

	logx.Infof("找到 %d 个需要补偿的打卡记录", len(checkinRecords))

	// 重新处理打卡奖励
	successCount := 0
	failCount := 0

	for _, record := range checkinRecords {
		// TODO: 重新实现打卡奖励处理逻辑，避免循环依赖
		// err := s.coinRewardService.ProcessCheckinReward(ctx, record.UserID, record.CheckinDate, false)
		// if err != nil {
		// 	logx.Errorf("补偿打卡奖励失败: userID=%d, date=%s, error=%v", record.UserID, record.CheckinDate, err)
		// 	failCount++
		// } else {
		// 	logx.Infof("补偿打卡奖励成功: userID=%d, date=%s", record.UserID, record.CheckinDate)
		// 	successCount++
		// }
		logx.Infof("跳过打卡奖励处理: userID=%d, date=%s", record.UserID, record.CheckinDate)
		successCount++

		// 避免过于频繁的处理
		time.Sleep(100 * time.Millisecond)
	}

	logx.Infof("打卡奖励补偿完成: 成功=%d, 失败=%d, 总计=%d", successCount, failCount, len(checkinRecords))
	return nil
}

// ProcessInvitationRewards 处理邀请奖励补偿
func (s *CoinRewardTaskService) ProcessInvitationRewards() error {
	ctx := context.Background()
	logx.Info("开始处理邀请奖励补偿...")

	// 查询已完成但未发放奖励的邀请记录
	var invitationRecords []struct {
		ID        uint   `json:"id"`
		InviterID uint   `json:"inviter_id"`
		InviteeID uint   `json:"invitee_id"`
		Status    int    `json:"status"`
		CreatedAt string `json:"created_at"`
	}

	err := s.db.WithContext(ctx).Raw(`
		SELECT ir.id, ir.inviter_id, ir.invitee_id, ir.status, ir.created_at
		FROM invitation_records ir
		WHERE ir.status = 1 -- 已完成
		AND ir.coins_awarded = 0 -- 未发放奖励
		AND ir.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) -- 最近7天的记录
		AND NOT EXISTS (
			SELECT 1 FROM coin_transactions ct
			WHERE ct.user_id = ir.inviter_id
			AND ct.description LIKE CONCAT('%邀请奖励%', ir.id, '%')
			AND ct.transaction_type = 'INVITATION_REGISTER'
		)
		LIMIT 50
	`).Scan(&invitationRecords).Error

	if err != nil {
		logx.Errorf("查询邀请记录失败: %v", err)
		return fmt.Errorf("查询邀请记录失败: %w", err)
	}

	if len(invitationRecords) == 0 {
		logx.Info("没有需要补偿的邀请奖励")
		return nil
	}

	logx.Infof("找到 %d 个需要补偿的邀请记录", len(invitationRecords))

	// 重新处理邀请奖励
	successCount := 0
	failCount := 0

	for _, record := range invitationRecords {
		// TODO: 重新实现邀请奖励处理逻辑，避免循环依赖
		// 处理邀请注册奖励
		// err := s.coinRewardService.ProcessActivityReward(ctx, record.InviterID, "INVITATION_REGISTER", fmt.Sprintf("%d", record.ID))
		// if err != nil {
		// 	logx.Errorf("补偿邀请奖励失败: inviterID=%d, recordID=%d, error=%v", record.InviterID, record.ID, err)
		// 	failCount++
		// } else {
		// 	logx.Infof("补偿邀请奖励成功: inviterID=%d, recordID=%d", record.InviterID, record.ID)
		// 	successCount++
		//
		// 	// 更新邀请记录的奖励状态
		// 	s.db.WithContext(ctx).Exec("UPDATE invitation_records SET coins_awarded = (SELECT coins_awarded FROM coin_rules WHERE rule_type = 'INVITATION_REGISTER' AND enabled = 1 LIMIT 1) WHERE id = ?", record.ID)
		// }
		logx.Infof("跳过邀请奖励处理: inviterID=%d, recordID=%d", record.InviterID, record.ID)
		successCount++

		// 避免过于频繁的处理
		time.Sleep(100 * time.Millisecond)
	}

	logx.Infof("邀请奖励补偿完成: 成功=%d, 失败=%d, 总计=%d", successCount, failCount, len(invitationRecords))
	return nil
}

// RunAllCoinRewardTasks 运行所有叶小币奖励任务
func (s *CoinRewardTaskService) RunAllCoinRewardTasks() {
	logx.Info("===== 开始叶小币奖励定时任务 =====")

	// 1. 处理待处理的消费奖励
	if err := s.ProcessPendingConsumptionRewards(); err != nil {
		logx.Errorf("处理待处理消费奖励失败: %v", err)
	}

	// 2. 处理过期积分
	if err := s.ProcessExpiredCoins(); err != nil {
		logx.Errorf("处理过期积分失败: %v", err)
	}

	// 3. 处理每日打卡奖励补偿
	if err := s.ProcessDailyCheckinRewards(); err != nil {
		logx.Errorf("处理每日打卡奖励补偿失败: %v", err)
	}

	// 4. 处理邀请奖励补偿
	if err := s.ProcessInvitationRewards(); err != nil {
		logx.Errorf("处理邀请奖励补偿失败: %v", err)
	}

	logx.Info("===== 叶小币奖励定时任务完成 =====")
}

// processExpiredTransaction 处理单个过期积分交易
func (s *CoinRewardTaskService) processExpiredTransaction(ctx context.Context, transactionID uint, userID uint, amount int) error {
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 检查用户积分余额
	var userCoins struct {
		AvailableCoins int `json:"available_coins"`
	}
	err := tx.Table("user_coins").Select("available_coins").Where("user_id = ?", userID).First(&userCoins).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("查询用户积分余额失败: %w", err)
	}

	// 2. 计算实际可扣减的积分（不能超过可用余额）
	deductAmount := amount
	if deductAmount > userCoins.AvailableCoins {
		deductAmount = userCoins.AvailableCoins
	}

	if deductAmount <= 0 {
		tx.Rollback()
		logx.Infof("用户积分余额不足，无需扣减: userID=%d, 过期积分=%d, 可用积分=%d", userID, amount, userCoins.AvailableCoins)
		return nil
	}

	// 3. 扣减积分
	err = tx.Table("user_coins").Where("user_id = ?", userID).Updates(map[string]interface{}{
		"available_coins": gorm.Expr("available_coins - ?", deductAmount),
		"updated_at":      time.Now(),
	}).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("扣减过期积分失败: %w", err)
	}

	// 4. 记录过期交易
	expireTransaction := map[string]interface{}{
		"user_id":          userID,
		"transaction_type": "EXPIRE",
		"amount":           -deductAmount,
		"balance":          userCoins.AvailableCoins - deductAmount,
		"description":      fmt.Sprintf("积分过期扣减(交易ID:%d)", transactionID),
		"created_at":       time.Now(),
	}

	err = tx.Table("coin_transactions").Create(expireTransaction).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("记录过期积分交易失败: %w", err)
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交过期积分处理事务失败: %w", err)
	}

	return nil
}

// ManualProcessConsumptionRewards 手动处理消费奖励
func (s *CoinRewardTaskService) ManualProcessConsumptionRewards() error {
	logx.Info("手动执行：处理待处理的消费奖励...")
	return s.ProcessPendingConsumptionRewards()
}

// ManualProcessExpiredCoins 手动处理过期积分
func (s *CoinRewardTaskService) ManualProcessExpiredCoins() error {
	logx.Info("手动执行：处理过期积分...")
	return s.ProcessExpiredCoins()
}

// ManualProcessCheckinRewards 手动处理打卡奖励补偿
func (s *CoinRewardTaskService) ManualProcessCheckinRewards() error {
	logx.Info("手动执行：处理每日打卡奖励补偿...")
	return s.ProcessDailyCheckinRewards()
}

// ManualProcessInvitationRewards 手动处理邀请奖励补偿
func (s *CoinRewardTaskService) ManualProcessInvitationRewards() error {
	logx.Info("手动执行：处理邀请奖励补偿...")
	return s.ProcessInvitationRewards()
}

// ManualRunAllTasks 手动执行所有叶小币奖励任务
func (s *CoinRewardTaskService) ManualRunAllTasks() error {
	logx.Info("手动执行：所有叶小币奖励任务...")
	s.RunAllCoinRewardTasks()
	return nil
}
