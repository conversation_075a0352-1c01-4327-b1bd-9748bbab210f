package handler

import (
	"net/http"
	"yekaitai/internal/modules/sms/model"
	"yekaitai/internal/modules/sms/service"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// SendSmsCodeRequest 发送短信验证码请求
type SendSmsCodeRequest struct {
	Mobile string `json:"mobile"` // 手机号
	Type   int    `json:"type"`   // 验证码类型
}

// VerifySmsCodeRequest 验证短信验证码请求
type VerifySmsCodeRequest struct {
	Mobile string `json:"mobile"` // 手机号
	Code   string `json:"code"`   // 验证码
	Type   int    `json:"type"`   // 验证码类型
}

// SendSmsCodeHandler 发送短信验证码处理
func SendSmsCodeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req SendSmsCodeRequest
		if err := httpx.ParseJsonBody(r, &req); err != nil {
			logx.Errorf("解析发送短信验证码请求参数失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		logx.Infof("收到发送短信验证码请求: mobile=%s, type=%d", req.Mobile, req.Type)

		// 调试：打印配置信息
		logx.Infof("当前配置中的腾讯云短信配置: %+v", svcCtx.Config.TencentSms)

		// 参数验证
		if req.Mobile == "" || len(req.Mobile) != 11 {
			logx.Errorf("发送验证码请求参数错误: mobile=%s", req.Mobile)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "请输入正确的手机号"))
			return
		}

		if req.Type < model.SmsTypeLogin || req.Type > model.SmsTypeChangeMobile {
			logx.Errorf("发送验证码请求参数错误: type=%d", req.Type)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "无效的验证码类型"))
			return
		}

		// 使用ServiceContext中的短信服务
		smsService := service.CreateSmsServiceWithConfig(svcCtx.Config.TencentSms)

		// 发送验证码
		_, err := smsService.SendSmsCode(req.Mobile, req.Type)
		if err != nil {
			logx.Errorf("发送短信验证码失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInternalError, "发送验证码失败"))
			return
		}

		// 响应结果（不返回验证码，确保安全）
		httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
			"mobile":  req.Mobile,
			"message": "验证码已发送，请注意查收",
		}))
	}
}

// VerifySmsCodeHandler 验证短信验证码处理
func VerifySmsCodeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req VerifySmsCodeRequest
		if err := httpx.ParseJsonBody(r, &req); err != nil {
			logx.Errorf("解析验证短信验证码请求参数失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 参数验证
		if req.Mobile == "" || len(req.Mobile) != 11 {
			logx.Errorf("验证验证码请求参数错误: mobile=%s", req.Mobile)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "请输入正确的手机号"))
			return
		}

		if req.Code == "" || len(req.Code) != 6 {
			logx.Errorf("验证验证码请求参数错误: code=%s", req.Code)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "请输入正确的验证码"))
			return
		}

		if req.Type < model.SmsTypeLogin || req.Type > model.SmsTypeChangeMobile {
			logx.Errorf("验证验证码请求参数错误: type=%d", req.Type)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "无效的验证码类型"))
			return
		}

		// 使用ServiceContext中的短信服务
		smsService := service.CreateSmsServiceWithConfig(svcCtx.Config.TencentSms)

		// 验证验证码
		valid, err := smsService.VerifySmsCode(req.Mobile, req.Code, req.Type)
		if err != nil {
			logx.Errorf("验证短信验证码失败: %v", err)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
			return
		}

		if !valid {
			logx.Errorf("验证码验证失败: mobile=%s, code=%s", req.Mobile, req.Code)
			httpx.OkJson(w, types.NewErrorResponse(types.CodeInvalidParams, "验证码错误"))
			return
		}

		// 响应结果
		httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
			"verified": true,
		}))
	}
}
