package types

// OutpatientQueryByPatientReq 按患者查询门诊单请求
type OutpatientQueryByPatientReq struct {
	PatientID string `path:"patientId"` // 患者ID
	BeginDate string `form:"beginDate"` // 开始日期 yyyy-MM-dd，默认为3个月前
	EndDate   string `form:"endDate"`   // 结束日期 yyyy-MM-dd，默认为当前
	Limit     int    `form:"limit"`     // 每页显示条数，默认20
	Offset    int    `form:"offset"`    // 分页起始下标，默认0
}

// OutpatientDetailReq 获取门诊单详情请求
type OutpatientDetailReq struct {
	ID string `path:"id"` // 门诊单ID
}

// OutpatientCreateReq 创建门诊单请求
type OutpatientCreateReq struct {
	DepartmentID  string `json:"departmentId"` // 科室ID
	DoctorID      string `json:"doctorId"`     // 医生ID
	PatientID     string `json:"patientId"`    // 患者ID
	OperatorID    string `json:"operatorId"`   // 操作人ID
	MedicalRecord struct {
		ChiefComplaint      string `json:"chiefComplaint"`      // 主诉
		PastHistory         string `json:"pastHistory"`         // 既往史
		FamilyHistory       string `json:"familyHistory"`       // 家族史
		PresentHistory      string `json:"presentHistory"`      // 现病史
		PhysicalExamination string `json:"physicalExamination"` // 体格检查
		Diagnosis           string `json:"diagnosis"`           // 诊断
		DoctorAdvice        string `json:"doctorAdvice"`        // 医嘱
		Syndrome            string `json:"syndrome"`            // 证候
		Therapy             string `json:"therapy"`             // 治法
		ChineseExamination  string `json:"chineseExamination"`  // 中医检查
		ObstetricalHistory  string `json:"obstetricalHistory"`  // 产科史
	} `json:"medicalRecord"`
	PrescriptionChineseForms []struct {
		IsTotalPriceChanged   int     `json:"isTotalPriceChanged"` // 是否修改总价
		PharmacyNo            int     `json:"pharmacyNo"`          // 药房编号
		ExpectedTotalPrice    float64 `json:"expectedTotalPrice"`  // 预期总价
		Specification         string  `json:"specification"`       // 规格
		DoseCount             int     `json:"doseCount"`           // 剂数
		PrescriptionFormItems []struct {
			ProductID          string  `json:"productId"`          // 产品ID
			SpecialRequirement string  `json:"specialRequirement"` // 特殊要求
			ProductShortID     string  `json:"productShortId"`     // 产品短ID
			UnitCount          float64 `json:"unitCount"`          // 单位数量
			ExpectedUnitPrice  float64 `json:"expectedUnitPrice"`  // 预期单价
			ExpectedTotalPrice float64 `json:"expectedTotalPrice"` // 预期总价
			UseDismounting     int     `json:"useDismounting"`     // 是否拆零
		} `json:"prescriptionFormItems"`
		Usage       string `json:"usage"`       // 用法
		DailyDosage string `json:"dailyDosage"` // 每日剂量
		Freq        string `json:"freq"`        // 频次
		UsageLevel  string `json:"usageLevel"`  // 用量
		UsageDays   string `json:"usageDays"`   // 用药天数
		Requirement string `json:"requirement"` // 要求
		ProcessInfo struct {
			UsageType    int     `json:"usageType"`    // 用法类型
			UsageSubType int     `json:"usageSubType"` // 用法子类型
			UnitCount    float64 `json:"unitCount"`    // 单位数量
			TotalCount   float64 `json:"totalCount"`   // 总数量
			Price        float64 `json:"price"`        // 价格
			Remark       string  `json:"remark"`       // 备注
		} `json:"processInfo"`
	} `json:"prescriptionChineseForms"`
	PrescriptionWesternForms []struct {
		IsTotalPriceChanged   int     `json:"isTotalPriceChanged"` // 是否修改总价
		ExpectedTotalPrice    float64 `json:"expectedTotalPrice"`  // 预期总价
		PrescriptionFormItems []struct {
			Ast                int     `json:"ast"`                // 是否皮试
			ProductID          string  `json:"productId"`          // 产品ID
			ProductShortID     string  `json:"productShortId"`     // 产品短ID
			Usage              string  `json:"usage"`              // 用法
			Freq               string  `json:"freq"`               // 频次
			UnitCount          float64 `json:"unitCount"`          // 单位数量
			Dosage             float64 `json:"dosage"`             // 剂量
			ExpectedUnitPrice  float64 `json:"expectedUnitPrice"`  // 预期单价
			DosageUnit         string  `json:"dosageUnit"`         // 剂量单位
			ExpectedTotalPrice float64 `json:"expectedTotalPrice"` // 预期总价
			Days               float64 `json:"days"`               // 天数
			UseDismounting     int     `json:"useDismounting"`     // 是否拆零
			SpecialRequirement string  `json:"specialRequirement"` // 特殊要求
			PharmacyNo         int     `json:"pharmacyNo"`         // 药房编号
		} `json:"prescriptionFormItems"`
	} `json:"prescriptionWesternForms"`
}

// OutpatientQueryByDateReq 按天查询门诊单请求
type OutpatientQueryByDateReq struct {
	Date   string `form:"date"`   // 日期（格式yyyy-MM-dd）
	Limit  int    `form:"limit"`  // 每页显示条数，默认20，最大100
	Offset int    `form:"offset"` // 分页起始下标，默认0
}

// OutpatientSummary 门诊单摘要信息
type OutpatientSummary struct {
	ID             string `json:"id"`             // 门诊单ID
	PatientOrderID string `json:"patientOrderId"` // 就诊单ID
	Patient        struct {
		ID       string `json:"id"`       // 患者ID
		Name     string `json:"name"`     // 姓名
		Mobile   string `json:"mobile"`   // 手机号
		Sex      string `json:"sex"`      // 性别
		Birthday string `json:"birthday"` // 生日
		Age      struct {
			Year  int `json:"year"`  // 年
			Month int `json:"month"` // 月
			Day   int `json:"day"`   // 日
		} `json:"age"`
		IDCard string `json:"idCard"` // 身份证号
	} `json:"patient"`
	Created        string `json:"created"`        // 创建时间
	DoctorID       string `json:"doctorId"`       // 医生ID
	DoctorName     string `json:"doctorName"`     // 医生姓名
	PatientOrderNo string `json:"patientOrderNo"` // 就诊单号
	Status         int    `json:"status"`         // 状态
	StatusName     string `json:"statusName"`     // 状态名称
}

// OutpatientDetail 门诊单详细信息
type OutpatientDetail struct {
	ID             string `json:"id"`             // 门诊单ID
	ClinicID       string `json:"clinicId"`       // 诊所ID
	PatientOrderID string `json:"patientOrderId"` // 就诊单ID
	PatientOrderNo string `json:"patientOrderNo"` // 就诊单号
	DepartmentID   string `json:"departmentId"`   // 科室ID
	DepartmentName string `json:"departmentName"` // 科室名称
	DoctorID       string `json:"doctorId"`       // 医生ID
	DoctorName     string `json:"doctorName"`     // 医生姓名
	Created        string `json:"created"`        // 创建时间
	DiagnosedDate  string `json:"diagnosedDate"`  // 就诊日期
	Status         int    `json:"status"`         // 状态
	StatusName     string `json:"statusName"`     // 状态名称
	RevisitStatus  int    `json:"revisitStatus"`  // 复诊状态
	Patient        struct {
		ID       string `json:"id"`       // 患者ID
		Name     string `json:"name"`     // 姓名
		Mobile   string `json:"mobile"`   // 手机号
		Sex      string `json:"sex"`      // 性别
		Birthday string `json:"birthday"` // 生日
		Age      struct {
			Year  int `json:"year"`  // 年
			Month int `json:"month"` // 月
			Day   int `json:"day"`   // 日
		} `json:"age"`
		IDCard string `json:"idCard"` // 身份证号
	} `json:"patient"`
	MedicalRecord struct {
		ID                  string `json:"id"`                  // 病历ID
		OutpatientSheetID   string `json:"outpatientSheetId"`   // 门诊单ID
		ChiefComplaint      string `json:"chiefComplaint"`      // 主诉
		PastHistory         string `json:"pastHistory"`         // 既往史
		FamilyHistory       string `json:"familyHistory"`       // 家族史
		PresentHistory      string `json:"presentHistory"`      // 现病史
		PhysicalExamination string `json:"physicalExamination"` // 体格检查
		Diagnosis           string `json:"diagnosis"`           // 诊断
		DoctorAdvice        string `json:"doctorAdvice"`        // 医嘱
		Syndrome            string `json:"syndrome"`            // 证候
		Therapy             string `json:"therapy"`             // 治法
		ChineseExamination  string `json:"chineseExamination"`  // 中医检查
		WearGlassesHistory  string `json:"wearGlassesHistory"`  // 戴镜史
		ObstetricalHistory  string `json:"obstetricalHistory"`  // 产科史
		EyeExamination      struct {
			Items []struct {
				Key           string `json:"key"`           // 检查项key
				Name          string `json:"name"`          // 检查项名称
				RightEyeValue string `json:"rightEyeValue"` // 右眼值
				LeftEyeValue  string `json:"leftEyeValue"`  // 左眼值
			} `json:"items"`
		} `json:"eyeExamination"`
		AllergicHistory string `json:"allergicHistory"` // 过敏史
		Attachments     []struct {
			URL      string `json:"url"`      // 附件URL
			FileName string `json:"fileName"` // 文件名
			FileSize int    `json:"fileSize"` // 文件大小
		} `json:"attachments"`
	} `json:"medicalRecord"`
	PrescriptionChineseForms []struct {
		ID                    string `json:"id"`            // 处方ID
		Type                  int    `json:"type"`          // 处方类型
		Specification         string `json:"specification"` // 规格
		DoseCount             int    `json:"doseCount"`     // 剂数
		DailyDosage           string `json:"dailyDosage"`   // 每日剂量
		Usage                 string `json:"usage"`         // 用法
		Freq                  string `json:"freq"`          // 频次
		Requirement           string `json:"requirement"`   // 要求
		UsageLevel            string `json:"usageLevel"`    // 用量
		PrescriptionFormItems []struct {
			ID                 string  `json:"id"`                 // 处方项ID
			PrescriptionFormID string  `json:"prescriptionFormId"` // 处方ID
			ProductID          string  `json:"productId"`          // 产品ID
			Type               int     `json:"type"`               // 类型
			SubType            int     `json:"subType"`            // 子类型
			Name               string  `json:"name"`               // 名称
			Specification      string  `json:"specification"`      // 规格
			Manufacturer       string  `json:"manufacturer"`       // 厂家
			Usage              string  `json:"usage"`              // 用法
			Ivgtt              float64 `json:"ivgtt"`              // 静滴
			IvgttUnit          string  `json:"ivgttUnit"`          // 静滴单位
			Freq               string  `json:"freq"`               // 频次
			Dosage             string  `json:"dosage"`             // 剂量
			DosageUnit         string  `json:"dosageUnit"`         // 剂量单位
			Days               int     `json:"days"`               // 天数
			SpecialRequirement string  `json:"specialRequirement"` // 特殊要求
			IsDismounting      int     `json:"isDismounting"`      // 是否拆零
			UnitCount          float64 `json:"unitCount"`          // 单位数量
			Unit               string  `json:"unit"`               // 单位
			UnitPrice          float64 `json:"unitPrice"`          // 单价
			GroupID            int     `json:"groupId"`            // 组ID
			IsAst              int     `json:"isAst"`              // 是否皮试
			AstResult          struct {
				Result      string `json:"result"`      // 结果
				Description string `json:"description"` // 描述
			} `json:"astResult"`
			ProductInfo struct {
				ID     string `json:"id"`   // 产品ID
				Name   string `json:"name"` // 名称
				Shebao struct {
					NationalCode   string `json:"nationalCode"` // 国标码
					InsuranceTypes []struct {
						InsuranceType      string  `json:"insuranceType"`      // 保险类型
						ReimbursementRatio float64 `json:"reimbursementRatio"` // 报销比例
					} `json:"insuranceTypes"`
				} `json:"shebao"`
				DisableSell    int     `json:"disableSell"`    // 是否禁用销售
				TypeID         int     `json:"typeId"`         // 类型ID
				TypeName       string  `json:"typeName"`       // 类型名称
				CustomTypeID   string  `json:"customTypeId"`   // 自定义类型ID
				CustomTypeName string  `json:"customTypeName"` // 自定义类型名称
				Manufacturer   string  `json:"manufacturer"`   // 厂家
				MedicineNmpn   string  `json:"medicineNmpn"`   // 药品批准文号
				BarCode        string  `json:"barCode"`        // 条码
				PieceUnit      string  `json:"pieceUnit"`      // 片剂单位
				PieceNum       float64 `json:"pieceNum"`       // 片剂数量
				PiecePrice     float64 `json:"piecePrice"`     // 片剂价格
				PackageUnit    string  `json:"packageUnit"`    // 包装单位
				PackagePrice   float64 `json:"packagePrice"`   // 包装价格
				ShortID        string  `json:"shortId"`        // 短ID
				Specification  string  `json:"specification"`  // 规格
				OtcType        int     `json:"otcType"`        // OTC类型
				Status         int     `json:"status"`         // 状态
				LastModified   string  `json:"lastModified"`   // 最后修改时间
				GoodsSpu       struct {
					ID        string `json:"id"`        // SPU ID
					Name      string `json:"name"`      // 名称
					BrandName string `json:"brandName"` // 品牌名称
					Material  string `json:"material"`  // 材质
				} `json:"goodsSpu"`
				GoodsSpec struct {
					Color string `json:"color"` // 颜色
					Spec  string `json:"spec"`  // 规格
				} `json:"goodsSpec"`
			} `json:"productInfo"`
		} `json:"prescriptionFormItems"`
	} `json:"prescriptionChineseForms"`
	PrescriptionWesternForms []struct {
		ID                    string `json:"id"`            // 处方ID
		Type                  int    `json:"type"`          // 处方类型
		Specification         string `json:"specification"` // 规格
		DoseCount             int    `json:"doseCount"`     // 剂数
		DailyDosage           string `json:"dailyDosage"`   // 每日剂量
		Usage                 string `json:"usage"`         // 用法
		Freq                  string `json:"freq"`          // 频次
		Requirement           string `json:"requirement"`   // 要求
		UsageLevel            string `json:"usageLevel"`    // 用量
		PrescriptionFormItems []struct {
			ID                 string  `json:"id"`                 // 处方项ID
			PrescriptionFormID string  `json:"prescriptionFormId"` // 处方ID
			ProductID          string  `json:"productId"`          // 产品ID
			Type               int     `json:"type"`               // 类型
			SubType            int     `json:"subType"`            // 子类型
			Name               string  `json:"name"`               // 名称
			Specification      string  `json:"specification"`      // 规格
			Manufacturer       string  `json:"manufacturer"`       // 厂家
			Usage              string  `json:"usage"`              // 用法
			Ivgtt              float64 `json:"ivgtt"`              // 静滴
			IvgttUnit          string  `json:"ivgttUnit"`          // 静滴单位
			Freq               string  `json:"freq"`               // 频次
			Dosage             string  `json:"dosage"`             // 剂量
			DosageUnit         string  `json:"dosageUnit"`         // 剂量单位
			Days               int     `json:"days"`               // 天数
			SpecialRequirement string  `json:"specialRequirement"` // 特殊要求
			IsDismounting      int     `json:"isDismounting"`      // 是否拆零
			UnitCount          float64 `json:"unitCount"`          // 单位数量
			Unit               string  `json:"unit"`               // 单位
			UnitPrice          float64 `json:"unitPrice"`          // 单价
			GroupID            int     `json:"groupId"`            // 组ID
			IsAst              int     `json:"isAst"`              // 是否皮试
			AstResult          struct {
				Result      string `json:"result"`      // 结果
				Description string `json:"description"` // 描述
			} `json:"astResult"`
			ProductInfo struct {
				ID     string `json:"id"`   // 产品ID
				Name   string `json:"name"` // 名称
				Shebao struct {
					NationalCode   string `json:"nationalCode"` // 国标码
					InsuranceTypes []struct {
						InsuranceType      string  `json:"insuranceType"`      // 保险类型
						ReimbursementRatio float64 `json:"reimbursementRatio"` // 报销比例
					} `json:"insuranceTypes"`
				} `json:"shebao"`
				DisableSell    int     `json:"disableSell"`    // 是否禁用销售
				TypeID         int     `json:"typeId"`         // 类型ID
				TypeName       string  `json:"typeName"`       // 类型名称
				CustomTypeID   string  `json:"customTypeId"`   // 自定义类型ID
				CustomTypeName string  `json:"customTypeName"` // 自定义类型名称
				Manufacturer   string  `json:"manufacturer"`   // 厂家
				MedicineNmpn   string  `json:"medicineNmpn"`   // 药品批准文号
				BarCode        string  `json:"barCode"`        // 条码
				PieceUnit      string  `json:"pieceUnit"`      // 片剂单位
				PieceNum       float64 `json:"pieceNum"`       // 片剂数量
				PiecePrice     float64 `json:"piecePrice"`     // 片剂价格
				PackageUnit    string  `json:"packageUnit"`    // 包装单位
				PackagePrice   float64 `json:"packagePrice"`   // 包装价格
				ShortID        string  `json:"shortId"`        // 短ID
				Specification  string  `json:"specification"`  // 规格
				OtcType        int     `json:"otcType"`        // OTC类型
				Status         int     `json:"status"`         // 状态
				LastModified   string  `json:"lastModified"`   // 最后修改时间
				GoodsSpu       struct {
					ID        string `json:"id"`        // SPU ID
					Name      string `json:"name"`      // 名称
					BrandName string `json:"brandName"` // 品牌名称
					Material  string `json:"material"`  // 材质
				} `json:"goodsSpu"`
				GoodsSpec struct {
					Color string `json:"color"` // 颜色
					Spec  string `json:"spec"`  // 规格
				} `json:"goodsSpec"`
			} `json:"productInfo"`
		} `json:"prescriptionFormItems"`
	} `json:"prescriptionWesternForms"`
	PrescriptionInfusionForms []struct {
		ID                    string `json:"id"`            // 处方ID
		Type                  int    `json:"type"`          // 处方类型
		Specification         string `json:"specification"` // 规格
		DoseCount             int    `json:"doseCount"`     // 剂数
		DailyDosage           string `json:"dailyDosage"`   // 每日剂量
		Usage                 string `json:"usage"`         // 用法
		Freq                  string `json:"freq"`          // 频次
		Requirement           string `json:"requirement"`   // 要求
		UsageLevel            string `json:"usageLevel"`    // 用量
		PrescriptionFormItems []struct {
			ID                 string  `json:"id"`                 // 处方项ID
			PrescriptionFormID string  `json:"prescriptionFormId"` // 处方ID
			ProductID          string  `json:"productId"`          // 产品ID
			Type               int     `json:"type"`               // 类型
			SubType            int     `json:"subType"`            // 子类型
			Name               string  `json:"name"`               // 名称
			Specification      string  `json:"specification"`      // 规格
			Manufacturer       string  `json:"manufacturer"`       // 厂家
			Usage              string  `json:"usage"`              // 用法
			Ivgtt              float64 `json:"ivgtt"`              // 静滴
			IvgttUnit          string  `json:"ivgttUnit"`          // 静滴单位
			Freq               string  `json:"freq"`               // 频次
			Dosage             string  `json:"dosage"`             // 剂量
			DosageUnit         string  `json:"dosageUnit"`         // 剂量单位
			Days               int     `json:"days"`               // 天数
			SpecialRequirement string  `json:"specialRequirement"` // 特殊要求
			IsDismounting      int     `json:"isDismounting"`      // 是否拆零
			UnitCount          float64 `json:"unitCount"`          // 单位数量
			Unit               string  `json:"unit"`               // 单位
			UnitPrice          float64 `json:"unitPrice"`          // 单价
			GroupID            int     `json:"groupId"`            // 组ID
			IsAst              int     `json:"isAst"`              // 是否皮试
			AstResult          struct {
				Result      string `json:"result"`      // 结果
				Description string `json:"description"` // 描述
			} `json:"astResult"`
			ProductInfo struct {
				ID     string `json:"id"`   // 产品ID
				Name   string `json:"name"` // 名称
				Shebao struct {
					NationalCode   string `json:"nationalCode"` // 国标码
					InsuranceTypes []struct {
						InsuranceType      string  `json:"insuranceType"`      // 保险类型
						ReimbursementRatio float64 `json:"reimbursementRatio"` // 报销比例
					} `json:"insuranceTypes"`
				} `json:"shebao"`
				DisableSell    int     `json:"disableSell"`    // 是否禁用销售
				TypeID         int     `json:"typeId"`         // 类型ID
				TypeName       string  `json:"typeName"`       // 类型名称
				CustomTypeID   string  `json:"customTypeId"`   // 自定义类型ID
				CustomTypeName string  `json:"customTypeName"` // 自定义类型名称
				Manufacturer   string  `json:"manufacturer"`   // 厂家
				MedicineNmpn   string  `json:"medicineNmpn"`   // 药品批准文号
				BarCode        string  `json:"barCode"`        // 条码
				PieceUnit      string  `json:"pieceUnit"`      // 片剂单位
				PieceNum       float64 `json:"pieceNum"`       // 片剂数量
				PiecePrice     float64 `json:"piecePrice"`     // 片剂价格
				PackageUnit    string  `json:"packageUnit"`    // 包装单位
				PackagePrice   float64 `json:"packagePrice"`   // 包装价格
				ShortID        string  `json:"shortId"`        // 短ID
				Specification  string  `json:"specification"`  // 规格
				OtcType        int     `json:"otcType"`        // OTC类型
				Status         int     `json:"status"`         // 状态
				LastModified   string  `json:"lastModified"`   // 最后修改时间
				GoodsSpu       struct {
					ID        string `json:"id"`        // SPU ID
					Name      string `json:"name"`      // 名称
					BrandName string `json:"brandName"` // 品牌名称
					Material  string `json:"material"`  // 材质
				} `json:"goodsSpu"`
				GoodsSpec struct {
					Color string `json:"color"` // 颜色
					Spec  string `json:"spec"`  // 规格
				} `json:"goodsSpec"`
			} `json:"productInfo"`
		} `json:"prescriptionFormItems"`
	} `json:"prescriptionInfusionForms"`
	PrescriptionExternalForms []struct {
		ID                    string `json:"id"`            // 处方ID
		Type                  int    `json:"type"`          // 处方类型
		Specification         string `json:"specification"` // 规格
		DoseCount             int    `json:"doseCount"`     // 剂数
		DailyDosage           string `json:"dailyDosage"`   // 每日剂量
		Usage                 string `json:"usage"`         // 用法
		Freq                  string `json:"freq"`          // 频次
		Requirement           string `json:"requirement"`   // 要求
		UsageLevel            string `json:"usageLevel"`    // 用量
		PrescriptionFormItems []struct {
			ID                 string  `json:"id"`                 // 处方项ID
			PrescriptionFormID string  `json:"prescriptionFormId"` // 处方ID
			ProductID          string  `json:"productId"`          // 产品ID
			Type               int     `json:"type"`               // 类型
			SubType            int     `json:"subType"`            // 子类型
			Name               string  `json:"name"`               // 名称
			Specification      string  `json:"specification"`      // 规格
			Manufacturer       string  `json:"manufacturer"`       // 厂家
			Usage              string  `json:"usage"`              // 用法
			Ivgtt              float64 `json:"ivgtt"`              // 静滴
			IvgttUnit          string  `json:"ivgttUnit"`          // 静滴单位
			Freq               string  `json:"freq"`               // 频次
			Dosage             string  `json:"dosage"`             // 剂量
			DosageUnit         string  `json:"dosageUnit"`         // 剂量单位
			Days               int     `json:"days"`               // 天数
			SpecialRequirement string  `json:"specialRequirement"` // 特殊要求
			IsDismounting      int     `json:"isDismounting"`      // 是否拆零
			UnitCount          float64 `json:"unitCount"`          // 单位数量
			Unit               string  `json:"unit"`               // 单位
			UnitPrice          float64 `json:"unitPrice"`          // 单价
			GroupID            int     `json:"groupId"`            // 组ID
			IsAst              int     `json:"isAst"`              // 是否皮试
			AstResult          struct {
				Result      string `json:"result"`      // 结果
				Description string `json:"description"` // 描述
			} `json:"astResult"`
			ProductInfo struct {
				ID     string `json:"id"`   // 产品ID
				Name   string `json:"name"` // 名称
				Shebao struct {
					NationalCode   string `json:"nationalCode"` // 国标码
					InsuranceTypes []struct {
						InsuranceType      string  `json:"insuranceType"`      // 保险类型
						ReimbursementRatio float64 `json:"reimbursementRatio"` // 报销比例
					} `json:"insuranceTypes"`
				} `json:"shebao"`
				DisableSell    int     `json:"disableSell"`    // 是否禁用销售
				TypeID         int     `json:"typeId"`         // 类型ID
				TypeName       string  `json:"typeName"`       // 类型名称
				CustomTypeID   string  `json:"customTypeId"`   // 自定义类型ID
				CustomTypeName string  `json:"customTypeName"` // 自定义类型名称
				Manufacturer   string  `json:"manufacturer"`   // 厂家
				MedicineNmpn   string  `json:"medicineNmpn"`   // 药品批准文号
				BarCode        string  `json:"barCode"`        // 条码
				PieceUnit      string  `json:"pieceUnit"`      // 片剂单位
				PieceNum       float64 `json:"pieceNum"`       // 片剂数量
				PiecePrice     float64 `json:"piecePrice"`     // 片剂价格
				PackageUnit    string  `json:"packageUnit"`    // 包装单位
				PackagePrice   float64 `json:"packagePrice"`   // 包装价格
				ShortID        string  `json:"shortId"`        // 短ID
				Specification  string  `json:"specification"`  // 规格
				OtcType        int     `json:"otcType"`        // OTC类型
				Status         int     `json:"status"`         // 状态
				LastModified   string  `json:"lastModified"`   // 最后修改时间
				GoodsSpu       struct {
					ID        string `json:"id"`        // SPU ID
					Name      string `json:"name"`      // 名称
					BrandName string `json:"brandName"` // 品牌名称
					Material  string `json:"material"`  // 材质
				} `json:"goodsSpu"`
				GoodsSpec struct {
					Color string `json:"color"` // 颜色
					Spec  string `json:"spec"`  // 规格
				} `json:"goodsSpec"`
			} `json:"productInfo"`
		} `json:"prescriptionFormItems"`
	} `json:"prescriptionExternalForms"`
	ProductForms []struct {
		ID               string `json:"id"`   // 处方ID
		Type             int    `json:"type"` // 处方类型
		ProductFormItems []struct {
			ID            string  `json:"id"`            // 处方项ID
			ProductFormID string  `json:"productFormId"` // 处方ID
			ProductID     string  `json:"productId"`     // 产品ID
			Name          string  `json:"name"`          // 名称
			UnitCount     float64 `json:"unitCount"`     // 单位数量
			Unit          string  `json:"unit"`          // 单位
			UnitPrice     float64 `json:"unitPrice"`     // 单价
			IsDismounting int     `json:"isDismounting"` // 是否拆零
			Type          int     `json:"type"`          // 类型
			SubType       int     `json:"subType"`       // 子类型
			Days          int     `json:"days"`          // 天数
			DailyDosage   int     `json:"dailyDosage"`   // 每日剂量
			Remark        string  `json:"remark"`        // 备注
		} `json:"productFormItems"`
	} `json:"productForms"`
}
