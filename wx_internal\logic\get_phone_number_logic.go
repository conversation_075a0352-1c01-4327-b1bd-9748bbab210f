package logic

import (
	"context"
	"fmt"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/svc"
	"yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPhoneNumberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

func NewGetPhoneNumberLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *GetPhoneNumberLogic {
	return &GetPhoneNumberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetPhoneNumber 获取微信手机号并更新用户信息
func (l *GetPhoneNumberLogic) GetPhoneNumber(req *types.GetPhoneNumberReq) (resp *types.GetPhoneNumberResp, err error) {
	// 1. 从上下文获取openId
	openIdVal := l.ctx.Value(middleware.OpenIDKey)
	if openIdVal == nil {
		return nil, fmt.Errorf("openid不存在")
	}

	openId, ok := openIdVal.(string)
	if !ok || openId == "" {
		return nil, fmt.Errorf("无效的openid")
	}

	logx.Infof("获取用户手机号, openId: %s, code: %s", openId, req.Code)

	// 2. 获取手机号并更新用户信息
	phoneInfo, err := l.svcCtx.UserService.UpdatePhoneNumber(l.ctx, openId, req.Code)
	if err != nil {
		logx.Errorf("获取手机号失败: %v", err)
		return nil, err
	}

	// 3. 返回手机号
	return &types.GetPhoneNumberResp{
		PhoneNumber: phoneInfo.PhoneNumber,
	}, nil
}
