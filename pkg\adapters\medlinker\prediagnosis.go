package medlinker

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"
)

// 创建一个json实例
var jsonAPI = jsoniter.ConfigCompatibleWithStandardLibrary

// PreDiagnosisService 预问诊服务
type PreDiagnosisService struct {
	Client        *medlinkerClient
	KafkaProducer KafkaProducer
	KafkaTopic    string     // Kafka Topic名称
	Phone         string     // 添加电话号码用于认证
	wsLock        sync.Mutex // 保护WebSocket写入的互斥锁
}

// KafkaProducer Kafka生产者接口
type KafkaProducer interface {
	SendMessage(topic string, key, value []byte) (int32, int64, error)
}

// WSMessage WebSocket消息格式
type WSMessage struct {
	Type      string `json:"type"`      // chunk/full/error
	Content   string `json:"content"`   // 内容
	SessionID string `json:"sessionid"` // 会话ID
	IsFinal   bool   `json:"is_final"`  // 是否最终消息
}

// KafkaMessage Kafka消息格式
type KafkaMessage struct {
	SessionID  string `json:"sessionid"`
	MsgGroupID string `json:"msg_group_id"`
	ChunkIndex int    `json:"chunk_index"`
	Content    string `json:"content"`
	IsFinal    bool   `json:"is_final"`
	Timestamp  int64  `json:"timestamp"`
}

// MedResponseFrame 医联响应数据帧
type MedResponseFrame struct {
	Code       int    `json:"code"`
	Msg        string `json:"msg"`
	MsgGroupID string `json:"msgGroupId"`
	Tag        string `json:"tag"`
	Type       string `json:"type"`
	SessionID  string `json:"sessionid"`
	Data       struct {
		Answer []struct {
			Role       string              `json:"role"`
			Content    jsoniter.RawMessage `json:"content"` // 可能是字符串或对象
			ID         int                 `json:"id"`
			IsSysMsg   int                 `json:"isSysMsg"`
			MsgType    int                 `json:"msgType"`
			SwitchMode string              `json:"switchMode"`
			IsContinue int                 `json:"is_continue"`
		} `json:"answer"`
		Elapse int `json:"elapse"`
		Stage  int `json:"stage"`
	} `json:"data"`
}

// WSMessageHandler 是WebSocket消息处理接口
type WSMessageHandler interface {
	// SendMessage 发送WebSocket消息
	SendMessage(msgType string, content string, isFinal bool) error
}

// 添加会话监控
func (s *PreDiagnosisService) monitorSessionTimeout(ctx context.Context, cancel context.CancelFunc, sessionID string, timeoutSeconds int) {
	timer := time.NewTimer(time.Duration(timeoutSeconds) * time.Second)
	defer timer.Stop()

	select {
	case <-ctx.Done():
		// 上下文已取消，监控结束
		return
	case <-timer.C:
		// 会话超时
		logx.Infof("医联会话超时: SessionID=%s, 超时时间=%d秒", sessionID, timeoutSeconds)
		cancel() // 取消上下文
	}
}

// HandlePreDiagnosis 处理预问诊会话
func (s *PreDiagnosisService) HandlePreDiagnosis(conn *websocket.Conn, modelID int, content, sessionID string) error {
	// 创建可取消的上下文，用于超时控制
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动会话监控，设置3分钟超时
	go s.monitorSessionTimeout(ctx, cancel, sessionID, 180)

	// 记录开始时间
	startTime := time.Now()
	defer func() {
		elapsed := time.Since(startTime)
		logx.Infof("医联会话处理耗时: SessionID=%s, 耗时=%v", sessionID, elapsed)
	}()

	// 首先确保已登录
	if s.Client.token == "" {
		if s.Phone == "" {
			s.safeWriteJSON(conn, WSMessage{
				Type:      "error",
				Content:   "未配置电话号码，无法进行认证",
				SessionID: sessionID,
				IsFinal:   true,
			})
			return fmt.Errorf("未配置电话号码，无法进行认证")
		}

		// 发送登录信息到WebSocket
		s.safeWriteJSON(conn, WSMessage{
			Type:      "info",
			Content:   "正在进行医联认证...",
			SessionID: sessionID,
			IsFinal:   false,
		})

		// 调用登录接口
		if err := s.Client.Login_V2(s.Phone); err != nil {
			s.safeWriteJSON(conn, WSMessage{
				Type:      "error",
				Content:   fmt.Sprintf("医联认证失败: %v", err),
				SessionID: sessionID,
				IsFinal:   true,
			})
			return err
		}

		// 登录成功通知
		s.safeWriteJSON(conn, WSMessage{
			Type:      "info",
			Content:   "医联认证成功，开始会话...",
			SessionID: sessionID,
			IsFinal:   false,
		})
	}

	// 调用医联流式接口
	responseStream, err := s.Client.Chat(modelID, content, sessionID)
	if err != nil {
		// 发送错误消息到WebSocket
		s.safeWriteJSON(conn, WSMessage{
			Type:      "error",
			Content:   fmt.Sprintf("请求医联服务失败: %v", err),
			SessionID: sessionID,
			IsFinal:   true,
		})
		return err
	}
	defer responseStream.Close()

	// 记录请求参数
	logx.Infof("发送给医联的请求: ModelID=%d, SessionID=%s, Content=%s", modelID, sessionID, content)

	// 添加异常恢复逻辑
	defer func() {
		if r := recover(); r != nil {
			logx.Errorf("处理医联会话时发生异常: %v, SessionID=%s", r, sessionID)
			// 尝试发送错误消息通知前端
			recoveryMsg := WSMessage{
				Type:      "error",
				Content:   fmt.Sprintf("服务器内部错误: %v", r),
				SessionID: sessionID,
				IsFinal:   true,
			}
			_ = s.safeWriteJSON(conn, recoveryMsg)
		}
	}()

	// 分块读取响应数据
	scanner := bufio.NewScanner(responseStream)
	scanner.Split(func(data []byte, atEOF bool) (advance int, token []byte, err error) {
		if i := bytes.Index(data, []byte("|||")); i >= 0 {
			return i + 3, data[0:i], nil
		}
		if atEOF && len(data) > 0 {
			return len(data), data, nil
		}
		return 0, nil, nil
	})

	// 处理数据
	chunkIndex := 0
	lastActivityTime := time.Now()

	// 使用带超时的上下文处理会话数据
	dataChan := make(chan []byte, 100)
	errChan := make(chan error, 1)

	// 启动goroutine读取响应数据
	go func() {
		for scanner.Scan() {
			select {
			case <-ctx.Done():
				// 上下文已取消，停止读取
				return
			default:
				chunk := scanner.Bytes()
				if len(chunk) > 0 {
					dataChan <- chunk
					lastActivityTime = time.Now()
				}
			}
		}

		if err := scanner.Err(); err != nil {
			errChan <- err
		}
		close(dataChan)
	}()

	// 处理接收到的数据
	for {
		select {
		case <-ctx.Done():
			// 上下文已取消，会话被强制终止
			logx.Infof("医联会话被强制终止: SessionID=%s", sessionID)
			s.safeWriteJSON(conn, WSMessage{
				Type:      "error",
				Content:   "会话已超时，请重新开始",
				SessionID: sessionID,
				IsFinal:   true,
			})
			return fmt.Errorf("会话超时或被取消: %w", ctx.Err())

		case err := <-errChan:
			// 处理读取错误
			if err != nil {
				logx.Errorf("读取医联响应数据流出错: %v", err)
				s.safeWriteJSON(conn, WSMessage{
					Type:      "error",
					Content:   fmt.Sprintf("读取响应数据出错: %v", err),
					SessionID: sessionID,
					IsFinal:   true,
				})
				return err
			}

		case chunk, ok := <-dataChan:
			if !ok {
				// 数据通道已关闭，所有数据处理完毕
				logx.Infof("医联会话数据读取完成: SessionID=%s", sessionID)
				return nil
			}

			// 检查数据是否为空或只包含空白字符
			if len(bytes.TrimSpace(chunk)) == 0 {
				logx.Info("跳过空数据块")
				continue
			}

			// 解析响应数据
			var medResponse MedResponseFrame
			if err := jsonAPI.Unmarshal(chunk, &medResponse); err != nil {
				logx.Errorf("解析医联响应数据失败: %v, 数据: %s", err, string(chunk))
				continue
			}

			// 处理异常场景
			if medResponse.Code == 400 {
				logx.Infof("收到医联错误响应: code=%d, msg=%s, SessionID=%s",
					medResponse.Code, medResponse.Msg, sessionID)

				// 即使收到400错误，也按最终消息处理
				medResponse.Type = "full" // 强制设置为最终消息
				content = medResponse.Msg // 使用错误消息作为内容
			}

			// 记录医联原始返回的完整数据
			logx.Infof("医联原始响应数据: %s", string(chunk))

			// 检查非活动超时
			if time.Since(lastActivityTime) > 30*time.Second {
				logx.Infof("医联会话检测到长时间无活动: SessionID=%s, 最后活动时间=%v",
					sessionID, lastActivityTime.Format("15:04:05"))
			}

			// 提取内容
			answerMsgType := 0
			isFinal := medResponse.Type == "full"

			if len(medResponse.Data.Answer) > 0 {
				answer := medResponse.Data.Answer[0]
				answerMsgType = answer.MsgType

				// 根据消息类型解析内容
				if answerMsgType == 1 { // 普通文本
					if answer.Content != nil {
						content = string(bytes.Trim(answer.Content, `"`))
						content = strings.Replace(content, "\\", "", -1)
					}
				} else {
					// 其他类型的消息（如卡片）直接传递原始JSON
					content = string(chunk)
				}
			}

			// 构建WebSocket消息
			var wsType string
			if isFinal {
				// 对于最终消息，直接使用 "end" 类型，不再发送两条消息
				wsType = "end"
				// 如果内容为空，则添加结束语
				if content == "" {
					content = "会话已结束"
				}
			} else {
				wsType = "chunk"
			}

			// 发送数据到WebSocket
			wsMsg := WSMessage{
				Type:      wsType,
				Content:   content,
				SessionID: sessionID,
				IsFinal:   isFinal,
			}

			// 记录发送给WebSocket的消息内容
			wsJson, _ := jsonAPI.MarshalToString(wsMsg)
			logx.Infof("发送WebSocket消息: %s", wsJson)

			if err := s.safeWriteJSON(conn, wsMsg); err != nil {
				logx.Errorf("发送WebSocket消息失败: %v", err)
				return err
			}

			// 如果是最终消息，关闭连接并处理Kafka数据合并
			if isFinal {
				// 关闭WebSocket连接
				logx.Infof("关闭WebSocket连接: SessionID=%s", sessionID)
				if err := conn.Close(); err != nil {
					logx.Errorf("关闭WebSocket连接失败: %v", err)
				}

				// 合并Kafka数据到数据库
				if s.KafkaProducer != nil {
					// 发送合并消息到Kafka
					mergeMsg := KafkaMessage{
						SessionID:  sessionID,
						MsgGroupID: medResponse.MsgGroupID,
						ChunkIndex: -1,              // 使用-1表示合并消息
						Content:    "MERGE_SESSION", // 特殊标记表示需要合并
						IsFinal:    true,
						Timestamp:  time.Now().Unix(),
					}

					kafkaValue, err := jsonAPI.Marshal(mergeMsg)
					if err != nil {
						logx.Errorf("序列化合并消息失败: %v", err)
					} else {
						key := []byte(sessionID)
						_, _, err = s.KafkaProducer.SendMessage(s.KafkaTopic, key, kafkaValue)
						if err != nil {
							logx.Errorf("发送合并消息失败: %v", err)
						} else {
							logx.Infof("已发送合并消息到Topic[%s]: SessionID=%s", s.KafkaTopic, sessionID)
						}
					}
				}

				return nil
			}

			// 发送数据到Kafka
			if s.KafkaProducer != nil {
				kafkaMsg := KafkaMessage{
					SessionID:  sessionID,
					MsgGroupID: medResponse.MsgGroupID,
					ChunkIndex: chunkIndex,
					Content:    string(chunk),
					IsFinal:    isFinal,
					Timestamp:  time.Now().Unix(),
				}

				// 序列化消息
				kafkaValue, err := jsonAPI.Marshal(kafkaMsg)
				if err != nil {
					logx.Errorf("序列化Kafka消息失败: %v", err)
				} else {
					// 记录将要发送的Kafka消息内容
					logx.Infof("准备发送Kafka消息: SessionID=%s, ChunkIndex=%d, IsFinal=%v, 内容长度=%d字节",
						kafkaMsg.SessionID, kafkaMsg.ChunkIndex, kafkaMsg.IsFinal, len(kafkaMsg.Content))

					// 发送消息
					key := []byte(sessionID)
					_, _, err = s.KafkaProducer.SendMessage(s.KafkaTopic, key, kafkaValue)
					if err != nil {
						logx.Errorf("发送Kafka消息失败: %v", err)
					}
				}
			}

			chunkIndex++
		}
	}
}

// HandlePreDiagnosisWithHandler 使用消息处理器处理预问诊会话
func (s *PreDiagnosisService) HandlePreDiagnosisWithHandler(handler WSMessageHandler, modelID int, content, sessionID string) error {
	// 创建可取消的上下文，用于超时控制
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动会话监控，设置3分钟超时
	go s.monitorSessionTimeout(ctx, cancel, sessionID, 180)

	// 记录开始时间
	startTime := time.Now()
	defer func() {
		elapsed := time.Since(startTime)
		logx.Infof("医联会话处理耗时: SessionID=%s, 耗时=%v", sessionID, elapsed)
	}()

	// 首先确保已登录
	if s.Client.token == "" {
		if s.Phone == "" {
			handler.SendMessage("error", "未配置电话号码，无法进行认证", true)
			return fmt.Errorf("未配置电话号码，无法进行认证")
		}

		// 发送登录信息到WebSocket
		handler.SendMessage("info", "正在进行医联认证...", false)

		// 调用登录接口
		if err := s.Client.Login_V2(s.Phone); err != nil {
			handler.SendMessage("error", fmt.Sprintf("医联认证失败: %v", err), true)
			return err
		}

		// 登录成功通知
		handler.SendMessage("info", "医联认证成功，开始会话...", false)
	}

	// 调用医联流式接口
	responseStream, err := s.Client.Chat(modelID, content, sessionID)
	if err != nil {
		// 发送错误消息到WebSocket
		handler.SendMessage("error", fmt.Sprintf("请求医联服务失败: %v", err), true)
		return err
	}
	defer responseStream.Close()

	// 记录请求参数
	logx.Infof("发送给医联的请求: ModelID=%d, SessionID=%s, Content=%s", modelID, sessionID, content)

	// 添加异常恢复逻辑
	defer func() {
		if r := recover(); r != nil {
			logx.Errorf("处理医联会话时发生异常: %v, SessionID=%s", r, sessionID)
			// 尝试发送错误消息通知前端
			handler.SendMessage("error", fmt.Sprintf("服务器内部错误: %v", r), true)
		}
	}()

	// 分块读取响应数据
	scanner := bufio.NewScanner(responseStream)
	scanner.Split(func(data []byte, atEOF bool) (advance int, token []byte, err error) {
		if i := bytes.Index(data, []byte("|||")); i >= 0 {
			return i + 3, data[0:i], nil
		}
		if atEOF && len(data) > 0 {
			return len(data), data, nil
		}
		return 0, nil, nil
	})

	// 处理数据
	chunkIndex := 0
	lastActivityTime := time.Now()

	// 使用带超时的上下文处理会话数据
	dataChan := make(chan []byte, 100)
	errChan := make(chan error, 1)

	// 启动goroutine读取响应数据
	go func() {
		for scanner.Scan() {
			select {
			case <-ctx.Done():
				// 上下文已取消，停止读取
				return
			default:
				chunk := scanner.Bytes()
				if len(chunk) > 0 {
					dataChan <- chunk
					lastActivityTime = time.Now()
				}
			}
		}

		if err := scanner.Err(); err != nil {
			errChan <- err
		}
		close(dataChan)
	}()

	// 处理接收到的数据
	for {
		select {
		case <-ctx.Done():
			// 上下文已取消，会话被强制终止
			logx.Infof("医联会话被强制终止: SessionID=%s", sessionID)
			handler.SendMessage("error", "会话已超时，请重新开始", true)
			return fmt.Errorf("会话超时或被取消: %w", ctx.Err())

		case err := <-errChan:
			// 处理读取错误
			if err != nil {
				logx.Errorf("读取医联响应数据流出错: %v", err)
				handler.SendMessage("error", fmt.Sprintf("读取响应数据出错: %v", err), true)
				return err
			}

		case chunk, ok := <-dataChan:
			if !ok {
				// 数据通道已关闭，所有数据处理完毕
				logx.Infof("医联会话数据读取完成: SessionID=%s", sessionID)
				return nil
			}

			// 检查数据是否为空或只包含空白字符
			if len(bytes.TrimSpace(chunk)) == 0 {
				logx.Info("跳过空数据块")
				continue
			}

			// 解析响应数据
			var medResponse MedResponseFrame
			if err := jsonAPI.Unmarshal(chunk, &medResponse); err != nil {
				logx.Errorf("解析医联响应数据失败: %v, 数据: %s", err, string(chunk))
				continue
			}

			// 处理异常场景
			if medResponse.Code == 400 {
				logx.Infof("收到医联错误响应: code=%d, msg=%s, SessionID=%s",
					medResponse.Code, medResponse.Msg, sessionID)

				// 即使收到400错误，也按最终消息处理
				medResponse.Type = "full" // 强制设置为最终消息
				content = medResponse.Msg // 使用错误消息作为内容
			}

			// 记录医联原始返回的完整数据
			logx.Infof("医联原始响应数据: %s", string(chunk))

			// 检查非活动超时
			if time.Since(lastActivityTime) > 30*time.Second {
				logx.Infof("医联会话检测到长时间无活动: SessionID=%s, 最后活动时间=%v",
					sessionID, lastActivityTime.Format("15:04:05"))
			}

			// 提取内容
			answerMsgType := 0
			isFinal := medResponse.Type == "full"

			if len(medResponse.Data.Answer) > 0 {
				answer := medResponse.Data.Answer[0]
				answerMsgType = answer.MsgType

				// 根据消息类型解析内容
				if answerMsgType == 1 { // 普通文本
					if answer.Content != nil {
						content = string(bytes.Trim(answer.Content, `"`))
						content = strings.Replace(content, "\\", "", -1)
					}
				} else {
					// 其他类型的消息（如卡片）直接传递原始JSON
					content = string(chunk)
				}
			}

			// 构建WebSocket消息
			var wsType string
			if isFinal {
				// 对于最终消息，直接使用 "end" 类型，不再发送两条消息
				wsType = "end"
				// 如果内容为空，则添加结束语
				if content == "" {
					content = "会话已结束"
				}
			} else {
				wsType = "chunk"
			}

			// 通过消息处理器发送消息
			if err := handler.SendMessage(wsType, content, isFinal); err != nil {
				logx.Errorf("发送消息失败: %v", err)
				return err
			}

			// 如果是最终消息，关闭连接并处理Kafka数据合并
			if isFinal {
				// 在客户端处理完毕后通知
				logx.Infof("处理最终消息: SessionID=%s", sessionID)

				// 合并Kafka数据到数据库
				if s.KafkaProducer != nil {
					// 发送合并消息到Kafka
					mergeMsg := KafkaMessage{
						SessionID:  sessionID,
						MsgGroupID: medResponse.MsgGroupID,
						ChunkIndex: -1,              // 使用-1表示合并消息
						Content:    "MERGE_SESSION", // 特殊标记表示需要合并
						IsFinal:    true,
						Timestamp:  time.Now().Unix(),
					}

					kafkaValue, err := jsonAPI.Marshal(mergeMsg)
					if err != nil {
						logx.Errorf("序列化合并消息失败: %v", err)
					} else {
						key := []byte(sessionID)
						_, _, err = s.KafkaProducer.SendMessage(s.KafkaTopic, key, kafkaValue)
						if err != nil {
							logx.Errorf("发送合并消息失败: %v", err)
						} else {
							logx.Infof("已发送合并消息到Topic[%s]: SessionID=%s", s.KafkaTopic, sessionID)
						}
					}
				}

				return nil
			}

			// 记录数据到Kafka
			if s.KafkaProducer != nil {
				kafkaMsg := KafkaMessage{
					SessionID:  sessionID,
					MsgGroupID: medResponse.MsgGroupID,
					ChunkIndex: chunkIndex,
					Content:    string(chunk),
					IsFinal:    isFinal,
					Timestamp:  time.Now().Unix(),
				}

				// 序列化消息
				kafkaValue, err := jsonAPI.Marshal(kafkaMsg)
				if err != nil {
					logx.Errorf("序列化Kafka消息失败: %v", err)
				} else {
					// 记录将要发送的Kafka消息内容
					logx.Infof("准备发送Kafka消息: SessionID=%s, ChunkIndex=%d, IsFinal=%v, 内容长度=%d字节",
						kafkaMsg.SessionID, kafkaMsg.ChunkIndex, kafkaMsg.IsFinal, len(kafkaMsg.Content))

					// 发送消息
					key := []byte(sessionID)
					_, _, err = s.KafkaProducer.SendMessage(s.KafkaTopic, key, kafkaValue)
					if err != nil {
						logx.Errorf("发送Kafka消息失败: %v", err)
					}
				}
			}

			// 更新计数器
			chunkIndex++
		}
	}
}

// safeWriteJSON 安全地向WebSocket连接写入JSON数据
func (s *PreDiagnosisService) safeWriteJSON(conn *websocket.Conn, v interface{}) error {
	s.wsLock.Lock()
	defer s.wsLock.Unlock()
	return conn.WriteJSON(v)
}

// NewPreDiagnosisService 创建预问诊服务
func NewPreDiagnosisService(client *medlinkerClient, kafkaProducer KafkaProducer, kafkaTopic string, phone string) *PreDiagnosisService {
	return &PreDiagnosisService{
		Client:        client,
		KafkaProducer: kafkaProducer,
		KafkaTopic:    kafkaTopic,
		Phone:         phone, // 从配置传入电话号码
	}
}
