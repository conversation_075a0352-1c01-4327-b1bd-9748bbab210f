package bootstrap

import (
	"time"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// InvitationRecord 邀请记录表
type InvitationRecord struct {
	ID             uint       `gorm:"primaryKey;autoIncrement;comment:邀请记录ID"`
	InviterID      uint       `gorm:"not null;index:idx_invitation_inviter;comment:邀请人用户ID"`
	InviteeID      uint       `gorm:"index:idx_invitation_invitee;comment:被邀请人用户ID"`
	InvitationCode string     `gorm:"type:varchar(32);not null;uniqueIndex;comment:邀请码"`
	Status         int        `gorm:"default:0;comment:邀请状态(0待完成,1已完成,2已失效)"`
	InviteePhone   string     `gorm:"type:varchar(20);comment:被邀请人手机号"`
	InviteeName    string     `gorm:"type:varchar(50);comment:被邀请人姓名"`
	CoinsAwarded   int        `gorm:"default:0;comment:奖励叶小币数量"`
	RewardType     string     `gorm:"type:varchar(50);comment:奖励类型"`
	CompletedAt    *time.Time `gorm:"comment:完成时间"`
	CreatedAt      time.Time  `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt      time.Time  `gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (InvitationRecord) TableName() string {
	return "invitation_records"
}

// InvitationRewardLog 邀请奖励日志表
type InvitationRewardLog struct {
	ID            uint      `gorm:"primaryKey;autoIncrement;comment:奖励日志ID"`
	InvitationID  uint      `gorm:"not null;index;comment:邀请记录ID"`
	InviterID     uint      `gorm:"not null;index;comment:邀请人用户ID"`
	InviteeID     uint      `gorm:"not null;index;comment:被邀请人用户ID"`
	RewardType    string    `gorm:"type:varchar(50);not null;comment:奖励类型"`
	CoinsAwarded  int       `gorm:"not null;comment:奖励叶小币数量"`
	RuleID        uint      `gorm:"comment:规则ID"`
	TransactionID uint      `gorm:"comment:交易记录ID"`
	Status        int       `gorm:"default:1;comment:状态(1已发放,0待发放,-1发放失败)"`
	CreatedAt     time.Time `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (InvitationRewardLog) TableName() string {
	return "invitation_reward_logs"
}

// InvitationConfig 邀请配置表
type InvitationConfig struct {
	ID                   uint      `gorm:"primaryKey;autoIncrement;comment:配置ID"`
	InvitationEnabled    bool      `gorm:"default:true;comment:是否启用邀请功能"`
	MaxInvitationsPerDay int       `gorm:"default:10;comment:每日最大邀请数"`
	InvitationExpireDays int       `gorm:"default:30;comment:邀请链接有效期(天)"`
	ShareTitle           string    `gorm:"type:varchar(100);comment:分享标题"`
	ShareDescription     string    `gorm:"type:text;comment:分享描述"`
	CreatedAt            time.Time `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt            time.Time `gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (InvitationConfig) TableName() string {
	return "invitation_config"
}

// MigrateInvitationTables 迁移邀请相关表
func MigrateInvitationTables() error {
	logx.Info("开始迁移邀请相关表...")

	// 获取数据库连接
	db := mysql.Master()

	// 创建邀请记录表
	if err := db.AutoMigrate(&InvitationRecord{}); err != nil {
		logx.Errorf("创建邀请记录表失败: %v", err)
		return err
	}

	// 创建邀请奖励日志表
	if err := db.AutoMigrate(&InvitationRewardLog{}); err != nil {
		logx.Errorf("创建邀请奖励日志表失败: %v", err)
		return err
	}

	// 创建邀请配置表
	if err := db.AutoMigrate(&InvitationConfig{}); err != nil {
		logx.Errorf("创建邀请配置表失败: %v", err)
		return err
	}

	// 插入默认邀请配置
	var configCount int64
	db.Model(&InvitationConfig{}).Count(&configCount)
	if configCount == 0 {
		defaultConfig := &InvitationConfig{
			InvitationEnabled:    true,
			MaxInvitationsPerDay: 10,
			InvitationExpireDays: 30,
			ShareTitle:           "叶开泰邀请您加入",
			ShareDescription:     "注册即可获得叶小币奖励，快来加入我们吧！",
		}
		if err := db.Create(defaultConfig).Error; err != nil {
			logx.Errorf("创建默认邀请配置失败: %v", err)
			return err
		}
		logx.Info("创建默认邀请配置成功")
	}

	// 添加邀请相关的叶小币规则
	invitationRules := []map[string]interface{}{
		{
			"user_level_id": 1,
			"rule_type":     "INVITATION_REGISTER",
			"rule_name":     "邀请新人注册",
			"description":   "邀请新用户注册成功获得叶小币奖励",
			"enabled":       true,
			"coins_awarded": 200,
			"is_one_time":   true,
			"activity_type": "INVITATION_REGISTER",
		},
		{
			"user_level_id": 1,
			"rule_type":     "INVITATION_ORDER",
			"rule_name":     "邀请新人首单",
			"description":   "被邀请人完成首单获得叶小币奖励",
			"enabled":       true,
			"coins_awarded": 50,
			"is_one_time":   true,
			"activity_type": "INVITATION_ORDER",
		},
	}

	for _, rule := range invitationRules {
		// 检查规则是否已存在
		var count int64
		db.Table("coin_rules").Where("user_level_id = ? AND rule_type = ?",
			rule["user_level_id"], rule["rule_type"]).Count(&count)

		if count == 0 {
			rule["created_at"] = time.Now()
			rule["updated_at"] = time.Now()

			if err := db.Table("coin_rules").Create(rule).Error; err != nil {
				logx.Errorf("创建邀请奖励规则失败: %v", err)
				return err
			}
			logx.Infof("创建邀请奖励规则成功: %s", rule["rule_name"])
		}
	}

	// 为其他用户等级添加邀请规则
	var userLevels []struct {
		ID uint `json:"id"`
	}
	db.Table("user_level_rules").Where("id > 1").Find(&userLevels)

	for _, level := range userLevels {
		for _, baseRule := range invitationRules {
			// 检查规则是否已存在
			var count int64
			db.Table("coin_rules").Where("user_level_id = ? AND rule_type = ?",
				level.ID, baseRule["rule_type"]).Count(&count)

			if count == 0 {
				rule := make(map[string]interface{})
				for k, v := range baseRule {
					rule[k] = v
				}
				rule["user_level_id"] = level.ID
				rule["created_at"] = time.Now()
				rule["updated_at"] = time.Now()

				if err := db.Table("coin_rules").Create(rule).Error; err != nil {
					logx.Errorf("为用户等级%d创建邀请奖励规则失败: %v", level.ID, err)
				} else {
					logx.Infof("为用户等级%d创建邀请奖励规则成功: %s", level.ID, rule["rule_name"])
				}
			}
		}
	}

	logx.Info("邀请相关表迁移完成")
	return nil
}
