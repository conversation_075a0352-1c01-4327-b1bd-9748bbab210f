package routes

import (
	"net/http"
	"yekaitai/internal/modules/tag/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterTagRoutes 注册标签管理相关路由
func RegisterTagRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建标签管理处理器
	tagHandler := handler.NewTagHandler(serverCtx)

	// 添加标签管理路由（外层已有认证中间件，无需重复添加）
	server.AddRoutes(
		[]rest.Route{
			// 标签列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/tags",
				Handler: tagHandler.ListTags,
			},
			// 标签详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/tags/:id",
				Handler: tagHandler.GetTag,
			},
			// 创建标签
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/tags",
				Handler: tagHandler.CreateTag,
			},
			// 更新标签
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/tags/:id",
				Handler: tagHandler.UpdateTag,
			},
			// 删除标签
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/tags/:id",
				Handler: tagHandler.DeleteTag,
			},
		},
	)
}
