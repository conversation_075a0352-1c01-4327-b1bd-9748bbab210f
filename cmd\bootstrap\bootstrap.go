package bootstrap

import "fmt"

// "fmt"

// Bootstrap 执行所有初始化操作
func Bootstrap() error {
	// 执行RBAC表结构迁移
	// if err := InitDefaultAdmin(); err != nil {
	// 	return fmt.Errorf("初始化管理员: %w", err)
	// }
	// 执行RBAC表结构迁移
	if err := MigrateRBACTables(); err != nil {
		return fmt.Errorf("RBAC表结构迁移失败: %w", err)
	}

	// 执行地区表结构迁移
	if err := MigrateRegionTables(); err != nil {
		return fmt.Errorf("地区表结构迁移失败: %w", err)
	}

	// 执行区域管理模块表结构迁移
	if err := MigrateAreaTables(); err != nil {
		return fmt.Errorf("区域管理模块表结构迁移失败: %w", err)
	}

	// 执行门店表结构迁移
	if err := MigrateStoreTables(); err != nil {
		return fmt.Errorf("门店表结构迁移失败: %w", err)
	}

	// 执行患者管理模块表结构迁移
	if err := MigratePatientTables(); err != nil {
		return fmt.Errorf("患者管理模块表结构迁移失败: %w", err)
	}

	// 执行医生管理模块表结构迁移
	if err := MigrateDoctorTables(); err != nil {
		return fmt.Errorf("医生管理模块表结构迁移失败: %w", err)
	}

	// 执行疾病库表结构迁移
	if err := MigrateDiseaseTables(); err != nil {
		return fmt.Errorf("疾病库表结构迁移失败: %w", err)
	}

	// 执行医生推荐表结构迁移
	if err := MigrateDoctorRecommendTables(); err != nil {
		return fmt.Errorf("医生推荐表结构迁移失败: %w", err)
	}

	// 执行内容管理模块表结构迁移
	if err := MigrateContentTables(); err != nil {
		return fmt.Errorf("内容管理模块表结构迁移失败: %w", err)
	}

	// 为内容表添加报名费用字段
	if err := AddSignUpAmountField(); err != nil {
		return fmt.Errorf("添加报名费用字段失败: %w", err)
	}

	// 执行标签管理模块表结构迁移
	if err := MigrateTagTables(); err != nil {
		return fmt.Errorf("标签管理模块表结构迁移失败: %w", err)
	}

	// 执行医疗聊天模块表结构迁移
	if err := MigrateMedChat(); err != nil {
		return fmt.Errorf("医疗聊天模块表结构迁移失败: %w", err)
	}

	// 执行聚水潭模块表结构迁移-弃用
	// if err := MigrateJushuitanTables(); err != nil {
	// 	return fmt.Errorf("聚水潭模块表结构迁移失败: %w", err)
	// }

	// 执行医生排班号源模块表结构迁移
	if err := MigrateScheduleTables(); err != nil {
		return fmt.Errorf("医生排班号源模块表结构迁移失败: %w", err)
	}

	// 执行预约挂号相关表结构迁移
	if err := MigrateRegistrationTables(); err != nil {
		return fmt.Errorf("预约挂号相关表结构迁移失败: %w", err)
	}

	// 执行同步进度表结构迁移
	if err := MigrateSyncProgress(); err != nil {
		return fmt.Errorf("同步进度表结构迁移失败: %w", err)
	}

	// 执行商品管理模块表结构迁移
	if err := MigrateGoodsTables(); err != nil {
		return fmt.Errorf("商品管理模块表结构迁移失败: %w", err)
	}

	// 执行运费配置模块表结构迁移
	if err := MigrateShippingTables(); err != nil {
		return fmt.Errorf("运费配置模块表结构迁移失败: %w", err)
	}

	// 执行订单模块表结构迁移
	if err := MigrateOrderTables(); err != nil {
		return fmt.Errorf("订单模块表结构迁移失败: %w", err)
	}

	// 执行店铺配置模块表结构迁移
	if err := MigrateStoreConfigTables(); err != nil {
		return fmt.Errorf("店铺配置模块表结构迁移失败: %w", err)
	}

	// 执行购物车模块表结构迁移
	if err := MigrateCartTables(); err != nil {
		return fmt.Errorf("购物车模块表结构迁移失败: %w", err)
	}

	// 执行用户地址模块表结构迁移
	if err := MigrateAddressTables(); err != nil {
		return fmt.Errorf("用户地址模块表结构迁移失败: %w", err)
	}

	// 执行优惠券模块表结构迁移
	if err := MigrateCouponTables(); err != nil {
		return fmt.Errorf("优惠券模块表结构迁移失败: %w", err)
	}

	// 执行优惠券发放任务表结构迁移
	if err := CouponIssueTaskMigration(); err != nil {
		return fmt.Errorf("优惠券发放任务表结构迁移失败: %w", err)
	}

	// 执行用户等级管理模块表结构迁移
	if err := MigrateUserLevelTables(); err != nil {
		return fmt.Errorf("用户等级管理模块表结构迁移失败: %w", err)
	}

	// 执行叶小币管理模块表结构迁移
	if err := CoinRulesMigration(); err != nil {
		return fmt.Errorf("叶小币管理模块表结构迁移失败: %w", err)
	}

	// 执行积分兑换模块表结构迁移
	if err := ExchangeConfigMigration(); err != nil {
		return fmt.Errorf("积分兑换模块表结构迁移失败: %w", err)
	}

	// 执行服务套餐标签管理模块表结构迁移
	if err := MigrateServicePackageTables(); err != nil {
		return fmt.Errorf("服务套餐标签管理模块表结构迁移失败: %w", err)
	}

	// 执行服务模块表结构迁移（包含服务订单表）
	MigrateServiceTables()

	// 执行推荐服务套餐表结构迁移
	if err := MigrateServicePackageRecommendTables(); err != nil {
		return fmt.Errorf("推荐服务套餐表结构迁移失败: %w", err)
	}

	// 执行服务评价表结构迁移
	if err := MigrateServiceEvaluationTables(); err != nil {
		return fmt.Errorf("服务评价表结构迁移失败: %w", err)
	}

	// 执行积分过期策略迁移
	RunPointsExpiryPolicyMigration()

	// 创建优惠券可见用户等级中间表
	MigrateCouponVisibleUserLevels()

	// 执行短信模板模块表结构迁移
	if err := MigrateSmsTemplateTables(); err != nil {
		return fmt.Errorf("短信模板模块表结构迁移失败: %w", err)
	}

	// 执行短信验证码模块表结构迁移
	if err := MigrateSmsCodeTables(); err != nil {
		return fmt.Errorf("短信验证码模块表结构迁移失败: %w", err)
	}

	// 执行AI知识库模块表结构迁移
	if err := MigrateAIKnowledgeBaseTables(); err != nil {
		return fmt.Errorf("AI知识库模块表结构迁移失败: %w", err)
	}

	// 执行签到模块表结构迁移
	if err := MigrateCheckinTables(); err != nil {
		return fmt.Errorf("签到模块表结构迁移失败: %w", err)
	}

	// 执行邀请模块表结构迁移
	if err := MigrateInvitationTables(); err != nil {
		return fmt.Errorf("邀请模块表结构迁移失败: %w", err)
	}

	// 初始化基础数据(手动执行250507)
	// 初始化RBAC基础数据，通过SQL文件执行
	// if err := InitRBACData(); err != nil {
	// 	return fmt.Errorf("初始化RBAC基础数据失败: %w", err)
	// }

	// 执行微信小程序相关迁移
	if err := MigrateWxAll(); err != nil {
		return err
	}

	// 初始化管理员账号
	if err := InitDefaultAdmin(); err != nil {
		return err
	}

	// 执行管理员操作日志表迁移
	if err := MigrateAdminOperationLog(); err != nil {
		return fmt.Errorf("管理员操作日志表迁移失败: %w", err)
	}

	if err := WxUserAnnualConsumptionMigration(); err != nil {
		return err
	}

	return nil
}
