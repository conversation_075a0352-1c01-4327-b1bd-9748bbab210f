package tasks

import (
	"context"
	"fmt"
	"time"

	"yekaitai/internal/modules/content/model"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/activity/service"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// ActivityTaskService 活动任务服务
type ActivityTaskService struct {
	db            *gorm.DB
	refundService *service.ActivityRefundService
	cronService   *service.ActivityCronService
}

// NewActivityTaskService 创建活动任务服务
func NewActivityTaskService() *ActivityTaskService {
	return &ActivityTaskService{
		db:            mysql.Master(),
		refundService: service.NewActivityRefundService(),
		cronService:   service.NewActivityCronService(),
	}
}

// ProcessExpiredActivities 处理过期活动（定时任务）
func (s *ActivityTaskService) ProcessExpiredActivities() error {
	logx.Info("开始执行过期活动处理任务")
	startTime := time.Now()

	ctx := context.Background()
	err := s.refundService.RefundExpiredOrders(ctx)
	if err != nil {
		logx.Errorf("处理过期活动失败: %v", err)
		return fmt.Errorf("处理过期活动失败: %w", err)
	}

	duration := time.Since(startTime)
	logx.Infof("过期活动处理任务完成，耗时: %v", duration)
	return nil
}

// UpdateActivitySignUpCounts 更新活动报名人数（定时任务）
func (s *ActivityTaskService) UpdateActivitySignUpCounts() error {
	logx.Info("开始执行活动报名人数更新任务")
	startTime := time.Now()

	// 获取所有启用的活动
	var activities []model.Content
	err := s.db.Where("type = ? AND is_enabled = ?", "activity", true).Find(&activities).Error
	if err != nil {
		logx.Errorf("查询活动列表失败: %v", err)
		return fmt.Errorf("查询活动列表失败: %w", err)
	}

	successCount := 0
	for _, activity := range activities {
		if err := s.updateSingleActivitySignUpCount(activity.ID); err != nil {
			logx.Errorf("更新活动报名人数失败: activityID=%d, error=%v", activity.ID, err)
			continue
		}
		successCount++
	}

	duration := time.Since(startTime)
	logx.Infof("活动报名人数更新任务完成，成功: %d/%d，耗时: %v", successCount, len(activities), duration)
	return nil
}

// updateSingleActivitySignUpCount 更新单个活动的报名人数
func (s *ActivityTaskService) updateSingleActivitySignUpCount(activityID uint) error {
	// 统计已报名和已核销的人数
	var count int64
	err := s.db.Model(&model.ContentSignUpOrder{}).
		Where("content_id = ? AND status IN (1, 4)", activityID).
		Count(&count).Error
	if err != nil {
		return fmt.Errorf("统计报名人数失败: %w", err)
	}

	// 更新活动的报名人数
	err = s.db.Model(&model.Content{}).
		Where("id = ?", activityID).
		Update("sign_up_count", count).Error
	if err != nil {
		return fmt.Errorf("更新报名人数失败: %w", err)
	}

	logx.Infof("更新活动报名人数成功: activityID=%d, count=%d", activityID, count)
	return nil
}

// BatchRefundExpiredOrders 批量退款过期订单（手动任务）
func (s *ActivityTaskService) BatchRefundExpiredOrders(batchSize int) error {
	logx.Infof("开始执行批量退款过期订单任务，批次大小: %d", batchSize)
	startTime := time.Now()

	ctx := context.Background()
	err := s.refundService.BatchRefundExpiredOrders(ctx, batchSize)
	if err != nil {
		logx.Errorf("批量退款过期订单失败: %v", err)
		return fmt.Errorf("批量退款过期订单失败: %w", err)
	}

	duration := time.Since(startTime)
	logx.Infof("批量退款过期订单任务完成，耗时: %v", duration)
	return nil
}

// CleanupExpiredQRCodes 清理过期二维码（定时任务）
func (s *ActivityTaskService) CleanupExpiredQRCodes() error {
	logx.Info("开始执行过期二维码清理任务")
	startTime := time.Now()

	// 清理7天前的已核销或已取消订单的二维码URL
	cutoffTime := time.Now().AddDate(0, 0, -7) // 7天前
	
	result := s.db.Model(&model.ContentSignUpOrder{}).
		Where("status IN (2, 3, 4) AND updated_at < ? AND qr_code_url != ''", cutoffTime).
		Update("qr_code_url", "")

	if result.Error != nil {
		logx.Errorf("清理过期二维码失败: %v", result.Error)
		return fmt.Errorf("清理过期二维码失败: %w", result.Error)
	}

	duration := time.Since(startTime)
	logx.Infof("过期二维码清理任务完成，清理数量: %d，耗时: %v", result.RowsAffected, duration)
	return nil
}

// GenerateActivityStatistics 生成活动统计报告（手动任务）
func (s *ActivityTaskService) GenerateActivityStatistics() error {
	logx.Info("开始生成活动统计报告")
	startTime := time.Now()

	// 统计总体数据
	var totalActivities int64
	s.db.Model(&model.Content{}).Where("type = ?", "activity").Count(&totalActivities)

	var enabledActivities int64
	s.db.Model(&model.Content{}).Where("type = ? AND is_enabled = ?", "activity", true).Count(&enabledActivities)

	var totalOrders int64
	s.db.Model(&model.ContentSignUpOrder{}).Count(&totalOrders)

	var paidOrders int64
	s.db.Model(&model.ContentSignUpOrder{}).Where("status IN (1, 4)").Count(&paidOrders)

	var cancelledOrders int64
	s.db.Model(&model.ContentSignUpOrder{}).Where("status IN (2, 3)").Count(&cancelledOrders)

	var verifiedOrders int64
	s.db.Model(&model.ContentSignUpOrder{}).Where("status = 4").Count(&verifiedOrders)

	// 统计本月数据
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	
	var monthlyOrders int64
	s.db.Model(&model.ContentSignUpOrder{}).Where("created_at >= ?", monthStart).Count(&monthlyOrders)

	var monthlyRevenue int64
	s.db.Model(&model.ContentSignUpOrder{}).
		Where("created_at >= ? AND status IN (1, 4)", monthStart).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&monthlyRevenue)

	duration := time.Since(startTime)

	// 输出统计报告
	fmt.Println("==================== 活动统计报告 ====================")
	fmt.Printf("生成时间: %s\n", now.Format("2006-01-02 15:04:05"))
	fmt.Printf("统计耗时: %v\n", duration)
	fmt.Println("------------------------------------------------------")
	fmt.Printf("活动总数: %d\n", totalActivities)
	fmt.Printf("启用活动: %d\n", enabledActivities)
	fmt.Printf("报名总数: %d\n", totalOrders)
	fmt.Printf("有效报名: %d (已报名+已核销)\n", paidOrders)
	fmt.Printf("取消报名: %d\n", cancelledOrders)
	fmt.Printf("已核销数: %d\n", verifiedOrders)
	fmt.Println("------------------------------------------------------")
	fmt.Printf("本月报名: %d\n", monthlyOrders)
	fmt.Printf("本月收入: %.2f 元\n", float64(monthlyRevenue)/100)
	fmt.Println("======================================================")

	logx.Infof("活动统计报告生成完成，耗时: %v", duration)
	return nil
}

// FixActivityData 修复活动数据（手动任务）
func (s *ActivityTaskService) FixActivityData() error {
	logx.Info("开始执行活动数据修复任务")
	startTime := time.Now()

	// 修复活动报名人数
	if err := s.fixActivitySignUpCounts(); err != nil {
		return fmt.Errorf("修复活动报名人数失败: %w", err)
	}

	// 修复订单状态
	if err := s.fixOrderStatus(); err != nil {
		return fmt.Errorf("修复订单状态失败: %w", err)
	}

	duration := time.Since(startTime)
	logx.Infof("活动数据修复任务完成，耗时: %v", duration)
	return nil
}

// fixActivitySignUpCounts 修复活动报名人数
func (s *ActivityTaskService) fixActivitySignUpCounts() error {
	logx.Info("开始修复活动报名人数")

	// 获取所有活动
	var activities []model.Content
	err := s.db.Where("type = ?", "activity").Find(&activities).Error
	if err != nil {
		return fmt.Errorf("查询活动列表失败: %w", err)
	}

	successCount := 0
	for _, activity := range activities {
		if err := s.updateSingleActivitySignUpCount(activity.ID); err != nil {
			logx.Errorf("修复活动报名人数失败: activityID=%d, error=%v", activity.ID, err)
			continue
		}
		successCount++
	}

	logx.Infof("活动报名人数修复完成，成功: %d/%d", successCount, len(activities))
	return nil
}

// fixOrderStatus 修复订单状态
func (s *ActivityTaskService) fixOrderStatus() error {
	logx.Info("开始修复订单状态")

	// 这里可以添加具体的订单状态修复逻辑
	// 例如：修复异常状态的订单、补充缺失的核销码等

	logx.Info("订单状态修复完成")
	return nil
}

// GetTaskStatistics 获取任务统计信息
func (s *ActivityTaskService) GetTaskStatistics() map[string]interface{} {
	return map[string]interface{}{
		"service_name":    "ActivityTaskService",
		"available_tasks": []string{
			"activity_expired_check",     // 过期活动检查
			"activity_signup_update",     // 报名人数更新
			"activity_batch_refund",      // 批量退款
			"activity_qrcode_cleanup",    // 二维码清理
			"activity_statistics",        // 统计报告
			"activity_data_fix",          // 数据修复
		},
		"last_run_time": time.Now().Format("2006-01-02 15:04:05"),
	}
}
