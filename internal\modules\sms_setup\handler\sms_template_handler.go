package handler

import (
	"net/http"
	"strconv"

	"yekaitai/internal/modules/sms_setup/model"
	"yekaitai/internal/modules/sms_setup/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// SmsTemplateHandler 短信模板处理器
type SmsTemplateHandler struct {
	smsTemplateService service.SmsTemplateService
}

// NewSmsTemplateHandler 创建短信模板处理器
func NewSmsTemplateHandler() *SmsTemplateHandler {
	return &SmsTemplateHandler{
		smsTemplateService: service.NewSmsTemplateService(),
	}
}

// CreateTemplate 创建短信模板
func (h *SmsTemplateHandler) CreateTemplate(w http.ResponseWriter, r *http.Request) {
	var req model.SmsTemplateCreateRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析创建短信模板请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	template, err := h.smsTemplateService.CreateTemplate(r.Context(), &req)
	if err != nil {
		logx.Errorf("创建短信模板失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(template))
}

// UpdateTemplate 更新短信模板
func (h *SmsTemplateHandler) UpdateTemplate(w http.ResponseWriter, r *http.Request) {
	var req model.SmsTemplateUpdateRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析更新短信模板请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	template, err := h.smsTemplateService.UpdateTemplate(r.Context(), &req)
	if err != nil {
		logx.Errorf("更新短信模板失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(template))
}

// DeleteTemplate 删除短信模板
func (h *SmsTemplateHandler) DeleteTemplate(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	if idStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "模板ID不能为空"))
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "模板ID格式错误"))
		return
	}

	if err := h.smsTemplateService.DeleteTemplate(r.Context(), uint(id)); err != nil {
		logx.Errorf("删除短信模板失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// BatchDeleteTemplates 批量删除短信模板
func (h *SmsTemplateHandler) BatchDeleteTemplates(w http.ResponseWriter, r *http.Request) {
	var req model.BatchDeleteRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析批量删除短信模板请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	if err := h.smsTemplateService.BatchDeleteTemplates(r.Context(), req.IDs); err != nil {
		logx.Errorf("批量删除短信模板失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// GetTemplate 获取短信模板详情
func (h *SmsTemplateHandler) GetTemplate(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	if idStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "模板ID不能为空"))
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "模板ID格式错误"))
		return
	}

	template, err := h.smsTemplateService.GetTemplate(r.Context(), uint(id))
	if err != nil {
		logx.Errorf("获取短信模板详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(template))
}

// ListTemplates 获取短信模板列表
func (h *SmsTemplateHandler) ListTemplates(w http.ResponseWriter, r *http.Request) {
	var req model.SmsTemplateListRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析获取短信模板列表请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	templates, total, err := h.smsTemplateService.ListTemplates(r.Context(), &req)
	if err != nil {
		logx.Errorf("获取短信模板列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 使用统一的分页响应
	pageRequest := &types.PageRequest{
		Page:  req.Page,
		Size:  req.Size,
		Query: req.TemplateName,
	}
	pageResponse := types.NewPageResponse(templates, total, pageRequest)

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(pageResponse))
}
