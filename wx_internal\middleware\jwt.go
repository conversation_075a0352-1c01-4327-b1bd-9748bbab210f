// 所有功能已经移至：
// - wx_jwt_utils.go：微信小程序JWT功能
// - token_blacklist.go：黑名单功能
//
// 此文件仅保留兼容层，所有方法内部调用专用模块的实现

package middleware

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"yekaitai/internal/types"
	"yekaitai/wx_internal/svc"

	"github.com/golang-jwt/jwt/v4"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
	xerrors "github.com/zeromicro/x/errors"
)

// 为微信用户定义OpenIDKey常量
const (
	OpenIDKey = "openid"
)

// JWTMiddleware JWT认证中间件
type JWTMiddleware struct {
	svcCtx *svc.WxServiceContext
}

// NewJWTMiddleware 创建JWT中间件
func NewJWTMiddleware(svcCtx *svc.WxServiceContext) *JWTMiddleware {
	return &JWTMiddleware{
		svcCtx: svcCtx,
	}
}

// Claims 自定义JWT声明
type Claims struct {
	UserID   uint   `json:"userId"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// RefreshClaims 刷新令牌的声明
type RefreshClaims struct {
	UserID   uint     `json:"userId"`
	Audience []string `json:"aud"`
	Issuer   string   `json:"iss"`
	jwt.RegisteredClaims
}

// TokenInfo 令牌信息
type TokenInfo struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	ExpiresAt    int64  `json:"expiresAt"`
}

// 错误定义 - 使用xerrors
var (
	ErrTokenExpired     = xerrors.New(401, "令牌已过期")
	ErrTokenInvalid     = xerrors.New(402, "无效的令牌")
	ErrTokenNotProvided = xerrors.New(403, "请提供认证令牌")
	ErrUserNotFound     = xerrors.New(404, "用户不存在")
	ErrRefreshFailed    = xerrors.New(405, "刷新令牌失败")
)

// 辅助函数：将WxTokenInfo转换为TokenInfo
func wxTokenToTokenInfo(wxToken *WxTokenInfo) *TokenInfo {
	return &TokenInfo{
		AccessToken:  wxToken.AccessToken,
		RefreshToken: wxToken.RefreshToken,
		ExpiresAt:    wxToken.ExpiresAt,
	}
}

// GenerateToken 生成JWT访问令牌和刷新令牌
// 根据角色自动选择调用微信的Token生成
func (m *JWTMiddleware) GenerateToken(userId uint, username, role string) (*TokenInfo, error) {
	logx.Infof("生成token，userId=%d, role=%s", userId, role)

	// 假设role字段存储的是角色名称列表的逗号分隔形式
	roles := strings.Split(role, ",")
	// 对微信用户，我们需要获取其openid
	// 此处简化处理，直接使用userId的字符串形式作为openid
	openid := strconv.FormatUint(uint64(userId), 10)
	wxToken, err := GenerateWxToken(m.svcCtx, openid, roles)
	if err != nil {
		return nil, err
	}
	return wxTokenToTokenInfo(wxToken), nil
}

// RefreshToken 刷新令牌
func (m *JWTMiddleware) RefreshToken(refreshToken string) (*TokenInfo, error) {
	// 尝试作为微信Token刷新
	wxToken, err := RefreshWxToken(m.svcCtx, refreshToken)
	if err == nil {
		return wxTokenToTokenInfo(wxToken), nil
	}

	// 如果刷新失败
	logx.Errorf("刷新令牌失败: %v", err)
	return nil, ErrRefreshFailed
}

// Handle JWT中间件处理函数
func (m *JWTMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 增加请求日志
		logx.Infof("收到认证请求: %s %s", r.Method, r.URL.Path)

		// 从请求头部获取Authorization
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			logx.Info("请求没有提供认证令牌")
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "请提供认证令牌"))
			return
		}

		// 记录认证头信息
		logx.Infof("认证头: %s", authHeader)

		// 检查头部格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 {
			logx.Errorf("认证格式错误: %s", authHeader)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
			return
		}

		tokenType := strings.ToLower(parts[0])
		tokenString := parts[1]

		// 验证认证类型，只支持Bearer
		if tokenType != "bearer" {
			logx.Errorf("不支持的认证类型: %s", tokenType)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "不支持的认证类型，请使用Bearer认证"))
			return
		}

		// 解析微信token
		wxClaims, err := ParseWxToken(m.svcCtx, tokenString)
		if err != nil {
			logx.Errorf("解析微信Token失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(
				types.CodeUnauthorized,
				fmt.Sprintf("认证失败: %v", err),
			))
			return
		}

		// 设置用户信息到上下文
		ctx := r.Context()
		openid := wxClaims.OpenID
		roles := strings.Join(wxClaims.Roles, ",")

		// 使用openid作为用户ID
		ctx = context.WithValue(ctx, types.UserIDKey, openid)
		ctx = context.WithValue(ctx, OpenIDKey, openid)
		ctx = context.WithValue(ctx, types.RoleKey, roles)

		// 记录成功认证信息
		logx.Infof("成功认证微信Token，OpenID: %s, 角色: %s", openid, roles)

		// 传递请求到下一个处理器
		next(w, r.WithContext(ctx))
	}
}

// SkipWhitelistMiddleware 处理白名单路径，跳过认证
func SkipWhitelistMiddleware(whitelistPaths []string, auth rest.Middleware) rest.Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			path := r.URL.Path
			for _, whitePath := range whitelistPaths {
				if strings.HasPrefix(path, whitePath) {
					next(w, r)
					return
				}
			}
			auth(next)(w, r)
		}
	}
}

// GetUserIDFromContext 从上下文中获取用户ID
func GetUserIDFromContext(ctx context.Context) (uint, error) {
	userId, ok := ctx.Value(types.UserIDKey).(uint)
	if !ok {
		return 0, errors.New("无法获取用户ID")
	}
	return userId, nil
}

// GetUsernameFromContext 从上下文中获取用户名
func GetUsernameFromContext(ctx context.Context) (string, error) {
	username, ok := ctx.Value(types.UsernameKey).(string)
	if !ok {
		return "", errors.New("无法获取用户名")
	}
	return username, nil
}

// GetRoleFromContext 从上下文中获取角色
func GetRoleFromContext(ctx context.Context) (string, error) {
	role, ok := ctx.Value(types.RoleKey).(string)
	if !ok {
		return "", errors.New("无法获取角色")
	}
	return role, nil
}

// JwtAuthMiddleware 创建JWT认证中间件
func JwtAuthMiddleware(serverCtx *svc.WxServiceContext) rest.Middleware {
	return NewJWTMiddleware(serverCtx).Handle
}
