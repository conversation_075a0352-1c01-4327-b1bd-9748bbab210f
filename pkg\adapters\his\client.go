package his

import (
	"context"
	"time"
)

// ProviderType HIS系统提供商类型
type ProviderType string

const (
	// ProviderABCYun ABC云诊所管家
	ProviderABCYun ProviderType = "abcyun"
	// ProviderChongQing 重庆HIS
	ProviderChongQing ProviderType = "chongqing"
)

// PatientInfo 患者信息
type PatientInfo struct {
	ID          string    // 患者ID
	Name        string    // 姓名
	Gender      int       // 性别：1-男，2-女，0-未知
	Birthday    time.Time // 出生日期
	IDCardType  int       // 证件类型：1-身份证，2-护照，3-军官证
	IDCardNo    string    // 证件号码
	Phone       string    // 手机号码
	Address     string    // 地址
	ExternalID  string    // 外部系统中的ID
	ExternalKey string    // 外部系统标识
}

// DepartmentInfo 科室信息
type DepartmentInfo struct {
	ID          string // 科室ID
	Name        string // 科室名称
	Description string // 科室描述
	Status      int    // 状态：1-正常，0-停用
	ExternalID  string // 外部系统中的ID
	ExternalKey string // 外部系统标识
}

// DoctorInfo 医生信息
type DoctorInfo struct {
	ID             string // 医生ID
	Name           string // 医生姓名
	Title          string // 职称
	DepartmentID   string // 科室ID
	DepartmentName string // 科室名称
	Introduction   string // 简介
	Status         int    // 状态：1-正常，0-停用
	ExternalID     string // 外部系统中的ID
	ExternalKey    string // 外部系统标识
}

// ScheduleInfo 排班信息
type ScheduleInfo struct {
	ID             string    // 排班ID
	DoctorID       string    // 医生ID
	DoctorName     string    // 医生姓名
	DepartmentID   string    // 科室ID
	DepartmentName string    // 科室名称
	Date           time.Time // 日期
	StartTime      time.Time // 开始时间
	EndTime        time.Time // 结束时间
	Quota          int       // 可预约人数
	Reserved       int       // 已预约人数
	RegisterFee    float64   // 挂号费
	Status         int       // 状态：1-正常，2-已满，3-停诊
	ExternalID     string    // 外部系统中的ID
	ExternalKey    string    // 外部系统标识
}

// AppointmentInfo 预约信息
type AppointmentInfo struct {
	ID             string    // 预约ID
	PatientID      string    // 患者ID
	PatientName    string    // 患者姓名
	DoctorID       string    // 医生ID
	DoctorName     string    // 医生姓名
	DepartmentID   string    // 科室ID
	DepartmentName string    // 科室名称
	ScheduleID     string    // 排班ID
	AppointTime    time.Time // 预约时间
	RegisterFee    float64   // 挂号费
	Status         int       // 预约状态：1-待支付，2-已支付，3-已完成，4-已取消
	ExternalID     string    // 外部系统中的ID
	ExternalKey    string    // 外部系统标识
}

// HISProvider HIS系统提供商接口
type HISProvider interface {
	// GetProviderType 获取提供商类型
	GetProviderType() ProviderType

	// GetClinicList 获取诊所列表
	GetClinicList(ctx context.Context) ([]string, error)

	// GetDepartments 获取科室列表
	GetDepartments(ctx context.Context, clinicID string) ([]DepartmentInfo, error)

	// GetDoctors 获取医生列表
	GetDoctors(ctx context.Context, clinicID, departmentID string, date time.Time) ([]DoctorInfo, error)

	// GetSchedules 获取排班信息
	GetSchedules(ctx context.Context, clinicID, departmentID, doctorID string, date time.Time) ([]ScheduleInfo, error)

	// CreatePatient 创建患者信息
	CreatePatient(ctx context.Context, clinicID string, patient *PatientInfo) (*PatientInfo, error)

	// GetPatients 获取患者列表
	GetPatients(ctx context.Context, clinicID, nameQuery string) ([]PatientInfo, error)

	// GetPatient 获取患者信息
	GetPatient(ctx context.Context, clinicID, patientID string) (*PatientInfo, error)

	// CreateAppointment 创建预约
	CreateAppointment(ctx context.Context, clinicID string, appointment *AppointmentInfo) (*AppointmentInfo, error)

	// CancelAppointment 取消预约
	CancelAppointment(ctx context.Context, clinicID, appointmentID, reason string) error

	// GetAppointments 获取预约列表
	GetAppointments(ctx context.Context, clinicID, patientID string, startDate, endDate time.Time, status int) ([]AppointmentInfo, error)

	// SyncDepartments 同步科室信息
	SyncDepartments(ctx context.Context, clinicID string) error

	// SyncDoctors 同步医生信息
	SyncDoctors(ctx context.Context, clinicID string, date time.Time) error

	// SyncSchedules 同步排班信息
	SyncSchedules(ctx context.Context, clinicID string, startDate, endDate time.Time) error

	// SyncAppointments 同步预约信息
	SyncAppointments(ctx context.Context, clinicID string, startDate, endDate time.Time) error
}

// Client HIS 系统客户端接口，是 HISProvider 的别名
type Client = HISProvider
