package chongqing

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
	"yekaitai/internal/config"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"
)

// Client 重庆HIS客户端
type Client struct {
	config     *config.ChongqingConfig
	httpClient *http.Client
}

// NewClient 创建重庆HIS客户端
func NewClient(config *config.ChongqingConfig) *Client {
	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// 发送HTTP请求
func (c *Client) doRequest(ctx context.Context, method, path string, data interface{}) ([]byte, error) {
	url := c.config.BaseURL + path

	var reqBody []byte
	var err error

	if data != nil {
		reqBody, err = json.Marshal(data)
		if err != nil {
			return nil, err
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("AppId", c.config.AppID)
	req.Header.Set("AppSecret", c.config.AppSecret)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// Hospital 医院信息
type Hospital struct {
	HospitalID   string `json:"hospital_id"`
	HospitalName string `json:"hospital_name"`
	Address      string `json:"address"`
	Phone        string `json:"phone"`
}

// GetHospitalList 获取医院列表
func (c *Client) GetHospitalList(ctx context.Context) ([]Hospital, error) {
	resp, err := c.doRequest(ctx, "GET", "/api/hospitals", nil)
	if err != nil {
		logx.Error("获取医院列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取医院列表失败: %w", err)
	}

	var result struct {
		Code    int        `json:"code"`
		Message string     `json:"message"`
		Data    []Hospital `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("获取医院列表失败: %s", result.Message)
	}

	return result.Data, nil
}

// Department 科室信息
type Department struct {
	DepartmentID   string `json:"department_id"`
	DepartmentName string `json:"department_name"`
	Description    string `json:"description"`
	Status         string `json:"status"`
}

// GetDepartmentList 获取科室列表
func (c *Client) GetDepartmentList(ctx context.Context, hospitalID string) ([]Department, error) {
	path := fmt.Sprintf("/api/hospitals/%s/departments", hospitalID)
	resp, err := c.doRequest(ctx, "GET", path, nil)
	if err != nil {
		logx.Error("获取科室列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取科室列表失败: %w", err)
	}

	var result struct {
		Code    int          `json:"code"`
		Message string       `json:"message"`
		Data    []Department `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("获取科室列表失败: %s", result.Message)
	}

	return result.Data, nil
}

// Doctor 医生信息
type Doctor struct {
	DoctorID       string `json:"doctor_id"`
	DoctorName     string `json:"doctor_name"`
	Title          string `json:"title"`
	DepartmentName string `json:"department_name"`
	Introduction   string `json:"introduction"`
	Status         string `json:"status"`
}

// GetDoctorList 获取医生列表
func (c *Client) GetDoctorList(ctx context.Context, hospitalID, departmentID, date string) ([]Doctor, error) {
	path := fmt.Sprintf("/api/hospitals/%s/departments/%s/doctors", hospitalID, departmentID)
	params := map[string]string{
		"date": date,
	}

	resp, err := c.doRequest(ctx, "GET", path, params)
	if err != nil {
		logx.Error("获取医生列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取医生列表失败: %w", err)
	}

	var result struct {
		Code    int      `json:"code"`
		Message string   `json:"message"`
		Data    []Doctor `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("获取医生列表失败: %s", result.Message)
	}

	return result.Data, nil
}

// Schedule 排班信息
type Schedule struct {
	ScheduleID     string `json:"schedule_id"`
	DoctorName     string `json:"doctor_name"`
	DepartmentName string `json:"department_name"`
	StartTime      string `json:"start_time"`
	EndTime        string `json:"end_time"`
	Quota          int    `json:"quota"`
	Reserved       int    `json:"reserved"`
	RegisterFee    string `json:"register_fee"`
	Status         string `json:"status"`
}

// GetDoctorSchedules 获取医生排班
func (c *Client) GetDoctorSchedules(ctx context.Context, hospitalID, doctorID, date string) ([]Schedule, error) {
	path := fmt.Sprintf("/api/hospitals/%s/doctors/%s/schedules", hospitalID, doctorID)
	params := map[string]string{
		"date": date,
	}

	resp, err := c.doRequest(ctx, "GET", path, params)
	if err != nil {
		logx.Error("获取医生排班失败", zap.Error(err))
		return nil, fmt.Errorf("获取医生排班失败: %w", err)
	}

	var result struct {
		Code    int        `json:"code"`
		Message string     `json:"message"`
		Data    []Schedule `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("获取医生排班失败: %s", result.Message)
	}

	return result.Data, nil
}

// Patient 患者信息
type Patient struct {
	PatientID  string `json:"patient_id"`
	Name       string `json:"name"`
	Gender     string `json:"gender"`
	Birthday   string `json:"birthday"`
	IDCardType string `json:"id_card_type"`
	IDCardNo   string `json:"id_card_no"`
	Phone      string `json:"phone"`
	Address    string `json:"address"`
}

// CreatePatientRequest 创建患者请求
type CreatePatientRequest struct {
	HospitalID string `json:"hospital_id"`
	Name       string `json:"name"`
	Gender     string `json:"gender"`
	Birthday   string `json:"birthday"`
	IDCardType string `json:"id_card_type"`
	IDCardNo   string `json:"id_card_no"`
	Phone      string `json:"phone"`
	Address    string `json:"address"`
}

// CreatePatientResponse 创建患者响应
type CreatePatientResponse struct {
	PatientID string `json:"patient_id"`
}

// CreatePatient 创建患者
func (c *Client) CreatePatient(ctx context.Context, req CreatePatientRequest) (*CreatePatientResponse, error) {
	path := fmt.Sprintf("/api/hospitals/%s/patients", req.HospitalID)
	resp, err := c.doRequest(ctx, "POST", path, req)
	if err != nil {
		logx.Error("创建患者失败", zap.Error(err))
		return nil, fmt.Errorf("创建患者失败: %w", err)
	}

	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    CreatePatientResponse `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("创建患者失败: %s", result.Message)
	}

	return &result.Data, nil
}

// GetPatientList 获取患者列表
func (c *Client) GetPatientList(ctx context.Context, hospitalID, nameQuery string) ([]Patient, error) {
	path := fmt.Sprintf("/api/hospitals/%s/patients", hospitalID)
	params := map[string]string{
		"name": nameQuery,
	}

	resp, err := c.doRequest(ctx, "GET", path, params)
	if err != nil {
		logx.Error("获取患者列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取患者列表失败: %w", err)
	}

	var result struct {
		Code    int       `json:"code"`
		Message string    `json:"message"`
		Data    []Patient `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("获取患者列表失败: %s", result.Message)
	}

	return result.Data, nil
}

// GetPatient 获取患者信息
func (c *Client) GetPatient(ctx context.Context, hospitalID, patientID string) (*Patient, error) {
	path := fmt.Sprintf("/api/hospitals/%s/patients/%s", hospitalID, patientID)
	resp, err := c.doRequest(ctx, "GET", path, nil)
	if err != nil {
		logx.Error("获取患者信息失败", zap.Error(err))
		return nil, fmt.Errorf("获取患者信息失败: %w", err)
	}

	var result struct {
		Code    int     `json:"code"`
		Message string  `json:"message"`
		Data    Patient `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("获取患者信息失败: %s", result.Message)
	}

	return &result.Data, nil
}

// Appointment 预约信息
type Appointment struct {
	AppointmentID   string `json:"appointment_id"`
	PatientID       string `json:"patient_id"`
	PatientName     string `json:"patient_name"`
	DoctorID        string `json:"doctor_id"`
	DoctorName      string `json:"doctor_name"`
	DepartmentID    string `json:"department_id"`
	DepartmentName  string `json:"department_name"`
	ScheduleID      string `json:"schedule_id"`
	AppointmentTime string `json:"appointment_time"`
	RegisterFee     string `json:"register_fee"`
	Status          string `json:"status"`
}

// CreateAppointmentRequest 创建预约请求
type CreateAppointmentRequest struct {
	HospitalID     string `json:"hospital_id"`
	PatientID      string `json:"patient_id"`
	ScheduleID     string `json:"schedule_id"`
	DoctorID       string `json:"doctor_id"`
	DepartmentID   string `json:"department_id"`
	Symptoms       string `json:"symptoms,omitempty"`
	MedicalHistory string `json:"medical_history,omitempty"`
}

// CreateAppointmentResponse 创建预约响应
type CreateAppointmentResponse struct {
	AppointmentID string `json:"appointment_id"`
	Status        string `json:"status"`
}

// CreateAppointment 创建预约
func (c *Client) CreateAppointment(ctx context.Context, req CreateAppointmentRequest) (*CreateAppointmentResponse, error) {
	path := fmt.Sprintf("/api/hospitals/%s/appointments", req.HospitalID)
	resp, err := c.doRequest(ctx, "POST", path, req)
	if err != nil {
		logx.Error("创建预约失败", zap.Error(err))
		return nil, fmt.Errorf("创建预约失败: %w", err)
	}

	var result struct {
		Code    int                       `json:"code"`
		Message string                    `json:"message"`
		Data    CreateAppointmentResponse `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("创建预约失败: %s", result.Message)
	}

	return &result.Data, nil
}

// CancelAppointment 取消预约
func (c *Client) CancelAppointment(ctx context.Context, hospitalID, appointmentID, reason string) error {
	path := fmt.Sprintf("/api/hospitals/%s/appointments/%s/cancel", hospitalID, appointmentID)
	params := map[string]string{
		"reason": reason,
	}

	resp, err := c.doRequest(ctx, "POST", path, params)
	if err != nil {
		logx.Error("取消预约失败", zap.Error(err))
		return fmt.Errorf("取消预约失败: %w", err)
	}

	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return err
	}

	if result.Code != 200 {
		return fmt.Errorf("取消预约失败: %s", result.Message)
	}

	return nil
}

// GetAppointmentList 获取预约列表
func (c *Client) GetAppointmentList(ctx context.Context, hospitalID, patientID, status, startDate, endDate string) ([]Appointment, error) {
	path := fmt.Sprintf("/api/hospitals/%s/appointments", hospitalID)
	params := map[string]string{
		"patient_id": patientID,
		"status":     status,
		"start_date": startDate,
		"end_date":   endDate,
	}

	resp, err := c.doRequest(ctx, "GET", path, params)
	if err != nil {
		logx.Error("获取预约列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取预约列表失败: %w", err)
	}

	var result struct {
		Code    int           `json:"code"`
		Message string        `json:"message"`
		Data    []Appointment `json:"data"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, fmt.Errorf("获取预约列表失败: %s", result.Message)
	}

	return result.Data, nil
}
