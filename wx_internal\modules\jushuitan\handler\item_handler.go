package handler

import (
	"net/http"
	"strconv"
	"strings"
	"time"
	jst "yekaitai/pkg/adapters/jushuitan"

	"github.com/zeromicro/go-zero/core/logx"
)

// QueryItemSku 查询普通商品资料(按sku)
func (h *JushuitanHandler) QueryItemSku(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageIndex, _ := strconv.Atoi(r.FormValue("page_index"))
	if pageIndex <= 0 {
		pageIndex = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	modifiedBegin := r.FormValue("modified_begin")
	modifiedEnd := r.FormValue("modified_end")
	skuIDs := r.FormValue("sku_ids")

	// 记录请求参数
	req := &jst.ItemSkuQueryRequest{
		PageIndex:     pageIndex,
		PageSize:      pageSize,
		ModifiedBegin: modifiedBegin,
		ModifiedEnd:   modifiedEnd,
		SkuIDs:        skuIDs,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QueryItemSku(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询普通商品资料(按sku)失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryMallItem 查询普通商品资料(按款)
func (h *JushuitanHandler) QueryMallItem(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageIndex, _ := strconv.Atoi(r.FormValue("page_index"))
	if pageIndex <= 0 {
		pageIndex = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	modifiedBegin := r.FormValue("modified_begin")
	modifiedEnd := r.FormValue("modified_end")
	iIDsStr := r.FormValue("i_ids")
	outerIDsStr := r.FormValue("outer_ids")
	onlyItemStr := r.FormValue("only_item")
	onlyItem := onlyItemStr == "true" || onlyItemStr == "1"

	// 转换i_ids
	var iIDs []string
	if iIDsStr != "" {
		iIDs = strings.Split(iIDsStr, ",")
	}

	// 转换outer_ids
	var outerIDs []string
	if outerIDsStr != "" {
		outerIDs = strings.Split(outerIDsStr, ",")
	}

	// 记录请求参数
	req := &jst.MallItemQueryRequest{
		PageIndex:     pageIndex,
		PageSize:      pageSize,
		ModifiedBegin: modifiedBegin,
		ModifiedEnd:   modifiedEnd,
		IIDs:          iIDs,
		OuterIDs:      outerIDs,
		OnlyItem:      onlyItem,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QueryMallItem(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询普通商品资料(按款)失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QuerySkuMap 查询店铺商品资料
func (h *JushuitanHandler) QuerySkuMap(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageIndex, _ := strconv.Atoi(r.FormValue("page_index"))
	if pageIndex <= 0 {
		pageIndex = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	modifiedBegin := r.FormValue("modified_begin")
	modifiedEnd := r.FormValue("modified_end")
	skuIDs := r.FormValue("sku_ids")
	shopID, _ := strconv.Atoi(r.FormValue("shop_id"))

	// 记录请求参数
	req := &jst.SkuMapQueryRequest{
		PageIndex:     pageIndex,
		PageSize:      pageSize,
		ModifiedBegin: modifiedBegin,
		ModifiedEnd:   modifiedEnd,
		SkuIDs:        skuIDs,
		ShopID:        shopID,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QuerySkuMap(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询店铺商品资料失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}
