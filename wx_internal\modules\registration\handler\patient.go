package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"

	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/response"
)

// PatientCreateRequest 患者信息创建请求
type PatientCreateRequest struct {
	ZjLx     string `json:"zjlx,optional"`     // 证件类型 例如：01居民身份证, 99其他法定有效证件
	ZjLxMC   string `json:"zjlxmc,optional"`   // 证件类型名称
	ZjHm     string `json:"zjhm"`     // 证件号码
	Xm       string `json:"xm"`       // 姓名
	XbDM     string `json:"xbdm"`     // 性别代码 0:未知的性别，1：男，2：女，9未说明的性别
	Xb       string `json:"xb,optional"`       // 性别名称
	CsSj     string `json:"cssj,optional"`     // 出生时间
	JtzzXxdz string `json:"jtzzXxdz,optional"` // 家庭住址
	SjHm     string `json:"sjhm,optional"`     // 手机号码
	GjDM     string `json:"gjdm,optional"`     // 国籍代码 156中国
	MzDM     string `json:"mzdm,optional"`     // 民族代码 01汉族
	HyzkDM   string `json:"hyzkdm,optional"`   // 婚姻状况代码 10未婚 20已婚
	// 新增本地患者ID参数
	LocalPatientID uint `json:"localPatientId,optional" form:"localPatientId,optional"` // 本地患者ID
}

// PatientHandler 患者管理处理器
type PatientHandler struct {
}

// NewPatientHandler 创建患者管理处理器
func NewPatientHandler() *PatientHandler {
	return &PatientHandler{}
}

// Create 创建患者信息
func (h *PatientHandler) Create(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[患者信息创建] 开始处理请求: %s %s", r.Method, r.URL.String())

	// 添加安全检查
	if r == nil {
		errMsg := "[患者信息创建] 请求对象为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "无效请求")
		return
	}

	if r.Body == nil {
		errMsg := "[患者信息创建] 请求体为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "请求体不能为空")
		return
	}

	// 记录原始请求内容
	requestBody, err := io.ReadAll(r.Body)
	if err != nil {
		errMsg := fmt.Sprintf("[患者信息创建] 读取请求体失败: %v", err)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("无法读取请求数据: %v", err))
		return
	}
	logx.Infof("[患者信息创建] 接收到的原始请求体数据: %s", string(requestBody))

	// 恢复请求体以便后续解析
	r.Body = io.NopCloser(bytes.NewBuffer(requestBody))

	var req PatientCreateRequest
	if err := httpx.Parse(r, &req); err != nil {
		errMsg := fmt.Sprintf("[患者信息创建] 解析请求参数失败: %v, 原始请求: %s", err, string(requestBody))
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("请求参数解析失败: %v", err))
		return
	}

	// 记录原始请求参数
	reqStr, _ := json.Marshal(req)
	logx.Infof("[患者信息创建] 创建患者信息原始请求参数: %s", string(reqStr))

	// 参数验证
	if req.ZjHm == "" {
		errMsg := "[患者信息创建] 参数验证失败: 证件号码不能为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "证件号码不能为空")
		return
	}
	if req.Xm == "" {
		errMsg := "[患者信息创建] 参数验证失败: 姓名不能为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "姓名不能为空")
		return
	}
	if req.XbDM == "" {
		errMsg := "[患者信息创建] 参数验证失败: 性别代码不能为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "性别代码不能为空")
		return
	}

	// 验证本地患者ID（如果提供）
	var localPatient *patient.WxPatient

	if req.LocalPatientID > 0 {
		patientRepo := patient.NewWxPatientRepository(mysql.Slave())
		localPatient, err = patientRepo.FindByID(req.LocalPatientID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				errMsg := fmt.Sprintf("[患者信息创建] 本地患者ID %d 不存在", req.LocalPatientID)
				logx.Errorf(errMsg)
				response.Error(w, response.CodeInvalidParams, "指定的本地患者不存在")
			} else {
				errMsg := fmt.Sprintf("[患者信息创建] 查询本地患者失败: %v", err)
				logx.Errorf(errMsg)
				response.Error(w, response.CodeInternalError, fmt.Sprintf("查询本地患者失败: %v", err))
			}
			return
		}
		logx.Infof("[患者信息创建] 关联本地患者: ID=%d, 姓名=%s", localPatient.PatientID, localPatient.Name)
	}

	// 设置默认值
	if req.ZjLx == "" {
		req.ZjLx = "01" // 默认居民身份证
	}
	if req.ZjLxMC == "" {
		req.ZjLxMC = "其他法定有效证件"
	}
	if req.GjDM == "" {
		req.GjDM = "156" // 默认中国
	}
	if req.MzDM == "" {
		req.MzDM = "01" // 默认汉族
	}
	if req.HyzkDM == "" {
		req.HyzkDM = "10" // 默认未婚
	}

	// 获取当前用户信息
	czrID := 17283
	czr := "系统"
	userInfo, err := hangzhou.GetCurrentUserInfo(r.Context())
	if err != nil {
		errMsg := fmt.Sprintf("[患者信息创建] 获取当前用户信息失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("获取用户信息失败: %v", err))
		}
		return
	}

	// 安全检查：避免空指针
	if userInfo != nil && userInfo.Data != nil {
		if userData, ok := userInfo.Data.(*hangzhou.CurrentUser); ok && userData != nil {
			czrID = userData.YhID
			czr = userData.YhMC
		}
	}

	// 直接构建PersonalInfo对象，准确映射到杭州HIS API请求格式
	personalInfo := map[string]interface{}{
		"zjlx":     req.ZjLx,     // 证件类型
		"zjlxmc":   req.ZjLxMC,   // 证件类型名称
		"zjhm":     req.ZjHm,     // 证件号码
		"xm":       req.Xm,       // 姓名
		"xbdm":     req.XbDM,     // 性别代码
		"xb":       req.Xb,       // 性别名称
		"cssj":     req.CsSj,     // 出生时间
		"jtzzXxdz": req.JtzzXxdz, // 家庭住址
		"sjhm":     req.SjHm,     // 手机号码
		"czrid":    czrID,        // 操作人ID
		"czr":      czr,          // 操作人
		"gjdm":     req.GjDM,     // 国籍代码
		"mzdm":     req.MzDM,     // 民族代码
		"hyzkdm":   req.HyzkDM,   // 婚姻状况代码
	}

	// 将map转换为PersonalInfo结构体
	finalRequestBytes, _ := json.Marshal(personalInfo)
	logx.Infof("[患者信息创建] 发送到杭州HIS的患者信息完整请求参数: %s", string(finalRequestBytes))

	// 调用杭州API创建患者信息
	// 创建一个HTTP请求
	client := &http.Client{}

	// 添加安全检查
	if hangzhou.DefaultClient == nil {
		errMsg := "[患者信息创建] 杭州HIS客户端未初始化"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInternalError, "系统未初始化，请联系管理员")
		return
	}

	config := hangzhou.DefaultClient.GetConfig()
	if config.BaseURL == "" {
		errMsg := "[患者信息创建] 杭州HIS配置未初始化"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInternalError, "系统配置异常，请联系管理员")
		return
	}

	url := config.BaseURL + "/api/v1/consumer-jcfw/jcfw/grxx"

	req1, err := http.NewRequestWithContext(r.Context(), "POST", url, strings.NewReader(string(finalRequestBytes)))
	if err != nil {
		errMsg := fmt.Sprintf("[患者信息创建] 创建HTTP请求失败: %v", err)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInternalError, fmt.Sprintf("系统内部错误: %v", err))
		return
	}

	// 获取token
	token, err := hangzhou.DefaultClient.GetAccessToken(r.Context())
	if err != nil {
		errMsg := fmt.Sprintf("[患者信息创建] 获取访问令牌失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("获取授权失败: %v", err))
		}
		return
	}

	// 设置请求头
	req1.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req1.Header.Set("Authorization", "Bearer "+token)

	// 添加HIS系统特定的认证头
	hangzhou.DefaultClient.SetHISAuthHeaders(req1)

	// 发送请求
	resp1, err := client.Do(req1)
	if err != nil {
		errMsg := fmt.Sprintf("[患者信息创建] 发送创建患者信息请求失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("连接HIS系统失败: %v", err))
		}
		return
	}
	defer resp1.Body.Close()

	// 解析响应
	var result map[string]interface{}
	if err := json.NewDecoder(resp1.Body).Decode(&result); err != nil {
		errMsg := fmt.Sprintf("[患者信息创建] 解析创建患者信息响应失败: %v", err)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInternalError, fmt.Sprintf("解析HIS系统响应失败: %v", err))
		return
	}

	// 检查响应状态码
	if resp1.StatusCode != http.StatusOK {
		var errorMsg string
		if msg, ok := result["message"].(string); ok {
			errorMsg = msg
		} else {
			errorMsg = "创建患者信息失败"
		}
		errMsg := fmt.Sprintf("[患者信息创建] HIS系统返回错误: %s, 状态码: %d", errorMsg, resp1.StatusCode)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInternalError, fmt.Sprintf("HIS系统错误: %s", errorMsg))
		return
	}

	// 记录响应结果
	respBytes, _ := json.Marshal(result)
	logx.Infof("[患者信息创建] 杭州HIS患者信息创建成功，响应: %s", string(respBytes))

	// 如果提供了本地患者ID，更新本地患者的杭州HIS患者ID
	if localPatient != nil {
		// 从响应中提取杭州HIS患者ID
		var hangzhouHisID string
		if dataMap, ok := result["data"].(map[string]interface{}); ok {
			if grxxID, exists := dataMap["grxxid"]; exists {
				hangzhouHisID = fmt.Sprintf("%v", grxxID)
			} else if id, exists := dataMap["id"]; exists {
				hangzhouHisID = fmt.Sprintf("%v", id)
			}
		} else if grxxID, exists := result["grxxid"]; exists {
			hangzhouHisID = fmt.Sprintf("%v", grxxID)
		}

		if hangzhouHisID != "" {
			// 更新本地患者的杭州HIS患者ID
			localPatient.HangzhouHisID = hangzhouHisID
			// 为了兼容性，也更新GrxxID字段
			// if localPatient.GrxxID == "" {
			// 	localPatient.GrxxID = hangzhouHisID
			// }

			err = mysql.Master().Save(localPatient).Error
			if err != nil {
				logx.Errorf("[患者信息创建] 更新本地患者杭州HIS ID失败: %v", err)
				// 不影响主流程，只记录错误
			} else {
				logx.Infof("[患者信息创建] 成功更新本地患者杭州HIS ID: 本地ID=%d, 杭州HIS ID=%s", localPatient.PatientID, hangzhouHisID)
			}
		} else {
			logx.Errorf("[患者信息创建] 无法从响应中提取杭州HIS患者ID")
		}
	}

	// 构建最终响应，包含LocalPatientID
	finalResponse := result
	if req.LocalPatientID > 0 {
		// 在响应中添加LocalPatientID
		if finalResponse == nil {
			finalResponse = make(map[string]interface{})
		}
		finalResponse["localPatientId"] = req.LocalPatientID
	}

	response.Success(w, finalResponse)
}
