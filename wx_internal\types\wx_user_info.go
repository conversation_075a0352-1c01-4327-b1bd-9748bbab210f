package types

// UpdateProfileReq 更新用户资料请求
type UpdateProfileReq struct {
	Avatar   string `json:"avatar,omitempty"`   // 用户头像，可选
	NickName string `json:"nickName,omitempty"` // 用户昵称，可选
}

// UpdateProfileResp 更新用户资料响应
type UpdateProfileResp struct {
	Success bool `json:"success"` // 是否成功
}

// GetPhoneNumberReq 获取手机号请求
type GetPhoneNumberReq struct {
	Code string `json:"code"` // 手机号获取凭证
}

// GetPhoneNumberResp 获取手机号响应
type GetPhoneNumberResp struct {
	PhoneNumber string `json:"phoneNumber"` // 用户手机号
}

// WxUser 微信用户信息
type WxUser struct {
	Id          int64  `json:"id" db:"id"`
	OpenId      string `json:"openId" db:"open_id"`
	PhoneNumber string `json:"phoneNumber" db:"phone_number"`
	Avatar      string `json:"avatar" db:"avatar"`
	NickName    string `json:"nickName" db:"nick_name"`
	CreateTime  int64  `json:"createTime" db:"create_time"`
	UpdateTime  int64  `json:"updateTime" db:"update_time"`
}

// PhoneInfo 手机号信息
type PhoneInfo struct {
	PhoneNumber     string `json:"phoneNumber"`     // 用户手机号
	PurePhoneNumber string `json:"purePhoneNumber"` // 不带区号的手机号
	CountryCode     string `json:"countryCode"`     // 国际区号
}
