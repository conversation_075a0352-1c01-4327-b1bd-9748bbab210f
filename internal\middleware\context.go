package middleware

import (
	"context"
)

type contextKey string

const (
	CtxKeyPrefix contextKey = "ctx_"
)

// NewContext 创建新的上下文，添加键值对
func NewContext(ctx context.Context, key, value string) context.Context {
	return context.WithValue(ctx, CtxKeyPrefix+contextKey(key), value)
}

// FromContext 从上下文获取值
func FromContext(ctx context.Context, key string) string {
	value, ok := ctx.Value(CtxKeyPrefix + contextKey(key)).(string)
	if !ok {
		return ""
	}
	return value
}
