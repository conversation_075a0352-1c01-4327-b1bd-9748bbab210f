package tasks

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/internal/modules/store/model"
	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/common/model/doctor"
)

// DoctorSyncService 医生信息同步服务
type DoctorSyncService struct {
	db                  *gorm.DB
	wsjgID              int               // 卫生机构ID
	storeMap            map[string]string // 卫生机构ID到名称的映射，一次同步只查询一次
	bookableDepartments map[int]bool      // 可预约科室ID映射表
}

// NewDoctorSyncService 创建医生信息同步服务
func NewDoctorSyncService(db *gorm.DB) *DoctorSyncService {
	logx.Info("初始化医生同步服务...")

	// 检查DefaultClient是否正确初始化
	if hangzhou.DefaultClient == nil {
		logx.Error("错误：hangzhou.DefaultClient未初始化，同步无法进行")
		return &DoctorSyncService{
			db:                  db,
			wsjgID:              5426,
			storeMap:            make(map[string]string),
			bookableDepartments: make(map[int]bool),
		}
	}

	// 获取配置中的卫生机构ID
	config := hangzhou.DefaultClient.GetConfig()
	logx.Infof("获取到的hangzhou配置：%+v", config)

	wsjgID, _ := strconv.Atoi(config.CurrentWsjgid)
	if wsjgID == 0 {
		logx.Error("无法获取卫生机构ID，将使用默认值")
		wsjgID = 5426 // 保留默认值作为后备
	}

	logx.Infof("医生同步服务初始化完成，使用卫生机构ID: %d", wsjgID)

	return &DoctorSyncService{
		db:                  db,
		wsjgID:              wsjgID,
		storeMap:            make(map[string]string),
		bookableDepartments: make(map[int]bool),
	}
}

// Start 启动定时任务
func (s *DoctorSyncService) Start() {
	logx.Info("=======================================================")
	logx.Info("============ 医生同步服务启动中，直接执行 ============")
	logx.Info("=======================================================")

	// 立即执行一次
	s.SyncDoctors()

	logx.Info("医生同步服务已启动，定时任务设置为每小时执行一次")

	// 每小时执行一次
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		<-ticker.C
		logx.Info("医生同步定时任务触发")
		s.SyncDoctors()
	}
}

// initStoreMap 初始化门店映射表，一次同步只查询一次
func (s *DoctorSyncService) initStoreMap() error {
	var stores []model.Store
	if err := s.db.Model(&model.Store{}).Find(&stores).Error; err != nil {
		logx.Errorf("获取门店数据失败: %v", err)
		return err
	}

	s.storeMap = make(map[string]string)
	for _, store := range stores {
		s.storeMap[store.WsjgID] = store.Name
	}

	logx.Infof("成功初始化门店映射表，共 %d 个门店", len(s.storeMap))
	return nil
}

// initBookableDepartments 初始化可预约科室映射表
func (s *DoctorSyncService) initBookableDepartments(ctx context.Context) error {
	// 调用可预约科室接口
	resp, err := hangzhou.GetDepartments(ctx, s.wsjgID, 1) // pbksbz=1表示可预约科室
	if err != nil {
		logx.Errorf("获取可预约科室数据失败: %v", err)
		return err
	}

	// 解析响应数据
	departments, ok := resp.Data.([]interface{})
	if !ok {
		logx.Errorf("解析可预约科室数据失败，响应数据类型不匹配")
		return err
	}

	s.bookableDepartments = make(map[int]bool)
	for _, item := range departments {
		dept, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		// 提取科室ID
		jgksID := 0
		if id, ok := dept["jgksid"].(float64); ok {
			jgksID = int(id)
		}

		if jgksID > 0 {
			s.bookableDepartments[jgksID] = true
		}
	}

	logx.Infof("成功初始化可预约科室映射表，共 %d 个可预约科室", len(s.bookableDepartments))
	return nil
}

// SyncDoctors 同步医生信息
func (s *DoctorSyncService) SyncDoctors() error {
	// 创建上下文，设置超时时间为30分钟
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	logx.Infof("========== 开始同步医生信息 ==========")
	logx.Infof("卫生机构ID: %d", s.wsjgID)

	// 初始化门店映射表
	if err := s.initStoreMap(); err != nil {
		return err
	}

	// 初始化可预约科室映射表
	if err := s.initBookableDepartments(ctx); err != nil {
		return err
	}

	// 创建人默认为系统管理员
	var creatorID uint = 1

	// 记录统计数据
	created, updated, filtered := 0, 0, 0

	defer func() {
		logx.Infof("========== 医生信息同步完成，共创建 %d 个医生，更新 %d 个医生，过滤 %d 个非本机构医生 ==========",
			created, updated, filtered)
	}()

	// 1. 获取所有卫生人员信息
	logx.Info("正在调用hangzhou.GetHealthPersons获取卫生人员...")
	resp, err := hangzhou.GetHealthPersons(ctx, s.wsjgID, 0, "")
	if err != nil {
		logx.Errorf("获取卫生人员信息失败: %v", err)
		return err
	}

	// 解析数据
	healthPersons, ok := resp.Data.([]hangzhou.HealthPerson)
	if !ok {
		logx.Errorf("解析卫生人员数据失败，响应数据类型不匹配")
		return err
	}

	logx.Infof("成功解析卫生人员数据，获取到卫生人员数量: %d", len(healthPersons))

	// 处理每个医生信息
	for i, person := range healthPersons {
		logx.Infof("处理第 %d 个人员: YhID=%d, 姓名=%s", i+1, person.YhID, person.YhMC)

		// 获取用户科室信息
		deptInfo, err := s.getUserDepartmentInfo(ctx, person.YhID)
		if err != nil {
			logx.Errorf("获取卫生人员 %s (YhID=%d) 的科室信息失败：%v", person.YhMC, person.YhID, err)
			filtered++
			continue
		}

		if deptInfo == nil {
			logx.Infof("卫生人员 %s (YhID=%d) 没有关联到当前卫生机构的科室，跳过", person.YhMC, person.YhID)
			filtered++
			continue
		}

		// 创建或更新医生记录
		if err := s.createOrUpdateDoctor(person, deptInfo, creatorID); err != nil {
			logx.Errorf("处理医生 %s 信息失败: %v", person.YhMC, err)
			continue
		}

		// 更新统计
		var wxDoctor doctor.WxDoctor
		if s.db.Where("ys_id = ?", strconv.Itoa(person.YhID)).First(&wxDoctor).Error == nil {
			// 医生已存在，算作更新
			updated++
		} else {
			// 新医生，算作创建
			created++
		}

		// 关联门店
		if err := s.associateStore(&wxDoctor); err != nil {
			logx.Errorf("关联医生门店失败: %v", err)
		}
	}

	return nil
}

// DepartmentInfo 科室信息结构
type DepartmentInfo struct {
	JgksID   int    // 机构科室ID
	JgksMC   string // 机构科室名称
	WsrylbDM string // 卫生人员类别代码
	WsrylbMC string // 卫生人员类别名称
}

// getUserDepartmentInfo 获取用户科室信息
func (s *DoctorSyncService) getUserDepartmentInfo(ctx context.Context, yhID int) (*DepartmentInfo, error) {
	userDeptResp, err := hangzhou.GetUserDepartments(ctx, s.wsjgID, yhID, "")
	if err != nil {
		return nil, err
	}

	userDepts, ok := userDeptResp.Data.([]hangzhou.UserDepartment)
	if !ok || len(userDepts) == 0 {
		return nil, nil
	}

	// 使用第一个科室信息
	dept := userDepts[0]

	// 检查科室是否在可预约科室列表中
	if !s.bookableDepartments[dept.JgksID] {
		logx.Infof("科室 %s (JgksID=%d) 不是可预约科室，跳过", dept.JgksMC, dept.JgksID)
		return nil, nil
	}

	return &DepartmentInfo{
		JgksID:   dept.JgksID,
		JgksMC:   dept.JgksMC,
		WsrylbDM: dept.WsrylbDM, // 从UserDepartment中获取卫生人员类别代码
		WsrylbMC: dept.WsrylbMC, // 从UserDepartment中获取卫生人员类别名称
	}, nil
}

// createOrUpdateDoctor 创建或更新医生信息
func (s *DoctorSyncService) createOrUpdateDoctor(person hangzhou.HealthPerson, deptInfo *DepartmentInfo, creatorID uint) error {
	var wxDoctor doctor.WxDoctor
	result := s.db.Where("ys_id = ?", strconv.Itoa(person.YhID)).First(&wxDoctor)
	isNew := result.Error != nil

	// 设置基本信息
	wxDoctor.Name = person.YhMC
	wxDoctor.YsID = strconv.Itoa(person.YhID) // 转换为字符串

	// 根据作废标志设置状态
	if person.Zfbz == 1 {
		wxDoctor.Status = 0
		logx.Infof("医生 %s (YhID=%d) 已作废，状态设置为禁用", person.YhMC, person.YhID)
	} else {
		wxDoctor.Status = 1
		logx.Infof("医生 %s (YhID=%d) 未作废，状态设置为正常", person.YhMC, person.YhID)
	}

	// 设置擅长领域到specialty字段
	wxDoctor.Specialty = person.Zytc

	// 设置简介
	wxDoctor.Introduction = person.Grjj

	// 根据wsjgid查询t_stores表获取医院名称
	if hospitalName, exists := s.storeMap[strconv.Itoa(s.wsjgID)]; exists {
		wxDoctor.Hospital = hospitalName
	}

	// 设置卫生人员类别信息（从deptInfo中获取）
	wxDoctor.MedicalLicense = deptInfo.WsrylbDM // 卫生人员类别代码
	wxDoctor.WsrylbMC = deptInfo.WsrylbMC       // 卫生人员类别名称

	// 设置职称（从person参数中获取zyjszwmc）
	if title, ok := person.ZyjszwMC.(string); ok {
		wxDoctor.Title = title
	}

	// 设置科室信息
	wxDoctor.JgksID = strconv.Itoa(deptInfo.JgksID)
	wxDoctor.Department = deptInfo.JgksMC
	wxDoctor.WsjgID = strconv.Itoa(s.wsjgID)

	wxDoctor.CreatorID = creatorID

	// 保存或更新医生信息
	if isNew {
		err := s.db.Create(&wxDoctor).Error
		if err != nil {
			return fmt.Errorf("创建医生失败: %v", err)
		}
		logx.Infof("创建医生成功: %s (YhID=%d)", wxDoctor.Name, person.YhID)
	} else {
		// 更新时保留原有的创建时间，只更新其他字段
		err := s.db.Model(&wxDoctor).Where("ys_id = ?", strconv.Itoa(person.YhID)).Updates(map[string]interface{}{
			"name":            wxDoctor.Name,
			"specialty":       wxDoctor.Specialty,
			"introduction":    wxDoctor.Introduction,
			"hospital":        wxDoctor.Hospital,
			"medical_license": wxDoctor.MedicalLicense,
			"wsrylb_mc":       wxDoctor.WsrylbMC,
			"title":           wxDoctor.Title,
			"jgks_id":         wxDoctor.JgksID,
			"department":      wxDoctor.Department,
			"wsjg_id":         wxDoctor.WsjgID,
			"creator_id":      wxDoctor.CreatorID,
			"status":          wxDoctor.Status,
		}).Error
		if err != nil {
			return fmt.Errorf("更新医生失败: %v", err)
		}
		logx.Infof("更新医生成功: %s (YhID=%d)", wxDoctor.Name, person.YhID)
	}

	return nil
}

// associateStore 关联医生与门店
func (s *DoctorSyncService) associateStore(wxDoctor *doctor.WxDoctor) error {
	// 根据医院名称查找门店
	var targetStore model.Store
	result := s.db.Where("wsjg_id = ?", wxDoctor.WsjgID).First(&targetStore)
	if result.Error != nil {
		logx.Errorf("未找到门店 '%s'，无法关联医生: %s", wxDoctor.Hospital, wxDoctor.Name)
		return result.Error
	}

	// 先清除旧的关联
	if err := s.db.Exec("DELETE FROM doctor_store_relation WHERE doctor_id = ?", wxDoctor.DoctorID).Error; err != nil {
		logx.Errorf("清除医生与医疗机构关联失败，医生ID=%d: %v", wxDoctor.DoctorID, err)
		return err
	}

	// 添加新的关联
	if err := s.db.Exec("INSERT INTO doctor_store_relation(doctor_id, store_id) VALUES(?, ?)",
		wxDoctor.DoctorID, targetStore.ID).Error; err != nil {
		logx.Errorf("添加医生与医疗机构关联失败，医生ID=%d, 医疗机构ID=%d: %v",
			wxDoctor.DoctorID, targetStore.ID, err)
		return err
	}

	logx.Infof("成功关联医生: %s 到门店: %s", wxDoctor.Name, targetStore.Name)
	return nil
}

// RunSync 执行同步任务的包装方法
func (s *DoctorSyncService) RunSync() {
	if err := s.SyncDoctors(); err != nil {
		logx.Errorf("执行医生同步失败: %v", err)
	}
}
