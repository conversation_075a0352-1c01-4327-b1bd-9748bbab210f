package service

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"yekaitai/internal/modules/member/repository"
	tagRepo "yekaitai/internal/modules/tag/repository"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/wx_internal/modules/user/types"
)

// MemberService 会员服务接口
type MemberService interface {
	// ListMembers 获取会员列表
	ListMembers(page, size int, query map[string]interface{}) ([]*types.WxUser, int64, error)
	// GetMember 获取会员详情
	GetMember(id uint) (*types.WxUser, error)
	// UpdateMemberStatus 更新会员状态
	UpdateMemberStatus(id uint, status int) error
	// GetMemberPatients 获取会员的就诊人列表
	GetMemberPatients(userID uint) ([]*patient.WxPatient, error)
	// UpdateMemberTags 更新会员标签
	UpdateMemberTags(id uint, tagIDsStr string) error
}

// memberService 会员服务实现
type memberService struct {
	repo    repository.MemberRepository
	tagRepo tagRepo.TagRepository
}

// NewMemberService 创建会员服务
func NewMemberService(repo repository.MemberRepository, tagRepo tagRepo.TagRepository) MemberService {
	return &memberService{repo: repo, tagRepo: tagRepo}
}

// ListMembers 获取会员列表
func (s *memberService) ListMembers(page, size int, query map[string]interface{}) ([]*types.WxUser, int64, error) {
	// 查询数据库获取会员列表
	users, total, err := s.repo.List(page, size, query)
	if err != nil {
		return nil, 0, err
	}

	// 由于types.WxUser现在是user.WxUser的别名，可以直接返回
	result := make([]*types.WxUser, len(users))
	for i, u := range users {
		result[i] = (*types.WxUser)(u)
	}

	return result, total, nil
}

// GetMember 获取会员详情
func (s *memberService) GetMember(id uint) (*types.WxUser, error) {
	// 查询数据库获取会员信息
	user, err := s.repo.FindByID(id)
	if err != nil {
		return nil, err
	}

	// 由于types.WxUser现在是user.WxUser的别名，可以直接返回
	return (*types.WxUser)(user), nil
}

// UpdateMemberStatus 更新会员状态
func (s *memberService) UpdateMemberStatus(id uint, status int) error {
	// 校验状态值
	if status != 0 && status != 1 {
		return errors.New("无效的状态值，只能是0或1")
	}

	// 查询会员是否存在
	user, err := s.repo.FindByID(id)
	if err != nil {
		return err
	}

	// 如果状态无变化，则无需更新
	if user.Status == status {
		return nil
	}

	// 更新状态
	return s.repo.UpdateStatus(id, status)
}

// GetMemberPatients 获取会员的就诊人列表
func (s *memberService) GetMemberPatients(userID uint) ([]*patient.WxPatient, error) {
	return s.repo.FindPatients(userID)
}

// UpdateMemberTags 更新会员标签
func (s *memberService) UpdateMemberTags(id uint, tagIDsStr string) error {
	// 查询会员是否存在
	_, err := s.repo.FindByID(id)
	if err != nil {
		return err
	}

	// 将字符串格式的标签ID转换为uint切片
	var tagIDs []uint
	if tagIDsStr != "" {
		tagIDStrSlice := strings.Split(tagIDsStr, ",")
		for _, idStr := range tagIDStrSlice {
			id, err := strconv.ParseUint(idStr, 10, 32)
			if err != nil {
				return fmt.Errorf("无效的标签ID: %s", idStr)
			}
			tagIDs = append(tagIDs, uint(id))
		}

		// 检查标签是否存在
		existingTags, err := s.tagRepo.FindByIDs(tagIDs)
		if err != nil {
			return fmt.Errorf("检查标签失败: %v", err)
		}

		if len(existingTags) != len(tagIDs) {
			return fmt.Errorf("部分标签不存在")
		}
	}

	// 更新会员标签
	return s.repo.UpdateMemberTags(id, tagIDs)
}
