package abcyun

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
)

// 通用响应结构
type Response struct {
	Data interface{} `json:"data"`
}

// Department 科室信息
type Department struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	Type              int    `json:"type"`
	MainMedicalName   string `json:"mainMedicalName"`
	SecondMedicalName string `json:"secondMedicalName"`
}

// DepartmentListResponse 科室列表响应
type DepartmentListResponse struct {
	DepartmentList []Department `json:"departmentList"`
}

// DepartmentDetailResponse 科室详情响应
type DepartmentDetailResponse struct {
	ID                string     `json:"id"`
	Principal         string     `json:"principal"`
	Name              string     `json:"name"`
	Phone             string     `json:"phone"`
	Created           string     `json:"created"`
	Type              int        `json:"type"`
	MainMedicalName   string     `json:"mainMedicalName"`
	SecondMedicalName string     `json:"secondMedicalName"`
	Employees         []Employee `json:"employees"`
}

// Employee 员工信息
type Employee struct {
	EmployeeID string `json:"employeeId"`
	Name       string `json:"name"`
	Mobile     string `json:"mobile"`
}

// EmployeeDetail 员工详细信息
type EmployeeDetail struct {
	ID                 string         `json:"id"`
	Position           string         `json:"position"`
	HeadImgURL         string         `json:"headImgUrl"`
	Name               string         `json:"name"`
	Mobile             string         `json:"mobile"`
	Sex                string         `json:"sex"`
	Birthday           string         `json:"birthday"`
	Role               string         `json:"role"`
	Introduction       string         `json:"introduction"`
	NationalDoctorCode string         `json:"nationalDoctorCode"`
	PracticeImgURL     string         `json:"practiceImgUrl"`
	GoodAt             string         `json:"goodAt"`
	Roles              []int          `json:"roles"`
	DoctorTags         []string       `json:"doctorTags"`
	PracticeInfos      []PracticeInfo `json:"practiceInfos"`
}

// PracticeInfo 执业信息
type PracticeInfo struct {
	Type  string `json:"type"`
	Title string `json:"title"`
}

// EmployeeListResponse 员工列表响应
type EmployeeListResponse struct {
	EmployeeList []EmployeeDetail `json:"employeeList"`
}

// ClinicInfo 诊所信息
type ClinicInfo struct {
	ID            string `json:"id"`
	ParentID      string `json:"parentId"`
	HisType       int    `json:"hisType"`
	Name          string `json:"name"`
	Phone         string `json:"phone"`
	Category      string `json:"category"`
	ProvinceName  string `json:"provinceName"`
	CityName      string `json:"cityName"`
	DistrictName  string `json:"districtName"`
	AddressDetail string `json:"addressDetail"`
}

// DoctorInfo 医生信息
type DoctorInfo struct {
	ID             string       `json:"id"`
	Mobile         string       `json:"mobile"`
	Name           string       `json:"name"`
	Sex            string       `json:"sex"`
	HeadImgURL     string       `json:"headImgUrl"`
	Introduction   string       `json:"introduction"`
	PracticeImgURL string       `json:"practiceImgUrl"`
	DoctorPageURL  string       `json:"doctorPageUrl"`
	DoctorTags     []string     `json:"doctorTags"`
	Departments    []Department `json:"departments,omitempty"`
}

// PaginatedResult 分页结果
type PaginatedResult struct {
	Rows   []DoctorInfo `json:"rows"`
	Total  int          `json:"total"`
	Offset int          `json:"offset"`
	Limit  int          `json:"limit"`
}

// PatientSummary 患者摘要信息
type PatientSummary struct {
	ID       string  `json:"id"`
	Name     string  `json:"name"`
	Mobile   string  `json:"mobile"`
	Sex      string  `json:"sex"`
	Birthday string  `json:"birthday"`
	Age      AgeInfo `json:"age"`
	IDCard   string  `json:"idCard"`
}

// AgeInfo 年龄信息
type AgeInfo struct {
	Year  int `json:"year"`
	Month int `json:"month"`
	Day   int `json:"day"`
}

// PatientAddress 患者地址信息
type PatientAddress struct {
	AddressProvinceID   string `json:"addressProvinceId"`
	AddressProvinceName string `json:"addressProvinceName"`
	AddressCityID       string `json:"addressCityId"`
	AddressCityName     string `json:"addressCityName"`
	AddressDistrictID   string `json:"addressDistrictId"`
	AddressDistrictName string `json:"addressDistrictName"`
	AddressDetail       string `json:"addressDetail"`
}

// PatientTag 患者标签
type PatientTag struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// PatientMemberTypeInfo 患者会员类型信息
type PatientMemberTypeInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// PatientMemberInfo 患者会员信息
type PatientMemberInfo struct {
	ID             string                `json:"id"`
	Principal      float64               `json:"principal"`
	Present        float64               `json:"present"`
	Created        string                `json:"created"`
	MemberTypeInfo PatientMemberTypeInfo `json:"memberTypeInfo"`
}

// PatientSource 患者来源信息
type PatientSource struct {
	ParentID       string `json:"parentId"`
	ParentName     string `json:"parentName"`
	ID             string `json:"id"`
	Name           string `json:"name"`
	SourceFrom     string `json:"sourceFrom"`
	SourceFromName string `json:"sourceFromName"`
	RelatedType    int    `json:"relatedType"`
	RelatedID      string `json:"relatedId"`
	RelateName     string `json:"relateName"`
}

// ShebaoCardInfo 社保卡信息
type ShebaoCardInfo struct {
	CardNo string `json:"cardNo"`
}

// PatientDetail 患者详细信息
type PatientDetail struct {
	CreatedClinicID   string            `json:"createdClinicId"`
	ID                string            `json:"id"`
	CreatedClinicName string            `json:"createdClinicName"`
	Name              string            `json:"name"`
	Created           string            `json:"created"`
	Mobile            string            `json:"mobile"`
	PatientSource     PatientSource     `json:"patientSource"`
	Sex               string            `json:"sex"`
	Birthday          string            `json:"birthday"`
	SN                string            `json:"sn"`
	Age               AgeInfo           `json:"age"`
	IDCard            string            `json:"idCard"`
	Profession        string            `json:"profession"`
	Company           string            `json:"company"`
	Remark            string            `json:"remark"`
	Address           PatientAddress    `json:"address"`
	ShebaoCardInfo    ShebaoCardInfo    `json:"shebaoCardInfo"`
	Tags              []PatientTag      `json:"tags"`
	MemberInfo        PatientMemberInfo `json:"memberInfo"`
}

// PatientListResponse 患者列表响应
type PatientListResponse struct {
	Rows []PatientSummary `json:"rows"`
}

// PatientQueryResponse 患者查询响应
type PatientQueryResponse struct {
	Rows []PatientSummary `json:"rows"`
}

// PaginatedPatientResult 患者分页结果
type PaginatedPatientResult struct {
	Rows   []PatientSummary `json:"rows"`
	Total  int              `json:"total"`
	Offset int              `json:"offset"`
	Limit  int              `json:"limit"`
}

// PatientAttachment 患者附件
type PatientAttachment struct {
	ID               string `json:"id"`
	URL              string `json:"url"`
	FileName         string `json:"fileName"`
	DisplayName      string `json:"displayName"`
	Created          string `json:"created"`
	BusinessCategory int    `json:"businessCategory"`
}

// PatientAttachmentListResponse 患者附件列表响应
type PatientAttachmentListResponse struct {
	Rows   []PatientAttachment `json:"rows"`
	Total  int                 `json:"total"`
	Offset int                 `json:"offset"`
	Limit  int                 `json:"limit"`
}

// AddPatientAttachmentRequest 添加患者附件请求
type AddPatientAttachmentRequest struct {
	URL              string `json:"url"`
	FileName         string `json:"fileName"`
	DisplayName      string `json:"displayName"`
	BusinessCategory int    `json:"businessCategory"`
}

// AddPatientAttachmentsRequest 批量添加患者附件请求
type AddPatientAttachmentsRequest struct {
	Attachments []AddPatientAttachmentRequest `json:"attachments"`
}

// AddPatientAttachmentsResponse 添加患者附件响应
type AddPatientAttachmentsResponse struct {
	Attachments []PatientAttachment `json:"attachments"`
}

// OperationResponse 操作响应结果
type OperationResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// PatientSourceType 患者来源类型
type PatientSourceType struct {
	ID          string              `json:"id"`
	Name        string              `json:"name"`
	RelatedType int                 `json:"relatedType"`
	RelatedID   string              `json:"relatedId"`
	Children    []PatientSourceType `json:"children"`
}

// PatientSourceTypesResponse 患者来源类型响应
type PatientSourceTypesResponse struct {
	List []PatientSourceType `json:"list"`
}

// PatientMemberTypeResponse 患者会员类型响应
type PatientMemberTypeResponse struct {
	MemberTypes []PatientMemberTypeInfo `json:"memberTypes"`
}

// CreatePatientRequest 创建患者请求
type CreatePatientRequest struct {
	Name         string         `json:"name"`
	Mobile       string         `json:"mobile"`
	Sex          string         `json:"sex"`
	Birthday     string         `json:"birthday"`
	SourceID     string         `json:"sourceId"`
	SourceFromID string         `json:"sourceFromId"`
	IDCard       string         `json:"idCard"`
	PastHistory  string         `json:"pastHistory"`
	Address      PatientAddress `json:"address"`
	SN           string         `json:"sn"`
	Remark       string         `json:"remark"`
	Profession   string         `json:"profession"`
	Company      string         `json:"company"`
	Marital      string         `json:"marital"`
	Weight       string         `json:"weight"`
}

// FamilyMember 家庭成员信息
type FamilyMember struct {
	FamilyRole int     `json:"familyRole"`
	ID         string  `json:"id"`
	Name       string  `json:"name"`
	Relation   string  `json:"relation"`
	Mobile     string  `json:"mobile"`
	Sex        string  `json:"sex"`
	Birthday   string  `json:"birthday"`
	Age        AgeInfo `json:"age"`
	IDCard     string  `json:"idCard"`
}

// FamilyMemberResponse 家庭成员响应
type FamilyMemberResponse struct {
	PatientID      string         `json:"patientId"`
	FamilyRole     int            `json:"familyRole"`
	FamilyPatients []FamilyMember `json:"familyPatients"`
}

// MemberCardPayRequest 会员卡支付请求
type MemberCardPayRequest struct {
	TransactionPatientID string  `json:"transactionPatientId"`
	Amount               float64 `json:"amount"`
	BusinessID           string  `json:"businessId"`
	Password             string  `json:"password"`
	OperatorID           string  `json:"operatorId"`
}

// MemberCardPayResponse 会员卡支付响应
type MemberCardPayResponse struct {
	TransactionID    string  `json:"transactionId"`
	PrincipalBalance float64 `json:"principalBalance"`
	PresentBalance   float64 `json:"presentBalance"`
	Principal        float64 `json:"principal"`
	Present          float64 `json:"present"`
	BusinessID       string  `json:"businessId"`
}

// MemberCardRefundRequest 会员卡退款请求
type MemberCardRefundRequest struct {
	Amount         float64  `json:"amount"`
	TransactionIDs []string `json:"transactionIds"`
	BusinessID     string   `json:"businessId"`
	OperatorID     string   `json:"operatorId"`
}

// 挂号相关结构体
// ScheduleInterval 排班时间段
type ScheduleInterval struct {
	TimeOfDay string         `json:"timeOfDay"`
	Start     string         `json:"start"`
	End       string         `json:"end"`
	List      []ScheduleItem `json:"list"`
}

// ScheduleItem 排班项
type ScheduleItem struct {
	RestCount int    `json:"restCount"`
	TimeOfDay string `json:"timeOfDay"`
	OrderNo   int    `json:"orderNo"`
	Type      int    `json:"type"`
	Start     string `json:"start"`
	End       string `json:"end"`
	Available int    `json:"available"`
}

// DoctorScheduleResponse 医生排班号源详情响应
type DoctorScheduleResponse struct {
	DoctorID          string             `json:"doctorId"`
	DepartmentID      string             `json:"departmentId"`
	WorkingDate       string             `json:"workingDate"`
	DayOfWeek         string             `json:"dayOfWeek"`
	RestCountToday    int                `json:"restCountToday"`
	CanReserve        int                `json:"canReserve"`
	ScheduleIntervals []ScheduleInterval `json:"scheduleIntervals"`
}

// RegistrationProduct 预约项目
type RegistrationProduct struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// RegistrationCreateRequest 创建挂号请求
type RegistrationCreateRequest struct {
	PatientID              string `json:"patientId"`
	DepartmentID           string `json:"departmentId"`
	DoctorID               string `json:"doctorId"`
	OrderNo                int    `json:"orderNo"`
	ReserveDate            string `json:"reserveDate"`
	ReserveStart           string `json:"reserveStart"`
	ReserveEnd             string `json:"reserveEnd"`
	SourceID               string `json:"sourceId"`
	SourceFromID           string `json:"sourceFromId"`
	SourceRemark           string `json:"sourceRemark"`
	RegistrationType       int    `json:"registrationType"`
	RegistrationProductIDs []int  `json:"registrationProductIds"`
}

// RegistrationCreateResponse 创建挂号响应
type RegistrationCreateResponse struct {
	RegistrationSheetID string `json:"registrationSheetId"`
	PatientOrderID      string `json:"patientOrderId"`
}

// RegistrationDetail 挂号详情
type RegistrationDetail struct {
	ClinicName             string                `json:"clinicName"`
	PatientOrderID         string                `json:"patientOrderId"`
	RegistrationSheetID    string                `json:"registrationSheetId"`
	VisitSourceParentID    string                `json:"visitSourceParentId"`
	PatientID              string                `json:"patientId"`
	VisitSourceParentName  string                `json:"visitSourceParentName"`
	DepartmentID           string                `json:"departmentId"`
	VisitSourceID          string                `json:"visitSourceId"`
	DepartmentName         string                `json:"departmentName"`
	VisitSourceName        string                `json:"visitSourceName"`
	DoctorID               string                `json:"doctorId"`
	VisitSourceFromID      string                `json:"visitSourceFromId"`
	DoctorName             string                `json:"doctorName"`
	VisitSourceFromName    string                `json:"visitSourceFromName"`
	OrderNo                string                `json:"orderNo"`
	VisitSourceRelatedType int                   `json:"visitSourceRelatedType"`
	ReserveDate            string                `json:"reserveDate"`
	VisitSourceRelatedID   string                `json:"visitSourceRelatedId"`
	ReserveTime            string                `json:"reserveTime"`
	VisitSourceRelateName  string                `json:"visitSourceRelateName"`
	IsReserved             int                   `json:"isReserved"`
	VisitSourceRemark      string                `json:"visitSourceRemark"`
	RegistrationProducts   []RegistrationProduct `json:"registrationProducts"`
	Status                 int                   `json:"status"`
	SignIn                 int                   `json:"signIn"`
	ConsultingRoomID       string                `json:"consultingRoomId"`
	ConsultingRoomName     string                `json:"consultingRoomName"`
	Type                   int                   `json:"type"`
	PayStatus              int                   `json:"payStatus"`
	Code                   int                   `json:"code"`
	RegistrationType       int                   `json:"registrationType"`
	Created                string                `json:"created"`
}

// RegistrationCancelResponse 取消挂号响应
type RegistrationCancelResponse struct {
	IsSuccess int    `json:"isSuccess"`
	Tips      string `json:"tips"`
}

// RemarkTemplate 预约备注模板
type RemarkTemplate struct {
	ID               string `json:"id"`
	RegistrationType int    `json:"registrationType"`
	Content          string `json:"content"`
	Sort             int    `json:"sort"`
	DisableModify    int    `json:"disableModify"`
	DisableDelete    int    `json:"disableDelete"`
}

// RemarkTemplateResponse 预约备注模板响应
type RemarkTemplateResponse struct {
	Rows []RemarkTemplate `json:"rows"`
}

// ProductAttachment 项目附件
type ProductAttachment struct {
	URL      string `json:"url"`
	FileName string `json:"fileName"`
	FileSize int    `json:"fileSize"`
}

// RegistrationProductDetail 预约项目详情
type RegistrationProductDetail struct {
	ID          string              `json:"id"`
	Introduce   string              `json:"introduce"`
	Name        string              `json:"name"`
	Price       float64             `json:"price"`
	Attachments []ProductAttachment `json:"attachments"`
}

// RegistrationProductResponse 预约项目响应
type RegistrationProductResponse struct {
	RegistrationProducts []RegistrationProductDetail `json:"registrationProducts"`
}

// ReserveSectionStatus 预约日期状态
type ReserveSectionStatus struct {
	Date   string `json:"date"`
	Status int    `json:"status"`
}

// ReserveSectionStatusResponse 科室可预约时间段状态响应
type ReserveSectionStatusResponse struct {
	ReserveSectionStatusList []ReserveSectionStatus `json:"reserveSectionStatusList"`
}

// RegistrationAvailableProject 可预约挂号项目
type RegistrationAvailableProject struct {
	ID             int     `json:"id"`
	Name           string  `json:"name"`
	DepartmentID   string  `json:"departmentId"`
	DepartmentName string  `json:"departmentName"`
	Description    string  `json:"description"`
	Price          float64 `json:"price"`
	Status         int     `json:"status"` // 1: 可用，0: 不可用
}

// RegistrationAvailableProjectsResponse 可预约挂号项目响应
type RegistrationAvailableProjectsResponse struct {
	AvailableProjects []RegistrationAvailableProject `json:"availableProjects"`
}

// ProjectAvailabilityStatus 项目可用状态
type ProjectAvailabilityStatus struct {
	Date   string `json:"date"`
	Status int    `json:"status"` // 1: 可用，0: 不可用
}

// RegistrationProjectAvailabilityResponse 可预约挂号项目每日可用状态响应
type RegistrationProjectAvailabilityResponse struct {
	ProjectID          int                         `json:"projectId"`
	AvailabilityStatus []ProjectAvailabilityStatus `json:"availabilityStatus"`
}

// ShiftDoctor 排班医生
type ShiftDoctor struct {
	DoctorID string `json:"doctorId"`
	Status   int    `json:"status"`
}

// DayShift 日班次
type DayShift struct {
	Start        string        `json:"start"`
	End          string        `json:"end"`
	TimeOfDay    string        `json:"timeOfDay"`
	Status       int           `json:"status"`
	ShiftDoctors []ShiftDoctor `json:"shiftDoctors"`
}

// RegistrationCategoryDaysShift 预约类别日班次
type RegistrationCategoryDaysShift struct {
	Status               int        `json:"status"`
	RegistrationCategory int        `json:"registrationCategory"`
	DayShifts            []DayShift `json:"dayShifts"`
}

// RegistrationCategoryDaysShiftResponse 预约类别日班次响应
type RegistrationCategoryDaysShiftResponse struct {
	RegistrationCategoryDaysShifts []RegistrationCategoryDaysShift `json:"registrationCategoryDaysShifts"`
}

// RegistrationSummary 预约摘要信息
type RegistrationSummary struct {
	ID                   string                `json:"id"`
	PatientOrderID       string                `json:"patientOrderId"`
	DepartmentID         string                `json:"departmentId"`
	DepartmentName       string                `json:"departmentName"`
	DoctorID             string                `json:"doctorId"`
	DoctorName           string                `json:"doctorName"`
	ReserveDate          string                `json:"reserveDate"`
	ReserveStart         string                `json:"reserveStart"`
	ReserveEnd           string                `json:"reserveEnd"`
	Type                 int                   `json:"type"`
	Status               int                   `json:"status"`
	PayStatus            int                   `json:"payStatus"`
	ConsultingRoomID     string                `json:"consultingRoomId"`
	ConsultingRoomName   string                `json:"consultingRoomName"`
	RegistrationProducts []RegistrationProduct `json:"registrationProducts"`
	Created              string                `json:"created"`
}

// PaginatedRegistrationResult 预约分页结果
type PaginatedRegistrationResult struct {
	Rows   []RegistrationSummary `json:"rows"`
	Total  int                   `json:"total"`
	Offset int                   `json:"offset"`
	Limit  int                   `json:"limit"`
}

// DoctorShift 医生排班信息
type DoctorShift struct {
	ShowInWeClinic int        `json:"showInWeClinic"`
	Doctor         DoctorInfo `json:"doctor"`
	Status         int        `json:"status"`
}

// DoctorShiftResponse 医生排班响应
type DoctorShiftResponse struct {
	DoctorShifts []DoctorShift `json:"doctorShifts"`
}

// ShiftItem 班次项
type ShiftItem struct {
	OrderNo    int    `json:"orderNo"`
	Start      string `json:"start"`
	End        string `json:"end"`
	TimeOfDay  string `json:"timeOfDay"`
	Count      int    `json:"count"`
	TotalCount int    `json:"totalCount"`
}

// DayShiftDetail 日班次详情
type DayShiftDetail struct {
	Shifts     []ShiftItem `json:"shifts"`
	RestCount  int         `json:"restCount"`
	TotalCount int         `json:"totalCount"`
	Status     int         `json:"status"`
	Start      string      `json:"start"`
	End        string      `json:"end"`
	TimeOfDay  string      `json:"timeOfDay"`
}

// RegistrationCategoryDayShift 预约类别日班次
type RegistrationCategoryDayShift struct {
	DepartmentID         string           `json:"departmentId"`
	RegistrationCategory int              `json:"registrationCategory"`
	RestCount            int              `json:"restCount"`
	TotalCount           int              `json:"totalCount"`
	Status               int              `json:"status"`
	DayShifts            []DayShiftDetail `json:"dayShifts"`
}

// DepartmentSchedule 科室排班信息
type DepartmentSchedule struct {
	ID                            string                         `json:"id"`
	Name                          string                         `json:"name"`
	RestCount                     int                            `json:"restCount"`
	TotalCount                    int                            `json:"totalCount"`
	Status                        int                            `json:"status"`
	Fee                           float64                        `json:"fee"`
	RegisterStartTime             string                         `json:"registerStartTime"`
	RegistrationCategoryDayShifts []RegistrationCategoryDayShift `json:"registrationCategoryDayShifts"`
}

// ReserveSection 预约日期信息
type ReserveSection struct {
	Date        string               `json:"date"`
	Departments []DepartmentSchedule `json:"departments"`
}

// DoctorShiftDateResponse 医生号源日期响应
type DoctorShiftDateResponse struct {
	ReserveSections []ReserveSection `json:"reserveSections"`
}

// RegistrationSummaryBasic 挂号单基础信息
type RegistrationSummaryBasic struct {
	PatientOrderID      string `json:"patientOrderId"`
	RegistrationSheetID string `json:"registrationSheetId"`
	PatientID           string `json:"patientId"`
	DepartmentID        string `json:"departmentId"`
	DepartmentName      string `json:"departmentName"`
	DoctorID            string `json:"doctorId"`
	DoctorName          string `json:"doctorName"`
	OrderNo             string `json:"orderNo"`
	ReserveDate         string `json:"reserveDate"`
	ReserveTime         string `json:"reserveTime"`
	IsReserved          int    `json:"isReserved"`
	Status              int    `json:"status"`
	SignIn              int    `json:"signIn"`
	ConsultingRoomID    string `json:"consultingRoomId"`
	ConsultingRoomName  string `json:"consultingRoomName"`
	Type                int    `json:"type"`
	PayStatus           int    `json:"payStatus"`
	Code                int    `json:"code"`
	RegistrationType    int    `json:"registrationType"`
	Created             string `json:"created"`
}

// PaginatedRegistrationBasicResult 挂号单分页基础结果
type PaginatedRegistrationBasicResult struct {
	Rows   []RegistrationSummaryBasic `json:"rows"`
	Total  int                        `json:"total"`
	Offset int                        `json:"offset"`
	Limit  int                        `json:"limit"`
}

// GetDepartmentList 获取科室列表
func (c *AbcYunClient) GetDepartmentList() ([]Department, error) {
	resp, err := c.Get("/api/v2/open-agency/clinics/departments", nil)
	if err != nil {
		return nil, fmt.Errorf("获取科室列表失败: %w", err)
	}

	var response Response
	response.Data = &DepartmentListResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析科室列表响应失败: %w", err)
	}

	departments := response.Data.(*DepartmentListResponse).DepartmentList
	return departments, nil
}

// GetClinicInfo 获取诊所信息
func (c *AbcYunClient) GetClinicInfo() (*ClinicInfo, error) {
	resp, err := c.Get("/api/v2/open-agency/clinics/info", nil)
	if err != nil {
		return nil, fmt.Errorf("获取诊所信息失败: %w", err)
	}

	var response Response
	response.Data = &ClinicInfo{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析诊所信息响应失败: %w", err)
	}

	clinicInfo := response.Data.(*ClinicInfo)
	return clinicInfo, nil
}

// GetDepartmentDetail 获取科室详情
func (c *AbcYunClient) GetDepartmentDetail(departmentID string) (*DepartmentDetailResponse, error) {
	path := fmt.Sprintf("/api/v2/open-agency/clinics/departments/%s", departmentID)
	resp, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取科室详情失败: %w", err)
	}

	var response Response
	response.Data = &DepartmentDetailResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析科室详情响应失败: %w", err)
	}

	departmentDetail := response.Data.(*DepartmentDetailResponse)
	return departmentDetail, nil
}

// GetEmployeeDetail 获取员工详情
func (c *AbcYunClient) GetEmployeeDetail(employeeID string) (*EmployeeDetail, error) {
	path := fmt.Sprintf("/api/v2/open-agency/clinics/employees/%s", employeeID)
	resp, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取员工详情失败: %w", err)
	}

	var response Response
	response.Data = &EmployeeDetail{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析员工详情响应失败: %w", err)
	}

	employeeDetail := response.Data.(*EmployeeDetail)
	return employeeDetail, nil
}

// GetEmployeeList 获取员工列表
func (c *AbcYunClient) GetEmployeeList() ([]EmployeeDetail, error) {
	resp, err := c.Get("/api/v2/open-agency/clinics/employees", nil)
	if err != nil {
		return nil, fmt.Errorf("获取员工列表失败: %w", err)
	}

	var response Response
	response.Data = &EmployeeListResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析员工列表响应失败: %w", err)
	}

	employees := response.Data.(*EmployeeListResponse).EmployeeList
	return employees, nil
}

// GetDoctorByMobile 根据手机号获取医生信息
func (c *AbcYunClient) GetDoctorByMobile(mobile string) (*DoctorInfo, error) {
	queryParams := map[string]string{
		"mobile": mobile,
	}
	resp, err := c.Get("/api/v2/open-agency/doctor/query-by-mobile", queryParams)
	if err != nil {
		return nil, fmt.Errorf("根据手机号获取医生信息失败: %w", err)
	}

	var response Response
	response.Data = &DoctorInfo{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析医生信息响应失败: %w", err)
	}

	doctorInfo := response.Data.(*DoctorInfo)
	return doctorInfo, nil
}

// GetDoctorList 获取医生列表
func (c *AbcYunClient) GetDoctorList(limit, offset int) (*PaginatedResult, error) {
	queryParams := map[string]string{
		"limit":  fmt.Sprintf("%d", limit),
		"offset": fmt.Sprintf("%d", offset),
	}
	resp, err := c.Get("/api/v2/open-agency/doctor/query-list", queryParams)
	if err != nil {
		return nil, fmt.Errorf("获取医生列表失败: %w", err)
	}

	var response Response
	response.Data = &PaginatedResult{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析医生列表响应失败: %w", err)
	}

	paginatedResult := response.Data.(*PaginatedResult)
	return paginatedResult, nil
}

// 客户端平台API封装
// 以下是与HIS对接相关的API函数

// ClientGetDepartmentList 获取科室列表
func ClientGetDepartmentList(clinicID string) ([]byte, error) {
	// 这里假设已经在其他地方创建了全局client
	// 因为这是从外部调用的函数，所以我们返回原始的JSON字节
	client := globalClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	resp, err := client.Get("/api/v2/open-agency/clinics/departments", nil)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// ClientGetDoctorList 获取医生列表
func ClientGetDoctorList(clinicID string, limit, offset int) ([]byte, error) {
	client := globalClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	queryParams := map[string]string{
		"limit":  fmt.Sprintf("%d", limit),
		"offset": fmt.Sprintf("%d", offset),
	}

	resp, err := client.Get("/api/v2/open-agency/doctor/query-list", queryParams)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// ClientGetProductTypes 获取商品分类
func ClientGetProductTypes(clinicID string) ([]byte, error) {
	params := map[string]string{
		"clinicId": clinicID,
	}
	return GetClient().Get("/api/v2/open-agency/products/types", params)
}

// ClientGetProductCustomTypes 获取自定义商品分类
func ClientGetProductCustomTypes(clinicID string) ([]byte, error) {
	params := map[string]string{
		"clinicId": clinicID,
	}
	return GetClient().Get("/api/v2/open-agency/products/custom-types", params)
}

// ClientGetProductList 获取商品列表
func ClientGetProductList(clinicID string, productType, offset, limit int) ([]byte, error) {
	params := map[string]string{
		"clinicId":    clinicID,
		"productType": strconv.Itoa(productType),
		"offset":      strconv.Itoa(offset),
		"limit":       strconv.Itoa(limit),
	}
	return GetClient().Get("/api/v2/open-agency/products", params)
}

// ClientGetProductDetail 获取商品详情
func ClientGetProductDetail(clinicID, productID string) ([]byte, error) {
	path := fmt.Sprintf("/api/v2/open-agency/products/%s", productID)
	params := map[string]string{
		"clinicId": clinicID,
	}
	return GetClient().Get(path, params)
}

// ClientQueryProductStock 查询商品库存
func ClientQueryProductStock(clinicID string, productIDs []string) ([]byte, error) {
	// 将商品ID数组转为逗号分隔的字符串
	productIDsStr := ""
	for i, id := range productIDs {
		if i > 0 {
			productIDsStr += ","
		}
		productIDsStr += id
	}

	params := map[string]string{
		"clinicId":   clinicID,
		"productIds": productIDsStr,
	}
	return GetClient().Get("/api/v2/open-agency/products/stock", params)
}

// 全局客户端变量
var globalClient *AbcYunClient

// InitClient 初始化全局客户端
func InitClient(client *AbcYunClient) {
	globalClient = client
}

// GetGlobalClient 获取全局客户端
func GetGlobalClient() *AbcYunClient {
	return globalClient
}

// 患者相关API
// GetPatientList 获取患者分页列表
func (c *AbcYunClient) GetPatientList(date string, limit, offset int) (*PaginatedPatientResult, error) {
	queryParams := map[string]string{
		"date":   date,
		"limit":  fmt.Sprintf("%d", limit),
		"offset": fmt.Sprintf("%d", offset),
	}
	resp, err := c.Get("/api/v2/open-agency/patient/query-list", queryParams)
	if err != nil {
		return nil, fmt.Errorf("获取患者列表失败: %w", err)
	}

	var response Response
	response.Data = &PaginatedPatientResult{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析患者列表响应失败: %w", err)
	}

	result := response.Data.(*PaginatedPatientResult)
	return result, nil
}

// SearchPatients 搜索患者
func (c *AbcYunClient) SearchPatients(mobile, name, sn string) (*PatientQueryResponse, error) {
	queryParams := map[string]string{}
	if mobile != "" {
		queryParams["mobile"] = mobile
	}
	if name != "" {
		queryParams["name"] = name
	}
	if sn != "" {
		queryParams["sn"] = sn
	}

	resp, err := c.Get("/api/v2/open-agency/patient/query", queryParams)
	if err != nil {
		return nil, fmt.Errorf("搜索患者失败: %w", err)
	}

	var response Response
	response.Data = &PatientQueryResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析患者搜索响应失败: %w", err)
	}

	result := response.Data.(*PatientQueryResponse)
	return result, nil
}

// GetPatientDetail 获取患者详情
func (c *AbcYunClient) GetPatientDetail(patientID string) (*PatientDetail, error) {
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s", patientID)
	resp, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取患者详情失败: %w", err)
	}

	var response Response
	response.Data = &PatientDetail{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析患者详情响应失败: %w", err)
	}

	result := response.Data.(*PatientDetail)
	return result, nil
}

// GetPatientSourceTypes 获取患者来源分类列表
func (c *AbcYunClient) GetPatientSourceTypes() (*PatientSourceTypesResponse, error) {
	resp, err := c.Get("/api/v2/open-agency/patient/source/types", nil)
	if err != nil {
		return nil, fmt.Errorf("获取患者来源分类列表失败: %w", err)
	}

	var response Response
	response.Data = &PatientSourceTypesResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析患者来源分类列表响应失败: %w", err)
	}

	result := response.Data.(*PatientSourceTypesResponse)
	return result, nil
}

// CreatePatient 创建患者
func (c *AbcYunClient) CreatePatient(request *CreatePatientRequest) (*PatientSummary, error) {
	// 添加调试日志
	logx.Infof("创建患者请求参数: %+v", request)

	// 直接传递request对象，不要手动序列化
	// 因为Post方法内部会进行JSON序列化
	resp, err := c.Post("/api/v2/open-agency/patient", request)
	if err != nil {
		return nil, fmt.Errorf("abcyun创建患者失败: %w", err)
	}

	// 添加响应调试日志
	logx.Infof("创建患者API响应: %s", string(resp))

	var response Response
	response.Data = &PatientSummary{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析创建患者响应失败: %w", err)
	}

	result := response.Data.(*PatientSummary)
	return result, nil
}

// UpdatePatient 修改患者
func (c *AbcYunClient) UpdatePatient(patientID string, request *CreatePatientRequest) (*PatientSummary, error) {
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化修改患者请求失败: %w", err)
	}

	path := fmt.Sprintf("/api/v2/open-agency/patient/%s", patientID)
	resp, err := c.Put(path, requestBody)
	if err != nil {
		return nil, fmt.Errorf("修改患者失败: %w", err)
	}

	var response Response
	response.Data = &PatientSummary{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析修改患者响应失败: %w", err)
	}

	result := response.Data.(*PatientSummary)
	return result, nil
}

// GetPatientFamilyMembers 获取患者家庭成员
func (c *AbcYunClient) GetPatientFamilyMembers(patientID string) (*FamilyMemberResponse, error) {
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s/family-member", patientID)
	resp, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取患者家庭成员失败: %w", err)
	}

	var response Response
	response.Data = &FamilyMemberResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析患者家庭成员响应失败: %w", err)
	}

	result := response.Data.(*FamilyMemberResponse)
	return result, nil
}

// GetPatientAttachments 获取患者附件
func (c *AbcYunClient) GetPatientAttachments(patientID string, limit, offset int) (*PatientAttachmentListResponse, error) {
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s/attachments", patientID)
	queryParams := map[string]string{
		"limit":  fmt.Sprintf("%d", limit),
		"offset": fmt.Sprintf("%d", offset),
	}

	resp, err := c.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("获取患者附件失败: %w", err)
	}

	var response Response
	response.Data = &PatientAttachmentListResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析患者附件响应失败: %w", err)
	}

	result := response.Data.(*PatientAttachmentListResponse)
	return result, nil
}

// AddPatientAttachments 添加患者附件
func (c *AbcYunClient) AddPatientAttachments(patientID string, request *AddPatientAttachmentsRequest) (*AddPatientAttachmentsResponse, error) {
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s/attachments", patientID)
	resp, err := c.Post(path, request)
	if err != nil {
		return nil, fmt.Errorf("添加患者附件失败: %w", err)
	}

	var response Response
	response.Data = &AddPatientAttachmentsResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析添加患者附件响应失败: %w", err)
	}

	result := response.Data.(*AddPatientAttachmentsResponse)
	return result, nil
}

// DeletePatientAttachment 删除患者附件
func (c *AbcYunClient) DeletePatientAttachment(patientID, attachmentID string) (*OperationResponse, error) {
	path := fmt.Sprintf("/api/v2/open-agency/patient/%s/attachments/%s", patientID, attachmentID)
	resp, err := c.Delete(path, nil)
	if err != nil {
		return nil, fmt.Errorf("删除患者附件失败: %w", err)
	}

	var response Response
	response.Data = &OperationResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析删除患者附件响应失败: %w", err)
	}

	result := response.Data.(*OperationResponse)
	return result, nil
}

// GetPatientMemberTypes 获取患者会员类型列表
func (c *AbcYunClient) GetPatientMemberTypes() (*PatientMemberTypeResponse, error) {
	resp, err := c.Get("/api/v2/open-agency/patient/members/types", nil)
	if err != nil {
		return nil, fmt.Errorf("获取患者会员类型列表失败: %w", err)
	}

	var response Response
	response.Data = &PatientMemberTypeResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析患者会员类型列表响应失败: %w", err)
	}

	result := response.Data.(*PatientMemberTypeResponse)
	return result, nil
}

// MemberCardPay 会员卡支付
func (c *AbcYunClient) MemberCardPay(memberCardID string, request *MemberCardPayRequest) (*MemberCardPayResponse, error) {
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化会员卡支付请求失败: %w", err)
	}

	path := fmt.Sprintf("/api/v2/open-agency/patient/members/%s/pay", memberCardID)
	resp, err := c.Put(path, requestBody)
	if err != nil {
		return nil, fmt.Errorf("会员卡支付失败: %w", err)
	}

	var response Response
	response.Data = &MemberCardPayResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析会员卡支付响应失败: %w", err)
	}

	result := response.Data.(*MemberCardPayResponse)
	return result, nil
}

// MemberCardRefund 会员卡退款
func (c *AbcYunClient) MemberCardRefund(memberCardID string, request *MemberCardRefundRequest) (*MemberCardPayResponse, error) {
	requestBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化会员卡退款请求失败: %w", err)
	}

	path := fmt.Sprintf("/api/v2/open-agency/patient/members/%s/refund", memberCardID)
	resp, err := c.Put(path, requestBody)
	if err != nil {
		return nil, fmt.Errorf("会员卡退款失败: %w", err)
	}

	var response Response
	response.Data = &MemberCardPayResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析会员卡退款响应失败: %w", err)
	}

	result := response.Data.(*MemberCardPayResponse)
	return result, nil
}

// 挂号相关API
// GetDoctorSchedule 获取科室医生的号源详情
func (c *AbcYunClient) GetDoctorSchedule(doctorID, workingDate string, departmentID string, registrationType int) (*DoctorScheduleResponse, error) {
	path := fmt.Sprintf("/api/v2/open-agency/registration/doctor/%s", doctorID)
	queryParams := map[string]string{
		"workingDate":      workingDate,
		"registrationType": fmt.Sprintf("%d", registrationType),
	}

	if departmentID != "" {
		queryParams["departmentId"] = departmentID
	}

	resp, err := c.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("获取科室医生的号源详情失败: %w", err)
	}

	var response Response
	response.Data = &DoctorScheduleResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析科室医生的号源详情响应失败: %w", err)
	}

	result := response.Data.(*DoctorScheduleResponse)
	return result, nil
}

// CreateRegistration 创建挂号
func (c *AbcYunClient) CreateRegistration(request *RegistrationCreateRequest) (*RegistrationCreateResponse, error) {
	resp, err := c.Post("/api/v2/open-agency/registration", request)
	if err != nil {
		return nil, fmt.Errorf("创建挂号失败: %w", err)
	}

	var response Response
	response.Data = &RegistrationCreateResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析创建挂号响应失败: %w", err)
	}

	result := response.Data.(*RegistrationCreateResponse)
	return result, nil
}

// GetRegistrationDetail 获取挂号详情
func (c *AbcYunClient) GetRegistrationDetail(registrationSheetID string) (*RegistrationDetail, error) {
	path := fmt.Sprintf("/api/v2/open-agency/registration/%s", registrationSheetID)
	resp, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取挂号详情失败: %w", err)
	}

	var response Response
	response.Data = &RegistrationDetail{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析挂号详情响应失败: %w", err)
	}

	result := response.Data.(*RegistrationDetail)
	return result, nil
}

// GetRegistrationDetailByPatientOrderID 通过就诊单ID获取挂号详情
func (c *AbcYunClient) GetRegistrationDetailByPatientOrderID(patientOrderID string) (*RegistrationDetail, error) {
	path := fmt.Sprintf("/api/v2/open-agency/registration/by-patient-order-id/%s", patientOrderID)
	resp, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("通过就诊单ID获取挂号详情失败: %w", err)
	}

	var response Response
	response.Data = &RegistrationDetail{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析挂号详情响应失败: %w", err)
	}

	result := response.Data.(*RegistrationDetail)
	return result, nil
}

// CancelRegistration 取消挂号
func (c *AbcYunClient) CancelRegistration(registrationSheetID string) (*RegistrationCancelResponse, error) {
	path := fmt.Sprintf("/api/v2/open-agency/registration/%s/cancel", registrationSheetID)
	resp, err := c.Put(path, nil)
	if err != nil {
		return nil, fmt.Errorf("取消挂号失败: %w", err)
	}

	var response Response
	response.Data = &RegistrationCancelResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析取消挂号响应失败: %w", err)
	}

	result := response.Data.(*RegistrationCancelResponse)
	return result, nil
}

// GetPatientRegistrations 查询患者预约列表
func (c *AbcYunClient) GetPatientRegistrations(patientID string, beginDate, endDate string, registrationType, limit, offset int) (*PaginatedRegistrationResult, error) {
	path := fmt.Sprintf("/api/v2/open-agency/registration/patient/%s", patientID)
	queryParams := map[string]string{
		"registrationType": fmt.Sprintf("%d", registrationType),
		"limit":            fmt.Sprintf("%d", limit),
		"offset":           fmt.Sprintf("%d", offset),
	}

	if beginDate != "" {
		queryParams["beginDate"] = beginDate
	}
	if endDate != "" {
		queryParams["endDate"] = endDate
	}

	resp, err := c.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("查询患者预约列表失败: %w", err)
	}

	var response Response
	response.Data = &PaginatedRegistrationResult{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析患者预约列表响应失败: %w", err)
	}

	result := response.Data.(*PaginatedRegistrationResult)
	return result, nil
}

// GetRegistrations 查询挂号单列表
func (c *AbcYunClient) GetRegistrations(registrationType int, reserveDate string, limit, offset int) (*PaginatedRegistrationBasicResult, error) {
	queryParams := map[string]string{
		"registrationType": fmt.Sprintf("%d", registrationType),
		"limit":            fmt.Sprintf("%d", limit),
		"offset":           fmt.Sprintf("%d", offset),
	}

	if reserveDate != "" {
		queryParams["reserveDate"] = reserveDate
	}

	resp, err := c.Get("/api/v2/open-agency/registration", queryParams)
	if err != nil {
		return nil, fmt.Errorf("查询挂号单列表失败: %w", err)
	}

	var response Response
	response.Data = &PaginatedRegistrationBasicResult{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析挂号单列表响应失败: %w", err)
	}

	result := response.Data.(*PaginatedRegistrationBasicResult)
	return result, nil
}

// GetRegistrationAvailableProjects 查询可预约挂号项目
func (c *AbcYunClient) GetRegistrationAvailableProjects(registrationType int, departmentID string) (*RegistrationAvailableProjectsResponse, error) {
	if c == nil {
		return nil, fmt.Errorf("AbcYunClient is nil")
	}

	queryParams := map[string]string{
		"registrationType": fmt.Sprintf("%d", registrationType),
	}

	if departmentID != "" {
		queryParams["departmentId"] = departmentID
	}

	resp, err := c.Get("/api/v2/open-agency/registration/available-projects", queryParams)
	if err != nil {
		return nil, fmt.Errorf("查询可预约挂号项目失败: %w", err)
	}

	var response Response
	response.Data = &RegistrationAvailableProjectsResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析可预约挂号项目响应失败: %w", err)
	}

	result := response.Data.(*RegistrationAvailableProjectsResponse)
	return result, nil
}

// GetRegistrationProjectAvailability 查询可预约挂号项目每日可用状态
func (c *AbcYunClient) GetRegistrationProjectAvailability(projectID int, beginDate, endDate string, registrationType int) (*RegistrationProjectAvailabilityResponse, error) {
	if c == nil {
		return nil, fmt.Errorf("AbcYunClient is nil")
	}

	path := fmt.Sprintf("/api/v2/open-agency/registration/project/%d/availability", projectID)
	queryParams := map[string]string{
		"beginDate":        beginDate,
		"endDate":          endDate,
		"registrationType": fmt.Sprintf("%d", registrationType),
	}

	resp, err := c.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("查询可预约挂号项目每日可用状态失败: %w", err)
	}

	var response Response
	response.Data = &RegistrationProjectAvailabilityResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析可预约挂号项目每日可用状态响应失败: %w", err)
	}

	result := response.Data.(*RegistrationProjectAvailabilityResponse)
	return result, nil
}

// GetDoctorRegistrationProducts 查询医生可预约项目列表
func (c *AbcYunClient) GetDoctorRegistrationProducts(doctorID string) (*RegistrationProductResponse, error) {
	if c == nil {
		return nil, fmt.Errorf("AbcYunClient is nil")
	}

	path := fmt.Sprintf("/api/v2/open-agency/registration/doctor/%s/product", doctorID)
	resp, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("查询医生可预约项目列表失败: %w", err)
	}

	var response Response
	response.Data = &RegistrationProductResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析医生可预约项目列表响应失败: %w", err)
	}

	result := response.Data.(*RegistrationProductResponse)
	return result, nil
}

// GetProductDaysShifts 查询项目指定日期排班信息
func (c *AbcYunClient) GetProductDaysShifts(registrationProductID int, reserveDate string) (*RegistrationCategoryDaysShiftResponse, error) {
	if c == nil {
		return nil, fmt.Errorf("AbcYunClient is nil")
	}

	path := fmt.Sprintf("/api/v2/open-agency/registration/product/%d/days-shifts", registrationProductID)
	queryParams := map[string]string{
		"reserveDate": reserveDate,
	}

	resp, err := c.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("查询项目指定日期排班信息失败: %w", err)
	}

	var response Response
	response.Data = &RegistrationCategoryDaysShiftResponse{}
	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析项目指定日期排班信息响应失败: %w", err)
	}

	result := response.Data.(*RegistrationCategoryDaysShiftResponse)
	return result, nil
}
