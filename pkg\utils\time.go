package utils

import (
	"time"
)

// GetCurrentTime 获取当前时间
func GetCurrentTime() time.Time {
	return time.Now()
}

// GetFutureTime 获取未来时间，参数为小时数
func GetFutureTime(hours int) time.Time {
	return time.Now().Add(time.Hour * time.Duration(hours))
}

// GetFutureTimeByDays 获取未来时间，参数为天数
func GetFutureTimeByDays(days int) time.Time {
	return time.Now().AddDate(0, 0, days)
}

// FormatTime 格式化时间为字符串
func FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// ParseTime 解析时间字符串为时间对象
func ParseTime(timeStr string) (time.Time, error) {
	return time.Parse("2006-01-02 15:04:05", timeStr)
}

// GetStartOfDay 获取当天开始时间
func GetStartOfDay(t time.Time) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, t.Location())
}

// GetEndOfDay 获取当天结束时间
func GetEndOfDay(t time.Time) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 23, 59, 59, 999999999, t.Location())
}

// GetStartOfMonth 获取当月开始时间
func GetStartOfMonth(t time.Time) time.Time {
	year, month, _ := t.Date()
	return time.Date(year, month, 1, 0, 0, 0, 0, t.Location())
}

// GetEndOfMonth 获取当月结束时间
func GetEndOfMonth(t time.Time) time.Time {
	year, month, _ := t.Date()
	// 获取下个月的第一天，然后减去一纳秒
	nextMonth := time.Date(year, month+1, 1, 0, 0, 0, 0, t.Location())
	return nextMonth.Add(-time.Nanosecond)
}

// GetStartOfYear 获取当年开始时间
func GetStartOfYear(t time.Time) time.Time {
	year, _, _ := t.Date()
	return time.Date(year, 1, 1, 0, 0, 0, 0, t.Location())
}

// GetEndOfYear 获取当年结束时间
func GetEndOfYear(t time.Time) time.Time {
	year, _, _ := t.Date()
	return time.Date(year, 12, 31, 23, 59, 59, 999999999, t.Location())
}

// IsSameDay 判断两个时间是否同一天
func IsSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

// IsSameMonth 判断两个时间是否同一月
func IsSameMonth(t1, t2 time.Time) bool {
	y1, m1, _ := t1.Date()
	y2, m2, _ := t2.Date()
	return y1 == y2 && m1 == m2
}

// IsSameYear 判断两个时间是否同一年
func IsSameYear(t1, t2 time.Time) bool {
	y1, _, _ := t1.Date()
	y2, _, _ := t2.Date()
	return y1 == y2
}

// GetDaysBetween 获取两个时间之间的天数
func GetDaysBetween(t1, t2 time.Time) int {
	// 先将两个时间转为同一天的 00:00:00
	start := GetStartOfDay(t1)
	end := GetStartOfDay(t2)

	// 计算相差的小时数，然后除以24
	hours := end.Sub(start).Hours()
	return int(hours / 24)
}

// GetMonthsBetween 获取两个时间之间的月数（按自然月计算）
func GetMonthsBetween(t1, t2 time.Time) int {
	// 确保 t1 < t2
	if t1.After(t2) {
		t1, t2 = t2, t1
	}

	// 计算年月差值
	y1, m1, _ := t1.Date()
	y2, m2, _ := t2.Date()

	months := (y2-y1)*12 + int(m2-m1)

	// 如果 t2 的日期小于 t1 的日期，需要减1个月
	_, _, d1 := t1.Date()
	_, _, d2 := t2.Date()
	if d2 < d1 {
		months--
	}

	return months
}

// GetYearsBetween 获取两个时间之间的年数
func GetYearsBetween(t1, t2 time.Time) int {
	// 确保 t1 < t2
	if t1.After(t2) {
		t1, t2 = t2, t1
	}

	// 计算年差值
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()

	years := y2 - y1

	// 如果 t2 的月日小于 t1 的月日，需要减1年
	if m2 < m1 || (m2 == m1 && d2 < d1) {
		years--
	}

	return years
}
