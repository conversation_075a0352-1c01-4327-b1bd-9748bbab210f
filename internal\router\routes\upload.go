package routes

import (
	"net/http"
	"yekaitai/internal/middleware"
	"yekaitai/internal/svc"
	"yekaitai/pkg/upload"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterUploadRoutes 注册后台管理系统的上传路由
func RegisterUploadRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 确保上传处理器已初始化
	if upload.DefaultUploadHandler == nil {
		upload.InitDefaultUploadHandler()
	}

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 注册需要认证的上传路由
	server.AddRoutes(
		[]rest.Route{
			// 通用文件上传
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/upload/file",
				Handler: adminAuthWrapper(upload.UploadFileHandler),
			},
			// 图片上传
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/upload/image",
				Handler: adminAuthWrapper(upload.UploadImageHandler),
			},
			// 文档上传
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/upload/document",
				Handler: adminAuthWrapper(upload.UploadDocumentHandler),
			},
			// 视频上传(大文件由前端直接分片上传)
			// {
			// 	Method:  http.MethodPost,
			// 	Path:    "/api/admin/upload/video",
			// 	Handler: adminAuthWrapper(upload.UploadVideoHandler),
			// },
		},
	)
}
