package wanliniu

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// Service 万里牛服务层
type Service struct {
	client *Client
}

// NewService 创建万里牛服务实例
func NewService() *Service {
	return &Service{}
}

// GetGoodsResponse 商品查询响应结构（用于对外接口）
type GetGoodsResponse struct {
	Code    int           `json:"code"`
	Message string        `json:"message"`
	Data    []GoodsSimple `json:"data"`
	Total   int           `json:"total"`
}

// GoodsSimple 简化的商品信息（用于对外接口）
type GoodsSimple struct {
	GoodsCode    string       `json:"goods_code"`    // 商品编码
	GoodsName    string       `json:"goods_name"`    // 商品名称
	BrandName    string       `json:"brand_name"`    // 品牌
	CategoryID   string       `json:"category_id"`   // 分类ID
	CategoryName string       `json:"category_name"` // 分类名称
	Status       int          `json:"status"`        // 状态 0停用 1启用
	TagPrice     float64      `json:"tag_price"`     // 吊牌价
	Pic          string       `json:"pic"`           // 主图
	Pics         []string     `json:"pics"`          // 所有图片
	Specs        []SpecSimple `json:"specs"`         // 规格信息
	ModifyTime   time.Time    `json:"modify_time"`   // 修改时间
}

// SpecSimple 简化的规格信息
type SpecSimple struct {
	SpecCode      string    `json:"spec_code"`      // 规格编码
	SpecName      string    `json:"spec_name"`      // 规格名称
	BarCode       string    `json:"bar_code"`       // 条码
	Price         float64   `json:"price"`          // 价格
	MarketPrice   float64   `json:"market_price"`   // 市场价
	StandardPrice float64   `json:"standard_price"` // 标准价
	Status        int       `json:"status"`         // 状态
	Pic           string    `json:"pic"`            // 规格图片
	ModifyTime    time.Time `json:"modify_time"`    // 修改时间
}

// GetGoods 获取商品列表（对外接口）
func (s *Service) GetGoods(ctx context.Context, page, limit int, modifyTime *time.Time) (*GetGoodsResponse, error) {
	if s.client == nil {
		return &GetGoodsResponse{
			Code:    500,
			Message: "万里牛客户端未初始化",
			Data:    []GoodsSimple{},
		}, fmt.Errorf("万里牛客户端未初始化")
	}

	// 构建查询参数
	queryParams := GoodsQueryParams{
		Page:           page,
		Limit:          limit,
		ModifyTime:     modifyTime,
		AllStatus:      true, // 查询所有状态的商品
		NeedProperties: true, // 需要查询商品属性
	}

	// 调用万里牛API
	response, err := s.client.QueryGoods(ctx, queryParams)
	if err != nil {
		logx.Errorf("[WanLiNiu] 获取商品列表失败: %v", err)
		return &GetGoodsResponse{
			Code:    500,
			Message: fmt.Sprintf("获取商品列表失败: %v", err),
			Data:    []GoodsSimple{},
		}, err
	}

	// 转换数据格式
	goods := make([]GoodsSimple, 0, len(response.Data))
	for _, item := range response.Data {
		// 收集图片
		pics := []string{}
		if item.Pic != "" {
			pics = append(pics, item.Pic)
		}
		if item.Pic2 != "" {
			pics = append(pics, item.Pic2)
		}
		if item.Pic3 != "" {
			pics = append(pics, item.Pic3)
		}
		if item.Pic4 != "" {
			pics = append(pics, item.Pic4)
		}

		// 转换规格信息
		specs := make([]SpecSimple, 0, len(item.Specs))
		for _, spec := range item.Specs {
			// 组合规格名称
			specName := strings.TrimSpace(spec.Spec1 + " " + spec.Spec2)
			if specName == "" {
				specName = spec.SpecCode // 如果没有规格名称，使用规格编码
			}

			specs = append(specs, SpecSimple{
				SpecCode:      spec.SpecCode,
				SpecName:      specName,            // 使用组合的规格名称
				BarCode:       spec.BarCode,        // 注意字段名是 barcode
				Price:         spec.SalePrice,      // 使用 SalePrice
				MarketPrice:   spec.WholesalePrice, // 使用 WholesalePrice 作为市场价
				StandardPrice: spec.PrimePrice,     // 使用 PrimePrice 作为标准价
				Status:        spec.Status,
				Pic:           spec.Pic,
				ModifyTime:    time.Now(), // 规格没有修改时间字段，使用当前时间
			})
		}

		goods = append(goods, GoodsSimple{
			GoodsCode:    item.GoodsCode,
			GoodsName:    item.GoodsName,
			BrandName:    item.BrandName,
			CategoryID:   item.CatagoryID,
			CategoryName: item.CatagoryName,
			Status:       item.Status,
			TagPrice:     item.TagPrice,
			Pic:          item.Pic,
			Pics:         pics,
			Specs:        specs,
			ModifyTime:   item.ModifyTime.ToTime(), // 使用ToTime()方法转换
		})
	}

	return &GetGoodsResponse{
		Code:    200,
		Message: "success",
		Data:    goods,
		Total:   len(goods),
	}, nil
}

// GetGoodsByCode 根据商品编码获取商品详情
func (s *Service) GetGoodsByCode(ctx context.Context, goodsCode string) (*GetGoodsResponse, error) {
	if s.client == nil {
		return &GetGoodsResponse{
			Code:    500,
			Message: "万里牛客户端未初始化",
			Data:    []GoodsSimple{},
		}, fmt.Errorf("万里牛客户端未初始化")
	}

	// 构建查询参数
	queryParams := GoodsQueryParams{
		Page:           1,
		Limit:          1,
		ItemCode:       goodsCode,
		AllStatus:      true,
		NeedProperties: true,
	}

	// 调用万里牛API
	response, err := s.client.QueryGoods(ctx, queryParams)
	if err != nil {
		logx.Errorf("[WanLiNiu] 根据编码获取商品失败: %v", err)
		return &GetGoodsResponse{
			Code:    500,
			Message: fmt.Sprintf("获取商品失败: %v", err),
			Data:    []GoodsSimple{},
		}, err
	}

	// 检查是否找到商品
	if len(response.Data) == 0 {
		return &GetGoodsResponse{
			Code:    404,
			Message: "商品不存在",
			Data:    []GoodsSimple{},
		}, nil
	}

	// 转换为简化格式
	goodsResp, err := s.GetGoods(ctx, 1, 1, nil)
	if err != nil {
		return goodsResp, err
	}

	return goodsResp, nil
}

// TestService 测试服务连接
func (s *Service) TestService(ctx context.Context) error {
	if s.client == nil {
		return fmt.Errorf("万里牛客户端未初始化")
	}

	return s.client.TestConnection(ctx)
}

// ===================== 类目管理服务 =====================

// PushCategories 推送类目到万里牛ERP
func (s *Service) PushCategories(ctx context.Context, categories []Category) error {
	if s.client == nil {
		return fmt.Errorf("万里牛客户端未初始化")
	}

	request := CategoryPushRequest{
		Categories: categories,
	}

	_, err := s.client.PushCategories(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] 推送类目失败: %v", err)
		return err
	}

	logx.Infof("[WanLiNiu] 成功推送 %d 个类目到万里牛ERP", len(categories))
	return nil
}

// ===================== 订单管理服务 =====================

// PushTrades 推送订单到万里牛ERP
func (s *Service) PushTrades(ctx context.Context, trades []Trade) error {
	if s.client == nil {
		return fmt.Errorf("万里牛客户端未初始化")
	}

	request := TradesPushRequest{
		Trades: trades,
	}

	_, err := s.client.PushTrades(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] 推送订单失败: %v", err)
		return err
	}

	logx.Infof("[WanLiNiu] 成功推送 %d 个订单到万里牛ERP", len(trades))
	return nil
}

// QueryTradeStatus 查询订单发货状态
func (s *Service) QueryTradeStatus(ctx context.Context, shopNick string, tradeIDs []string) (*BaseResponse, error) {
	if s.client == nil {
		return nil, fmt.Errorf("万里牛客户端未初始化")
	}

	request := TradeStatusRequest{
		ShopType: 100, // B2C平台店铺类型固定为100
		ShopNick: shopNick,
		TradeIDs: strings.Join(tradeIDs, ","),
	}

	response, err := s.client.QueryTradeStatus(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] 查询订单状态失败: %v", err)
		return nil, err
	}

	logx.Infof("[WanLiNiu] 成功查询订单状态，订单数量: %d", len(tradeIDs))
	return response, nil
}

// ===================== 库存管理服务 =====================

// QueryInventoryBatch 批量查询ERP库存
func (s *Service) QueryInventoryBatch(ctx context.Context, page, limit int, start, end *time.Time, storageCode string) (*InventoryResponse, error) {
	if s.client == nil {
		return nil, fmt.Errorf("万里牛客户端未初始化")
	}

	request := InventoryBatchRequest{
		Page:        page,
		Limit:       limit,
		Start:       start,
		End:         end,
		StorageCode: storageCode,
	}

	response, err := s.client.QueryInventoryBatch(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] 批量查询库存失败: %v", err)
		return nil, err
	}

	logx.Infof("[WanLiNiu] 成功批量查询库存，页码: %d, 页大小: %d", page, limit)
	return response, nil
}

// QueryInventorySingle 查询单个商品ERP库存
func (s *Service) QueryInventorySingle(ctx context.Context, itemID, skuID, storageCode string) (*InventoryResponse, error) {
	if s.client == nil {
		return nil, fmt.Errorf("万里牛客户端未初始化")
	}

	// 使用客户端配置中的店铺信息
	shopNick := s.client.ShopNick
	shopType := s.client.ShopType

	// 如果配置中没有设置，使用默认值
	if shopNick == "" {
		shopNick = "叶小医1748511285" // 默认店铺昵称
	}
	if shopType == 0 {
		shopType = 100 // 默认店铺类型
	}

	request := InventorySingleRequest{
		ShopType:    shopType,
		ShopNick:    shopNick,
		ItemID:      itemID,
		SkuID:       skuID,
		StorageCode: storageCode,
	}

	response, err := s.client.QueryInventorySingle(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] 查询单个商品库存失败: %v", err)
		return nil, err
	}

	logx.Infof("[WanLiNiu] 成功查询单个商品库存，商品ID: %s, 店铺: %s", itemID, shopNick)
	return response, nil
}

// QueryInventoryV2BySkuCode 根据规格编码查询库存V2
func (s *Service) QueryInventoryV2BySkuCode(ctx context.Context, skuCode string, storageCode string) (*InventoryQueryV2Response, error) {
	if s.client == nil {
		return nil, fmt.Errorf("万里牛客户端未初始化")
	}

	request := InventoryQueryV2Request{
		PageNo:   1,
		PageSize: 50,
		SkuCode:  skuCode,
		Storage:  storageCode,
	}

	response, err := s.client.QueryInventoryV2(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] V2查询库存失败: %v", err)
		return nil, err
	}

	logx.Infof("[WanLiNiu] 成功V2查询库存，规格编码: %s, 返回记录数: %d", skuCode, len(response.Data))
	return response, nil
}

// QueryInventoryV2ByArticleNumber 根据货号查询库存V2
func (s *Service) QueryInventoryV2ByArticleNumber(ctx context.Context, articleNumber string, storageCode string) (*InventoryQueryV2Response, error) {
	if s.client == nil {
		return nil, fmt.Errorf("万里牛客户端未初始化")
	}

	request := InventoryQueryV2Request{
		PageNo:        1,
		PageSize:      50,
		ArticleNumber: articleNumber,
		Storage:       storageCode,
	}

	response, err := s.client.QueryInventoryV2(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] V2查询库存失败: %v", err)
		return nil, err
	}

	logx.Infof("[WanLiNiu] 成功V2查询库存，货号: %s, 返回记录数: %d", articleNumber, len(response.Data))
	return response, nil
}

// QueryInventoryV2ByBarCode 根据条码查询库存V2
func (s *Service) QueryInventoryV2ByBarCode(ctx context.Context, barCode string, storageCode string) (*InventoryQueryV2Response, error) {
	if s.client == nil {
		return nil, fmt.Errorf("万里牛客户端未初始化")
	}

	request := InventoryQueryV2Request{
		PageNo:   1,
		PageSize: 50,
		BarCode:  barCode,
		Storage:  storageCode,
	}

	response, err := s.client.QueryInventoryV2(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] V2查询库存失败: %v", err)
		return nil, err
	}

	logx.Infof("[WanLiNiu] 成功V2查询库存，条码: %s, 返回记录数: %d", barCode, len(response.Data))
	return response, nil
}

// QueryInventoryV2ByModifyTime 根据修改时间查询库存V2（用于同步）
func (s *Service) QueryInventoryV2ByModifyTime(ctx context.Context, pageNo, pageSize int, modifyTimeStart, modifyTimeEnd string, storageCode string) (*InventoryQueryV2Response, error) {
	if s.client == nil {
		return nil, fmt.Errorf("万里牛客户端未初始化")
	}

	request := InventoryQueryV2Request{
		PageNo:        pageNo,
		PageSize:      pageSize,
		ModifyTime:    &modifyTimeStart,
		ModifyTimeEnd: &modifyTimeEnd,
		Storage:       storageCode,
	}

	response, err := s.client.QueryInventoryV2(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] V2查询库存失败: %v", err)
		return nil, err
	}

	logx.Infof("[WanLiNiu] 成功V2查询库存，页码: %d, 返回记录数: %d", pageNo, len(response.Data))
	return response, nil
}

// ===================== 售后管理服务 =====================

// PushRefund 推送售后单到万里牛ERP
func (s *Service) PushRefund(ctx context.Context, refund RefundInfo) error {
	if s.client == nil {
		return fmt.Errorf("万里牛客户端未初始化")
	}

	request := RefundPushRequest{
		Refund: refund,
	}

	_, err := s.client.PushRefund(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] 推送售后单失败: %v", err)
		return err
	}

	logx.Infof("[WanLiNiu] 成功推送售后单到万里牛ERP，售后单号: %s", refund.RefundID)
	return nil
}

// ===================== 商品分类相关接口 =====================

// GetCategoriesResponse 商品分类查询响应结构（用于对外接口）
type GetCategoriesResponse struct {
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Data    []CategoryDataSimple `json:"data"`
	Total   int                  `json:"total"`
}

// CategoryDataSimple 简化的分类信息（用于对外接口）
type CategoryDataSimple struct {
	CategoryID   string `json:"category_id"`   // 分类ID
	CategoryName string `json:"category_name"` // 分类名称
	HasChild     bool   `json:"has_child"`     // 是否有子节点
	ParentID     string `json:"parent_id"`     // 父节点ID
}

// GetCategories 获取商品分类列表（对外接口）
func (s *Service) GetCategories(ctx context.Context, page, limit int) (*GetCategoriesResponse, error) {
	if s.client == nil {
		return &GetCategoriesResponse{
			Code:    500,
			Message: "万里牛客户端未初始化",
			Data:    []CategoryDataSimple{},
		}, fmt.Errorf("万里牛客户端未初始化")
	}

	// 参数验证
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 50 {
		limit = 10 // 默认每页10条，最大50条
	}

	// 构建查询参数
	queryParams := CategoryQueryParams{
		Page:  page,
		Limit: limit,
	}

	// 调用万里牛API
	response, err := s.client.QueryCategories(ctx, queryParams)
	if err != nil {
		logx.Errorf("[WanLiNiu] 获取商品分类列表失败: %v", err)
		return &GetCategoriesResponse{
			Code:    500,
			Message: fmt.Sprintf("获取商品分类列表失败: %v", err),
			Data:    []CategoryDataSimple{},
		}, err
	}

	// 转换数据格式
	categories := make([]CategoryDataSimple, 0, len(response.Data))
	for _, item := range response.Data {
		categories = append(categories, CategoryDataSimple{
			CategoryID:   item.CatagoryID,
			CategoryName: item.CatagoryName,
			HasChild:     item.HasChild == 1, // 1表示有子节点，0表示无子节点
			ParentID:     item.ParentID,
		})
	}

	return &GetCategoriesResponse{
		Code:    200,
		Message: "success",
		Data:    categories,
		Total:   len(categories),
	}, nil
}

// GetAllCategories 获取所有商品分类（分页获取）
func (s *Service) GetAllCategories(ctx context.Context) (*GetCategoriesResponse, error) {
	if s.client == nil {
		return &GetCategoriesResponse{
			Code:    500,
			Message: "万里牛客户端未初始化",
			Data:    []CategoryDataSimple{},
		}, fmt.Errorf("万里牛客户端未初始化")
	}

	var allCategories []CategoryDataSimple
	page := 1
	limit := 50 // 每页最大条数

	for {
		// 获取当前页的分类
		response, err := s.GetCategories(ctx, page, limit)
		if err != nil {
			return response, err
		}

		// 如果当前页没有数据，说明已经获取完所有分类
		if len(response.Data) == 0 {
			break
		}

		// 添加到总列表中
		allCategories = append(allCategories, response.Data...)

		// 如果返回的数据少于limit，说明已经是最后一页
		if len(response.Data) < limit {
			break
		}

		page++
	}

	return &GetCategoriesResponse{
		Code:    200,
		Message: "success",
		Data:    allCategories,
		Total:   len(allCategories),
	}, nil
}
