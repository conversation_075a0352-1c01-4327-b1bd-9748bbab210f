package queue

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Shopify/sarama"
	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/internal/config"
	"yekaitai/pkg/adapters/medlinker"
	"yekaitai/wx_internal/modules/consultation/model"
)

// StartChatStreamConsumer 启动聊天流消费者
func StartChatStreamConsumer(chatStreamModel model.ChatStreamModel, chatSessionModel model.ChatSessionModel, kafkaConfig config.KafkaConfig) {
	// 使用传入的Kafka配置
	topic := kafkaConfig.Topic
	if topic == "" {
		topic = "medical_chat_stream" // 默认topic，与配置文件保持一致
		logx.Infof("使用默认Kafka Topic: %s", topic)
	} else {
		logx.Infof("使用配置的Kafka Topic: %s", topic)
	}
	brokers := kafkaConfig.Brokers

	// 创建Kafka配置
	saramaConfig := sarama.NewConfig()
	saramaConfig.Consumer.Return.Errors = true
	saramaConfig.Version = sarama.V2_8_1_0 // 匹配 CKafka 2.8.1 版本
	saramaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest

	// 配置SASL认证（如果提供了用户名和密码）
	if kafkaConfig.Username != "" && kafkaConfig.Password != "" {
		saramaConfig.Net.SASL.Enable = true
		saramaConfig.Net.SASL.User = kafkaConfig.Username
		saramaConfig.Net.SASL.Password = kafkaConfig.Password

		// 根据配置选择SASL机制
		switch kafkaConfig.SASLMechanism {
		case "SCRAM-SHA-256":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
		case "SCRAM-SHA-512":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA512
		case "PLAIN", "":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		default:
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		}
		logx.Infof("启用Kafka SASL认证: %s", kafkaConfig.Username)
	}

	// 配置TLS（如果启用）
	if kafkaConfig.EnableTLS {
		saramaConfig.Net.TLS.Enable = true
		logx.Info("启用Kafka TLS连接")
	}

	// 创建消费者
	consumer, err := sarama.NewConsumer(brokers, saramaConfig)
	if err != nil {
		logx.Errorf("创建Kafka消费者失败: %v", err)
		return
	}
	defer func() {
		if err := consumer.Close(); err != nil {
			logx.Errorf("关闭Kafka消费者失败: %v", err)
		}
	}()

	// 订阅主题分区
	partitions, err := consumer.Partitions(topic)
	if err != nil {
		logx.Errorf("获取主题分区失败: %v", err)
		return
	}

	logx.Info("医疗聊天Kafka消费者已启动，等待消息...")

	// 为每个分区创建一个消费者
	for _, partition := range partitions {
		pc, err := consumer.ConsumePartition(topic, partition, sarama.OffsetNewest)
		if err != nil {
			logx.Errorf("创建分区消费者失败: %v", err)
			continue
		}

		// 在goroutine中处理消息
		go func(pc sarama.PartitionConsumer) {
			defer func() {
				if err := pc.Close(); err != nil {
					logx.Errorf("关闭分区消费者失败: %v", err)
				}
			}()

			for msg := range pc.Messages() {
				logx.Infof("收到消息: Topic=%s, Partition=%d, Offset=%d",
					msg.Topic, msg.Partition, msg.Offset)

				// 解析消息
				var kafkaMsg medlinker.KafkaMessage
				if err := json.Unmarshal(msg.Value, &kafkaMsg); err != nil {
					logx.Errorf("解析Kafka消息失败: %v", err)
					continue
				}

				// 保存聊天流数据
				chatStream := &model.ChatStream{
					SessionID:  kafkaMsg.SessionID,
					ChunkIndex: kafkaMsg.ChunkIndex,
					Content:    kafkaMsg.Content,
					IsFinal:    kafkaMsg.IsFinal,
					CreatedAt:  time.Unix(kafkaMsg.Timestamp, 0),
				}

				ctx := context.Background()
				err := chatStreamModel.Insert(ctx, chatStream)
				if err != nil {
					logx.Errorf("保存聊天流数据失败: %v", err)
					continue
				}

				// 如果是最终消息，更新会话数据
				if kafkaMsg.IsFinal {
					// 获取现有会话
					session, err := chatSessionModel.FindBySessionID(ctx, kafkaMsg.SessionID)
					if err != nil {
						logx.Errorf("查询聊天会话数据失败: %v", err)
						continue
					}

					if session == nil {
						// 会话不存在，创建新会话
						newSession := &model.ChatSession{
							SessionID:   kafkaMsg.SessionID,
							FullContent: kafkaMsg.Content,
							StartTime:   time.Unix(kafkaMsg.Timestamp, 0),
							EndTime:     time.Unix(kafkaMsg.Timestamp, 0),
						}

						err := chatSessionModel.Insert(ctx, newSession)
						if err != nil {
							logx.Errorf("创建聊天会话数据失败: %v", err)
						}
					} else {
						// 会话存在，更新内容
						session.FullContent = kafkaMsg.Content
						session.EndTime = time.Unix(kafkaMsg.Timestamp, 0)

						err := chatSessionModel.Update(ctx, session)
						if err != nil {
							logx.Errorf("更新聊天会话数据失败: %v", err)
						}
					}
				}
			}
		}(pc)
	}

	// 保持主程序运行
	select {}
}
