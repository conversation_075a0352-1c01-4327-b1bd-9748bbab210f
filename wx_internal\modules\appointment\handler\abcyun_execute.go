package handler

import (
	"errors"
	"net/http"

	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// ExecuteQueryByDateRequest 按天查询执行单列表请求参数
type ExecuteQueryByDateRequest struct {
	Date string `form:"date"` // 日期 yyyy-MM-dd
}

// ExecuteDetailRequest 执行单详情请求参数
type ExecuteDetailRequest struct {
	ID string `path:"id"` // 执行单ID
}

// ExecuteDoRequest 执行请求参数
type ExecuteDoRequest struct {
	ID          string                 `path:"id"`          // 执行单ID
	Items       []abcyun.ExecuteDoItem `json:"items"`       // 执行项目列表
	ExecutorIds []string               `json:"executorIds"` // 执行人ID列表
	OperatorId  string                 `json:"operatorId"`  // 操作人ID
	Comment     string                 `json:"comment"`     // 备注
}

// ExecuteRecordRequest 查询执行单执行历史请求参数
type ExecuteRecordRequest struct {
	ID string `path:"id"` // 执行单ID
}

// ExecuteUndoRequest 撤销执行请求参数
type ExecuteUndoRequest struct {
	ID              string `path:"id"`              // 执行单ID
	ExecuteRecordId string `path:"executeRecordId"` // 执行记录ID
	OperatorId      string `json:"operatorId"`      // 操作人ID
}

// ExecutePatientRequest 查询患者执行单列表请求参数
type ExecutePatientRequest struct {
	PatientId     string `path:"patientId"`                 // 患者ID
	BeginDate     string `form:"beginDate,optional"`        // 开始日期，yyyy-MM-dd，为空则默认为三个月前
	EndDate       string `form:"endDate,optional"`          // 结束日期，yyyy-MM-dd，为空则默认为今天
	ExecuteStatus int    `form:"executeStatus,optional"`    // 执行状态 1:待执行 2:已执行 3:已取消（已退费）
	Limit         int    `form:"limit,optional,default=10"` // 分页大小，默认为 10，最大为 25
	Offset        int    `form:"offset,optional,default=0"` // 分页偏移，默认为 0
}

// AbcYunExecuteQueryByDateHandler 按天查询执行单列表
func AbcYunExecuteQueryByDateHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req ExecuteQueryByDateRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("缺少日期参数"))
		return
	}

	// 调用ABC云API获取执行单列表
	result, err := client.GetExecuteListByDate(req.Date)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, result)
}

// AbcYunExecuteDetailHandler 执行单详情
func AbcYunExecuteDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req ExecuteDetailRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("缺少执行单ID参数"))
		return
	}

	// 调用ABC云API获取执行单详情
	result, err := client.GetExecuteDetail(req.ID)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, result)
}

// AbcYunExecuteDoHandler 执行
func AbcYunExecuteDoHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req ExecuteDoRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("缺少执行单ID参数"))
		return
	}

	if len(req.Items) == 0 {
		httpx.Error(w, errors.New("缺少执行项目"))
		return
	}

	// 调用ABC云API执行
	result, err := client.DoExecute(req.ID, req.Items, req.ExecutorIds, req.OperatorId, req.Comment)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, result)
}

// AbcYunExecuteRecordHandler 查询执行单执行历史
func AbcYunExecuteRecordHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req ExecuteRecordRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("缺少执行单ID参数"))
		return
	}

	// 调用ABC云API获取执行单执行历史
	result, err := client.GetExecuteRecord(req.ID)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, result)
}

// AbcYunExecuteUndoHandler 撤销执行
func AbcYunExecuteUndoHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req ExecuteUndoRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("缺少执行单ID参数"))
		return
	}

	if req.ExecuteRecordId == "" {
		httpx.Error(w, errors.New("缺少执行记录ID参数"))
		return
	}

	// 调用ABC云API撤销执行
	result, err := client.UndoExecute(req.ID, req.ExecuteRecordId, req.OperatorId)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, result)
}

// AbcYunExecutePatientHandler 查询患者执行单列表
func AbcYunExecutePatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req ExecutePatientRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PatientId == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	// 调用ABC云API获取患者执行单列表
	result, err := client.GetPatientExecuteList(req.PatientId, req.BeginDate, req.EndDate, req.ExecuteStatus, req.Limit, req.Offset)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, result)
}
