# 七牛云对象存储上传组件

本组件提供了基于七牛云对象存储的文件上传功能，支持图片、文档和通用文件的上传。

## 功能特点

- 支持图片、文档和通用文件的上传
- 自动生成唯一的文件名
- 支持自定义存储路径
- 支持文件类型验证
- 支持文件大小限制
- 提供HTTP处理器，方便集成到Web应用

## 配置说明

默认配置：

```go
// 默认七牛云配置
var DefaultQiniuConfig = QiniuConfig{
    AccessKey: "-BftVKiSPGF4Aznszuynn8NqTlx5xzYEr9hNIRUP",
    SecretKey: "dEBEjShOwfqJYPSnn4jocunXwkU2oCsMOZIO1Ck0",
    Bucket:    "pub-med-yekaitai",
    Domain:    "pub-med-yekaitai.medlinker.com",
    Zone:      "z2", // 华南-广东 对应的区域代码为 z2
}
```

## 使用示例

### 初始化

```go
// 初始化默认配置
upload.Init()

// 或者使用自定义配置
config := upload.Config{
    Qiniu: upload.QiniuConfig{
        AccessKey: "your_access_key",
        SecretKey: "your_secret_key",
        Bucket:    "your_bucket",
        Domain:    "your_domain.com",
        Zone:      "z0", // 根据实际区域设置
    },
}
upload.SetConfig(config)
upload.Init()
```

### 直接使用服务接口

```go
import (
    "context"
    "fmt"
    "net/http"
    "yekaitai/pkg/upload"
)

func main() {
    // 初始化上传组件
    upload.Init()

    // 处理文件上传
    http.HandleFunc("/upload", func(w http.ResponseWriter, r *http.Request) {
        // 解析表单
        if err := r.ParseMultipartForm(10 << 20); err != nil {
            http.Error(w, "解析表单失败", http.StatusBadRequest)
            return
        }

        // 获取文件
        file, header, err := r.FormFile("file")
        if err != nil {
            http.Error(w, "获取文件失败", http.StatusBadRequest)
            return
        }
        defer file.Close()

        // 上传图片
        result, err := upload.DefaultService.UploadImage(r.Context(), header, "images")
        if err != nil {
            http.Error(w, fmt.Sprintf("上传失败: %v", err), http.StatusInternalServerError)
            return
        }

        // 返回上传结果
        fmt.Fprintf(w, "上传成功: %s", result.URL)
    })

    http.ListenAndServe(":8080", nil)
}
```

### 使用内置的HTTP处理器

```go
import (
    "net/http"
    "yekaitai/pkg/upload"
)

func main() {
    // 初始化上传组件
    upload.Init()

    // 创建多路复用器
    mux := http.NewServeMux()

    // 注册上传处理器
    upload.RegisterHandlers(mux)

    // 启动HTTP服务
    http.ListenAndServe(":8080", mux)
}
```

## API接口

### 服务接口

```go
// Service 上传服务接口
type Service interface {
    // UploadFile 上传文件
    UploadFile(ctx context.Context, file *multipart.FileHeader, uploadType UploadType, customPath string) (*UploadResult, error)
    
    // UploadImage 上传图片
    UploadImage(ctx context.Context, file *multipart.FileHeader, customPath string) (*UploadResult, error)
    
    // UploadDocument 上传文档
    UploadDocument(ctx context.Context, file *multipart.FileHeader, customPath string) (*UploadResult, error)
}
```

### HTTP接口

注册处理器后，将提供以下HTTP接口：

- `POST /api/upload/image` - 上传图片
- `POST /api/upload/document` - 上传文档
- `POST /api/upload/file` - 上传通用文件

请求参数：
- `file` - 要上传的文件（必需）
- `path` - 自定义存储路径（可选）
- `type` - 文件类型（仅适用于 `/api/upload/file` 接口，可选值：image, document, general）

响应格式：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "url": "https://example.com/path/file.jpg",
    "key": "path/file.jpg",
    "size": 12345,
    "filename": "original.jpg",
    "extension": ".jpg",
    "mime_type": "image/jpeg",
    "created_at": "2023-05-01T12:34:56Z"
  }
}
``` 