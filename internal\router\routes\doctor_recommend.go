package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	doctorRecommendHandler "yekaitai/internal/modules/doctor_recommend/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterDoctorRecommendRoutes 注册医生推荐管理相关路由
func RegisterDoctorRecommendRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建医生推荐处理器
	handler := doctorRecommendHandler.NewDoctorRecommendHandler(serverCtx)

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 注册路由
	server.AddRoutes(
		[]rest.Route{
			// 医生推荐管理
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/doctor-recommends",
				Handler: adminAuthWrapper(handler.ListDoctorRecommends),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/doctor-recommends",
				Handler: adminAuthWrapper(handler.CreateDoctorRecommend),
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/doctor-recommends/:id",
				Handler: adminAuthWrapper(handler.UpdateDoctorRecommend),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/doctor-recommends/:id",
				Handler: adminAuthWrapper(handler.DeleteDoctorRecommend),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/doctor-recommends/batch-create",
				Handler: adminAuthWrapper(handler.BatchCreateDoctorRecommends),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/doctor-recommends/batch-delete",
				Handler: adminAuthWrapper(handler.BatchDeleteDoctorRecommends),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/doctor-recommends/:id/set-top",
				Handler: adminAuthWrapper(handler.SetTopDoctorRecommend),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/doctor-recommends/:id/cancel-top",
				Handler: adminAuthWrapper(handler.CancelTopDoctorRecommend),
			},
			// 医生选择列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/doctor-select",
				Handler: adminAuthWrapper(handler.ListDoctorSelect),
			},
		},
	)
}
