package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/modules/admin/model"
	storeModel "yekaitai/internal/modules/store/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 门店列表请求
type StoreListRequest struct {
	types.PageRequest
}

// 门店详情请求
type StoreDetailRequest struct {
	StoreID uint `path:"storeId"` // 门店ID
}

// 创建门店请求
type CreateStoreRequest struct {
	Name        string   `json:"name"`                      // 门店名称
	Phone       string   `json:"phone"`                     // 联系电话
	ProvinceID  string   `json:"provinceId"`                // 省份编码
	CityID      string   `json:"cityId"`                    // 城市编码
	AreaID      string   `json:"areaId"`                    // 区县编码
	Address     string   `json:"address"`                   // 详细地址
	Latitude    float64  `json:"latitude,optional"`         // 纬度，可选
	Longitude   float64  `json:"longitude,optional"`        // 经度，可选
	Description string   `json:"description,optional"`      // 门店介绍，可选
	Images      []string `json:"images,optional"`           // 门店图片列表，可选
	ManagerID   uint     `json:"managerId"`                 // 门店管理员ID
	Status      int      `json:"status,optional,default=1"` // 状态：1-正常，0-禁用，默认1
}

// 更新门店请求
type UpdateStoreRequest struct {
	StoreID     uint     `path:"storeId"`              // 门店ID
	Name        string   `json:"name,optional"`        // 门店名称
	Phone       string   `json:"phone,optional"`       // 联系电话
	ProvinceID  string   `json:"provinceId,optional"`  // 省份编码
	CityID      string   `json:"cityId,optional"`      // 城市编码
	AreaID      string   `json:"areaId,optional"`      // 区县编码
	Address     string   `json:"address,optional"`     // 详细地址
	Latitude    float64  `json:"latitude,optional"`    // 纬度
	Longitude   float64  `json:"longitude,optional"`   // 经度
	Description string   `json:"description,optional"` // 门店介绍
	Images      []string `json:"images,optional"`      // 门店图片列表
	ManagerID   uint     `json:"managerId,optional"`   // 门店管理员ID
	Status      int      `json:"status,optional"`      // 状态：1-正常，0-禁用
}

// 更新门店状态请求
type UpdateStoreStatusRequest struct {
	StoreID uint `path:"storeId"` // 门店ID
	Status  int  `json:"status"`  // 状态：1-正常，0-禁用
}

// 删除门店请求
type DeleteStoreRequest struct {
	StoreID uint `path:"storeId"` // 门店ID
}

// StoreHandler 门店处理器
type StoreHandler struct {
	svcCtx *svc.ServiceContext
}

// NewStoreHandler 创建门店处理器
func NewStoreHandler(svcCtx *svc.ServiceContext) *StoreHandler {
	return &StoreHandler{
		svcCtx: svcCtx,
	}
}

// ListStores 获取门店列表
func (h *StoreHandler) ListStores(w http.ResponseWriter, r *http.Request) {
	var req StoreListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取门店列表: page=%d, size=%d, query=%s", req.Page, req.Size, req.Query)

	stores, total, err := h.svcCtx.StoreRepo.List(req.Page, req.Size, req.Query)
	if err != nil {
		logx.Errorf("获取门店列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取门店列表失败"))
		return
	}

	// 处理门店数据，将JSON格式的字段解析为对象
	result := make([]map[string]interface{}, len(stores))
	for i, store := range stores {
		storeMap := map[string]interface{}{
			"id":          store.ID,
			"name":        store.Name,
			"phone":       store.Phone,
			"provinceId":  store.ProvinceID,
			"cityId":      store.CityID,
			"areaId":      store.AreaID,
			"address":     store.Address,
			"latitude":    store.Latitude,
			"longitude":   store.Longitude,
			"description": store.Description,
			"managerId":   store.ManagerID,
			"creatorId":   store.CreatorID,
			"status":      store.Status,
			"createdAt":   store.CreatedAt,
			"updatedAt":   store.UpdatedAt,
		}

		// 解析门店图片
		var images []string
		if store.Images != "" {
			if err := json.Unmarshal([]byte(store.Images), &images); err != nil {
				logx.Errorf("解析门店图片失败: %v", err)
				images = []string{}
			}
		}
		storeMap["images"] = images

		// 获取管理员和创建人信息
		adminRepo := model.NewAdminUserRepository()
		if store.ManagerID > 0 {
			manager, err := adminRepo.FindByID(store.ManagerID)
			if err == nil {
				storeMap["managerName"] = manager.Username
			}
		}
		if store.CreatorID > 0 {
			creator, err := adminRepo.FindByID(store.CreatorID)
			if err == nil {
				storeMap["creatorName"] = creator.Username
			}
		}

		result[i] = storeMap
	}

	logx.Infof("获取门店列表成功: 共%d条记录", total)

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取门店列表成功"))
}

// GetStore 获取门店详情
func (h *StoreHandler) GetStore(w http.ResponseWriter, r *http.Request) {
	var req StoreDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取门店详情: storeId=%d", req.StoreID)

	store, err := h.svcCtx.StoreRepo.FindByID(req.StoreID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "门店不存在"))
		} else {
			logx.Errorf("获取门店详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取门店详情失败"))
		}
		return
	}

	// 构建结果
	result := map[string]interface{}{
		"id":          store.ID,
		"name":        store.Name,
		"phone":       store.Phone,
		"provinceId":  store.ProvinceID,
		"cityId":      store.CityID,
		"areaId":      store.AreaID,
		"address":     store.Address,
		"latitude":    store.Latitude,
		"longitude":   store.Longitude,
		"description": store.Description,
		"managerId":   store.ManagerID,
		"creatorId":   store.CreatorID,
		"status":      store.Status,
		"createdAt":   store.CreatedAt,
		"updatedAt":   store.UpdatedAt,
	}

	// 解析门店图片
	var images []string
	if store.Images != "" {
		if err := json.Unmarshal([]byte(store.Images), &images); err != nil {
			logx.Errorf("解析门店图片失败: %v", err)
			images = []string{}
		}
	}
	result["images"] = images

	// 获取管理员和创建人信息
	adminRepo := model.NewAdminUserRepository()
	if store.ManagerID > 0 {
		manager, err := adminRepo.FindByID(store.ManagerID)
		if err == nil {
			result["managerName"] = manager.Username
		}
	}
	if store.CreatorID > 0 {
		creator, err := adminRepo.FindByID(store.CreatorID)
		if err == nil {
			result["creatorName"] = creator.Username
		}
	}

	// 获取门店所在地区名称
	regionRepo := model.NewRegionRepository(mysql.Slave())
	if store.ProvinceID != "" {
		province, err := regionRepo.FindByCode(store.ProvinceID)
		if err == nil {
			result["provinceName"] = province.Name
		}
	}
	if store.CityID != "" {
		city, err := regionRepo.FindByCode(store.CityID)
		if err == nil {
			result["cityName"] = city.Name
		}
	}
	if store.AreaID != "" {
		area, err := regionRepo.FindByCode(store.AreaID)
		if err == nil {
			result["areaName"] = area.Name
		}
	}

	logx.Infof("获取门店详情成功: ID=%d, 名称=%s", store.ID, store.Name)

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取门店详情成功"))
}

// CreateStore 创建门店
func (h *StoreHandler) CreateStore(w http.ResponseWriter, r *http.Request) {
	var req CreateStoreRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 参数验证
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "门店名称不能为空"))
		return
	}
	if req.Phone == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "联系电话不能为空"))
		return
	}
	if req.ProvinceID == "" || req.CityID == "" || req.AreaID == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "门店地区信息不完整"))
		return
	}
	if req.Address == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "详细地址不能为空"))
		return
	}
	if req.ManagerID == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "管理员ID不能为空"))
		return
	}

	// 检查管理员是否存在
	adminRepo := model.NewAdminUserRepository()
	_, err := adminRepo.FindByID(req.ManagerID)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "指定的管理员不存在"))
		return
	}

	// 转换图片列表为JSON字符串
	imagesJSON, err := json.Marshal(req.Images)
	if err != nil {
		logx.Errorf("序列化门店图片失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建门店失败"))
		return
	}

	// 获取当前管理员ID
	var adminID uint64 = 1 // 默认为系统管理员ID
	if adminIDValue := r.Context().Value("admin_id"); adminIDValue != nil {
		if adminIDStr, ok := adminIDValue.(string); ok {
			parsedID, err := strconv.ParseUint(adminIDStr, 10, 64)
			if err == nil {
				adminID = parsedID
			} else {
				logx.Errorf("解析管理员ID失败: %v, 使用默认ID=1", err)
			}
		}
	} else {
		logx.Infof("未能从上下文获取管理员ID，使用默认ID=1")
	}

	// 创建门店
	now := time.Now()
	store := &storeModel.Store{
		Name:        req.Name,
		Phone:       req.Phone,
		ProvinceID:  req.ProvinceID,
		CityID:      req.CityID,
		AreaID:      req.AreaID,
		Address:     req.Address,
		Latitude:    req.Latitude,
		Longitude:   req.Longitude,
		Description: req.Description,
		Images:      string(imagesJSON),
		ManagerID:   req.ManagerID,
		CreatorID:   uint(adminID),
		Status:      req.Status,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	if err := h.svcCtx.StoreRepo.Create(store); err != nil {
		logx.Errorf("创建门店失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建门店失败"))
		return
	}

	logx.Infof("门店创建成功: ID=%d, 名称=%s", store.ID, store.Name)

	httpx.OkJson(w, types.NewSuccessResponse(store, "创建门店成功"))
}

// UpdateStore 更新门店
func (h *StoreHandler) UpdateStore(w http.ResponseWriter, r *http.Request) {
	var req UpdateStoreRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("更新门店: storeId=%d", req.StoreID)

	// 检查门店是否存在
	store, err := h.svcCtx.StoreRepo.FindByID(req.StoreID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "门店不存在"))
		} else {
			logx.Errorf("获取门店失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新门店失败"))
		}
		return
	}

	// 更新标志
	updatePerformed := false

	// 更新门店名称
	if req.Name != "" && req.Name != store.Name {
		store.Name = req.Name
		updatePerformed = true
	}

	// 更新联系电话
	if req.Phone != "" && req.Phone != store.Phone {
		store.Phone = req.Phone
		updatePerformed = true
	}

	// 更新地区信息
	if req.ProvinceID != "" && req.ProvinceID != store.ProvinceID {
		store.ProvinceID = req.ProvinceID
		updatePerformed = true
	}
	if req.CityID != "" && req.CityID != store.CityID {
		store.CityID = req.CityID
		updatePerformed = true
	}
	if req.AreaID != "" && req.AreaID != store.AreaID {
		store.AreaID = req.AreaID
		updatePerformed = true
	}

	// 更新详细地址
	if req.Address != "" && req.Address != store.Address {
		store.Address = req.Address
		updatePerformed = true
	}

	// 更新经纬度信息
	if req.Latitude != 0 && req.Latitude != store.Latitude {
		store.Latitude = req.Latitude
		updatePerformed = true
	}
	if req.Longitude != 0 && req.Longitude != store.Longitude {
		store.Longitude = req.Longitude
		updatePerformed = true
	}

	// 更新门店介绍
	if req.Description != store.Description {
		store.Description = req.Description
		updatePerformed = true
	}

	// 更新门店管理员
	if req.ManagerID > 0 && req.ManagerID != store.ManagerID {
		// 检查管理员是否存在
		adminRepo := model.NewAdminUserRepository()
		_, err := adminRepo.FindByID(req.ManagerID)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "指定的管理员不存在"))
			return
		}
		store.ManagerID = req.ManagerID
		updatePerformed = true
	}

	// 更新门店图片
	if req.Images != nil {
		imagesJSON, err := json.Marshal(req.Images)
		if err != nil {
			logx.Errorf("序列化门店图片失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新门店失败"))
			return
		}
		if string(imagesJSON) != store.Images {
			store.Images = string(imagesJSON)
			updatePerformed = true
		}
	}

	// 更新状态
	if req.Status != 0 || store.Status != 0 { // 考虑零值问题
		if req.Status != store.Status {
			store.Status = req.Status
			updatePerformed = true
		}
	}

	// 仅在有实际字段更新时更新数据库
	if updatePerformed {
		store.UpdatedAt = time.Now()
		if err := h.svcCtx.StoreRepo.Update(store); err != nil {
			logx.Errorf("更新门店失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新门店失败"))
			return
		}
		logx.Infof("门店信息更新成功: ID=%d", store.ID)
	} else {
		logx.Infof("没有需要更新的字段: ID=%d", store.ID)
	}

	httpx.OkJson(w, types.NewSuccessResponse(nil, "更新门店成功"))
}

// UpdateStoreStatus 更新门店状态
func (h *StoreHandler) UpdateStoreStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdateStoreStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 验证状态值
	if req.Status != 0 && req.Status != 1 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "状态值无效，只能是0或1"))
		return
	}

	logx.Infof("更新门店状态: storeId=%d, status=%d", req.StoreID, req.Status)

	// 检查门店是否存在
	store, err := h.svcCtx.StoreRepo.FindByID(req.StoreID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "门店不存在"))
		} else {
			logx.Errorf("获取门店失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新门店状态失败"))
		}
		return
	}

	// 如果状态没有变化，则不需要更新
	if store.Status == req.Status {
		logx.Infof("门店状态已经是 %d，无需更新", req.Status)
		statusText := "启用"
		if req.Status == 0 {
			statusText = "禁用"
		}
		httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
			"message": fmt.Sprintf("门店已处于%s状态", statusText),
			"status":  req.Status,
		}, fmt.Sprintf("门店已处于%s状态", statusText)))
		return
	}

	// 更新状态
	if err := h.svcCtx.StoreRepo.UpdateStatus(req.StoreID, req.Status); err != nil {
		logx.Errorf("更新门店状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新门店状态失败"))
		return
	}

	statusText := "启用"
	if req.Status == 0 {
		statusText = "禁用"
	}

	logx.Infof("门店状态更新成功: ID=%d, 状态=%d", req.StoreID, req.Status)

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"message": fmt.Sprintf("门店%s成功", statusText),
		"status":  req.Status,
	}, fmt.Sprintf("门店%s成功", statusText)))
}

// DeleteStore 删除门店
func (h *StoreHandler) DeleteStore(w http.ResponseWriter, r *http.Request) {
	var req DeleteStoreRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("收到删除门店请求: storeId=%d", req.StoreID)

	// 检查门店是否存在
	store, err := h.svcCtx.StoreRepo.FindByID(req.StoreID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "门店不存在"))
		} else {
			logx.Errorf("获取门店失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除门店失败"))
		}
		return
	}

	// 检查门店是否有关联的管理员
	adminRepo := model.NewAdminUserRepository()
	admins, _, err := adminRepo.List(1, 10, "")
	if err == nil {
		for _, admin := range admins {
			if admin.StoreID != nil && *admin.StoreID == req.StoreID {
				logx.Errorf("门店存在关联的管理员账号，无法删除: storeId=%d", req.StoreID)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "门店存在关联的管理员账号，无法删除"))
				return
			}
		}
	}

	// 执行删除操作
	if err := h.svcCtx.StoreRepo.Delete(req.StoreID); err != nil {
		logx.Errorf("删除门店失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除门店失败"))
		return
	}

	logx.Infof("门店删除成功: ID=%d, 名称=%s", store.ID, store.Name)

	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除门店成功"))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *StoreHandler) logAdminOperation(r *http.Request, adminID, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 管理员ID=%s, 模块=%s, 操作=%s", adminID, module, action)
}
