package service

import (
	"context"
	"fmt"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type CategoryService struct {
	db *gorm.DB
}

func NewCategoryService() *CategoryService {
	return &CategoryService{
		db: mysql.GetDB(),
	}
}

// CreateCategory 创建分类
func (s *CategoryService) CreateCategory(ctx context.Context, req *model.CategoryCreateRequest) (*model.Category, error) {
	// 生成唯一的CategoryID
	categoryUUID := uuid.New().String()

	category := &model.Category{
		CategoryID:    categoryUUID,
		ParentID:      req.ParentID,
		CategoryName:  req.CategoryName,
		CategoryIcon:  req.CategoryIcon,
		CategoryImage: req.CategoryImage,
		Description:   req.Description,
		Status:        req.Status,
		Level:         1,
	}

	// 计算层级和路径
	if req.ParentID > 0 {
		var parent model.Category
		if err := s.db.First(&parent, req.ParentID).Error; err != nil {
			return nil, fmt.Errorf("父分类不存在")
		}
		category.Level = parent.Level + 1
		category.Path = parent.Path + "," + fmt.Sprintf("%d", parent.ID)
	} else {
		category.Path = "0"
	}

	if err := s.db.Create(category).Error; err != nil {
		logx.Errorf("创建分类失败: %v", err)
		return nil, err
	}

	// 更新路径包含自己的ID
	category.Path = category.Path + "," + fmt.Sprintf("%d", category.ID)
	s.db.Save(category)

	logx.Infof("创建分类成功: %s (CategoryID: %s)", category.CategoryName, category.CategoryID)
	return category, nil
}

// UpdateCategory 更新分类
func (s *CategoryService) UpdateCategory(ctx context.Context, id uint, req *model.CategoryUpdateRequest) (*model.Category, error) {
	var category model.Category
	if err := s.db.First(&category, id).Error; err != nil {
		return nil, fmt.Errorf("分类不存在")
	}

	// 更新字段
	category.CategoryName = req.CategoryName
	category.CategoryIcon = req.CategoryIcon
	category.CategoryImage = req.CategoryImage
	category.Description = req.Description
	category.Status = req.Status

	if err := s.db.Save(&category).Error; err != nil {
		logx.Errorf("更新分类失败: %v", err)
		return nil, err
	}

	logx.Infof("更新分类成功: %s", category.CategoryName)
	return &category, nil
}

// DeleteCategory 删除分类
func (s *CategoryService) DeleteCategory(ctx context.Context, id uint) error {
	// 先获取要删除的分类信息
	var category model.Category
	if err := s.db.First(&category, id).Error; err != nil {
		return fmt.Errorf("分类不存在")
	}

	// 检查是否为默认分类（不可删除）
	if category.IsDefaultCategory() {
		return fmt.Errorf("默认分类\"%s\"不可删除", category.CategoryName)
	}

	// 检查是否有子分类
	var count int64
	s.db.Model(&model.Category{}).Where("parent_id = ?", id).Count(&count)
	if count > 0 {
		return fmt.Errorf("该分类下有子分类，无法删除")
	}

	// 检查是否有商品（使用category_id字段）
	var goodsCount int64
	s.db.Model(&model.Goods{}).Where("category_id = ?", category.CategoryID).Count(&goodsCount)
	if goodsCount > 0 {
		return fmt.Errorf("该分类下有商品，无法删除")
	}

	if err := s.db.Delete(&model.Category{}, id).Error; err != nil {
		logx.Errorf("删除分类失败: %v", err)
		return err
	}

	logx.Infof("删除分类成功: ID=%d", id)
	return nil
}

// GetCategory 获取分类详情
func (s *CategoryService) GetCategory(ctx context.Context, id uint) (*model.Category, error) {
	var category model.Category
	if err := s.db.Preload("Children").First(&category, id).Error; err != nil {
		return nil, fmt.Errorf("分类不存在")
	}

	// 统计商品数量（使用category_id字段）
	s.db.Model(&model.Goods{}).Where("category_id = ?", category.CategoryID).Count(&category.GoodsCount)

	return &category, nil
}

// ListCategories 获取分类列表
func (s *CategoryService) ListCategories(ctx context.Context, params *model.CategoryQueryParams) ([]*model.Category, int64, error) {
	query := s.db.Model(&model.Category{})

	// 构建查询条件
	if params.ParentID != nil {
		query = query.Where("parent_id = ?", *params.ParentID)
	}
	if params.CategoryName != "" {
		query = query.Where("category_name LIKE ?", "%"+params.CategoryName+"%")
	}
	if params.Status != nil {
		query = query.Where("status = ?", *params.Status)
	}
	if params.IsRecommended != nil {
		query = query.Where("is_recommended = ?", *params.IsRecommended)
	}
	if params.Level != nil {
		query = query.Where("level = ?", *params.Level)
	}

	// 统计总数
	var total int64
	query.Count(&total)

	// 分页
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	// 排序
	query = query.Order("sort_order ASC, id ASC")

	var categories []*model.Category
	if err := query.Find(&categories).Error; err != nil {
		logx.Errorf("查询分类列表失败: %v", err)
		return nil, 0, err
	}

	// 统计每个分类下的商品数量
	for _, category := range categories {
		s.db.Model(&model.Goods{}).Where("category_id = ?", category.CategoryID).Count(&category.GoodsCount)
	}

	return categories, total, nil
}

// GetCategoryTree 获取分类树
func (s *CategoryService) GetCategoryTree(ctx context.Context, parentID uint) ([]*model.CategoryTree, error) {
	// 优化方案：一次性查询所有分类，然后在内存中构建树形结构
	var allCategories []*model.Category
	if err := s.db.WithContext(ctx).Where("status = 1").Order("sort_order ASC, id ASC").Find(&allCategories).Error; err != nil {
		logx.Errorf("查询分类列表失败: %v", err)
		return nil, err
	}

	// 如果没有分类数据，直接返回
	if len(allCategories) == 0 {
		return []*model.CategoryTree{}, nil
	}

	// 一次性查询所有分类的商品数量
	type CategoryCount struct {
		CategoryID string
		Count      int64
	}
	var categoryCounts []CategoryCount
	s.db.WithContext(ctx).Model(&model.Goods{}).
		Select("category_id, COUNT(*) as count").
		Group("category_id").
		Find(&categoryCounts)

	// 构建商品数量映射
	goodsCountMap := make(map[string]int64)
	for _, cc := range categoryCounts {
		goodsCountMap[cc.CategoryID] = cc.Count
	}

	// 构建分类映射和子分类映射
	categoryMap := make(map[uint]*model.CategoryTree)
	childrenMap := make(map[uint][]*model.CategoryTree)

	// 转换为CategoryTree结构并建立映射
	for _, category := range allCategories {
		tree := &model.CategoryTree{
			ID:            category.ID,
			CategoryID:    category.CategoryID,
			ParentID:      category.ParentID,
			CategoryName:  category.CategoryName,
			CategoryIcon:  category.CategoryIcon,
			CategoryImage: category.CategoryImage,
			Description:   category.Description,
			Status:        category.Status,
			Level:         category.Level,
			Path:          category.Path,
			GoodsCount:    goodsCountMap[category.CategoryID],
			CreatedAt:     category.CreatedAt,
			UpdatedAt:     category.UpdatedAt,
			Children:      []*model.CategoryTree{},
		}

		categoryMap[category.ID] = tree
		childrenMap[category.ParentID] = append(childrenMap[category.ParentID], tree)
	}

	// 构建子分类关系
	for _, tree := range categoryMap {
		if children, exists := childrenMap[tree.ID]; exists {
			tree.Children = children
		}
	}

	// 返回指定父分类的子分类
	if children, exists := childrenMap[parentID]; exists {
		return children, nil
	}

	return []*model.CategoryTree{}, nil
}

// GetRecommendCategories 获取推荐分类
func (s *CategoryService) GetRecommendCategories(ctx context.Context, limit int) ([]*model.Category, error) {
	var categories []*model.Category
	query := s.db.Where("is_recommended = 1 AND status = 1").
		Order("recommend_order ASC, id ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&categories).Error; err != nil {
		logx.Errorf("查询推荐分类失败: %v", err)
		return nil, err
	}

	// 统计每个分类下的商品数量（使用category_id字段）
	for _, category := range categories {
		s.db.Model(&model.Goods{}).Where("category_id = ?", category.CategoryID).Count(&category.GoodsCount)
	}

	return categories, nil
}

// InitDefaultCategory 初始化默认分类
func (s *CategoryService) InitDefaultCategory(ctx context.Context) error {
	// 检查默认分类是否已存在
	var existingCategory model.Category
	err := s.db.WithContext(ctx).Where("category_id = ? OR category_name = ?",
		model.DefaultCategoryID, model.DefaultCategoryName).First(&existingCategory).Error

	if err == gorm.ErrRecordNotFound {
		// 创建默认分类
		defaultCategory := model.Category{
			CategoryID:   model.DefaultCategoryID,
			CategoryName: model.DefaultCategoryName,
			ParentID:     0, // 顶级分类
			Status:       1, // 启用状态
			Level:        1, // 一级分类
			Path:         "0",
			SortOrder:    999, // 排在最后
			Description:  "默认分类，用于未分类的商品",
		}

		if err := s.db.WithContext(ctx).Create(&defaultCategory).Error; err != nil {
			logx.Errorf("创建默认分类失败: %v", err)
			return fmt.Errorf("创建默认分类失败: %w", err)
		}

		logx.Infof("成功创建默认分类: %s", model.DefaultCategoryName)
	} else if err != nil {
		return fmt.Errorf("检查默认分类失败: %w", err)
	} else {
		// 默认分类已存在，确保其状态正确
		if existingCategory.Status != 1 {
			existingCategory.Status = 1
			if err := s.db.WithContext(ctx).Save(&existingCategory).Error; err != nil {
				logx.Errorf("更新默认分类状态失败: %v", err)
			} else {
				logx.Info("默认分类状态已更新为启用")
			}
		}
		logx.Info("默认分类已存在")
	}

	return nil
}

// GetDefaultCategory 获取默认分类
func (s *CategoryService) GetDefaultCategory(ctx context.Context) (*model.Category, error) {
	var category model.Category
	err := s.db.WithContext(ctx).Where("category_id = ?", model.DefaultCategoryID).First(&category).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果默认分类不存在，先初始化
			if initErr := s.InitDefaultCategory(ctx); initErr != nil {
				return nil, fmt.Errorf("初始化默认分类失败: %w", initErr)
			}
			// 重新查询
			err = s.db.WithContext(ctx).Where("category_id = ?", model.DefaultCategoryID).First(&category).Error
		}
		if err != nil {
			return nil, fmt.Errorf("获取默认分类失败: %w", err)
		}
	}
	return &category, nil
}
