package model

import (
	"time"
)

// InvitationRecord 邀请记录
type InvitationRecord struct {
	ID             uint       `json:"id" gorm:"primaryKey;autoIncrement;comment:邀请记录ID"`
	InviterID      uint       `json:"inviter_id" gorm:"not null;index:idx_invitation_inviter;comment:邀请人用户ID"`
	InviteeID      uint       `json:"invitee_id" gorm:"not null;index:idx_invitation_invitee;comment:被邀请人用户ID"`
	InvitationCode string     `json:"invitation_code" gorm:"type:varchar(32);not null;uniqueIndex;comment:邀请码"`
	Status         int        `json:"status" gorm:"default:0;comment:邀请状态(0待完成,1已完成,2已失效)"`
	InviteePhone   string     `json:"invitee_phone" gorm:"type:varchar(20);comment:被邀请人手机号"`
	InviteeName    string     `json:"invitee_name" gorm:"type:varchar(50);comment:被邀请人姓名"`
	CoinsAwarded   int        `json:"coins_awarded" gorm:"default:0;comment:奖励叶小币数量"`
	RewardType     string     `json:"reward_type" gorm:"type:varchar(50);comment:奖励类型"`
	CompletedAt    *time.Time `json:"completed_at" gorm:"comment:完成时间"`
	CreatedAt      time.Time  `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt      time.Time  `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (ir *InvitationRecord) TableName() string {
	return "invitation_records"
}

// InvitationCenterRequest 邀请中心请求
type InvitationCenterRequest struct {
	Page     int `json:"page" form:"page"`           // 页码，默认1
	PageSize int `json:"page_size" form:"page_size"` // 每页数量，默认10
}

// InvitationCenterResponse 邀请中心响应
type InvitationCenterResponse struct {
	TotalCoins      int                    `json:"total_coins"`      // 已获得邀请叶小币总数（通过邀请获得的叶小币总数）
	InvitationCount int                    `json:"invitation_count"` // 邀请成功总数（已完成的邀请数量）
	InvitationRules []InvitationRule       `json:"invitation_rules"` // 邀请奖励规则（包含注册奖励和首单奖励）
	Records         []InvitationRecordInfo `json:"records"`          // 邀请记录列表（分页数据，每页10条）
	HasMore         bool                   `json:"has_more"`         // 是否有更多数据（用于前端判断是否显示加载更多）
	CurrentPage     int                    `json:"current_page"`     // 当前页码（从1开始）
	TotalPages      int                    `json:"total_pages"`      // 总页数
}

// InvitationRecordInfo 邀请记录信息
type InvitationRecordInfo struct {
	ID           uint      `json:"id"`            // 记录ID
	InviteeName  string    `json:"invitee_name"`  // 被邀请人姓名（从wx_users.nickname获取）
	InviteePhone string    `json:"invitee_phone"` // 被邀请人手机号（脱敏处理，格式：138****1234）
	RewardType   string    `json:"reward_type"`   // 奖励类型（REGISTRATION=注册奖励，FIRST_ORDER=首单奖励）
	CoinsAwarded int       `json:"coins_awarded"` // 奖励叶小币数量
	CompletedAt  time.Time `json:"completed_at"`  // 完成时间（邀请完成的时间）
	Status       int       `json:"status"`        // 状态（0=待完成，1=已完成，2=已失效）
}

// InvitationRule 邀请奖励规则
type InvitationRule struct {
	RuleType     string `json:"rule_type"`     // 规则类型（INVITATION_REGISTER=邀请注册，INVITATION_ORDER=邀请首单）
	RuleName     string `json:"rule_name"`     // 规则名称（如：邀请新人注册）
	Description  string `json:"description"`   // 规则描述（详细说明奖励条件）
	CoinsAwarded int    `json:"coins_awarded"` // 奖励叶小币数量（注册奖励200，首单奖励50）
}

// CreateInvitationRequest 创建邀请请求
type CreateInvitationRequest struct {
	// 无需参数，系统自动生成邀请码
}

// CreateInvitationResponse 创建邀请响应
type CreateInvitationResponse struct {
	InvitationCode string `json:"invitation_code"` // 邀请码
	ShareUrl       string `json:"share_url"`       // 分享链接
	ShareTitle     string `json:"share_title"`     // 分享标题
	ShareDesc      string `json:"share_desc"`      // 分享描述
}

// AcceptInvitationRequest 接受邀请请求
type AcceptInvitationRequest struct {
	InvitationCode string `json:"invitation_code" form:"invitation_code" binding:"required"` // 邀请码
}

// AcceptInvitationResponse 接受邀请响应
type AcceptInvitationResponse struct {
	Success     bool   `json:"success"`      // 是否成功
	Message     string `json:"message"`      // 提示信息
	InviterName string `json:"inviter_name"` // 邀请人姓名
	Reward      int    `json:"reward"`       // 奖励叶小币数量
}

// InvitationStatsRequest 邀请统计请求
type InvitationStatsRequest struct {
	StartDate string `json:"start_date" form:"start_date"` // 开始日期
	EndDate   string `json:"end_date" form:"end_date"`     // 结束日期
}

// InvitationStatsResponse 邀请统计响应
type InvitationStatsResponse struct {
	TotalInvitations   int `json:"total_invitations"`   // 总邀请数
	SuccessInvitations int `json:"success_invitations"` // 成功邀请数
	TotalCoinsEarned   int `json:"total_coins_earned"`  // 总获得叶小币
	TodayInvitations   int `json:"today_invitations"`   // 今日邀请数
	WeekInvitations    int `json:"week_invitations"`    // 本周邀请数
	MonthInvitations   int `json:"month_invitations"`   // 本月邀请数
}

// InvitationLink 邀请链接信息
type InvitationLink struct {
	InviterID      uint       `json:"inviter_id"`      // 邀请人ID
	InvitationCode string     `json:"invitation_code"` // 邀请码
	ExpiredAt      *time.Time `json:"expired_at"`      // 过期时间
	IsValid        bool       `json:"is_valid"`        // 是否有效
}

// InvitationRewardLog 邀请奖励日志
type InvitationRewardLog struct {
	ID            uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:奖励日志ID"`
	InvitationID  uint      `json:"invitation_id" gorm:"not null;index;comment:邀请记录ID"`
	InviterID     uint      `json:"inviter_id" gorm:"not null;index;comment:邀请人用户ID"`
	InviteeID     uint      `json:"invitee_id" gorm:"not null;index;comment:被邀请人用户ID"`
	RewardType    string    `json:"reward_type" gorm:"type:varchar(50);not null;comment:奖励类型"`
	CoinsAwarded  int       `json:"coins_awarded" gorm:"not null;comment:奖励叶小币数量"`
	RuleID        uint      `json:"rule_id" gorm:"comment:规则ID"`
	TransactionID uint      `json:"transaction_id" gorm:"comment:交易记录ID"`
	Status        int       `json:"status" gorm:"default:1;comment:状态(1已发放,0待发放,-1发放失败)"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (irl *InvitationRewardLog) TableName() string {
	return "invitation_reward_logs"
}

// InvitationConfig 邀请配置
type InvitationConfig struct {
	ID                   uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:配置ID"`
	InvitationEnabled    bool      `json:"invitation_enabled" gorm:"default:true;comment:是否启用邀请功能"`
	MaxInvitationsPerDay int       `json:"max_invitations_per_day" gorm:"default:10;comment:每日最大邀请数"`
	InvitationExpireDays int       `json:"invitation_expire_days" gorm:"default:30;comment:邀请链接有效期(天)"`
	ShareTitle           string    `json:"share_title" gorm:"type:varchar(100);comment:分享标题"`
	ShareDescription     string    `json:"share_description" gorm:"type:text;comment:分享描述"`
	CreatedAt            time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt            time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (ic *InvitationConfig) TableName() string {
	return "invitation_config"
}

// 邀请状态常量
const (
	InvitationStatusPending   = 0 // 待完成
	InvitationStatusCompleted = 1 // 已完成
	InvitationStatusExpired   = 2 // 已失效
)

// 奖励类型常量
const (
	RewardTypeRegistration = "REGISTRATION" // 注册奖励
	RewardTypeFirstOrder   = "FIRST_ORDER"  // 首单奖励
	RewardTypeConsumption  = "CONSUMPTION"  // 消费奖励
)

// 邀请奖励规则类型
const (
	InvitationRuleTypeRegister = "INVITATION_REGISTER" // 邀请注册
	InvitationRuleTypeOrder    = "INVITATION_ORDER"    // 邀请首单
)

// InvitationRepository 邀请仓库接口
type InvitationRepository interface {
	// 邀请记录相关
	CreateInvitationRecord(record *InvitationRecord) error
	GetInvitationRecord(invitationCode string) (*InvitationRecord, error)
	GetInvitationRecordByID(id uint) (*InvitationRecord, error)
	UpdateInvitationRecord(record *InvitationRecord) error
	GetUserInvitationRecords(userID uint, page, pageSize int) ([]*InvitationRecord, int64, error)

	// 邀请统计相关
	GetUserInvitationStats(userID uint) (*InvitationStatsResponse, error)
	GetUserTotalInvitationCoins(userID uint) (int, error)

	// 邀请配置相关
	GetInvitationConfig() (*InvitationConfig, error)
	UpdateInvitationConfig(config *InvitationConfig) error

	// 奖励日志相关
	CreateRewardLog(log *InvitationRewardLog) error
	GetRewardLogs(invitationID uint) ([]*InvitationRewardLog, error)
}
