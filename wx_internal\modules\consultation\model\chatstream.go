package model

import (
	"context"
	"time"

	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// ChatStream 聊天流数据模型
type ChatStream struct {
	ID         int64          `db:"id" gorm:"primaryKey;autoIncrement"`
	SessionID  string         `db:"session_id" gorm:"type:varchar(64);index:idx_session_id"`
	ChunkIndex int            `db:"chunk_index" gorm:"index:idx_session_chunk,priority:2"`
	Content    string         `db:"content" gorm:"type:text"`
	IsFinal    bool           `db:"is_final" gorm:"default:false"`
	CreatedAt  time.Time      `db:"created_at" gorm:"autoCreateTime"`
	DeletedAt  gorm.DeletedAt `gorm:"index"`
}

// TableName 设置ChatStream的表名
func (ChatStream) TableName() string {
	return "med_chat_stream"
}

// ChatStreamModel 聊天流数据模型接口
type ChatStreamModel interface {
	Insert(ctx context.Context, data *ChatStream) error
	FindBySessionID(ctx context.Context, sessionID string) ([]*ChatStream, error)
}

// defaultChatStreamModel 默认实现
type defaultChatStreamModel struct{}

// NewChatStreamModel 创建聊天流数据模型
func NewChatStreamModel() ChatStreamModel {
	return &defaultChatStreamModel{}
}

// Insert 插入聊天流数据
func (m *defaultChatStreamModel) Insert(ctx context.Context, data *ChatStream) error {
	if data.CreatedAt.IsZero() {
		data.CreatedAt = time.Now()
	}

	return mysql.Master().WithContext(ctx).Create(data).Error
}

// FindBySessionID 根据会话ID查询聊天流数据
func (m *defaultChatStreamModel) FindBySessionID(ctx context.Context, sessionID string) ([]*ChatStream, error) {
	var result []*ChatStream

	err := mysql.Slave().WithContext(ctx).
		Where("session_id = ? AND deleted_at IS NULL", sessionID).
		Order("chunk_index ASC").
		Find(&result).Error

	if err != nil {
		logx.Errorf("查询聊天流数据失败: %v", err)
		return nil, err
	}

	return result, nil
}
