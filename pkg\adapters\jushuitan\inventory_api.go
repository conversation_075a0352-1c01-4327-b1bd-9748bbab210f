package jushuitan

import (
	"context"
	"fmt"
	"time"

	jsoniter "github.com/json-iterator/go"
)

// InventoryQueryRequest 商品库存查询请求
type InventoryQueryRequest struct {
	WmsCoID       int    `json:"wms_co_id,omitempty"`      // 分仓公司编号，值不传或为0查询所有仓的总库存
	PageIndex     int    `json:"page_index"`               // 第几页，从1开始
	PageSize      int    `json:"page_size"`                // 默认30，最大不超过100
	ModifiedBegin string `json:"modified_begin,omitempty"` // 修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天
	ModifiedEnd   string `json:"modified_end,omitempty"`   // 修改结束时间，和结束时间必须同时存在，时间间隔不能超过七天
	SkuIDs        string `json:"sku_ids,omitempty"`        // 商品编码，多个用逗号分隔，最大不超过100个
	HasLockQty    bool   `json:"has_lock_qty,omitempty"`   // 是否查询库存锁定数
	Names         string `json:"names,omitempty"`          // 商品名称，多个用逗号分隔，最大不超过100个
	IIDs          string `json:"i_ids,omitempty"`          // 款式编码，多个用逗号分隔
	TS            int64  `json:"ts,omitempty"`             // 时间戳，防漏单，如果用ts查询不需要传时间查询条件
}

// InventoryItem 商品库存项
type InventoryItem struct {
	SkuID         string  `json:"sku_id"`          // 商品编码
	TS            int64   `json:"ts"`              // 时间戳
	IID           string  `json:"i_id"`            // 款式编码
	Qty           int     `json:"qty"`             // 主仓实际库存
	OrderLock     int     `json:"order_lock"`      // 订单占有数
	PickLock      int     `json:"pick_lock"`       // 仓库待发数
	VirtualQty    int     `json:"virtual_qty"`     // 虚拟库存
	PurchaseQty   int     `json:"purchase_qty"`    // 采购在途数
	ReturnQty     int     `json:"return_qty"`      // 销退仓库存
	InQty         int     `json:"in_qty"`          // 进货仓库存
	DefectiveQty  int     `json:"defective_qty"`   // 次品库存
	Modified      string  `json:"modified"`        // 修改时间
	MinQty        int     `json:"min_qty"`         // 安全库存下限
	MaxQty        int     `json:"max_qty"`         // 安全库存上限
	LockQty       int     `json:"lock_qty"`        // 库存锁定数
	Name          string  `json:"name"`            // 商品名称
	CustomizeQty1 int     `json:"customize_qty_1"` // 自定义仓1
	CustomizeQty2 int     `json:"customize_qty_2"` // 自定义仓2
	CustomizeQty3 int     `json:"customize_qty_3"` // 自定义仓3
	AllocateQty   int     `json:"allocate_qty"`    // 调拨在途数
	SaleRefundQty float64 `json:"sale_refund_qty"` // 销退在途数
}

// InventoryQueryData 商品库存查询响应数据
type InventoryQueryData struct {
	PageSize   int             `json:"page_size"`  // 每页多少条
	PageIndex  int             `json:"page_index"` // 第几页
	HasNext    bool            `json:"has_next"`   // 是否有下一页
	Inventorys []InventoryItem `json:"inventorys"` // 数据集合
}

// InventoryQueryResponse 商品库存查询响应
type InventoryQueryResponse struct {
	Code int                `json:"code"` // 错误码
	Msg  string             `json:"msg"`  // 错误描述
	Data InventoryQueryData `json:"data"` // 响应数据
}

// QueryInventory 商品库存查询
func (c *Client) QueryInventory(ctx context.Context, req *InventoryQueryRequest) (*InventoryQueryResponse, error) {
	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 校验修改时间范围
	if (req.ModifiedBegin != "" || req.ModifiedEnd != "") && (req.ModifiedBegin == "" || req.ModifiedEnd == "") {
		return nil, fmt.Errorf("修改起始时间和结束时间必须同时存在")
	}

	// 校验时间间隔
	if req.ModifiedBegin != "" && req.ModifiedEnd != "" {
		begin, err := time.Parse("2006-01-02 15:04:05", req.ModifiedBegin)
		if err != nil {
			return nil, fmt.Errorf("修改起始时间格式错误：%w", err)
		}

		end, err := time.Parse("2006-01-02 15:04:05", req.ModifiedEnd)
		if err != nil {
			return nil, fmt.Errorf("修改结束时间格式错误：%w", err)
		}

		// 检查时间间隔不能超过7天
		if end.Sub(begin).Hours() > 7*24 {
			return nil, fmt.Errorf("时间间隔不能超过七天")
		}
	}

	// 调用接口
	body, err := c.doRequest(ctx, InventoryQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("商品库存查询请求失败: %w", err)
	}

	// 解析返回结果
	var resp InventoryQueryResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析商品库存查询响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}

	return &resp, nil
}

// PackQueryRequest 箱及仓位库存查询请求
type PackQueryRequest struct {
	SkuIDs    []string `json:"sku_ids,omitempty"`     // 商品编码列表，最多20个
	WmsCoID   int      `json:"wms_co_id,omitempty"`   // 仓库编号
	PackType  string   `json:"pack_type,omitempty"`   // 类型，Bin=仓位库存，DefaultPack=暂存位，Pack=装箱库存
	PageSize  int      `json:"page_size,omitempty"`   // 每页多少条（最大200）
	PageIndex int      `json:"page_index,omitempty"`  // 第几页
	OwnerCoID int      `json:"owner_co_id,omitempty"` // 货主编号
	StartTime string   `json:"start_time,omitempty"`  // 修改开始时间
	EndTime   string   `json:"end_time,omitempty"`    // 修改结束时间（开始结束范围不超过1天）
}

// PackItem 箱及仓位库存项
type PackItem struct {
	WmsCoID        string `json:"wms_co_id"`       // 分仓编号
	PackID         string `json:"pack_id"`         // 箱号
	WhID           int    `json:"wh_id"`           // 仓库编号
	PackType       string `json:"pack_type"`       // 类型
	Bin            string `json:"bin"`             // 主仓位
	ItemBin        string `json:"item_bin"`        // 明细仓位
	SkuID          string `json:"sku_id"`          // 商品编码
	Qty            int    `json:"qty"`             // 数量
	ExpirationDate string `json:"expiration_date"` // 有效期
	SupplierID     int    `json:"supplier_id"`     // 供应商ID
	ProductDate    string `json:"product_date"`    // 生产日期
	BatchNo        string `json:"batch_no"`        // 生产批次
	Modified       string `json:"modified"`        // 修改时间
	BinGroup       string `json:"bin_group"`       // 仓位分组
}

// PackQueryData 箱及仓位库存查询响应数据
type PackQueryData struct {
	PageSize  int        `json:"page_size"`  // 每页多少条
	PageIndex int        `json:"page_index"` // 第几页
	DateCount int        `json:"date_count"` // 总条数
	Datas     []PackItem `json:"datas"`      // 数据集合
}

// PackQueryResponse 箱及仓位库存查询响应
type PackQueryResponse struct {
	Code int           `json:"code"` // 错误码，0代表成功
	Msg  string        `json:"msg"`  // 描述
	Data PackQueryData `json:"data"` // 响应数据
}

// QueryPack 箱及仓位库存查询
func (c *Client) QueryPack(ctx context.Context, req *PackQueryRequest) (*PackQueryResponse, error) {
	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 200 {
		req.PageSize = 200 // 最大不超过200
	}

	// 校验查询条件
	if len(req.SkuIDs) == 0 && req.StartTime == "" && req.EndTime == "" {
		return nil, fmt.Errorf("商品编码和修改时间范围不能同时为空")
	}

	// 校验时间范围
	if (req.StartTime != "" || req.EndTime != "") && (req.StartTime == "" || req.EndTime == "") {
		return nil, fmt.Errorf("修改起始时间和结束时间必须同时存在")
	}

	// 校验时间间隔
	if req.StartTime != "" && req.EndTime != "" {
		begin, err := time.Parse("2006-01-02 15:04:05", req.StartTime)
		if err != nil {
			return nil, fmt.Errorf("修改起始时间格式错误：%w", err)
		}

		end, err := time.Parse("2006-01-02 15:04:05", req.EndTime)
		if err != nil {
			return nil, fmt.Errorf("修改结束时间格式错误：%w", err)
		}

		// 检查时间间隔不能超过1天
		if end.Sub(begin).Hours() > 24 {
			return nil, fmt.Errorf("时间间隔不能超过1天")
		}
	}

	// 商品编码数量限制
	if len(req.SkuIDs) > 20 {
		return nil, fmt.Errorf("商品编码列表最多支持20个")
	}

	// 调用接口
	body, err := c.doRequest(ctx, PackQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("箱及仓位库存查询请求失败: %w", err)
	}

	// 解析返回结果
	var resp PackQueryResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析箱及仓位库存查询响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}

	return &resp, nil
}

// InventoryUploadRequest 新建盘点单请求
type InventoryUploadRequest struct {
	WmsCoID     int                   `json:"wms_co_id,omitempty"`    // 仓库编码，不填写默认主仓
	Type        string                `json:"type,omitempty"`         // 盘点类型，全量:check，增量:adjust(默认)
	IsConfirm   bool                  `json:"is_confirm,omitempty"`   // 是否自动确认单据；默认false
	SoID        string                `json:"so_id"`                  // 外部单号（自定义传唯一值不可重复传）
	Warehouse   string                `json:"warehouse"`              // 仓库;主仓=1，销退仓=2，进货仓=3，次品仓=4,自定义1仓=6，自定义2仓=7，自定义3仓=8
	Remark      string                `json:"remark,omitempty"`       // 备注
	Bin         string                `json:"bin,omitempty"`          // 仓位(开启精细化管理有效且必填)
	DefaultType string                `json:"default_type,omitempty"` // 暂存位类型（开启精细化管理有效且必填可传值Default, Pick, None）
	Items       []InventoryUploadItem `json:"items"`                  // 商品明细，最大500
}

// InventoryUploadItem 盘点单商品明细
type InventoryUploadItem struct {
	Qty            int      `json:"qty"`                       // 数量
	SkuSns         []string `json:"sku_sns,omitempty"`         // 唯一码集合，增量盘点不支持传
	SkuID          string   `json:"sku_id"`                    // 商品编码
	SupplierID     int      `json:"supplier_id,omitempty"`     // 供应商编码
	BatchID        string   `json:"batch_id,omitempty"`        // 批次号
	ProducedDate   string   `json:"produced_date,omitempty"`   // 生产日期
	ExpirationDate string   `json:"expiration_date,omitempty"` // 有效期
}

// InventoryUploadResponseData 盘点单创建结果
type InventoryUploadResponseData struct {
	Msg  string `json:"msg"`   // 执行结果
	SoID string `json:"so_id"` // 外部单号
	IoID int    `json:"io_id"` // 盘点单号
}

// InventoryUploadResponse 新建盘点单响应
type InventoryUploadResponse struct {
	Code      int                         `json:"code"`      // 状态码；0表示成功
	IsSuccess bool                        `json:"issuccess"` // 执行结果
	Msg       string                      `json:"msg"`       // 执行结果描述信息
	Data      InventoryUploadResponseData `json:"data"`      // 响应数据
}

// UploadInventory 新建盘点单
func (c *Client) UploadInventory(ctx context.Context, req *InventoryUploadRequest) (*InventoryUploadResponse, error) {
	// 参数校验
	if req.SoID == "" {
		return nil, fmt.Errorf("外部单号不能为空")
	}
	if req.Warehouse == "" {
		return nil, fmt.Errorf("仓库不能为空")
	}
	if len(req.Items) == 0 {
		return nil, fmt.Errorf("商品明细不能为空")
	}
	if len(req.Items) > 500 {
		return nil, fmt.Errorf("商品明细最多支持500个")
	}

	// 校验商品明细
	for _, item := range req.Items {
		if item.SkuID == "" {
			return nil, fmt.Errorf("商品编码不能为空")
		}
	}

	// 设置默认值
	if req.Type == "" {
		req.Type = "adjust" // 默认为增量盘点
	}

	// 调用接口
	body, err := c.doRequest(ctx, InventoryUploadAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("新建盘点单请求失败: %w", err)
	}

	// 解析返回结果
	var resp InventoryUploadResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析新建盘点单响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
		resp.IsSuccess = true
	}

	return &resp, nil
}

// InventoryCountQueryRequest 库存盘点查询请求
type InventoryCountQueryRequest struct {
	PageIndex     int    `json:"page_index,omitempty"`     // 第几页，从1开始，默认1
	PageSize      int    `json:"page_size,omitempty"`      // 每页多少条，默认30，最大50
	ModifiedBegin string `json:"modified_begin,omitempty"` // 修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天
	ModifiedEnd   string `json:"modified_end,omitempty"`   // 修改结束时间，和起始时间必须同时存在，时间间隔不能超过七天
	IoIDs         string `json:"io_ids,omitempty"`         // 指定盘点单号，多个用逗号分隔，最多50，和时间段不能同时为空
	Status        string `json:"status,omitempty"`         // 状态;WaitConfirm:待确认,Confirmed:生效,Archive:归档,Cancelled:取消,Delete:作废
}

// InventoryBatch 批次信息
type InventoryBatch struct {
	BatchNo        string `json:"batch_no"`        // 批次号
	Qty            int    `json:"qty"`             // 数量
	SkuID          string `json:"sku_id"`          // 商品编码
	SupplierName   string `json:"supplier_name"`   // 供应商名称
	ExpirationDate string `json:"expiration_date"` // 有效期至
	SupplierID     int    `json:"supplier_id"`     // 供应商编号
	ProductDate    string `json:"product_date"`    // 批次日期
}

// InventorySerialNumber 唯一码信息
type InventorySerialNumber struct {
	SkuID string `json:"sku_id"` // 商品编码
	SN    string `json:"sn"`     // 唯一码
}

// InventoryCountItem 盘点单商品项
type InventoryCountItem struct {
	IoID            int                     `json:"io_id"`            // 盘点单号
	IoiID           int                     `json:"ioi_id"`           // 子单号
	SkuID           string                  `json:"sku_id"`           // 商品编码
	IID             string                  `json:"i_id"`             // 款式编码
	Name            string                  `json:"name"`             // 商品名称
	PropertiesValue string                  `json:"properties_value"` // 颜色及规格
	RQty            int                     `json:"r_qty"`            // 盘点后数量
	Qty             int                     `json:"qty"`              // 盈亏数量
	BatchID         string                  `json:"batch_id"`         // 批次号
	ProductDate     string                  `json:"product_date"`     // 批次日期
	SupplierID      int                     `json:"supplier_id"`      // 供应商编号
	ExpirationDate  string                  `json:"expiration_date"`  // 有效期至
	Batches         []InventoryBatch        `json:"batchs"`           // 批次集合
	SNs             []InventorySerialNumber `json:"sns"`              // 唯一码集合
	FStatus         string                  `json:"f_status"`         // 财审状态，WaitConfirm=待审核;Confirmed=已审核
	LockWhID        int                     `json:"lock_wh_id"`       // 虚拟仓编码
	LockWhName      string                  `json:"lock_wh_name"`     // 虚拟仓名称
	Labels          string                  `json:"labels"`           // 标记|多标签
}

// InventoryCountData 盘点单数据
type InventoryCountData struct {
	Type        string               `json:"type"`         // 单据类型
	IoID        int                  `json:"io_id"`        // 盘点单号
	IoDate      string               `json:"io_date"`      // 单据日期
	Status      string               `json:"status"`       // 状态
	Warehouse   string               `json:"warehouse"`    // 仓库名称
	CreatorName string               `json:"creator_name"` // 创建人
	Remark      string               `json:"remark"`       // 备注
	WhID        int                  `json:"wh_id"`        // 仓库编号
	WmsCoID     int                  `json:"wms_co_id"`    // 分仓编号
	Modified    string               `json:"modified"`     // 修改时间
	Items       []InventoryCountItem `json:"items"`        // 商品集合
}

// InventoryCountQueryData 库存盘点查询响应数据
type InventoryCountQueryData struct {
	PageSize  int                  `json:"page_size"`  // 每页多少条
	PageIndex int                  `json:"page_index"` // 第几页
	DataCount int                  `json:"data_count"` // 总条数
	PageCount int                  `json:"page_count"` // 总页数
	HasNext   bool                 `json:"has_next"`   // 是否有下一页
	Datas     []InventoryCountData `json:"datas"`      // 数据集合
}

// InventoryCountQueryResponse 库存盘点查询响应
type InventoryCountQueryResponse struct {
	Code int                     `json:"code"` // 错误码
	Msg  string                  `json:"msg"`  // 错误描述
	Data InventoryCountQueryData `json:"data"` // 响应数据
}

// QueryInventoryCount 库存盘点查询
func (c *Client) QueryInventoryCount(ctx context.Context, req *InventoryCountQueryRequest) (*InventoryCountQueryResponse, error) {
	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 校验修改时间范围
	if (req.ModifiedBegin != "" || req.ModifiedEnd != "") && (req.ModifiedBegin == "" || req.ModifiedEnd == "") {
		return nil, fmt.Errorf("修改起始时间和结束时间必须同时存在")
	}

	// 校验时间间隔
	if req.ModifiedBegin != "" && req.ModifiedEnd != "" {
		begin, err := time.Parse("2006-01-02 15:04:05", req.ModifiedBegin)
		if err != nil {
			return nil, fmt.Errorf("修改起始时间格式错误：%w", err)
		}

		end, err := time.Parse("2006-01-02 15:04:05", req.ModifiedEnd)
		if err != nil {
			return nil, fmt.Errorf("修改结束时间格式错误：%w", err)
		}

		// 检查时间间隔不能超过7天
		if end.Sub(begin).Hours() > 7*24 {
			return nil, fmt.Errorf("时间间隔不能超过七天")
		}
	}

	// 校验查询条件
	if req.ModifiedBegin == "" && req.ModifiedEnd == "" && req.IoIDs == "" {
		return nil, fmt.Errorf("查询条件不能为空，时间范围和盘点单号不能同时为空")
	}

	// 调用接口
	body, err := c.doRequest(ctx, InventoryCountQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("库存盘点查询请求失败: %w", err)
	}

	// 解析返回结果
	var resp InventoryCountQueryResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析库存盘点查询响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}

	return &resp, nil
}

// UpdateVirtualInventoryRequest 更新虚拟库存请求
type UpdateVirtualInventoryRequest struct {
	WmsCoID   int                          `json:"wms_co_id"`   // 分仓编码
	SkuAndQty []UpdateVirtualInventoryItem `json:"sku_and_qty"` // 商品明细，最多1000条
}

// UpdateVirtualInventoryItem 更新虚拟库存明细项
type UpdateVirtualInventoryItem struct {
	SkuID string `json:"sku_id"` // 商品编码
	Qty   int    `json:"qty"`    // 商品数量
}

// UpdateVirtualInventoryResponse 更新虚拟库存响应
type UpdateVirtualInventoryResponse struct {
	Code string `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data string `json:"data"` // 响应数据
}

// UpdateVirtualInventory 导入/更新虚拟库存
func (c *Client) UpdateVirtualInventory(ctx context.Context, req *UpdateVirtualInventoryRequest) (*UpdateVirtualInventoryResponse, error) {
	// 参数校验
	if req.WmsCoID <= 0 {
		return nil, fmt.Errorf("分仓编码不能为空")
	}
	if len(req.SkuAndQty) == 0 {
		return nil, fmt.Errorf("商品明细不能为空")
	}
	if len(req.SkuAndQty) > 1000 {
		return nil, fmt.Errorf("商品明细最多支持1000个")
	}

	// 校验商品明细
	for _, item := range req.SkuAndQty {
		if item.SkuID == "" {
			return nil, fmt.Errorf("商品编码不能为空")
		}
	}

	// 调用接口
	body, err := c.doRequest(ctx, UpdateVirtualInventoryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("更新虚拟库存请求失败: %w", err)
	}

	// 解析返回结果
	var resp UpdateVirtualInventoryResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析更新虚拟库存响应失败: %w", err)
	}

	return &resp, nil
}

// PackBatchQueryRequest 箱及仓位库存批量查询请求
type PackBatchQueryRequest struct {
	PageIndex int      `json:"page_index,omitempty"`  // 页码，默认第一页
	PageSize  int      `json:"page_size,omitempty"`   // 单页条数，最多500
	SkuIDs    []string `json:"sku_ids,omitempty"`     // 商品编码列表
	WmsCoID   int      `json:"wms_co_id,omitempty"`   // 仓储方，默认为授权账号
	OwnerCoID int      `json:"owner_co_id,omitempty"` // 货主
	PackType  string   `json:"pack_type,omitempty"`   // 仓位类型。 Bin=仓位库存，DefaultPack=暂存位 Pack=装箱库存
}

// PackBatchItem 箱及仓位库存批量查询项
type PackBatchItem struct {
	PackID         string `json:"pack_id"`         // 箱号
	WhhID          int    `json:"whh_id"`          // 仓库
	PackType       string `json:"pack_type"`       // 仓位类型
	Bin            string `json:"bin"`             // 仓位
	BinGroup       string `json:"bin_group"`       // 仓位分组
	SkuID          string `json:"sku_id"`          // 商品编码
	Qty            int    `json:"qty"`             // 数量
	BatchID        string `json:"batch_id"`        // 生产批次号
	SupplierID     int    `json:"supplier_id"`     // 供应商
	ProducedDate   string `json:"produced_date"`   // 生产日期
	ExpirationDate string `json:"expiration_date"` // 到期日
}

// PackBatchQueryData 箱及仓位库存批量查询数据
type PackBatchQueryData struct {
	PageIndex int             `json:"page_index"` // 当前页码
	PageSize  int             `json:"page_size"`  // 当前单页大小
	DataCount int             `json:"data_count"` // 数据总条数
	PageCount int             `json:"page_count"` // 总页数
	HasNext   bool            `json:"has_next"`   // 是否有下一页
	Data      []PackBatchItem `json:"data"`       // 数据
}

// PackBatchQueryResponse 箱及仓位库存批量查询响应
type PackBatchQueryResponse struct {
	Code      int                `json:"code"`       // 错误码
	Msg       string             `json:"msg"`        // 错误描述
	Data      PackBatchQueryData `json:"data"`       // 响应数据
	RequestID string             `json:"request_id"` // 请求ID
}

// QueryPackBatch 箱及仓位库存批量查询
func (c *Client) QueryPackBatch(ctx context.Context, req *PackBatchQueryRequest) (*PackBatchQueryResponse, error) {
	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 500 {
		req.PageSize = 500 // 最大不超过500
	}

	// 调用接口
	body, err := c.doRequest(ctx, PackBatchQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("箱及仓位库存批量查询请求失败: %w", err)
	}

	// 解析返回结果
	var resp PackBatchQueryResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析箱及仓位库存批量查询响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}

	return &resp, nil
}

// 以下是库存相关的数据库模型

// InventoryDB 商品库存数据库模型
type InventoryDB struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	SkuID         string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`              // 商品编码
	TS            int64     `gorm:"column:ts;comment:时间戳" json:"ts"`                             // 时间戳
	IID           string    `gorm:"column:i_id;index;comment:款式编码" json:"i_id"`                  // 款式编码
	Qty           int       `gorm:"column:qty;comment:主仓实际库存" json:"qty"`                        // 主仓实际库存
	OrderLock     int       `gorm:"column:order_lock;comment:订单占有数" json:"order_lock"`           // 订单占有数
	PickLock      int       `gorm:"column:pick_lock;comment:仓库待发数" json:"pick_lock"`             // 仓库待发数
	VirtualQty    int       `gorm:"column:virtual_qty;comment:虚拟库存" json:"virtual_qty"`          // 虚拟库存
	PurchaseQty   int       `gorm:"column:purchase_qty;comment:采购在途数" json:"purchase_qty"`       // 采购在途数
	ReturnQty     int       `gorm:"column:return_qty;comment:销退仓库存" json:"return_qty"`           // 销退仓库存
	InQty         int       `gorm:"column:in_qty;comment:进货仓库存" json:"in_qty"`                   // 进货仓库存
	DefectiveQty  int       `gorm:"column:defective_qty;comment:次品库存" json:"defective_qty"`      // 次品库存
	Modified      string    `gorm:"column:modified;comment:修改时间" json:"modified"`                // 修改时间
	MinQty        int       `gorm:"column:min_qty;comment:安全库存下限" json:"min_qty"`                // 安全库存下限
	MaxQty        int       `gorm:"column:max_qty;comment:安全库存上限" json:"max_qty"`                // 安全库存上限
	LockQty       int       `gorm:"column:lock_qty;comment:库存锁定数" json:"lock_qty"`               // 库存锁定数
	Name          string    `gorm:"column:name;comment:商品名称" json:"name"`                        // 商品名称
	CustomizeQty1 int       `gorm:"column:customize_qty_1;comment:自定义仓1" json:"customize_qty_1"` // 自定义仓1
	CustomizeQty2 int       `gorm:"column:customize_qty_2;comment:自定义仓2" json:"customize_qty_2"` // 自定义仓2
	CustomizeQty3 int       `gorm:"column:customize_qty_3;comment:自定义仓3" json:"customize_qty_3"` // 自定义仓3
	AllocateQty   int       `gorm:"column:allocate_qty;comment:调拨在途数" json:"allocate_qty"`       // 调拨在途数
	SaleRefundQty float64   `gorm:"column:sale_refund_qty;comment:销退在途数" json:"sale_refund_qty"` // 销退在途数
	WmsCoID       int       `gorm:"column:wms_co_id;index;comment:仓库编号" json:"wms_co_id"`        // 仓库编号
	CreatedAt     time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`          // 记录创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`          // 记录更新时间
}

// TableName 指定表名
func (InventoryDB) TableName() string {
	return "jst_inventory"
}

// PackDB 箱及仓位库存数据库模型
type PackDB struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	WmsCoID        string    `gorm:"column:wms_co_id;index;comment:分仓编号" json:"wms_co_id"`      // 分仓编号
	PackID         string    `gorm:"column:pack_id;index;comment:箱号" json:"pack_id"`            // 箱号
	WhID           int       `gorm:"column:wh_id;comment:仓库编号" json:"wh_id"`                    // 仓库编号
	PackType       string    `gorm:"column:pack_type;comment:类型" json:"pack_type"`              // 类型
	Bin            string    `gorm:"column:bin;comment:主仓位" json:"bin"`                         // 主仓位
	ItemBin        string    `gorm:"column:item_bin;comment:明细仓位" json:"item_bin"`              // 明细仓位
	SkuID          string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`            // 商品编码
	Qty            int       `gorm:"column:qty;comment:数量" json:"qty"`                          // 数量
	ExpirationDate string    `gorm:"column:expiration_date;comment:有效期" json:"expiration_date"` // 有效期
	SupplierID     int       `gorm:"column:supplier_id;comment:供应商ID" json:"supplier_id"`       // 供应商ID
	ProductDate    string    `gorm:"column:product_date;comment:生产日期" json:"product_date"`      // 生产日期
	BatchNo        string    `gorm:"column:batch_no;comment:生产批次" json:"batch_no"`              // 生产批次
	Modified       string    `gorm:"column:modified;comment:修改时间" json:"modified"`              // 修改时间
	BinGroup       string    `gorm:"column:bin_group;comment:仓位分组" json:"bin_group"`            // 仓位分组
	CreatedAt      time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`        // 记录创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`        // 记录更新时间
}

// TableName 指定表名
func (PackDB) TableName() string {
	return "jst_pack"
}

// InventoryCountDB 盘点单数据库模型
type InventoryCountDB struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Type        string    `gorm:"column:type;comment:单据类型" json:"type"`                 // 单据类型
	IoID        int       `gorm:"column:io_id;index;comment:盘点单号" json:"io_id"`         // 盘点单号
	IoDate      string    `gorm:"column:io_date;comment:单据日期" json:"io_date"`           // 单据日期
	Status      string    `gorm:"column:status;comment:状态" json:"status"`               // 状态
	Warehouse   string    `gorm:"column:warehouse;comment:仓库名称" json:"warehouse"`       // 仓库名称
	CreatorName string    `gorm:"column:creator_name;comment:创建人" json:"creator_name"`  // 创建人
	Remark      string    `gorm:"column:remark;comment:备注" json:"remark"`               // 备注
	WhID        int       `gorm:"column:wh_id;comment:仓库编号" json:"wh_id"`               // 仓库编号
	WmsCoID     int       `gorm:"column:wms_co_id;index;comment:分仓编号" json:"wms_co_id"` // 分仓编号
	Modified    string    `gorm:"column:modified;comment:修改时间" json:"modified"`         // 修改时间
	CreatedAt   time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`   // 记录创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`   // 记录更新时间
}

// TableName 指定表名
func (InventoryCountDB) TableName() string {
	return "jst_inventory_count"
}

// InventoryCountItemDB 盘点单商品明细数据库模型
type InventoryCountItemDB struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	IoID            int       `gorm:"column:io_id;index;comment:盘点单号" json:"io_id"`                  // 盘点单号
	IoiID           int       `gorm:"column:ioi_id;index;comment:子单号" json:"ioi_id"`                 // 子单号
	SkuID           string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`                // 商品编码
	IID             string    `gorm:"column:i_id;index;comment:款式编码" json:"i_id"`                    // 款式编码
	Name            string    `gorm:"column:name;comment:商品名称" json:"name"`                          // 商品名称
	PropertiesValue string    `gorm:"column:properties_value;comment:颜色及规格" json:"properties_value"` // 颜色及规格
	RQty            int       `gorm:"column:r_qty;comment:盘点后数量" json:"r_qty"`                       // 盘点后数量
	Qty             int       `gorm:"column:qty;comment:盈亏数量" json:"qty"`                            // 盈亏数量
	BatchID         string    `gorm:"column:batch_id;comment:批次号" json:"batch_id"`                   // 批次号
	ProductDate     string    `gorm:"column:product_date;comment:批次日期" json:"product_date"`          // 批次日期
	SupplierID      int       `gorm:"column:supplier_id;comment:供应商编号" json:"supplier_id"`           // 供应商编号
	ExpirationDate  string    `gorm:"column:expiration_date;comment:有效期至" json:"expiration_date"`    // 有效期至
	FStatus         string    `gorm:"column:f_status;comment:财审状态" json:"f_status"`                  // 财审状态，WaitConfirm=待审核;Confirmed=已审核
	LockWhID        int       `gorm:"column:lock_wh_id;comment:虚拟仓编码" json:"lock_wh_id"`             // 虚拟仓编码
	LockWhName      string    `gorm:"column:lock_wh_name;comment:虚拟仓名称" json:"lock_wh_name"`         // 虚拟仓名称
	Labels          string    `gorm:"column:labels;comment:标记|多标签" json:"labels"`                    // 标记|多标签
	CreatedAt       time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`            // 记录创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`            // 记录更新时间
}

// TableName 指定表名
func (InventoryCountItemDB) TableName() string {
	return "jst_inventory_count_item"
}

// InventoryBatchDB 盘点单批次信息数据库模型
type InventoryBatchDB struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	IoID           int       `gorm:"column:io_id;index;comment:盘点单号" json:"io_id"`               // 盘点单号
	IoiID          int       `gorm:"column:ioi_id;index;comment:子单号" json:"ioi_id"`              // 子单号
	BatchNo        string    `gorm:"column:batch_no;comment:批次号" json:"batch_no"`                // 批次号
	Qty            int       `gorm:"column:qty;comment:数量" json:"qty"`                           // 数量
	SkuID          string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`             // 商品编码
	SupplierName   string    `gorm:"column:supplier_name;comment:供应商名称" json:"supplier_name"`    // 供应商名称
	ExpirationDate string    `gorm:"column:expiration_date;comment:有效期至" json:"expiration_date"` // 有效期至
	SupplierID     int       `gorm:"column:supplier_id;comment:供应商编号" json:"supplier_id"`        // 供应商编号
	ProductDate    string    `gorm:"column:product_date;comment:批次日期" json:"product_date"`       // 批次日期
	CreatedAt      time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`         // 记录创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`         // 记录更新时间
}

// TableName 指定表名
func (InventoryBatchDB) TableName() string {
	return "jst_inventory_batch"
}

// InventorySerialNumberDB 盘点单唯一码数据库模型
type InventorySerialNumberDB struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	IoID      int       `gorm:"column:io_id;index;comment:盘点单号" json:"io_id"`       // 盘点单号
	IoiID     int       `gorm:"column:ioi_id;index;comment:子单号" json:"ioi_id"`      // 子单号
	SkuID     string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`     // 商品编码
	SN        string    `gorm:"column:sn;comment:唯一码" json:"sn"`                    // 唯一码
	CreatedAt time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"` // 记录创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"` // 记录更新时间
}

// TableName 指定表名
func (InventorySerialNumberDB) TableName() string {
	return "jst_inventory_serial_number"
}

// PackBatchDB 箱及仓位批次库存数据库模型
type PackBatchDB struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	PackID         string    `gorm:"column:pack_id;index;comment:箱号" json:"pack_id"`            // 箱号
	WhhID          int       `gorm:"column:whh_id;comment:仓库" json:"whh_id"`                    // 仓库
	PackType       string    `gorm:"column:pack_type;comment:仓位类型" json:"pack_type"`            // 仓位类型
	Bin            string    `gorm:"column:bin;comment:仓位" json:"bin"`                          // 仓位
	BinGroup       string    `gorm:"column:bin_group;comment:仓位分组" json:"bin_group"`            // 仓位分组
	SkuID          string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`            // 商品编码
	Qty            int       `gorm:"column:qty;comment:数量" json:"qty"`                          // 数量
	BatchID        string    `gorm:"column:batch_id;comment:生产批次号" json:"batch_id"`             // 生产批次号
	SupplierID     int       `gorm:"column:supplier_id;comment:供应商" json:"supplier_id"`         // 供应商
	ProducedDate   string    `gorm:"column:produced_date;comment:生产日期" json:"produced_date"`    // 生产日期
	ExpirationDate string    `gorm:"column:expiration_date;comment:到期日" json:"expiration_date"` // 到期日
	WmsCoID        int       `gorm:"column:wms_co_id;index;comment:仓库编号" json:"wms_co_id"`      // 仓库编号
	OwnerCoID      int       `gorm:"column:owner_co_id;comment:货主编号" json:"owner_co_id"`        // 货主编号
	CreatedAt      time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`        // 记录创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`        // 记录更新时间
}

// TableName 指定表名
func (PackBatchDB) TableName() string {
	return "jst_pack_batch"
}
