package model

import (
	"yekaitai/pkg/infra/mysql"
)

// Create 创建核销用户
func (r *verifierRepository) Create(verifier *Verifier) error {
	return mysql.Master().Create(verifier).Error
}

// Update 更新核销用户
func (r *verifierRepository) Update(verifier *Verifier) error {
	return mysql.Master().Save(verifier).Error
}

// Delete 删除核销用户
func (r *verifierRepository) Delete(id uint) error {
	return mysql.Master().Delete(&Verifier{}, id).Error
}

// FindByID 根据ID查找核销用户
func (r *verifierRepository) FindByID(id uint) (*Verifier, error) {
	var verifier Verifier
	err := mysql.Slave().Where("id = ?", id).First(&verifier).Error
	if err != nil {
		return nil, err
	}
	return &verifier, nil
}

// FindByUserID 根据用户ID查找核销用户
func (r *verifierRepository) FindByUserID(userID uint) (*Verifier, error) {
	var verifier Verifier
	err := mysql.Slave().Where("user_id = ?", userID).First(&verifier).Error
	if err != nil {
		return nil, err
	}
	return &verifier, nil
}

// List 获取核销用户列表
func (r *verifierRepository) List(page, size int) ([]*Verifier, int64, error) {
	var verifiers []*Verifier
	var total int64

	db := mysql.Slave()

	// 获取总数
	if err := db.Model(&Verifier{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&verifiers).Error; err != nil {
		return nil, 0, err
	}

	return verifiers, total, nil
}

// ListByShop 根据门店ID获取核销用户列表
func (r *verifierRepository) ListByShop(shopID uint, page, size int) ([]*Verifier, int64, error) {
	var verifiers []*Verifier
	var total int64

	db := mysql.Slave().Where("shop_id = ?", shopID)

	// 获取总数
	if err := db.Model(&Verifier{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&verifiers).Error; err != nil {
		return nil, 0, err
	}

	return verifiers, total, nil
}

// UpdateStatus 更新核销用户状态
func (r *verifierRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&Verifier{}).Where("id = ?", id).Update("status", status).Error
}
