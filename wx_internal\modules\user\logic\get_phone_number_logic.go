package logic

import (
	"context"
	"fmt"
	"time"

	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/user/model"
	"yekaitai/wx_internal/modules/user/service"
	"yekaitai/wx_internal/modules/user/types"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPhoneNumberLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

func NewGetPhoneNumberLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *GetPhoneNumberLogic {
	return &GetPhoneNumberLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetPhoneNumber 获取微信手机号并更新用户信息
func (l *GetPhoneNumberLogic) GetPhoneNumber(req *types.GetPhoneNumberReq) (resp *types.GetPhoneNumberResp, err error) {
	// 1. 从上下文获取openId
	openIdVal := l.ctx.Value(middleware.OpenIDKey)
	if openIdVal == nil {
		logx.Error("获取手机号失败: openid不存在")
		return nil, fmt.Errorf("openid不存在")
	}

	openId, ok := openIdVal.(string)
	if !ok || openId == "" {
		logx.Error("获取手机号失败: 无效的openid")
		return nil, fmt.Errorf("无效的openid")
	}

	logx.Infof("开始获取用户手机号, openId: %s, code: %s", openId, req.Code)

	// 检查UserService是否为nil
	if l.svcCtx.UserService == nil {
		logx.Error("获取手机号失败: UserService未初始化")
		return nil, fmt.Errorf("系统内部错误: UserService未初始化")
	}

	// 2. 获取手机号并更新用户信息
	logx.Infof("调用UpdatePhoneNumber, openId: %s, code长度: %d", openId, len(req.Code))
	phoneInfo, err := l.svcCtx.UserService.UpdatePhoneNumber(l.ctx, openId, req.Code)
	if err != nil {
		logx.Errorf("获取手机号失败: %v", err)
		return nil, fmt.Errorf("获取手机号失败: %v", err)
	}

	logx.Infof("获取手机号成功, 电话号码: %s", phoneInfo.PhoneNumber)

	// 3. 获取用户详细信息和身份信息
	db := mysql.Master()

	// 查询用户基本信息
	var wxUser model.WxUser
	if err := db.Where("open_id = ?", openId).First(&wxUser).Error; err != nil {
		logx.Errorf("查询用户信息失败: %v", err)
		return nil, fmt.Errorf("查询用户信息失败: %v", err)
	}

	// 查询用户的所有身份
	var isPatient, isDoctor, isRedeemer bool
	var patientID, doctorID, redeemerID uint
	var roles []string

	// 检查患者身份（优先按user_id查询，如果没有再按手机号查询）
	var patient model.WxPatient
	if err := db.Where("user_id = ?", wxUser.UserID).First(&patient).Error; err == nil {
		isPatient = true
		patientID = patient.PatientID
		roles = append(roles, "patient")
	} else if wxUser.Mobile != "" {
		// 如果按user_id没找到，且有手机号，则按手机号查询
		if err := db.Where("mobile = ?", wxUser.Mobile).First(&patient).Error; err == nil {
			isPatient = true
			patientID = patient.PatientID
			roles = append(roles, "patient")

			// 如果通过手机号找到了患者记录，但user_id不匹配，需要更新user_id
			if patient.UserID != wxUser.UserID {
				logx.Infof("更新患者记录的user_id: patientID=%d, oldUserID=%d, newUserID=%d",
					patient.PatientID, patient.UserID, wxUser.UserID)
				db.Model(&patient).Update("user_id", wxUser.UserID)
			}
		}
	}

	// 检查医生身份（只有有手机号时才检查）
	var doctor model.WxDoctor
	if wxUser.Mobile != "" {
		if err := db.Where("mobile = ?", wxUser.Mobile).First(&doctor).Error; err == nil {
			isDoctor = true
			doctorID = doctor.DoctorID
			roles = append(roles, "doctor")
		}
	}

	// 检查核销用户身份（只有有手机号时才检查）
	var redeemer model.WxRedeemer
	if wxUser.Mobile != "" {
		if err := db.Where("mobile = ?", wxUser.Mobile).First(&redeemer).Error; err == nil {
			isRedeemer = true
			redeemerID = redeemer.RedeemerID
			roles = append(roles, "redeemer")
		}
	}

	// 如果用户没有任何角色，默认创建患者角色
	if !isPatient && !isDoctor && !isRedeemer {
		// 创建默认患者记录
		newPatient := model.WxPatient{
			UserID:    wxUser.UserID,
			Mobile:    wxUser.Mobile, // 设置手机号
			Status:    1,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if err := db.Create(&newPatient).Error; err != nil {
			logx.Errorf("创建默认患者记录失败: %v", err)
		} else {
			isPatient = true
			patientID = newPatient.PatientID
			roles = append(roles, "patient")
			logx.Infof("创建默认患者记录成功: patientID=%d, userID=%d, mobile=%s",
				newPatient.PatientID, wxUser.UserID, wxUser.Mobile)
		}
	} else if isPatient {
		// 如果患者记录已存在，确保手机号是最新的
		if patient.Mobile != wxUser.Mobile {
			logx.Infof("更新患者记录手机号: patientID=%d, oldMobile=%s, newMobile=%s",
				patient.PatientID, patient.Mobile, wxUser.Mobile)
			db.Model(&patient).Update("mobile", wxUser.Mobile)
		}
	}

	// 组装用户身份信息
	userRoles := map[string]interface{}{}
	if isPatient {
		userRoles["patient"] = map[string]interface{}{
			"patientId": patientID,
		}
	}
	if isDoctor {
		userRoles["doctor"] = map[string]interface{}{
			"doctorId": doctorID,
		}
	}
	if isRedeemer {
		userRoles["redeemer"] = map[string]interface{}{
			"redeemerId": redeemerID,
		}
	}

	logx.Infof("用户身份查询完成, 用户ID: %d, 角色: %v", wxUser.UserID, roles)

	// 4. 检查是否为新注册用户（手机号刚获取成功），触发注册后处理
	if phoneInfo.PhoneNumber != "" {
		logx.Infof("用户获取手机号成功，触发注册后处理: userID=%d, mobile=%s", wxUser.UserID, phoneInfo.PhoneNumber)

		// 异步执行注册后处理，避免影响接口响应时间
		go func() {
			// 触发用户等级升级检查
			userConsumptionService := service.NewUserConsumptionService()
			if err := userConsumptionService.TriggerUserLevelUpdateOnRegister(wxUser.UserID); err != nil {
				logx.Errorf("用户注册等级升级失败: userID=%d, error=%v", wxUser.UserID, err)
			} else {
				logx.Infof("用户注册等级升级成功: userID=%d", wxUser.UserID)
			}

			// 触发注册积分奖励
			if err := l.handleRegisterPointsReward(context.Background(), wxUser.UserID); err != nil {
				logx.Errorf("用户注册积分奖励失败: userID=%d, error=%v", wxUser.UserID, err)
			} else {
				logx.Infof("用户注册积分奖励成功: userID=%d", wxUser.UserID)
			}
		}()
	}

	// 5. 返回手机号和用户信息
	return &types.GetPhoneNumberResp{
		Mobile:   phoneInfo.PhoneNumber,
		UserID:   wxUser.UserID,
		Nickname: wxUser.Nickname,
		Avatar:   wxUser.Avatar,
		Roles:    roles,
		Identity: userRoles,
	}, nil
}

// handleRegisterPointsReward 处理注册积分奖励
func (l *GetPhoneNumberLogic) handleRegisterPointsReward(ctx context.Context, userID uint) error {
	// 检查用户等级规则中的注册奖励积分
	var levelRule struct {
		ExtraCoins int `json:"extra_coins"`
	}

	err := mysql.Master().Table("wx_user u").
		Select("COALESCE(ulr.extra_coins, 0) as extra_coins").
		Joins("LEFT JOIN user_level_rules ulr ON u.user_level = ulr.level_order").
		Where("u.user_id = ?", userID).
		First(&levelRule).Error

	if err != nil {
		logx.Errorf("查询用户等级规则失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("查询用户等级规则失败: %w", err)
	}

	// 如果没有注册奖励积分，直接返回
	if levelRule.ExtraCoins <= 0 {
		logx.Infof("用户等级无注册奖励积分: userID=%d", userID)
		return nil
	}

	// 检查是否已经奖励过（防止重复奖励）
	var existingRecord int64
	err = mysql.Master().Table("coin_transactions").
		Where("user_id = ? AND transaction_type = 'EARN' AND description LIKE '%注册奖励%'", userID).
		Count(&existingRecord).Error

	if err != nil {
		logx.Errorf("检查注册奖励记录失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("检查注册奖励记录失败: %w", err)
	}

	if existingRecord > 0 {
		logx.Infof("用户已获得注册奖励积分: userID=%d", userID)
		return nil
	}

	// 开始事务
	tx := mysql.Master().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 创建或更新用户积分账户
	err = tx.Exec(`
		INSERT INTO user_coins (user_id, total_coins, available_coins, frozen_coins, used_coins, created_at, updated_at)
		VALUES (?, ?, ?, 0, 0, NOW(), NOW())
		ON DUPLICATE KEY UPDATE
		total_coins = total_coins + VALUES(total_coins),
		available_coins = available_coins + VALUES(available_coins),
		updated_at = NOW()
	`, userID, levelRule.ExtraCoins, levelRule.ExtraCoins).Error

	if err != nil {
		tx.Rollback()
		logx.Errorf("更新用户积分账户失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("更新用户积分账户失败: %w", err)
	}

	// 2. 记录积分交易流水
	err = tx.Exec(`
		INSERT INTO coin_transactions (user_id, transaction_type, amount, balance, description, created_at, updated_at)
		VALUES (?, 'EARN', ?, (SELECT available_coins FROM user_coins WHERE user_id = ?), '注册奖励', NOW(), NOW())
	`, userID, levelRule.ExtraCoins, userID).Error

	if err != nil {
		tx.Rollback()
		logx.Errorf("记录积分交易流水失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("记录积分交易流水失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logx.Errorf("提交注册积分奖励事务失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("提交注册积分奖励事务失败: %w", err)
	}

	logx.Infof("注册积分奖励成功: userID=%d, 奖励积分=%d", userID, levelRule.ExtraCoins)
	return nil
}
