package bootstrap

import (
	"log"

	"yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/infra/mysql"
)

// MigrateDoctorTables 执行医生相关表结构迁移
func MigrateDoctorTables() error {
	log.Println("开始执行医生表结构迁移...")

	// 执行医生表结构迁移
	db := mysql.Master()

	// 迁移医生表，添加新字段
	db.Set("gorm:table_options", "COMMENT='医生信息表'").AutoMigrate(&doctor.WxDoctor{})

	// 迁移医生门店关系表，添加IsPrimary字段
	db.Set("gorm:table_options", "COMMENT='医生门店关系表'").AutoMigrate(&doctor.DoctorStoreRelation{})

	// 迁移医生标签关系表
	db.Set("gorm:table_options", "COMMENT='医生标签关系表'").AutoMigrate(&doctor.DoctorTag{})

	log.Println("医生表结构迁移完成")
	return nil
}
