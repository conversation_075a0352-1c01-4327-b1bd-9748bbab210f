package bootstrap

import (
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateAddressTables 执行用户地址模块表结构迁移
func MigrateAddressTables() error {
	db := mysql.Master()

	logx.Info("开始执行用户地址模块表结构迁移...")

	// 自动迁移用户地址表
	if err := db.AutoMigrate(&user.Address{}); err != nil {
		logx.Errorf("用户地址表迁移失败: %v", err)
		return err
	}
	logx.Info("用户地址表迁移完成")

	logx.Info("用户地址模块表结构迁移完成")
	return nil
}
