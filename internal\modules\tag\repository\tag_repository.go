package repository

import (
	"time"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// TagRepository 标签仓库接口
type TagRepository interface {
	// Create 创建标签
	Create(tag *user.Tag) error
	// Update 更新标签
	Update(tag *user.Tag) error
	// Delete 删除标签
	Delete(id uint) error
	// FindByID 根据ID查找标签
	FindByID(id uint) (*user.Tag, error)
	// FindByName 根据名称查找标签
	FindByName(name string) (*user.Tag, error)
	// FindByIDs 根据ID列表查找标签
	FindByIDs(ids []uint) ([]*user.Tag, error)
	// List 获取标签列表
	List(page, size int, query string) ([]*user.Tag, int64, error)
}

// tagRepository 标签仓库实现
type tagRepository struct {
	db *gorm.DB
}

// NewTagRepository 创建标签仓库
func NewTagRepository(db *gorm.DB) TagRepository {
	if db == nil {
		db = mysql.Master()
	}
	return &tagRepository{db: db}
}

// Create 创建标签
func (r *tagRepository) Create(tag *user.Tag) error {
	return r.db.Create(tag).Error
}

// Update 更新标签
func (r *tagRepository) Update(tag *user.Tag) error {
	return r.db.Save(tag).Error
}

// Delete 删除标签
func (r *tagRepository) Delete(id uint) error {
	return r.db.Model(&user.Tag{}).Where("id = ?", id).Update("deleted_at", time.Now()).Error
}

// FindByID 根据ID查找标签
func (r *tagRepository) FindByID(id uint) (*user.Tag, error) {
	var tag user.Tag
	err := r.db.Where("id = ? AND deleted_at IS NULL", id).First(&tag).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

// FindByName 根据名称查找标签
func (r *tagRepository) FindByName(name string) (*user.Tag, error) {
	var tag user.Tag
	err := r.db.Where("name = ? AND deleted_at IS NULL", name).First(&tag).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

// FindByIDs 根据ID列表查找标签
func (r *tagRepository) FindByIDs(ids []uint) ([]*user.Tag, error) {
	var tags []*user.Tag
	err := r.db.Where("id IN ? AND deleted_at IS NULL", ids).Find(&tags).Error
	if err != nil {
		return nil, err
	}
	return tags, nil
}

// List 获取标签列表
func (r *tagRepository) List(page, size int, query string) ([]*user.Tag, int64, error) {
	var tags []*user.Tag
	var total int64

	db := r.db.Model(&user.Tag{}).Where("deleted_at IS NULL")

	if query != "" {
		db = db.Where("name LIKE ?", "%"+query+"%")
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if page > 0 && size > 0 {
		offset := (page - 1) * size
		db = db.Offset(offset).Limit(size)
	}

	err = db.Order("id DESC").Find(&tags).Error
	if err != nil {
		return nil, 0, err
	}

	return tags, total, nil
}
