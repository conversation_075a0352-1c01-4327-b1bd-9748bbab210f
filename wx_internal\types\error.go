package types

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// SetupErrorHandler 设置全局错误处理器
func SetupErrorHandler() {
	// 设置httpx的错误处理器
	httpx.SetErrorHandler(func(err error) (int, interface{}) {
		var code int
		var msg string

		// 根据错误类型处理
		if e, ok := err.(*CodeError); ok {
			// 处理自定义CodeError类型
			code = e.Code
			msg = e.Message
		} else {
			// 处理其他类型错误，默认返回500内部服务器错误
			code = CodeInternalError
			msg = err.Error()
		}

		// 返回HTTP状态码200，让业务状态码在响应体内指示实际状态
		return http.StatusOK, NewErrorResponse(code, msg)
	})
}
