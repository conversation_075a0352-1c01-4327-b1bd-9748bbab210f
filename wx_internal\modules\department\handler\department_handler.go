package handler

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"

	"yekaitai/wx_internal/modules/department/model"
)

// DepartmentListRequest 科室列表请求
type DepartmentListRequest struct {
	Page     int    `form:"page,default=1"`       // 页码，默认1
	PageSize int    `form:"page_size,default=10"` // 每页数量，默认10
	WsjgID   string `form:"wsjgid,optional"`      // 卫生机构ID
	Search   string `form:"search,optional"`      // 搜索关键词
}

// DepartmentDetailRequest 科室详情请求
type DepartmentDetailRequest struct {
	ID uint `path:"id"` // 科室ID
}

// Response 通用响应结构
type Response struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// PageData 分页数据
type PageData struct {
	List  interface{} `json:"list"`
	Total int64       `json:"total"`
	Page  int         `json:"page"`
	Size  int         `json:"size"`
}

// GetDepartmentList 获取科室列表
func GetDepartmentList(w http.ResponseWriter, r *http.Request) {
	var req DepartmentListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &Response{
			Code: 1,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 创建科室仓库
	deptRepo := model.NewDepartmentRepository()

	// 查询科室列表
	depts, total, err := deptRepo.List(req.Page, req.PageSize, req.WsjgID, req.Search)
	if err != nil {
		logx.Errorf("获取科室列表失败: %v", err)
		httpx.OkJson(w, &Response{
			Code: 1,
			Msg:  "获取科室列表失败",
			Data: nil,
		})
		return
	}

	// 构建响应数据
	result := make([]map[string]interface{}, len(depts))
	for i, dept := range depts {
		result[i] = map[string]interface{}{
			"id":     dept.ID,
			"jgksid": dept.JgksID,
			"jgksmc": dept.JgksMC,
			"wsjgid": dept.WsjgID,
			"kslx":   dept.KsLx,
			"ksflmc": dept.KsFLMC,
			"zfbz":   dept.ZfBz,
			"fwfwmc": dept.FwFwMC,
			"gllbmc": dept.GlLbMC,
		}
	}

	// 返回响应
	httpx.OkJson(w, &Response{
		Code: 0,
		Msg:  "success",
		Data: &PageData{
			List:  result,
			Total: total,
			Page:  req.Page,
			Size:  req.PageSize,
		},
	})
}

// GetDepartmentDetail 获取科室详情
func GetDepartmentDetail(w http.ResponseWriter, r *http.Request) {
	// 从URL路径中获取科室ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		httpx.OkJson(w, &Response{
			Code: 1,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 获取ID参数
	idStr := parts[len(parts)-1]
	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		httpx.OkJson(w, &Response{
			Code: 1,
			Msg:  "ID参数错误",
			Data: nil,
		})
		return
	}

	// 创建科室仓库
	deptRepo := model.NewDepartmentRepository()

	// 查询科室详情
	dept, err := deptRepo.FindByID(uint(id))
	if err != nil {
		logx.Errorf("获取科室详情失败: %v", err)
		httpx.OkJson(w, &Response{
			Code: 1,
			Msg:  "科室不存在",
			Data: nil,
		})
		return
	}

	// 构建响应
	result := map[string]interface{}{
		"id":     dept.ID,
		"jgksid": dept.JgksID,
		"jgksmc": dept.JgksMC,
		"wsjgid": dept.WsjgID,
		"pym":    dept.PYM,
		"zfbz":   dept.ZfBz,
		"fwfw":   dept.FwFw,
		"fwfwmc": dept.FwFwMC,
		"gllb":   dept.GlLb,
		"gllbmc": dept.GlLbMC,
		"ksfldm": dept.KsFLDM,
		"ksflmc": dept.KsFLMC,
		"kslx":   dept.KsLx,
	}

	// 返回响应
	httpx.OkJson(w, &Response{
		Code: 0,
		Msg:  "success",
		Data: result,
	})
}
