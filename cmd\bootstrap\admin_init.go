package bootstrap

import (
	"fmt"
	"log"
	"time"

	"yekaitai/internal/modules/admin/model"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"
)

// InitDefaultAdmin 初始化默认管理员账号
func InitDefaultAdmin() error {
	log.Println("开始初始化管理员账号...")

	// 获取数据库连接
	db := mysql.Master()

	// 检查是否已存在管理员
	var count int64
	db.Model(&model.AdminUser{}).Count(&count)
	if count > 0 {
		log.Println("已存在管理员账号，跳过初始化")
		return nil
	}

	// 创建默认超级管理员账号
	password, err := utils.HashPassword("admin123") // 默认密码
	fmt.Println("password", password)
	if err != nil {
		return err
	}

	admin := model.AdminUser{
		Username:  "admin",
		Password:  password,
		Email:     "<EMAIL>",
		Mobile:    "13800138000",
		Status:    1,
		LastLogin: time.Now(),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := db.Create(&admin).Error; err != nil {
		return err
	}

	// 为管理员分配超级管理员角色
	userRole := model.AdminUserRole{
		AdminID:   admin.AdminID,
		RoleID:    1001, // 超级管理员角色ID
		CreatedAt: time.Now(),
	}

	if err := db.Create(&userRole).Error; err != nil {
		return err
	}

	log.Printf("成功创建管理员账号: %s", admin.Username)
	return nil
}
