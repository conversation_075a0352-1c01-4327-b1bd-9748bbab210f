package model

import (
	"time"
)

// CheckinRecord 签到记录
type CheckinRecord struct {
	ID             uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:签到记录ID"`
	UserID         uint      `json:"user_id" gorm:"not null;index:idx_checkin_user_date;comment:用户ID"`
	CheckinDate    string    `json:"checkin_date" gorm:"type:varchar(10);not null;index:idx_checkin_user_date;comment:签到日期(YYYY-MM-DD)"`
	CheckinTime    time.Time `json:"checkin_time" gorm:"not null;comment:签到时间"`
	ContinuousDays int       `json:"continuous_days" gorm:"default:1;comment:连续签到天数"`
	CoinsAwarded   int       `json:"coins_awarded" gorm:"default:0;comment:奖励叶小币数量"`
	IsShared       bool      `json:"is_shared" gorm:"default:false;comment:是否已分享"`
	ShareCoins     int       `json:"share_coins" gorm:"default:0;comment:分享奖励叶小币"`
	CreatedAt      time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (cr *CheckinRecord) TableName() string {
	return "checkin_records"
}

// CheckinConfig 签到配置
type CheckinConfig struct {
	ID               uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:配置ID"`
	Day              int       `json:"day" gorm:"not null;comment:签到天数"`
	CoinsReward      int       `json:"coins_reward" gorm:"not null;comment:奖励叶小币数量"`
	ShareCoinsReward int       `json:"share_coins_reward" gorm:"default:0;comment:分享奖励叶小币数量"`
	IsActive         bool      `json:"is_active" gorm:"default:true;comment:是否启用"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (cc *CheckinConfig) TableName() string {
	return "checkin_config"
}

// CheckinCalendarRequest 签到日历请求
type CheckinCalendarRequest struct {
	Year  int `json:"year" form:"year" binding:"required,min=2020,max=2030"` // 年份
	Month int `json:"month" form:"month" binding:"required,min=1,max=12"`    // 月份
}

// CheckinCalendarResponse 签到日历响应
type CheckinCalendarResponse struct {
	Year            int                 `json:"year"`             // 年份（如：2024）
	Month           int                 `json:"month"`            // 月份（1-12）
	TotalCoins      int                 `json:"total_coins"`      // 当前叶小币总数
	ContinuousDays  int                 `json:"continuous_days"`  // 连续签到天数（从最近一次签到开始计算）
	MonthlyCheckins int                 `json:"monthly_checkins"` // 本月签到次数
	TodayChecked    bool                `json:"today_checked"`    // 今日是否已签到
	CanCheckin      bool                `json:"can_checkin"`      // 是否可以签到（今天未签到时为true）
	CheckinDays     []CheckinDayInfo    `json:"checkin_days"`     // 签到日期信息（当月每一天的签到状态）
	RewardRules     []CheckinRewardRule `json:"reward_rules"`     // 奖励规则（连续签到的奖励配置）
}

// CheckinDayInfo 签到日期信息
type CheckinDayInfo struct {
	Day         int  `json:"day"`          // 日期（1-31）
	IsChecked   bool `json:"is_checked"`   // 是否已签到
	IsToday     bool `json:"is_today"`     // 是否是今天
	CanCheckin  bool `json:"can_checkin"`  // 是否可以签到（只有今天且未签到时为true）
	CoinsReward int  `json:"coins_reward"` // 奖励叶小币数量（已签到显示实际奖励，未签到显示预期奖励）
}

// CheckinRewardRule 签到奖励规则
type CheckinRewardRule struct {
	Day              int `json:"day"`                // 签到天数
	CoinsReward      int `json:"coins_reward"`       // 奖励叶小币数量
	ShareCoinsReward int `json:"share_coins_reward"` // 分享奖励叶小币数量
}

// CheckinRequest 签到请求
type CheckinRequest struct {
	Date string `json:"date" form:"date"` // 签到日期，可选，默认为今天
}

// CheckinResponse 签到响应
type CheckinResponse struct {
	Success        bool   `json:"success"`         // 签到是否成功
	Message        string `json:"message"`         // 提示信息
	ContinuousDays int    `json:"continuous_days"` // 连续签到天数
	CoinsAwarded   int    `json:"coins_awarded"`   // 奖励叶小币数量
	TotalCoins     int    `json:"total_coins"`     // 签到后叶小币总数
	CanShare       bool   `json:"can_share"`       // 是否可以分享
	ShareCoins     int    `json:"share_coins"`     // 分享可获得的叶小币
}

// ShareCheckinRequest 分享签到请求
type ShareCheckinRequest struct {
	Date string `json:"date" form:"date" binding:"required"` // 分享的签到日期
}

// ShareCheckinResponse 分享签到响应
type ShareCheckinResponse struct {
	Success      bool   `json:"success"`       // 分享是否成功
	Message      string `json:"message"`       // 提示信息
	CoinsAwarded int    `json:"coins_awarded"` // 分享奖励叶小币数量
	TotalCoins   int    `json:"total_coins"`   // 分享后叶小币总数
}

// CheckinStatsRequest 签到统计请求
type CheckinStatsRequest struct {
	StartDate string `json:"start_date" form:"start_date"` // 开始日期
	EndDate   string `json:"end_date" form:"end_date"`     // 结束日期
}

// CheckinStatsResponse 签到统计响应
type CheckinStatsResponse struct {
	TotalCheckins   int `json:"total_checkins"`   // 总签到次数
	ContinuousDays  int `json:"continuous_days"`  // 连续签到天数
	TotalCoins      int `json:"total_coins"`      // 总获得叶小币
	MonthlyCheckins int `json:"monthly_checkins"` // 本月签到次数
}

// CheckinPopupResponse 签到弹窗响应
type CheckinPopupResponse struct {
	ShowPopup      bool   `json:"show_popup"`      // 是否显示弹窗（今天未签到时为true）
	Message        string `json:"message"`         // 弹窗消息（如：签到可获得1叶小币，连续签到奖励更丰富！）
	TodayChecked   bool   `json:"today_checked"`   // 今日是否已签到
	ContinuousDays int    `json:"continuous_days"` // 连续签到天数（从最近一次签到开始计算）
	TodayReward    int    `json:"today_reward"`    // 今日签到奖励（根据连续天数计算的奖励数量）
	CanCheckin     bool   `json:"can_checkin"`     // 是否可以签到（今天未签到时为true）
}

// CheckinRepository 签到仓库接口
type CheckinRepository interface {
	// 签到记录相关
	CreateCheckinRecord(record *CheckinRecord) error
	GetCheckinRecord(userID uint, date string) (*CheckinRecord, error)
	GetUserCheckinRecords(userID uint, startDate, endDate string) ([]*CheckinRecord, error)
	GetUserContinuousDays(userID uint) (int, error)
	UpdateCheckinRecord(record *CheckinRecord) error

	// 签到配置相关
	GetCheckinConfigs() ([]*CheckinConfig, error)
	GetCheckinConfigByDay(day int) (*CheckinConfig, error)

	// 统计相关
	GetUserCheckinStats(userID uint, startDate, endDate string) (*CheckinStatsResponse, error)
	GetMonthlyCheckinDays(userID uint, year, month int) ([]string, error)
}
