package model

import (
	"time"

	"yekaitai/pkg/infra/mysql"
)

// AdminUserRepository 管理员用户仓库接口
type AdminUserRepository interface {
	// 基础CRUD
	Create(adminUser *AdminUser) error
	Update(adminUser *AdminUser) error
	Delete(id uint) error
	FindByID(id uint) (*AdminUser, error)
	FindByUsername(username string) (*AdminUser, error)
	FindByMobile(mobile string) (*AdminUser, error)
	List(page, size int, query string) ([]*AdminUser, int64, error)

	// 状态管理
	UpdateStatus(id uint, status int) error

	// 登录记录
	UpdateLastLogin(id uint, ip string) error
}

// adminUserRepository 管理员用户仓库实现
type adminUserRepository struct{}

// NewAdminUserRepository 创建管理员用户仓库
func NewAdminUserRepository() AdminUserRepository {
	return &adminUserRepository{}
}

// Create 创建管理员用户
func (r *adminUserRepository) Create(adminUser *AdminUser) error {
	return mysql.Master().Create(adminUser).Error
}

// Update 更新管理员用户
func (r *adminUserRepository) Update(adminUser *AdminUser) error {
	return mysql.Master().Save(adminUser).Error
}

// Delete 删除管理员用户（逻辑删除）
func (r *adminUserRepository) Delete(adminID uint) error {
	now := time.Now()
	return mysql.Master().Model(&AdminUser{}).Where("admin_id = ?", adminID).
		Updates(map[string]interface{}{
			"is_deleted": true,
			"deleted_at": now,
		}).Error
}

// FindByID 根据ID查找管理员用户
func (r *adminUserRepository) FindByID(adminID uint) (*AdminUser, error) {
	var adminUser AdminUser
	err := mysql.Slave().Where("admin_id = ? AND is_deleted = ?", adminID, false).First(&adminUser).Error
	if err != nil {
		return nil, err
	}
	return &adminUser, nil
}

// FindByUsername 根据用户名查找管理员用户
func (r *adminUserRepository) FindByUsername(username string) (*AdminUser, error) {
	var adminUser AdminUser
	err := mysql.Slave().Where("username = ? AND is_deleted = ?", username, false).First(&adminUser).Error
	if err != nil {
		return nil, err
	}
	return &adminUser, nil
}

// FindByMobile 根据手机号查找管理员用户
func (r *adminUserRepository) FindByMobile(mobile string) (*AdminUser, error) {
	var adminUser AdminUser
	err := mysql.Slave().Where("mobile = ? AND is_deleted = ?", mobile, false).First(&adminUser).Error
	if err != nil {
		return nil, err
	}
	return &adminUser, nil
}

// List 获取管理员用户列表
func (r *adminUserRepository) List(page, size int, query string) ([]*AdminUser, int64, error) {
	var adminUsers []*AdminUser
	var total int64

	db := mysql.Slave().Where("is_deleted = ?", false)

	// 如果有查询条件，添加到查询中
	if query != "" {
		db = db.Where("username LIKE ? OR email LIKE ? OR mobile LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// 获取总数
	if err := db.Model(&AdminUser{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("admin_id DESC").Offset(offset).Limit(size).Find(&adminUsers).Error; err != nil {
		return nil, 0, err
	}

	return adminUsers, total, nil
}

// UpdateStatus 更新管理员用户状态
func (r *adminUserRepository) UpdateStatus(adminID uint, status int) error {
	return mysql.Master().Model(&AdminUser{}).Where("admin_id = ? AND is_deleted = ?", adminID, false).
		Update("status", status).Error
}

// UpdateLastLogin 更新管理员最后登录信息
func (r *adminUserRepository) UpdateLastLogin(adminID uint, ip string) error {
	return mysql.Master().Model(&AdminUser{}).Where("admin_id = ? AND is_deleted = ?", adminID, false).
		Updates(map[string]interface{}{
			"last_login": time.Now(),
			"last_ip":    ip,
		}).Error
}
