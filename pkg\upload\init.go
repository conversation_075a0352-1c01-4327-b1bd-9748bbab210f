package upload

import (
	"fmt"
	"yekaitai/internal/config"

	"github.com/zeromicro/go-zero/core/logx"
)

// Init 初始化上传模块
func Init() {
	logx.Info("初始化上传模块")
}

// InitWithConfig 使用配置初始化上传模块
func InitWithConfig(cfg config.Config) {
	logx.Info("使用配置初始化上传模块")

	// 如果配置文件中有七牛云配置，则使用配置文件中的配置
	if cfg.Upload.Qiniu.AccessKey != "" {
		qiniuConfig := QiniuConfig{
			AccessKey: cfg.Upload.Qiniu.AccessKey,
			SecretKey: cfg.Upload.Qiniu.SecretKey,
			Bucket:    cfg.Upload.Qiniu.Bucket,
			Domain:    cfg.Upload.Qiniu.Domain,
			Zone:      cfg.Upload.Qiniu.Zone,
		}
		logx.Infof("使用配置文件中的七牛云配置: Bucket=%s, Domain=%s, Zone=%s",
			qiniuConfig.Bucket, qiniuConfig.Domain, qiniuConfig.Zone)
		DefaultQiniuConfig = qiniuConfig
	}

	// 如果配置文件中有上传配置，则使用配置文件中的配置
	if cfg.Upload.Config.MaxImageSize > 0 {
		uploadConfig := UploadConfig{
			MaxImageSize:    cfg.Upload.Config.MaxImageSize,
			MaxDocumentSize: cfg.Upload.Config.MaxDocumentSize,
			MaxVideoSize:    cfg.Upload.Config.MaxVideoSize,
			MaxGeneralSize:  cfg.Upload.Config.MaxGeneralSize,
			AllowedDomains:  cfg.Upload.Config.AllowedDomains,
		}
		logx.Infof("使用配置文件中的上传配置: MaxImageSize=%d, MaxDocumentSize=%d, MaxVideoSize=%d, MaxGeneralSize=%d",
			uploadConfig.MaxImageSize, uploadConfig.MaxDocumentSize, uploadConfig.MaxVideoSize, uploadConfig.MaxGeneralSize)
		DefaultUploadConfig = uploadConfig
	}

	// 更新全局配置
	DefaultConfig = Config{
		Qiniu:  DefaultQiniuConfig,
		Upload: DefaultUploadConfig,
	}
	globalConfig = DefaultConfig

	// 初始化七牛云上传器
	if DefaultQiniuUploader == nil {
		InitDefaultQiniuUploader()
	}

	// 输出初始化完成日志
	logx.Infof("上传模块初始化完成，七牛云配置: %v", fmt.Sprintf("Bucket=%s, Domain=%s, Zone=%s",
		DefaultQiniuConfig.Bucket, DefaultQiniuConfig.Domain, DefaultQiniuConfig.Zone))
}
