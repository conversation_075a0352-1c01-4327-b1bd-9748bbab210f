package model

type StoreCreateRequest struct {
	Name        string `json:"name" binding:"required" comment:"门店名称"`
	Phone       string `json:"phone" binding:"required" comment:"联系电话"`
	ProvinceID  string `json:"province_id" binding:"required" comment:"省份编码"`
	CityID      string `json:"city_id" binding:"required" comment:"城市编码"`
	AreaID      string `json:"area_id" binding:"required" comment:"区县编码"`
	Address     string `json:"address" binding:"required" comment:"详细地址"`
	Description string `json:"description" comment:"门店介绍"`
	Images      string `json:"images" comment:"门店图片,JSON格式"`
	ManagerID   uint   `json:"manager_id" binding:"required" comment:"门店管理员ID"`
}

type StoreUpdateRequest struct {
	ID          uint   `json:"id" binding:"required" comment:"门店ID"`
	Name        string `json:"name" binding:"required" comment:"门店名称"`
	Phone       string `json:"phone" binding:"required" comment:"联系电话"`
	ProvinceID  string `json:"province_id" binding:"required" comment:"省份编码"`
	CityID      string `json:"city_id" binding:"required" comment:"城市编码"`
	AreaID      string `json:"area_id" binding:"required" comment:"区县编码"`
	Address     string `json:"address" binding:"required" comment:"详细地址"`
	Description string `json:"description" comment:"门店介绍"`
	Images      string `json:"images" comment:"门店图片,JSON格式"`
	ManagerID   uint   `json:"manager_id" binding:"required" comment:"门店管理员ID"`
	Status      int    `json:"status" binding:"required" comment:"状态：1-正常，0-禁用"`
}

type StoreListRequest struct {
	Name       string `form:"name" comment:"门店名称"`
	Phone      string `form:"phone" comment:"联系电话"`
	ProvinceID string `form:"province_id" comment:"省份编码"`
	CityID     string `form:"city_id" comment:"城市编码"`
	AreaID     string `form:"area_id" comment:"区县编码"`
	ManagerID  uint   `form:"manager_id" comment:"门店管理员ID"`
	Status     int    `form:"status" comment:"状态：1-正常，0-禁用"`
	Page       int    `form:"page" binding:"required" comment:"页码"`
	PageSize   int    `form:"page_size" binding:"required" comment:"每页数量"`
}
