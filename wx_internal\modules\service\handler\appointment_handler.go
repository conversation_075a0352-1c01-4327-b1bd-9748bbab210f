package handler

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/modules/service_setup/model"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	wxSvc "yekaitai/wx_internal/svc"
	wxUtils "yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// ServiceAppointmentHandler 服务预约处理器
type ServiceAppointmentHandler struct {
	svcCtx *wxSvc.WxServiceContext
}

// NewServiceAppointmentHandler 创建服务预约处理器
func NewServiceAppointmentHandler(svcCtx *wxSvc.WxServiceContext) *ServiceAppointmentHandler {
	return &ServiceAppointmentHandler{
		svcCtx: svcCtx,
	}
}

// GetAppointmentAvailability 获取预约可用性
func (h *ServiceAppointmentHandler) GetAppointmentAvailability(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	orderIDStr := r.URL.Query().Get("order_id")
	if orderIDStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID格式错误"))
		return
	}

	// 查询订单和服务信息
	var order model.ServiceOrder
	err = mysql.Slave().Table("service_orders").
		Where("id = ? AND user_id = ? AND pay_status = 'paid' AND deleted_at IS NULL", orderID, userID).
		First(&order).Error

	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在或未支付"))
		return
	}

	// 查询服务套餐信息
	var service model.ServicePackage
	err = mysql.Slave().Table("service_packages").
		Where("id = ?", order.ServicePackageID).
		First(&service).Error

	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "服务套餐不存在"))
		return
	}

	// 检查是否需要预约
	if service.AppointmentRule == "no_need" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该服务无需预约"))
		return
	}

	// 检查是否已有预约
	var existingAppointment model.ServiceAppointment
	hasExisting := mysql.Slave().Table("service_appointments").
		Where("order_id = ? AND status = 'booked'", orderID).
		First(&existingAppointment).Error == nil

	if hasExisting {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "已有预约，请先取消现有预约"))
		return
	}

	// 获取适用门店
	stores := h.getServiceStores(service)

	// 获取可预约日期和时间
	availability := h.getAvailabilityCalendar(service, stores)

	result := map[string]interface{}{
		"order_id":               orderID,
		"service_id":             service.ID,
		"service_name":           service.Name,
		"appointment_rule":       service.AppointmentRule,
		"advance_hours":          service.AdvanceHours,
		"appointment_time_type":  service.AppointmentTimeType,
		"appointment_start_time": service.AppointmentStartTime,
		"appointment_end_time":   service.AppointmentEndTime,
		"max_appointments":       service.MaxAppointments,
		"stores":                 stores,
		"availability":           availability,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取预约信息成功"))
}

// CreateAppointment 创建预约
func (h *ServiceAppointmentHandler) CreateAppointment(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	var req struct {
		OrderID         uint   `json:"order_id"`         // 订单ID
		StoreID         uint   `json:"store_id"`         // 门店ID
		AppointmentDate string `json:"appointment_date"` // 预约日期 YYYY-MM-DD
		AppointmentTime string `json:"appointment_time"` // 预约时间段 HH:MM-HH:MM
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	// 解析预约日期
	appointmentDate, err := time.Parse("2006-01-02", req.AppointmentDate)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "预约日期格式错误"))
		return
	}

	// 开始事务
	tx := mysql.Master().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询订单和服务信息
	var order model.ServiceOrder
	err = tx.Table("service_orders").
		Where("id = ? AND user_id = ? AND pay_status = 'paid' AND deleted_at IS NULL", req.OrderID, userID).
		First(&order).Error

	if err != nil {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在或未支付"))
		return
	}

	var service model.ServicePackage
	err = tx.Table("service_packages").
		Where("id = ?", order.ServicePackageID).
		First(&service).Error

	if err != nil {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "服务套餐不存在"))
		return
	}

	// 验证预约规则
	if !h.validateAppointmentRules(service, appointmentDate, req.AppointmentTime) {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "预约时间不符合规则"))
		return
	}

	// 检查预约容量
	if !h.checkAppointmentCapacity(service, req.StoreID, appointmentDate, req.AppointmentTime) {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该时段已约满"))
		return
	}

	// 创建预约
	appointment := model.ServiceAppointment{
		OrderID:          req.OrderID,
		UserID:           userID,
		ServicePackageID: order.ServicePackageID,
		StoreID:          req.StoreID,
		AppointmentDate:  appointmentDate,
		AppointmentTime:  &req.AppointmentTime,
		Status:           "booked",
		ModifyCount:      0,
	}

	if err := tx.Create(&appointment).Error; err != nil {
		tx.Rollback()
		logx.Errorf("创建预约失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建预约失败"))
		return
	}

	tx.Commit()

	result := map[string]interface{}{
		"appointment_id":   appointment.ID,
		"order_id":         req.OrderID,
		"store_id":         req.StoreID,
		"appointment_date": req.AppointmentDate,
		"appointment_time": req.AppointmentTime,
		"status":           "booked",
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "预约成功"))
}

// ModifyAppointment 修改预约
func (h *ServiceAppointmentHandler) ModifyAppointment(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	var req struct {
		AppointmentID   uint   `json:"appointment_id"`   // 预约ID
		StoreID         uint   `json:"store_id"`         // 门店ID
		AppointmentDate string `json:"appointment_date"` // 预约日期 YYYY-MM-DD
		AppointmentTime string `json:"appointment_time"` // 预约时间段 HH:MM-HH:MM
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	// 解析预约日期
	appointmentDate, err := time.Parse("2006-01-02", req.AppointmentDate)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "预约日期格式错误"))
		return
	}

	// 开始事务
	tx := mysql.Master().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询预约信息
	var appointment model.ServiceAppointment
	err = tx.Table("service_appointments").
		Where("id = ? AND user_id = ? AND status = 'booked'", req.AppointmentID, userID).
		First(&appointment).Error

	if err != nil {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "预约不存在"))
		return
	}

	// 检查修改次数限制
	if appointment.ModifyCount >= 1 {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "预约只能修改1次"))
		return
	}

	// 查询服务信息
	var service model.ServicePackage
	err = tx.Table("service_packages").
		Where("id = ?", appointment.ServicePackageID).
		First(&service).Error

	if err != nil {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "服务套餐不存在"))
		return
	}

	// 验证预约规则
	if !h.validateAppointmentRules(service, appointmentDate, req.AppointmentTime) {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "预约时间不符合规则"))
		return
	}

	// 检查预约容量
	if !h.checkAppointmentCapacity(service, req.StoreID, appointmentDate, req.AppointmentTime) {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该时段已约满"))
		return
	}

	// 更新预约
	err = tx.Table("service_appointments").
		Where("id = ?", req.AppointmentID).
		Updates(map[string]interface{}{
			"store_id":         req.StoreID,
			"appointment_date": appointmentDate,
			"appointment_time": req.AppointmentTime,
			"modify_count":     appointment.ModifyCount + 1,
			"updated_at":       time.Now(),
		}).Error

	if err != nil {
		tx.Rollback()
		logx.Errorf("修改预约失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "修改预约失败"))
		return
	}

	tx.Commit()

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "修改预约成功"))
}

// CancelAppointment 取消预约
func (h *ServiceAppointmentHandler) CancelAppointment(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	var req struct {
		AppointmentID uint `json:"appointment_id"` // 预约ID
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	// 查询预约信息
	var appointment model.ServiceAppointment
	err := mysql.Slave().Table("service_appointments").
		Where("id = ? AND user_id = ? AND status = 'booked'", req.AppointmentID, userID).
		First(&appointment).Error

	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "预约不存在"))
		return
	}

	// 检查取消时间限制（预约时间前5小时不可取消）
	appointmentDateTime := time.Date(
		appointment.AppointmentDate.Year(),
		appointment.AppointmentDate.Month(),
		appointment.AppointmentDate.Day(),
		0, 0, 0, 0, time.Local,
	)

	if time.Now().Add(5 * time.Hour).After(appointmentDateTime) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "预约时间前5小时不可取消"))
		return
	}

	// 更新预约状态
	err = mysql.Master().Table("service_appointments").
		Where("id = ?", req.AppointmentID).
		Updates(map[string]interface{}{
			"status":      "cancelled",
			"cancel_time": time.Now(),
			"updated_at":  time.Now(),
		}).Error

	if err != nil {
		logx.Errorf("取消预约失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "取消预约失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "取消预约成功"))
}

// getServiceStores 获取服务适用门店
func (h *ServiceAppointmentHandler) getServiceStores(service model.ServicePackage) []map[string]interface{} {
	var storeIDs []uint

	if service.IsAllStores {
		// 查询所有门店
		mysql.Slave().Table("t_stores").Where("deleted_at IS NULL").Pluck("id", &storeIDs)
	} else {
		// 解析 service.StoreIDs JSON 数组
		if service.StoreIDs != "" {
			var ids []uint
			if err := json.Unmarshal([]byte(service.StoreIDs), &ids); err == nil {
				storeIDs = ids
			}
		}
	}

	if len(storeIDs) == 0 {
		return []map[string]interface{}{}
	}

	// 查询门店信息
	var stores []struct {
		ID        uint    `json:"id"`
		Name      string  `json:"name"`
		Address   string  `json:"address"`
		Phone     string  `json:"phone"`
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
	}

	err := mysql.Slave().Table("t_stores").
		Select("id, name, address, phone, latitude, longitude").
		Where("id IN ? AND deleted_at IS NULL", storeIDs).
		Find(&stores).Error

	if err != nil {
		return []map[string]interface{}{}
	}

	result := make([]map[string]interface{}, 0, len(stores))
	for _, store := range stores {
		item := map[string]interface{}{
			"id":        store.ID,
			"name":      store.Name,
			"address":   store.Address,
			"phone":     store.Phone,
			"latitude":  store.Latitude,
			"longitude": store.Longitude,
		}
		result = append(result, item)
	}

	return result
}

// getAvailabilityCalendar 获取可预约日历
func (h *ServiceAppointmentHandler) getAvailabilityCalendar(service model.ServicePackage, stores []map[string]interface{}) map[string]interface{} {
	now := time.Now()

	// 计算可预约的日期范围
	startDate := now.AddDate(0, 0, 1) // 明天开始
	if service.AdvanceHours > 0 {
		startDate = now.Add(time.Duration(service.AdvanceHours) * time.Hour)
	}

	endDate := startDate.AddDate(0, 1, 0) // 一个月内
	if service.ValidityEnd != nil {
		endDate = *service.ValidityEnd
	}

	// 生成日期列表
	dates := make([]map[string]interface{}, 0)
	for d := startDate; d.Before(endDate) || d.Equal(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")

		// 检查是否在不可用日期列表中
		if h.isDateUnavailable(service, dateStr) {
			continue
		}

		dateInfo := map[string]interface{}{
			"date":      dateStr,
			"available": true,
			"stores":    h.getDateStoreAvailability(service, stores, d),
		}

		dates = append(dates, dateInfo)
	}

	return map[string]interface{}{
		"dates":                  dates,
		"appointment_time_type":  service.AppointmentTimeType,
		"appointment_start_time": service.AppointmentStartTime,
		"appointment_end_time":   service.AppointmentEndTime,
	}
}

// isDateUnavailable 检查日期是否不可用
func (h *ServiceAppointmentHandler) isDateUnavailable(service model.ServicePackage, dateStr string) bool {
	if service.UnavailableDates == "" {
		return false
	}

	var unavailableDates []string
	if err := json.Unmarshal([]byte(service.UnavailableDates), &unavailableDates); err != nil {
		return false
	}

	for _, date := range unavailableDates {
		if date == dateStr {
			return true
		}
	}

	return false
}

// getDateStoreAvailability 获取指定日期各门店的可预约情况
func (h *ServiceAppointmentHandler) getDateStoreAvailability(service model.ServicePackage, stores []map[string]interface{}, date time.Time) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(stores))

	for _, store := range stores {
		storeID := store["id"].(uint)

		storeInfo := map[string]interface{}{
			"store_id":   storeID,
			"store_name": store["name"],
			"available":  true,
			"times":      h.getStoreTimeSlots(service, storeID, date),
		}

		result = append(result, storeInfo)
	}

	return result
}

// getStoreTimeSlots 获取门店时间段
func (h *ServiceAppointmentHandler) getStoreTimeSlots(service model.ServicePackage, storeID uint, date time.Time) []map[string]interface{} {
	if service.AppointmentTimeType == "all_day" {
		return []map[string]interface{}{
			{
				"time":      "全天",
				"available": true,
				"booked":    0,
				"capacity":  service.MaxAppointments,
			},
		}
	}

	// TODO: 根据 appointment_start_time 和 appointment_end_time 生成时间段
	// 这里简化处理，返回几个固定时间段
	timeSlots := []string{"09:00-10:00", "10:00-11:00", "14:00-15:00", "15:00-16:00"}
	result := make([]map[string]interface{}, 0, len(timeSlots))

	for _, slot := range timeSlots {
		// 查询该时间段已预约数量
		var bookedCount int64
		mysql.Slave().Table("service_appointments").
			Where("store_id = ? AND appointment_date = ? AND appointment_time = ? AND status = 'booked'",
				storeID, date.Format("2006-01-02"), slot).
			Count(&bookedCount)

		available := bookedCount < int64(service.MaxAppointments)

		result = append(result, map[string]interface{}{
			"time":      slot,
			"available": available,
			"booked":    bookedCount,
			"capacity":  service.MaxAppointments,
		})
	}

	return result
}

// validateAppointmentRules 验证预约规则
func (h *ServiceAppointmentHandler) validateAppointmentRules(service model.ServicePackage, appointmentDate time.Time, appointmentTime string) bool {
	now := time.Now()

	// 检查提前预约时间
	if service.AdvanceHours > 0 {
		minTime := now.Add(time.Duration(service.AdvanceHours) * time.Hour)
		if appointmentDate.Before(minTime) {
			return false
		}
	}

	// 检查有效期
	if service.ValidityEnd != nil && appointmentDate.After(*service.ValidityEnd) {
		return false
	}

	// 检查不可用日期
	if h.isDateUnavailable(service, appointmentDate.Format("2006-01-02")) {
		return false
	}

	return true
}

// checkAppointmentCapacity 检查预约容量
func (h *ServiceAppointmentHandler) checkAppointmentCapacity(service model.ServicePackage, storeID uint, appointmentDate time.Time, appointmentTime string) bool {
	var bookedCount int64
	mysql.Slave().Table("service_appointments").
		Where("store_id = ? AND appointment_date = ? AND appointment_time = ? AND status = 'booked'",
			storeID, appointmentDate.Format("2006-01-02"), appointmentTime).
		Count(&bookedCount)

	return bookedCount < int64(service.MaxAppointments)
}
