package handler

import (
	"net/http"

	"yekaitai/internal/types"
	"yekaitai/wx_internal/modules/points/service"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type PointsGoZeroHandler struct {
	pointsService *service.PointsService
}

func NewPointsGoZeroHandler() *PointsGoZeroHandler {
	return &PointsGoZeroHandler{
		pointsService: service.NewPointsService(),
	}
}

// 积分规则查询请求
type PointsRuleRequest struct {
	TotalAmount float64 `json:"total_amount" validate:"required"` // 商品总金额
}

// 积分抵扣计算请求
type PointsDeductionRequest struct {
	UsePoints   int     `json:"use_points" validate:"required"`   // 使用积分数量
	TotalAmount float64 `json:"total_amount" validate:"required"` // 商品总金额
}

// 积分规则信息
type PointsRuleInfo struct {
	Available    bool    `json:"available"`     // 是否可用积分
	UserPoints   int     `json:"user_points"`   // 用户积分余额
	ExchangeRate float64 `json:"exchange_rate"` // 积分兑换比例（如100积分=1元）
	MaxUse       int     `json:"max_use"`       // 最大可用积分
	MaxAmount    float64 `json:"max_amount"`    // 最大抵扣金额
	MinPoints    int     `json:"min_points"`    // 最少使用积分
	Description  string  `json:"description"`   // 规则说明
}

// 积分抵扣结果
type PointsDeductionResult struct {
	CanUse       bool    `json:"can_use"`       // 是否可以使用
	UsePoints    int     `json:"use_points"`    // 实际使用积分
	DeductAmount float64 `json:"deduct_amount"` // 抵扣金额
	FinalAmount  float64 `json:"final_amount"`  // 最终金额
	Message      string  `json:"message"`       // 提示信息
}

// GetPointsRule 获取积分使用规则
func (h *PointsGoZeroHandler) GetPointsRule(w http.ResponseWriter, r *http.Request) {
	var req PointsRuleRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 获取积分规则
	rule, err := h.pointsService.GetPointsRule(r.Context(), userID, req.TotalAmount)
	if err != nil {
		logx.Errorf("获取积分规则失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取积分规则失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(rule))
}

// CalculatePointsDeduction 计算积分抵扣
func (h *PointsGoZeroHandler) CalculatePointsDeduction(w http.ResponseWriter, r *http.Request) {
	var req PointsDeductionRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 计算积分抵扣
	result, err := h.pointsService.CalculateDeduction(r.Context(), userID, req.UsePoints, req.TotalAmount)
	if err != nil {
		logx.Errorf("计算积分抵扣失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetUserPoints 获取用户积分余额
func (h *PointsGoZeroHandler) GetUserPoints(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 获取用户积分
	points, err := h.pointsService.GetUserPoints(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取用户积分失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取用户积分失败"))
		return
	}

	response := map[string]interface{}{
		"points": points,
	}
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(response))
}
