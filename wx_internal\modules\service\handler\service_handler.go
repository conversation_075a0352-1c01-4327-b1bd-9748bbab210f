package handler

import (
	"encoding/json"
	"math"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/modules/service_setup/model"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	serviceQRCode "yekaitai/wx_internal/modules/service/service"
	wxSvc "yekaitai/wx_internal/svc"
	wxUtils "yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// ServiceHandler 服务处理器
type ServiceHandler struct {
	svcCtx *wxSvc.WxServiceContext
}

// NewServiceHandler 创建服务处理器
func NewServiceHandler(svcCtx *wxSvc.WxServiceContext) *ServiceHandler {
	return &ServiceHandler{
		svcCtx: svcCtx,
	}
}

// GetServiceList 获取服务列表
func (h *ServiceHandler) GetServiceList(w http.ResponseWriter, r *http.Request) {
	var req struct {
		TagID    *uint   `json:"tag_id"`    // 标签ID，可选
		SortType string  `json:"sort_type"` // 排序类型：default(默认推荐), distance(按距离)
		UserLat  float64 `json:"user_lat"`  // 用户纬度
		UserLng  float64 `json:"user_lng"`  // 用户经度
		Page     int     `json:"page"`      // 页码，默认1
		PageSize int     `json:"page_size"` // 每页数量，默认10
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.SortType == "" {
		req.SortType = "default"
	}

	// 获取用户ID（用于推荐算法）
	userID, _ := wxUtils.GetUserIDFromContext(r.Context())
	_ = userID // 暂时未使用，避免编译错误

	// 构建查询条件
	query := mysql.Slave().Table("service_packages").
		Where("status = ? AND deleted_at IS NULL", "active")

	// 检查售卖时间
	now := time.Now()
	query = query.Where("(sale_start_type = 'after_release' OR (sale_start_type = 'fixed' AND sale_start_at <= ?))", now).
		Where("(sale_end_at IS NULL OR sale_end_at >= ?)", now)

	// 标签筛选
	if req.TagID != nil {
		query = query.Where("tag_id = ?", *req.TagID)
	}

	// 排序逻辑
	switch req.SortType {
	case "distance":
		// 按距离排序需要特殊处理
		if req.UserLat != 0 && req.UserLng != 0 {
			query = query.Order("sort ASC, created_at DESC")
		} else {
			query = query.Order("sort ASC, created_at DESC")
		}
	default:
		// 默认推荐排序
		if req.TagID != nil {
			// 有标签时：标签匹配 + 推荐排序
			query = query.Order("sort ASC, created_at DESC")
		} else {
			// 无标签时：推荐排序
			query = query.Order("sort ASC, created_at DESC")
		}
	}

	// 分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询服务列表
	var services []model.ServicePackage
	if err := query.Find(&services).Error; err != nil {
		logx.Errorf("查询服务列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询服务列表失败"))
		return
	}

	// 处理距离计算和数据格式化
	result := h.formatServiceList(services, req.UserLat, req.UserLng, req.SortType)

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取服务列表成功"))
}

// formatServiceList 格式化服务列表数据
func (h *ServiceHandler) formatServiceList(services []model.ServicePackage, userLat, userLng float64, sortType string) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(services))

	for _, service := range services {
		item := map[string]interface{}{
			"id":                      service.ID,
			"name":                    service.Name,
			"images":                  service.Images,
			"tag_id":                  service.TagID,
			"sort":                    service.Sort,
			"body_part":               service.BodyPart,
			"process":                 service.Process,
			"duration":                service.Duration,
			"times":                   service.Times,
			"suitable_for":            service.SuitableFor,
			"price":                   service.Price,
			"point_price":             service.PointPrice,
			"original_price":          service.OriginalPrice,
			"extra_info":              service.ExtraInfo,
			"store_ids":               service.StoreIDs,
			"is_all_stores":           service.IsAllStores,
			"discount_info":           service.DiscountInfo,
			"gift_info":               service.GiftInfo,
			"validity_type":           service.ValidityType,
			"validity_start":          service.ValidityStart,
			"validity_end":            service.ValidityEnd,
			"unavailable_dates":       service.UnavailableDates,
			"sale_start_type":         service.SaleStartType,
			"sale_start_at":           service.SaleStartAt,
			"sale_end_at":             service.SaleEndAt,
			"appointment_rule":        service.AppointmentRule,
			"advance_hours":           service.AdvanceHours,
			"appointment_time_type":   service.AppointmentTimeType,
			"appointment_start_time":  service.AppointmentStartTime,
			"appointment_end_time":    service.AppointmentEndTime,
			"max_appointments":        service.MaxAppointments,
			"max_purchases":           service.MaxPurchases,
			"single_max_purchases":    service.SingleMaxPurchases,
			"warm_tips":               service.WarmTips,
			"support_refund_any_time": service.SupportRefundAnyTime,
			"refund_any_time_desc":    service.RefundAnyTimeDesc,
			"support_auto_refund":     service.SupportAutoRefund,
			"auto_refund_desc":        service.AutoRefundDesc,
			"description":             service.Description,
			"status":                  service.Status,
			"sales_count":             service.SalesCount,
			"created_at":              service.CreatedAt,
			"updated_at":              service.UpdatedAt,
		}

		// 计算最近门店距离
		if userLat != 0 && userLng != 0 {
			nearestStore, distance := h.findNearestStore(service, userLat, userLng)
			if nearestStore != nil {
				item["nearest_store"] = nearestStore
				item["distance"] = distance
			}
		}

		result = append(result, item)
	}

	// 如果是按距离排序，重新排序
	if sortType == "distance" && userLat != 0 && userLng != 0 {
		// TODO: 实现按距离排序逻辑
	}

	return result
}

// findNearestStore 查找最近的门店
func (h *ServiceHandler) findNearestStore(service model.ServicePackage, userLat, userLng float64) (map[string]interface{}, float64) {
	var storeIDs []uint

	if service.IsAllStores {
		// 查询所有门店
		mysql.Slave().Table("t_stores").Where("deleted_at IS NULL").Pluck("id", &storeIDs)
	} else {
		// 解析指定门店ID
		// TODO: 解析 service.StoreIDs JSON 数组
	}

	if len(storeIDs) == 0 {
		return nil, 0
	}

	// 查询门店信息并计算距离
	var stores []struct {
		ID        uint    `json:"id"`
		Name      string  `json:"name"`
		Address   string  `json:"address"`
		Phone     string  `json:"phone"`
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
	}

	err := mysql.Slave().Table("t_stores").
		Select("id, name, address, phone, latitude, longitude").
		Where("id IN ? AND deleted_at IS NULL", storeIDs).
		Find(&stores).Error

	if err != nil || len(stores) == 0 {
		return nil, 0
	}

	// 找到最近的门店
	var nearestStore *struct {
		ID        uint    `json:"id"`
		Name      string  `json:"name"`
		Address   string  `json:"address"`
		Phone     string  `json:"phone"`
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
	}
	minDistance := math.MaxFloat64

	for i := range stores {
		distance := calculateDistance(userLat, userLng, stores[i].Latitude, stores[i].Longitude)
		if distance < minDistance {
			minDistance = distance
			nearestStore = &stores[i]
		}
	}

	if nearestStore == nil {
		return nil, 0
	}

	return map[string]interface{}{
		"id":        nearestStore.ID,
		"name":      nearestStore.Name,
		"address":   nearestStore.Address,
		"phone":     nearestStore.Phone,
		"latitude":  nearestStore.Latitude,
		"longitude": nearestStore.Longitude,
	}, minDistance
}

// calculateDistance 计算两点间距离（公里）
func calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const earthRadius = 6371 // 地球半径（公里）

	lat1Rad := lat1 * math.Pi / 180
	lng1Rad := lng1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lng2Rad := lng2 * math.Pi / 180

	deltaLat := lat2Rad - lat1Rad
	deltaLng := lng2Rad - lng1Rad

	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(deltaLng/2)*math.Sin(deltaLng/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return earthRadius * c
}

// GetServiceDetail 获取服务详情
func (h *ServiceHandler) GetServiceDetail(w http.ResponseWriter, r *http.Request) {
	serviceIDStr := r.URL.Query().Get("id")
	if serviceIDStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务ID不能为空"))
		return
	}

	serviceID, err := strconv.ParseUint(serviceIDStr, 10, 64)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务ID格式错误"))
		return
	}

	// 获取用户位置参数（可选）
	userLatStr := r.URL.Query().Get("user_lat")
	userLngStr := r.URL.Query().Get("user_lng")

	var userLat, userLng float64
	if userLatStr != "" && userLngStr != "" {
		userLat, err = strconv.ParseFloat(userLatStr, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "用户纬度格式错误"))
			return
		}
		userLng, err = strconv.ParseFloat(userLngStr, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "用户经度格式错误"))
			return
		}
	}

	// 查询服务详情
	var service model.ServicePackage
	err = mysql.Slave().Table("service_packages").
		Where("id = ? AND status = ? AND deleted_at IS NULL", serviceID, "active").
		First(&service).Error

	if err != nil {
		logx.Errorf("查询服务详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "服务不存在"))
		return
	}

	// 检查售卖时间
	now := time.Now()
	if service.SaleStartType == "fixed" && service.SaleStartAt != nil && service.SaleStartAt.After(now) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务尚未开始售卖"))
		return
	}
	if service.SaleEndAt != nil && service.SaleEndAt.Before(now) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务已停止售卖"))
		return
	}

	// 格式化服务详情（包含距离计算）
	result := h.formatServiceDetail(service, userLat, userLng)

	// 获取推荐服务（根据用户标签或距离）
	userID, _ := wxUtils.GetUserIDFromContext(r.Context())
	recommendations := h.getServiceRecommendations(userID, &service.TagID, userLat, userLng, 4)
	result["recommendations"] = recommendations

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取服务详情成功"))
}

// formatServiceDetail 格式化服务详情
func (h *ServiceHandler) formatServiceDetail(service model.ServicePackage, userLat, userLng float64) map[string]interface{} {
	result := map[string]interface{}{
		"id":                      service.ID,
		"name":                    service.Name,
		"images":                  service.Images,
		"tag_id":                  service.TagID,
		"body_part":               service.BodyPart,
		"process":                 service.Process,
		"duration":                service.Duration,
		"times":                   service.Times,
		"suitable_for":            service.SuitableFor,
		"price":                   service.Price,
		"point_price":             service.PointPrice,
		"original_price":          service.OriginalPrice,
		"extra_info":              service.ExtraInfo,
		"discount_info":           service.DiscountInfo,
		"gift_info":               service.GiftInfo,
		"validity_type":           service.ValidityType,
		"validity_start":          service.ValidityStart,
		"validity_end":            service.ValidityEnd,
		"appointment_rule":        service.AppointmentRule,
		"advance_hours":           service.AdvanceHours,
		"max_purchases":           service.MaxPurchases,
		"single_max_purchases":    service.SingleMaxPurchases,
		"warm_tips":               service.WarmTips,
		"support_refund_any_time": service.SupportRefundAnyTime,
		"refund_any_time_desc":    service.RefundAnyTimeDesc,
		"support_auto_refund":     service.SupportAutoRefund,
		"auto_refund_desc":        service.AutoRefundDesc,
		"description":             service.Description,
		"sales_count":             service.SalesCount,
		"created_at":              service.CreatedAt,
	}

	// 是否支持叶小币兑换
	result["support_points"] = service.PointPrice > 0

	// 获取适用门店信息
	stores := h.getServiceStores(service, userLat, userLng)
	result["stores"] = stores

	// 如果有用户位置，计算最近门店
	if userLat != 0 && userLng != 0 && len(stores) > 0 {
		nearestStore, distance := h.findNearestStoreFromList(stores, userLat, userLng)
		if nearestStore != nil {
			result["nearest_store"] = nearestStore
			result["nearest_distance"] = distance
		}
	}

	return result
}

// getServiceStores 获取服务适用门店
func (h *ServiceHandler) getServiceStores(service model.ServicePackage, userLat, userLng float64) []map[string]interface{} {
	var storeIDs []uint

	if service.IsAllStores {
		// 查询所有门店
		mysql.Slave().Table("t_stores").Where("deleted_at IS NULL").Pluck("id", &storeIDs)
	} else {
		// 解析 service.StoreIDs JSON 数组
		if service.StoreIDs != "" {
			var ids []uint
			if err := json.Unmarshal([]byte(service.StoreIDs), &ids); err == nil {
				storeIDs = ids
			}
		}
	}

	if len(storeIDs) == 0 {
		return []map[string]interface{}{}
	}

	// 查询门店信息
	var stores []struct {
		ID        uint    `json:"id"`
		Name      string  `json:"name"`
		Address   string  `json:"address"`
		Phone     string  `json:"phone"`
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
	}

	err := mysql.Slave().Table("t_stores").
		Select("id, name, address, phone, latitude, longitude").
		Where("id IN ? AND deleted_at IS NULL", storeIDs).
		Find(&stores).Error

	if err != nil {
		return []map[string]interface{}{}
	}

	result := make([]map[string]interface{}, 0, len(stores))
	for _, store := range stores {
		item := map[string]interface{}{
			"id":        store.ID,
			"name":      store.Name,
			"address":   store.Address,
			"phone":     store.Phone,
			"latitude":  store.Latitude,
			"longitude": store.Longitude,
		}

		// 计算距离
		if userLat != 0 && userLng != 0 {
			distance := calculateDistance(userLat, userLng, store.Latitude, store.Longitude)
			item["distance"] = distance
		}

		result = append(result, item)
	}

	return result
}

// findNearestStoreFromList 从门店列表中找到最近的门店
func (h *ServiceHandler) findNearestStoreFromList(stores []map[string]interface{}, userLat, userLng float64) (map[string]interface{}, float64) {
	if len(stores) == 0 {
		return nil, 0
	}

	var nearestStore map[string]interface{}
	minDistance := math.MaxFloat64

	for _, store := range stores {
		if distance, ok := store["distance"].(float64); ok {
			if distance < minDistance {
				minDistance = distance
				nearestStore = store
			}
		}
	}

	return nearestStore, minDistance
}

// getServiceRecommendations 获取服务推荐
func (h *ServiceHandler) getServiceRecommendations(userID uint, tagID *uint, userLat, userLng float64, limit int) []map[string]interface{} {
	query := mysql.Slave().Table("service_packages").
		Where("status = ? AND deleted_at IS NULL", "active")

	// 检查售卖时间
	now := time.Now()
	query = query.Where("(sale_start_type = 'after_release' OR (sale_start_type = 'fixed' AND sale_start_at <= ?))", now).
		Where("(sale_end_at IS NULL OR sale_end_at >= ?)", now)

	// 根据用户标签或距离推荐
	if tagID != nil {
		// 有标签时：优先推荐相同标签的服务
		query = query.Where("tag_id = ?", *tagID)
	}

	// 排序：推荐排序 + 创建时间
	query = query.Order("sort ASC, created_at DESC").Limit(limit)

	var services []model.ServicePackage
	if err := query.Find(&services).Error; err != nil {
		logx.Errorf("查询推荐服务失败: %v", err)
		return []map[string]interface{}{}
	}

	return h.formatServiceList(services, userLat, userLng, "default")
}

// GetMyServiceList 获取我的服务列表（已购买待使用、已预约）
func (h *ServiceHandler) GetMyServiceList(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 查询已购买待使用的订单（含次数未用完的订单）
	var orders []struct {
		model.ServiceOrder
		ServicePackage model.ServicePackage      `gorm:"foreignKey:ServicePackageID"`
		Appointment    *model.ServiceAppointment `gorm:"foreignKey:OrderID"`
	}

	err := mysql.Slave().Table("service_orders").
		Select("service_orders.*, service_packages.*, service_appointments.*").
		Joins("LEFT JOIN service_packages ON service_orders.service_package_id = service_packages.id").
		Joins("LEFT JOIN service_appointments ON service_orders.id = service_appointments.order_id AND service_appointments.status = 'booked'").
		Where("service_orders.user_id = ? AND service_orders.pay_status = 'paid' AND service_orders.remaining_times > 0", userID).
		Where("service_orders.deleted_at IS NULL").
		Order("CASE WHEN service_appointments.id IS NOT NULL THEN 0 ELSE 1 END, service_appointments.created_at DESC, service_orders.created_at DESC").
		Find(&orders).Error

	if err != nil {
		logx.Errorf("查询我的服务列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询服务列表失败"))
		return
	}

	result := make([]map[string]interface{}, 0, len(orders))
	for _, order := range orders {
		item := map[string]interface{}{
			"order_id":         order.ID,
			"order_no":         order.OrderNo,
			"service_id":       order.ServicePackageID,
			"service_name":     order.ServiceName,
			"service_images":   order.ServicePackage.Images,
			"price":            order.PayAmount,
			"original_price":   order.OriginalAmount,
			"remaining_times":  order.RemainingTimes,
			"used_times":       order.UsedTimes,
			"validity_start":   order.ValidityStart,
			"validity_end":     order.ValidityEnd,
			"appointment_rule": order.ServicePackage.AppointmentRule,
			"status":           order.Status,
			"pay_time":         order.PayTime,
			"created_at":       order.CreatedAt,
		}

		// 如果有预约信息
		if order.Appointment != nil {
			item["appointment"] = map[string]interface{}{
				"id":               order.Appointment.ID,
				"appointment_date": order.Appointment.AppointmentDate,
				"appointment_time": order.Appointment.AppointmentTime,
				"store_id":         order.Appointment.StoreID,
				"status":           order.Appointment.Status,
				"modify_count":     order.Appointment.ModifyCount,
			}
			item["has_appointment"] = true
		} else {
			item["has_appointment"] = false
		}

		result = append(result, item)
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取我的服务列表成功"))
}

// GenerateServiceQRCode 生成服务订单二维码
func (h *ServiceHandler) GenerateServiceQRCode(w http.ResponseWriter, r *http.Request) {
	var req struct {
		OrderID uint `json:"order_id"` // 订单ID
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 获取用户ID
	userID, err := wxUtils.GetUserIDFromContext(r.Context())
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	logx.Infof("生成服务订单二维码: userID=%d, orderId=%d", userID, req.OrderID)

	// 查询订单详情
	var order model.ServiceOrder
	if err := mysql.Slave().Where("id = ? AND user_id = ?", req.OrderID, userID).First(&order).Error; err != nil {
		logx.Errorf("查询服务订单详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在"))
		return
	}

	// 检查订单状态
	if order.Status != "paid" { // 只有已支付状态才能生成二维码
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单状态不允许生成二维码"))
		return
	}

	// 使用服务二维码服务生成二维码
	qrCodeService := serviceQRCode.NewServiceQRCodeService()
	qrCodeURL, err := qrCodeService.GenerateServiceQRCode(r.Context(), req.OrderID)
	if err != nil {
		logx.Errorf("生成二维码失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "生成二维码失败"))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"qr_code_url": qrCodeURL,
	}, "生成二维码成功"))
}

// VerifyServiceRequest 核销服务订单请求
type VerifyServiceRequest struct {
	OrderNo          string `json:"order_no"`          // 订单编号
	VerificationCode string `json:"verification_code"` // 核销码
	Sign             string `json:"sign"`              // 签名
	Timestamp        string `json:"timestamp"`         // 时间戳
	VerifierID       uint   `json:"verifier_id"`       // 核销人ID
	VerifierName     string `json:"verifier_name"`     // 核销人姓名
}

// VerifyServiceOrder 核销服务订单
func (h *ServiceHandler) VerifyServiceOrder(w http.ResponseWriter, r *http.Request) {
	var req VerifyServiceRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	logx.Infof("核销服务订单: orderNo=%s, verificationCode=%s, verifierID=%d",
		req.OrderNo, req.VerificationCode, req.VerifierID)

	// 使用服务二维码服务进行核销
	qrCodeService := serviceQRCode.NewServiceQRCodeService()
	err := qrCodeService.VerifyServiceCode(r.Context(), req.OrderNo, req.VerificationCode, req.VerifierID, req.VerifierName)
	if err != nil {
		logx.Errorf("核销服务订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"order_no":          req.OrderNo,
		"verification_time": time.Now(),
		"verifier_name":     req.VerifierName,
	}, "核销成功"))
}
