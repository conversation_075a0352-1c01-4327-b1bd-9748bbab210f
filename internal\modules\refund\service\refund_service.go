package service

import (
	"context"
	"fmt"
	"time"

	refundModel "yekaitai/internal/modules/refund/model"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/payment"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// RefundService 退款服务
type RefundService struct {
	db             *gorm.DB
	refundRepo     refundModel.RefundRecordRepository
	paymentService *payment.PaymentService
}

// NewRefundService 创建退款服务
func NewRefundService() *RefundService {
	db := mysql.Master()
	return &RefundService{
		db:             db,
		refundRepo:     refundModel.NewRefundRecordRepository(db),
		paymentService: payment.GetGlobalPaymentService(),
	}
}

// CreateRefundRequest 创建退款请求
type CreateRefundRequest struct {
	OrderNo      string `json:"order_no" validate:"required"`     // 订单号
	Amount       int    `json:"amount" validate:"required,min=1"` // 退款金额（分）
	Reason       string `json:"reason" validate:"required"`       // 退款原因
	OperatorID   uint   `json:"operator_id"`                      // 操作人ID
	OperatorName string `json:"operator_name"`                    // 操作人姓名
}

// CreateRefundResponse 创建退款响应
type CreateRefundResponse struct {
	RefundNo   string `json:"refund_no"`    // 退款单号
	Status     string `json:"status"`       // 退款状态
	WxRefundID string `json:"wx_refund_id"` // 微信退款单号
}

// CreateRefund 创建退款
func (s *RefundService) CreateRefund(ctx context.Context, req *CreateRefundRequest) (*CreateRefundResponse, error) {
	// 获取业务类型
	bizType := refundModel.GetBizTypeFromOrderNo(req.OrderNo)
	if bizType == "" {
		return nil, fmt.Errorf("无法识别订单号的业务类型: %s", req.OrderNo)
	}

	// 生成退款单号
	refundNo := refundModel.GenerateRefundNo(req.OrderNo)

	// 创建退款记录
	refundRecord := &refundModel.RefundRecord{
		RefundNo:     refundNo,
		OrderNo:      req.OrderNo,
		BizType:      bizType,
		Amount:       req.Amount,
		Reason:       req.Reason,
		Status:       refundModel.RefundStatusPending,
		OperatorID:   req.OperatorID,
		OperatorName: req.OperatorName,
	}

	if err := s.refundRepo.Create(refundRecord); err != nil {
		logx.Errorf("创建退款记录失败: %v", err)
		return nil, fmt.Errorf("创建退款记录失败: %w", err)
	}

	// 调用微信退款接口
	err := s.paymentService.RefundPayment(ctx, req.OrderNo, int64(req.Amount), req.Reason)
	if err != nil {
		// 更新退款状态为失败
		s.refundRepo.UpdateStatus(refundNo, refundModel.RefundStatusFailed, "")
		logx.Errorf("调用微信退款失败: orderNo=%s, error=%v", req.OrderNo, err)
		return nil, fmt.Errorf("调用微信退款失败: %w", err)
	}

	logx.Infof("创建退款成功: refundNo=%s, orderNo=%s, amount=%d", refundNo, req.OrderNo, req.Amount)

	return &CreateRefundResponse{
		RefundNo:   refundNo,
		Status:     refundModel.RefundStatusPending,
		WxRefundID: "", // 微信退款ID将在回调中更新
	}, nil
}

// ProcessRefundCallback 处理退款回调
func (s *RefundService) ProcessRefundCallback(ctx context.Context, callbackData map[string]interface{}) error {
	// 提取回调数据
	wxRefundID, ok := callbackData["refund_id"].(string)
	if !ok {
		return fmt.Errorf("回调数据中缺少退款ID")
	}

	refundStatus, ok := callbackData["refund_status"].(string)
	if !ok {
		return fmt.Errorf("回调数据中缺少退款状态")
	}

	outRefundNo, ok := callbackData["out_refund_no"].(string)
	if !ok {
		return fmt.Errorf("回调数据中缺少商户退款单号")
	}

	logx.Infof("处理退款回调: wxRefundID=%s, refundStatus=%s, outRefundNo=%s", wxRefundID, refundStatus, outRefundNo)

	// 查询退款记录
	refundRecord, err := s.refundRepo.GetByRefundNo(outRefundNo)
	if err != nil {
		logx.Errorf("查询退款记录失败: refundNo=%s, error=%v", outRefundNo, err)
		return fmt.Errorf("查询退款记录失败: %w", err)
	}

	// 转换退款状态
	var status string
	switch refundStatus {
	case "SUCCESS":
		status = refundModel.RefundStatusSuccess
	case "ABNORMAL", "CLOSED":
		status = refundModel.RefundStatusFailed
	default:
		status = refundModel.RefundStatusPending
	}

	// 更新退款记录状态
	if err := s.refundRepo.UpdateStatus(outRefundNo, status, wxRefundID); err != nil {
		logx.Errorf("更新退款记录状态失败: refundNo=%s, error=%v", outRefundNo, err)
		return fmt.Errorf("更新退款记录状态失败: %w", err)
	}

	// 根据业务类型路由到具体的业务处理
	if err := s.routeRefundCallback(ctx, refundRecord, status); err != nil {
		logx.Errorf("路由退款回调失败: refundNo=%s, bizType=%s, error=%v", outRefundNo, refundRecord.BizType, err)
		return fmt.Errorf("路由退款回调失败: %w", err)
	}

	logx.Infof("退款回调处理完成: refundNo=%s, status=%s", outRefundNo, status)
	return nil
}

// routeRefundCallback 根据业务类型路由退款回调
func (s *RefundService) routeRefundCallback(ctx context.Context, refundRecord *refundModel.RefundRecord, status string) error {
	switch refundRecord.BizType {
	case refundModel.BizTypeActivity:
		return s.handleActivityRefundCallback(ctx, refundRecord, status)
	case refundModel.BizTypeService:
		return s.handleServiceRefundCallback(ctx, refundRecord, status)
	case refundModel.BizTypeMall:
		return s.handleMallRefundCallback(ctx, refundRecord, status)
	default:
		return fmt.Errorf("未知的业务类型: %s", refundRecord.BizType)
	}
}

// handleActivityRefundCallback 处理活动退款回调
func (s *RefundService) handleActivityRefundCallback(ctx context.Context, refundRecord *refundModel.RefundRecord, status string) error {
	logx.Infof("处理活动退款回调: orderNo=%s, status=%s", refundRecord.OrderNo, status)

	// 调用活动模块的退款处理逻辑
	// 这里可以通过接口或者直接调用活动服务
	// 为了避免循环依赖，暂时使用日志记录，实际可以通过事件总线或消息队列实现

	if status == refundModel.RefundStatusSuccess {
		logx.Infof("活动退款成功: orderNo=%s, amount=%d", refundRecord.OrderNo, refundRecord.Amount)
		// TODO: 可以通过事件总线发送退款成功事件
		// eventBus.Publish("activity.refund.success", refundRecord)
	} else if status == refundModel.RefundStatusFailed {
		logx.Errorf("活动退款失败: orderNo=%s, amount=%d", refundRecord.OrderNo, refundRecord.Amount)
		// TODO: 可以通过事件总线发送退款失败事件
		// eventBus.Publish("activity.refund.failed", refundRecord)
	}

	return nil
}

// handleServiceRefundCallback 处理服务退款回调
func (s *RefundService) handleServiceRefundCallback(ctx context.Context, refundRecord *refundModel.RefundRecord, status string) error {
	logx.Infof("处理服务退款回调: orderNo=%s, status=%s", refundRecord.OrderNo, status)

	// TODO: 实现服务模块的退款回调处理逻辑

	return nil
}

// handleMallRefundCallback 处理商城退款回调
func (s *RefundService) handleMallRefundCallback(ctx context.Context, refundRecord *refundModel.RefundRecord, status string) error {
	logx.Infof("处理商城退款回调: orderNo=%s, status=%s", refundRecord.OrderNo, status)

	// TODO: 实现商城模块的退款回调处理逻辑

	return nil
}

// GetRefundByOrderNo 根据订单号查询退款记录
func (s *RefundService) GetRefundByOrderNo(orderNo string) ([]*refundModel.RefundRecord, error) {
	return s.refundRepo.GetByOrderNo(orderNo)
}

// GetRefundByRefundNo 根据退款单号查询退款记录
func (s *RefundService) GetRefundByRefundNo(refundNo string) (*refundModel.RefundRecord, error) {
	return s.refundRepo.GetByRefundNo(refundNo)
}

// ListRefunds 分页查询退款记录
func (s *RefundService) ListRefunds(page, pageSize int, filters map[string]interface{}) ([]*refundModel.RefundRecord, int64, error) {
	return s.refundRepo.List(page, pageSize, filters)
}

// GetRefundStatistics 获取退款统计信息
func (s *RefundService) GetRefundStatistics() map[string]interface{} {
	// 统计各种状态的退款数量和金额
	var stats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
		Amount int64  `json:"amount"`
	}

	s.db.Model(&refundModel.RefundRecord{}).
		Select("status, COUNT(*) as count, SUM(amount) as amount").
		Group("status").
		Find(&stats)

	// 统计各业务类型的退款情况
	var bizStats []struct {
		BizType string `json:"biz_type"`
		Count   int64  `json:"count"`
		Amount  int64  `json:"amount"`
	}

	s.db.Model(&refundModel.RefundRecord{}).
		Select("biz_type, COUNT(*) as count, SUM(amount) as amount").
		Group("biz_type").
		Find(&bizStats)

	return map[string]interface{}{
		"status_stats":   stats,
		"biz_type_stats": bizStats,
		"last_updated":   time.Now().Format("2006-01-02 15:04:05"),
	}
}
