package patient

import (
	"time"

	"gorm.io/gorm"
)

// AbcYunTreatmentPrescription ABC云治疗单处方表
type AbcYunTreatmentPrescription struct {
	ID                uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	PatientID         uint           `gorm:"column:patient_id;index;comment:患者ID(关联wx_patient表)" json:"patient_id"`
	ExternalID        string         `gorm:"column:external_id;index;comment:外部治疗单ID" json:"external_id"`
	OutpatientSheetID string         `gorm:"column:outpatient_sheet_id;index;comment:门诊单ID" json:"outpatient_sheet_id"`
	Type              int            `gorm:"column:type;comment:类别(2:检查检验,3:治疗理疗,8:商品,9:材料)" json:"type"`
	CreatedAt         time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (AbcYunTreatmentPrescription) TableName() string {
	return "abcyun_treatment_prescriptions"
}

// AbcYunTreatmentPrescriptionItem ABC云治疗单处方子项表
type AbcYunTreatmentPrescriptionItem struct {
	ID             uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	PatientID      uint           `gorm:"column:patient_id;index;comment:患者ID(关联wx_patient表)" json:"patient_id"`
	PrescriptionID uint           `gorm:"column:prescription_id;index;comment:治疗单处方ID" json:"prescription_id"`
	ExternalID     string         `gorm:"column:external_id;index;comment:外部治疗单子项ID" json:"external_id"`
	ProductFormID  string         `gorm:"column:product_form_id;comment:外部治疗单ID" json:"product_form_id"`
	ProductID      string         `gorm:"column:product_id;comment:治疗项ID" json:"product_id"`
	Name           string         `gorm:"column:name;comment:治疗项名称" json:"name"`
	UnitCount      float64        `gorm:"column:unit_count;comment:单位次数" json:"unit_count"`
	Unit           string         `gorm:"column:unit;comment:单位" json:"unit"`
	UnitPrice      float64        `gorm:"column:unit_price;comment:单位价格" json:"unit_price"`
	IsDismounting  int            `gorm:"column:is_dismounting;comment:是否拆零(0:未拆零,1:拆零)" json:"is_dismounting"`
	Type           int            `gorm:"column:type;comment:类型(1:药品,2:物资,3:检查,4:治疗,5:挂号)" json:"type"`
	SubType        int            `gorm:"column:sub_type;comment:子类型" json:"sub_type"`
	Days           int            `gorm:"column:days;comment:诊疗项目天数" json:"days"`
	DailyDosage    int            `gorm:"column:daily_dosage;comment:每天次数" json:"daily_dosage"`
	Remark         string         `gorm:"column:remark;comment:备注" json:"remark"`
	CreatedAt      time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt      time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (AbcYunTreatmentPrescriptionItem) TableName() string {
	return "abcyun_treatment_prescription_items"
}
