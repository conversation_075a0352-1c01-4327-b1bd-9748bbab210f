package model

import (
	"time"

	"gorm.io/gorm"
)

// AIKnowledgeBase AI知识库文件模型
type AIKnowledgeBase struct {
	ID          uint   `gorm:"primarykey" json:"id"`
	FileName    string `gorm:"size:255;not null;comment:文件名称" json:"file_name"`
	FileURL     string `gorm:"size:500;not null;comment:文件URL" json:"file_url"`
	FileSize    int64  `gorm:"comment:文件大小(字节)" json:"file_size"`
	FileType    string `gorm:"size:50;comment:文件类型" json:"file_type"`
	UploadBy    uint   `gorm:"not null;comment:上传人ID" json:"upload_by"`
	UploadName  string `gorm:"size:100;not null;comment:上传人姓名" json:"upload_name"`
	Description string `gorm:"size:500;comment:文件描述" json:"description"`
	Status      string `gorm:"size:20;not null;default:active;comment:状态:active-正常,deleted-已删除" json:"status"`

	// 医联相关字段
	MedlinkerKnowledgeID string     `gorm:"size:100;comment:医联知识库ID" json:"medlinker_knowledge_id"`
	MedlinkerDocumentID  string     `gorm:"size:100;comment:医联文档ID" json:"medlinker_document_id"`
	SyncStatus           string     `gorm:"size:20;default:pending;comment:同步状态:pending-待同步,syncing-同步中,success-成功,failed-失败" json:"sync_status"`
	SyncMessage          string     `gorm:"size:500;comment:同步消息" json:"sync_message"`
	SyncAt               *time.Time `gorm:"comment:同步时间" json:"sync_at"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (AIKnowledgeBase) TableName() string {
	return "ai_knowledge_base"
}

// AIKnowledgeBaseRepository AI知识库仓库接口
type AIKnowledgeBaseRepository interface {
	Create(kb *AIKnowledgeBase) error
	FindByID(id uint) (*AIKnowledgeBase, error)
	List(page, size int, fileName string) ([]*AIKnowledgeBase, int64, error)
	Update(kb *AIKnowledgeBase) error
	Delete(id uint) error
	SoftDelete(id uint) error
	GetActiveKnowledgeBase() (*AIKnowledgeBase, error) // 获取当前活跃的知识库
	DeleteAll() error                                  // 删除所有知识库（用于覆盖上传）
}

// aiKnowledgeBaseRepository AI知识库仓库实现
type aiKnowledgeBaseRepository struct {
	db *gorm.DB
}

// NewAIKnowledgeBaseRepository 创建AI知识库仓库
func NewAIKnowledgeBaseRepository(db *gorm.DB) AIKnowledgeBaseRepository {
	return &aiKnowledgeBaseRepository{db: db}
}

// Create 创建AI知识库记录
func (r *aiKnowledgeBaseRepository) Create(kb *AIKnowledgeBase) error {
	return r.db.Create(kb).Error
}

// FindByID 根据ID查找AI知识库记录
func (r *aiKnowledgeBaseRepository) FindByID(id uint) (*AIKnowledgeBase, error) {
	var kb AIKnowledgeBase
	err := r.db.Where("id = ? AND status != ?", id, "deleted").First(&kb).Error
	return &kb, err
}

// List 获取AI知识库列表
func (r *aiKnowledgeBaseRepository) List(page, size int, fileName string) ([]*AIKnowledgeBase, int64, error) {
	var kbs []*AIKnowledgeBase
	var total int64

	db := r.db.Model(&AIKnowledgeBase{}).Where("status != ?", "deleted")

	// 添加筛选条件
	if fileName != "" {
		db = db.Where("file_name LIKE ?", "%"+fileName+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("created_at DESC").Offset(offset).Limit(size).Find(&kbs).Error; err != nil {
		return nil, 0, err
	}

	return kbs, total, nil
}

// Update 更新AI知识库记录
func (r *aiKnowledgeBaseRepository) Update(kb *AIKnowledgeBase) error {
	return r.db.Save(kb).Error
}

// Delete 物理删除AI知识库记录
func (r *aiKnowledgeBaseRepository) Delete(id uint) error {
	return r.db.Unscoped().Delete(&AIKnowledgeBase{}, id).Error
}

// SoftDelete 软删除AI知识库记录
func (r *aiKnowledgeBaseRepository) SoftDelete(id uint) error {
	return r.db.Model(&AIKnowledgeBase{}).Where("id = ?", id).
		Updates(map[string]any{
			"status":     "deleted",
			"updated_at": time.Now(),
		}).Error
}

// GetActiveKnowledgeBase 获取当前活跃的知识库
func (r *aiKnowledgeBaseRepository) GetActiveKnowledgeBase() (*AIKnowledgeBase, error) {
	var kb AIKnowledgeBase
	err := r.db.Where("status = ?", "active").First(&kb).Error
	if err != nil {
		return nil, err
	}
	return &kb, nil
}

// DeleteAll 删除所有知识库（用于覆盖上传）
func (r *aiKnowledgeBaseRepository) DeleteAll() error {
	return r.db.Where("1 = 1").Delete(&AIKnowledgeBase{}).Error
}
