package abcyun

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/internal/modules/appointment/types"
	"yekaitai/internal/svc"
)

// --- Product Types --- //

type AbcYunProductTypesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductTypesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductTypesLogic {
	return &AbcYunProductTypesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductTypes 获取商品分类
func (l *AbcYunProductTypesLogic) AbcYunProductTypes(req *types.AbcYunRequest) (*types.AbcYunProductTypesResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := "/api/v2/open-agency/products/types"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取商品分类失败: %w", err)
	}

	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.ProductTypeData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductTypesResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product Custom Types --- //

type AbcYunProductCustomTypesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductCustomTypesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductCustomTypesLogic {
	return &AbcYunProductCustomTypesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductCustomTypes 获取商品自定义分类
func (l *AbcYunProductCustomTypesLogic) AbcYunProductCustomTypes(req *types.AbcYunRequest) (*types.AbcYunProductCustomTypesResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := "/api/v2/open-agency/products/custom-types"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取商品自定义分类失败: %w", err)
	}

	var result struct {
		Code    int                         `json:"code"`
		Message string                      `json:"message"`
		Data    types.CustomProductTypeData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductCustomTypesResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product List --- //

type AbcYunProductListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductListLogic {
	return &AbcYunProductListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductList 获取商品列表
func (l *AbcYunProductListLogic) AbcYunProductList(req *types.AbcYunProductListRequest) (*types.AbcYunProductListResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}
	// Type 是必填项
	params["type"] = strconv.Itoa(req.Type)
	if req.BeginDate != "" {
		params["beginDate"] = req.BeginDate
	}
	if req.EndDate != "" {
		params["endDate"] = req.EndDate
	}
	if req.DateFieldType > 0 {
		params["dateFieldType"] = strconv.Itoa(req.DateFieldType)
	}
	if req.IncludeDeleted > 0 { // 0:不包含, 1:包含
		params["includeDeleted"] = strconv.Itoa(req.IncludeDeleted)
	}
	if req.Limit > 0 {
		params["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset >= 0 { // Offset 可以是 0
		params["offset"] = strconv.Itoa(req.Offset)
	}

	path := "/api/v2/open-agency/products"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取商品列表失败: %w", err)
	}

	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.ProductListData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductListResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product Detail --- //

type AbcYunProductDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductDetailLogic {
	return &AbcYunProductDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductDetail 获取商品详情
func (l *AbcYunProductDetailLogic) AbcYunProductDetail(req *types.AbcYunProductDetailRequest) (*types.AbcYunProductDetailResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/%s", req.ProductID)
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取商品详情失败: %w", err)
	}

	var result struct {
		Code    int                     `json:"code"`
		Message string                  `json:"message"`
		Data    types.ProductDetailInfo `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductDetailResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product Out Order List --- //

type AbcYunProductOutOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductOutOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductOutOrderListLogic {
	return &AbcYunProductOutOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductOutOrderList 获取出库单列表
func (l *AbcYunProductOutOrderListLogic) AbcYunProductOutOrderList(req *types.AbcYunProductOutOrderListRequest) (*types.AbcYunProductOutOrderListResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}
	// BeginDate 和 EndDate 是必填项
	params["beginDate"] = req.BeginDate
	params["endDate"] = req.EndDate
	if req.Limit > 0 {
		params["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset >= 0 {
		params["offset"] = strconv.Itoa(req.Offset)
	}

	path := "/api/v2/open-agency/products/out-order-list"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取出库单列表失败: %w", err)
	}

	var result struct {
		Code    int                         `json:"code"`
		Message string                      `json:"message"`
		Data    types.StockOutOrderListData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductOutOrderListResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product Out Order Detail --- //

type AbcYunProductOutOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductOutOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductOutOrderDetailLogic {
	return &AbcYunProductOutOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductOutOrderDetail 获取出库单详情
func (l *AbcYunProductOutOrderDetailLogic) AbcYunProductOutOrderDetail(req *types.AbcYunProductOutOrderDetailRequest) (*types.AbcYunProductOutOrderDetailResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/out-order-detail/%s", req.OrderID)
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取出库单详情失败: %w", err)
	}

	var result struct {
		Code    int                       `json:"code"`
		Message string                    `json:"message"`
		Data    types.StockOutOrderDetail `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductOutOrderDetailResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product In Order List --- //

type AbcYunProductInOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductInOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductInOrderListLogic {
	return &AbcYunProductInOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductInOrderList 获取入库单列表
func (l *AbcYunProductInOrderListLogic) AbcYunProductInOrderList(req *types.AbcYunProductInOrderListRequest) (*types.AbcYunProductInOrderListResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}
	// BeginDate 和 EndDate 是必填项
	params["beginDate"] = req.BeginDate
	params["endDate"] = req.EndDate
	if req.Type >= 0 { // Type 可以是 0
		params["type"] = strconv.Itoa(req.Type)
	}
	if req.Limit > 0 {
		params["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset >= 0 {
		params["offset"] = strconv.Itoa(req.Offset)
	}

	path := "/api/v2/open-agency/products/in-order-list"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取入库单列表失败: %w", err)
	}

	var result struct {
		Code    int                        `json:"code"`
		Message string                     `json:"message"`
		Data    types.StockInOrderListData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductInOrderListResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product In Order Detail --- //

type AbcYunProductInOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductInOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductInOrderDetailLogic {
	return &AbcYunProductInOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductInOrderDetail 获取入库单详情
func (l *AbcYunProductInOrderDetailLogic) AbcYunProductInOrderDetail(req *types.AbcYunProductInOrderDetailRequest) (*types.AbcYunProductInOrderDetailResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/in-order-detail/%s", req.OrderID)
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取入库单详情失败: %w", err)
	}

	var result struct {
		Code    int                      `json:"code"`
		Message string                   `json:"message"`
		Data    types.StockInOrderDetail `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductInOrderDetailResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product Check Order List --- //

type AbcYunProductCheckOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductCheckOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductCheckOrderListLogic {
	return &AbcYunProductCheckOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductCheckOrderList 获取盘点单列表
func (l *AbcYunProductCheckOrderListLogic) AbcYunProductCheckOrderList(req *types.AbcYunProductCheckOrderListRequest) (*types.AbcYunProductCheckOrderListResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}
	// BeginDate 和 EndDate 是必填项
	params["beginDate"] = req.BeginDate
	params["endDate"] = req.EndDate
	if req.Limit > 0 {
		params["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset >= 0 {
		params["offset"] = strconv.Itoa(req.Offset)
	}

	path := "/api/v2/open-agency/products/check-order-list"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取盘点单列表失败: %w", err)
	}

	var result struct {
		Code    int                      `json:"code"`
		Message string                   `json:"message"`
		Data    types.CheckOrderListData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductCheckOrderListResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Product Check Order Detail --- //

type AbcYunProductCheckOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunProductCheckOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunProductCheckOrderDetailLogic {
	return &AbcYunProductCheckOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunProductCheckOrderDetail 获取盘点单详情
func (l *AbcYunProductCheckOrderDetailLogic) AbcYunProductCheckOrderDetail(req *types.AbcYunProductCheckOrderDetailRequest) (*types.AbcYunProductCheckOrderDetailResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/check-order-detail/%s", req.OrderID)
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取盘点单详情失败: %w", err)
	}

	var result struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    types.CheckOrderDetail `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunProductCheckOrderDetailResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Settlement Order List (5.3.11) --- //

type AbcYunSettlementOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunSettlementOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunSettlementOrderListLogic {
	return &AbcYunSettlementOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunSettlementOrderList 获取结算单列表
func (l *AbcYunSettlementOrderListLogic) AbcYunSettlementOrderList(req *types.AbcYunSettlementOrderListRequest) (*types.AbcYunSettlementOrderListResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}
	if req.BeginDate != "" {
		params["beginDate"] = req.BeginDate
	}
	if req.EndDate != "" {
		params["endDate"] = req.EndDate
	}
	if req.DateFieldType > 0 { // 默认是1，所以只有大于0时才传
		params["dateFieldType"] = strconv.Itoa(req.DateFieldType)
	}

	path := "/api/v2/open-agency/products/settlement-order-list"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取结算单列表失败: %w", err)
	}

	var result struct {
		Code    int                           `json:"code"`
		Message string                        `json:"message"`
		Data    types.SettlementOrderListData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunSettlementOrderListResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Settlement Order Detail (5.3.12) --- //

type AbcYunSettlementOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunSettlementOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunSettlementOrderDetailLogic {
	return &AbcYunSettlementOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunSettlementOrderDetail 获取结算单详情
func (l *AbcYunSettlementOrderDetailLogic) AbcYunSettlementOrderDetail(req *types.AbcYunSettlementOrderDetailRequest) (*types.AbcYunSettlementOrderDetailResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/settlement-order-detail/%s", req.OrderID)
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取结算单详情失败: %w", err)
	}

	var result struct {
		Code    int                         `json:"code"`
		Message string                      `json:"message"`
		Data    types.SettlementOrderDetail `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunSettlementOrderDetailResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Supplier List (5.3.13) --- //

type AbcYunSupplierListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunSupplierListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunSupplierListLogic {
	return &AbcYunSupplierListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunSupplierList 获取供应商列表
func (l *AbcYunSupplierListLogic) AbcYunSupplierList(req *types.AbcYunSupplierListRequest) (*types.AbcYunSupplierListResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := "/api/v2/open-agency/products/suppliers"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	var result struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    types.SupplierListData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunSupplierListResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- InOut Stock Details (5.3.14) --- //

type AbcYunInOutStockDetailsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunInOutStockDetailsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunInOutStockDetailsLogic {
	return &AbcYunInOutStockDetailsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunInOutStockDetails 获取进销存明细
func (l *AbcYunInOutStockDetailsLogic) AbcYunInOutStockDetails(req *types.AbcYunInOutStockDetailsRequest) (*types.AbcYunInOutStockDetailsResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// Prepare body payload
	payload := map[string]interface{}{
		"offset": req.Offset,
		"limit":  req.Limit,
	}
	if req.Date != "" {
		payload["date"] = req.Date
	}
	if len(req.OrderIDList) > 0 {
		payload["orderIdList"] = req.OrderIDList
	}
	if len(req.ChargeSheetList) > 0 {
		payload["chargeSheetList"] = req.ChargeSheetList
	}
	if req.Type != 0 { // Check if type is provided and non-zero
		payload["type"] = req.Type
	}
	if req.ClinicID != "" { // Add clinicId if provided
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := "/api/v2/open-agency/products/inout-stock-details"
	respData, err := client.Post(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("获取进销存明细失败: %w", err)
	}

	var result struct {
		Code    int                         `json:"code"`
		Message string                      `json:"message"`
		Data    types.InOutStockDetailsData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunInOutStockDetailsResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Query Product Stock (5.3.15) --- //

type AbcYunQueryProductStockLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunQueryProductStockLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunQueryProductStockLogic {
	return &AbcYunQueryProductStockLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunQueryProductStock 查询商品库存
func (l *AbcYunQueryProductStockLogic) AbcYunQueryProductStock(req *types.AbcYunQueryProductStockRequest) (*types.AbcYunQueryProductStockResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	payload := map[string]interface{}{
		"productIds": req.ProductIDs,
	}
	if req.ClinicID != "" { // Add clinicId if provided
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := "/api/v2/open-agency/products/stock/query-by-id"
	respData, err := client.Post(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("查询商品库存失败: %w", err)
	}

	var result struct {
		Code    int                         `json:"code"`
		Message string                      `json:"message"`
		Data    types.QueryProductStockData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunQueryProductStockResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Query Product Batches (5.3.16) --- //

type AbcYunQueryProductBatchesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunQueryProductBatchesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunQueryProductBatchesLogic {
	return &AbcYunQueryProductBatchesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunQueryProductBatches 查询商品批次信息
func (l *AbcYunQueryProductBatchesLogic) AbcYunQueryProductBatches(req *types.AbcYunQueryProductBatchesRequest) (*types.AbcYunQueryProductBatchesResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	payload := map[string]interface{}{
		"productIds": req.ProductIDs,
	}
	if req.ClinicID != "" { // Add clinicId if provided
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := "/api/v2/open-agency/products/batches/query-by-id"
	respData, err := client.Post(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("查询商品批次信息失败: %w", err)
	}

	var result struct {
		Code    int                           `json:"code"`
		Message string                        `json:"message"`
		Data    types.QueryProductBatchesData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunQueryProductBatchesResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Create Supplier (5.3.17) --- //

type AbcYunCreateSupplierLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunCreateSupplierLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunCreateSupplierLogic {
	return &AbcYunCreateSupplierLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunCreateSupplier 新增供应商
func (l *AbcYunCreateSupplierLogic) AbcYunCreateSupplier(req *types.AbcYunCreateSupplierRequest) (*types.AbcYunCreateSupplierResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// Create payload excluding ClinicID from AbcYunRequest
	payload := map[string]interface{}{
		"name":       req.Name,
		"status":     req.Status,
		"licenseId":  req.LicenseID,
		"contact":    req.Contact,
		"mobile":     req.Mobile,
		"mark":       req.Mark,
		"operatorId": req.OperatorID,
	}
	if req.ClinicID != "" { // Add clinicId if provided
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := "/api/v2/open-agency/products/suppliers"
	respData, err := client.Post(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("新增供应商失败: %w", err)
	}

	var result struct {
		Code    int                `json:"code"`
		Message string             `json:"message"`
		Data    types.SupplierInfo `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunCreateSupplierResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Update Supplier (5.3.18) --- //

type AbcYunUpdateSupplierLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunUpdateSupplierLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunUpdateSupplierLogic {
	return &AbcYunUpdateSupplierLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunUpdateSupplier 修改供应商
func (l *AbcYunUpdateSupplierLogic) AbcYunUpdateSupplier(req *types.AbcYunUpdateSupplierRequest) (*types.AbcYunUpdateSupplierResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// Create payload excluding ClinicID and ID (from path) from request
	payload := map[string]interface{}{
		"name":       req.Name,
		"status":     req.Status,
		"licenseId":  req.LicenseID,
		"contact":    req.Contact,
		"mobile":     req.Mobile,
		"mark":       req.Mark,
		"operatorId": req.OperatorID,
	}
	if req.ClinicID != "" { // Add clinicId if provided
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/suppliers/%s", req.ID)
	respData, err := client.Put(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("修改供应商失败: %w", err)
	}

	var result struct {
		Code    int                `json:"code"`
		Message string             `json:"message"`
		Data    types.SupplierInfo `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunUpdateSupplierResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Create Product (5.3.19) --- //

type AbcYunCreateProductLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunCreateProductLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunCreateProductLogic {
	return &AbcYunCreateProductLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunCreateProduct 新增商品
func (l *AbcYunCreateProductLogic) AbcYunCreateProduct(req *types.AbcYunCreateProductRequest) (*types.AbcYunCreateProductResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// Create payload excluding ClinicID from AbcYunRequest
	payload := map[string]interface{}{
		"type":         req.Type,
		"subType":      req.SubType,
		"medicineCadn": req.MedicineCadn,
		"name":         req.Name,
		"pieceNum":     req.PieceNum,
		"pieceUnit":    req.PieceUnit,
		"packageUnit":  req.PackageUnit,
		"piecePrice":   req.PiecePrice,
		"packagePrice": req.PackagePrice,
		"operatorId":   req.OperatorID,
	}
	// Add optional fields if they exist
	if req.ShortID != "" {
		payload["shortId"] = req.ShortID
	}
	if req.CMSpec != "" {
		payload["cMSpec"] = req.CMSpec
	}
	// Assuming dismounting defaults to 0 if not present, check API behavior
	payload["dismounting"] = req.Dismounting
	if req.ExtendSpec != "" {
		payload["extendSpec"] = req.ExtendSpec
	}
	if req.MaterialSpec != "" {
		payload["materialSpec"] = req.MaterialSpec
	}
	if req.Manufacturer != "" {
		payload["manufacturer"] = req.Manufacturer
	}
	if req.MedicineNmpn != "" {
		payload["medicineNmpn"] = req.MedicineNmpn
	}
	if req.BarCode != "" {
		payload["barCode"] = req.BarCode
	}
	if req.ComponentContentUnit != "" {
		payload["componentContentUnit"] = req.ComponentContentUnit
	}
	// Check for non-zero as ComponentContentNum could be 0.0
	if req.ComponentContentNum != 0 {
		payload["componentContentNum"] = req.ComponentContentNum
	}
	if req.MedicineDosageNum != 0 {
		payload["medicineDosageNum"] = req.MedicineDosageNum
	}
	if req.MedicineDosageUnit != "" {
		payload["medicineDosageUnit"] = req.MedicineDosageUnit
	}
	if req.ClinicID != "" {
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := "/api/v2/open-agency/products"
	respData, err := client.Post(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("新增商品失败: %w", err)
	}

	var result struct {
		Code    int                             `json:"code"`
		Message string                          `json:"message"`
		Data    types.CreateProductResponseData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunCreateProductResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Update Product (5.3.20) --- //

type AbcYunUpdateProductLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunUpdateProductLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunUpdateProductLogic {
	return &AbcYunUpdateProductLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunUpdateProduct 修改商品
func (l *AbcYunUpdateProductLogic) AbcYunUpdateProduct(req *types.AbcYunUpdateProductRequest) (*types.AbcYunUpdateProductResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// Create payload excluding ClinicID and ID (path param) from request
	payload := map[string]interface{}{
		"type":         req.Type,
		"subType":      req.SubType,
		"medicineCadn": req.MedicineCadn,
		"name":         req.Name,
		"pieceNum":     req.PieceNum,
		"pieceUnit":    req.PieceUnit,
		"packageUnit":  req.PackageUnit,
		"piecePrice":   req.PiecePrice,
		"packagePrice": req.PackagePrice,
		"operatorId":   req.OperatorID,
	}
	// Add optional fields if they exist
	if req.ShortID != "" {
		payload["shortId"] = req.ShortID
	}
	if req.CMSpec != "" {
		payload["cMSpec"] = req.CMSpec
	}
	payload["dismounting"] = req.Dismounting
	if req.ExtendSpec != "" {
		payload["extendSpec"] = req.ExtendSpec
	}
	if req.MaterialSpec != "" {
		payload["materialSpec"] = req.MaterialSpec
	}
	if req.Manufacturer != "" {
		payload["manufacturer"] = req.Manufacturer
	}
	if req.MedicineNmpn != "" {
		payload["medicineNmpn"] = req.MedicineNmpn
	}
	if req.BarCode != "" {
		payload["barCode"] = req.BarCode
	}
	if req.ComponentContentUnit != "" {
		payload["componentContentUnit"] = req.ComponentContentUnit
	}
	if req.ComponentContentNum != 0 {
		payload["componentContentNum"] = req.ComponentContentNum
	}
	if req.MedicineDosageNum != 0 {
		payload["medicineDosageNum"] = req.MedicineDosageNum
	}
	if req.MedicineDosageUnit != "" {
		payload["medicineDosageUnit"] = req.MedicineDosageUnit
	}
	if req.ClinicID != "" {
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/%s", req.ID)
	respData, err := client.Put(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("修改商品失败: %w", err)
	}

	var result struct {
		Code    int                             `json:"code"`
		Message string                          `json:"message"`
		Data    types.CreateProductResponseData `json:"data"` // Response uses same structure as create
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunUpdateProductResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Purchase Order List (5.3.21) --- //

type AbcYunPurchaseOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunPurchaseOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunPurchaseOrderListLogic {
	return &AbcYunPurchaseOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunPurchaseOrderList 获取采购单列表
func (l *AbcYunPurchaseOrderListLogic) AbcYunPurchaseOrderList(req *types.AbcYunPurchaseOrderListRequest) (*types.AbcYunPurchaseOrderListResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}
	params["beginDate"] = req.BeginDate // Required
	params["endDate"] = req.EndDate     // Required
	// Add limit/offset if provided (assuming they exist based on convention)
	if req.Limit > 0 {
		params["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset >= 0 {
		params["offset"] = strconv.Itoa(req.Offset)
	}

	path := "/api/v2/open-agency/products/purchase-orders"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取采购单列表失败: %w", err)
	}

	var result struct {
		Code    int                         `json:"code"`
		Message string                      `json:"message"`
		Data    types.PurchaseOrderListData `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunPurchaseOrderListResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Purchase Order Detail (5.3.22) --- //

type AbcYunPurchaseOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunPurchaseOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunPurchaseOrderDetailLogic {
	return &AbcYunPurchaseOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunPurchaseOrderDetail 获取采购单详情
func (l *AbcYunPurchaseOrderDetailLogic) AbcYunPurchaseOrderDetail(req *types.AbcYunPurchaseOrderDetailRequest) (*types.AbcYunPurchaseOrderDetailResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/purchase-orders/%s", req.OrderID)
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取采购单详情失败: %w", err)
	}

	var result struct {
		Code    int                       `json:"code"`
		Message string                    `json:"message"`
		Data    types.PurchaseOrderDetail `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunPurchaseOrderDetailResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Review Purchase Order (5.3.23) --- //

type AbcYunReviewPurchaseOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunReviewPurchaseOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunReviewPurchaseOrderLogic {
	return &AbcYunReviewPurchaseOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunReviewPurchaseOrder 审核采购单
func (l *AbcYunReviewPurchaseOrderLogic) AbcYunReviewPurchaseOrder(req *types.AbcYunReviewPurchaseOrderRequest) (*types.AbcYunReviewPurchaseOrderResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	payload := map[string]interface{}{
		"pass":    req.Pass,
		"comment": req.Comment,
	}
	if req.ClinicID != "" { // Add clinicId if provided
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/purchase-orders/%s/review", req.PurchaseOrderID)
	respData, err := client.Put(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("审核采购单失败: %w", err)
	}

	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunReviewPurchaseOrderResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Review Fixed In Order (5.3.24) --- //

type AbcYunReviewFixedInOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunReviewFixedInOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunReviewFixedInOrderLogic {
	return &AbcYunReviewFixedInOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunReviewFixedInOrder 审核修正入库单
func (l *AbcYunReviewFixedInOrderLogic) AbcYunReviewFixedInOrder(req *types.AbcYunReviewFixedInOrderRequest) (*types.AbcYunReviewFixedInOrderResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	payload := map[string]interface{}{
		"pass":    req.Pass,
		"comment": req.Comment,
	}
	if req.ClinicID != "" { // Add clinicId if provided
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/fixed-in-orders/%s/review", req.FixInOrderID)
	respData, err := client.Put(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("审核修正入库单失败: %w", err)
	}

	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunReviewFixedInOrderResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Create In Order (5.3.25) --- //

type AbcYunCreateInOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunCreateInOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunCreateInOrderLogic {
	return &AbcYunCreateInOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunCreateInOrder 创建入库单
func (l *AbcYunCreateInOrderLogic) AbcYunCreateInOrder(req *types.AbcYunCreateInOrderRequest) (*types.AbcYunCreateInOrderResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// Create payload excluding ClinicID
	payload := map[string]interface{}{
		"inOrderItemList": req.InOrderItemList,
	}
	if req.SupplierID != "" {
		payload["supplierId"] = req.SupplierID
	}
	if req.OutOrderNo != "" {
		payload["outOrderNo"] = req.OutOrderNo
	}
	// pharmacyNo defaults to 0? Include if non-zero or always?
	payload["pharmacyNo"] = req.PharmacyNo
	if req.ToClinicID != "" {
		payload["toClinicId"] = req.ToClinicID
	}
	if req.Comment != "" {
		payload["comment"] = req.Comment
	}
	if req.ClinicID != "" {
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := "/api/v2/open-agency/products/in-orders"
	respData, err := client.Post(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("创建入库单失败: %w", err)
	}

	var result struct {
		Code    int                      `json:"code"`
		Message string                   `json:"message"`
		Data    types.StockInOrderDetail `json:"data"` // Response is InOrderDetail
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunCreateInOrderResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Create Out Order (Return) (5.3.26) --- //

type AbcYunCreateOutOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunCreateOutOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunCreateOutOrderLogic {
	return &AbcYunCreateOutOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunCreateOutOrder 创建退货出库单
func (l *AbcYunCreateOutOrderLogic) AbcYunCreateOutOrder(req *types.AbcYunCreateOutOrderRequest) (*types.AbcYunCreateOutOrderResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// Create payload excluding ClinicID
	payload := map[string]interface{}{
		"inOrderId":        req.InOrderID,
		"pharmacyNo":       req.PharmacyNo,
		"outOrderItemList": req.OutOrderItemList,
	}
	if req.ToClinicID != "" {
		payload["toClinicId"] = req.ToClinicID
	}
	if req.Comment != "" {
		payload["comment"] = req.Comment
	}
	if req.ClinicID != "" {
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := "/api/v2/open-agency/products/out-orders"
	respData, err := client.Post(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("创建退货出库单失败: %w", err)
	}

	// Doc example shows InOrderDetail response, but logically should be OutOrderDetail?
	// Assuming OutOrderDetail for now based on the API purpose.
	var result struct {
		Code    int                       `json:"code"`
		Message string                    `json:"message"`
		Data    types.StockOutOrderDetail `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		// Try parsing as InOrderDetail based on doc example as fallback
		var fallbackResult struct {
			Code    int                      `json:"code"`
			Message string                   `json:"message"`
			Data    types.StockInOrderDetail `json:"data"`
		}
		fbErr := json.Unmarshal(respData, &fallbackResult)
		if fbErr != nil {
			// If both fail, return original error
			return nil, fmt.Errorf("解析响应失败 (尝试Out/In Order Detail均失败): %w", err)
		}
		// If fallback succeeds, log a warning and return based on InOrderDetail
		logx.Errorf("CreateOutOrder API returned InOrderDetail structure instead of expected OutOrderDetail. Doc Example mismatch? Data: %s", string(respData))
		// Need to decide how to handle this mismatch. Return error or adapt?
		// For now, returning error as the response type is ambiguous.
		return nil, fmt.Errorf("API响应类型与预期不符(期望OutOrderDetail，得到InOrderDetail)")
	}

	return &types.AbcYunCreateOutOrderResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Review Out Apply Order (5.3.27) --- //

type AbcYunReviewOutApplyOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunReviewOutApplyOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunReviewOutApplyOrderLogic {
	return &AbcYunReviewOutApplyOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunReviewOutApplyOrder 审核退货出库申请单
func (l *AbcYunReviewOutApplyOrderLogic) AbcYunReviewOutApplyOrder(req *types.AbcYunReviewOutApplyOrderRequest) (*types.AbcYunReviewOutApplyOrderResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	payload := map[string]interface{}{
		"pass":    req.Pass,
		"comment": req.Comment,
	}
	if req.ClinicID != "" { // Add clinicId if provided
		payload["clinicId"] = req.ClinicID
	}

	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("构建请求体失败: %w", err)
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/out-apply-orders/%d/review", req.OutApplyOrderID)
	respData, err := client.Put(path, bodyBytes)
	if err != nil {
		return nil, fmt.Errorf("审核退货出库申请单失败: %w", err)
	}

	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunReviewOutApplyOrderResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Query Store Batches (5.3.28) --- //

type AbcYunQueryStoreBatchesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunQueryStoreBatchesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunQueryStoreBatchesLogic {
	return &AbcYunQueryStoreBatchesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunQueryStoreBatches 查询门店库存(批次)
func (l *AbcYunQueryStoreBatchesLogic) AbcYunQueryStoreBatches(req *types.AbcYunQueryStoreBatchesRequest) (*types.AbcYunQueryStoreBatchesResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}
	if req.Limit > 0 {
		params["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset >= 0 {
		params["offset"] = strconv.Itoa(req.Offset)
	}

	path := "/api/v2/open-agency/products/batches"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("查询门店库存(批次)失败: %w", err)
	}

	var result struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    types.StoreBatchesPage `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunQueryStoreBatchesResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Trans Order List (5.3.29) --- //

type AbcYunTransOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunTransOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunTransOrderListLogic {
	return &AbcYunTransOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunTransOrderList 获取调拨单列表
func (l *AbcYunTransOrderListLogic) AbcYunTransOrderList(req *types.AbcYunTransOrderListRequest) (*types.AbcYunTransOrderListResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}
	params["beginDate"] = req.BeginDate         // Required
	params["endDate"] = req.EndDate             // Required
	params["limit"] = strconv.Itoa(req.Limit)   // Required
	params["offset"] = strconv.Itoa(req.Offset) // Required

	if req.DateFieldType != 0 { // Default is 0
		params["dateFieldType"] = strconv.Itoa(req.DateFieldType)
	}
	if req.TransType != 0 { // Default is 0
		params["transType"] = strconv.Itoa(req.TransType)
	}

	path := "/api/v2/open-agency/products/trans-order-list"
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取调拨单列表失败: %w", err)
	}

	var result struct {
		Code    int                      `json:"code"`
		Message string                   `json:"message"`
		Data    types.TransOrderListPage `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunTransOrderListResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}

// --- Trans Order Detail (5.3.30) --- //

type AbcYunTransOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAbcYunTransOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AbcYunTransOrderDetailLogic {
	return &AbcYunTransOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AbcYunTransOrderDetail 获取调拨单详情
func (l *AbcYunTransOrderDetailLogic) AbcYunTransOrderDetail(req *types.AbcYunTransOrderDetailRequest) (*types.AbcYunTransOrderDetailResponse, error) {
	client := l.svcCtx.AbcYunClient
	if client == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	params := make(map[string]string)
	if req.ClinicID != "" {
		params["clinicId"] = req.ClinicID
	}

	path := fmt.Sprintf("/api/v2/open-agency/products/trans-order-detail/%d", req.OrderID)
	respData, err := client.Get(path, params)
	if err != nil {
		return nil, fmt.Errorf("获取调拨单详情失败: %w", err)
	}

	var result struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    types.TransOrderDetail `json:"data"`
	}

	err = json.Unmarshal(respData, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &types.AbcYunTransOrderDetailResponse{
		AbcYunResponse: types.AbcYunResponse{
			Code:    result.Code,
			Message: result.Message,
		},
		Data: result.Data,
	}, nil
}
