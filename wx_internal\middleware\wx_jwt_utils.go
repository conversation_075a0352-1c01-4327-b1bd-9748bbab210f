package middleware

import (
	"context"
	"crypto/rand"
	"errors"
	"time"
	"yekaitai/pkg/infra/mysql"
	infraRedis "yekaitai/pkg/infra/redis"
	"yekaitai/wx_internal/modules/user/model"
	"yekaitai/wx_internal/svc"

	"encoding/base64"

	"github.com/golang-jwt/jwt/v4"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// 常量定义
const (
	WxIssuer   = "mini-program"
	WxAudience = "api-pub"
)

// WxTokenInfo 小程序令牌信息
type WxTokenInfo struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	ExpiresAt    int64  `json:"expiresAt"`
}

// WxClaims 小程序JWT声明
type WxClaims struct {
	OpenID string   `json:"openid"`
	Roles  []string `json:"roles,omitempty"`
	Type   string   `json:"type"`
	jwt.RegisteredClaims
}

// GenerateWxToken 生成小程序Token
func GenerateWxToken(svcCtx *svc.WxServiceContext, openid string, roles []string) (*WxTokenInfo, error) {
	// 获取小程序密钥
	accessSecret := svcCtx.Config.JWT.WxAccessSecret
	if accessSecret == "" {
		accessSecret = "mini_program_default_secret" // 默认密钥
	}

	// 设置过期时间
	accessExpire := svcCtx.Config.JWT.WxAccessExpire
	if accessExpire == 0 {
		accessExpire = 2 * 60 * 60 // 默认2小时
	}
	accessExpireTime := time.Now().Add(time.Duration(accessExpire) * time.Second)

	// 构建Claims
	claims := WxClaims{
		OpenID: openid,
		Roles:  roles,
		Type:   TokenTypeWx,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(accessExpireTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    WxIssuer,
			Subject:   openid,
			Audience:  []string{WxAudience},
			ID:        wxGenerateJTI(), // 唯一标识符，防止重放攻击
		},
	}

	// 生成Token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(accessSecret))
	if err != nil {
		return nil, err
	}

	// 刷新Token
	refreshSecret := svcCtx.Config.JWT.WxRefreshSecret
	if refreshSecret == "" {
		refreshSecret = "mini_program_refresh_default_secret"
	}

	refreshExpire := svcCtx.Config.JWT.WxRefreshExpire
	if refreshExpire == 0 {
		refreshExpire = 7 * 24 * 60 * 60 // 默认7天
	}
	refreshExpireTime := time.Now().Add(time.Duration(refreshExpire) * time.Second)

	refreshClaims := jwt.RegisteredClaims{
		ExpiresAt: jwt.NewNumericDate(refreshExpireTime),
		IssuedAt:  jwt.NewNumericDate(time.Now()),
		NotBefore: jwt.NewNumericDate(time.Now()),
		Issuer:    WxIssuer,
		Subject:   openid,
		ID:        wxGenerateJTI(),
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString([]byte(refreshSecret))
	if err != nil {
		return nil, err
	}

	// 存储刷新Token
	redisCli := infraRedis.GetClient()
	err = redisCli.Set(context.Background(),
		"wx_refresh_token:"+openid,
		refreshTokenString,
		time.Duration(refreshExpire)*time.Second).Err()
	if err != nil {
		logx.Errorf("存储小程序刷新令牌失败: %v", err)
	}

	return &WxTokenInfo{
		AccessToken:  tokenString,
		RefreshToken: refreshTokenString,
		ExpiresAt:    accessExpireTime.Unix(),
	}, nil
}

// 验证小程序Token
func ParseWxToken(svcCtx *svc.WxServiceContext, tokenString string) (*WxClaims, error) {
	accessSecret := svcCtx.Config.JWT.WxAccessSecret
	if accessSecret == "" {
		accessSecret = "mini_program_default_secret" // 默认密钥
	}

	// 解析Token
	token, err := jwt.ParseWithClaims(tokenString, &WxClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(accessSecret), nil
	})

	if err != nil {
		logx.Errorf("解析Token失败: %v", err)
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, errors.New("令牌已过期")
		}
		return nil, errors.New("无效的令牌")
	}

	if !token.Valid {
		return nil, errors.New("无效的令牌")
	}

	// 类型转换
	if claims, ok := token.Claims.(*WxClaims); ok {
		// 验证发行者和受众
		if claims.Issuer != WxIssuer {
			return nil, errors.New("无效的令牌签发者")
		}
		if !contains(claims.Audience, WxAudience) {
			return nil, errors.New("无效的令牌受众")
		}
		if claims.Type != TokenTypeWx {
			return nil, errors.New("令牌类型错误")
		}

		return claims, nil
	}

	return nil, errors.New("无效的令牌")
}

// RefreshWxToken 刷新小程序Token
func RefreshWxToken(svcCtx *svc.WxServiceContext, refreshToken string) (*WxTokenInfo, error) {
	// 验证刷新Token
	refreshSecret := svcCtx.Config.JWT.WxRefreshSecret
	if refreshSecret == "" {
		refreshSecret = "mini_program_refresh_default_secret"
	}

	// 解析Token并验证签名
	token, err := jwt.ParseWithClaims(refreshToken, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(refreshSecret), nil
	})

	if err != nil {
		// 检查是否是过期错误
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, errors.New("刷新令牌已过期")
		}
		return nil, errors.New("无效的刷新令牌")
	}

	if !token.Valid {
		return nil, errors.New("无效的刷新令牌")
	}

	claims, ok := token.Claims.(*jwt.RegisteredClaims)
	if !ok || claims.Issuer != WxIssuer {
		return nil, errors.New("无效的令牌签发者")
	}

	// 显式检查令牌是否过期
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, errors.New("刷新令牌已过期")
	}

	// 从Redis检查刷新Token
	openid := claims.Subject
	redisCli := infraRedis.GetClient()
	storedToken, err := redisCli.Get(context.Background(),
		"wx_refresh_token:"+openid).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			return nil, errors.New("刷新令牌不存在或已被撤销")
		}
		return nil, errors.New("检查刷新令牌时发生错误")
	}

	if storedToken != refreshToken {
		return nil, errors.New("刷新令牌不匹配或已被撤销")
	}

	// 从数据库查询用户角色
	db := mysql.Master()
	var roles []string
	var wxUserID uint

	// 1. 查询用户ID
	var wxUser model.WxUser
	if err := db.Where("open_id = ?", openid).First(&wxUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, errors.New("查询用户信息失败")
	}

	wxUserID = wxUser.UserID

	// 2. 查询是否有患者身份
	var patient model.WxPatient
	if err := db.Where("user_id = ?", wxUserID).First(&patient).Error; err == nil {
		roles = append(roles, "patient")
	}

	// 3. 查询是否有医生身份
	var doctor model.WxDoctor
	if err := db.Where("user_id = ?", wxUserID).First(&doctor).Error; err == nil {
		roles = append(roles, "doctor")
	}

	// 4. 查询是否有核销员身份
	var redeemer model.WxRedeemer
	if err := db.Where("user_id = ?", wxUserID).First(&redeemer).Error; err == nil {
		roles = append(roles, "redeemer")
	}

	// 如果没有任何角色，使用默认角色"patient"
	if len(roles) == 0 {
		roles = append(roles, "patient")
	}

	// 生成新Token
	return GenerateWxToken(svcCtx, openid, roles)
}

// generateJTI 生成JWT ID，用于防止重放攻击
func wxGenerateJTI() string {
	// 使用加密安全的随机数生成器
	b := make([]byte, 16)
	rand.Read(b)
	// 使用URL安全的Base64编码
	return base64.RawURLEncoding.EncodeToString(b)
}

// 辅助函数：检查切片中是否包含某个字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
