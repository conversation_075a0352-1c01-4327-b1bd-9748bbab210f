package handler

import (
	"net/http"

	"yekaitai/internal/types"
	checkoutModel "yekaitai/pkg/common/model/checkout"
	"yekaitai/wx_internal/modules/checkout/service"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type CheckoutGoZeroHandler struct {
	checkoutService *service.CheckoutService
}

func NewCheckoutGoZeroHandler() *CheckoutGoZeroHandler {
	return &CheckoutGoZeroHandler{
		checkoutService: service.NewCheckoutService(),
	}
}

// Preload 预加载结算数据
func (h *CheckoutGoZeroHandler) Preload(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 解析JSON请求参数
	var req checkoutModel.PreloadRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 调用服务预加载数据
	resp, err := h.checkoutService.Preload(r.Context(), userID, &req)
	if err != nil {
		logx.Errorf("预加载结算数据失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// Recalculate 动态重新计算
func (h *CheckoutGoZeroHandler) Recalculate(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 解析请求参数
	var req checkoutModel.RecalculateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 调用服务重新计算
	resp, err := h.checkoutService.Recalculate(r.Context(), userID, &req)
	if err != nil {
		logx.Errorf("动态重新计算失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}
