package disease

import (
	"time"

	"gorm.io/gorm"
)

// Disease 疾病库表
type Disease struct {
	ID           uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	DiseaseName  string         `gorm:"column:disease_name;type:varchar(255);not null;index;comment:疾病名称" json:"disease_name"`
	Department   string         `gorm:"column:department;type:varchar(100);not null;index;comment:所属科室" json:"department"`
	SubDepartment string        `gorm:"column:sub_department;type:varchar(100);index;comment:子科室" json:"sub_department"`
	Keywords     string         `gorm:"column:keywords;type:text;comment:关键词，用于搜索匹配" json:"keywords"`
	Description  string         `gorm:"column:description;type:text;comment:疾病描述" json:"description"`
	Status       int            `gorm:"column:status;default:1;comment:状态(0-禁用，1-启用)" json:"status"`
	CreatedAt    time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (Disease) TableName() string {
	return "diseases"
}

// DiseaseCreateRequest 创建疾病请求
type DiseaseCreateRequest struct {
	DiseaseName   string `json:"disease_name" validate:"required,max=255"`
	Department    string `json:"department" validate:"required,max=100"`
	SubDepartment string `json:"sub_department,optional" validate:"max=100"`
	Keywords      string `json:"keywords,optional"`
	Description   string `json:"description,optional"`
	Status        int    `json:"status,optional" validate:"oneof=0 1"`
}

// DiseaseUpdateRequest 更新疾病请求
type DiseaseUpdateRequest struct {
	ID            uint   `json:"id" validate:"required"`
	DiseaseName   string `json:"disease_name" validate:"required,max=255"`
	Department    string `json:"department" validate:"required,max=100"`
	SubDepartment string `json:"sub_department,optional" validate:"max=100"`
	Keywords      string `json:"keywords,optional"`
	Description   string `json:"description,optional"`
	Status        int    `json:"status,optional" validate:"oneof=0 1"`
}

// DiseaseQueryParams 疾病查询参数
type DiseaseQueryParams struct {
	Page          int    `json:"page" validate:"min=1"`
	PageSize      int    `json:"page_size" validate:"min=1,max=100"`
	DiseaseName   string `json:"disease_name,optional"`
	Department    string `json:"department,optional"`
	SubDepartment string `json:"sub_department,optional"`
	Keyword       string `json:"keyword,optional"`
	Status        *int   `json:"status,optional"`
}

// DiseaseListResponse 疾病列表响应
type DiseaseListResponse struct {
	List  []*Disease `json:"list"`
	Total int64      `json:"total"`
	Page  int        `json:"page"`
	Size  int        `json:"size"`
}
