package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"yekaitai/internal/modules/appointment/types"
	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// AbcYunChargeQueryByDateHandler 查询指定日期的收费单
func AbcYunChargeQueryByDateHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.QueryByDateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/charge/query-by-date"
	queryParams := map[string]string{
		"date": req.Date,
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                             `json:"code"`
		Message string                          `json:"message"`
		Data    types.ChargeSheetByDateResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询指定日期的收费单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunChargeQueryDetailHandler 获取收费单明细
func AbcYunChargeQueryDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ChargeSheetDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("收费单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/charge/query-detail/%s", req.ID)

	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                             `json:"code"`
		Message string                          `json:"message"`
		Data    types.ChargeSheetDetailResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取收费单明细成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunChargePayHandler 收费单付款（支持部分收费）
func AbcYunChargePayHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 获取chargeSheetId路径参数
	idStr := r.URL.Path
	parts := strings.Split(idStr, "/")
	chargeSheetId := ""
	// 路径格式为 /api/abcyun/charge/:chargeSheetId/pay
	for i, part := range parts {
		if part == "charge" && i < len(parts)-2 {
			chargeSheetId = parts[i+1]
			break
		}
	}
	if chargeSheetId == "" {
		httpx.Error(w, errors.New("收费单ID不能为空"))
		return
	}

	var req types.ChargePayRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PayMode == 0 {
		httpx.Error(w, errors.New("支付方式不能为空"))
		return
	}
	if req.OperatorID == "" {
		httpx.Error(w, errors.New("操作人ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/charge/%s/pay", chargeSheetId)

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                     `json:"code"`
		Message string                  `json:"message"`
		Data    types.ChargePayResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "收费单付款成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunChargeRefundHandler 收费单退款（支持部分退）
func AbcYunChargeRefundHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 获取chargeSheetId路径参数
	idStr := r.URL.Path
	parts := strings.Split(idStr, "/")
	chargeSheetId := ""
	// 路径格式为 /api/abcyun/charge/:chargeSheetId/refund
	for i, part := range parts {
		if part == "charge" && i < len(parts)-2 {
			chargeSheetId = parts[i+1]
			break
		}
	}
	if chargeSheetId == "" {
		httpx.Error(w, errors.New("收费单ID不能为空"))
		return
	}

	var req types.ChargeRefundRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.OperatorID == "" {
		httpx.Error(w, errors.New("操作人ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/charge/%s/refund", chargeSheetId)

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                        `json:"code"`
		Message string                     `json:"message"`
		Data    types.ChargeRefundResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "收费单退款成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunChargeDeliveryHandler 设置或修改收费单快递信息
func AbcYunChargeDeliveryHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 获取chargeSheetId路径参数
	idStr := r.URL.Path
	parts := strings.Split(idStr, "/")
	chargeSheetId := ""
	// 路径格式为 /api/abcyun/charge/:chargeSheetId/delivery
	for i, part := range parts {
		if part == "charge" && i < len(parts)-2 {
			chargeSheetId = parts[i+1]
			break
		}
	}
	if chargeSheetId == "" {
		httpx.Error(w, errors.New("收费单ID不能为空"))
		return
	}

	var req types.ChargeDeliveryRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.OperatorID == "" {
		httpx.Error(w, errors.New("操作人ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/charge/%s/delivery", chargeSheetId)

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                             `json:"code"`
		Message string                          `json:"message"`
		Data    types.ChargeSheetDetailResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "设置或修改收费单快递信息成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunChargeByPatientOrderIDHandler 通过就诊单ID查询收费单
func AbcYunChargeByPatientOrderIDHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ChargeByPatientOrderIDRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PatientOrderID == "" {
		httpx.Error(w, errors.New("就诊单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/charge/by-patient-order-id/%s", req.PatientOrderID)

	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                                  `json:"code"`
		Message string                               `json:"message"`
		Data    types.ChargeByPatientOrderIDResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "通过就诊单ID查询收费单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunChargeByPatientHandler 查询患者的收费单
func AbcYunChargeByPatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ChargeByPatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PatientID == "" {
		httpx.Error(w, errors.New("患者ID不能为空"))
		return
	}

	// 设置默认值
	if req.BeginDate == "" {
		// 默认3个月前
		req.BeginDate = time.Now().AddDate(0, -3, 0).Format("2006-01-02")
	}
	if req.EndDate == "" {
		// 默认当前日期
		req.EndDate = time.Now().Format("2006-01-02")
	}

	// 限制分页大小
	if req.Limit > 20 {
		req.Limit = 20
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/charge/patient/%s", req.PatientID)

	queryParams := map[string]string{
		"beginDate": req.BeginDate,
		"endDate":   req.EndDate,
		"limit":     strconv.Itoa(req.Limit),
		"offset":    strconv.Itoa(req.Offset),
	}

	if req.Status != "" {
		queryParams["status"] = req.Status
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                           `json:"code"`
		Message string                        `json:"message"`
		Data    types.ChargeByPatientResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询患者的收费单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunChargeCancelDeliveryHandler 取消收费单快递费
func AbcYunChargeCancelDeliveryHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 获取chargeSheetId路径参数
	idStr := r.URL.Path
	parts := strings.Split(idStr, "/")
	chargeSheetId := ""
	// 路径格式为 /api/abcyun/charge/:chargeSheetId/delivery/cancel
	for i, part := range parts {
		if part == "charge" && i < len(parts)-2 {
			chargeSheetId = parts[i+1]
			break
		}
	}
	if chargeSheetId == "" {
		httpx.Error(w, errors.New("收费单ID不能为空"))
		return
	}

	var req types.ChargeCancelDeliveryRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.OperatorID == "" {
		httpx.Error(w, errors.New("操作人ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/charge/%s/delivery/cancel", chargeSheetId)

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                             `json:"code"`
		Message string                          `json:"message"`
		Data    types.ChargeSheetDetailResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "取消收费单快递费成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}
