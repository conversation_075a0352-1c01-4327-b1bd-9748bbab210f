package middleware

import (
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// LogMiddleware 日志中间件
func LogMiddleware() func(http.HandlerFunc) http.HandlerFunc {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			logx.Infof("请求开始: %s %s", r.Method, r.URL.Path)

			next(w, r)

			// 记录请求耗时
			duration := time.Since(start)
			logx.Infof("请求结束: %s %s, 耗时: %v", r.Method, r.URL.Path, duration)
		}
	}
}
