package tasks

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	orderModel "yekaitai/pkg/common/model/order"
	"yekaitai/pkg/infra/mysql"
	wanliniuService "yekaitai/wx_internal/modules/wanliniu/service"
)

// WanLiNiuFailedPushService 万里牛失败订单推送服务
type WanLiNiuFailedPushService struct {
	db               *gorm.DB
	orderPushService *wanliniuService.OrderPushService
}

// NewWanLiNiuFailedPushService 创建万里牛失败订单推送服务
func NewWanLiNiuFailedPushService() *WanLiNiuFailedPushService {
	return &WanLiNiuFailedPushService{
		db:               mysql.Master(),
		orderPushService: wanliniuService.NewOrderPushService(),
	}
}

// PushFailedOrders 推送失败的订单
func (s *WanLiNiuFailedPushService) PushFailedOrders() error {
	logx.Info("[WanLiNiu] 开始定时推送失败的订单...")

	// 推送失败的订单
	if err := s.pushFailedOrdersToERP(); err != nil {
		logx.Errorf("[WanLiNiu] 推送失败的订单出错: %v", err)
	}

	// 推送失败的售后单
	if err := s.pushFailedRefundsToERP(); err != nil {
		logx.Errorf("[WanLiNiu] 推送失败的售后单出错: %v", err)
	}

	logx.Info("[WanLiNiu] 失败订单推送任务完成")
	return nil
}

// pushFailedOrdersToERP 推送失败的订单到ERP
func (s *WanLiNiuFailedPushService) pushFailedOrdersToERP() error {
	const batchSize = 100 // 每次最多查询100条

	// 查询需要推送的失败订单：推送状态为失败，且最后推送时间超过2分钟（临时调整，用于快速验证签名修正）
	var orders []orderModel.Order
	err := s.db.Where(`
		wanliniu_order_push_status = ?
		AND (wanliniu_last_push_time IS NULL OR wanliniu_last_push_time < ?)
	`, orderModel.WanLiNiuPushStatusFailed, time.Now().Add(-2*time.Minute)).
		Limit(batchSize).
		Order("created_at ASC").
		Find(&orders).Error

	if err != nil {
		return fmt.Errorf("查询需要推送的失败订单失败: %w", err)
	}

	if len(orders) == 0 {
		logx.Info("[WanLiNiu] 没有需要推送的失败订单")
		return nil
	}

	logx.Infof("[WanLiNiu] 找到 %d 个需要推送的失败订单，开始逐个推送", len(orders))

	successCount := 0
	for i, order := range orders {
		logx.Infof("[WanLiNiu] 推送失败订单 [%d/%d]: %s (第%d次重试)",
			i+1, len(orders), order.OrderNo, order.WanLiNiuOrderPushCount+1)

		ctx := context.Background()
		err := s.orderPushService.PushOrderToERP(ctx, order.OrderNo)

		if err == nil {
			successCount++
			logx.Infof("[WanLiNiu] 订单推送成功: %s", order.OrderNo)
		} else {
			logx.Errorf("[WanLiNiu] 订单推送失败: %s, 错误: %v", order.OrderNo, err)
		}

		// 每推送1单间隔1秒
		if i < len(orders)-1 {
			time.Sleep(1 * time.Second)
		}
	}

	logx.Infof("[WanLiNiu] 失败订单推送完成: 总数=%d, 成功=%d", len(orders), successCount)
	return nil
}

// pushFailedRefundsToERP 推送失败的售后单到ERP
func (s *WanLiNiuFailedPushService) pushFailedRefundsToERP() error {
	const batchSize = 100 // 每次最多查询100条

	// 查询需要推送的失败售后单：推送状态为失败，且最后推送时间超过2分钟（临时调整，用于快速验证签名修正）
	var orders []orderModel.Order
	err := s.db.Where(`
		wanliniu_refund_push_status = ?
		AND refund_status = ?
		AND refund_no IS NOT NULL AND refund_no != ''
		AND (wanliniu_last_push_time IS NULL OR wanliniu_last_push_time < ?)
	`, orderModel.WanLiNiuPushStatusFailed, orderModel.RefundStatusProcessing, time.Now().Add(-2*time.Minute)).
		Limit(batchSize).
		Order("refund_time ASC").
		Find(&orders).Error

	if err != nil {
		return fmt.Errorf("查询需要推送的失败售后单失败: %w", err)
	}

	if len(orders) == 0 {
		logx.Info("[WanLiNiu] 没有需要推送的失败售后单")
		return nil
	}

	logx.Infof("[WanLiNiu] 找到 %d 个需要推送的失败售后单，开始逐个推送", len(orders))

	successCount := 0
	for i, order := range orders {
		logx.Infof("[WanLiNiu] 推送失败售后单 [%d/%d]: 订单=%s, 退款单=%s (第%d次重试)",
			i+1, len(orders), order.OrderNo, order.RefundNo, order.WanLiNiuRefundPushCount+1)

		ctx := context.Background()
		err := s.orderPushService.PushRefundToERP(ctx, order.OrderNo, order.RefundNo, order.RefundReason, order.RefundAmount)

		if err == nil {
			successCount++
			logx.Infof("[WanLiNiu] 售后单推送成功: 订单=%s, 退款单=%s", order.OrderNo, order.RefundNo)
		} else {
			logx.Errorf("[WanLiNiu] 售后单推送失败: 订单=%s, 退款单=%s, 错误: %v",
				order.OrderNo, order.RefundNo, err)
		}

		// 每推送1单间隔1秒
		if i < len(orders)-1 {
			time.Sleep(1 * time.Second)
		}
	}

	logx.Infof("[WanLiNiu] 失败售后单推送完成: 总数=%d, 成功=%d", len(orders), successCount)
	return nil
}
