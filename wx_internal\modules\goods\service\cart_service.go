package service

import (
	"context"
	"fmt"

	"yekaitai/internal/modules/goods/model"
	cartModel "yekaitai/pkg/common/model/cart"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type CartService struct {
	db *gorm.DB
}

func NewCartService() *CartService {
	return &CartService{
		db: mysql.GetDB(),
	}
}

// CartSummary 购物车统计信息
type CartSummary struct {
	TotalCount int     `json:"total_count"`
	TotalPrice float64 `json:"total_price"`
}

// AddToCart 添加商品到购物车
func (s *CartService) AddToCart(ctx context.Context, userID uint, req *cartModel.CartAddRequest) (*cartModel.Cart, error) {
	// 检查商品是否存在
	var goods model.Goods
	if err := s.db.WithContext(ctx).First(&goods, req.GoodsID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商品不存在")
		}
		return nil, fmt.Errorf("查询商品失败: %w", err)
	}

	// 检查商品是否上架
	if goods.Status != 1 || goods.IsOnSale != 1 {
		return nil, fmt.Errorf("商品已下架")
	}

	// 如果有规格ID，检查规格是否存在
	if req.SpecID > 0 {
		var spec model.GoodsSpec
		if err := s.db.WithContext(ctx).Where("id = ? AND goods_id = ?", req.SpecID, req.GoodsID).First(&spec).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, fmt.Errorf("商品规格不存在")
			}
			return nil, fmt.Errorf("查询商品规格失败: %w", err)
		}
	}

	// 检查购物车中是否已存在相同商品和规格
	var existingCart cartModel.Cart
	err := s.db.WithContext(ctx).Where("user_id = ? AND goods_id = ? AND spec_id = ?",
		userID, req.GoodsID, req.SpecID).First(&existingCart).Error

	if err == nil {
		// 已存在，更新数量
		newQuantity := existingCart.Quantity + req.Quantity
		if newQuantity > 99 {
			newQuantity = 99
		}
		existingCart.Quantity = newQuantity
		if err := s.db.WithContext(ctx).Save(&existingCart).Error; err != nil {
			return nil, fmt.Errorf("更新购物车失败: %w", err)
		}
		return &existingCart, nil
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询购物车失败: %w", err)
	}

	// 不存在，创建新的购物车记录
	cart := &cartModel.Cart{
		UserID:   userID,
		GoodsID:  req.GoodsID,
		SpecID:   req.SpecID,
		Quantity: req.Quantity,
	}

	if err := s.db.WithContext(ctx).Create(cart).Error; err != nil {
		return nil, fmt.Errorf("添加购物车失败: %w", err)
	}

	return cart, nil
}

// GetCartList 获取购物车列表
func (s *CartService) GetCartList(ctx context.Context, userID uint) ([]*cartModel.CartResponse, error) {
	var carts []*cartModel.Cart
	if err := s.db.WithContext(ctx).Where("user_id = ?", userID).
		Preload("Goods").Preload("Spec").Find(&carts).Error; err != nil {
		return nil, fmt.Errorf("查询购物车列表失败: %w", err)
	}

	var cartResponses []*cartModel.CartResponse
	for _, cart := range carts {
		// 计算总价
		var price float64
		if cart.Spec != nil {
			price = cart.Spec.SalePrice // 使用规格的标准售价
		} else if cart.Goods != nil {
			price = cart.Goods.TagPrice // 使用商品的吊牌价
		}
		totalPrice := price * float64(cart.Quantity)

		cartResponse := &cartModel.CartResponse{
			ID:         cart.ID,
			UserID:     cart.UserID,
			GoodsID:    cart.GoodsID,
			SpecID:     cart.SpecID,
			Quantity:   cart.Quantity,
			Goods:      cart.Goods,
			Spec:       cart.Spec,
			TotalPrice: totalPrice,
			CreatedAt:  cart.CreatedAt,
			UpdatedAt:  cart.UpdatedAt,
		}
		cartResponses = append(cartResponses, cartResponse)
	}

	return cartResponses, nil
}

// GetCartSummary 获取购物车统计信息
func (s *CartService) GetCartSummary(ctx context.Context, userID uint) (*CartSummary, error) {
	var totalCount int64
	var totalPrice float64

	// 获取购物车列表
	cartList, err := s.GetCartList(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 计算统计信息
	for _, cart := range cartList {
		totalCount += int64(cart.Quantity)
		totalPrice += cart.TotalPrice
	}

	return &CartSummary{
		TotalCount: int(totalCount),
		TotalPrice: totalPrice,
	}, nil
}

// UpdateCartQuantity 更新购物车商品数量
func (s *CartService) UpdateCartQuantity(ctx context.Context, cartID, userID uint, quantity int) error {
	if quantity <= 0 || quantity > 99 {
		return fmt.Errorf("数量必须在1-99之间")
	}

	result := s.db.WithContext(ctx).Model(&cartModel.Cart{}).
		Where("id = ? AND user_id = ?", cartID, userID).
		Update("quantity", quantity)

	if result.Error != nil {
		return fmt.Errorf("更新购物车数量失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("购物车记录不存在")
	}

	return nil
}

// RemoveFromCart 从购物车删除商品
func (s *CartService) RemoveFromCart(ctx context.Context, cartID, userID uint) error {
	result := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", cartID, userID).
		Delete(&cartModel.Cart{})

	if result.Error != nil {
		return fmt.Errorf("删除购物车商品失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("购物车记录不存在")
	}

	return nil
}

// ClearCart 清空购物车
func (s *CartService) ClearCart(ctx context.Context, userID uint) error {
	if err := s.db.WithContext(ctx).Where("user_id = ?", userID).
		Delete(&cartModel.Cart{}).Error; err != nil {
		return fmt.Errorf("清空购物车失败: %w", err)
	}

	return nil
}

// GetCartCount 获取购物车商品数量
func (s *CartService) GetCartCount(ctx context.Context, userID uint) (int, error) {
	var totalCount int64
	if err := s.db.WithContext(ctx).Model(&cartModel.Cart{}).
		Where("user_id = ?", userID).
		Select("SUM(quantity)").Scan(&totalCount).Error; err != nil {
		return 0, fmt.Errorf("获取购物车数量失败: %w", err)
	}

	return int(totalCount), nil
}

// BatchRemoveFromCart 批量删除购物车商品
func (s *CartService) BatchRemoveFromCart(ctx context.Context, cartIDs []uint, userID uint) error {
	if len(cartIDs) == 0 {
		return fmt.Errorf("没有指定要删除的购物车项")
	}

	result := s.db.WithContext(ctx).Where("id IN ? AND user_id = ?", cartIDs, userID).
		Delete(&cartModel.Cart{})

	if result.Error != nil {
		return fmt.Errorf("批量删除购物车商品失败: %w", result.Error)
	}

	return nil
}

// GetCartByGoodsAndSpec 根据商品和规格获取购物车记录
func (s *CartService) GetCartByGoodsAndSpec(ctx context.Context, userID, goodsID, specID uint) (*cartModel.Cart, error) {
	var cart cartModel.Cart
	err := s.db.WithContext(ctx).Where("user_id = ? AND goods_id = ? AND spec_id = ?",
		userID, goodsID, specID).First(&cart).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询购物车记录失败: %w", err)
	}

	return &cart, nil
}

// UpdateCartSpec 更新购物车商品规格
func (s *CartService) UpdateCartSpec(ctx context.Context, cartID, userID, specID uint) error {
	// 查找购物车商品
	var cart cartModel.Cart
	if err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", cartID, userID).First(&cart).Error; err != nil {
		return err
	}

	// 验证新规格是否属于该商品
	var spec struct {
		ID      uint `json:"id"`
		GoodsID uint `json:"goods_id"`
		Stock   int  `json:"stock"`
	}
	if err := s.db.WithContext(ctx).Table("goods_spec").
		Select("id, goods_id, stock").
		Where("id = ? AND goods_id = ?", specID, cart.GoodsID).
		First(&spec).Error; err != nil {
		return fmt.Errorf("商品规格不存在")
	}

	// 检查库存
	if spec.Stock < cart.Quantity {
		return fmt.Errorf("该规格库存不足")
	}

	// 更新规格
	cart.SpecID = specID
	if err := s.db.WithContext(ctx).Save(&cart).Error; err != nil {
		return fmt.Errorf("更新商品规格失败: %w", err)
	}

	return nil
}
