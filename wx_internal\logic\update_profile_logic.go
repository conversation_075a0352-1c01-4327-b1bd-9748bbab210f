package logic

import (
	"context"
	"fmt"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/svc"
	"yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateProfileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

func NewUpdateProfileLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *UpdateProfileLogic {
	return &UpdateProfileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// UpdateProfile 更新用户资料（头像和昵称）
func (l *UpdateProfileLogic) UpdateProfile(req *types.UpdateProfileReq) (resp *types.UpdateProfileResp, err error) {
	// 1. 从上下文获取openId
	openIdVal := l.ctx.Value(middleware.OpenIDKey)
	if openIdVal == nil {
		return nil, fmt.Errorf("openid不存在")
	}

	openId, ok := openIdVal.(string)
	if !ok || openId == "" {
		return nil, fmt.Errorf("无效的openid")
	}

	logx.Infof("更新用户资料, openId: %s, avatar: %s, nickName: %s", openId, req.Avatar, req.NickName)

	// 2. 更新用户资料
	err = l.svcCtx.UserService.UpdateUserProfile(l.ctx, openId, req.Avatar, req.NickName)
	if err != nil {
		logx.Errorf("更新用户资料失败: %v", err)
		return nil, err
	}

	return &types.UpdateProfileResp{
		Success: true,
	}, nil
}
