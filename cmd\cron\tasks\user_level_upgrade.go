package tasks

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/pkg/infra/mysql"
	userService "yekaitai/wx_internal/modules/user/service"
)

// UserLevelUpgradeService 用户等级升级定时任务服务
type UserLevelUpgradeService struct {
	db                     *gorm.DB
	userConsumptionService *userService.UserConsumptionService
}

// NewUserLevelUpgradeService 创建用户等级升级服务
func NewUserLevelUpgradeService() *UserLevelUpgradeService {
	return &UserLevelUpgradeService{
		db:                     mysql.GetDB(),
		userConsumptionService: userService.NewUserConsumptionService(),
	}
}

// ProcessUserLevelUpgrades 处理用户等级升级
func (s *UserLevelUpgradeService) ProcessUserLevelUpgrades() error {
	ctx := context.Background()
	logx.Info("开始执行用户等级升级定时任务...")

	// 查询所有需要检查等级升级的用户
	// 这里我们查询所有有消费记录但可能需要升级的用户
	var userIDs []uint
	err := s.db.WithContext(ctx).Raw(`
		SELECT DISTINCT u.user_id
		FROM wx_user u
		LEFT JOIN user_level_rules ulr ON u.user_level = ulr.level_order
		WHERE u.user_id IS NOT NULL
		AND (
			-- 用户还没有等级，需要检查是否满足LV1条件
			u.user_level IS NULL OR u.user_level = 0
			OR
			-- 用户有消费记录，需要检查是否满足更高等级条件
			EXISTS (
				SELECT 1 FROM wx_user_annual_consumption wuac
				WHERE wuac.user_id = u.user_id
				AND wuac.year = YEAR(NOW())
				AND wuac.total_amount > 0
				AND wuac.updated_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
			)
		)
		LIMIT 1000
	`).Scan(&userIDs).Error

	if err != nil {
		logx.Errorf("查询需要升级的用户失败: %v", err)
		return fmt.Errorf("查询需要升级的用户失败: %w", err)
	}

	if len(userIDs) == 0 {
		logx.Info("没有需要检查等级升级的用户")
		return nil
	}

	logx.Infof("找到 %d 个用户需要检查等级升级", len(userIDs))

	// 统计升级结果
	var successCount, failCount int

	// 批量处理用户等级升级
	for _, userID := range userIDs {
		if err := s.userConsumptionService.TriggerUserLevelUpdate(userID); err != nil {
			logx.Errorf("用户等级升级失败: userID=%d, error=%v", userID, err)
			failCount++
		} else {
			successCount++
		}

		// 避免过于频繁的数据库操作，每处理10个用户休息10毫秒
		if successCount%10 == 0 {
			time.Sleep(10 * time.Millisecond)
		}
	}

	logx.Infof("用户等级升级定时任务完成: 成功=%d, 失败=%d, 总计=%d",
		successCount, failCount, len(userIDs))

	return nil
}

// ProcessConsumptionBasedUpgrades 处理基于消费金额的等级升级
func (s *UserLevelUpgradeService) ProcessConsumptionBasedUpgrades() error {
	ctx := context.Background()
	logx.Info("开始执行基于消费金额的用户等级升级...")

	// 查询所有等级规则，按等级顺序排序
	var levelRules []struct {
		ID                 uint    `json:"id"`
		LevelName          string  `json:"level_name"`
		LevelOrder         int     `json:"level_order"`
		RequireConsumption bool    `json:"require_consumption"`
		ConsumptionAmount  float64 `json:"consumption_amount"`
	}

	err := s.db.WithContext(ctx).Table("user_level_rules").
		Select("id, level_name, level_order, require_consumption, consumption_amount").
		Where("require_consumption = ? AND consumption_amount > 0", true).
		Order("level_order ASC").
		Find(&levelRules).Error

	if err != nil {
		logx.Errorf("查询等级规则失败: %v", err)
		return fmt.Errorf("查询等级规则失败: %w", err)
	}

	if len(levelRules) == 0 {
		logx.Info("没有基于消费金额的等级规则")
		return nil
	}

	logx.Infof("找到 %d 个基于消费金额的等级规则", len(levelRules))

	// 统计升级结果
	var totalUpgraded int

	// 为每个等级规则查找符合条件的用户
	for _, rule := range levelRules {
		logx.Infof("检查等级 %s (order=%d, 消费要求=%.2f)", rule.LevelName, rule.LevelOrder, rule.ConsumptionAmount)

		// 查询累积消费达到要求但等级低于当前规则的用户
		var eligibleUsers []struct {
			UserID           uint    `json:"user_id"`
			CurrentLevel     int     `json:"current_level"`
			TotalConsumption float64 `json:"total_consumption"`
		}

		err := s.db.WithContext(ctx).Raw(`
			SELECT
				u.user_id,
				COALESCE(u.user_level, 0) as current_level,
				COALESCE(wuac.total_amount, 0) as total_consumption
			FROM wx_user u
			LEFT JOIN wx_user_annual_consumption wuac ON u.user_id = wuac.user_id AND wuac.year = YEAR(NOW())
			WHERE u.user_id IS NOT NULL
			AND COALESCE(wuac.total_amount, 0) >= ?
			AND (COALESCE(u.user_level, 0) < ?)
			LIMIT 100
		`, rule.ConsumptionAmount, rule.LevelOrder).Scan(&eligibleUsers).Error

		if err != nil {
			logx.Errorf("查询符合等级 %s 条件的用户失败: %v", rule.LevelName, err)
			continue
		}

		if len(eligibleUsers) == 0 {
			logx.Infof("没有用户符合等级 %s 的升级条件", rule.LevelName)
			continue
		}

		logx.Infof("找到 %d 个用户符合等级 %s 的升级条件", len(eligibleUsers), rule.LevelName)

		// 升级符合条件的用户
		for _, user := range eligibleUsers {
			err := s.db.WithContext(ctx).Model(&struct{}{}).Table("wx_user").
				Where("user_id = ?", user.UserID).
				Update("user_level", rule.LevelOrder).Error

			if err != nil {
				logx.Errorf("升级用户等级失败: userID=%d, targetLevel=%d, error=%v",
					user.UserID, rule.LevelOrder, err)
			} else {
				logx.Infof("用户等级升级成功: userID=%d, %d->%d, 累积消费=%.2f",
					user.UserID, user.CurrentLevel, rule.LevelOrder, user.TotalConsumption)
				totalUpgraded++
			}
		}

		// 避免过于频繁的数据库操作
		time.Sleep(100 * time.Millisecond)
	}

	logx.Infof("基于消费金额的用户等级升级完成: 总计升级 %d 个用户", totalUpgraded)
	return nil
}

// RunUpgradeTask 运行升级任务（定时任务入口）
func (s *UserLevelUpgradeService) RunUpgradeTask() {
	logx.Info("===== 开始用户等级升级定时任务 =====")

	// 1. 处理常规等级升级检查
	if err := s.ProcessUserLevelUpgrades(); err != nil {
		logx.Errorf("常规等级升级检查失败: %v", err)
	}

	// 2. 处理基于消费金额的等级升级
	if err := s.ProcessConsumptionBasedUpgrades(); err != nil {
		logx.Errorf("基于消费金额的等级升级失败: %v", err)
	}

	logx.Info("===== 用户等级升级定时任务完成 =====")
}
