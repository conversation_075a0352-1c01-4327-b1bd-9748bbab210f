package his

import (
	"context"
	"fmt"
	"strconv"
	"time"
	"yekaitai/pkg/adapters/chongqing"
	"yekaitai/pkg/utils/constants"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"
)

// ChongQingClient 重庆HIS客户端适配器
type ChongQingClient struct {
	client *chongqing.Client
}

// NewChongQingClient 创建重庆HIS客户端适配器
func NewChongQingClient(client *chongqing.Client) *ChongQingClient {
	return &ChongQingClient{
		client: client,
	}
}

// GetProviderType 获取提供商类型
func (c *ChongQingClient) GetProviderType() ProviderType {
	return ProviderChongQing
}

// GetClinicList 获取诊所列表
func (c *ChongQingClient) GetClinicList(ctx context.Context) ([]string, error) {
	// 获取诊所列表
	clinics, err := c.client.GetHospitalList(ctx)
	if err != nil {
		logx.Error("获取重庆HIS诊所列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取诊所列表失败: %w", err)
	}

	// 提取诊所ID列表
	result := make([]string, 0, len(clinics))
	for _, clinic := range clinics {
		result = append(result, clinic.HospitalID)
	}

	return result, nil
}

// GetDepartments 获取科室列表
func (c *ChongQingClient) GetDepartments(ctx context.Context, clinicID string) ([]DepartmentInfo, error) {
	// 获取科室列表
	departments, err := c.client.GetDepartmentList(ctx, clinicID)
	if err != nil {
		logx.Error("获取重庆HIS科室列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取科室列表失败: %w", err)
	}

	// 转换为通用科室信息
	result := make([]DepartmentInfo, 0, len(departments))
	for _, dept := range departments {
		result = append(result, DepartmentInfo{
			ID:          dept.DepartmentID,
			Name:        dept.DepartmentName,
			Description: dept.Description,
			Status:      convertDepartmentStatus(dept.Status),
			ExternalID:  dept.DepartmentID,
			ExternalKey: string(ProviderChongQing),
		})
	}

	return result, nil
}

// 转换科室状态
func convertDepartmentStatus(status string) int {
	switch status {
	case "normal":
		return constants.DepartmentStatusActive // 正常
	case "disabled":
		return constants.DepartmentStatusInactive // 停用
	default:
		return constants.DepartmentStatusActive // 默认为正常
	}
}

// GetDoctors 获取医生列表
func (c *ChongQingClient) GetDoctors(ctx context.Context, clinicID, departmentID string, date time.Time) ([]DoctorInfo, error) {
	// 日期格式化
	dateStr := date.Format("2006-01-02")

	// 获取医生列表
	doctors, err := c.client.GetDoctorList(ctx, clinicID, departmentID, dateStr)
	if err != nil {
		logx.Error("获取重庆HIS医生列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取医生列表失败: %w", err)
	}

	// 转换为通用医生信息
	result := make([]DoctorInfo, 0, len(doctors))
	for _, doc := range doctors {
		result = append(result, DoctorInfo{
			ID:             doc.DoctorID,
			Name:           doc.DoctorName,
			Title:          doc.Title,
			DepartmentID:   departmentID,
			DepartmentName: doc.DepartmentName,
			Introduction:   doc.Introduction,
			Status:         convertDoctorStatus(doc.Status),
			ExternalID:     doc.DoctorID,
			ExternalKey:    string(ProviderChongQing),
		})
	}

	return result, nil
}

// 转换医生状态
func convertDoctorStatus(status string) int {
	switch status {
	case "normal":
		return constants.DoctorStatusActive // 正常
	case "disabled":
		return constants.DoctorStatusInactive // 停用
	default:
		return constants.DoctorStatusActive // 默认为正常
	}
}

// GetSchedules 获取排班信息
func (c *ChongQingClient) GetSchedules(ctx context.Context, clinicID, departmentID, doctorID string, date time.Time) ([]ScheduleInfo, error) {
	// 日期格式化
	dateStr := date.Format("2006-01-02")

	// 获取排班信息
	schedules, err := c.client.GetDoctorSchedules(ctx, clinicID, doctorID, dateStr)
	if err != nil {
		logx.Error("获取重庆HIS排班信息失败", zap.Error(err))
		return nil, fmt.Errorf("获取排班信息失败: %w", err)
	}

	// 转换为通用排班信息
	result := make([]ScheduleInfo, 0, len(schedules))
	for _, sch := range schedules {
		// 解析时间
		startTime, err := time.Parse("2006-01-02 15:04:05", sch.StartTime)
		if err != nil {
			logx.Error("解析开始时间失败", zap.String("time", sch.StartTime), zap.Error(err))
			continue
		}

		endTime, err := time.Parse("2006-01-02 15:04:05", sch.EndTime)
		if err != nil {
			logx.Error("解析结束时间失败", zap.String("time", sch.EndTime), zap.Error(err))
			continue
		}

		// 解析挂号费
		registerFee, err := strconv.ParseFloat(sch.RegisterFee, 64)
		if err != nil {
			registerFee = 0
		}

		// 转换状态
		status := constants.ScheduleStatusNormal // 默认为正常
		if sch.Status == "disabled" {
			status = constants.ScheduleStatusInactive // 停诊
		} else if sch.Reserved >= sch.Quota {
			status = constants.ScheduleStatusFull // 已满
		}

		result = append(result, ScheduleInfo{
			ID:             sch.ScheduleID,
			DoctorID:       doctorID,
			DoctorName:     sch.DoctorName,
			DepartmentID:   departmentID,
			DepartmentName: sch.DepartmentName,
			Date:           startTime.Truncate(24 * time.Hour),
			StartTime:      startTime,
			EndTime:        endTime,
			Quota:          sch.Quota,
			Reserved:       sch.Reserved,
			RegisterFee:    registerFee,
			Status:         status,
			ExternalID:     sch.ScheduleID,
			ExternalKey:    string(ProviderChongQing),
		})
	}

	return result, nil
}

// 转换性别
func genderStringToInt(gender string) int {
	switch gender {
	case "male":
		return 1 // 男
	case "female":
		return 2 // 女
	default:
		return 0 // 未知
	}
}

// 转换性别
func genderIntToString(gender int) string {
	switch gender {
	case 1:
		return "male"
	case 2:
		return "female"
	default:
		return "unknown"
	}
}

// 转换证件类型
func idCardTypeIntToString(idCardType int) string {
	switch idCardType {
	case 1:
		return "idcard" // 身份证
	case 2:
		return "passport" // 护照
	case 3:
		return "militarycard" // 军官证
	default:
		return "idcard" // 默认身份证
	}
}

// 转换证件类型
func idCardTypeStringToInt(idCardType string) int {
	switch idCardType {
	case "idcard":
		return 1 // 身份证
	case "passport":
		return 2 // 护照
	case "militarycard":
		return 3 // 军官证
	default:
		return 1 // 默认身份证
	}
}

// CreatePatient 创建患者信息
func (c *ChongQingClient) CreatePatient(ctx context.Context, clinicID string, patient *PatientInfo) (*PatientInfo, error) {
	// 转换性别
	gender := genderIntToString(patient.Gender)

	// 转换为重庆HIS的请求格式
	req := chongqing.CreatePatientRequest{
		HospitalID: clinicID,
		Name:       patient.Name,
		Gender:     gender,
		Birthday:   patient.Birthday.Format("2006-01-02"),
		IDCardType: idCardTypeIntToString(patient.IDCardType),
		IDCardNo:   patient.IDCardNo,
		Phone:      patient.Phone,
		Address:    patient.Address,
	}

	// 创建患者
	resp, err := c.client.CreatePatient(ctx, req)
	if err != nil {
		logx.Error("创建患者失败", zap.Error(err))
		return nil, fmt.Errorf("创建患者失败: %w", err)
	}

	// 复制返回的患者信息
	result := *patient
	result.ID = resp.PatientID
	result.ExternalID = resp.PatientID
	result.ExternalKey = string(ProviderChongQing)

	return &result, nil
}

// GetPatients 获取患者列表
func (c *ChongQingClient) GetPatients(ctx context.Context, clinicID, nameQuery string) ([]PatientInfo, error) {
	// 获取患者列表
	patients, err := c.client.GetPatientList(ctx, clinicID, nameQuery)
	if err != nil {
		logx.Error("获取患者列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取患者列表失败: %w", err)
	}

	// 转换为通用患者信息
	result := make([]PatientInfo, 0, len(patients))
	for _, p := range patients {
		// 解析生日
		birthday, err := time.Parse("2006-01-02", p.Birthday)
		if err != nil {
			logx.Error("解析生日失败", zap.String("birthday", p.Birthday), zap.Error(err))
			birthday = time.Now() // 默认值
		}

		result = append(result, PatientInfo{
			ID:          p.PatientID,
			Name:        p.Name,
			Gender:      genderStringToInt(p.Gender),
			Birthday:    birthday,
			IDCardType:  idCardTypeStringToInt(p.IDCardType),
			IDCardNo:    p.IDCardNo,
			Phone:       p.Phone,
			Address:     p.Address,
			ExternalID:  p.PatientID,
			ExternalKey: string(ProviderChongQing),
		})
	}

	return result, nil
}

// GetPatient 获取患者信息
func (c *ChongQingClient) GetPatient(ctx context.Context, clinicID, patientID string) (*PatientInfo, error) {
	// 获取患者信息
	p, err := c.client.GetPatient(ctx, clinicID, patientID)
	if err != nil {
		logx.Error("获取患者信息失败", zap.Error(err))
		return nil, fmt.Errorf("获取患者信息失败: %w", err)
	}

	// 解析生日
	birthday, err := time.Parse("2006-01-02", p.Birthday)
	if err != nil {
		logx.Error("解析生日失败", zap.String("birthday", p.Birthday), zap.Error(err))
		birthday = time.Now() // 默认值
	}

	// 创建返回结果
	result := &PatientInfo{
		ID:          p.PatientID,
		Name:        p.Name,
		Gender:      genderStringToInt(p.Gender),
		Birthday:    birthday,
		IDCardType:  idCardTypeStringToInt(p.IDCardType),
		IDCardNo:    p.IDCardNo,
		Phone:       p.Phone,
		Address:     p.Address,
		ExternalID:  p.PatientID,
		ExternalKey: string(ProviderChongQing),
	}

	return result, nil
}

// CreateAppointment 创建预约
func (c *ChongQingClient) CreateAppointment(ctx context.Context, clinicID string, appointment *AppointmentInfo) (*AppointmentInfo, error) {
	// 转换为重庆HIS的请求格式
	req := chongqing.CreateAppointmentRequest{
		HospitalID:   clinicID,
		PatientID:    appointment.PatientID,
		ScheduleID:   appointment.ScheduleID,
		DoctorID:     appointment.DoctorID,
		DepartmentID: appointment.DepartmentID,
	}

	// 创建预约
	resp, err := c.client.CreateAppointment(ctx, req)
	if err != nil {
		logx.Error("创建预约失败", zap.Error(err))
		return nil, fmt.Errorf("创建预约失败: %w", err)
	}

	// 复制返回的预约信息
	result := *appointment
	result.ID = resp.AppointmentID
	result.Status = convertAppointmentStatus(resp.Status)
	result.ExternalID = resp.AppointmentID
	result.ExternalKey = string(ProviderChongQing)

	return &result, nil
}

// 转换预约状态
func convertAppointmentStatus(status string) int {
	switch status {
	case "pending":
		return constants.AppointmentStatusPending // 待支付
	case "paid":
		return constants.AppointmentStatusPaid // 已支付
	case "completed":
		return constants.AppointmentStatusCompleted // 已完成
	case "cancelled":
		return constants.AppointmentStatusCancelled // 已取消
	default:
		return constants.AppointmentStatusPending // 默认为待支付
	}
}

// CancelAppointment 取消预约
func (c *ChongQingClient) CancelAppointment(ctx context.Context, clinicID, appointmentID, reason string) error {
	// 调用重庆HIS取消预约接口
	err := c.client.CancelAppointment(ctx, clinicID, appointmentID, reason)
	if err != nil {
		logx.Error("取消预约失败", zap.Error(err))
		return fmt.Errorf("取消预约失败: %w", err)
	}
	return nil
}

// GetAppointments 获取预约列表
func (c *ChongQingClient) GetAppointments(ctx context.Context, clinicID, patientID string, startDate, endDate time.Time, status int) ([]AppointmentInfo, error) {
	// 日期格式化
	startDateStr := startDate.Format("2006-01-02")
	endDateStr := endDate.Format("2006-01-02")

	// 转换状态
	statusStr := ""
	switch status {
	case constants.AppointmentStatusPending:
		statusStr = "pending"
	case constants.AppointmentStatusPaid:
		statusStr = "paid"
	case constants.AppointmentStatusCompleted:
		statusStr = "completed"
	case constants.AppointmentStatusCancelled:
		statusStr = "cancelled"
	}

	// 获取预约列表
	appointments, err := c.client.GetAppointmentList(ctx, clinicID, patientID, statusStr, startDateStr, endDateStr)
	if err != nil {
		logx.Error("获取预约列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取预约列表失败: %w", err)
	}

	// 转换为通用预约信息
	result := make([]AppointmentInfo, 0, len(appointments))
	for _, appt := range appointments {
		// 解析预约时间
		appointTime, err := time.Parse("2006-01-02 15:04:05", appt.AppointmentTime)
		if err != nil {
			logx.Error("解析预约时间失败", zap.String("time", appt.AppointmentTime), zap.Error(err))
			continue
		}

		// 解析挂号费
		registerFee, err := strconv.ParseFloat(appt.RegisterFee, 64)
		if err != nil {
			registerFee = 0
		}

		result = append(result, AppointmentInfo{
			ID:             appt.AppointmentID,
			PatientID:      appt.PatientID,
			PatientName:    appt.PatientName,
			DoctorID:       appt.DoctorID,
			DoctorName:     appt.DoctorName,
			DepartmentID:   appt.DepartmentID,
			DepartmentName: appt.DepartmentName,
			ScheduleID:     appt.ScheduleID,
			AppointTime:    appointTime,
			RegisterFee:    registerFee,
			Status:         convertAppointmentStatus(appt.Status),
			ExternalID:     appt.AppointmentID,
			ExternalKey:    string(ProviderChongQing),
		})
	}

	return result, nil
}

// SyncDepartments 同步科室信息
func (c *ChongQingClient) SyncDepartments(ctx context.Context, clinicID string) error {
	// 实际实现中，我们只需要获取科室列表，系统会处理同步
	_, err := c.GetDepartments(ctx, clinicID)
	if err != nil {
		logx.Error("同步科室信息失败", zap.Error(err))
		return fmt.Errorf("同步科室信息失败: %w", err)
	}
	return nil
}

// SyncDoctors 同步医生信息
func (c *ChongQingClient) SyncDoctors(ctx context.Context, clinicID string, date time.Time) error {
	// 获取科室列表
	departments, err := c.GetDepartments(ctx, clinicID)
	if err != nil {
		logx.Error("获取科室列表失败", zap.Error(err))
		return fmt.Errorf("同步医生信息失败: %w", err)
	}

	// 遍历科室，同步每个科室的医生
	for _, dept := range departments {
		_, err := c.GetDoctors(ctx, clinicID, dept.ID, date)
		if err != nil {
			logx.Error("获取医生列表失败",
				zap.String("clinicID", clinicID),
				zap.String("departmentID", dept.ID),
				zap.Error(err))
			continue
		}
	}

	return nil
}

// SyncSchedules 同步排班信息
func (c *ChongQingClient) SyncSchedules(ctx context.Context, clinicID string, startDate, endDate time.Time) error {
	// 获取科室列表
	departments, err := c.GetDepartments(ctx, clinicID)
	if err != nil {
		logx.Error("获取科室列表失败", zap.Error(err))
		return fmt.Errorf("同步排班信息失败: %w", err)
	}

	// 计算需要同步的天数
	days := int(endDate.Sub(startDate).Hours() / 24)
	if days < 1 {
		days = 1
	}

	// 遍历科室
	for _, dept := range departments {
		// 获取该科室的医生
		doctors, err := c.GetDoctors(ctx, clinicID, dept.ID, startDate)
		if err != nil {
			logx.Error("获取医生列表失败",
				zap.String("clinicID", clinicID),
				zap.String("departmentID", dept.ID),
				zap.Error(err))
			continue
		}

		// 遍历医生
		for _, doc := range doctors {
			// 遍历日期，获取每天的排班
			currentDate := startDate
			for i := 0; i <= days; i++ {
				_, err := c.GetSchedules(ctx, clinicID, dept.ID, doc.ID, currentDate)
				if err != nil {
					logx.Error("获取排班信息失败",
						zap.String("clinicID", clinicID),
						zap.String("doctorID", doc.ID),
						zap.Time("date", currentDate),
						zap.Error(err))
				}
				currentDate = currentDate.AddDate(0, 0, 1)
			}
		}
	}

	return nil
}

// SyncAppointments 同步预约信息
func (c *ChongQingClient) SyncAppointments(ctx context.Context, clinicID string, startDate, endDate time.Time) error {
	// 获取患者列表（如果需要同步特定患者的预约，这里可能需要改进）
	patients, err := c.GetPatients(ctx, clinicID, "")
	if err != nil {
		logx.Error("获取患者列表失败", zap.Error(err))
		return fmt.Errorf("同步预约信息失败: %w", err)
	}

	// 遍历患者，获取每个患者的预约信息
	for _, patient := range patients {
		_, err := c.GetAppointments(ctx, clinicID, patient.ID, startDate, endDate, 0)
		if err != nil {
			logx.Error("获取预约列表失败",
				zap.String("clinicID", clinicID),
				zap.String("patientID", patient.ID),
				zap.Error(err))
			continue
		}
	}

	return nil
}
