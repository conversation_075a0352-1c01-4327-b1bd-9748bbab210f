package handler

import (
	"net/http"

	"yekaitai/wx_internal/modules/checkin/service"
	"yekaitai/wx_internal/types"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// WxCheckinGoZeroHandler 小程序端go-zero格式的签到处理器
type WxCheckinGoZeroHandler struct {
	checkinService *service.CheckinService
}

func NewWxCheckinGoZeroHandler() *WxCheckinGoZeroHandler {
	return &WxCheckinGoZeroHandler{
		checkinService: service.NewCheckinService(),
	}
}

// 签到请求
type CheckinRequest struct {
	Date string `json:"date,optional"` // 签到日期，可选，默认今天
}

// 签到日历请求
type CheckinCalendarRequest struct {
	Year  int `form:"year,optional"`  // 年份，可选，默认当前年
	Month int `form:"month,optional"` // 月份，可选，默认当前月
}

// 分享签到请求
type ShareCheckinRequest struct {
	Date string `json:"date"` // 签到日期
}

// Checkin 用户签到
func (h *WxCheckinGoZeroHandler) Checkin(w http.ResponseWriter, r *http.Request) {
	var req CheckinRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 调用签到服务
	result, err := h.checkinService.Checkin(r.Context(), userID, req.Date)
	if err != nil {
		logx.Errorf("用户签到失败: userID=%d, date=%s, error=%v", userID, req.Date, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetCheckinCalendar 获取签到日历
func (h *WxCheckinGoZeroHandler) GetCheckinCalendar(w http.ResponseWriter, r *http.Request) {
	var req CheckinCalendarRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 设置默认值
	if req.Year == 0 {
		req.Year = 2024 // 或者使用当前年份
	}
	if req.Month == 0 {
		req.Month = 1 // 或者使用当前月份
	}

	// 调用签到服务
	result, err := h.checkinService.GetCheckinCalendar(r.Context(), userID, req.Year, req.Month)
	if err != nil {
		logx.Errorf("获取签到日历失败: userID=%d, year=%d, month=%d, error=%v", userID, req.Year, req.Month, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取签到日历失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// ShareCheckin 分享签到
func (h *WxCheckinGoZeroHandler) ShareCheckin(w http.ResponseWriter, r *http.Request) {
	var req ShareCheckinRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "签到日期不能为空"))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 调用签到服务
	result, err := h.checkinService.ShareCheckin(r.Context(), userID, req.Date)
	if err != nil {
		logx.Errorf("分享签到失败: userID=%d, date=%s, error=%v", userID, req.Date, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetCheckinPopup 获取签到弹窗信息
func (h *WxCheckinGoZeroHandler) GetCheckinPopup(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 调用签到服务
	result, err := h.checkinService.GetCheckinPopupInfo(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取签到弹窗信息失败: userID=%d, error=%v", userID, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取签到信息失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetCheckinStats 获取签到统计
func (h *WxCheckinGoZeroHandler) GetCheckinStats(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 调用签到服务
	result, err := h.checkinService.GetCheckinStats(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取签到统计失败: userID=%d, error=%v", userID, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取签到统计失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetCheckinRewardRules 获取签到奖励规则
func (h *WxCheckinGoZeroHandler) GetCheckinRewardRules(w http.ResponseWriter, r *http.Request) {
	// 调用签到服务
	result, err := h.checkinService.GetCheckinRewardRules(r.Context())
	if err != nil {
		logx.Errorf("获取签到奖励规则失败: error=%v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取奖励规则失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// 以下是兼容旧版本的处理器函数

// CheckinHandler 签到处理器（兼容旧版本）
func CheckinHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxCheckinGoZeroHandler()
	return handler.Checkin
}

// CheckinCalendarHandler 签到日历处理器（兼容旧版本）
func CheckinCalendarHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxCheckinGoZeroHandler()
	return handler.GetCheckinCalendar
}

// ShareCheckinHandler 分享签到处理器（兼容旧版本）
func ShareCheckinHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxCheckinGoZeroHandler()
	return handler.ShareCheckin
}

// CheckinPopupHandler 签到弹窗处理器（兼容旧版本）
func CheckinPopupHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxCheckinGoZeroHandler()
	return handler.GetCheckinPopup
}

// CheckinStatsHandler 签到统计处理器（兼容旧版本）
func CheckinStatsHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxCheckinGoZeroHandler()
	return handler.GetCheckinStats
}

// CheckinRewardRulesHandler 签到奖励规则处理器（兼容旧版本）
func CheckinRewardRulesHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxCheckinGoZeroHandler()
	return handler.GetCheckinRewardRules
}
