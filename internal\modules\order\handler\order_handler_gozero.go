package handler

import (
	"net/http"
	"strconv"
	"strings"

	"yekaitai/internal/modules/order/service"
	"yekaitai/internal/types"
	orderModel "yekaitai/pkg/common/model/order"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type OrderGoZeroHandler struct {
	orderService *service.OrderService
}

func NewOrderGoZeroHandler() *OrderGoZeroHandler {
	return &OrderGoZeroHandler{
		orderService: service.NewOrderService(),
	}
}

// OrderListRequest 订单列表请求
type OrderListRequest struct {
	types.PageRequest
	UserID        *uint  `form:"user_id,optional"`        // 用户ID
	Status        *int   `form:"status,optional"`         // 订单状态
	PaymentStatus *int   `form:"payment_status,optional"` // 支付状态
	OrderNo       string `form:"order_no,optional"`       // 订单号
	StartDate     string `form:"start_date,optional"`     // 开始日期
	EndDate       string `form:"end_date,optional"`       // 结束日期
}

// CreateOrder 创建订单
func (h *OrderGoZeroHandler) CreateOrder(w http.ResponseWriter, r *http.Request) {
	var req orderModel.CreateOrderRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 从上下文获取用户ID
	userIDValue := r.Context().Value(types.UserIDKey)
	if userIDValue == nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	var userID uint
	switch v := userIDValue.(type) {
	case uint:
		userID = v
	case string:
		if id, err := strconv.ParseUint(v, 10, 32); err == nil {
			userID = uint(id)
		} else {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "无效的用户ID"))
			return
		}
	default:
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "无效的用户ID类型"))
		return
	}

	order, err := h.orderService.CreateOrder(r.Context(), userID, &req)
	if err != nil {
		logx.Errorf("创建订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(order))
}

// UpdateOrderStatus 更新订单状态
func (h *OrderGoZeroHandler) UpdateOrderStatus(w http.ResponseWriter, r *http.Request) {
	idStr := extractIDFromPath(r.URL.Path, "/api/admin/orders/")
	// 移除可能存在的 "/status" 后缀
	idStr = strings.TrimSuffix(idStr, "/status")

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	var req orderModel.OrderUpdateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	order, err := h.orderService.UpdateOrderStatus(r.Context(), uint(id), &req)
	if err != nil {
		logx.Errorf("更新订单状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(order))
}

// GetOrder 获取订单详情
func (h *OrderGoZeroHandler) GetOrder(w http.ResponseWriter, r *http.Request) {
	idStr := extractIDFromPath(r.URL.Path, "/api/admin/orders/")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	order, err := h.orderService.GetOrderByID(r.Context(), uint(id))
	if err != nil {
		logx.Errorf("获取订单详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(order))
}

// ListOrders 获取订单列表
func (h *OrderGoZeroHandler) ListOrders(w http.ResponseWriter, r *http.Request) {
	var req OrderListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 转换为service需要的参数
	params := &orderModel.OrderQueryParams{
		Page:          req.Page,
		PageSize:      req.Size,
		UserID:        req.UserID,
		Status:        req.Status,
		PaymentStatus: req.PaymentStatus,
		OrderNo:       req.OrderNo,
		StartDate:     req.StartDate,
		EndDate:       req.EndDate,
	}

	orders, total, err := h.orderService.ListOrders(r.Context(), params)
	if err != nil {
		logx.Errorf("获取订单列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	resp := types.NewPageResponse(orders, total, &req.PageRequest)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// GetOrderStatistics 获取订单统计
func (h *OrderGoZeroHandler) GetOrderStatistics(w http.ResponseWriter, r *http.Request) {
	stats, err := h.orderService.GetOrderStatistics(r.Context())
	if err != nil {
		logx.Errorf("获取订单统计失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(stats))
}

// extractIDFromPath 从URL路径中提取ID参数
func extractIDFromPath(fullPath, prefix string) string {
	if !strings.HasPrefix(fullPath, prefix) {
		return ""
	}

	remaining := fullPath[len(prefix):]
	// 如果有其他路径段，只取第一个
	if idx := strings.Index(remaining, "/"); idx > 0 {
		return remaining[:idx]
	}
	return remaining
}
