package abcyun

import (
	"encoding/json"
	"time"

	"yekaitai/internal/modules/pharmacy/model"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"
)

// MedicineSyncService 药品同步服务
type MedicineSyncService struct {
	medicineRepo    model.MedicineRepository
	medicineCatRepo model.MedicineCategoryRepository
	clinicIDs       []string
}

// NewMedicineSyncService 创建药品同步服务
func NewMedicineSyncService(
	medicineRepo model.MedicineRepository,
	medicineCatRepo model.MedicineCategoryRepository,
	clinicIDs []string,
) *MedicineSyncService {
	return &MedicineSyncService{
		medicineRepo:    medicineRepo,
		medicineCatRepo: medicineCatRepo,
		clinicIDs:       clinicIDs,
	}
}

// SyncMedicineCategories 同步药品分类
func (s *MedicineSyncService) SyncMedicineCategories() error {
	for _, clinicID := range s.clinicIDs {
		// 获取商品分类
		resp, err := ClientGetProductTypes(clinicID)
		if err != nil {
			logx.Error("获取商品分类失败",
				zap.String("clinicID", clinicID),
				zap.Error(err))
			return err
		}

		var result map[string]interface{}
		if err = json.Unmarshal(resp, &result); err != nil {
			logx.Error("解析商品分类失败", zap.Error(err))
			return err
		}

		// 获取数据字段
		dataObj, ok := result["data"]
		if !ok {
			logx.Error("商品分类响应缺少data字段")
			return nil
		}

		// 获取typeTree字段
		data, ok := dataObj.(map[string]interface{})
		if !ok {
			logx.Error("商品分类data不是有效对象")
			return nil
		}

		typeTree, ok := data["typeTree"].([]interface{})
		if !ok {
			logx.Error("商品分类typeTree不是有效数组")
			return nil
		}

		// 处理分类树
		if err = s.processCategoryTree(typeTree, 0, clinicID); err != nil {
			logx.Error("处理商品分类树失败", zap.Error(err))
			return err
		}

		// 获取自定义分类
		customResp, err := ClientGetProductCustomTypes(clinicID)
		if err != nil {
			logx.Error("获取自定义商品分类失败",
				zap.String("clinicID", clinicID),
				zap.Error(err))
			return err
		}

		var customResult map[string]interface{}
		if err = json.Unmarshal(customResp, &customResult); err != nil {
			logx.Error("解析自定义商品分类失败", zap.Error(err))
			return err
		}

		// 获取数据字段
		customDataObj, ok := customResult["data"]
		if !ok {
			logx.Error("自定义商品分类响应缺少data字段")
			return nil
		}

		// 获取list字段
		customData, ok := customDataObj.(map[string]interface{})
		if !ok {
			logx.Error("自定义商品分类data不是有效对象")
			return nil
		}

		customList, ok := customData["list"].([]interface{})
		if !ok {
			logx.Error("自定义商品分类list不是有效数组")
			return nil
		}

		// 处理自定义分类
		for _, item := range customList {
			customType, ok := item.(map[string]interface{})
			if !ok {
				continue
			}

			id, _ := customType["id"].(string)
			name, _ := customType["name"].(string)

			internalCategory := &model.MedicineCategory{
				Name:         name,
				ExternalID:   id,
				ExternalType: "abcyun_custom",
				ParentID:     0,
				Level:        1,
				Sort:         0,
				Status:       1,
			}

			// 查询分类是否存在
			existingCategory, err := s.medicineCatRepo.FindByExternalID(id)
			if err != nil && err.Error() != "record not found" {
				logx.Error("查询商品分类失败",
					zap.String("categoryID", id),
					zap.Error(err))
				continue
			}

			if existingCategory == nil {
				// 创建新分类
				if err = s.medicineCatRepo.Create(internalCategory); err != nil {
					logx.Error("创建自定义商品分类失败",
						zap.String("categoryID", id),
						zap.Error(err))
					continue
				}
			} else {
				// 更新分类
				internalCategory.ID = existingCategory.ID
				if err = s.medicineCatRepo.Update(internalCategory); err != nil {
					logx.Error("更新自定义商品分类失败",
						zap.String("categoryID", id),
						zap.Error(err))
					continue
				}
			}
		}
	}

	return nil
}

// processCategoryTree 处理分类树
func (s *MedicineSyncService) processCategoryTree(categories []interface{}, parentID uint, clinicID string) error {
	for i, categoryObj := range categories {
		category, ok := categoryObj.(map[string]interface{})
		if !ok {
			continue
		}

		id, _ := category["id"].(string)
		name, _ := category["name"].(string)

		// 转换为内部模型
		internalCategory := &model.MedicineCategory{
			Name:         name,
			ExternalID:   id,
			ExternalType: "abcyun",
			ParentID:     parentID,
			Level: func() int {
				if parentID > 0 {
					return 2
				} else {
					return 1
				}
			}(),
			Sort:   i,
			Status: 1,
		}

		// 查询分类是否存在
		existingCategory, err := s.medicineCatRepo.FindByExternalID(id)
		if err != nil && err.Error() != "record not found" {
			logx.Error("查询商品分类失败",
				zap.String("categoryID", id),
				zap.Error(err))
			continue
		}

		var categoryID uint
		if existingCategory == nil {
			// 创建新分类
			if err = s.medicineCatRepo.Create(internalCategory); err != nil {
				logx.Error("创建商品分类失败",
					zap.String("categoryID", id),
					zap.Error(err))
				continue
			}
			categoryID = internalCategory.ID
		} else {
			// 更新分类
			internalCategory.ID = existingCategory.ID
			if err = s.medicineCatRepo.Update(internalCategory); err != nil {
				logx.Error("更新商品分类失败",
					zap.String("categoryID", id),
					zap.Error(err))
				continue
			}
			categoryID = existingCategory.ID
		}

		// 递归处理子分类
		children, ok := category["children"].([]interface{})
		if ok && len(children) > 0 {
			if err = s.processCategoryTree(children, categoryID, clinicID); err != nil {
				logx.Error("处理子分类失败",
					zap.String("parentID", id),
					zap.Error(err))
				continue
			}
		}
	}

	return nil
}

// SyncMedicines 同步药品信息
func (s *MedicineSyncService) SyncMedicines() error {
	// 药品类型 1:药品
	productType := 1

	for _, clinicID := range s.clinicIDs {
		// 获取药品列表
		offset := 0
		limit := 100

		for {
			resp, err := ClientGetProductList(clinicID, productType, offset, limit)
			if err != nil {
				logx.Error("获取药品列表失败",
					zap.String("clinicID", clinicID),
					zap.Error(err))
				return err
			}

			var result map[string]interface{}
			if err = json.Unmarshal(resp, &result); err != nil {
				logx.Error("解析药品列表失败", zap.Error(err))
				return err
			}

			// 获取数据字段
			dataObj, ok := result["data"]
			if !ok {
				logx.Error("药品列表响应缺少data字段")
				break
			}

			// 获取rows字段
			data, ok := dataObj.(map[string]interface{})
			if !ok {
				logx.Error("药品列表data不是有效对象")
				break
			}

			rows, ok := data["rows"].([]interface{})
			if !ok {
				logx.Error("药品列表rows不是有效数组")
				break
			}

			// 如果没有数据，结束循环
			if len(rows) == 0 {
				break
			}

			// 处理药品数据
			for _, productObj := range rows {
				product, ok := productObj.(map[string]interface{})
				if !ok {
					continue
				}

				productID, _ := product["id"].(string)

				// 获取药品详情
				detailResp, err := ClientGetProductDetail(clinicID, productID)
				if err != nil {
					logx.Error("获取药品详情失败",
						zap.String("productID", productID),
						zap.Error(err))
					continue
				}

				var detailResult map[string]interface{}
				if err = json.Unmarshal(detailResp, &detailResult); err != nil {
					logx.Error("解析药品详情失败", zap.Error(err))
					continue
				}

				// 获取详情数据
				detailDataObj, ok := detailResult["data"]
				if !ok {
					logx.Error("药品详情响应缺少data字段")
					continue
				}

				detailData, ok := detailDataObj.(map[string]interface{})
				if !ok {
					logx.Error("药品详情data不是有效对象")
					continue
				}

				// 转换为内部模型
				internalMedicine := convertToInternalMedicine(detailData, clinicID)

				// 查询药品是否存在
				existingMedicine, err := s.medicineRepo.FindByExternalID(productID)
				if err != nil && err.Error() != "record not found" {
					logx.Error("查询药品失败",
						zap.String("productID", productID),
						zap.Error(err))
					continue
				}

				if existingMedicine == nil {
					// 创建新药品
					if err = s.medicineRepo.Create(internalMedicine); err != nil {
						logx.Error("创建药品失败",
							zap.String("productID", productID),
							zap.Error(err))
						continue
					}
				} else {
					// 更新药品
					internalMedicine.ID = existingMedicine.ID
					if err = s.medicineRepo.Update(internalMedicine); err != nil {
						logx.Error("更新药品失败",
							zap.String("productID", productID),
							zap.Error(err))
						continue
					}
				}

				// 同步库存信息
				s.syncMedicineStock(clinicID, productID)
			}

			// 更新偏移量
			offset += limit
		}
	}

	return nil
}

// syncMedicineStock 同步药品库存
func (s *MedicineSyncService) syncMedicineStock(clinicID, productID string) {
	// 查询库存
	resp, err := ClientQueryProductStock(clinicID, []string{productID})
	if err != nil {
		logx.Error("查询药品库存失败",
			zap.String("productID", productID),
			zap.Error(err))
		return
	}

	var result map[string]interface{}
	if err = json.Unmarshal(resp, &result); err != nil {
		logx.Error("解析药品库存失败", zap.Error(err))
		return
	}

	// 获取数据字段
	dataObj, ok := result["data"]
	if !ok {
		logx.Error("药品库存响应缺少data字段")
		return
	}

	// 获取goodsStocks字段
	data, ok := dataObj.(map[string]interface{})
	if !ok {
		logx.Error("药品库存data不是有效对象")
		return
	}

	goodsStocks, ok := data["goodsStocks"].([]interface{})
	if !ok || len(goodsStocks) == 0 {
		logx.Error("药品库存goodsStocks不是有效数组或为空")
		return
	}

	// 处理库存数据
	for _, stockObj := range goodsStocks {
		stock, ok := stockObj.(map[string]interface{})
		if !ok {
			continue
		}

		stockProductID, _ := stock["productId"].(string)
		if stockProductID != productID {
			continue
		}

		// 更新药品库存
		stockPackageCount, _ := stock["stockPackageCount"].(float64)

		medicine, err := s.medicineRepo.FindByExternalID(productID)
		if err != nil {
			logx.Error("查询药品失败",
				zap.String("productID", productID),
				zap.Error(err))
			return
		}

		medicine.Stock = int(stockPackageCount)
		if err = s.medicineRepo.Update(medicine); err != nil {
			logx.Error("更新药品库存失败",
				zap.String("productID", productID),
				zap.Error(err))
		}

		break
	}
}

// ScheduleSync 定时同步
func (s *MedicineSyncService) ScheduleSync() {
	// 每天凌晨3点同步
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	for {
		<-ticker.C
		now := time.Now()
		if now.Hour() == 3 && now.Minute() < 10 {
			logx.Info("开始定时同步药品数据")
			s.syncAll()
		}
	}
}

// syncAll 同步所有数据
func (s *MedicineSyncService) syncAll() {
	// 同步药品分类
	if err := s.SyncMedicineCategories(); err != nil {
		logx.Error("同步药品分类失败", zap.Error(err))
	}

	// 同步药品信息
	if err := s.SyncMedicines(); err != nil {
		logx.Error("同步药品信息失败", zap.Error(err))
	}
}

// convertToInternalMedicine 转换为内部药品模型
func convertToInternalMedicine(product map[string]interface{}, clinicID string) *model.Medicine {
	id, _ := product["id"].(string)
	name, _ := product["name"].(string)
	manufacturer, _ := product["manufacturer"].(string)
	specification, _ := product["specification"].(string)
	barCode, _ := product["barCode"].(string)
	packageUnit, _ := product["packageUnit"].(string)
	packagePrice, _ := product["packagePrice"].(float64)

	return &model.Medicine{
		Name:          name,
		Manufacturer:  manufacturer,
		Specification: specification,
		Barcode:       barCode,
		Unit:          packageUnit,
		Price:         float64(packagePrice),
		ExternalID:    id,
		ExternalType:  "abcyun",
		CategoryID:    0, // 此处应根据customTypeID查询对应的分类ID
		Status:        1,
	}
}
