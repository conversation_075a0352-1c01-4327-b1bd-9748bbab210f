package handler

import (
	"net/http"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/internal/modules/goods/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// GoodsGoZeroHandler go-zero格式的商品处理器
type GoodsGoZeroHandler struct {
	goodsService *service.GoodsService
}

func NewGoodsGoZeroHandler() *GoodsGoZeroHandler {
	return &GoodsGoZeroHandler{
		goodsService: service.NewGoodsService(),
	}
}

// GoodsListRequest 商品列表请求
type GoodsListRequest struct {
	types.PageRequest
	CategoryID string `form:"category_id,optional"` // 分类ID
	GoodsName  string `form:"goods_name,optional"`  // 商品名称
	Status     *int   `form:"status,optional"`      // 状态
}

// GoodsDetailRequest 商品详情请求
type GoodsDetailRequest struct {
	GoodsID uint `path:"id"` // 商品ID
}

// GoodsUpdateDetailRequest 商品更新请求
type GoodsUpdateDetailRequest struct {
	GoodsID uint `path:"id"` // 商品ID
	model.GoodsUpdateRequest
}

// GoodsDeleteRequest 商品删除请求
type GoodsDeleteRequest struct {
	GoodsID uint `path:"id"` // 商品ID
}

// ListGoods 获取商品列表
func (h *GoodsGoZeroHandler) ListGoods(w http.ResponseWriter, r *http.Request) {
	var req GoodsListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 转换为service需要的参数
	params := &model.GoodsQueryParams{
		Page:       req.Page,
		PageSize:   req.Size,
		CategoryID: req.CategoryID, // 使用CategoryID
		GoodsName:  req.GoodsName,
		Status:     req.Status,
	}

	goods, total, err := h.goodsService.ListGoods(r.Context(), params)
	if err != nil {
		logx.Errorf("获取商品列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	resp := types.NewPageResponse(goods, total, &req.PageRequest)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// CreateGoods 创建商品
func (h *GoodsGoZeroHandler) CreateGoods(w http.ResponseWriter, r *http.Request) {
	var req model.GoodsCreateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	goods, err := h.goodsService.CreateGoods(r.Context(), &req)
	if err != nil {
		logx.Errorf("创建商品失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(goods))
}

// GetGoods 获取商品详情
func (h *GoodsGoZeroHandler) GetGoods(w http.ResponseWriter, r *http.Request) {
	var req GoodsDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的商品ID"))
		return
	}

	goods, err := h.goodsService.GetGoods(r.Context(), req.GoodsID)
	if err != nil {
		logx.Errorf("获取商品详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(goods))
}

// UpdateGoods 更新商品
func (h *GoodsGoZeroHandler) UpdateGoods(w http.ResponseWriter, r *http.Request) {
	var req GoodsUpdateDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	goods, err := h.goodsService.UpdateGoods(r.Context(), req.GoodsID, &req.GoodsUpdateRequest)
	if err != nil {
		logx.Errorf("更新商品失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(goods))
}

// DeleteGoods 删除商品
func (h *GoodsGoZeroHandler) DeleteGoods(w http.ResponseWriter, r *http.Request) {
	var req GoodsDeleteRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的商品ID"))
		return
	}

	err := h.goodsService.DeleteGoods(r.Context(), req.GoodsID)
	if err != nil {
		logx.Errorf("删除商品失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}
