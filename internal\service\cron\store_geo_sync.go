package cron

import (
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/internal/config"
	"yekaitai/internal/modules/store/model"
	"yekaitai/internal/modules/store/service"
	"yekaitai/pkg/infra/mysql"
)

// StoreGeoSyncService 门店地理编码同步服务
type StoreGeoSyncService struct {
	config    *config.CronConfig
	mapApiKey string
	location  service.LocationService
	repo      model.StoreRepository
	running   bool
	mutex     sync.Mutex
}

// NewStoreGeoSyncService 创建门店地理编码同步服务
func NewStoreGeoSyncService(cronConfig *config.CronConfig, mapApiKey string) *StoreGeoSyncService {
	return &StoreGeoSyncService{
		config:    cronConfig,
		mapApiKey: mapApiKey,
		location:  service.NewLocationService(mapApiKey),
		repo:      model.NewStoreRepository(mysql.Master()),
	}
}

// Start 启动定时任务
func (s *StoreGeoSyncService) Start() error {
	if !s.config.Enable || !s.config.StoreGeoSync.Enable {
		logx.Info("门店地理编码同步服务未启用")
		return nil
	}

	c := cron.New(cron.WithSeconds())
	_, err := c.AddFunc(s.config.StoreGeoSync.Cron, func() {
		s.SyncAll()
	})
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %v", err)
	}

	c.Start()
	logx.Info("门店地理编码同步服务已启动")
	return nil
}

// SyncAll 同步所有门店的地理编码
func (s *StoreGeoSyncService) SyncAll() error {
	s.mutex.Lock()
	if s.running {
		s.mutex.Unlock()
		return fmt.Errorf("同步任务正在运行中")
	}
	s.running = true
	s.mutex.Unlock()
	defer func() {
		s.mutex.Lock()
		s.running = false
		s.mutex.Unlock()
	}()

	logx.Info("开始同步门店地理编码")
	startTime := time.Now()

	// 获取所有需要同步的门店
	stores, total, err := s.repo.List(1, s.config.StoreGeoSync.BatchSize, "")
	if err != nil {
		return fmt.Errorf("获取门店列表失败: %v", err)
	}

	// 统计信息
	var success, failed int
	batchSize := s.config.StoreGeoSync.BatchSize
	if batchSize <= 0 {
		batchSize = 100
	}

	// 分批处理
	for i := 0; i < len(stores); i += batchSize {
		end := i + batchSize
		if end > len(stores) {
			end = len(stores)
		}
		batch := stores[i:end]

		// 处理每个门店
		for _, store := range batch {
			if err := s.syncStore(store); err != nil {
				failed++
				logx.Errorf("同步门店[%d]地理编码失败: %v", store.ID, err)
			} else {
				success++
			}
		}
	}

	duration := time.Since(startTime)
	logx.Infof("门店地理编码同步完成，总数：%d，成功：%d，失败：%d，耗时：%v", total, success, failed, duration)
	return nil
}

// syncStore 同步单个门店的地理编码
func (s *StoreGeoSyncService) syncStore(store *model.Store) error {
	if store.Address == "" {
		return fmt.Errorf("门店地址为空")
	}

	// 构建完整地址
	fullAddress := fmt.Sprintf("%s%s%s%s", store.ProvinceID, store.CityID, store.AreaID, store.Address)

	// 获取地理编码
	lat, lng, err := s.location.Geocode(fullAddress)
	if err != nil {
		return fmt.Errorf("获取地理编码失败: %v", err)
	}
	fmt.Printf("门店[%d]地理编码: %f, %f\n", store.ID, lat, lng)

	// 更新门店信息
	store.Latitude = lat
	store.Longitude = lng
	if err := s.repo.Update(store); err != nil {
		return fmt.Errorf("更新门店信息失败: %v", err)
	}

	return nil
}
