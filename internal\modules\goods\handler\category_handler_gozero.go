package handler

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/internal/modules/goods/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// CategoryGoZeroHandler go-zero格式的分类处理器
type CategoryGoZeroHandler struct {
	categoryService *service.CategoryService
}

func NewCategoryGoZeroHandler() *CategoryGoZeroHandler {
	return &CategoryGoZeroHandler{
		categoryService: service.NewCategoryService(),
	}
}

// CategoryListRequest 分类列表请求
type CategoryListRequest struct {
	types.PageRequest
	ParentID      *uint  `form:"parent_id,optional"`      // 父分类ID
	CategoryName  string `form:"category_name,optional"`  // 分类名称
	Status        *int   `form:"status,optional"`         // 状态
	IsRecommended *int   `form:"is_recommended,optional"` // 是否推荐
	Level         *int   `form:"level,optional"`          // 层级
}

// CategoryDetailRequest 分类详情请求
type CategoryDetailRequest struct {
	ID uint `path:"id"` // 分类ID
}

// ListCategories 获取分类列表
func (h *CategoryGoZeroHandler) ListCategories(w http.ResponseWriter, r *http.Request) {
	var req CategoryListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 转换为service需要的参数
	params := &model.CategoryQueryParams{
		Page:          req.Page,
		PageSize:      req.Size,
		ParentID:      req.ParentID,
		CategoryName:  req.CategoryName,
		Status:        req.Status,
		IsRecommended: req.IsRecommended,
		Level:         req.Level,
	}

	categories, total, err := h.categoryService.ListCategories(r.Context(), params)
	if err != nil {
		logx.Errorf("获取分类列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	resp := types.NewPageResponse(categories, total, &req.PageRequest)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// CreateCategory 创建分类
func (h *CategoryGoZeroHandler) CreateCategory(w http.ResponseWriter, r *http.Request) {
	var req model.CategoryCreateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	category, err := h.categoryService.CreateCategory(r.Context(), &req)
	if err != nil {
		logx.Errorf("创建分类失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(category))
}

// GetCategory 获取分类详情
func (h *CategoryGoZeroHandler) GetCategory(w http.ResponseWriter, r *http.Request) {
	var req CategoryDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	category, err := h.categoryService.GetCategory(r.Context(), req.ID)
	if err != nil {
		logx.Errorf("获取分类详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(category))
}

// UpdateCategory 更新分类
func (h *CategoryGoZeroHandler) UpdateCategory(w http.ResponseWriter, r *http.Request) {
	// 定义包含ID的请求结构
	type UpdateCategoryRequest struct {
		ID uint `path:"id"` // 分类ID
		model.CategoryUpdateRequest
	}

	var req UpdateCategoryRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	category, err := h.categoryService.UpdateCategory(r.Context(), req.ID, &req.CategoryUpdateRequest)
	if err != nil {
		logx.Errorf("更新分类失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(category))
}

// DeleteCategory 删除分类
func (h *CategoryGoZeroHandler) DeleteCategory(w http.ResponseWriter, r *http.Request) {
	var req CategoryDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	err := h.categoryService.DeleteCategory(r.Context(), req.ID)
	if err != nil {
		logx.Errorf("删除分类失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// GetCategoryTree 获取分类树
func (h *CategoryGoZeroHandler) GetCategoryTree(w http.ResponseWriter, r *http.Request) {
	// 增加超时设置，避免查询时间过长
	ctx, cancel := context.WithTimeout(r.Context(), 10*time.Second)
	defer cancel()

	parentIDStr := r.URL.Query().Get("parent_id")
	if parentIDStr == "" {
		parentIDStr = "0"
	}

	parentID, err := strconv.ParseUint(parentIDStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的父分类ID"))
		return
	}

	tree, err := h.categoryService.GetCategoryTree(ctx, uint(parentID))
	if err != nil {
		logx.Errorf("获取分类树失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(tree))
}
