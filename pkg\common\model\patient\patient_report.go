package patient

import (
	"time"

	"gorm.io/gorm"
)

// AbcYunPatientReport ABC云患者报告表
type AbcYunPatientReport struct {
	ID               uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:报告ID"`
	PatientID        uint           `json:"patient_id" gorm:"index;not null;comment:关联患者ID"`
	AbcYunReportID   string         `json:"abcyun_report_id" gorm:"type:varchar(100);index;comment:ABC云报告ID"`
	FileName         string         `json:"file_name" gorm:"type:varchar(255);comment:文件名"`
	DisplayName      string         `json:"display_name" gorm:"type:varchar(255);comment:显示名称"`
	OriginalURL      string         `json:"original_url" gorm:"type:text;comment:原始URL"`
	QiniuURL         string         `json:"qiniu_url" gorm:"type:text;comment:七牛云URL"`
	UploadStatus     int            `json:"upload_status" gorm:"default:0;comment:上传状态(0-未上传,1-上传成功,2-上传失败)"`
	BusinessCategory int            `json:"business_category" gorm:"comment:业务分类"`
	ReportDate       string         `json:"report_date" gorm:"type:varchar(30);comment:报告日期"`
	CreatedAt        time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt        time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 设置表名
func (AbcYunPatientReport) TableName() string {
	return "abcyun_patient_reports"
}
