package handler

import (
	"context"
	"net/http"

	"yekaitai/internal/modules/admin/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// AdminOperationLogHandler 操作日志处理器
type AdminOperationLogHandler struct {
	logService *service.AdminOperationLogService
}

// NewAdminOperationLogHandler 创建操作日志处理器
func NewAdminOperationLogHandler() *AdminOperationLogHandler {
	return &AdminOperationLogHandler{
		logService: service.NewAdminOperationLogService(),
	}
}

// GetOperationLogList 获取操作日志列表
func (h *AdminOperationLogHandler) GetOperationLogList(w http.ResponseWriter, r *http.Request) {
	var req service.AdminOperationLogListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	logs, total, err := h.logService.GetOperationLogList(context.Background(), &req)
	if err != nil {
		logx.Errorf("获取操作日志列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取操作日志列表失败"))
		return
	}

	response := map[string]interface{}{
		"list":  logs,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(response, "获取操作日志列表成功"))
}
