package bootstrap

import (
	"yekaitai/internal/modules/goods/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateShippingTables 执行运费配置模块表结构迁移
func MigrateShippingTables() error {
	db := mysql.Master()

	logx.Info("开始执行运费配置模块表结构迁移...")

	// 自动迁移运费配置表
	if err := db.AutoMigrate(&model.ShippingConfig{}); err != nil {
		logx.Errorf("运费配置表迁移失败: %v", err)
		return err
	}
	logx.Info("运费配置表迁移完成")

	logx.Info("运费配置模块表结构迁移完成")
	return nil
}
