package tasks

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/common/model/user"
)

// PatientSyncService 患者信息同步服务
type PatientSyncService struct {
	db     *gorm.DB
	wsjgID int // 卫生机构ID
}

// NewPatientSyncService 创建患者信息同步服务
func NewPatientSyncService(db *gorm.DB) *PatientSyncService {
	// 获取配置中的卫生机构ID
	wsjgID := 5426 // 默认值

	// 如果DefaultClient已初始化，从配置中获取
	if hangzhou.DefaultClient != nil {
		config := hangzhou.DefaultClient.GetConfig()
		if config.CurrentWsjgid != "" {
			wsjgIDFromConfig, _ := strconv.Atoi(config.CurrentWsjgid)
			if wsjgIDFromConfig > 0 {
				wsjgID = wsjgIDFromConfig
			}
		}
	}

	logx.Infof("患者同步服务初始化，使用卫生机构ID: %d", wsjgID)

	return &PatientSyncService{
		db:     db,
		wsjgID: wsjgID,
	}
}

// Start 启动定时任务
func (s *PatientSyncService) Start() {
	// 立即执行一次
	s.RunSync()

	// 每小时执行一次
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		<-ticker.C
		s.RunSync()
	}
}

// RunSync 执行同步任务的包装方法
func (s *PatientSyncService) RunSync() {
	if err := s.SyncPatients(); err != nil {
		logx.Errorf("执行患者同步失败: %v", err)
	}
}

// SyncPatients 同步患者信息到数据库 (公开方法，便于直接调用)
func (s *PatientSyncService) SyncPatients() error {
	// 添加panic恢复
	defer func() {
		if r := recover(); r != nil {
			logx.Errorf("患者同步过程中发生panic: %v", r)
			return
		}
	}()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	logx.Info("开始同步患者信息...")
	logx.Infof("使用卫生机构ID: %d", s.wsjgID)

	// 1. 获取所有患者信息
	logx.Infof("调用GetPersonalInfos API，参数: xm=\"\", zfbz=\"\", wsjgID=%d", s.wsjgID)
	resp, err := hangzhou.GetPersonalInfos(ctx, "", "", s.wsjgID)
	if err != nil {
		logx.Errorf("获取患者信息失败: %v", err)
		return err
	}

	// 将接口数据转换为切片
	personalInfos, ok := resp.Data.([]hangzhou.PersonalInfo)
	if !ok {
		logx.Errorf("解析患者数据失败")
		return fmt.Errorf("解析患者数据失败")
	}

	logx.Infof("获取到患者数量: %d", len(personalInfos))

	// 记录创建和更新的患者数量
	created := 0
	updated := 0
	// filtered 变量暂时未使用，因为API已经根据wsjgID过滤了结果

	// 处理每个患者信息
	for i, info := range personalInfos {
		logx.Infof("处理第 %d 个患者: ID=%d, 姓名=%s", i+1, info.GrxxID, info.Xm)

		// 首先根据证件号码查找是否已存在用户
		var wxUser user.WxUser
		userResult := s.db.Where("mobile = ?", info.SjHm).First(&wxUser)

		// 如果用户不存在且手机号存在，则创建用户
		if userResult.Error != nil && info.SjHm != "" {
			// 创建新用户
			wxUser = user.WxUser{
				Mobile:       info.SjHm,
				Nickname:     info.Xm,
				Gender:       getGenderCode(info.XbDM),
				Status:       1,
				RegisterDate: time.Now(),
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			}

			if err := s.db.Create(&wxUser).Error; err != nil {
				logx.Errorf("创建微信用户失败: %v", err)
				continue
			}
			logx.Infof("成功创建微信用户: %s, 手机号: %s", wxUser.Nickname, wxUser.Mobile)
		}

		// 如果找不到用户且手机号不存在，则跳过此患者
		if userResult.Error != nil && info.SjHm == "" {
			logx.Infof("患者 %s 没有手机号，无法创建用户，跳过", info.Xm)
			continue
		}

		// 根据用户ID查找是否存在患者记录
		var wxPatient patient.WxPatient
		patientResult := s.db.Where("user_id = ?", wxUser.UserID).First(&wxPatient)

		isNew := patientResult.Error != nil

		// 格式化出生日期
		birthDate := ""
		if info.CsSj != "" {
			birthDate = info.CsSj
		}

		// 设置患者信息
		wxPatient.UserID = wxUser.UserID
		wxPatient.Name = info.Xm
		wxPatient.Gender = getGenderCode(info.XbDM)
		wxPatient.BirthDate = birthDate
		wxPatient.IdCard = info.ZjHm
		wxPatient.Mobile = info.SjHm
		wxPatient.RelationshipWithUser = "本人" // 设置默认值为"本人"
		wxPatient.Status = 1
		wxPatient.HangzhouHisID = strconv.Itoa(info.GrxxID)

		// 如果是新患者，设置创建时间
		if isNew {
			wxPatient.CreatedAt = time.Now()
			if err := s.db.Create(&wxPatient).Error; err != nil {
				logx.Errorf("创建患者信息失败，患者ID=%d: %v", info.GrxxID, err)
				continue
			}
			logx.Infof("成功创建患者: %s, 用户ID: %d", wxPatient.Name, wxPatient.UserID)
			created++
		} else {
			// 更新现有患者信息 - 使用Updates代替Save，只更新特定字段
			updateTime := time.Now()
			updateValues := map[string]interface{}{
				"name":                   info.Xm,
				"gender":                 getGenderCode(info.XbDM),
				"birth_date":             birthDate,
				"id_card":                info.ZjHm,
				"mobile":                 info.SjHm,
				"relationship_with_user": "本人",
				"status":                 1,
				"hangzhou_his_id":        strconv.Itoa(info.GrxxID),
				"updated_at":             updateTime,
			}
			
			if err := s.db.Model(&wxPatient).Updates(updateValues).Error; err != nil {
				logx.Errorf("更新患者信息失败，患者ID=%d: %v", info.GrxxID, err)
				continue
			}
			logx.Infof("成功更新患者: %s, 用户ID: %d", wxPatient.Name, wxPatient.UserID)
			updated++
		}
	}

	logx.Infof("患者信息同步完成，共创建 %d 个患者，更新 %d 个患者", created, updated)
	return nil
}

// getGenderCode 将接口的性别代码转换为系统使用的性别代码
func getGenderCode(xbdm string) int {
	switch xbdm {
	case "1":
		return 1 // 男
	case "2":
		return 2 // 女
	default:
		return 0 // 未知
	}
}
