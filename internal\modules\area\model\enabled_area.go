package model

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// EnabledArea 已开通地区模型
type EnabledArea struct {
	ID         int64        `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	Code       string       `json:"code" gorm:"type:varchar(20);uniqueIndex;comment:行政区划代码"`      // 行政区划代码
	Name       string       `json:"name" gorm:"type:varchar(50);not null;comment:名称"`             // 名称
	ParentCode string       `json:"parent_code" gorm:"type:varchar(20);index;comment:父级行政区划代码"`   // 父级行政区划代码
	Level      int          `json:"level" gorm:"type:tinyint;not null;comment:级别:1-省级 2-地级 3-县级"` // 级别: 1-省级 2-地级 3-县级
	CreatedBy  uint         `json:"created_by" gorm:"type:int unsigned;comment:创建人ID"`            // 创建人ID
	CreatedAt  time.Time    `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt  time.Time    `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt  sql.NullTime `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (r *EnabledArea) TableName() string {
	return "t_enabled_areas"
}

// EnabledAreaList 带分页的已开通地区列表
type EnabledAreaList struct {
	Total int64          `json:"total"`
	List  []*EnabledArea `json:"list"`
}

// EnabledAreaRepository 已开通地区仓库接口
type EnabledAreaRepository interface {
	// 创建已开通地区
	Create(area *EnabledArea) error
	// 批量创建已开通地区
	BatchCreate(areas []*EnabledArea) error
	// 根据编码查找已开通地区
	FindByCode(code string) (*EnabledArea, error)
	// 获取已开通地区列表（分页）
	List(page, size int, query string) ([]*EnabledArea, int64, error)
	// 获取所有已开通省份
	GetProvinces() ([]*EnabledArea, error)
	// 获取指定已开通省份的所有城市
	GetCities(provinceCode string) ([]*EnabledArea, error)
	// 获取指定已开通城市的所有区县
	GetAreas(cityCode string) ([]*EnabledArea, error)
	// 根据编码删除已开通地区
	DeleteByCode(code string) error
	// 检查地区是否已开通
	IsEnabled(code string) (bool, error)
}

// enabledAreaRepository 实现了EnabledAreaRepository接口
type enabledAreaRepository struct {
	db *gorm.DB
}

// NewEnabledAreaRepository 创建已开通地区仓库实例
func NewEnabledAreaRepository(db *gorm.DB) EnabledAreaRepository {
	return &enabledAreaRepository{db: db}
}

// Create 创建已开通地区
func (r *enabledAreaRepository) Create(area *EnabledArea) error {
	// 检查是否已存在
	exists, err := r.IsEnabled(area.Code)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if exists {
		return errors.New("地区已开通")
	}
	return r.db.Create(area).Error
}

// BatchCreate 批量创建已开通地区
func (r *enabledAreaRepository) BatchCreate(areas []*EnabledArea) error {
	if len(areas) == 0 {
		return nil
	}
	return r.db.CreateInBatches(areas, 100).Error
}

// FindByCode 根据编码查找已开通地区
func (r *enabledAreaRepository) FindByCode(code string) (*EnabledArea, error) {
	var area EnabledArea
	err := r.db.Where("code = ?", code).First(&area).Error
	if err != nil {
		return nil, err
	}
	return &area, nil
}

// List 获取已开通地区列表（分页）
func (r *enabledAreaRepository) List(page, size int, query string) ([]*EnabledArea, int64, error) {
	var areas []*EnabledArea
	var total int64

	db := r.db.Model(&EnabledArea{})

	if query != "" {
		db = db.Where("name LIKE ? OR code LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if page > 0 && size > 0 {
		offset := (page - 1) * size
		db = db.Offset(offset).Limit(size)
	}

	// 按级别和名称排序
	err = db.Order("level ASC, name ASC").Find(&areas).Error
	return areas, total, err
}

// GetProvinces 获取所有已开通省份
func (r *enabledAreaRepository) GetProvinces() ([]*EnabledArea, error) {
	// 获取所有已开通的区县
	var enabledAreas []*EnabledArea
	err := r.db.Find(&enabledAreas).Error
	if err != nil {
		return nil, err
	}

	if len(enabledAreas) == 0 {
		return []*EnabledArea{}, nil
	}

	// 先收集所有已开通区县的parent_code（城市编码）
	var cityCodes []string
	for _, area := range enabledAreas {
		if area.ParentCode != "" {
			cityCodes = append(cityCodes, area.ParentCode)
		}
	}

	if len(cityCodes) == 0 {
		return []*EnabledArea{}, nil
	}

	// 查询t_regions表，获取这些城市的信息
	var cities []struct {
		Code       string `gorm:"column:code"`
		ParentCode string `gorm:"column:parent_code"`
	}

	err = r.db.Raw("SELECT code, parent_code FROM t_regions WHERE code IN ? AND level = 2",
		cityCodes).Scan(&cities).Error
	if err != nil {
		return nil, err
	}

	// 收集所有城市的parent_code（省份编码）
	var provinceCodes []string
	for _, city := range cities {
		if city.ParentCode != "" {
			provinceCodes = append(provinceCodes, city.ParentCode)
		}
	}

	if len(provinceCodes) == 0 {
		return []*EnabledArea{}, nil
	}

	// 查询这些省份编码对应的省份信息
	var provinces []struct {
		Code  string `gorm:"column:code"`
		Name  string `gorm:"column:name"`
		Level int    `gorm:"column:level"`
	}

	err = r.db.Raw("SELECT code, name, level FROM t_regions WHERE code IN ? AND level = 1",
		provinceCodes).Scan(&provinces).Error
	if err != nil {
		return nil, err
	}

	// 转换为EnabledArea对象
	var result []*EnabledArea
	for _, province := range provinces {
		result = append(result, &EnabledArea{
			Code:  province.Code,
			Name:  province.Name,
			Level: province.Level,
		})
	}

	return result, nil
}

// GetCities 获取指定省份的所有城市
func (r *enabledAreaRepository) GetCities(provinceCode string) ([]*EnabledArea, error) {
	if len(provinceCode) < 2 {
		return nil, fmt.Errorf("省份编码无效")
	}

	// 获取所有已开通的区县
	var enabledAreas []*EnabledArea
	err := r.db.Find(&enabledAreas).Error
	if err != nil {
		return nil, err
	}

	if len(enabledAreas) == 0 {
		return []*EnabledArea{}, nil
	}

	// 收集所有已开通区县的parent_code
	var parentCodes []string
	for _, area := range enabledAreas {
		if area.ParentCode != "" {
			parentCodes = append(parentCodes, area.ParentCode)
		}
	}

	if len(parentCodes) == 0 {
		return []*EnabledArea{}, nil
	}

	// 直接查询t_regions表，获取所有parent_code对应的市级数据
	var cities []struct {
		Code       string `gorm:"column:code"`
		Name       string `gorm:"column:name"`
		ParentCode string `gorm:"column:parent_code"`
		Level      int    `gorm:"column:level"`
	}

	err = r.db.Raw("SELECT code, name, parent_code, level FROM t_regions WHERE code IN ? AND level = 2 AND parent_code = ?",
		parentCodes, provinceCode).Scan(&cities).Error
	if err != nil {
		return nil, err
	}

	// 转换为EnabledArea对象
	var result []*EnabledArea
	for _, city := range cities {
		result = append(result, &EnabledArea{
			Code:       city.Code,
			Name:       city.Name,
			ParentCode: city.ParentCode,
			Level:      city.Level,
		})
	}

	return result, nil
}

// GetAreas 获取指定城市的所有区县
func (r *enabledAreaRepository) GetAreas(cityCode string) ([]*EnabledArea, error) {
	if len(cityCode) < 4 {
		return nil, fmt.Errorf("城市编码无效")
	}

	// 直接查询已开通的区县，parent_code等于指定的城市编码
	var areas []*EnabledArea
	err := r.db.Where("parent_code = ?", cityCode).Find(&areas).Error
	if err != nil {
		return nil, err
	}

	return areas, nil
}

// DeleteByCode 根据编码删除已开通地区
func (r *enabledAreaRepository) DeleteByCode(code string) error {
	return r.db.Where("code = ?", code).Delete(&EnabledArea{}).Error
}

// IsEnabled 检查地区是否已开通
func (r *enabledAreaRepository) IsEnabled(code string) (bool, error) {
	var count int64
	err := r.db.Model(&EnabledArea{}).Where("code = ?", code).Count(&count).Error
	return count > 0, err
}
