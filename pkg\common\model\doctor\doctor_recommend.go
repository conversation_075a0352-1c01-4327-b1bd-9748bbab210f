package doctor

import (
	"time"

	"gorm.io/gorm"
)

// DoctorRecommend 医生推荐表
type DoctorRecommend struct {
	ID           uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	DoctorID     uint           `gorm:"column:doctor_id;not null;index;comment:医生ID" json:"doctor_id"`
	IsTop        bool           `gorm:"column:is_top;default:false;comment:是否置顶" json:"is_top"`
	SortOrder    int            `gorm:"column:sort_order;default:0;comment:排序，数字越小越靠前" json:"sort_order"`
	Status       int            `gorm:"column:status;default:1;comment:状态(0-禁用，1-启用)" json:"status"`
	CreatorID    uint           `gorm:"column:creator_id;comment:创建人ID" json:"creator_id"`
	CreatedAt    time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
	
	// 关联关系
	Doctor *WxDoctor `gorm:"foreignKey:DoctorID" json:"doctor,omitempty"`
}

// TableName 设置表名
func (DoctorRecommend) TableName() string {
	return "doctor_recommends"
}

// DoctorRecommendCreateRequest 创建医生推荐请求
type DoctorRecommendCreateRequest struct {
	DoctorID  uint `json:"doctor_id" validate:"required"`
	IsTop     bool `json:"is_top,optional"`
	SortOrder int  `json:"sort_order,optional"`
	Status    int  `json:"status,optional" validate:"oneof=0 1"`
}

// DoctorRecommendUpdateRequest 更新医生推荐请求
type DoctorRecommendUpdateRequest struct {
	ID        uint `json:"id" validate:"required"`
	DoctorID  uint `json:"doctor_id" validate:"required"`
	IsTop     bool `json:"is_top,optional"`
	SortOrder int  `json:"sort_order,optional"`
	Status    int  `json:"status,optional" validate:"oneof=0 1"`
}

// DoctorRecommendQueryParams 医生推荐查询参数
type DoctorRecommendQueryParams struct {
	Page       int    `json:"page" validate:"min=1"`
	PageSize   int    `json:"page_size" validate:"min=1,max=100"`
	DoctorName string `json:"doctor_name,optional"`
	Department string `json:"department,optional"`
	IsTop      *bool  `json:"is_top,optional"`
	Status     *int   `json:"status,optional"`
}

// DoctorRecommendListResponse 医生推荐列表响应
type DoctorRecommendListResponse struct {
	List  []*DoctorRecommendInfo `json:"list"`
	Total int64                  `json:"total"`
	Page  int                    `json:"page"`
	Size  int                    `json:"size"`
}

// DoctorRecommendInfo 医生推荐信息
type DoctorRecommendInfo struct {
	ID           uint      `json:"id"`
	DoctorID     uint      `json:"doctor_id"`
	DoctorName   string    `json:"doctor_name"`
	Department   string    `json:"department"`
	Hospital     string    `json:"hospital"`
	Title        string    `json:"title"`
	Specialty    string    `json:"specialty"`
	HeadImgUrl   string    `json:"head_img_url"`
	IsTop        bool      `json:"is_top"`
	SortOrder    int       `json:"sort_order"`
	Status       int       `json:"status"`
	CreatorID    uint      `json:"creator_id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// SetTopRequest 设置置顶请求
type SetTopRequest struct {
	ID uint `json:"id" validate:"required"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	IDs []uint `json:"ids" validate:"required,min=1"`
}

// DoctorSelectInfo 医生选择信息（用于选择医生弹窗）
type DoctorSelectInfo struct {
	DoctorID     uint   `json:"doctor_id"`
	Name         string `json:"name"`
	Department   string `json:"department"`
	Hospital     string `json:"hospital"`
	Title        string `json:"title"`
	Specialty    string `json:"specialty"`
	HeadImgUrl   string `json:"head_img_url"`
	IsRecommended bool  `json:"is_recommended"` // 是否已被推荐
}

// DoctorSelectQueryParams 医生选择查询参数
type DoctorSelectQueryParams struct {
	Page       int    `json:"page" validate:"min=1"`
	PageSize   int    `json:"page_size" validate:"min=1,max=100"`
	StoreID    uint   `json:"store_id,optional"`
	DoctorName string `json:"doctor_name,optional"`
	Department string `json:"department,optional"`
}

// DoctorSelectResponse 医生选择响应
type DoctorSelectResponse struct {
	List  []*DoctorSelectInfo `json:"list"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Size  int                 `json:"size"`
}
