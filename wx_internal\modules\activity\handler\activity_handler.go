package handler

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"strings"
	"time"

	"yekaitai/internal/modules/content/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/payment"
	"yekaitai/wx_internal/modules/activity/service"
	wxUtils "yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// ActivityHandler 活动处理器
type ActivityHandler struct {
	svcCtx *svc.ServiceContext
}

// NewActivityHandler 创建活动处理器
func NewActivityHandler(svcCtx *svc.ServiceContext) *ActivityHandler {
	return &ActivityHandler{
		svcCtx: svcCtx,
	}
}

// 活动列表请求
type ActivityListRequest struct {
	types.PageRequest
	IsRecommended *bool `form:"is_recommended,optional"` // 是否推荐筛选
}

// 活动详情请求
type ActivityDetailRequest struct {
	ActivityID uint    `path:"activityId"`        // 活动ID
	UserLat    float64 `form:"user_lat,optional"` // 用户纬度
	UserLng    float64 `form:"user_lng,optional"` // 用户经度
}

// 用户报名列表请求
type UserSignUpListRequest struct {
	types.PageRequest
}

// 报名信息请求
type SignUpInfoRequest struct {
	ActivityID uint `path:"activityId"` // 活动ID
}

// 立即报名请求
type SignUpRequest struct {
	ActivityID uint   `json:"activity_id"` // 活动ID
	Name       string `json:"name"`        // 姓名
	Phone      string `json:"phone"`       // 手机号
}

// 取消报名请求
type CancelSignUpRequest struct {
	OrderID uint `json:"order_id"` // 订单ID
}

// 已报名活动详情请求
type SignedActivityDetailRequest struct {
	OrderID uint `path:"orderId"` // 订单ID
}

// 最新活动弹框请求
type LatestActivityPopupRequest struct {
}

// 生成二维码请求
type GenerateQRCodeRequest struct {
	OrderID uint `json:"order_id"` // 订单ID
}

// 核销请求
type VerifyActivityRequest struct {
	OrderNo          string `json:"order_no"`          // 订单编号
	VerificationCode string `json:"verification_code"` // 核销码
	Sign             string `json:"sign"`              // 签名
	Timestamp        string `json:"timestamp"`         // 时间戳
	VerifierID       uint   `json:"verifier_id"`       // 核销人ID
	VerifierName     string `json:"verifier_name"`     // 核销人姓名
}

// 活动结算页请求
type ActivityCheckoutRequest struct {
	ActivityID uint   `json:"activity_id" validate:"required"` // 活动ID
	Name       string `json:"name" validate:"required"`        // 报名人姓名
	Phone      string `json:"phone" validate:"required"`       // 报名人手机号
}

// GetActivityList 获取活动列表
func (h *ActivityHandler) GetActivityList(w http.ResponseWriter, r *http.Request) {
	var req ActivityListRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析活动列表请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 设置默认分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	logx.Infof("获取活动列表: page=%d, size=%d, is_recommended=%v", req.Page, req.Size, req.IsRecommended)

	// 构建查询条件
	db := mysql.Slave().Model(&model.Content{})

	// 只查询活动类型且已上架的内容
	db = db.Where("type = ? AND is_enabled = ?", "activity", true)

	// 过滤已过期的活动（报名截止时间已过）
	db = db.Where("sign_up_deadline IS NULL OR sign_up_deadline > ?", time.Now())

	// 是否推荐筛选
	if req.IsRecommended != nil {
		db = db.Where("is_recommended = ?", *req.IsRecommended)
	}

	// 排序：推荐排序值优先，未设置的按创建时间倒序
	db = db.Order("CASE WHEN recommend_sort > 0 THEN recommend_sort ELSE 999999 END ASC, created_at DESC")

	// 分页查询
	var activities []model.Content
	var total int64

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logx.Errorf("查询活动总数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询失败"))
		return
	}

	// 分页查询
	offset := (req.Page - 1) * req.Size
	if err := db.Offset(offset).Limit(req.Size).Find(&activities).Error; err != nil {
		logx.Errorf("查询活动列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询失败"))
		return
	}

	// 构建返回数据
	result := make([]map[string]interface{}, len(activities))
	for i, activity := range activities {
		result[i] = h.buildActivityResponse(&activity)
	}

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"list":  result,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}, "获取活动列表成功"))
}

// GetActivityDetail 获取活动详情
func (h *ActivityHandler) GetActivityDetail(w http.ResponseWriter, r *http.Request) {
	var req ActivityDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析活动详情请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	logx.Infof("获取活动详情: activityId=%d, userLat=%f, userLng=%f", req.ActivityID, req.UserLat, req.UserLng)

	// 查询活动详情
	var activity model.Content
	if err := mysql.Slave().Where("id = ? AND type = ? AND is_enabled = ?", req.ActivityID, "activity", true).First(&activity).Error; err != nil {
		logx.Errorf("查询活动详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "活动不存在"))
		return
	}

	// 构建活动详情响应
	result := h.buildActivityDetailResponse(&activity, req.UserLat, req.UserLng)

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取活动详情成功"))
}

// buildActivityResponse 构建活动响应数据
func (h *ActivityHandler) buildActivityResponse(activity *model.Content) map[string]interface{} {
	return map[string]interface{}{
		"id":                  activity.ID,
		"title":               activity.Title,
		"type":                activity.Type,
		"format":              activity.Format,
		"description":         activity.Description,
		"content":             activity.Content,
		"cover_image":         activity.CoverImage,
		"video_url":           activity.VideoUrl,
		"is_enabled":          activity.IsEnabled,
		"is_recommended":      activity.IsRecommended,
		"recommend_sort":      activity.RecommendSort,
		"view_count":          activity.ViewCount,
		"report_count":        activity.ReportCount,
		"max_sign_up":         activity.MaxSignUp,
		"can_sign_up":         activity.CanSignUp,
		"sign_up_deadline":    activity.SignUpDeadline,
		"sign_up_count":       activity.SignUpCount,
		"sign_up_method":      activity.SignUpMethod,
		"is_new_activity":     activity.IsNewActivity,
		"is_special_activity": activity.IsSpecialActivity,
		"created_by":          activity.CreatedBy,
		"created_at":          activity.CreatedAt,
		"updated_at":          activity.UpdatedAt,
	}
}

// buildActivityDetailResponse 构建活动详情响应数据
func (h *ActivityHandler) buildActivityDetailResponse(activity *model.Content, userLat, userLng float64) map[string]interface{} {
	result := h.buildActivityResponse(activity)

	// 获取活动关联的门店信息
	stores, err := h.getActivityStores(activity.ID, userLat, userLng)
	if err != nil {
		logx.Errorf("获取活动门店信息失败: %v", err)
		stores = []map[string]interface{}{}
	}

	result["stores"] = stores
	return result
}

// getActivityStores 获取活动关联的门店信息（包含距离计算）
func (h *ActivityHandler) getActivityStores(activityID uint, userLat, userLng float64) ([]map[string]interface{}, error) {
	// 查询活动关联的门店
	var relations []model.ContentStoreRelation
	if err := mysql.Slave().Where("content_id = ?", activityID).Find(&relations).Error; err != nil {
		return nil, fmt.Errorf("查询活动门店关联失败: %v", err)
	}

	if len(relations) == 0 {
		return []map[string]interface{}{}, nil
	}

	// 获取门店ID列表
	storeIDs := make([]uint, len(relations))
	for i, relation := range relations {
		storeIDs[i] = relation.StoreID
	}

	// 查询门店详情
	var stores []struct {
		ID        uint    `json:"id"`
		Name      string  `json:"name"`
		Address   string  `json:"address"`
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
		Phone     string  `json:"phone"`
	}

	if err := mysql.Slave().Table("t_stores").Where("id IN ?", storeIDs).Find(&stores).Error; err != nil {
		return nil, fmt.Errorf("查询门店详情失败: %v", err)
	}

	result := make([]map[string]interface{}, len(stores))
	for i, store := range stores {
		distance := ""
		if userLat != 0 && userLng != 0 && store.Latitude != 0 && store.Longitude != 0 {
			// 计算距离
			distanceKm := calculateDistance(userLat, userLng, store.Latitude, store.Longitude)
			if distanceKm < 1 {
				distance = fmt.Sprintf("%.0fm", distanceKm*1000)
			} else {
				distance = fmt.Sprintf("%.1fkm", distanceKm)
			}
		}

		result[i] = map[string]interface{}{
			"id":        store.ID,
			"name":      store.Name,
			"address":   store.Address,
			"latitude":  store.Latitude,
			"longitude": store.Longitude,
			"phone":     store.Phone,
			"distance":  distance,
		}
	}

	return result, nil
}

// GetUserSignUpList 获取用户报名列表
func (h *ActivityHandler) GetUserSignUpList(w http.ResponseWriter, r *http.Request) {
	var req UserSignUpListRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析用户报名列表请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 设置默认分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	logx.Infof("获取用户报名列表: userID=%d, page=%d, size=%d", userID, req.Page, req.Size)

	// 查询用户的报名订单
	var orders []model.ContentSignUpOrder
	var total int64

	db := mysql.Slave().Model(&model.ContentSignUpOrder{}).Where("user_id = ?", userID)

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		logx.Errorf("查询报名订单总数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询失败"))
		return
	}

	// 分页查询，按创建时间倒序
	offset := (req.Page - 1) * req.Size
	if err := db.Order("created_at DESC").Offset(offset).Limit(req.Size).Find(&orders).Error; err != nil {
		logx.Errorf("查询报名订单列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询失败"))
		return
	}

	// 构建返回数据
	result := make([]map[string]interface{}, len(orders))
	for i, order := range orders {
		// 查询活动详情
		var activity model.Content
		if err := mysql.Slave().Where("id = ?", order.ContentID).First(&activity).Error; err != nil {
			logx.Errorf("查询活动详情失败: %v", err)
			continue
		}

		// 获取活动门店信息
		stores, _ := h.getActivityStores(activity.ID, 0, 0)

		result[i] = map[string]interface{}{
			"order_id":          order.ID,
			"order_no":          order.OrderNo,
			"content_id":        order.ContentID,
			"activity_title":    activity.Title,
			"activity_cover":    activity.CoverImage,
			"activity_deadline": activity.SignUpDeadline,
			"stores":            stores,
			"amount":            order.Amount,
			"status":            order.Status,
			"status_text":       h.getOrderStatusText(order.Status),
			"verification_code": order.VerificationCode,
			"verification_time": order.VerificationTime,
			"created_at":        order.CreatedAt,
			"updated_at":        order.UpdatedAt,
		}
	}

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"list":  result,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}, "获取报名列表成功"))
}

// GetSignUpInfo 获取报名信息
func (h *ActivityHandler) GetSignUpInfo(w http.ResponseWriter, r *http.Request) {
	var req SignUpInfoRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 获取用户ID
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	logx.Infof("获取报名信息: userID=%d, activityId=%d", userID, req.ActivityID)

	// 查询活动详情
	var activity model.Content
	if err := mysql.Slave().Where("id = ? AND type = ? AND is_enabled = ?", req.ActivityID, "activity", true).First(&activity).Error; err != nil {
		logx.Errorf("查询活动详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "活动不存在"))
		return
	}

	// 注意：这里不再直接返回错误，而是在后面的逻辑中通过 can_signup 字段告知前端是否可以报名

	// 获取用户信息
	var user struct {
		Nickname string `json:"nickname"`
		Mobile   string `json:"mobile"`
	}
	if err := mysql.Slave().Table("wx_user").Select("nickname, mobile").Where("user_id = ?", userID).First(&user).Error; err != nil {
		logx.Errorf("查询用户信息失败: %v", err)
		user.Nickname = ""
		user.Mobile = ""
	}

	// 检查用户是否已经报名
	var existingOrder model.ContentSignUpOrder
	hasSignedUp := false
	err := mysql.Slave().Where("content_id = ? AND user_id = ? AND status IN (1, 4)", req.ActivityID, userID).First(&existingOrder).Error
	if err == nil {
		hasSignedUp = true
		// 如果已报名，使用订单中的姓名和手机号
		if existingOrder.Name != "" {
			user.Nickname = existingOrder.Name
		}
		if existingOrder.Phone != "" {
			user.Mobile = existingOrder.Phone
		}
	}

	// 计算剩余名额
	remainingQuota := activity.MaxSignUp - activity.SignUpCount
	if remainingQuota < 0 {
		remainingQuota = 0
	}

	// 判断是否可以报名
	canSignUp := !hasSignedUp &&
		activity.CanSignUp &&
		(activity.SignUpDeadline == nil || activity.SignUpDeadline.After(time.Now())) &&
		remainingQuota > 0

	// 构建返回数据
	result := map[string]interface{}{
		"activity_id":     activity.ID,
		"activity_title":  activity.Title,
		"user_name":       user.Nickname,
		"user_phone":      user.Mobile,
		"can_signup":      canSignUp,
		"signup_deadline": activity.SignUpDeadline,
		"remaining_quota": remainingQuota,
	}

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取报名信息成功"))
}

// getOrderStatusText 获取订单状态文本
func (h *ActivityHandler) getOrderStatusText(status int) string {
	switch status {
	case 1:
		return "已报名"
	case 2:
		return "已取消"
	case 3:
		return "已取消"
	case 4:
		return "已核销"
	default:
		return "未知状态"
	}
}

// calculateDistance 计算两个经纬度之间的距离（单位：公里）
func calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const earthRadius = 6371 // 地球半径，单位：公里

	// 将角度转换为弧度
	lat1Rad := lat1 * math.Pi / 180
	lng1Rad := lng1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lng2Rad := lng2 * math.Pi / 180

	// 计算差值
	deltaLat := lat2Rad - lat1Rad
	deltaLng := lng2Rad - lng1Rad

	// 使用 Haversine 公式计算距离
	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(deltaLng/2)*math.Sin(deltaLng/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return earthRadius * c
}

// SignUp 立即报名
func (h *ActivityHandler) SignUp(w http.ResponseWriter, r *http.Request) {
	var req SignUpRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析立即报名请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID和OpenID
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 获取用户OpenID
	userOpenID, ok := h.getUserOpenID(r)
	if !ok {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "获取用户信息失败"))
		return
	}

	logx.Infof("用户报名: userID=%d, activityId=%d, name=%s, phone=%s", userID, req.ActivityID, req.Name, req.Phone)

	// 查询活动详情
	var activity model.Content
	if err := mysql.Slave().Where("id = ? AND type = ? AND is_enabled = ?", req.ActivityID, "activity", true).First(&activity).Error; err != nil {
		logx.Errorf("查询活动详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "活动不存在"))
		return
	}

	// 验证活动状态
	if err := h.validateActivityForSignUp(&activity, userID); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 创建订单
	order, err := h.createActivityOrderRecord(&activity, userID, req.Name, req.Phone)
	if err != nil {
		logx.Errorf("创建活动订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建订单失败"))
		return
	}

	// 生成支付参数
	paymentParams, needPayment, err := h.generateActivityPaymentParams(order, userOpenID)
	if err != nil {
		logx.Errorf("生成支付参数失败: %v", err)
		// 提取具体的错误信息
		errorMsg := h.extractPaymentError(err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, errorMsg))
		return
	}

	// 返回订单信息和支付参数
	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"order_id":       order.ID,
		"order_no":       order.OrderNo,
		"total_amount":   order.Amount,
		"pay_amount":     order.Amount,
		"need_payment":   needPayment,
		"payment_params": paymentParams,
	}, "报名成功"))
}

// CancelSignUp 取消报名
func (h *ActivityHandler) CancelSignUp(w http.ResponseWriter, r *http.Request) {
	var req CancelSignUpRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 获取用户ID
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	logx.Infof("取消报名: userID=%d, orderId=%d", userID, req.OrderID)

	// 查询订单信息，用于积分回收
	var order model.ContentSignUpOrder
	if err := mysql.Slave().Where("id = ? AND user_id = ?", req.OrderID, userID).First(&order).Error; err != nil {
		logx.Errorf("查询订单信息失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在"))
		return
	}

	// 使用活动退款服务处理取消报名
	refundService := service.NewActivityRefundService()
	err := refundService.RefundActivityOrder(r.Context(), req.OrderID, "用户主动取消报名")
	if err != nil {
		logx.Errorf("取消报名失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 取消报名成功后，回收积分奖励
	h.processActivitySignUpRewardRefund(userID, order.OrderNo)

	httpx.OkJson(w, types.NewSuccessResponse(nil, "取消报名成功"))
}

// GetSignedActivityDetail 获取已报名活动详情
func (h *ActivityHandler) GetSignedActivityDetail(w http.ResponseWriter, r *http.Request) {
	var req SignedActivityDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 获取用户ID
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	logx.Infof("获取已报名活动详情: userID=%d, orderId=%d", userID, req.OrderID)

	// 查询订单详情
	var order model.ContentSignUpOrder
	if err := mysql.Slave().Where("id = ? AND user_id = ?", req.OrderID, userID).First(&order).Error; err != nil {
		logx.Errorf("查询订单详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在"))
		return
	}

	// 查询活动详情
	var activity model.Content
	if err := mysql.Slave().Where("id = ?", order.ContentID).First(&activity).Error; err != nil {
		logx.Errorf("查询活动详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "活动不存在"))
		return
	}

	// 获取活动门店信息
	stores, _ := h.getActivityStores(activity.ID, 0, 0)

	// 构建返回数据
	result := map[string]interface{}{
		// 活动信息
		"activity": h.buildActivityResponse(&activity),
		"stores":   stores,

		// 订单信息
		"order": map[string]interface{}{
			"id":                order.ID,
			"order_no":          order.OrderNo,
			"content_id":        order.ContentID,
			"user_id":           order.UserID,
			"name":              order.Name,
			"phone":             order.Phone,
			"amount":            order.Amount,
			"status":            order.Status,
			"status_text":       h.getOrderStatusText(order.Status),
			"payment_time":      order.PaymentTime,
			"payment_type":      order.PaymentType,
			"refund_time":       order.RefundTime,
			"refund_amount":     order.RefundAmount,
			"verification_code": order.VerificationCode,
			"verification_time": order.VerificationTime,
			"verifier_id":       order.VerifierID,
			"verifier_name":     order.VerifierName,
			"qr_code_url":       order.QRCodeURL,
			"created_at":        order.CreatedAt,
			"updated_at":        order.UpdatedAt,
		},
	}

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取已报名活动详情成功"))
}

// GetLatestActivityPopup 获取最新活动弹框
func (h *ActivityHandler) GetLatestActivityPopup(w http.ResponseWriter, r *http.Request) {
	var req LatestActivityPopupRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	logx.Info("获取最新活动弹框")

	// 查询最新的已上架已推荐的活动
	var activity model.Content
	err := mysql.Slave().Where("type = ? AND is_enabled = ? AND is_recommended = ? AND is_new_activity = ?",
		"activity", true, true, true).
		Order("created_at DESC").
		First(&activity).Error

	if err != nil {
		// 没有找到符合条件的活动，返回空
		httpx.OkJson(w, types.NewSuccessResponse(nil, "暂无最新活动"))
		return
	}

	// 构建返回数据
	result := h.buildActivityResponse(&activity)

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取最新活动成功"))
}

// GenerateQRCode 生成二维码
func (h *ActivityHandler) GenerateQRCode(w http.ResponseWriter, r *http.Request) {
	var req GenerateQRCodeRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 获取用户ID
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	logx.Infof("生成二维码: userID=%d, orderId=%d", userID, req.OrderID)

	// 查询订单详情
	var order model.ContentSignUpOrder
	if err := mysql.Slave().Where("id = ? AND user_id = ?", req.OrderID, userID).First(&order).Error; err != nil {
		logx.Errorf("查询订单详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在"))
		return
	}

	// 检查订单状态
	if order.Status != 1 { // 只有已报名状态才能生成二维码
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单状态不允许生成二维码"))
		return
	}

	// 使用活动二维码服务生成二维码
	qrCodeService := service.NewActivityQRCodeService()
	qrCodeURL, err := qrCodeService.GenerateActivityQRCode(r.Context(), req.OrderID)
	if err != nil {
		logx.Errorf("生成二维码失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "生成二维码失败"))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"qr_code_url": qrCodeURL,
	}, "生成二维码成功"))
}

// VerifyActivity 核销活动
func (h *ActivityHandler) VerifyActivity(w http.ResponseWriter, r *http.Request) {
	var req VerifyActivityRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	logx.Infof("核销活动: orderNo=%s, verificationCode=%s, verifierID=%d",
		req.OrderNo, req.VerificationCode, req.VerifierID)

	// 使用活动二维码服务进行核销
	qrCodeService := service.NewActivityQRCodeService()
	err := qrCodeService.VerifyActivityCode(r.Context(), req.OrderNo, req.VerificationCode, req.VerifierID, req.VerifierName)
	if err != nil {
		logx.Errorf("核销活动失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"order_no":          req.OrderNo,
		"verification_time": time.Now(),
		"verifier_name":     req.VerifierName,
	}, "核销成功"))
}

// getUserOpenID 从请求中获取用户OpenID
func (h *ActivityHandler) getUserOpenID(r *http.Request) (string, bool) {
	openIDValue := r.Context().Value("openid")
	logx.Infof("获取OpenID: openIDValue=%v", openIDValue)

	if openIDValue == nil {
		logx.Errorf("OpenID为空: context中没有openid")
		return "", false
	}

	openID, ok := openIDValue.(string)
	if !ok {
		logx.Errorf("OpenID类型转换失败: openIDValue=%v, type=%T", openIDValue, openIDValue)
		return "", false
	}

	if openID == "" {
		logx.Errorf("OpenID为空字符串")
		return "", false
	}

	logx.Infof("获取到OpenID: %s", openID)
	return openID, true
}

// validateActivityForSignUp 验证活动是否可以报名
func (h *ActivityHandler) validateActivityForSignUp(activity *model.Content, userID uint) error {
	// 检查活动是否可以报名
	if !activity.CanSignUp {
		return fmt.Errorf("该活动不支持报名")
	}

	// 检查报名截止时间
	if activity.SignUpDeadline != nil && activity.SignUpDeadline.Before(time.Now()) {
		return fmt.Errorf("报名已截止")
	}

	// 检查报名人数是否已满
	if activity.SignUpCount >= activity.MaxSignUp {
		return fmt.Errorf("报名人数已满")
	}

	// 检查用户是否已经报名
	var existingOrder model.ContentSignUpOrder
	err := mysql.Slave().Where("content_id = ? AND user_id = ? AND status IN (1, 4)", activity.ID, userID).First(&existingOrder).Error
	if err == nil {
		return fmt.Errorf("您已经报名过该活动")
	}

	return nil
}

// createActivityOrderRecord 创建活动订单记录
func (h *ActivityHandler) createActivityOrderRecord(activity *model.Content, userID uint, name, phone string) (*model.ContentSignUpOrder, error) {
	// 生成订单号
	orderNo := fmt.Sprintf("ACT%d%06d", time.Now().Unix(), time.Now().Nanosecond()%1000000)

	// 创建订单记录
	order := &model.ContentSignUpOrder{
		OrderNo:   orderNo,
		ContentID: activity.ID,
		UserID:    userID,
		Name:      name,
		Phone:     phone,
		Amount:    activity.SignUpAmount, // 使用活动配置的报名费用
		Status:    1,                     // 已报名
	}

	// 保存到数据库
	if err := mysql.Master().Create(order).Error; err != nil {
		return nil, fmt.Errorf("保存订单失败: %w", err)
	}

	// 更新活动报名人数
	if err := mysql.Master().Model(activity).UpdateColumn("sign_up_count", activity.SignUpCount+1).Error; err != nil {
		logx.Errorf("更新活动报名人数失败: %v", err)
		// 不阻断订单创建流程
	}

	return order, nil
}

// generateActivityPaymentParams 生成活动支付参数
func (h *ActivityHandler) generateActivityPaymentParams(order *model.ContentSignUpOrder, userOpenID string) (map[string]string, bool, error) {
	// 使用通用支付服务
	paymentService := payment.GetGlobalPaymentService()
	if paymentService == nil {
		logx.Errorf("支付服务未初始化")
		return nil, false, fmt.Errorf("支付服务未初始化")
	}

	req := &payment.CreatePaymentRequest{
		OrderID:      order.ID,
		OrderNo:      order.OrderNo,
		Amount:       order.Amount,
		Description:  fmt.Sprintf("活动报名-%s", order.OrderNo),
		OpenID:       userOpenID,
		Attach:       fmt.Sprintf("activity_order_id:%d", order.ID),
		BusinessType: "activity",
	}

	logx.Infof("创建活动支付参数: orderNo=%s, amount=%d, openID=%s", order.OrderNo, order.Amount, userOpenID)

	paymentParams, err := paymentService.CreatePayment(context.Background(), req)
	if err != nil {
		logx.Errorf("创建活动支付参数失败: orderNo=%s, error=%v", order.OrderNo, err)
		return nil, false, err
	}

	// 如果是免费活动，更新订单状态并处理积分奖励
	if !paymentParams.NeedPayment {
		now := time.Now()
		updates := map[string]interface{}{
			"status":       1, // 已报名
			"payment_time": &now,
		}
		if err := mysql.Master().Model(order).Updates(updates).Error; err != nil {
			logx.Errorf("更新免费订单状态失败: %v", err)
		} else {
			// 免费活动报名成功，根据活动类型处理积分奖励
			h.processActivitySignUpRewardWithType(order.UserID, order.ContentID, order.OrderNo, "免费活动报名")
		}
	}

	return paymentParams.PaymentParams, paymentParams.NeedPayment, nil
}

// GetActivityCheckout 获取活动结算页信息
func (h *ActivityHandler) GetActivityCheckout(w http.ResponseWriter, r *http.Request) {
	var req ActivityCheckoutRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 获取用户ID
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	logx.Infof("获取活动结算页信息: userID=%d, activityId=%d, name=%s, phone=%s", userID, req.ActivityID, req.Name, req.Phone)

	// 查询活动详情
	var activity model.Content
	if err := mysql.Slave().Where("id = ? AND type = ? AND is_enabled = ?", req.ActivityID, "activity", true).First(&activity).Error; err != nil {
		logx.Errorf("查询活动详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "活动不存在"))
		return
	}

	// 验证活动状态
	if err := h.validateActivityForSignUp(&activity, userID); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 计算费用信息
	checkoutInfo := h.calculateActivityCheckout(&activity, req.Name, req.Phone)

	httpx.OkJson(w, types.NewSuccessResponse(checkoutInfo, "获取结算信息成功"))
}

// calculateActivityCheckout 计算活动结算信息
func (h *ActivityHandler) calculateActivityCheckout(activity *model.Content, name, phone string) map[string]interface{} {
	// 活动基础费用（使用活动配置的报名费用）
	baseAmount := activity.SignUpAmount

	// 计算总费用
	totalAmount := baseAmount

	// 优惠信息（活动不支持优惠券和积分）
	discountAmount := 0
	finalAmount := totalAmount - discountAmount

	// 构建结算信息
	checkoutInfo := map[string]interface{}{
		// 活动信息
		"activity_info": map[string]interface{}{
			"id":          activity.ID,
			"title":       activity.Title,
			"cover_image": activity.CoverImage,
			"description": activity.Description,
			"deadline":    activity.SignUpDeadline,
		},
		// 报名人信息
		"signup_info": map[string]interface{}{
			"name":  name,
			"phone": phone,
		},
		// 费用信息
		"price_info": map[string]interface{}{
			"base_amount":     baseAmount,      // 基础费用（分）
			"discount_amount": discountAmount,  // 优惠金额（分）
			"total_amount":    totalAmount,     // 总费用（分）
			"final_amount":    finalAmount,     // 最终费用（分）
			"need_payment":    finalAmount > 0, // 是否需要支付
		},
		// 优惠信息（活动不支持）
		"discount_info": map[string]interface{}{
			"can_use_coupon":    false,           // 不支持优惠券
			"can_use_points":    false,           // 不支持积分
			"available_coupons": []interface{}{}, // 可用优惠券列表
			"available_points":  0,               // 可用积分
		},
		// 支付方式
		"payment_methods": []map[string]interface{}{
			{
				"type":        "wechat_pay",
				"name":        "微信支付",
				"icon":        "wechat_pay_icon",
				"enabled":     true,
				"description": "使用微信支付完成付款",
			},
		},
	}

	return checkoutInfo
}

// extractPaymentError 提取支付错误的具体信息
func (h *ActivityHandler) extractPaymentError(err error) string {
	if err == nil {
		return "未知错误"
	}

	errStr := err.Error()
	logx.Infof("提取支付错误信息: %s", errStr)

	// 检查常见的错误类型
	switch {
	case strings.Contains(errStr, "无效的openid"):
		return "用户身份验证失败，请重新登录"
	case strings.Contains(errStr, "PARAM_ERROR"):
		if strings.Contains(errStr, "openid") {
			return "用户身份验证失败，请重新登录"
		}
		return "请求参数错误"
	case strings.Contains(errStr, "支付服务未初始化"):
		return "支付服务暂时不可用，请稍后重试"
	case strings.Contains(errStr, "微信支付服务未初始化"):
		return "微信支付服务暂时不可用，请稍后重试"
	case strings.Contains(errStr, "APPID_NOT_EXIST"):
		return "支付配置错误，请联系客服"
	case strings.Contains(errStr, "MCH_NOT_EXIST"):
		return "商户配置错误，请联系客服"
	case strings.Contains(errStr, "SIGN_ERROR"):
		return "支付签名错误，请联系客服"
	case strings.Contains(errStr, "SYSTEMERROR"):
		return "支付系统繁忙，请稍后重试"
	case strings.Contains(errStr, "ORDERPAID"):
		return "订单已支付，请勿重复支付"
	case strings.Contains(errStr, "ORDERNOTEXIST"):
		return "订单不存在"
	case strings.Contains(errStr, "OUT_TRADE_NO_USED"):
		return "订单号重复，请重新提交"
	case strings.Contains(errStr, "NOAUTH"):
		return "商户无权限，请联系客服"
	case strings.Contains(errStr, "AMOUNT_LIMIT"):
		return "金额超限，请联系客服"
	case strings.Contains(errStr, "FREQUENCY_LIMITED"):
		return "频率限制，请稍后重试"
	default:
		// 如果是网络错误或其他未知错误，返回通用错误信息
		if strings.Contains(errStr, "timeout") || strings.Contains(errStr, "connection") {
			return "网络连接超时，请检查网络后重试"
		}
		return "支付服务暂时不可用，请稍后重试"
	}
}

// processActivitySignUpReward 处理活动报名积分奖励
func (h *ActivityHandler) processActivitySignUpReward(userID uint, orderNo, description string) {
	// 使用积分奖励助手
	coinHelper := NewCoinRewardHelper()
	coinHelper.ProcessActivitySignUpReward(userID, orderNo, description)
}

// processActivitySignUpRewardWithType 根据活动类型和后台配置处理活动报名积分奖励
func (h *ActivityHandler) processActivitySignUpRewardWithType(userID uint, activityID uint, orderNo, description string) {
	// 查询活动信息，判断是否为专项活动
	var activity struct {
		IsSpecialActivity bool `json:"is_special_activity"`
	}

	err := mysql.Slave().Table("t_contents").
		Select("is_special_activity").
		Where("id = ? AND type = 'activity'", activityID).
		First(&activity).Error

	if err != nil {
		logx.Errorf("查询活动信息失败: activityID=%d, error=%v", activityID, err)
		return
	}

	// 检查后台积分奖励配置
	normalActivityEnabled := h.isActivityRewardEnabled("ACTIVITY_SIGNUP")
	specialActivityEnabled := h.isActivityRewardEnabled("SPECIAL_ACTIVITY_SIGNUP")

	// 使用积分奖励助手
	coinHelper := NewCoinRewardHelper()

	if activity.IsSpecialActivity {
		// 专项活动：根据配置给予奖励
		if normalActivityEnabled {
			coinHelper.ProcessActivitySignUpReward(userID, orderNo, description)
			logx.Infof("专项活动报名，给予普通活动奖励: userID=%d, activityID=%d", userID, activityID)
		}
		if specialActivityEnabled {
			coinHelper.ProcessSpecialActivitySignUpReward(userID, orderNo, description+" - 专项活动额外奖励")
			logx.Infof("专项活动报名，给予专项活动额外奖励: userID=%d, activityID=%d", userID, activityID)
		}
		if !normalActivityEnabled && !specialActivityEnabled {
			logx.Infof("专项活动报名，但积分奖励未启用: userID=%d, activityID=%d", userID, activityID)
		}
	} else {
		// 普通活动：只检查普通活动奖励配置
		if normalActivityEnabled {
			coinHelper.ProcessActivitySignUpReward(userID, orderNo, description)
			logx.Infof("普通活动报名，给予基础积分奖励: userID=%d, activityID=%d", userID, activityID)
		} else {
			logx.Infof("普通活动报名，但积分奖励未启用: userID=%d, activityID=%d", userID, activityID)
		}
	}
}

// isActivityRewardEnabled 检查指定类型的活动积分奖励是否启用
func (h *ActivityHandler) isActivityRewardEnabled(activityType string) bool {
	var count int64
	err := mysql.Slave().Table("coin_rules").
		Where("activity_type = ? AND enabled = 1", activityType).
		Count(&count).Error

	if err != nil {
		logx.Errorf("检查积分奖励配置失败: activityType=%s, error=%v", activityType, err)
		return false
	}

	return count > 0
}

// processActivitySignUpRewardRefund 处理活动报名积分奖励回收
func (h *ActivityHandler) processActivitySignUpRewardRefund(userID uint, orderNo string) {
	// 使用积分奖励助手
	coinHelper := NewCoinRewardHelper()
	coinHelper.ProcessActivitySignUpRewardRefund(userID, orderNo)
}
