package handler

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"

	jst "yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// InventoryHandler 聚水潭库存处理器
type InventoryHandler struct {
	ctx    *svc.WxServiceContext
	client *jst.Client
}

// NewInventoryHandler 创建聚水潭库存处理器
func NewInventoryHandler(ctx *svc.WxServiceContext) *InventoryHandler {
	return &InventoryHandler{
		ctx:    ctx,
		client: ctx.JushuitanClient,
	}
}

// QueryInventory 商品库存查询处理
func (h *InventoryHandler) QueryInventory(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 商品库存查询原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.InventoryQueryRequest{}

	// 处理wms_co_id
	if wmsCoID, ok := rawReq["wms_co_id"]; ok && wmsCoID != nil {
		switch v := wmsCoID.(type) {
		case float64:
			req.WmsCoID = int(v)
		case string:
			if coID, err := strconv.Atoi(v); err == nil {
				req.WmsCoID = coID
			}
		case int:
			req.WmsCoID = v
		}
	}

	// 处理page_index
	if pageIndex, ok := rawReq["page_index"]; ok && pageIndex != nil {
		switch v := pageIndex.(type) {
		case float64:
			req.PageIndex = int(v)
		case string:
			if idx, err := strconv.Atoi(v); err == nil {
				req.PageIndex = idx
			}
		case int:
			req.PageIndex = v
		}
	}

	// 处理page_size
	if pageSize, ok := rawReq["page_size"]; ok && pageSize != nil {
		switch v := pageSize.(type) {
		case float64:
			req.PageSize = int(v)
		case string:
			if size, err := strconv.Atoi(v); err == nil {
				req.PageSize = size
			}
		case int:
			req.PageSize = v
		}
	}

	// 处理字符串类型字段
	if modifiedBegin, ok := rawReq["modified_begin"]; ok && modifiedBegin != nil {
		req.ModifiedBegin = fmt.Sprintf("%v", modifiedBegin)
	}
	if modifiedEnd, ok := rawReq["modified_end"]; ok && modifiedEnd != nil {
		req.ModifiedEnd = fmt.Sprintf("%v", modifiedEnd)
	}
	if skuIDs, ok := rawReq["sku_ids"]; ok && skuIDs != nil {
		req.SkuIDs = fmt.Sprintf("%v", skuIDs)
	}
	if names, ok := rawReq["names"]; ok && names != nil {
		req.Names = fmt.Sprintf("%v", names)
	}
	if iIDs, ok := rawReq["i_ids"]; ok && iIDs != nil {
		req.IIDs = fmt.Sprintf("%v", iIDs)
	}

	// 处理has_lock_qty
	if hasLockQty, ok := rawReq["has_lock_qty"]; ok && hasLockQty != nil {
		switch v := hasLockQty.(type) {
		case bool:
			req.HasLockQty = v
		case string:
			req.HasLockQty = strings.ToLower(v) == "true"
		case float64:
			req.HasLockQty = v > 0
		}
	}

	// 处理ts
	if ts, ok := rawReq["ts"]; ok && ts != nil {
		switch v := ts.(type) {
		case float64:
			req.TS = int64(v)
		case string:
			if tsInt, err := strconv.ParseInt(v, 10, 64); err == nil {
				req.TS = tsInt
			}
		case int64:
			req.TS = v
		}
	}

	LogRequest(r, req)

	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 校验修改时间范围
	if (req.ModifiedBegin != "" || req.ModifiedEnd != "") && (req.ModifiedBegin == "" || req.ModifiedEnd == "") {
		err := fmt.Errorf("修改起始时间和结束时间必须同时存在")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 校验参数
	if req.ModifiedBegin == "" && req.ModifiedEnd == "" && req.SkuIDs == "" && req.Names == "" && req.IIDs == "" && req.TS == 0 {
		err := fmt.Errorf("查询条件不能为空，至少需要一个条件（修改时间范围、商品编码、商品名称、款式编码或时间戳）")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.QueryInventory(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 商品库存查询原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "商品库存查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryPack 箱及仓位库存查询处理
func (h *InventoryHandler) QueryPack(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 箱及仓位库存查询原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.PackQueryRequest{}

	// 处理wms_co_id
	if wmsCoID, ok := rawReq["wms_co_id"]; ok && wmsCoID != nil {
		switch v := wmsCoID.(type) {
		case float64:
			req.WmsCoID = int(v)
		case string:
			if coID, err := strconv.Atoi(v); err == nil {
				req.WmsCoID = coID
			}
		case int:
			req.WmsCoID = v
		}
	}

	// 处理pack_type
	if packType, ok := rawReq["pack_type"]; ok && packType != nil {
		req.PackType = fmt.Sprintf("%v", packType)
	}

	// 处理page_index
	if pageIndex, ok := rawReq["page_index"]; ok && pageIndex != nil {
		switch v := pageIndex.(type) {
		case float64:
			req.PageIndex = int(v)
		case string:
			if idx, err := strconv.Atoi(v); err == nil {
				req.PageIndex = idx
			}
		case int:
			req.PageIndex = v
		}
	}

	// 处理page_size
	if pageSize, ok := rawReq["page_size"]; ok && pageSize != nil {
		switch v := pageSize.(type) {
		case float64:
			req.PageSize = int(v)
		case string:
			if size, err := strconv.Atoi(v); err == nil {
				req.PageSize = size
			}
		case int:
			req.PageSize = v
		}
	}

	// 处理owner_co_id
	if ownerCoID, ok := rawReq["owner_co_id"]; ok && ownerCoID != nil {
		switch v := ownerCoID.(type) {
		case float64:
			req.OwnerCoID = int(v)
		case string:
			if coID, err := strconv.Atoi(v); err == nil {
				req.OwnerCoID = coID
			}
		case int:
			req.OwnerCoID = v
		}
	}

	// 处理字符串类型字段
	if startTime, ok := rawReq["start_time"]; ok && startTime != nil {
		req.StartTime = fmt.Sprintf("%v", startTime)
	}
	if endTime, ok := rawReq["end_time"]; ok && endTime != nil {
		req.EndTime = fmt.Sprintf("%v", endTime)
	}

	// 处理sku_ids（数组）
	if skuIDsRaw, ok := rawReq["sku_ids"]; ok && skuIDsRaw != nil {
		if skuIDsArr, ok := skuIDsRaw.([]interface{}); ok {
			for _, item := range skuIDsArr {
				req.SkuIDs = append(req.SkuIDs, fmt.Sprintf("%v", item))
			}
		}
	}

	LogRequest(r, req)

	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 200 {
		req.PageSize = 200
	}

	// 校验查询条件
	if len(req.SkuIDs) == 0 && req.StartTime == "" && req.EndTime == "" {
		err := fmt.Errorf("查询条件不能为空，至少需要一个条件（商品编码或修改时间范围）")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 校验时间范围
	if (req.StartTime != "" || req.EndTime != "") && (req.StartTime == "" || req.EndTime == "") {
		err := fmt.Errorf("修改起始时间和结束时间必须同时存在")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.QueryPack(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 箱及仓位库存查询原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "箱及仓位库存查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// UploadInventory 新建盘点单处理
func (h *InventoryHandler) UploadInventory(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 新建盘点单原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.InventoryUploadRequest{}

	// 处理wms_co_id
	if wmsCoID, ok := rawReq["wms_co_id"]; ok && wmsCoID != nil {
		switch v := wmsCoID.(type) {
		case float64:
			req.WmsCoID = int(v)
		case string:
			if coID, err := strconv.Atoi(v); err == nil {
				req.WmsCoID = coID
			}
		case int:
			req.WmsCoID = v
		}
	}

	// 处理type
	if typeVal, ok := rawReq["type"]; ok && typeVal != nil {
		req.Type = fmt.Sprintf("%v", typeVal)
	}

	// 处理is_confirm
	if isConfirm, ok := rawReq["is_confirm"]; ok && isConfirm != nil {
		switch v := isConfirm.(type) {
		case bool:
			req.IsConfirm = v
		case string:
			req.IsConfirm = strings.ToLower(v) == "true"
		case float64:
			req.IsConfirm = v > 0
		}
	}

	// 处理字符串类型字段
	if soID, ok := rawReq["so_id"]; ok && soID != nil {
		req.SoID = fmt.Sprintf("%v", soID)
	}
	if warehouse, ok := rawReq["warehouse"]; ok && warehouse != nil {
		req.Warehouse = fmt.Sprintf("%v", warehouse)
	}
	if remark, ok := rawReq["remark"]; ok && remark != nil {
		req.Remark = fmt.Sprintf("%v", remark)
	}
	if bin, ok := rawReq["bin"]; ok && bin != nil {
		req.Bin = fmt.Sprintf("%v", bin)
	}
	if defaultType, ok := rawReq["default_type"]; ok && defaultType != nil {
		req.DefaultType = fmt.Sprintf("%v", defaultType)
	}

	// 处理items（数组）
	if itemsRaw, ok := rawReq["items"]; ok && itemsRaw != nil {
		if itemsArr, ok := itemsRaw.([]interface{}); ok {
			for _, itemRaw := range itemsArr {
				if itemMap, ok := itemRaw.(map[string]interface{}); ok {
					item := jst.InventoryUploadItem{}

					// 处理sku_id
					if skuID, ok := itemMap["sku_id"]; ok && skuID != nil {
						item.SkuID = fmt.Sprintf("%v", skuID)
					}

					// 处理qty
					if qty, ok := itemMap["qty"]; ok && qty != nil {
						switch v := qty.(type) {
						case float64:
							item.Qty = int(v)
						case string:
							if qtyInt, err := strconv.Atoi(v); err == nil {
								item.Qty = qtyInt
							}
						case int:
							item.Qty = v
						}
					}

					// 处理supplier_id
					if supplierID, ok := itemMap["supplier_id"]; ok && supplierID != nil {
						switch v := supplierID.(type) {
						case float64:
							item.SupplierID = int(v)
						case string:
							if sid, err := strconv.Atoi(v); err == nil {
								item.SupplierID = sid
							}
						case int:
							item.SupplierID = v
						}
					}

					// 处理字符串类型字段
					if batchID, ok := itemMap["batch_id"]; ok && batchID != nil {
						item.BatchID = fmt.Sprintf("%v", batchID)
					}
					if producedDate, ok := itemMap["produced_date"]; ok && producedDate != nil {
						item.ProducedDate = fmt.Sprintf("%v", producedDate)
					}
					if expirationDate, ok := itemMap["expiration_date"]; ok && expirationDate != nil {
						item.ExpirationDate = fmt.Sprintf("%v", expirationDate)
					}

					// 处理sku_sns（数组）
					if skuSnsRaw, ok := itemMap["sku_sns"]; ok && skuSnsRaw != nil {
						if skuSnsArr, ok := skuSnsRaw.([]interface{}); ok {
							for _, sn := range skuSnsArr {
								item.SkuSns = append(item.SkuSns, fmt.Sprintf("%v", sn))
							}
						}
					}

					req.Items = append(req.Items, item)
				}
			}
		}
	}

	LogRequest(r, req)

	// 参数校验
	if req.SoID == "" {
		err := fmt.Errorf("外部单号不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}
	if req.Warehouse == "" {
		err := fmt.Errorf("仓库不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}
	if len(req.Items) == 0 {
		err := fmt.Errorf("商品明细不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.UploadInventory(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 新建盘点单原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "新建盘点单失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryInventoryCount 库存盘点查询处理
func (h *InventoryHandler) QueryInventoryCount(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 库存盘点查询原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.InventoryCountQueryRequest{}

	// 处理page_index
	if pageIndex, ok := rawReq["page_index"]; ok && pageIndex != nil {
		switch v := pageIndex.(type) {
		case float64:
			req.PageIndex = int(v)
		case string:
			if idx, err := strconv.Atoi(v); err == nil {
				req.PageIndex = idx
			}
		case int:
			req.PageIndex = v
		}
	}

	// 处理page_size
	if pageSize, ok := rawReq["page_size"]; ok && pageSize != nil {
		switch v := pageSize.(type) {
		case float64:
			req.PageSize = int(v)
		case string:
			if size, err := strconv.Atoi(v); err == nil {
				req.PageSize = size
			}
		case int:
			req.PageSize = v
		}
	}

	// 处理字符串类型字段
	if modifiedBegin, ok := rawReq["modified_begin"]; ok && modifiedBegin != nil {
		req.ModifiedBegin = fmt.Sprintf("%v", modifiedBegin)
	}
	if modifiedEnd, ok := rawReq["modified_end"]; ok && modifiedEnd != nil {
		req.ModifiedEnd = fmt.Sprintf("%v", modifiedEnd)
	}
	if ioIDs, ok := rawReq["io_ids"]; ok && ioIDs != nil {
		req.IoIDs = fmt.Sprintf("%v", ioIDs)
	}
	if status, ok := rawReq["status"]; ok && status != nil {
		req.Status = fmt.Sprintf("%v", status)
	}

	LogRequest(r, req)

	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 校验修改时间范围
	if (req.ModifiedBegin != "" || req.ModifiedEnd != "") && (req.ModifiedBegin == "" || req.ModifiedEnd == "") {
		err := fmt.Errorf("修改起始时间和结束时间必须同时存在")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 校验查询条件
	if req.ModifiedBegin == "" && req.ModifiedEnd == "" && req.IoIDs == "" {
		err := fmt.Errorf("查询条件不能为空，时间范围和盘点单号不能同时为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.QueryInventoryCount(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 库存盘点查询原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "库存盘点查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// UpdateVirtualInventory 更新虚拟库存处理
func (h *InventoryHandler) UpdateVirtualInventory(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 更新虚拟库存原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.UpdateVirtualInventoryRequest{}

	// 处理wms_co_id
	if wmsCoID, ok := rawReq["wms_co_id"]; ok && wmsCoID != nil {
		switch v := wmsCoID.(type) {
		case float64:
			req.WmsCoID = int(v)
		case string:
			if coID, err := strconv.Atoi(v); err == nil {
				req.WmsCoID = coID
			}
		case int:
			req.WmsCoID = v
		}
	}

	// 处理sku_and_qty（数组）
	if skuAndQtyRaw, ok := rawReq["sku_and_qty"]; ok && skuAndQtyRaw != nil {
		if skuAndQtyArr, ok := skuAndQtyRaw.([]interface{}); ok {
			for _, itemRaw := range skuAndQtyArr {
				if itemMap, ok := itemRaw.(map[string]interface{}); ok {
					item := jst.UpdateVirtualInventoryItem{}

					// 处理sku_id
					if skuID, ok := itemMap["sku_id"]; ok && skuID != nil {
						item.SkuID = fmt.Sprintf("%v", skuID)
					}

					// 处理qty
					if qty, ok := itemMap["qty"]; ok && qty != nil {
						switch v := qty.(type) {
						case float64:
							item.Qty = int(v)
						case string:
							if qtyInt, err := strconv.Atoi(v); err == nil {
								item.Qty = qtyInt
							}
						case int:
							item.Qty = v
						}
					}

					req.SkuAndQty = append(req.SkuAndQty, item)
				}
			}
		}
	}

	LogRequest(r, req)

	// 参数校验
	if req.WmsCoID <= 0 {
		err := fmt.Errorf("分仓编码不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}
	if len(req.SkuAndQty) == 0 {
		err := fmt.Errorf("商品明细不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.UpdateVirtualInventory(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 更新虚拟库存原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "更新虚拟库存失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryPackBatch 箱及仓位库存批量查询处理
func (h *InventoryHandler) QueryPackBatch(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 箱及仓位库存批量查询原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.PackBatchQueryRequest{}

	// 处理page_index
	if pageIndex, ok := rawReq["page_index"]; ok && pageIndex != nil {
		switch v := pageIndex.(type) {
		case float64:
			req.PageIndex = int(v)
		case string:
			if idx, err := strconv.Atoi(v); err == nil {
				req.PageIndex = idx
			}
		case int:
			req.PageIndex = v
		}
	}

	// 处理page_size
	if pageSize, ok := rawReq["page_size"]; ok && pageSize != nil {
		switch v := pageSize.(type) {
		case float64:
			req.PageSize = int(v)
		case string:
			if size, err := strconv.Atoi(v); err == nil {
				req.PageSize = size
			}
		case int:
			req.PageSize = v
		}
	}

	// 处理wms_co_id
	if wmsCoID, ok := rawReq["wms_co_id"]; ok && wmsCoID != nil {
		switch v := wmsCoID.(type) {
		case float64:
			req.WmsCoID = int(v)
		case string:
			if coID, err := strconv.Atoi(v); err == nil {
				req.WmsCoID = coID
			}
		case int:
			req.WmsCoID = v
		}
	}

	// 处理owner_co_id
	if ownerCoID, ok := rawReq["owner_co_id"]; ok && ownerCoID != nil {
		switch v := ownerCoID.(type) {
		case float64:
			req.OwnerCoID = int(v)
		case string:
			if coID, err := strconv.Atoi(v); err == nil {
				req.OwnerCoID = coID
			}
		case int:
			req.OwnerCoID = v
		}
	}

	// 处理pack_type
	if packType, ok := rawReq["pack_type"]; ok && packType != nil {
		req.PackType = fmt.Sprintf("%v", packType)
	}

	// 处理sku_ids（数组）
	if skuIDsRaw, ok := rawReq["sku_ids"]; ok && skuIDsRaw != nil {
		if skuIDsArr, ok := skuIDsRaw.([]interface{}); ok {
			for _, item := range skuIDsArr {
				req.SkuIDs = append(req.SkuIDs, fmt.Sprintf("%v", item))
			}
		}
	}

	LogRequest(r, req)

	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 500 {
		req.PageSize = 500
	}

	// 调用聚水潭API
	resp, err := h.client.QueryPackBatch(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 箱及仓位库存批量查询原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "箱及仓位库存批量查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}
