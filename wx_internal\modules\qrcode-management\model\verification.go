package model

import (
	"context"
	"fmt"
	"strconv"
	"time"

	storeModel "yekaitai/internal/modules/store/model"
	registration "yekaitai/pkg/common/model/registration"
	redeemerRepo "yekaitai/pkg/common/repository/redeemer"
	repo_registration "yekaitai/pkg/common/repository/registration"
	"yekaitai/pkg/hmac"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/qrcode-management/service"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
)

// VerificationRecord 核销记录
type VerificationRecord struct {
	ID                     uint      `json:"id"`                       // ID
	OrderID                uint      `json:"order_id"`                 // 订单ID
	OrderNo                string    `json:"order_no"`                 // 订单编号
	WxPatientID            uint      `json:"wx_patient_id"`            // 患者ID
	WxDoctorID             uint      `json:"wx_doctor_id"`             // 医生ID
	StoreID                uint      `json:"store_id"`                 // 门店ID
	AppointDate            string    `json:"appoint_date"`             // 预约日期
	AppointPeriod          int       `json:"appoint_period"`           // 预约时段
	AppointPeriodText      string    `json:"appoint_period_text"`      // 预约时段文本
	OrderStatus            int       `json:"order_status"`             // 订单状态
	StatusText             string    `json:"status_text"`              // 状态文本
	VerificationCode       string    `json:"verification_code"`        // 核销码
	VerificationStatus     int       `json:"verification_status"`      // 核销状态
	VerificationStatusText string    `json:"verification_status_text"` // 核销状态文本
	VerifiedAt             time.Time `json:"verified_at"`              // 核销时间
	VerifierID             uint      `json:"verifier_id"`              // 核销人ID
	VerifierName           string    `json:"verifier_name"`            // 核销人姓名
	CreatedAt              time.Time `json:"created_at"`               // 创建时间
	UpdatedAt              time.Time `json:"updated_at"`               // 更新时间
}

// VerificationStatus 核销状态
const (
	VerificationStatusPending   = 0 // 待核销
	VerificationStatusVerified  = 1 // 已核销
	VerificationStatusCancelled = 2 // 已取消
)

// VerifyRequestData 核销请求数据结构
type VerifyRequestData struct {
	OrderNo          string `json:"order_no"`
	VerificationCode string `json:"verification_code"`
	Sign             string `json:"sign"`
	Timestamp        string `json:"timestamp"`
	VerifierID       uint   `json:"verifier_id"`
	VerifierName     string `json:"verifier_name"`
}

// QRCodeService 二维码服务
type QRCodeService interface {
	// 生成二维码
	GenerateQRCode(ctx context.Context, orderID uint) (string, error)
	// 获取订单二维码
	GetQRCode(ctx context.Context, orderID uint) (string, error)
	// 验证核销码
	VerifyCode(ctx context.Context, verificationCode string, verifierID uint, verifierName string) error
	// 带完整验证的核销码验证
	VerifyCodeWithValidation(ctx context.Context, req *VerifyRequestData) error
	// 获取核销记录列表
	GetVerificationRecords(ctx context.Context, storeID string, startDate string, endDate string, page int, pageSize int) ([]*VerificationRecord, int, error)
	// 获取核销记录详情
	GetVerificationRecord(ctx context.Context, id uint) (*VerificationRecord, error)
}

// qrCodeService 二维码服务实现
type qrCodeService struct{}

// NewQRCodeService 创建二维码服务
func NewQRCodeService() QRCodeService {
	return &qrCodeService{}
}

// GenerateQRCode 生成二维码
func (s *qrCodeService) GenerateQRCode(ctx context.Context, orderID uint) (string, error) {
	// 获取订单信息
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	order, err := orderRepo.GetAppointmentOrderByID(ctx, orderID)
	if err != nil {
		logx.Errorf("获取预约挂号订单失败: %v", err)
		return "", fmt.Errorf("获取预约挂号订单失败: %v", err)
	}

	// 如果订单已经有二维码，则直接返回
	if order.QRCodeURL != "" {
		return order.QRCodeURL, nil
	}

	// 检查是否已有核销码，如果没有则生成新的
	verificationCode := order.VerificationCode
	if verificationCode == "" {
		verificationCode = uuid.New().String()
		// 更新订单核销码
		order.VerificationCode = verificationCode
		order.VerificationStatus = int(registration.VerificationStatusPending)
	}

	// 调用service包中的函数生成二维码并上传
	qrCodeURL, err := service.GenerateAppointmentQRCode(
		ctx,
		order.ID,
		order.OrderNo,
		verificationCode,
	)
	if err != nil {
		logx.Errorf("生成二维码失败: %v", err)
		return "", fmt.Errorf("生成二维码失败: %v", err)
	}

	// 更新订单二维码URL
	order.QRCodeURL = qrCodeURL
	if err := orderRepo.UpdateAppointmentOrder(ctx, order); err != nil {
		logx.Errorf("更新预约挂号订单二维码失败: %v", err)
		return "", fmt.Errorf("更新预约挂号订单二维码失败: %v", err)
	}

	logx.Infof("生成二维码成功: orderID=%d, orderNo=%s, verificationCode=%s",
		order.ID, order.OrderNo, verificationCode)
	return qrCodeURL, nil
}

// GetQRCode 获取订单二维码
func (s *qrCodeService) GetQRCode(ctx context.Context, orderID uint) (string, error) {
	// 获取订单信息
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	order, err := orderRepo.GetAppointmentOrderByID(ctx, orderID)
	if err != nil {
		logx.Errorf("获取预约挂号订单失败: %v", err)
		return "", fmt.Errorf("获取预约挂号订单失败: %v", err)
	}

	// 如果订单还没有二维码，则生成二维码
	if order.QRCodeURL == "" {
		return s.GenerateQRCode(ctx, orderID)
	}

	return order.QRCodeURL, nil
}

// VerifyCode 验证核销码
func (s *qrCodeService) VerifyCode(ctx context.Context, verificationCode string, verifierID uint, verifierName string) error {
	// 获取订单信息
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	order, err := orderRepo.GetAppointmentOrderByVerificationCode(ctx, verificationCode)
	if err != nil {
		logx.Errorf("获取预约挂号订单失败: %v", err)
		return fmt.Errorf("无效的核销码: %v", err)
	}

	// 检查订单状态
	if order.OrderStatus != registration.OrderStatusWaitDiagnosis {
		return fmt.Errorf("订单状态不允许核销")
	}

	// 检查核销状态
	if order.VerificationStatus == int(registration.VerificationStatusVerified) {
		return fmt.Errorf("订单已经被核销")
	}

	// 更新订单核销状态
	order.VerificationStatus = int(registration.VerificationStatusVerified)
	order.VerifiedAt = time.Now().Format("2006-01-02 15:04:05")
	order.VerifierID = int(verifierID)
	order.VerifierName = verifierName

	// 保存订单
	if err := orderRepo.UpdateAppointmentOrder(ctx, order); err != nil {
		logx.Errorf("更新预约挂号订单核销状态失败: %v", err)
		return fmt.Errorf("更新预约挂号订单核销状态失败: %v", err)
	}

	return nil
}

// VerifyCodeWithValidation 带完整验证的核销码验证
func (s *qrCodeService) VerifyCodeWithValidation(ctx context.Context, req *VerifyRequestData) error {
	// 1. 后端签名验证
	signatureData := fmt.Sprintf("%s%s", req.OrderNo, req.VerificationCode)
	expectedSign := hmac.GenerateHMAC(signatureData)
	if req.Sign != expectedSign {
		logx.Errorf("签名验证失败: expected=%s, actual=%s", expectedSign, req.Sign)
		return fmt.Errorf("签名验证失败")
	}

	// 3. 数据库核对 - 根据order_no查询订单
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	order, err := orderRepo.GetAppointmentOrderByOrderNo(ctx, req.OrderNo)
	if err != nil {
		logx.Errorf("获取预约挂号订单失败: %v", err)
		return fmt.Errorf("订单不存在: %v", err)
	}

	// 验证核销码是否匹配
	if order.VerificationCode != req.VerificationCode {
		logx.Errorf("核销码不匹配: expected=%s, actual=%s", order.VerificationCode, req.VerificationCode)
		return fmt.Errorf("核销码不匹配")
	}

	// 4. 业务规则检查
	// 4.1 检查订单状态 - 必须是待就诊状态
	if order.OrderStatus != registration.OrderStatusWaitDiagnosis {
		return fmt.Errorf("订单状态不允许核销，当前状态：%d", int(order.OrderStatus))
	}

	// 4.2 检查核销状态
	if order.VerificationStatus == int(registration.VerificationStatusVerified) {
		return fmt.Errorf("订单已经被核销")
	}

	// 4.3 根据store_id查询t_stores表检查wsjg_id
	storeRepo := storeModel.NewStoreRepository(mysql.Slave())
	store, err := storeRepo.FindByID(uint(order.StoreID))
	if err != nil {
		logx.Errorf("查询门店信息失败: %v, storeID=%d", err, order.StoreID)
		return fmt.Errorf("查询门店信息失败: %v", err)
	}

	// 检查是否为免支付门店（wsjg_id为5424或5426）
	if store.WsjgID != "5424" && store.WsjgID != "5426" {
		logx.Errorf("门店不支持免支付核销: storeID=%d, wsjgID=%d", order.StoreID, store.WsjgID)
		return fmt.Errorf("该门店不支持免支付核销")
	}

	// 2. 时间戳校验 - 改为基于预约日期的校验
	timestamp, err := strconv.ParseInt(req.Timestamp, 10, 64)
	if err != nil {
		logx.Errorf("时间戳格式无效: %s", req.Timestamp)
		return fmt.Errorf("时间戳格式无效")
	}

	// 解析预约日期
	appointDate, err := time.Parse("2006-01-02", order.AppointDate)
	if err != nil {
		logx.Errorf("预约日期格式无效: %s", order.AppointDate)
		return fmt.Errorf("预约日期格式无效")
	}

	// 计算预约日期当天的23:59:59
	appointEndOfDay := time.Date(appointDate.Year(), appointDate.Month(), appointDate.Day(), 23, 59, 59, 0, time.Local)

	// 检查时间戳是否在预约日期当天23:59:59之前
	timestampTime := time.Unix(0, timestamp)
	now := time.Now()

	// 不能是未来的时间戳
	if timestampTime.After(now) {
		logx.Errorf("时间戳不能是未来时间: timestamp=%s, now=%s", timestampTime.Format("2006-01-02 15:04:05"), now.Format("2006-01-02 15:04:05"))
		return fmt.Errorf("时间戳不能是未来时间")
	}

	// 检查是否超过预约日期当天23:59:59
	if now.After(appointEndOfDay) {
		logx.Errorf("二维码已过期: 预约日期=%s, 过期时间=%s, 当前时间=%s",
			order.AppointDate, appointEndOfDay.Format("2006-01-02 15:04:05"), now.Format("2006-01-02 15:04:05"))
		return fmt.Errorf("二维码已过期，过期时间：%s", appointEndOfDay.Format("2006-01-02 15:04:05"))
	}

	// 验证核销员权限
	redeemerRepository := redeemerRepo.NewWxRedeemerRepository()
	isValid, err := redeemerRepository.IsValidRedeemer(ctx, req.VerifierID, uint(order.StoreID))
	if err != nil {
		logx.Errorf("验证核销员权限失败: %v", err)
		return fmt.Errorf("验证核销员权限失败: %v", err)
	}
	if !isValid {
		logx.Errorf("核销员无权限: verifierID=%d, storeID=%d", req.VerifierID, order.StoreID)
		return fmt.Errorf("核销员无权限核销此门店的订单")
	}

	// 5. 标记为已使用
	order.VerificationStatus = int(registration.VerificationStatusVerified)
	order.OrderStatus = registration.OrderStatusCompleted // 设置订单状态为已完成
	order.VerifiedAt = time.Now().Format("2006-01-02 15:04:05")
	order.VerifierID = int(req.VerifierID)
	order.VerifierName = req.VerifierName

	// 保存订单
	if err := orderRepo.UpdateAppointmentOrder(ctx, order); err != nil {
		logx.Errorf("更新预约挂号订单核销状态失败: %v", err)
		return fmt.Errorf("更新预约挂号订单核销状态失败: %v", err)
	}

	logx.Infof("核销成功: orderNo=%s, verifierID=%d, verifierName=%s, appointDate=%s, storeWsjgID=%d",
		req.OrderNo, req.VerifierID, req.VerifierName, order.AppointDate, store.WsjgID)
	return nil
}

// GetVerificationRecords 获取核销记录列表
func (s *qrCodeService) GetVerificationRecords(ctx context.Context, storeID string, startDate string, endDate string, page int, pageSize int) ([]*VerificationRecord, int, error) {
	// 获取订单信息
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	orders, total, err := orderRepo.GetAppointmentOrdersByVerification(ctx, storeID, startDate, endDate, page, pageSize)
	if err != nil {
		logx.Errorf("获取核销记录失败: %v", err)
		return nil, 0, fmt.Errorf("获取核销记录失败: %v", err)
	}

	var records []*VerificationRecord
	for _, order := range orders {
		// 转换为核销记录
		record := &VerificationRecord{
			ID:                     order.ID,
			OrderID:                order.ID,
			OrderNo:                order.OrderNo,
			WxPatientID:            uint(order.WxPatientID),
			WxDoctorID:             uint(order.WxDoctorID),
			StoreID:                uint(order.StoreID),
			AppointDate:            order.AppointDate,
			AppointPeriod:          int(order.AppointPeriod),
			AppointPeriodText:      registration.GetAppointPeriodText(order.AppointPeriod),
			OrderStatus:            int(order.OrderStatus),
			StatusText:             getOrderStatusText(int(order.OrderStatus)),
			VerificationCode:       order.VerificationCode,
			VerificationStatus:     order.VerificationStatus,
			VerificationStatusText: GetVerificationStatusText(order.VerificationStatus),
		}

		// 设置核销时间
		if order.VerifiedAt != "" {
			verifiedAt, err := time.Parse("2006-01-02 15:04:05", order.VerifiedAt)
			if err == nil {
				record.VerifiedAt = verifiedAt
			}
		}

		// 设置核销人信息
		record.VerifierID = uint(order.VerifierID)
		record.VerifierName = order.VerifierName

		// 设置创建和更新时间
		record.CreatedAt = order.CreatedAt
		record.UpdatedAt = order.UpdatedAt

		records = append(records, record)
	}

	return records, total, nil
}

// GetVerificationRecord 获取核销记录详情
func (s *qrCodeService) GetVerificationRecord(ctx context.Context, id uint) (*VerificationRecord, error) {
	// 获取订单信息
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	order, err := orderRepo.GetAppointmentOrderByID(ctx, id)
	if err != nil {
		logx.Errorf("获取预约挂号订单失败: %v", err)
		return nil, fmt.Errorf("获取预约挂号订单失败: %v", err)
	}

	// 转换为核销记录
	record := &VerificationRecord{
		ID:                     order.ID,
		OrderID:                order.ID,
		OrderNo:                order.OrderNo,
		WxPatientID:            uint(order.WxPatientID),
		WxDoctorID:             uint(order.WxDoctorID),
		StoreID:                uint(order.StoreID),
		AppointDate:            order.AppointDate,
		AppointPeriod:          int(order.AppointPeriod),
		AppointPeriodText:      registration.GetAppointPeriodText(order.AppointPeriod),
		OrderStatus:            int(order.OrderStatus),
		StatusText:             getOrderStatusText(int(order.OrderStatus)),
		VerificationCode:       order.VerificationCode,
		VerificationStatus:     order.VerificationStatus,
		VerificationStatusText: GetVerificationStatusText(order.VerificationStatus),
	}

	// 设置核销时间
	if order.VerifiedAt != "" {
		verifiedAt, err := time.Parse("2006-01-02 15:04:05", order.VerifiedAt)
		if err == nil {
			record.VerifiedAt = verifiedAt
		}
	}

	// 设置核销人信息
	record.VerifierID = uint(order.VerifierID)
	record.VerifierName = order.VerifierName

	// 设置创建和更新时间
	record.CreatedAt = order.CreatedAt
	record.UpdatedAt = order.UpdatedAt

	return record, nil
}

// GetVerificationStatusText 获取核销状态文本描述
func GetVerificationStatusText(status int) string {
	switch status {
	case VerificationStatusPending:
		return "待核销"
	case VerificationStatusVerified:
		return "已核销"
	case VerificationStatusCancelled:
		return "已取消"
	default:
		return ""
	}
}

// getOrderStatusText 获取订单状态文本描述
func getOrderStatusText(status int) string {
	switch status {
	case 1:
		return "待就诊"
	case 2:
		return "待支付"
	case 3:
		return "已完成"
	case 4:
		return "已取消"
	case 5:
		return "已退款"
	default:
		return ""
	}
}
