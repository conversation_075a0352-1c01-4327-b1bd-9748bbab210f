package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/service_setup/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterServicePackageRoutes 注册服务套餐管理相关路由
func RegisterServicePackageRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建处理器
	packageHandler := handler.NewServicePackageHandler(serverCtx)
	tagHandler := handler.NewServiceTagHandler(serverCtx)

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 套餐标签管理路由
	server.AddRoutes(
		[]rest.Route{
			// 标签列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/service-tags",
				Handler: adminAuthWrapper(tagHandler.ListTags),
			},
			// 标签详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/service-tags/:tagId",
				Handler: adminAuthWrapper(tagHandler.GetTag),
			},
			// 创建标签
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/service-tags",
				Handler: adminAuthWrapper(tagHandler.CreateTag),
			},
			// 更新标签
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/service-tags/:tagId",
				Handler: adminAuthWrapper(tagHandler.UpdateTag),
			},
			// 更新标签状态
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/service-tags/:tagId/status",
				Handler: adminAuthWrapper(tagHandler.UpdateTagStatus),
			},
			// 删除标签
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/service-tags",
				Handler: adminAuthWrapper(tagHandler.DeleteTag),
			},
		},
	)

	// 套餐管理路由
	server.AddRoutes(
		[]rest.Route{
			// 套餐列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/service-packages",
				Handler: adminAuthWrapper(packageHandler.ListPackages),
			},
			// 套餐详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/service-packages/:packageId",
				Handler: adminAuthWrapper(packageHandler.GetPackage),
			},
			// 创建套餐
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/service-packages",
				Handler: adminAuthWrapper(packageHandler.CreatePackage),
			},
			// 更新套餐
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/service-packages/:packageId",
				Handler: adminAuthWrapper(packageHandler.UpdatePackage),
			},
			// 更新套餐状态
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/service-packages/:packageId/status",
				Handler: adminAuthWrapper(packageHandler.UpdatePackageStatus),
			},
			// 删除套餐
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/service-packages/:packageId",
				Handler: adminAuthWrapper(packageHandler.DeletePackage),
			},
		},
	)
}
