package handler

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"

	"yekaitai/internal/models/enum"
	patientModel "yekaitai/pkg/common/model/patient"
	userModel "yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"
	"yekaitai/wx_internal/middleware"
	userService "yekaitai/wx_internal/modules/user/service"
	"yekaitai/wx_internal/svc"
	"yekaitai/wx_internal/types"
)

// 患者列表请求
type PatientListRequest struct {
	Page int `form:"page,default=1"`  // 页码
	Size int `form:"size,default=10"` // 每页记录数
}

// 患者详情请求
type PatientDetailRequest struct {
	PatientID uint `path:"patientId"` // 患者ID
}

// 创建患者请求
type CreatePatientRequest struct {
	Name                 string `json:"name"`                    // 姓名
	Gender               int    `json:"gender"`                  // 性别：1-男，2-女
	BirthDate            string `json:"birthDate"`               // 出生年月日 YYYY-MM-DD
	IdCard               string `json:"idCard"`                  // 身份证号
	Ethnicity            string `json:"ethnicity,optional"`      // 民族
	RelationshipWithUser string `json:"relationshipWithUser"`    // 与本人关系
	Mobile               string `json:"mobile"`                  // 手机号码
	MaritalStatus        int    `json:"maritalStatus,optional"`  // 婚姻状态：1-已婚，2-未婚
	MedicalHistory       string `json:"medicalHistory,optional"` // 病史
	Allergies            string `json:"allergies,optional"`      // 过敏史
	IsDefault            bool   `json:"isDefault,optional"`      // 是否设为默认就诊人
	// 新增平台患者ID字段
	HangzhouHisID   string `json:"hangzhouHisId,optional"`   // 杭州HIS患者ID
	AbcyunPatientID string `json:"abcyunPatientId,optional"` // ABC云患者ID
}

// 更新患者请求
type UpdatePatientRequest struct {
	PatientID            uint   `path:"patientId"`                     // 患者ID
	Name                 string `json:"name,optional"`                 // 姓名
	Gender               int    `json:"gender,optional"`               // 性别：1-男，2-女
	BirthDate            string `json:"birthDate,optional"`            // 出生年月日
	IdCard               string `json:"idCard,optional"`               // 身份证号
	Ethnicity            string `json:"ethnicity,optional"`            // 民族
	RelationshipWithUser string `json:"relationshipWithUser,optional"` // 与本人关系
	Mobile               string `json:"mobile,optional"`               // 手机号码
	MaritalStatus        int    `json:"maritalStatus,optional"`        // 婚姻状态：1-已婚，2-未婚
	MedicalHistory       string `json:"medicalHistory,optional"`       // 病史
	Allergies            string `json:"allergies,optional"`            // 过敏史
	IsDefault            bool   `json:"isDefault,optional"`            // 是否设为默认就诊人
	// 新增平台患者ID字段
	HangzhouHisID   string `json:"hangzhouHisId,optional"`   // 杭州HIS患者ID
	AbcyunPatientID string `json:"abcyunPatientId,optional"` // ABC云患者ID
}

// 删除患者请求
type DeletePatientRequest struct {
	PatientID uint `path:"patientId"` // 患者ID
}

// 设置默认就诊人请求
type SetDefaultPatientRequest struct {
	PatientID uint `path:"patientId"` // 患者ID
}

// PatientHandler 患者处理器
type PatientHandler struct {
	svcCtx *svc.WxServiceContext
}

// NewPatientHandler 创建患者处理器
func NewPatientHandler(svcCtx *svc.WxServiceContext) *PatientHandler {
	return &PatientHandler{
		svcCtx: svcCtx,
	}
}

// ListPatients 获取患者列表
func (h *PatientHandler) ListPatients(w http.ResponseWriter, r *http.Request) {
	var req PatientListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取用户ID
	userID, err := h.getUserIDFromContext(r.Context())
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	logx.Infof("获取患者列表: userId=%d, page=%d, size=%d", userID, req.Page, req.Size)

	// 构建查询条件
	db := mysql.Slave().Model(&patientModel.WxPatient{})
	db = db.Where("user_id = ?", userID)

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logx.Errorf("统计患者数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取患者列表失败"))
		return
	}

	// 检查是否超过最大限制
	if total >= 10 {
		logx.Infof("用户%d的就诊人数量已达上限: %d", userID, total)
	}

	// 查询患者列表，按照业务规则排序：
	// 1. 默认就诊人在前（通过relationship_with_user='本人'判断）
	// 2. 其他按创建时间正序排列（最新创建的在最下方）
	var patients []patientModel.WxPatient
	offset := (req.Page - 1) * req.Size
	if err := db.Order("CASE WHEN relationship_with_user = '本人' THEN 0 ELSE 1 END, created_at ASC").
		Offset(offset).Limit(req.Size).Find(&patients).Error; err != nil {
		logx.Errorf("查询患者列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取患者列表失败"))
		return
	}

	// 处理患者数据，计算年龄
	result := make([]map[string]interface{}, len(patients))
	for i, patient := range patients {
		age := h.calculateAge(patient.BirthDate)

		// 调试：打印平台患者ID字段值
		logx.Infof("患者ID=%d, HangzhouHisID='%s', AbcyunPatientID='%s'",
			patient.PatientID, patient.HangzhouHisID, patient.AbcyunPatientID)

		// 转换为Map，返回全部字段
		patientMap := map[string]interface{}{
			"patientId":            patient.PatientID,
			"userId":               patient.UserID,
			"name":                 patient.Name,
			"gender":               patient.Gender,
			"birthDate":            patient.BirthDate,
			"age":                  age,
			"idCard":               patient.IdCard,
			"ethnicity":            patient.Ethnicity,
			"relationshipWithUser": patient.RelationshipWithUser,
			"mobile":               patient.Mobile,
			"maritalStatus":        patient.MaritalStatus,
			"medicalHistory":       patient.MedicalHistory,
			"allergies":            patient.Allergies,
			"status":               patient.Status,
			"isDefault":            patient.RelationshipWithUser == "本人",
			"createdAt":            patient.CreatedAt,
			"updatedAt":            patient.UpdatedAt,
			// 平台患者ID字段（确保即使为空也返回）
			"hangzhouHisId":   patient.HangzhouHisID,
			"abcyunPatientId": patient.AbcyunPatientID,
		}

		result[i] = patientMap
	}

	logx.Infof("获取患者列表成功: 共%d条记录", total)

	// 返回结果
	responseData := map[string]interface{}{
		"list":       result,
		"total":      total,
		"page":       req.Page,
		"size":       req.Size,
		"canAddMore": total < 10, // 是否还能添加更多就诊人
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(responseData, "获取患者列表成功"))
}

// GetPatient 获取患者详情
func (h *PatientHandler) GetPatient(w http.ResponseWriter, r *http.Request) {
	var req PatientDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取用户ID
	userID, err := h.getUserIDFromContext(r.Context())
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	logx.Infof("获取患者详情: userId=%d, patientId=%d", userID, req.PatientID)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 获取患者信息
	patient, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取患者详情失败"))
		}
		return
	}

	// 验证患者是否属于当前用户
	if patient.UserID != userID {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "无权访问该患者信息"))
		return
	}

	// 计算年龄
	age := h.calculateAge(patient.BirthDate)

	// 调试：打印平台患者ID字段值
	logx.Infof("患者详情ID=%d, HangzhouHisID='%s', AbcyunPatientID='%s'",
		patient.PatientID, patient.HangzhouHisID, patient.AbcyunPatientID)

	// 准备返回数据
	result := map[string]interface{}{
		"patientId":            patient.PatientID,
		"userId":               patient.UserID,
		"name":                 patient.Name,
		"gender":               patient.Gender,
		"birthDate":            patient.BirthDate,
		"age":                  age,
		"idCard":               patient.IdCard,
		"ethnicity":            patient.Ethnicity,
		"relationshipWithUser": patient.RelationshipWithUser,
		"mobile":               patient.Mobile,
		"maritalStatus":        patient.MaritalStatus,
		"medicalHistory":       patient.MedicalHistory,
		"allergies":            patient.Allergies,
		"status":               patient.Status,
		"isDefault":            patient.RelationshipWithUser == "本人",
		"createdAt":            patient.CreatedAt,
		"updatedAt":            patient.UpdatedAt,
		// 新增平台患者ID字段
		"hangzhouHisId":   patient.HangzhouHisID,
		"abcyunPatientId": patient.AbcyunPatientID,
	}

	logx.Infof("获取患者详情成功: patientId=%d", req.PatientID)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取患者详情成功"))
}

// CreatePatient 创建患者
func (h *PatientHandler) CreatePatient(w http.ResponseWriter, r *http.Request) {
	var req CreatePatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取用户ID
	userID, err := h.getUserIDFromContext(r.Context())
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	logx.Infof("创建患者: userId=%d, 姓名=%s, 手机号=%s", userID, req.Name, req.Mobile)

	// 验证参数
	if err := h.validateCreatePatientRequest(&req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 检查患者数量限制
	var count int64
	if err := mysql.Slave().Model(&patientModel.WxPatient{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		logx.Errorf("查询患者数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建患者失败"))
		return
	}

	if count >= 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "最多可创建10位就诊人"))
		return
	}

	// 设置默认值
	if req.Ethnicity == "" {
		req.Ethnicity = enum.GetDefaultEthnicity()
	}

	// 如果设置为默认就诊人，需要先取消其他默认就诊人
	if req.IsDefault || req.RelationshipWithUser == "本人" {
		if err := h.clearDefaultPatient(userID); err != nil {
			logx.Errorf("清除默认就诊人失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建患者失败"))
			return
		}
		req.RelationshipWithUser = "本人"
	}

	// 创建患者对象
	patient := &patientModel.WxPatient{
		UserID:               userID,
		Name:                 req.Name,
		Gender:               req.Gender,
		BirthDate:            req.BirthDate,
		IdCard:               req.IdCard,
		Ethnicity:            req.Ethnicity,
		RelationshipWithUser: req.RelationshipWithUser,
		Mobile:               req.Mobile,
		MaritalStatus:        req.MaritalStatus,
		MedicalHistory:       req.MedicalHistory,
		Allergies:            req.Allergies,
		Status:               1, // 默认状态为正常
		// 新增平台患者ID字段
		HangzhouHisID:   req.HangzhouHisID,
		AbcyunPatientID: req.AbcyunPatientID,
	}

	// 保存患者信息
	if err := mysql.Master().Create(patient).Error; err != nil {
		logx.Errorf("创建患者失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建患者失败"))
		return
	}

	logx.Infof("创建患者成功: patientId=%d", patient.PatientID)

	// 异步触发用户等级升级检查（完善就诊人档案）
	go func() {
		userConsumptionService := userService.NewUserConsumptionService()
		if err := userConsumptionService.TriggerUserLevelUpdate(userID); err != nil {
			logx.Errorf("完善就诊人档案等级升级失败: userID=%d, error=%v", userID, err)
		} else {
			logx.Infof("完善就诊人档案等级升级成功: userID=%d", userID)
		}
	}()

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(map[string]interface{}{
		"patientId": patient.PatientID,
	}, "创建患者成功"))
}

// UpdatePatient 更新患者信息
func (h *PatientHandler) UpdatePatient(w http.ResponseWriter, r *http.Request) {
	var req UpdatePatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取用户ID
	userID, err := h.getUserIDFromContext(r.Context())
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	logx.Infof("更新患者信息: userId=%d, patientId=%d", userID, req.PatientID)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 获取当前患者信息
	patient, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新患者信息失败"))
		}
		return
	}

	// 验证患者是否属于当前用户
	if patient.UserID != userID {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "无权修改该患者信息"))
		return
	}

	// 验证参数
	if err := h.validateUpdatePatientRequest(&req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 更新字段
	if req.Name != "" {
		patient.Name = req.Name
	}
	if req.Gender != 0 {
		patient.Gender = req.Gender
	}
	if req.BirthDate != "" {
		patient.BirthDate = req.BirthDate
	}
	if req.IdCard != "" {
		patient.IdCard = req.IdCard
	}
	if req.Ethnicity != "" {
		patient.Ethnicity = req.Ethnicity
	}
	if req.RelationshipWithUser != "" {
		patient.RelationshipWithUser = req.RelationshipWithUser
	}
	if req.Mobile != "" {
		patient.Mobile = req.Mobile
	}
	if req.MaritalStatus != 0 {
		patient.MaritalStatus = req.MaritalStatus
	}
	if req.MedicalHistory != "" {
		patient.MedicalHistory = req.MedicalHistory
	}
	if req.Allergies != "" {
		patient.Allergies = req.Allergies
	}
	// 更新平台患者ID字段
	if req.HangzhouHisID != "" {
		patient.HangzhouHisID = req.HangzhouHisID
	}
	if req.AbcyunPatientID != "" {
		patient.AbcyunPatientID = req.AbcyunPatientID
	}

	// 如果设置为默认就诊人，需要先取消其他默认就诊人
	if req.IsDefault || req.RelationshipWithUser == "本人" {
		if err := h.clearDefaultPatient(userID); err != nil {
			logx.Errorf("清除默认就诊人失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新患者信息失败"))
			return
		}
		patient.RelationshipWithUser = "本人"
	}

	// 更新患者信息
	if err := mysql.Master().Save(patient).Error; err != nil {
		logx.Errorf("更新患者信息失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新患者信息失败"))
		return
	}

	logx.Infof("更新患者信息成功: patientId=%d", req.PatientID)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "更新患者信息成功"))
}

// DeletePatient 删除患者
func (h *PatientHandler) DeletePatient(w http.ResponseWriter, r *http.Request) {
	var req DeletePatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取用户ID
	userID, err := h.getUserIDFromContext(r.Context())
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	logx.Infof("删除患者: userId=%d, patientId=%d", userID, req.PatientID)

	// 检查用户是否只有一个就诊人
	var count int64
	if err := mysql.Slave().Model(&patientModel.WxPatient{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		logx.Errorf("查询患者数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除患者失败"))
		return
	}

	if count <= 1 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "至少需要保留一位就诊人"))
		return
	}

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 获取当前患者信息
	patient, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除患者失败"))
		}
		return
	}

	// 验证患者是否属于当前用户
	if patient.UserID != userID {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "无权删除该患者信息"))
		return
	}

	// 检查是否有未结束的订单
	hasUnfinishedOrders, err := h.checkUnfinishedOrders(req.PatientID)
	if err != nil {
		logx.Errorf("检查未结束订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除患者失败"))
		return
	}

	if hasUnfinishedOrders {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "当前就诊人有未结束的订单，不可删除"))
		return
	}

	// 删除患者信息（软删除）
	if err := mysql.Master().Delete(patient).Error; err != nil {
		logx.Errorf("删除患者失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除患者失败"))
		return
	}

	logx.Infof("删除患者成功: patientId=%d", req.PatientID)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "删除患者成功"))
}

// SetDefaultPatient 设置默认就诊人
func (h *PatientHandler) SetDefaultPatient(w http.ResponseWriter, r *http.Request) {
	var req SetDefaultPatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 从上下文获取用户ID
	userID, err := h.getUserIDFromContext(r.Context())
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	logx.Infof("设置默认就诊人: userId=%d, patientId=%d", userID, req.PatientID)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 获取当前患者信息
	patient, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "设置默认就诊人失败"))
		}
		return
	}

	// 验证患者是否属于当前用户
	if patient.UserID != userID {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "无权设置该患者为默认就诊人"))
		return
	}

	// 先取消其他默认就诊人
	if err := h.clearDefaultPatient(userID); err != nil {
		logx.Errorf("清除默认就诊人失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "设置默认就诊人失败"))
		return
	}

	// 设置当前患者为默认就诊人
	patient.RelationshipWithUser = "本人"
	if err := mysql.Master().Save(patient).Error; err != nil {
		logx.Errorf("设置默认就诊人失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "设置默认就诊人失败"))
		return
	}

	logx.Infof("设置默认就诊人成功: patientId=%d", req.PatientID)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "设置默认就诊人成功"))
}

// 辅助方法

// getUserIDFromContext 从上下文获取用户ID
func (h *PatientHandler) getUserIDFromContext(ctx context.Context) (uint, error) {
	// 从上下文获取openId
	openIdVal := ctx.Value(middleware.OpenIDKey)
	if openIdVal == nil {
		return 0, fmt.Errorf("openid不存在")
	}

	openId, ok := openIdVal.(string)
	if !ok || openId == "" {
		return 0, fmt.Errorf("无效的openid")
	}

	// 根据openId查询用户ID
	userRepo := userModel.NewWxUserRepository(mysql.Slave())
	user, err := userRepo.FindByOpenID(openId)
	if err != nil {
		return 0, fmt.Errorf("查询用户信息失败: %v", err)
	}

	return user.UserID, nil
}

// calculateAge 计算年龄
func (h *PatientHandler) calculateAge(birthDate string) map[string]interface{} {
	if birthDate == "" {
		return map[string]interface{}{
			"years":  0,
			"months": 0,
			"text":   "",
		}
	}

	// 解析出生日期
	birth, err := time.Parse("2006-01-02", birthDate)
	if err != nil {
		return map[string]interface{}{
			"years":  0,
			"months": 0,
			"text":   "",
		}
	}

	now := time.Now()
	years := now.Year() - birth.Year()
	months := int(now.Month()) - int(birth.Month())

	// 如果还没到生日，年龄减1
	if now.YearDay() < birth.YearDay() {
		years--
		months += 12
	}

	// 如果月份为负数，调整
	if months < 0 {
		months += 12
	}

	// 生成年龄文本
	var ageText string
	if years > 0 {
		if months > 0 {
			ageText = fmt.Sprintf("%d岁%d个月", years, months)
		} else {
			ageText = fmt.Sprintf("%d岁", years)
		}
	} else {
		if months > 0 {
			ageText = fmt.Sprintf("%d个月", months)
		} else {
			// 计算天数
			days := int(now.Sub(birth).Hours() / 24)
			if days > 0 {
				ageText = fmt.Sprintf("%d天", days)
			} else {
				ageText = "新生儿"
			}
		}
	}

	return map[string]interface{}{
		"years":  years,
		"months": months,
		"text":   ageText,
	}
}

// validateCreatePatientRequest 验证创建患者请求
func (h *PatientHandler) validateCreatePatientRequest(req *CreatePatientRequest) error {
	// 判断：患者名称是否填写
	if req.Name == "" {
		return fmt.Errorf("请先填写患者名称")
	}

	// 判断：性别是否选择
	if req.Gender == 0 {
		return fmt.Errorf("请先选择性别")
	}

	// 判断：出生年月是否填写
	if req.BirthDate == "" {
		return fmt.Errorf("请选择出生年月")
	}

	// 判断：身份证和手机号是否填写
	if req.IdCard == "" {
		return fmt.Errorf("请先填写身份证号")
	}

	if req.Mobile == "" {
		return fmt.Errorf("请先填写手机号")
	}

	// 验证手机号码格式
	if !utils.IsValidPhoneNumber(req.Mobile) {
		return fmt.Errorf("无效的手机号码格式")
	}

	// 验证身份证号码格式
	if !utils.IsValidIDCard(req.IdCard) {
		return fmt.Errorf("无效的身份证号码格式")
	}

	// 验证民族
	if req.Ethnicity != "" && !enum.IsValidEthnicity(req.Ethnicity) {
		return fmt.Errorf("无效的民族")
	}

	// 验证与本人关系
	if req.RelationshipWithUser != "" && !enum.IsValidRelationship(req.RelationshipWithUser) {
		return fmt.Errorf("无效的与本人关系")
	}

	// 验证出生日期格式
	if _, err := time.Parse("2006-01-02", req.BirthDate); err != nil {
		return fmt.Errorf("出生日期格式错误，应为YYYY-MM-DD")
	}

	return nil
}

// validateUpdatePatientRequest 验证更新患者请求
func (h *PatientHandler) validateUpdatePatientRequest(req *UpdatePatientRequest) error {
	// 验证手机号码格式
	if req.Mobile != "" && !utils.IsValidPhoneNumber(req.Mobile) {
		return fmt.Errorf("无效的手机号码格式")
	}

	// 验证身份证号码格式
	if req.IdCard != "" && !utils.IsValidIDCard(req.IdCard) {
		return fmt.Errorf("无效的身份证号码格式")
	}

	// 验证民族
	if req.Ethnicity != "" && !enum.IsValidEthnicity(req.Ethnicity) {
		return fmt.Errorf("无效的民族")
	}

	// 验证与本人关系
	if req.RelationshipWithUser != "" && !enum.IsValidRelationship(req.RelationshipWithUser) {
		return fmt.Errorf("无效的与本人关系")
	}

	// 验证出生日期格式
	if req.BirthDate != "" {
		if _, err := time.Parse("2006-01-02", req.BirthDate); err != nil {
			return fmt.Errorf("出生日期格式错误，应为YYYY-MM-DD")
		}
	}

	return nil
}

// clearDefaultPatient 清除默认就诊人
func (h *PatientHandler) clearDefaultPatient(userID uint) error {
	// 将所有"本人"关系的患者改为"其他"
	return mysql.Master().Model(&patientModel.WxPatient{}).
		Where("user_id = ? AND relationship_with_user = ?", userID, "本人").
		Update("relationship_with_user", "其他").Error
}

// checkUnfinishedOrders 检查是否有未结束的订单
func (h *PatientHandler) checkUnfinishedOrders(patientID uint) (bool, error) {
	// TODO: 这里需要根据实际的订单表结构来实现
	// 目前先返回false，表示没有未结束的订单
	// 实际实现时需要查询订单表，检查该患者是否有状态为"未支付"或"进行中"的订单

	// 示例查询逻辑（需要根据实际订单表调整）:
	// var count int64
	// err := mysql.Slave().Table("orders").
	//     Where("patient_id = ? AND status IN (?)", patientID, []string{"unpaid", "processing"}).
	//     Count(&count).Error
	// if err != nil {
	//     return false, err
	// }
	// return count > 0, nil

	return false, nil
}
