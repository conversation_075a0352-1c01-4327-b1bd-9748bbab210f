package bootstrap

import (
	"fmt"
	"yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/common/model/redeemer"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
)

// MigrateUser 执行用户相关表结构迁移
func MigrateUser() error {
	fmt.Println("开始迁移用户相关表结构...")
	db := mysql.Master()

	// 禁用外键检查
	db = db.Exec("SET FOREIGN_KEY_CHECKS = 0;")
	defer db.Exec("SET FOREIGN_KEY_CHECKS = 1;")

	// 用户相关表
	db.Set("gorm:table_options", "COMMENT='微信用户基础信息表'").AutoMigrate(&user.WxUser{})
	db.Set("gorm:table_options", "COMMENT='微信患者信息表'").AutoMigrate(&patient.WxPatient{})
	db.Set("gorm:table_options", "COMMENT='微信医生信息表'").AutoMigrate(&doctor.WxDoctor{})
	db.Set("gorm:table_options", "COMMENT='微信药师信息表'").AutoMigrate(&redeemer.WxRedeemer{})

	fmt.Println("用户相关表结构迁移完成")
	return nil
}
