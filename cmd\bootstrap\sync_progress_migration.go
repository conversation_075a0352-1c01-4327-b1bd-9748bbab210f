package bootstrap

import (
	"log"

	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/infra/mysql"
)

// MigrateSyncProgress 迁移同步进度表
func MigrateSyncProgress() error {
	log.Println("开始迁移同步进度表...")

	// 获取数据库连接
	db := mysql.Master()

	// 自动迁移 sync_progress 表
	err := db.Set("gorm:table_options", "COMMENT='同步进度表'").AutoMigrate(&patient.SyncProgress{})
	if err != nil {
		log.Printf("迁移同步进度表失败: %v", err)
		return err
	}

	log.Println("同步进度表迁移完成")
	return nil
}
