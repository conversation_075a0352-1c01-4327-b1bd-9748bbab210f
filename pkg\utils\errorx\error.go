package errorx

import "fmt"

// BizError 业务错误
type BizError struct {
	Message string
}

// Error 实现error接口
func (e *BizError) Error() string {
	return e.Message
}

// NewBizError 创建业务错误
func NewBizError(message string) error {
	return &BizError{Message: message}
}

// 添加错误原因
func WithReason(err error, reason string) error {
	if bizErr, ok := err.(*BizError); ok {
		return &BizError{Message: fmt.Sprintf("%s：%s", bizErr.Message, reason)}
	}
	return err
}
