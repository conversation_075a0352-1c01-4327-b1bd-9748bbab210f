package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"yekaitai/internal/modules/user_points/model"

	"gorm.io/gorm"
)

// UserPointsRulesService 叶小币规则服务
type UserPointsRulesService struct {
	db *gorm.DB
}

// NewUserPointsRulesService 创建叶小币规则服务
func NewUserPointsRulesService(db *gorm.DB) *UserPointsRulesService {
	return &UserPointsRulesService{
		db: db,
	}
}

// SaveRules 批量保存积分规则
func (s *UserPointsRulesService) SaveRules(ctx context.Context, req model.SaveRulesRequest) error {
	// 添加性能监控
	start := time.Now()
	defer func() {
		duration := time.Since(start)
		log.Printf("规则更新完成，耗时: %v, 等级ID: %d, 规则数量: %d", duration, req.LevelID, len(req.Rules))
	}()

	// 验证规则数据
	if err := s.validateRules(req.Rules); err != nil {
		return err
	}

	// 使用 GORM 事务
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 处理等级范围
		levelIDs := []int{req.LevelID}
		if req.SyncAllLevel {
			// 获取所有等级ID
			allLevels, err := s.getAllLevelIDs(ctx)
			if err != nil {
				return fmt.Errorf("获取所有等级失败: %w", err)
			}
			levelIDs = allLevels
		}

		// 批量保存规则 - 分批处理以提高性能
		batchSize := 10
		for i := 0; i < len(levelIDs); i += batchSize {
			end := i + batchSize
			if end > len(levelIDs) {
				end = len(levelIDs)
			}
			batch := levelIDs[i:end]
			
			// 处理每个批次
			for _, levelID := range batch {
				// 先软删除该等级所有现有规则（设置DeletedAt字段）
				if err := tx.Table("coin_rules").Where("user_level_id = ?", levelID).Update("deleted_at", time.Now()).Error; err != nil {
					return fmt.Errorf("软删除等级 %d 的规则失败: %w", levelID, err)
				}

				// 批量插入新规则
				if err := s.batchInsertRules(tx, levelID, req.Rules, req.ExpiryPolicy); err != nil {
					return fmt.Errorf("批量插入等级 %d 的规则失败: %w", levelID, err)
				}
			}
		}

		return nil
	})
}

// 验证规则数据
func (s *UserPointsRulesService) validateRules(rules []model.RuleConfig) error {
	for _, rule := range rules {
		// 验证基础积分范围
		if rule.BasePoints < 0 || rule.BasePoints > 9999 {
			return fmt.Errorf("规则 %s 的基础积分超出范围 (0-9999)", rule.RuleType)
		}
		
		// 验证额外积分
		if rule.ExtraPoints < 0 || rule.ExtraPoints > 9999 {
			return fmt.Errorf("规则 %s 的额外积分超出范围 (0-9999)", rule.RuleType)
		}
		
		// 验证金额阈值
		params := s.GetRuleParameters()
		if config, ok := params[rule.RuleType]; ok {
			if config.HasMinAmount && rule.MinAmount < 0 {
				return fmt.Errorf("规则 %s 的最小金额不能为负数", rule.RuleType)
			}
			
			if config.HasAmountStep && rule.AmountStep < 0 {
				return fmt.Errorf("规则 %s 的步长不能为负数", rule.RuleType)
			}
		} else {
			return fmt.Errorf("未知的规则类型: %s", rule.RuleType)
		}
	}
	return nil
}

// 批量插入规则
func (s *UserPointsRulesService) batchInsertRules(tx *gorm.DB, levelID int, rules []model.RuleConfig, expiryPolicy *model.ExpiryPolicy) error {
	// 准备批量插入
	var values []map[string]interface{}
	
	for _, rule := range rules {
		// 设置规则名称
		ruleName := s.getRuleTypeName(rule.RuleType)
		
		// 构建规则数据
		ruleData := map[string]interface{}{
			"user_level_id":      levelID,
			"rule_type":          rule.RuleType,
			"rule_name":          ruleName,
			"enabled":            rule.Enabled,
			"coins_awarded":      rule.BasePoints,
			"min_amount":         rule.MinAmount,
			"amount_threshold":   rule.AmountStep,
			"expiry_policy_type": expiryPolicy.Policy,
			"custom_years":       expiryPolicy.CustomYears,
			"created_at":         time.Now(),
			"updated_at":         time.Now(),
		}
		
		values = append(values, ruleData)
	}
	
	// 批量创建规则
	if len(values) > 0 {
		if err := tx.Table("coin_rules").Create(values).Error; err != nil {
			return err
		}
	}
	
	return nil
}

// 获取所有等级ID
func (s *UserPointsRulesService) getAllLevelIDs(ctx context.Context) ([]int, error) {
	rows, err := s.db.WithContext(ctx).Raw("SELECT id FROM user_level_rules WHERE deleted_at IS NULL").Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var levelIDs []int
	for rows.Next() {
		var id int
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		levelIDs = append(levelIDs, id)
	}
	return levelIDs, nil
}

// 获取规则类型名称
func (s *UserPointsRulesService) getRuleTypeName(ruleType string) string {
	ruleNames := map[string]string{
		model.RuleTypeRegister:        "注册奖励",
		model.RuleTypeCompleteProfile: "完善信息奖励",
		model.RuleTypeActivity:        "活动参与奖励",
		model.RuleTypeCheckin:         "打卡奖励",
		model.RuleTypeCheckinShare:    "打卡分享奖励",
		model.RuleTypeReferral:        "推荐奖励",
		model.RuleTypeConsume:         "消费奖励",
		model.RuleTypeConsumeAccumulate: "累计消费奖励",
		model.RuleTypeReferralConsume:  "推荐人消费奖励",
	}

	if name, ok := ruleNames[ruleType]; ok {
		return name
	}
	return "未知规则"
}

// GetRuleParameters 获取规则参数映射表
func (s *UserPointsRulesService) GetRuleParameters() map[string]struct {
	HasMinAmount  bool // 是否有金额/天数阈值
	HasExtra      bool // 是否有额外积分
	HasAmountStep bool // 是否有"每满"概念
} {
	return map[string]struct {
		HasMinAmount  bool
		HasExtra      bool
		HasAmountStep bool
	}{
		model.RuleTypeRegister:          {false, false, false},
		model.RuleTypeCompleteProfile:   {false, false, false},
		model.RuleTypeConsumeAccumulate: {true, false, true},  // 每满X元获得Y币
		model.RuleTypeConsume:           {true, false, false}, // 单笔满X元额外获得Y币
		model.RuleTypeActivity:          {false, false, false},
		model.RuleTypeCheckin:           {false, false, false}, // 签到奖励
		// 特殊活动奖励，使用额外积分字段
		"ACTIVITY_SPECIAL":              {false, true, false},  // 额外奖励
		model.RuleTypeCheckinShare:      {false, false, false},
		// 连续打卡，有基础和额外奖励
		"CHECKIN_CONTINUOUS":            {true, true, false}, // 连续X天，基础+额外
		model.RuleTypeReferral:          {false, false, false},
		model.RuleTypeReferralConsume:   {true, false, true},   // 每满X元获得Y币
	}
}

// GetRules 获取规则配置
func (s *UserPointsRulesService) GetRules(ctx context.Context, levelID int) ([]model.RuleConfig, *model.ExpiryPolicy, error) {
	// 获取该等级的所有规则
	var rules []struct {
		RuleType         string
		Enabled          bool
		CoinsAwarded     int
		MinAmount        int
		AmountThreshold  int
		ExpiryPolicyType string
		CustomYears      int
	}

	err := s.db.WithContext(ctx).Table("coin_rules").
		Select("rule_type, enabled, coins_awarded, min_amount, amount_threshold, expiry_policy_type, custom_years").
		Where("user_level_id = ? AND deleted_at IS NULL", levelID).
		Find(&rules).Error

	if err != nil {
		return nil, nil, err
	}

	// 转换成前端需要的格式
	var ruleConfigs []model.RuleConfig
	var expiryPolicy *model.ExpiryPolicy

	for _, rule := range rules {
		// 设置规则配置
		ruleConfig := model.RuleConfig{
			RuleType:    rule.RuleType,
			Enabled:     rule.Enabled,
			BasePoints:  rule.CoinsAwarded,
			ExtraPoints: 0,
			MinAmount:   rule.MinAmount,
			AmountStep:  rule.AmountThreshold,
		}
		ruleConfigs = append(ruleConfigs, ruleConfig)

		// 从第一个规则获取过期策略（因为所有规则都使用相同的过期策略）
		if expiryPolicy == nil {
			expiryPolicy = &model.ExpiryPolicy{
				Policy:      rule.ExpiryPolicyType,
				CustomYears: rule.CustomYears,
			}
		}
	}

	// 如果没有规则，使用默认过期策略
	if expiryPolicy == nil {
		expiryPolicy = &model.ExpiryPolicy{
			Policy:      model.ExpiryTypePermanent,
			CustomYears: 0,
		}
	}

	return ruleConfigs, expiryPolicy, nil
} 