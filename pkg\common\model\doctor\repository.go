package doctor

import (
	"fmt"
	"strings"
	"time"

	"yekaitai/internal/modules/store/model"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// WxDoctorRepository 医生仓库接口
type WxDoctorRepository interface {
	// 基础操作
	Create(doctor *WxDoctor) error
	Update(doctor *WxDoctor) error
	Delete(id uint) error
	FindByID(id uint) (*WxDoctor, error)
	FindByUserID(userID uint) (*WxDoctor, error)
	List(page, size int, name, mobile, hospital, department string, isRecommended *bool, sortBy, sortOrder string) ([]*WxDoctor, int64, error)
	UpdateStatus(id uint, status int) error
	// 根据手机号查找医生
	FindByMobile(mobile string) (*WxDoctor, error)
	// 添加医生与门店的关联
	SetStores(doctor *WxDoctor, storeIDs []uint, primaryStoreID uint) error
	// 根据医生ID和门店ID获取主要门店标记
	IsPrimaryStore(doctorID, storeID uint) (bool, error)
	// 根据科室获取医生
	GetDoctorsByDepartment(department string, limit int) ([]*WxDoctor, error)
}

// wxDoctorRepository 医生仓库实现
type wxDoctorRepository struct {
	db *gorm.DB // 保留db字段以兼容接口，但不在方法中使用
}

// NewWxDoctorRepository 创建医生仓库
func NewWxDoctorRepository(db *gorm.DB) WxDoctorRepository {
	return &wxDoctorRepository{
		db: db,
	}
}

// Create 创建医生
func (r *wxDoctorRepository) Create(doctor *WxDoctor) error {
	return mysql.Master().Create(doctor).Error
}

// Update 更新医生
func (r *wxDoctorRepository) Update(doctor *WxDoctor) error {
	return mysql.Master().Save(doctor).Error
}

// Delete 删除医生
func (r *wxDoctorRepository) Delete(id uint) error {
	return mysql.Master().Delete(&WxDoctor{}, id).Error
}

// FindByID 根据ID查找医生
func (r *wxDoctorRepository) FindByID(id uint) (*WxDoctor, error) {
	var doctor WxDoctor
	// 首先查询医生基本信息，同时预加载标签
	err := mysql.Slave().Preload("Tags").Where("doctor_id = ?", id).First(&doctor).Error
	if err != nil {
		return nil, err
	}

	// 手动加载门店信息，使用Unscoped()跳过软删除条件
	var doctorStores []DoctorStoreRelation
	if err := mysql.Slave().Unscoped().Where("doctor_id = ?", id).Find(&doctorStores).Error; err != nil {
		return nil, err
	}

	// 如果有门店关联，获取门店详情
	if len(doctorStores) > 0 {
		var storeIDs []uint
		for _, ds := range doctorStores {
			storeIDs = append(storeIDs, ds.StoreID)
		}

		// 查询门店详情
		var stores []model.Store
		if err := mysql.Slave().Where("id IN ?", storeIDs).Find(&stores).Error; err != nil {
			return nil, err
		}

		doctor.Stores = stores
	}

	return &doctor, nil
}

// FindByUserID 根据用户ID查找医生
func (r *wxDoctorRepository) FindByUserID(userID uint) (*WxDoctor, error) {
	var doctor WxDoctor
	err := mysql.Slave().Where("user_id = ?", userID).First(&doctor).Error
	if err != nil {
		return nil, err
	}
	return &doctor, nil
}

// List 获取医生列表
func (r *wxDoctorRepository) List(page, size int, name, mobile, hospital, department string, isRecommended *bool, sortBy, sortOrder string) ([]*WxDoctor, int64, error) {
	// 计算偏移量
	offset := (page - 1) * size
	if offset < 0 {
		offset = 0
	}

	// 设置默认排序
	if sortBy == "" {
		sortBy = "created_at"
	}

	if sortOrder == "" {
		sortOrder = "DESC"
	} else {
		sortOrder = strings.ToUpper(sortOrder)
	}

	// 基础查询
	db := mysql.Slave().Model(&WxDoctor{})

	// 按医生姓名筛选
	if name != "" {
		db = db.Where("name LIKE ?", "%"+name+"%")
	}

	// 按医生手机号筛选
	if mobile != "" {
		db = db.Where("mobile LIKE ?", "%"+mobile+"%")
	}

	// 按医院名称筛选
	if hospital != "" {
		db = db.Where("hospital LIKE ?", "%"+hospital+"%")
	}

	// 科室筛选
	if department != "" {
		db = db.Where("department = ?", department)
	}

	// 是否只查询推荐医生
	if isRecommended != nil {
		db = db.Where("is_recommended = ?", *isRecommended)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，预加载标签关联
	var doctors []*WxDoctor
	if err := db.Preload("Tags").Order(fmt.Sprintf("%s %s", sortBy, sortOrder)).Offset(offset).Limit(size).Find(&doctors).Error; err != nil {
		return nil, 0, err
	}

	// 为每个医生加载门店信息
	if len(doctors) > 0 {
		// 获取所有医生ID
		var doctorIDs []uint
		for _, doctor := range doctors {
			doctorIDs = append(doctorIDs, doctor.DoctorID)
		}

		// 查询所有医生-门店关联
		var doctorStores []DoctorStoreRelation
		if err := mysql.Slave().Unscoped().Where("doctor_id IN ?", doctorIDs).Find(&doctorStores).Error; err != nil {
			return nil, 0, err
		}

		// 收集所有门店ID
		var storeIDs []uint
		doctorStoreMap := make(map[uint][]uint) // 医生ID -> 门店ID列表
		for _, ds := range doctorStores {
			storeIDs = append(storeIDs, ds.StoreID)
			doctorStoreMap[ds.DoctorID] = append(doctorStoreMap[ds.DoctorID], ds.StoreID)
		}

		// 一次性查询所有门店
		var stores []model.Store
		if len(storeIDs) > 0 {
			if err := mysql.Slave().Where("id IN ?", storeIDs).Find(&stores).Error; err != nil {
				return nil, 0, err
			}
		}

		// 创建门店ID到门店对象的映射
		storeMap := make(map[uint]model.Store)
		for _, store := range stores {
			storeMap[store.ID] = store
		}

		// 为每个医生设置门店
		for _, doctor := range doctors {
			storeIDs := doctorStoreMap[doctor.DoctorID]
			if len(storeIDs) > 0 {
				doctorStores := make([]model.Store, 0, len(storeIDs))
				for _, storeID := range storeIDs {
					if store, ok := storeMap[storeID]; ok {
						doctorStores = append(doctorStores, store)
					}
				}
				doctor.Stores = doctorStores
			}
		}
	}

	return doctors, total, nil
}

// UpdateStatus 更新医生状态
func (r *wxDoctorRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&WxDoctor{}).Where("doctor_id = ?", id).Update("status", status).Error
}

// FindByMobile 根据手机号查找医生
func (r *wxDoctorRepository) FindByMobile(mobile string) (*WxDoctor, error) {
	var doctor WxDoctor
	// 直接从医生表查询手机号
	err := mysql.Slave().Where("mobile = ?", mobile).First(&doctor).Error
	if err != nil {
		return nil, err
	}
	return &doctor, nil
}

// SetStores 设置医生关联的门店
func (r *wxDoctorRepository) SetStores(doctor *WxDoctor, storeIDs []uint, primaryStoreID uint) error {
	// 使用事务处理
	tx := mysql.Master().Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 回滚事务的辅助函数
	rollback := func(err error) error {
		tx.Rollback()
		return err
	}

	// 清除医生现有的所有门店关联
	if err := tx.Where("doctor_id = ?", doctor.DoctorID).Delete(&DoctorStoreRelation{}).Error; err != nil {
		return rollback(err)
	}

	// 如果没有门店ID，提交事务并返回
	if len(storeIDs) == 0 {
		return tx.Commit().Error
	}

	// 创建新的门店关联
	for _, storeID := range storeIDs {
		isPrimary := (storeID == primaryStoreID)

		doctorStore := &DoctorStoreRelation{
			DoctorID:  doctor.DoctorID,
			StoreID:   storeID,
			IsPrimary: isPrimary,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		if err := tx.Create(doctorStore).Error; err != nil {
			return rollback(err)
		}
	}

	// 更新医生对象中的Stores字段，以便在请求处理完成后正确显示门店信息
	if err := tx.Commit().Error; err != nil {
		return err
	}

	// 重新加载医生的门店信息
	var stores []model.Store
	if err := mysql.Slave().Where("id IN ?", storeIDs).Find(&stores).Error; err != nil {
		return err
	}
	doctor.Stores = stores

	return nil
}

// IsPrimaryStore 检查是否为主要门店
func (r *wxDoctorRepository) IsPrimaryStore(doctorID, storeID uint) (bool, error) {
	var doctorStore DoctorStoreRelation
	err := mysql.Slave().Where("doctor_id = ? AND store_id = ?", doctorID, storeID).First(&doctorStore).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil // 如果没有找到关联记录，返回false而不是错误
		}
		return false, err
	}
	return doctorStore.IsPrimary, nil
}

// GetDoctorsByDepartment 根据科室获取医生
func (r *wxDoctorRepository) GetDoctorsByDepartment(department string, limit int) ([]*WxDoctor, error) {
	var doctors []*WxDoctor

	err := mysql.Slave().Where("status = 1").
		Where("department = ?", department).
		Order("created_at DESC").
		Limit(limit).
		Find(&doctors).Error

	return doctors, err
}

// LoadDoctorStores 加载医生的门店信息
func (r *wxDoctorRepository) LoadDoctorStores(doctor *WxDoctor) error {
	var stores []model.Store
	err := mysql.Slave().
		Table("t_stores").
		Joins("JOIN doctor_store_relation ON t_stores.id = doctor_store_relation.store_id").
		Where("doctor_store_relation.doctor_id = ?", doctor.DoctorID).
		Find(&stores).Error

	if err != nil {
		return err
	}

	doctor.Stores = stores
	return nil
}

// LoadDoctorTags 加载医生的标签信息
func (r *wxDoctorRepository) LoadDoctorTags(doctor *WxDoctor) error {
	var tags []user.Tag
	err := mysql.Slave().
		Table("tag").
		Joins("JOIN doctor_tag ON tag.id = doctor_tag.tag_id").
		Where("doctor_tag.doctor_id = ?", doctor.DoctorID).
		Find(&tags).Error

	if err != nil {
		return err
	}

	doctor.Tags = tags
	return nil
}

// LoadDoctorsStores 批量加载多个医生的门店信息
func (r *wxDoctorRepository) LoadDoctorsStores(doctors []*WxDoctor) error {
	if len(doctors) == 0 {
		return nil
	}

	// 收集所有医生ID
	doctorIDs := make([]uint, len(doctors))
	for i, doctor := range doctors {
		doctorIDs[i] = doctor.DoctorID
	}

	// 查询所有关联关系
	var relations []struct {
		DoctorID uint        `json:"doctor_id"`
		Store    model.Store `json:"store"`
	}

	err := mysql.Slave().
		Table("doctor_store_relation").
		Select("doctor_store_relation.doctor_id, t_stores.*").
		Joins("JOIN t_stores ON doctor_store_relation.store_id = t_stores.id").
		Where("doctor_store_relation.doctor_id IN ?", doctorIDs).
		Scan(&relations).Error

	if err != nil {
		return err
	}

	// 按医生ID分组
	storeMap := make(map[uint][]model.Store)
	for _, rel := range relations {
		storeMap[rel.DoctorID] = append(storeMap[rel.DoctorID], rel.Store)
	}

	// 设置每个医生的门店
	for _, doctor := range doctors {
		if stores, exists := storeMap[doctor.DoctorID]; exists {
			doctor.Stores = stores
		}
	}

	return nil
}

// LoadDoctorsTags 批量加载多个医生的标签信息
func (r *wxDoctorRepository) LoadDoctorsTags(doctors []*WxDoctor) error {
	if len(doctors) == 0 {
		return nil
	}

	// 收集所有医生ID
	doctorIDs := make([]uint, len(doctors))
	for i, doctor := range doctors {
		doctorIDs[i] = doctor.DoctorID
	}

	// 查询所有关联关系
	var relations []struct {
		DoctorID uint     `json:"doctor_id"`
		Tag      user.Tag `json:"tag"`
	}

	err := mysql.Slave().
		Table("doctor_tag").
		Select("doctor_tag.doctor_id, tag.*").
		Joins("JOIN tag ON doctor_tag.tag_id = tag.id").
		Where("doctor_tag.doctor_id IN ?", doctorIDs).
		Scan(&relations).Error

	if err != nil {
		return err
	}

	// 按医生ID分组
	tagMap := make(map[uint][]user.Tag)
	for _, rel := range relations {
		tagMap[rel.DoctorID] = append(tagMap[rel.DoctorID], rel.Tag)
	}

	// 设置每个医生的标签
	for _, doctor := range doctors {
		if tags, exists := tagMap[doctor.DoctorID]; exists {
			doctor.Tags = tags
		}
	}

	return nil
}

// DoctorDisplayRepository 医生展示信息仓库接口
type DoctorDisplayRepository interface {
	Create(display *DoctorDisplay) error
	Update(display *DoctorDisplay) error
	Delete(displayID uint) error
	FindByID(displayID uint) (*DoctorDisplay, error)
	FindByDoctorID(doctorID uint) (*DoctorDisplay, error)
	ListByDisplayOrder(page, size int, isRecommended bool) ([]*DoctorDisplay, int64, error)
}

// doctorDisplayRepository 医生展示信息仓库实现
type doctorDisplayRepository struct {
	db *gorm.DB // 保留db字段以兼容接口，但不在方法中使用
}

// NewDoctorDisplayRepository 创建医生展示信息仓库
func NewDoctorDisplayRepository(db *gorm.DB) DoctorDisplayRepository {
	return &doctorDisplayRepository{
		db: db,
	}
}

// Create 创建医生展示信息
func (r *doctorDisplayRepository) Create(display *DoctorDisplay) error {
	return mysql.Master().Create(display).Error
}

// Update 更新医生展示信息
func (r *doctorDisplayRepository) Update(display *DoctorDisplay) error {
	return mysql.Master().Save(display).Error
}

// Delete 删除医生展示信息
func (r *doctorDisplayRepository) Delete(displayID uint) error {
	return mysql.Master().Delete(&DoctorDisplay{}, displayID).Error
}

// FindByID 根据ID查找医生展示信息
func (r *doctorDisplayRepository) FindByID(displayID uint) (*DoctorDisplay, error) {
	var display DoctorDisplay
	err := mysql.Slave().Where("id = ?", displayID).First(&display).Error
	if err != nil {
		return nil, err
	}
	return &display, nil
}

// FindByDoctorID 根据医生ID查找展示信息
func (r *doctorDisplayRepository) FindByDoctorID(doctorID uint) (*DoctorDisplay, error) {
	var display DoctorDisplay
	err := mysql.Slave().Where("doctor_id = ?", doctorID).First(&display).Error
	if err != nil {
		return nil, err
	}
	return &display, nil
}

// ListByDisplayOrder 按展示顺序获取医生展示信息列表
func (r *doctorDisplayRepository) ListByDisplayOrder(page, size int, isRecommended bool) ([]*DoctorDisplay, int64, error) {
	// 计算偏移量
	offset := (page - 1) * size
	if offset < 0 {
		offset = 0
	}

	// 基础查询
	db := mysql.Slave().Model(&DoctorDisplay{})

	// 是否只查询推荐医生
	if isRecommended {
		db = db.Where("is_recommended = ?", true)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	var displays []*DoctorDisplay
	if err := db.Order("display_order DESC").Offset(offset).Limit(size).Find(&displays).Error; err != nil {
		return nil, 0, err
	}

	return displays, total, nil
}

// DoctorStoreRelationRepository 医生门店关系仓库接口
type DoctorStoreRelationRepository interface {
	Create(relation *DoctorStoreRelation) error
	Update(relation *DoctorStoreRelation) error
	Delete(relationID uint) error
	DeleteByDoctorID(doctorID uint) error
	FindByID(relationID uint) (*DoctorStoreRelation, error)
	FindByDoctorID(doctorID uint) ([]*DoctorStoreRelation, error)
	FindByStoreID(storeID uint) ([]*DoctorStoreRelation, error)
}

// doctorStoreRelationRepository 医生门店关系仓库实现
type doctorStoreRelationRepository struct {
	db *gorm.DB // 保留db字段以兼容接口，但不在方法中使用
}

// NewDoctorStoreRelationRepository 创建医生门店关系仓库
func NewDoctorStoreRelationRepository(db *gorm.DB) DoctorStoreRelationRepository {
	return &doctorStoreRelationRepository{
		db: db,
	}
}

// Create 创建医生门店关系
func (r *doctorStoreRelationRepository) Create(relation *DoctorStoreRelation) error {
	return mysql.Master().Create(relation).Error
}

// Update 更新医生门店关系
func (r *doctorStoreRelationRepository) Update(relation *DoctorStoreRelation) error {
	return mysql.Master().Save(relation).Error
}

// Delete 删除医生门店关系
func (r *doctorStoreRelationRepository) Delete(relationID uint) error {
	return mysql.Master().Delete(&DoctorStoreRelation{}, relationID).Error
}

// DeleteByDoctorID 删除医生的所有门店关系
func (r *doctorStoreRelationRepository) DeleteByDoctorID(doctorID uint) error {
	return mysql.Master().Where("doctor_id = ?", doctorID).Delete(&DoctorStoreRelation{}).Error
}

// FindByID 根据ID查找医生门店关系
func (r *doctorStoreRelationRepository) FindByID(relationID uint) (*DoctorStoreRelation, error) {
	var relation DoctorStoreRelation
	err := mysql.Slave().Where("id = ?", relationID).First(&relation).Error
	if err != nil {
		return nil, err
	}
	return &relation, nil
}

// FindByDoctorID 根据医生ID查找门店关系
func (r *doctorStoreRelationRepository) FindByDoctorID(doctorID uint) ([]*DoctorStoreRelation, error) {
	var relations []*DoctorStoreRelation
	err := mysql.Slave().Where("doctor_id = ?", doctorID).Find(&relations).Error
	if err != nil {
		return nil, err
	}
	return relations, nil
}

// FindByStoreID 根据门店ID查找医生关系
func (r *doctorStoreRelationRepository) FindByStoreID(storeID uint) ([]*DoctorStoreRelation, error) {
	var relations []*DoctorStoreRelation
	err := mysql.Slave().Where("store_id = ?", storeID).Find(&relations).Error
	if err != nil {
		return nil, err
	}
	return relations, nil
}
