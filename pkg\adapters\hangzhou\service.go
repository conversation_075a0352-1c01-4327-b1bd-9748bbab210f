package hangzhou

import (
	"context"
	"strconv"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
)

// Service HIS服务适配器
type Service struct {
	client *Client
}

// NewService 创建HIS服务适配器
func NewService(configFile string) (*Service, error) {
	var config struct {
		HangzhouHIS Config
	}

	// 从配置文件加载
	err := conf.Load(configFile, &config)
	if err != nil {
		logx.Errorf("加载杭州HIS系统配置失败: %v", err)
		return nil, err
	}

	// 初始化客户端
	client := NewClient(config.HangzhouHIS)

	// 设置默认客户端
	DefaultClient = client

	logx.Infof("杭州HIS系统服务已初始化: BaseURL=%s", config.HangzhouHIS.BaseURL)

	return &Service{client: client}, nil
}

// Client 获取HIS客户端
func (s *Service) Client() *Client {
	return s.client
}

// GetAccessToken 获取访问令牌
func (s *Service) GetAccessToken(ctx context.Context) (string, error) {
	return s.client.GetAccessToken(ctx)
}

// GetHealthOrganization 获取卫生机构信息
func (s *Service) GetHealthOrganization(ctx context.Context, wsjgID int) (*BaseResp, error) {
	return s.client.GetHealthOrganization(ctx, wsjgID)
}

// GetDepartments 获取机构科室列表
func (s *Service) GetDepartments(ctx context.Context, wsjgID int, pbksbz int) (*BaseResp, error) {
	return s.client.GetDepartments(ctx, pbksbz, strconv.Itoa(wsjgID))
}

// GetUsers 获取用户列表
func (s *Service) GetUsers(ctx context.Context, qybz, zfbz string) (*BaseResp, error) {
	return s.client.GetUsers(ctx, qybz, zfbz)
}

// GetUserDepartments 获取用户科室列表
func (s *Service) GetUserDepartments(ctx context.Context, wsjgID, yhID int, zfbz string) (*BaseResp, error) {
	return s.client.GetUserDepartments(ctx, wsjgID, yhID, zfbz)
}

// GetHealthPersons 获取卫生人员列表
func (s *Service) GetHealthPersons(ctx context.Context, wsjgID, yhID int, zfbz string) (*BaseResp, error) {
	return s.client.GetHealthPersons(ctx, wsjgID, yhID, zfbz)
}
