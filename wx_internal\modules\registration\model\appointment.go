package model

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/adapters/hangzhou"
	model_registration "yekaitai/pkg/common/model/registration"
	repo_registration "yekaitai/pkg/common/repository/registration"
	departmentModel "yekaitai/wx_internal/modules/department/model"
	qrmodel "yekaitai/wx_internal/modules/qrcode-management/model"

	"github.com/google/uuid"
)

// HangzhouAppointmentRequest 杭州HIS预约挂号请求
type HangzhouAppointmentRequest struct {
	// 杭州HIS特有字段（int类型）
	HyjlID int `json:"hyjlid"` // 号源记录ID（必填）
	GrxxID int `json:"grxxid"` // 个人信息ID（必填）
	WsjgID int `json:"wsjgid"` // 卫生机构ID（必填）
	JgksID int `json:"jgksid"` // 机构科室ID（必填）

	// 杭州HIS其他字段
	YsID   string `json:"ysid"`             // 医生ID（必填，普通医生传"0"）
	YsMC   string `json:"ysmc,omitempty"`   // 医生名称（可选）
	JgksMC string `json:"jgksmc,omitempty"` // 机构科室名称（可选）
	WsjgMC string `json:"wsjgmc,omitempty"` // 卫生机构名称（可选）

	// 本地系统字段
	WxDoctorID    int    `json:"wx_doctor_id"`   // 本地医生ID（必填）
	WxPatientID   int    `json:"wx_patient_id"`  // 本地患者ID（必填）
	StoreID       int    `json:"store_id"`       // 门店ID（必填）
	AppointDate   string `json:"appoint_date"`   // 预约日期（必填）
	AppointPeriod int    `json:"appoint_period"` // 预约时段（必填）1-上午, 2-下午, 3-夜班

	// 通用字段
	YyLx  int    `json:"yylx,omitempty"`  // 预约类型 1:现场预约 2:自助机预约 3:电话预约 4:网上预约 5:微信小程序预约（可选，默认5）
	BzXx  string `json:"bzxx,omitempty"`  // 备注信息（可选）
	CzrID int    `json:"czrid,omitempty"` // 操作人ID（可选）
	Czr   string `json:"czr,omitempty"`   // 操作人（可选）
}

// AbcYunAppointmentRequest ABC云预约挂号请求
type AbcYunAppointmentRequest struct {
	// ABC云特有字段（string类型）
	AbcYunPatientID    string `json:"abcyun_patient_id"`    // ABC云患者ID（必填）
	AbcYunDoctorID     string `json:"abcyun_doctor_id"`     // ABC云医生ID（必填）
	AbcYunDepartmentID string `json:"abcyun_department_id"` // ABC云科室ID（必填）
	WsjgIDStr          string `json:"wsjgid_str"`           // 卫生机构ID字符串格式（必填）

	// ABC云预约相关字段
	OrderNo          int    `json:"order_no"`          // ABC云排队号（必填）
	ReserveStart     string `json:"reserve_start"`     // ABC云预约开始时间（必填）
	ReserveEnd       string `json:"reserve_end"`       // ABC云预约结束时间（必填）
	RegistrationType int    `json:"registration_type"` // ABC云预约类型，0:门诊预约 1:理疗预约（必填）

	// ABC云可选字段
	SourceID     string `json:"source_id,omitempty"`      // ABC云来源ID（可选）
	SourceFromID string `json:"source_from_id,omitempty"` // ABC云来源From ID（可选）
	SourceRemark string `json:"source_remark,omitempty"`  // ABC云来源备注（可选）

	// 本地系统字段
	WxDoctorID    int    `json:"wx_doctor_id"`   // 本地医生ID（必填）
	WxPatientID   int    `json:"wx_patient_id"`  // 本地患者ID（必填）
	StoreID       int    `json:"store_id"`       // 门店ID（必填）
	AppointDate   string `json:"appoint_date"`   // 预约日期（必填）
	AppointPeriod int    `json:"appoint_period"` // 预约时段（必填）1-上午, 2-下午, 3-夜班

	// 通用字段
	YyLx  int    `json:"yylx,omitempty"`  // 预约类型 1:现场预约 2:自助机预约 3:电话预约 4:网上预约 5:微信小程序预约（可选，默认5）
	BzXx  string `json:"bzxx,omitempty"`  // 备注信息（可选）
	CzrID int    `json:"czrid,omitempty"` // 操作人ID（可选）
	Czr   string `json:"czr,omitempty"`   // 操作人（可选）
}

// AppointmentRequest 统一预约挂号请求（用于接口层）
type AppointmentRequest struct {
	HisType int `json:"his_type"` // HIS系统类型，1-杭州HIS，2-ABC云（必填）

	// 杭州HIS请求（当his_type=1时使用）
	HangzhouRequest *HangzhouAppointmentRequest `json:"hangzhou_request,omitempty"`

	// ABC云请求（当his_type=2时使用）
	AbcYunRequest *AbcYunAppointmentRequest `json:"abcyun_request,omitempty"`
}

// AppointmentResponse 预约挂号响应
type AppointmentResponse struct {
	// 预约基本信息
	YyghID int    `json:"yyghid"` // 预约挂号ID
	HyjlID int    `json:"hyjlid"` // 号源记录ID
	GrxxID int    `json:"grxxid"` // 个人信息ID
	YyLx   string `json:"yylx"`   // 预约类型

	// 患者信息
	Bah  string `json:"bah"`  // 病案号
	XM   string `json:"xm"`   // 姓名
	XbDM string `json:"xbdm"` // 性别代码
	XbMC string `json:"xbmc"` // 性别名称
	CsSJ string `json:"cssj"` // 出生时间
	ZJHM string `json:"zjhm"` // 证件号码
	ZJLx string `json:"zjlx"` // 证件类型

	// 号源信息
	QhMM    string  `json:"qhmm"`    // 取号密码
	HyZT    string  `json:"hyzt"`    // 号源状态
	HyRQ    string  `json:"hyrq"`    // 号源日期
	HySJ    string  `json:"hysj"`    // 号源时间
	HyXH    int     `json:"hyxh"`    // 号源序号
	GhF     float64 `json:"ghf"`     // 挂号费
	GhFID   int     `json:"ghfid"`   // 挂号费ID
	ZlF     float64 `json:"zlf"`     // 诊疗费
	ZlFID   int     `json:"zlfid"`   // 诊疗费ID
	PbLx    string  `json:"pblx"`    // 排班类型
	JkGlSID int     `json:"jkglsid"` // 健康管理师ID
	JkGlSMC string  `json:"jkglsmc"` // 健康管理师名称

	// 机构和医生信息
	WsjgID int    `json:"wsjgid"` // 卫生机构ID
	WsjgMC string `json:"wsjgmc"` // 卫生机构名称
	JgksID int    `json:"jgksid"` // 机构科室ID
	JgksMC string `json:"jgksmc"` // 机构科室名称
	YsID   string `json:"ysid"`   // 医生ID
	YsMC   string `json:"ysmc"`   // 医生名称

	// 其他字段
	CzrID int    `json:"czrid"` // 创建人ID
	CzRMC string `json:"czrmc"` // 创建人名称
	CzSJ  string `json:"czsj"`  // 创建时间
	ZfBz  int    `json:"zfbz"`  // 作废标志
	ZfRID int    `json:"zfrid"` // 作废人ID
	ZfRMC string `json:"zfrmc"` // 作废人名称
	ZfSJ  string `json:"zfsj"`  // 作废时间
	ZfYY  string `json:"zfyy"`  // 作废原因
	BZ    string `json:"bz"`    // 备注

	// ABC云相关字段
	RegistrationSheetID string `json:"registration_sheet_id,omitempty"` // ABC云挂号单ID
	PatientOrderID      string `json:"patient_order_id,omitempty"`      // ABC云就诊单ID
}

// AppointmentOrderListResponse 预约挂号订单列表响应
type AppointmentOrderListResponse struct {
	ID                uint   `json:"id"`                  // 订单ID
	OrderNo           string `json:"order_no"`            // 订单编号
	AppointDate       string `json:"appoint_date"`        // 预约日期
	AppointPeriod     int    `json:"appoint_period"`      // 预约时段
	AppointPeriodText string `json:"appoint_period_text"` // 预约时段文本
	SyncStatus        int    `json:"sync_status"`         // 同步状态
	SyncStatusText    string `json:"sync_status_text"`    // 同步状态文本
	OrderStatus       int    `json:"order_status"`        // 订单状态
	StatusText        string `json:"status_text"`         // 状态文本
	HisType           int    `json:"his_type"`            // HIS系统类型
	YyghID            int    `json:"yyghid"`              // 预约挂号ID
	GrxxID            int    `json:"grxxid"`              // 个人信息ID
	HyjlID            int    `json:"hyjlid"`              // 号源记录ID
	YylX              int    `json:"yylx"`                // 预约类型
	WxDoctorID        int    `json:"wx_doctor_id"`        // 医生ID
	WxPatientID       int    `json:"wx_patient_id"`       // 患者ID
	StoreID           int    `json:"store_id"`            // 门店ID
	// 新增名字字段
	PatientName            string `json:"patient_name"`             // 患者姓名
	DoctorName             string `json:"doctor_name"`              // 医生姓名
	StoreName              string `json:"store_name"`               // 门店名称
	ZfrID                  int    `json:"zfrid"`                    // 作废人ID
	ZfrMC                  string `json:"zfrmc"`                    // 作废人名称
	ZfYY                   string `json:"zfyy"`                     // 作废原因
	ZfSJ                   string `json:"zfsj"`                     // 作废时间
	CzrID                  int    `json:"czrid"`                    // 创建人ID
	Czr                    string `json:"czr"`                      // 创建人名称
	QRCodeURL              string `json:"qr_code_url"`              // 二维码URL
	VerificationCode       string `json:"verification_code"`        // 核销码
	VerificationStatus     int    `json:"verification_status"`      // 核销状态
	VerificationStatusText string `json:"verification_status_text"` // 核销状态文本
	VerifiedAt             string `json:"verified_at"`              // 核销时间
	VerifierID             int    `json:"verifier_id"`              // 核销人ID
	VerifierName           string `json:"verifier_name"`            // 核销人姓名
	JgksID                 int    `json:"jgksid"`                   // 机构科室ID
	DepartmentID           uint   `json:"department_id"`            // 科室ID(关联t_departments表)
	CanCancel              bool   `json:"can_cancel"`               // 是否可以取消
	CreatedAt              string `json:"created_at"`               // 创建时间
	UpdatedAt              string `json:"updated_at"`               // 更新时间
	// 新增全部表字段
	RequestData         string `json:"request_data"`          // 请求数据JSON
	ResponseData        string `json:"response_data"`         // 响应数据JSON
	RegistrationSheetID string `json:"registration_sheet_id"` // ABC云挂号单ID
	PatientOrderID      string `json:"patient_order_id"`      // ABC云就诊单ID
}

// AppointmentOrderDetailResponse 预约挂号订单详情响应
type AppointmentOrderDetailResponse struct {
	ID                uint   `json:"id"`                  // 订单ID
	OrderNo           string `json:"order_no"`            // 订单编号
	StoreAddress      string `json:"store_address"`       // 门店地址
	StorePhone        string `json:"store_phone"`         // 门店电话
	AppointDate       string `json:"appoint_date"`        // 预约日期
	AppointPeriod     int    `json:"appoint_period"`      // 预约时段
	AppointPeriodText string `json:"appoint_period_text"` // 预约时段文本
	Department        string `json:"department"`          // 科室
	OrderStatus       int    `json:"order_status"`        // 订单状态
	// 新增名字字段
	PatientName  string  `json:"patient_name"`  // 患者姓名
	DoctorName   string  `json:"doctor_name"`   // 医生姓名
	StoreName    string  `json:"store_name"`    // 门店名称
	StatusText   string  `json:"status_text"`   // 状态文本
	CanCancel    bool    `json:"can_cancel"`    // 是否可以取消
	OriginalFee  float64 `json:"original_fee"`  // 原始费用
	CouponAmount float64 `json:"coupon_amount"` // 优惠券金额
	PointsAmount float64 `json:"points_amount"` // 积分抵扣金额
	PayAmount    float64 `json:"pay_amount"`    // 支付金额
	CreatedAt    string  `json:"created_at"`    // 创建时间
	PayTime      string  `json:"pay_time"`      // 支付时间
	QRCodeURL    string  `json:"qr_code_url"`   // 二维码URL
	CancelReason string  `json:"cancel_reason"` // 取消原因
	CancelTime   string  `json:"cancel_time"`   // 取消时间
}

// AppointmentRepository 预约挂号仓库接口
type AppointmentRepository interface {
	CreateAppointment(ctx context.Context, req *AppointmentRequest) (*AppointmentResponse, error)
	SaveAppointment(ctx context.Context, resp *AppointmentResponse, req *AppointmentRequest, requestData, responseData string) error
	CancelAppointment(ctx context.Context, req *CancelAppointmentRequest) (*AppointmentResponse, error)
}

// CancelAppointmentRequest 取消预约挂号请求（统一结构）
type CancelAppointmentRequest struct {
	// 杭州HIS字段
	YyghID int `json:"yyghid,omitempty"` // 预约挂号ID（杭州HIS用）
	GrxxID int `json:"grxxid,omitempty"` // 个人信息ID（杭州HIS用）
	HyjlID int `json:"hyjlid,omitempty"` // 号源记录ID（杭州HIS用）
	YyLx   int `json:"yylx,omitempty"`   // 预约类型（杭州HIS用）

	// ABC云字段
	RegistrationSheetID string `json:"registration_sheet_id,omitempty"` // ABC云挂号单ID（ABC云用）
	PatientOrderID      string `json:"patient_order_id,omitempty"`      // ABC云就诊单ID（ABC云用）

	// 通用字段
	HisType int    `json:"his_type"` // HIS系统类型，1-杭州HIS，2-ABC云
	ZfrID   int    `json:"zfrid"`    // 作废人ID
	ZfrMC   string `json:"zfrmc"`    // 作废人名称
	ZfYy    string `json:"zfyy"`     // 作废原因
}

// appointmentRepository 预约挂号仓库实现
type appointmentRepository struct{}

// NewAppointmentRepository 创建预约挂号仓库
func NewAppointmentRepository() AppointmentRepository {
	return &appointmentRepository{}
}

// CreateAppointment 创建预约挂号
func (r *appointmentRepository) CreateAppointment(ctx context.Context, req *AppointmentRequest) (*AppointmentResponse, error) {
	// 根据HIS类型选择不同的处理逻辑
	if req.HisType == 2 { // ABC云
		return r.createAbcYunAppointment(ctx, req)
	} else { // 杭州HIS（默认）
		return r.createHangzhouAppointment(ctx, req)
	}
}

// createHangzhouAppointment 创建杭州HIS预约挂号
func (r *appointmentRepository) createHangzhouAppointment(ctx context.Context, req *AppointmentRequest) (*AppointmentResponse, error) {
	// 转换为杭州API的请求结构
	hangzhouReq := &hangzhou.AppointmentRequest{
		HyjlID: req.HangzhouRequest.HyjlID,
		GrxxID: req.HangzhouRequest.GrxxID,
		YyLx:   req.HangzhouRequest.YyLx,
		BzXx:   req.HangzhouRequest.BzXx,
		CzrID:  req.HangzhouRequest.CzrID,
		Czr:    req.HangzhouRequest.Czr,
		YsID:   req.HangzhouRequest.YsID, // 直接使用字符串类型
		YsMC:   req.HangzhouRequest.YsMC,
		JgksID: req.HangzhouRequest.JgksID, // 保持int类型，杭州HIS期望int
		JgksMC: req.HangzhouRequest.JgksMC,
		WsjgID: req.HangzhouRequest.WsjgID, // 保持int类型，杭州HIS期望int
		WsjgMC: req.HangzhouRequest.WsjgMC,
	}

	// 将请求参数保存为JSON字符串
	requestJSON, err := json.Marshal(hangzhouReq)
	if err != nil {
		logx.WithContext(ctx).Errorf("序列化请求参数失败: %v", err)
		requestJSON = []byte("{}")
	}

	// 记录发送的请求参数
	logx.WithContext(ctx).Infof("发送到杭州HIS的预约挂号请求: hyjlID=%d, grxxID=%d, yyLx=%d",
		hangzhouReq.HyjlID, hangzhouReq.GrxxID, hangzhouReq.YyLx)

	// 调用杭州API
	resp, err := hangzhou.CreateAppointment(ctx, hangzhouReq)
	if err != nil {
		logx.WithContext(ctx).Errorf("创建预约挂号失败: %v", err)
		return nil, err
	}

	// 将响应数据转换为字符串查看原始内容
	respBytes, err := json.Marshal(resp.Data)
	if err != nil {
		logx.WithContext(ctx).Errorf("Marshal预约挂号响应失败: %v", err)
		respBytes = []byte("{}")
	}
	logx.WithContext(ctx).Infof("预约挂号原始响应: %s", string(respBytes))

	// 使用json.RawMessage接收原始JSON数据
	var rawJson json.RawMessage
	if respBytes, err := json.Marshal(resp.Data); err == nil {
		rawJson = respBytes
	} else {
		logx.WithContext(ctx).Errorf("转换响应为RawJSON失败: %v", err)
		return nil, fmt.Errorf("转换响应数据失败: %v", err)
	}

	// 尝试直接解析到结构体中
	var result AppointmentResponse
	decoder := json.NewDecoder(bytes.NewReader(rawJson))
	decoder.UseNumber() // 使用Number接口避免浮点数问题

	if err := decoder.Decode(&result); err != nil {
		logx.WithContext(ctx).Errorf("解析预约挂号响应失败: %v, 原始数据: %s", err, string(rawJson))

		// 失败则回退到map解析方式
		var respMap map[string]interface{}
		if err := json.Unmarshal(rawJson, &respMap); err != nil {
			logx.WithContext(ctx).Errorf("解析到map也失败: %v", err)
			return nil, fmt.Errorf("解析响应数据失败: %v", err)
		}

		// 使用之前编写的字段提取逻辑从map中构建结果
		result = extractFromMap(respMap)
	}

	// 记录关键字段日志
	logx.WithContext(ctx).Infof("成功创建预约挂号，关键字段: yyghID=%d, grxxID=%d, hyjlID=%d, yylx=%v",
		result.YyghID, req.HangzhouRequest.GrxxID, req.HangzhouRequest.HyjlID, req.HangzhouRequest.YyLx)

	// 保存预约信息到数据库
	if err := r.SaveAppointment(ctx, &result, req, string(requestJSON), string(respBytes)); err != nil {
		logx.WithContext(ctx).Errorf("保存预约挂号信息到数据库失败: %v", err)
		// 注意：这里不返回错误，因为预约已经创建成功，数据库保存失败不应影响用户
	}

	return &result, nil
}

// createAbcYunAppointment 创建ABC云预约挂号
func (r *appointmentRepository) createAbcYunAppointment(ctx context.Context, req *AppointmentRequest) (*AppointmentResponse, error) {
	// 导入ABC云客户端
	abcyunClient := abcyun.GetGlobalClient()
	if abcyunClient == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// 构建ABC云创建挂号请求
	abcyunReq := map[string]interface{}{
		"patientId":              req.AbcYunRequest.AbcYunPatientID,
		"departmentId":           req.AbcYunRequest.AbcYunDepartmentID,
		"doctorId":               req.AbcYunRequest.AbcYunDoctorID,
		"orderNo":                req.AbcYunRequest.OrderNo,
		"reserveDate":            req.AbcYunRequest.AppointDate,
		"reserveStart":           req.AbcYunRequest.ReserveStart,
		"reserveEnd":             req.AbcYunRequest.ReserveEnd,
		"registrationType":       req.AbcYunRequest.RegistrationType,
		"registrationProductIds": []int{0}, // 默认值
	}

	// 添加可选字段
	if req.AbcYunRequest.SourceID != "" {
		abcyunReq["sourceId"] = req.AbcYunRequest.SourceID
	}
	if req.AbcYunRequest.SourceFromID != "" {
		abcyunReq["sourceFromId"] = req.AbcYunRequest.SourceFromID
	}
	if req.AbcYunRequest.SourceRemark != "" {
		abcyunReq["sourceRemark"] = req.AbcYunRequest.SourceRemark
	}

	// 将请求参数保存为JSON字符串
	requestJSON, err := json.Marshal(abcyunReq)
	if err != nil {
		logx.WithContext(ctx).Errorf("序列化ABC云请求参数失败: %v", err)
		requestJSON = []byte("{}")
	}

	// 记录发送的请求参数
	logx.WithContext(ctx).Infof("发送到ABC云的预约挂号请求: patientId=%s, doctorId=%s, departmentId=%s",
		req.AbcYunRequest.AbcYunPatientID, req.AbcYunRequest.AbcYunDoctorID, req.AbcYunRequest.AbcYunDepartmentID)

	// 调用ABC云创建挂号API
	path := "/api/v2/open-agency/registration"
	respBody, err := abcyunClient.Post(path, abcyunReq)
	if err != nil {
		logx.WithContext(ctx).Errorf("创建ABC云预约挂号失败: %v", err)
		return nil, err
	}

	// 解析ABC云响应
	var abcyunResp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			RegistrationSheetID string `json:"registrationSheetId"`
			PatientOrderID      string `json:"patientOrderId"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &abcyunResp); err != nil {
		logx.WithContext(ctx).Errorf("解析ABC云预约挂号响应失败: %v", err)
		return nil, fmt.Errorf("解析ABC云响应失败: %v", err)
	}

	if abcyunResp.Code != 0 {
		logx.WithContext(ctx).Errorf("ABC云预约挂号失败: %s", abcyunResp.Message)
		return nil, fmt.Errorf("ABC云预约挂号失败: %s", abcyunResp.Message)
	}

	logx.WithContext(ctx).Infof("ABC云预约挂号成功: registrationSheetId=%s, patientOrderId=%s",
		abcyunResp.Data.RegistrationSheetID, abcyunResp.Data.PatientOrderID)

	// 获取挂号详情
	detailPath := fmt.Sprintf("/api/v2/open-agency/registration/%s", abcyunResp.Data.RegistrationSheetID)
	detailRespBody, err := abcyunClient.Get(detailPath, nil)
	if err != nil {
		logx.WithContext(ctx).Errorf("获取ABC云挂号详情失败: %v", err)
		return nil, fmt.Errorf("获取ABC云挂号详情失败: %v", err)
	}

	// 解析挂号详情响应
	var detailResp struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    map[string]interface{} `json:"data"`
	}
	if err := json.Unmarshal(detailRespBody, &detailResp); err != nil {
		logx.WithContext(ctx).Errorf("解析ABC云挂号详情响应失败: %v", err)
		return nil, fmt.Errorf("解析ABC云挂号详情响应失败: %v", err)
	}

	if detailResp.Code != 0 {
		logx.WithContext(ctx).Errorf("获取ABC云挂号详情失败: %s", detailResp.Message)
		return nil, fmt.Errorf("获取ABC云挂号详情失败: %s", detailResp.Message)
	}

	// 构建统一的响应结构
	result := &AppointmentResponse{
		// ABC云特有字段
		RegistrationSheetID: abcyunResp.Data.RegistrationSheetID,
		PatientOrderID:      abcyunResp.Data.PatientOrderID,

		// 从详情中提取基础信息
		YsID:   toString(detailResp.Data["doctorId"]),
		YsMC:   toString(detailResp.Data["doctorName"]),
		JgksID: toInt(detailResp.Data["departmentId"]),
		JgksMC: toString(detailResp.Data["departmentName"]),

		// 预约信息
		HyRQ: toString(detailResp.Data["reserveDate"]),
		HySJ: toString(detailResp.Data["reserveTime"]),

		// 状态信息
		HyZT: toString(detailResp.Data["status"]),

		// 其他字段设置默认值（ABC云没有对应的杭州HIS字段）
		YyghID: 0, // ABC云没有对应字段，设为0
		HyjlID: 0, // ABC云没有对应字段，设为0
		GrxxID: 0, // ABC云没有对应字段，设为0
		YyLx:   strconv.Itoa(req.AbcYunRequest.YyLx),
	}

	// 保存预约信息到数据库
	if err := r.SaveAppointment(ctx, result, req, string(requestJSON), string(detailRespBody)); err != nil {
		logx.WithContext(ctx).Errorf("保存ABC云预约挂号信息到数据库失败: %v", err)
		// 注意：这里不返回错误，因为预约已经创建成功，数据库保存失败不应影响用户
	}

	return result, nil
}

// SaveAppointment 保存预约挂号信息到数据库
func (r *appointmentRepository) SaveAppointment(ctx context.Context, resp *AppointmentResponse, req *AppointmentRequest, requestData, responseData string) error {
	// 创建预约挂号订单
	orderRepo := repo_registration.NewAppointmentOrderRepository()

	// 确定HIS类型
	hisType := model_registration.HisTypeHangzhou // 默认杭州HIS
	if req.HisType == 2 {
		hisType = model_registration.HisTypeAbcyun // ABC云
	}

	// 根据HIS类型获取相应的字段值
	var (
		appointDate   string
		appointPeriod int
		grxxID        int
		hyjlID        int
		yylX          int
		jgksID        int
		wxDoctorID    int
		wxPatientID   int
		storeID       int
		departmentID  uint
	)

	if req.HisType == 2 { // ABC云
		appointDate = req.AbcYunRequest.AppointDate
		appointPeriod = req.AbcYunRequest.AppointPeriod
		grxxID = 0 // ABC云暂时设为0
		hyjlID = 0 // ABC云暂时设为0
		yylX = req.AbcYunRequest.YyLx
		jgksID = 0 // ABC云暂时设为0，后续可从科室字符串转换
		wxDoctorID = req.AbcYunRequest.WxDoctorID
		wxPatientID = req.AbcYunRequest.WxPatientID
		storeID = req.AbcYunRequest.StoreID

		// 根据ABC云科室ID查询t_departments表
		deptRepo := departmentModel.NewDepartmentRepository()
		dept, err := deptRepo.FindByJgksIDStr(req.AbcYunRequest.AbcYunDepartmentID)
		if err != nil {
			logx.WithContext(ctx).Infof("通过ABC云科室ID查询科室失败: %s, err=%v", req.AbcYunRequest.AbcYunDepartmentID, err)
			// 如果找不到科室，设为0
			departmentID = 0
		} else {
			departmentID = uint(dept.ID)
		}
	} else { // 杭州HIS
		appointDate = req.HangzhouRequest.AppointDate
		appointPeriod = req.HangzhouRequest.AppointPeriod
		grxxID = req.HangzhouRequest.GrxxID
		hyjlID = req.HangzhouRequest.HyjlID
		yylX = req.HangzhouRequest.YyLx
		jgksID = req.HangzhouRequest.JgksID
		wxDoctorID = req.HangzhouRequest.WxDoctorID
		wxPatientID = req.HangzhouRequest.WxPatientID
		storeID = req.HangzhouRequest.StoreID

		// 根据杭州HIS科室ID查询t_departments表
		deptRepo := departmentModel.NewDepartmentRepository()
		dept, err := deptRepo.FindByJgksIDAndWsjgID(jgksID, strconv.Itoa(req.HangzhouRequest.WsjgID))
		if err != nil {
			logx.WithContext(ctx).Errorf("查询杭州HIS科室失败: jgksID=%d, wsjgID=%d, err=%v", jgksID, req.HangzhouRequest.WsjgID, err)
			// 如果找不到科室，设为0
			departmentID = 0
		} else {
			departmentID = uint(dept.ID)
		}
	}

	// 记录关键字段日志
	logx.Infof("准备保存预约挂号记录，关键字段: yyghID=%d, grxxID=%d, hyjlID=%d, yylx=%v, hisType=%d, departmentID=%d",
		resp.YyghID, grxxID, hyjlID, yylX, req.HisType, departmentID)

	// 确保YyLx字段值格式正确
	yylxValue := toInt(resp.YyLx)
	if yylxValue <= 0 {
		// 如果API返回的预约类型无效，则使用请求中的预约类型
		yylxValue = yylX
		logx.Infof("API返回的预约类型无效或为空，使用请求中的预约类型: %d", yylxValue)
	}

	// 构建预约挂号订单对象
	order := &model_registration.AppointmentOrder{
		OrderNo:            fmt.Sprintf("YY%s%d", time.Now().Format("20060102150405"), wxPatientID),
		AppointDate:        appointDate,                                         // 从请求中获取
		AppointPeriod:      model_registration.AppointPeriodType(appointPeriod), // 转换为AppointPeriodType
		SyncStatus:         model_registration.SyncStatusSuccess,
		OrderStatus:        model_registration.OrderStatusWaitDiagnosis,
		HisType:            hisType,      // 根据实际HIS类型设置
		YyghID:             resp.YyghID,  // 确保保存预约挂号ID
		GrxxID:             grxxID,       // 确保保存个人信息ID
		HyjlID:             hyjlID,       // 确保保存号源记录ID
		YylX:               yylX,         // 确保保存预约类型
		JgksID:             jgksID,       // 保存机构科室ID
		DepartmentID:       departmentID, // 保存t_departments表的ID
		WxDoctorID:         wxDoctorID,
		WxPatientID:        wxPatientID,
		StoreID:            storeID,
		RequestData:        requestData,  // 保存API请求参数JSON
		ResponseData:       responseData, // 保存API响应数据JSON
		CzrID:              resp.CzrID,
		Czr:                resp.CzRMC,
		VerificationCode:   uuid.New().String(),                               // 生成核销码
		VerificationStatus: int(model_registration.VerificationStatusPending), // 设置为待核销状态

		// ABC云相关字段
		RegistrationSheetID: resp.RegistrationSheetID,
		PatientOrderID:      resp.PatientOrderID,
	}

	// 记录订单数据日志
	orderJSON, _ := json.Marshal(order)
	logx.Infof("即将保存的订单完整数据: %s", string(orderJSON))

	// 保存到数据库
	err := orderRepo.SaveAppointmentOrder(ctx, order)
	if err != nil {
		logx.WithContext(ctx).Errorf("保存预约挂号订单失败: %v", err)
		return err
	}

	// 为所有订单生成二维码（包括ABC云）
	qrCodeService := qrmodel.NewQRCodeService()
	qrCodeURL, err := qrCodeService.GenerateQRCode(ctx, order.ID)
	if err != nil {
		logx.WithContext(ctx).Errorf("生成二维码失败: %v", err)
	} else {
		// 更新订单的二维码URL
		order.QRCodeURL = qrCodeURL
		if err := orderRepo.UpdateAppointmentOrder(ctx, order); err != nil {
			logx.WithContext(ctx).Errorf("更新订单二维码URL失败: %v", err)
		} else {
			logx.WithContext(ctx).Infof("订单ID=%d 的二维码已生成: %s", order.ID, qrCodeURL)
		}
	}

	logx.WithContext(ctx).Infof("保存预约挂号订单成功: ID=%d, 订单编号=%s, YyghID=%d, GrxxID=%d, HyjlID=%d, YylX=%d, HisType=%d, DepartmentID=%d, RegistrationSheetID=%s, PatientOrderID=%s",
		order.ID, order.OrderNo, order.YyghID, order.GrxxID, order.HyjlID, order.YylX, int(order.HisType), order.DepartmentID, order.RegistrationSheetID, order.PatientOrderID)
	return nil
}

// CancelAppointment 取消预约挂号
func (r *appointmentRepository) CancelAppointment(ctx context.Context, req *CancelAppointmentRequest) (*AppointmentResponse, error) {
	// 根据HIS类型选择不同的处理逻辑
	if req.HisType == 2 { // ABC云
		return r.cancelAbcYunAppointment(ctx, req)
	} else { // 杭州HIS（默认）
		return r.cancelHangzhouAppointment(ctx, req)
	}
}

// cancelHangzhouAppointment 取消杭州HIS预约挂号
func (r *appointmentRepository) cancelHangzhouAppointment(ctx context.Context, req *CancelAppointmentRequest) (*AppointmentResponse, error) {
	// 查询现有订单
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	existingOrder, err := orderRepo.GetAppointmentOrderByYyghID(ctx, req.YyghID)
	if err != nil {
		logx.WithContext(ctx).Errorf("查询预约挂号订单失败: %v", err)
		return nil, fmt.Errorf("查询预约挂号订单失败: %v", err)
	}

	// 构建杭州HIS取消请求
	hangzhouCancelReq := &hangzhou.CancelAppointmentRequest{
		YyghID: req.YyghID,
		GrxxID: req.GrxxID,
		HyjlID: req.HyjlID,
		YyLx:   req.YyLx,
		ZfrID:  req.ZfrID,
		ZfrMC:  req.ZfrMC,
		ZfYy:   req.ZfYy,
	}

	// 将请求参数保存为JSON字符串
	requestJSON, err := json.Marshal(hangzhouCancelReq)
	if err != nil {
		logx.WithContext(ctx).Errorf("序列化取消请求参数失败: %v", err)
		requestJSON = []byte("{}")
	}

	// 调用杭州HIS取消预约API
	resp, err := hangzhou.CancelAppointment(ctx, hangzhouCancelReq)
	if err != nil {
		logx.WithContext(ctx).Errorf("调用杭州HIS取消预约API失败: %v", err)

		// 将错误信息也保存到数据库
		r.updateOrderStatusAfterCancel(ctx, existingOrder, req, string(requestJSON), fmt.Sprintf("{\"error\": \"%s\"}", err.Error()))

		return nil, fmt.Errorf("调用杭州HIS取消预约API失败: %v", err)
	}

	// 将响应保存为JSON字符串
	responseJSON, err := json.Marshal(resp)
	if err != nil {
		logx.WithContext(ctx).Errorf("序列化取消响应参数失败: %v", err)
		responseJSON = []byte("{}")
	}

	// 更新本地订单状态
	err = r.updateOrderStatusAfterCancel(ctx, existingOrder, req, string(requestJSON), string(responseJSON))
	if err != nil {
		logx.WithContext(ctx).Errorf("更新订单状态失败: %v", err)
		return nil, fmt.Errorf("更新订单状态失败: %v", err)
	}

	// 转换响应格式
	result := &AppointmentResponse{
		YyghID: resp.Code, // 使用响应中的code作为YyghID
		GrxxID: req.GrxxID,
		YyLx:   strconv.Itoa(req.YyLx),
		// 其他字段根据需要填充
	}

	return result, nil
}

// cancelAbcYunAppointment 取消ABC云预约挂号
func (r *appointmentRepository) cancelAbcYunAppointment(ctx context.Context, req *CancelAppointmentRequest) (*AppointmentResponse, error) {
	// 查询现有订单 - 通过RegistrationSheetID查询

	// 验证RegistrationSheetID是否存在
	if req.RegistrationSheetID == "" {
		return nil, fmt.Errorf("ABC云挂号单ID为空，无法取消")
	}

	// 通过RegistrationSheetID查询订单
	existingOrder, err := r.findOrderByRegistrationSheetID(ctx, req.RegistrationSheetID)
	if err != nil {
		logx.WithContext(ctx).Errorf("查询ABC云预约挂号订单失败: %v", err)
		return nil, fmt.Errorf("查询ABC云预约挂号订单失败: %v", err)
	}

	// 验证是否是ABC云订单
	if existingOrder.HisType != 2 {
		return nil, fmt.Errorf("订单不是ABC云类型，无法使用ABC云取消接口")
	}

	// 验证ABC云相关字段
	if existingOrder.RegistrationSheetID == "" {
		return nil, fmt.Errorf("ABC云挂号单ID为空，无法取消")
	}

	// 构建ABC云取消请求
	abcyunCancelReq := map[string]interface{}{
		"registrationSheetId": existingOrder.RegistrationSheetID,
		"patientOrderId":      existingOrder.PatientOrderID,
		"cancelReason":        req.ZfYy,
		"operatorId":          req.ZfrID,
		"operatorName":        req.ZfrMC,
	}

	// 将请求参数保存为JSON字符串
	requestJSON, err := json.Marshal(abcyunCancelReq)
	if err != nil {
		logx.WithContext(ctx).Errorf("序列化ABC云取消请求参数失败: %v", err)
		requestJSON = []byte("{}")
	}

	// 调用ABC云取消预约API
	abcyunClient := abcyun.GetGlobalClient()
	if abcyunClient == nil {
		return nil, fmt.Errorf("ABC云客户端未初始化")
	}

	// 使用ABC云API取消挂号
	path := fmt.Sprintf("/api/v2/open-agency/registration/%s/cancel", existingOrder.RegistrationSheetID)
	respBody, err := abcyunClient.Put(path, nil)
	if err != nil {
		logx.WithContext(ctx).Errorf("调用ABC云取消预约API失败: %v", err)

		// 将错误信息也保存到数据库
		r.updateOrderStatusAfterCancel(ctx, existingOrder, req, string(requestJSON), fmt.Sprintf("{\"error\": \"%s\"}", err.Error()))

		return nil, fmt.Errorf("调用ABC云取消预约API失败: %v", err)
	}

	// 解析ABC云响应
	var abcyunResp struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    map[string]interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &abcyunResp); err != nil {
		logx.WithContext(ctx).Errorf("解析ABC云取消响应失败: %v", err)
		return nil, fmt.Errorf("解析ABC云取消响应失败: %v", err)
	}

	if abcyunResp.Code != 0 {
		logx.WithContext(ctx).Errorf("ABC云取消预约失败: %s", abcyunResp.Message)
		return nil, fmt.Errorf("ABC云取消预约失败: %s", abcyunResp.Message)
	}

	// 更新本地订单状态
	err = r.updateOrderStatusAfterCancel(ctx, existingOrder, req, string(requestJSON), string(respBody))
	if err != nil {
		logx.WithContext(ctx).Errorf("更新订单状态失败: %v", err)
		return nil, fmt.Errorf("更新订单状态失败: %v", err)
	}

	// 转换响应格式
	result := &AppointmentResponse{
		RegistrationSheetID: existingOrder.RegistrationSheetID,
		PatientOrderID:      existingOrder.PatientOrderID,
		// 其他字段根据需要填充
	}

	return result, nil
}

// findOrderByRegistrationSheetID 通过RegistrationSheetID查找订单
func (r *appointmentRepository) findOrderByRegistrationSheetID(ctx context.Context, registrationSheetID string) (*model_registration.AppointmentOrder, error) {
	// 通过数据库查询RegistrationSheetID对应的订单
	orderRepo := repo_registration.NewAppointmentOrderRepository()

	// 这里需要实现一个通过RegistrationSheetID查询的方法
	// 由于当前repository可能没有这个方法，我们暂时通过直接查询实现
	orders, err := orderRepo.GetAppointmentOrdersByPatientID(ctx, 0, "") // 获取所有订单
	if err != nil {
		return nil, fmt.Errorf("查询订单列表失败: %v", err)
	}

	// 遍历查找匹配的RegistrationSheetID
	for _, order := range orders {
		if order.RegistrationSheetID == registrationSheetID {
			return order, nil
		}
	}

	return nil, fmt.Errorf("未找到RegistrationSheetID对应的订单: %s", registrationSheetID)
}

// updateOrderStatusAfterCancel 取消后更新订单状态
func (r *appointmentRepository) updateOrderStatusAfterCancel(ctx context.Context, existingOrder *model_registration.AppointmentOrder, req *CancelAppointmentRequest, requestData, responseData string) error {
	// 更新订单状态为已取消
	existingOrder.OrderStatus = model_registration.OrderStatusCanceled
	existingOrder.SyncStatus = model_registration.SyncStatusSuccess // 修复：使用正确的同步状态常量

	// 设置作废信息
	if req.HisType == 2 && req.RegistrationSheetID != "" {
		existingOrder.ZfrID = req.ZfrID
		existingOrder.ZfrMC = req.ZfrMC
		existingOrder.ZfYY = req.ZfYy
	} else if req.YyghID != 0 {
		existingOrder.ZfrID = req.ZfrID
		existingOrder.ZfrMC = req.ZfrMC
		existingOrder.ZfYY = req.ZfYy
	}

	existingOrder.ZfSJ = time.Now().Format("2006-01-02 15:04:05")
	existingOrder.RequestData = requestData
	existingOrder.ResponseData = responseData

	// 保存到数据库
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	err := orderRepo.UpdateAppointmentOrder(ctx, existingOrder)
	if err != nil {
		return fmt.Errorf("更新预约挂号订单失败: %v", err)
	}

	return nil
}

// extractFromMap 从map中提取字段值
func extractFromMap(respMap map[string]interface{}) AppointmentResponse {
	result := AppointmentResponse{}

	// 基本信息
	if v, ok := respMap["yyghid"]; ok {
		result.YyghID = toInt(v)
	}
	if v, ok := respMap["hyjlid"]; ok {
		result.HyjlID = toInt(v)
	}
	if v, ok := respMap["grxxid"]; ok {
		result.GrxxID = toInt(v)
	}
	if v, ok := respMap["yylx"]; ok {
		result.YyLx = toString(v)
	}

	// 患者信息
	if v, ok := respMap["bah"]; ok {
		result.Bah = toString(v)
	}
	if v, ok := respMap["xm"]; ok {
		result.XM = toString(v)
	}
	if v, ok := respMap["xbdm"]; ok {
		result.XbDM = toString(v)
	}
	if v, ok := respMap["xbmc"]; ok {
		result.XbMC = toString(v)
	}
	if v, ok := respMap["cssj"]; ok {
		result.CsSJ = toString(v)
	}
	if v, ok := respMap["zjhm"]; ok {
		result.ZJHM = toString(v)
	}
	if v, ok := respMap["zjlx"]; ok {
		result.ZJLx = toString(v)
	}

	// 号源信息
	if v, ok := respMap["qhmm"]; ok {
		result.QhMM = toString(v)
	}
	if v, ok := respMap["hyzt"]; ok {
		result.HyZT = toString(v)
	}
	if v, ok := respMap["hyrq"]; ok {
		result.HyRQ = toString(v)
	}
	if v, ok := respMap["hysj"]; ok {
		result.HySJ = toString(v)
	}
	if v, ok := respMap["hyxh"]; ok {
		result.HyXH = toInt(v)
	}
	if v, ok := respMap["ghf"]; ok {
		result.GhF = toFloat64(v)
	}
	if v, ok := respMap["ghfid"]; ok {
		result.GhFID = toInt(v)
	}
	if v, ok := respMap["zlf"]; ok {
		result.ZlF = toFloat64(v)
	}
	if v, ok := respMap["zlfid"]; ok {
		result.ZlFID = toInt(v)
	}
	if v, ok := respMap["pblx"]; ok {
		result.PbLx = toString(v)
	}
	if v, ok := respMap["jkglsid"]; ok {
		result.JkGlSID = toInt(v)
	}
	if v, ok := respMap["jkglsmc"]; ok {
		result.JkGlSMC = toString(v)
	}

	// 机构和医生信息
	if v, ok := respMap["wsjgid"]; ok {
		result.WsjgID = toInt(v)
	}
	if v, ok := respMap["wsjgmc"]; ok {
		result.WsjgMC = toString(v)
	}
	if v, ok := respMap["jgksid"]; ok {
		result.JgksID = toInt(v)
	}
	if v, ok := respMap["jgksmc"]; ok {
		result.JgksMC = toString(v)
	}
	if v, ok := respMap["ysid"]; ok {
		result.YsID = toString(v)
	}
	if v, ok := respMap["ysmc"]; ok {
		result.YsMC = toString(v)
	}

	// 其他字段
	if v, ok := respMap["czrid"]; ok {
		result.CzrID = toInt(v)
	}
	if v, ok := respMap["czrmc"]; ok {
		result.CzRMC = toString(v)
	}
	if v, ok := respMap["czsj"]; ok {
		result.CzSJ = toString(v)
	}
	if v, ok := respMap["zfbz"]; ok {
		result.ZfBz = toInt(v)
	}
	if v, ok := respMap["zfrid"]; ok {
		result.ZfRID = toInt(v)
	}
	if v, ok := respMap["zfrmc"]; ok {
		result.ZfRMC = toString(v)
	}
	if v, ok := respMap["zfsj"]; ok {
		result.ZfSJ = toString(v)
	}
	if v, ok := respMap["zfyy"]; ok {
		result.ZfYY = toString(v)
	}
	if v, ok := respMap["bz"]; ok {
		result.BZ = toString(v)
	}

	return result
}

// toString 将任意类型转换为字符串
func toString(v interface{}) string {
	if v == nil {
		return ""
	}

	switch vt := v.(type) {
	case string:
		return vt
	case float64:
		return strconv.FormatFloat(vt, 'f', -1, 64)
	case int:
		return strconv.Itoa(vt)
	case int64:
		return strconv.FormatInt(vt, 10)
	case json.Number:
		return vt.String()
	default:
		str, _ := json.Marshal(v)
		return string(str)
	}
}

// toInt 将任意类型转换为整数
func toInt(v interface{}) int {
	if v == nil {
		return 0
	}

	switch vt := v.(type) {
	case int:
		return vt
	case int64:
		return int(vt)
	case float64:
		return int(vt)
	case string:
		i, _ := strconv.Atoi(vt)
		return i
	case json.Number:
		i, _ := vt.Int64()
		return int(i)
	default:
		return 0
	}
}

// toFloat64 将任意类型转换为float64
func toFloat64(v interface{}) float64 {
	if v == nil {
		return 0
	}

	switch vt := v.(type) {
	case float64:
		return vt
	case int:
		return float64(vt)
	case int64:
		return float64(vt)
	case string:
		f, _ := strconv.ParseFloat(vt, 64)
		return f
	case json.Number:
		f, _ := vt.Float64()
		return f
	default:
		return 0
	}
}

// GetOrderStatusText 获取订单状态文本
func GetOrderStatusText(status interface{}) string {
	var statusValue int
	switch v := status.(type) {
	case int:
		statusValue = v
	case uint:
		statusValue = int(v)
	case int64:
		statusValue = int(v)
	case uint64:
		statusValue = int(v)
	default:
		return ""
	}

	switch statusValue {
	case 1:
		return "待就诊"
	case 2:
		return "待支付"
	case 3:
		return "已完成"
	case 4:
		return "已取消"
	case 5:
		return "已退款"
	default:
		return ""
	}
}

// GetSyncStatusText 获取同步状态文本
func GetSyncStatusText(status int) string {
	switch status {
	case 0:
		return "待同步"
	case 1:
		return "已同步"
	case 2:
		return "同步失败"
	default:
		return ""
	}
}

// GetVerificationStatusText 获取核销状态文本
func GetVerificationStatusText(status int) string {
	switch status {
	case 0:
		return "待核销"
	case 1:
		return "已核销"
	case 2:
		return "已取消"
	default:
		return ""
	}
}
