package routes

import (
	"net/http"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/jushuitan/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterJushuitanRoutes 注册聚水潭相关路由
func RegisterJushuitanRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, ctx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(ctx)(next).ServeHTTP)
	}

	jstHandler := handler.NewJushuitanHandler(ctx)
	orderHandler := handler.NewOrderHandler(ctx)
	inventoryHandler := handler.NewInventoryHandler(ctx)
	afterSaleHandler := handler.NewAfterSaleHandler(ctx)
	logisticHandler := handler.NewLogisticHandler(ctx)

	// 注册聚水潭路由
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/jst/shops",
				Handler: wxAuthWrapper(jstHandler.QueryShops),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/jst/logistics/companies",
				Handler: wxAuthWrapper(jstHandler.QueryLogisticsCompanies),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/jst/warehouses",
				Handler: wxAuthWrapper(jstHandler.QueryWarehouses),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/jst/users",
				Handler: wxAuthWrapper(jstHandler.QueryUsers),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/jst/suppliers",
				Handler: wxAuthWrapper(jstHandler.QuerySuppliers),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/jst/distributors",
				Handler: wxAuthWrapper(jstHandler.QueryDistributors),
			},
		},
	)
	// 商品相关接口
	server.AddRoutes([]rest.Route{
		// 普通商品资料
		{
			// 按SKU
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/item/query",
			Handler: wxAuthWrapper(jstHandler.QueryItemSku),
		},
		{
			// 按款
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/mall/item/query",
			Handler: wxAuthWrapper(jstHandler.QueryMallItem),
		},

		// 店铺商品资料
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/item/shop/query",
			Handler: wxAuthWrapper(jstHandler.QuerySkuMap),
		},

		// 组合装商品资料
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/item/combine/query",
			Handler: wxAuthWrapper(jstHandler.QueryCombineSku),
		},

		// 商品类目管理
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/item/category/query",
			Handler: wxAuthWrapper(jstHandler.QueryCategory),
		},

		// BOM管理
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/item/bom/query",
			Handler: wxAuthWrapper(jstHandler.GetSkuBomPageList),
		},

		// 商品历史成本价
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/item/costprice/query",
			Handler: wxAuthWrapper(jstHandler.GetHistoryCostPriceV2),
		},

		// 商品多供应商
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/item/supplier/query",
			Handler: wxAuthWrapper(jstHandler.GetSupplierSkuList),
		},
	})

	// 订单相关接口
	server.AddRoutes([]rest.Route{
		// 订单查询 - 仅支持POST方法
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/query",
			Handler: wxAuthWrapper(orderHandler.QueryOrder),
		},
		// 订单上传
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/upload",
			Handler: wxAuthWrapper(orderHandler.UploadOrder),
		},
		// 取消订单
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/cancel",
			Handler: wxAuthWrapper(orderHandler.CancelOrder),
		},
		// 订单拆分
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/split",
			Handler: wxAuthWrapper(orderHandler.SplitOrder),
		},
		// 订单指定发货仓
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/wms",
			Handler: wxAuthWrapper(orderHandler.ModifyOrderWMS),
		},
		// 订单转异常
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/question",
			Handler: wxAuthWrapper(orderHandler.QuestionOrder),
		},
		// 修改卖家备注（更新订单备注）
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/remark",
			Handler: wxAuthWrapper(orderHandler.UploadOrderRemark),
		},
		// 修改线下备注
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/node",
			Handler: wxAuthWrapper(orderHandler.SetOrderNode),
		},
		// 修改订单标签
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/label",
			Handler: wxAuthWrapper(orderHandler.UploadOrderLabel),
		},
		// 订单发货
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/order/sent",
			Handler: wxAuthWrapper(orderHandler.UploadOrderSent),
		},
		// 订单操作日志查询
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/jst/order/action",
			Handler: wxAuthWrapper(orderHandler.QueryOrderAction),
		},
	})

	// 库存相关接口
	server.AddRoutes([]rest.Route{
		// 商品库存查询
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/inventory/query",
			Handler: wxAuthWrapper(inventoryHandler.QueryInventory),
		},
		// 箱及仓位库存查询
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/pack/query",
			Handler: wxAuthWrapper(inventoryHandler.QueryPack),
		},
		// 新建盘点单-修改库存
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/inventoryv2/upload",
			Handler: wxAuthWrapper(inventoryHandler.UploadInventory),
		},
		// 盘点单查询
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/count/query",
			Handler: wxAuthWrapper(inventoryHandler.QueryInventoryCount),
		},
		// 导入/更新虚拟库存
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/iteminventory/batchupdatewmsvirtualqtys",
			Handler: wxAuthWrapper(inventoryHandler.UpdateVirtualInventory),
		},
		// 箱及仓位库存批量查询
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/pack/pagequerypackanditems",
			Handler: wxAuthWrapper(inventoryHandler.QueryPackBatch),
		},
	})

	// 售后相关接口
	server.AddRoutes([]rest.Route{
		// 售后上传
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/aftersale/upload",
			Handler: wxAuthWrapper(afterSaleHandler.UploadAfterSale),
		},
		// 无信息件售后上传
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/aftersale/noinfo/upload",
			Handler: wxAuthWrapper(afterSaleHandler.UploadAfterSaleNoInfo),
		},
		// 售后退货退款查询
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/refund/single/query",
			Handler: wxAuthWrapper(afterSaleHandler.QueryRefundSingle),
		},
		// 售后确认收到货物
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/aftersale/confirmgoods",
			Handler: wxAuthWrapper(afterSaleHandler.ConfirmAfterSaleGoods),
		},
		// 实际收货查询
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/aftersale/received/query",
			Handler: wxAuthWrapper(afterSaleHandler.QueryAfterSaleReceived),
		},
		// 售后单确认
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/aftersale/confirm",
			Handler: wxAuthWrapper(afterSaleHandler.ConfirmAfterSale),
		},
		// 售后单反确认
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/aftersale/unconfirm",
			Handler: wxAuthWrapper(afterSaleHandler.UnconfirmAfterSale),
		},
		// 唯一码批量确认收货
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/aftersale/confirmgoodsbyskusns",
			Handler: wxAuthWrapper(afterSaleHandler.ConfirmAfterSaleBySns),
		},
		// 退款单查询
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/aftersale/refund/query",
			Handler: wxAuthWrapper(afterSaleHandler.QueryRefund),
		},
		// 物流相关接口
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/jst/logistic/query",
			Handler: wxAuthWrapper(logisticHandler.QueryLogistic),
		},
	})
}
