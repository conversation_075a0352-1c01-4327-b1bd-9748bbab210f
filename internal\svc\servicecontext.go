package svc

import (
	"yekaitai/internal/config"
	"yekaitai/internal/modules/admin/model"
	appointmentModel "yekaitai/internal/modules/appointment/model"
	"yekaitai/internal/modules/member/repository"
	memberService "yekaitai/internal/modules/member/service"

	// consultationModel "yekaitai/internal/modules/consultation/model"
	contentModel "yekaitai/internal/modules/content/model"
	pharmacyModel "yekaitai/internal/modules/pharmacy/model"
	serviceModel "yekaitai/internal/modules/service_setup/model"
	storeModel "yekaitai/internal/modules/store/model"
	tagRepo "yekaitai/internal/modules/tag/repository"
	tagService "yekaitai/internal/modules/tag/service"
	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/adapters/chongqing"
	"yekaitai/pkg/adapters/his"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/upload"

	"github.com/zeromicro/go-zero/core/logx"
)

// ServiceContext 服务上下文
type ServiceContext struct {
	Config       config.Config
	AbcYunClient *abcyun.AbcYunClient
	// MedlinkerClient    *medlinker.MedlinkerClient
	// MedlinkerAIService *medlinker.AIService

	ChongqingClient *chongqing.Client
	HisFactory      *his.ClientFactory

	// 科室仓库
	DepartmentRepo appointmentModel.DepartmentRepository

	// 药品仓库
	MedicineRepo pharmacyModel.MedicineRepository

	// 药品分类仓库
	MedicineCategoryRepo pharmacyModel.MedicineCategoryRepository

	// 库存仓库
	StockRepo pharmacyModel.StockRepository

	// AI问诊记录仓库
	// AIConsultRecordRepo consultationModel.AIConsultRecordRepository

	// 管理员仓库
	AdminUserRepo         model.AdminUserRepository
	AdminOperationLogRepo model.AdminOperationLogRepository
	AdminRepo             model.AdminUserRepository

	// 预约系统仓库
	AppointmentRepo appointmentModel.AppointmentRepository

	// 地区仓库
	RegionRepo model.RegionRepository

	// 门店仓库
	StoreRepo storeModel.StoreRepository

	// 服务套餐管理仓库
	ServicePackageRepo serviceModel.ServicePackageRepository

	// 服务套餐标签仓库
	ServiceTagRepo serviceModel.ServiceTagRepository

	// 内容管理仓库
	ContentRepo contentModel.ContentRepository

	// 会员服务
	MemberService memberService.MemberService

	// 标签服务
	TagService tagService.TagService

	// 短信服务
	SmsService interface {
		SendSmsCode(mobile string, smsType int) (string, error)
		VerifySmsCode(mobile string, code string, smsType int) (bool, error)
		SetSmsClient(client interface {
			SendSms(phoneNumber, code string) error
			ValidateConfig() error
		})
	}
}

// NewServiceContext 创建服务上下文
func NewServiceContext(c config.Config) *ServiceContext {
	var abcYunClient *abcyun.AbcYunClient
	// var medlinkerClient *medlinker.MedlinkerClient
	// var medlinkerAIService *medlinker.AIService

	var chongqingClient *chongqing.Client

	// 初始化ABC云客户端
	if c.AbcYun.BaseURL != "" {
		abcyun.Init(c.AbcYun)
		abcYunClient = abcyun.Client
	}

	// 创建医联客户端
	// if c.Medlinker.BaseURL != "" {
	// 	medlinker.Init(c.Medlinker)
	// 	medlinkerClient = medlinker.Client
	// }

	// 创建重庆HIS客户端
	if c.Chongqing.BaseURL != "" {
		chongqingClient = chongqing.NewClient(&c.Chongqing)
	}

	// 创建HIS客户端工厂
	hisFactory := his.NewClientFactory(&c, abcYunClient)

	// 创建仓库实例
	departmentRepo := appointmentModel.NewDepartmentRepository()
	medicineRepo := pharmacyModel.NewMedicineRepository()
	medicineCategoryRepo := pharmacyModel.NewMedicineCategoryRepository()
	stockRepo := pharmacyModel.NewStockRepository()
	// aiConsultRecordRepo := consultationModel.NewAIConsultRecordRepository()

	// 创建医联AI服务
	// if medlinkerClient != nil {
	// 	medlinkerAIService = medlinker.NewAIService(medlinkerClient, aiConsultRecordRepo)
	// }

	// 初始化预约系统仓库
	appointmentRepo := appointmentModel.NewAppointmentRepository()

	// 获取数据库连接
	db := mysql.GetDB()

	// 初始化管理员相关仓库
	adminUserRepo := model.NewAdminUserRepository()
	adminOperationLogRepo := model.NewAdminOperationLogRepository(db)

	// 初始化地区仓库
	regionRepo := model.NewRegionRepository(db)

	// 初始化门店仓库
	storeRepo := storeModel.NewStoreRepository(db)

	// 初始化服务套餐管理仓库
	servicePackageRepo := serviceModel.NewServicePackageRepository()

	// 初始化服务套餐标签仓库
	serviceTagRepo := serviceModel.NewServiceTagRepository()

	// 初始化内容管理仓库
	contentRepo := contentModel.NewContentRepository()

	// 初始化上传模块
	upload.InitWithConfig(c)
	logx.Info("上传模块初始化成功")

	// 初始化标签仓库和服务
	tagRepository := tagRepo.NewTagRepository(nil)
	tagServ := tagService.NewTagService(tagRepository)

	// 初始化会员仓库和服务
	memberRepository := repository.NewMemberRepository(nil)
	memberServ := memberService.NewMemberService(memberRepository, tagRepository)

	// 初始化短信服务
	smsServ := NewSmsServiceWithConfig(c.TencentSms)

	logx.Info("服务上下文初始化成功")

	return &ServiceContext{
		Config:       c,
		AbcYunClient: abcYunClient,
		// MedlinkerClient:    medlinkerClient,
		// MedlinkerAIService: medlinkerAIService,

		ChongqingClient: chongqingClient,
		HisFactory:      hisFactory,

		DepartmentRepo:       departmentRepo,
		MedicineRepo:         medicineRepo,
		MedicineCategoryRepo: medicineCategoryRepo,
		StockRepo:            stockRepo,
		// AIConsultRecordRepo:  aiConsultRecordRepo,

		AdminUserRepo:         adminUserRepo,
		AdminOperationLogRepo: adminOperationLogRepo,
		AdminRepo:             adminUserRepo,

		AppointmentRepo:    appointmentRepo,
		RegionRepo:         regionRepo,
		StoreRepo:          storeRepo,
		ServicePackageRepo: servicePackageRepo,
		ServiceTagRepo:     serviceTagRepo,
		ContentRepo:        contentRepo,

		MemberService: memberServ,
		TagService:    tagServ,
		SmsService:    smsServ,
	}
}

// JWTConfig JWT配置
type JWTConfig struct {
	// 全局设置
	Issuer   string   `json:",optional"`
	Audience []string `json:",optional"`

	// 管理员JWT配置
	AdminAccessSecret  string `json:",optional"`
	AdminAccessExpire  int64  `json:",default=28800"` // 默认8小时
	AdminRefreshSecret string `json:",optional"`
	AdminRefreshExpire int64  `json:",default=1296000"` // 默认15天
	AdminBlacklistKey  string `json:",default=admin_blacklist:"`
}

// NewSmsServiceWithConfig 创建带配置的短信服务
func NewSmsServiceWithConfig(smsConfig config.TencentSmsConfig) interface {
	SendSmsCode(mobile string, smsType int) (string, error)
	VerifySmsCode(mobile string, code string, smsType int) (bool, error)
	SetSmsClient(client interface {
		SendSms(phoneNumber, code string) error
		ValidateConfig() error
	})
} {
	// 由于Go的包导入限制，我们需要通过反射或其他方式来创建服务
	// 这里暂时返回nil，实际使用时需要在具体的handler中创建
	return createSmsService(smsConfig)
}

// createSmsService 创建短信服务的内部函数
func createSmsService(smsConfig config.TencentSmsConfig) interface {
	SendSmsCode(mobile string, smsType int) (string, error)
	VerifySmsCode(mobile string, code string, smsType int) (bool, error)
	SetSmsClient(client interface {
		SendSms(phoneNumber, code string) error
		ValidateConfig() error
	})
} {
	// 这里需要实际的实现，暂时返回nil
	return nil
}
