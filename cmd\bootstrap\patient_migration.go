package bootstrap

import (
	"log"

	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/infra/mysql"
)

// MigratePatientTables 执行患者管理模块表结构迁移
func MigratePatientTables() error {
	log.Println("开始执行患者管理模块表结构迁移...")

	// 执行表结构迁移
	db := mysql.Master()

	// 迁移患者健康档案表
	db.Set("gorm:table_options", "COMMENT='患者健康档案表'").AutoMigrate(&patient.HealthRecord{})
	db.Set("gorm:table_options", "COMMENT='患者紧急联系人信息表'").AutoMigrate(&patient.PatientEmergencyContact{})

	// 重要：迁移诊断记录表（确保创建）
	db.Set("gorm:table_options", "COMMENT='患者诊断记录表'").AutoMigrate(&patient.DiagnosisRecord{})

	// 迁移同步进度表
	db.Set("gorm:table_options", "COMMENT='同步进度表'").AutoMigrate(&patient.SyncProgress{})

	// 迁移ABC云相关表
	db.Set("gorm:table_options", "COMMENT='ABC云患者报告表'").AutoMigrate(&patient.AbcYunPatientReport{})
	db.Set("gorm:table_options", "COMMENT='ABC云病历表'").AutoMigrate(&patient.AbcYunMedicalRecord{})
	db.Set("gorm:table_options", "COMMENT='ABC云中药处方表'").AutoMigrate(&patient.AbcYunChinesePrescription{})
	db.Set("gorm:table_options", "COMMENT='ABC云中药处方单项表'").AutoMigrate(&patient.AbcYunChinesePrescriptionItem{})
	db.Set("gorm:table_options", "COMMENT='ABC云西药处方表'").AutoMigrate(&patient.AbcYunWesternPrescription{})
	db.Set("gorm:table_options", "COMMENT='ABC云西药处方单项表'").AutoMigrate(&patient.AbcYunWesternPrescriptionItem{})
	db.Set("gorm:table_options", "COMMENT='ABC云治疗单处方表'").AutoMigrate(&patient.AbcYunTreatmentPrescription{})
	db.Set("gorm:table_options", "COMMENT='ABC云治疗单子项表'").AutoMigrate(&patient.AbcYunTreatmentPrescriptionItem{})

	log.Println("患者管理模块表结构迁移完成")
	return nil
}
