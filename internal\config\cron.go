package config

// CronConfig 定时任务配置
type CronConfig struct {
	// 是否启用定时任务
	Enable bool `json:"enable"`
	// 门店地理编码同步配置
	StoreGeoSync struct {
		Enable    bool   `json:"enable"`    // 是否启用
		Cron      string `json:"cron"`      // cron表达式，默认每天凌晨3点执行
		BatchSize int    `json:"batchSize"` // 每批处理数量
	} `json:"storeGeoSync"`
}

// TencentMapConfig 腾讯地图API配置
type TencentMapConfig struct {
	Key string `json:"key"` // 对应配置文件中的key字段
}
