package middleware

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"strconv"
	"time"
	"yekaitai/internal/modules/admin/model"
	"yekaitai/internal/svc"

	"yekaitai/pkg/infra/mysql"
	infraRedis "yekaitai/pkg/infra/redis"

	"github.com/golang-jwt/jwt/v4"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// 常量定义
const (
	AdminIssuer   = "admin-system"
	AdminAudience = "api-admin"
)

// AdminTokenInfo 管理后台令牌信息
type AdminTokenInfo struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	ExpiresAt    int64  `json:"expiresAt"`
}

// AdminClaims 管理后台JWT声明
type AdminClaims struct {
	UserID uint     `json:"userId"`
	Roles  []string `json:"roles,omitempty"`
	Perms  []string `json:"perms,omitempty"`
	Type   string   `json:"type"`
	jwt.RegisteredClaims
}

// GenerateAdminToken 生成管理员Token
func GenerateAdminToken(svcCtx *svc.ServiceContext, userId uint, roles []string, perms []string) (*AdminTokenInfo, error) {
	// 获取管理员密钥
	accessSecret := svcCtx.Config.JWT.AdminAccessSecret
	if accessSecret == "" {
		accessSecret = "admin_default_secret" // 默认密钥
	}

	// 设置过期时间
	accessExpire := svcCtx.Config.JWT.AdminAccessExpire
	if accessExpire == 0 {
		accessExpire = 8 * 60 * 60 // 默认8小时
	}
	accessExpireTime := time.Now().Add(time.Duration(accessExpire) * time.Second)

	// 构建Claims
	claims := AdminClaims{
		UserID: userId,
		Roles:  roles,
		Perms:  perms,
		Type:   TokenTypeAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(accessExpireTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    AdminIssuer,
			Subject:   strconv.FormatUint(uint64(userId), 10),
			Audience:  []string{AdminAudience},
			ID:        generateJTI(), // 唯一标识符，防止重放攻击
		},
	}

	// 生成Token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(accessSecret))
	if err != nil {
		return nil, err
	}

	// 刷新Token
	refreshSecret := svcCtx.Config.JWT.AdminRefreshSecret
	if refreshSecret == "" {
		refreshSecret = "admin_refresh_default_secret"
	}

	refreshExpire := svcCtx.Config.JWT.AdminRefreshExpire
	if refreshExpire == 0 {
		refreshExpire = 15 * 24 * 60 * 60 // 默认15天
	}
	refreshExpireTime := time.Now().Add(time.Duration(refreshExpire) * time.Second)

	refreshClaims := jwt.RegisteredClaims{
		ExpiresAt: jwt.NewNumericDate(refreshExpireTime),
		IssuedAt:  jwt.NewNumericDate(time.Now()),
		NotBefore: jwt.NewNumericDate(time.Now()),
		Issuer:    AdminIssuer,
		Subject:   strconv.FormatUint(uint64(userId), 10),
		ID:        generateJTI(),
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString([]byte(refreshSecret))
	if err != nil {
		return nil, err
	}

	// 存储刷新Token
	redisCli := infraRedis.GetClient()
	userIdStr := strconv.FormatUint(uint64(userId), 10)
	err = redisCli.Set(context.Background(),
		"admin_refresh_token:"+userIdStr,
		refreshTokenString,
		time.Duration(refreshExpire)*time.Second).Err()
	if err != nil {
		logx.Errorf("存储管理员刷新令牌失败: %v", err)
	}

	return &AdminTokenInfo{
		AccessToken:  tokenString,
		RefreshToken: refreshTokenString,
		ExpiresAt:    accessExpireTime.Unix(),
	}, nil
}

// 验证管理员Token
func ParseAdminToken(svcCtx *svc.ServiceContext, tokenString string) (*AdminClaims, error) {
	accessSecret := svcCtx.Config.JWT.AdminAccessSecret
	if accessSecret == "" {
		accessSecret = "admin_default_secret" // 默认密钥
	}

	// 解析Token
	token, err := jwt.ParseWithClaims(tokenString, &AdminClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(accessSecret), nil
	})

	if err != nil {
		logx.Errorf("解析Token失败: %v", err)
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, errors.New("令牌已过期")
		}
		return nil, errors.New("无效的令牌")
	}

	if !token.Valid {
		return nil, errors.New("无效的令牌")
	}

	// 类型转换
	if claims, ok := token.Claims.(*AdminClaims); ok {
		// 验证发行者和受众
		if claims.Issuer != AdminIssuer {
			return nil, errors.New("无效的令牌签发者")
		}
		if !contains(claims.Audience, AdminAudience) {
			return nil, errors.New("无效的令牌受众")
		}
		if claims.Type != TokenTypeAdmin {
			return nil, errors.New("令牌类型错误")
		}

		return claims, nil
	}

	return nil, errors.New("无效的令牌")
}

// RefreshAdminToken 刷新管理员Token
func RefreshAdminToken(svcCtx *svc.ServiceContext, refreshToken string) (*AdminTokenInfo, error) {
	// 验证刷新Token
	refreshSecret := svcCtx.Config.JWT.AdminRefreshSecret
	if refreshSecret == "" {
		refreshSecret = "admin_refresh_default_secret"
	}

	// 使用空结构先解析获取payload
	var claims jwt.RegisteredClaims
	token, err := jwt.ParseWithClaims(refreshToken, &claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(refreshSecret), nil
	})

	if err != nil {
		// 检查是否是过期错误
		if errors.Is(err, jwt.ErrTokenExpired) {
			logx.Error("管理员刷新令牌已过期")
			return nil, errors.New("刷新令牌已过期")
		}
		logx.Errorf("解析管理员刷新令牌失败: %v", err)
		return nil, errors.New("无效的刷新令牌")
	}

	if !token.Valid {
		logx.Error("管理员刷新令牌无效")
		return nil, errors.New("无效的刷新令牌")
	}

	// 检查签发者
	if claims.Issuer != AdminIssuer {
		logx.Error("刷新令牌签发者错误")
		return nil, errors.New("无效的令牌签发者")
	}

	// 显式检查令牌是否过期
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		logx.Error("管理员刷新令牌已过期")
		return nil, errors.New("刷新令牌已过期")
	}

	// 从Redis检查刷新Token
	redisCli := infraRedis.GetClient()
	storedToken, err := redisCli.Get(context.Background(),
		"admin_refresh_token:"+claims.Subject).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			logx.Error("刷新令牌不存在或已被撤销")
			return nil, errors.New("刷新令牌不存在或已被撤销")
		}
		logx.Errorf("Redis检查刷新令牌失败: %v", err)
		return nil, errors.New("检查刷新令牌时发生错误")
	}

	if storedToken != refreshToken {
		logx.Error("刷新令牌不匹配")
		return nil, errors.New("刷新令牌不匹配或已被撤销")
	}

	// 获取用户ID
	userId, err := strconv.ParseUint(claims.Subject, 10, 64)
	if err != nil {
		logx.Errorf("解析用户ID失败: %v", err)
		return nil, errors.New("无效的用户ID")
	}

	// 查询管理员信息
	db := mysql.Master()
	var admin model.AdminUser
	if err := db.Where("admin_id = ?", userId).First(&admin).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logx.Errorf("管理员不存在: %d", userId)
			return nil, errors.New("用户不存在")
		}
		logx.Errorf("查询管理员信息失败: %v", err)
		return nil, errors.New("查询用户信息失败")
	}

	// 检查管理员状态
	if admin.Status != 1 {
		logx.Errorf("管理员账号已被禁用: admin_id=%d", userId)
		return nil, errors.New("账号已被禁用")
	}

	// 获取管理员角色
	var roles []string
	var roleRecords []model.AdminRole
	if err := db.Joins("JOIN admin_user_role ON admin_role.role_id = admin_user_role.role_id").
		Where("admin_user_role.admin_id = ?", userId).
		Find(&roleRecords).Error; err != nil {
		logx.Errorf("查询管理员角色失败: %v", err)
		// 继续处理，使用空角色列表
	} else {
		for _, role := range roleRecords {
			roles = append(roles, role.RoleName)
		}
	}

	// 获取权限信息
	var perms []string
	// 简单处理，给管理员默认权限
	if len(roles) == 0 {
		roles = append(roles, "admin")
		perms = []string{"system:view"}
	} else {
		// 更全面的权限映射可以通过RBAC仓库查询
		perms = []string{"system:view", "user:manage"}
	}

	// 生成新Token
	return GenerateAdminToken(svcCtx, uint(userId), roles, perms)
}

// RevokeAdminToken 撤销管理员Token
func RevokeAdminToken(svcCtx *svc.ServiceContext, userID string) error {
	// 从Redis中删除刷新Token
	redisCli := infraRedis.GetClient()
	err := redisCli.Del(context.Background(), "admin_refresh_token:"+userID).Err()
	if err != nil {
		logx.Errorf("撤销管理员令牌失败: %v", err)
		return err
	}

	return nil
}

// generateJTI 生成JWT ID，用于防止重放攻击
func generateJTI() string {
	// 使用加密安全的随机数生成器
	b := make([]byte, 16)
	rand.Read(b)
	// 使用URL安全的Base64编码
	return base64.RawURLEncoding.EncodeToString(b)
}

// contains 检查切片中是否包含某个字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
