package payment

import (
	"context"
	"fmt"
	"time"

	"yekaitai/pkg/common/wechatpay"

	"github.com/zeromicro/go-zero/core/logx"
)

// PaymentService 通用支付服务
type PaymentService struct {
	wechatPayService *wechatpay.PaymentService
}

// NewPaymentService 创建支付服务
func NewPaymentService() *PaymentService {
	return &PaymentService{
		wechatPayService: wechatpay.GetGlobalPaymentService(),
	}
}

// PaymentParams 支付参数
type PaymentParams struct {
	OrderID       uint              `json:"order_id"`       // 订单ID
	OrderNo       string            `json:"order_no"`       // 订单号
	TotalAmount   int               `json:"total_amount"`   // 总金额（分）
	PayAmount     int               `json:"pay_amount"`     // 支付金额（分）
	NeedPayment   bool              `json:"need_payment"`   // 是否需要支付
	PaymentParams map[string]string `json:"payment_params"` // 支付参数
}

// CreatePaymentRequest 创建支付请求
type CreatePaymentRequest struct {
	OrderID      uint   `json:"order_id"`      // 订单ID
	OrderNo      string `json:"order_no"`      // 订单号
	Amount       int    `json:"amount"`        // 金额（分）
	Description  string `json:"description"`   // 商品描述
	OpenID       string `json:"openid"`        // 用户OpenID
	Attach       string `json:"attach"`        // 附加数据
	BusinessType string `json:"business_type"` // 业务类型：activity, service, goods等
}

// CreatePayment 创建支付
func (s *PaymentService) CreatePayment(ctx context.Context, req *CreatePaymentRequest) (*PaymentParams, error) {
	// 如果金额为0，处理为免费订单
	if req.Amount == 0 {
		return s.handleFreeOrder(ctx, req)
	}

	// 创建微信支付
	return s.createWechatPayment(ctx, req)
}

// handleFreeOrder 处理免费订单
func (s *PaymentService) handleFreeOrder(ctx context.Context, req *CreatePaymentRequest) (*PaymentParams, error) {
	logx.Infof("处理免费订单: orderNo=%s, businessType=%s", req.OrderNo, req.BusinessType)

	return &PaymentParams{
		OrderID:     req.OrderID,
		OrderNo:     req.OrderNo,
		TotalAmount: 0,
		PayAmount:   0,
		NeedPayment: false,
		PaymentParams: map[string]string{
			"need_payment": "false",
			"message":      "免费订单，无需支付",
		},
	}, nil
}

// createWechatPayment 创建微信支付
func (s *PaymentService) createWechatPayment(ctx context.Context, req *CreatePaymentRequest) (*PaymentParams, error) {
	if s.wechatPayService == nil {
		return nil, fmt.Errorf("微信支付服务未初始化")
	}

	// 构建微信支付请求
	wechatReq := &wechatpay.CreateJSAPIPaymentRequest{
		OrderNo:     req.OrderNo,
		Amount:      int64(req.Amount),
		Description: req.Description,
		OpenID:      req.OpenID,
		Attach:      req.Attach,
		TimeExpire:  time.Now().Add(15 * time.Minute), // 15分钟有效期
	}

	// 调用微信支付接口
	resp, err := s.wechatPayService.CreateJSAPIPayment(ctx, wechatReq)
	if err != nil {
		logx.Errorf("创建微信支付失败: orderNo=%s, businessType=%s, error=%v", req.OrderNo, req.BusinessType, err)
		return nil, fmt.Errorf("创建微信支付失败: %w", err)
	}

	logx.Infof("创建微信支付成功: orderNo=%s, businessType=%s, prepayID=%s", req.OrderNo, req.BusinessType, resp.PrepayID)

	return &PaymentParams{
		OrderID:       req.OrderID,
		OrderNo:       req.OrderNo,
		TotalAmount:   req.Amount,
		PayAmount:     req.Amount,
		NeedPayment:   true,
		PaymentParams: resp.PaymentParams,
	}, nil
}

// QueryPaymentStatus 查询支付状态
func (s *PaymentService) QueryPaymentStatus(ctx context.Context, orderNo string) (string, error) {
	if s.wechatPayService == nil {
		return "", fmt.Errorf("微信支付服务未初始化")
	}

	req := &wechatpay.QueryOrderRequest{
		OutTradeNo: orderNo,
	}

	resp, err := s.wechatPayService.QueryOrder(ctx, req)
	if err != nil {
		return "", fmt.Errorf("查询支付状态失败: %w", err)
	}

	return resp.TradeState, nil
}

// CancelPayment 取消支付
func (s *PaymentService) CancelPayment(ctx context.Context, orderNo string) error {
	if s.wechatPayService == nil {
		return fmt.Errorf("微信支付服务未初始化")
	}

	req := &wechatpay.CloseOrderRequest{
		OutTradeNo: orderNo,
	}

	if err := s.wechatPayService.CloseOrder(ctx, req); err != nil {
		return fmt.Errorf("取消支付失败: %w", err)
	}

	logx.Infof("取消支付成功: orderNo=%s", orderNo)
	return nil
}

// RefundPayment 退款
func (s *PaymentService) RefundPayment(ctx context.Context, orderNo string, refundAmount int64, reason string) error {
	// 获取退款服务
	refundService := wechatpay.GetGlobalRefundService()
	if refundService == nil {
		return fmt.Errorf("微信退款服务未初始化")
	}

	refundReq := &wechatpay.CreateRefundRequest{
		OutTradeNo:   orderNo,
		OutRefundNo:  fmt.Sprintf("RF%s%d", orderNo, time.Now().Unix()),
		Reason:       reason,
		RefundAmount: refundAmount,
		TotalAmount:  refundAmount,
	}

	_, err := refundService.CreateRefund(ctx, refundReq)
	if err != nil {
		return fmt.Errorf("创建退款失败: %w", err)
	}

	logx.Infof("创建退款成功: orderNo=%s, refundAmount=%d", orderNo, refundAmount)
	return nil
}

// ValidatePaymentCallback 验证支付回调
func (s *PaymentService) ValidatePaymentCallback(ctx context.Context, callbackData map[string]interface{}) error {
	// 获取回调服务
	notifyService := wechatpay.GetGlobalNotifyService()
	if notifyService == nil {
		return fmt.Errorf("微信回调服务未初始化")
	}

	// 这里应该实现具体的回调验证逻辑
	// 暂时返回成功，实际应该验证签名等
	logx.Infof("验证支付回调: %+v", callbackData)
	return nil
}

// ProcessPaymentCallback 处理支付回调
func (s *PaymentService) ProcessPaymentCallback(ctx context.Context, callbackData map[string]interface{}) error {
	// 验证回调
	if err := s.ValidatePaymentCallback(ctx, callbackData); err != nil {
		return err
	}

	// 提取订单信息
	orderNo, ok := callbackData["out_trade_no"].(string)
	if !ok {
		return fmt.Errorf("回调数据中缺少订单号")
	}

	tradeState, ok := callbackData["trade_state"].(string)
	if !ok {
		return fmt.Errorf("回调数据中缺少交易状态")
	}

	// 处理支付结果
	if tradeState == "SUCCESS" {
		logx.Infof("订单支付成功: orderNo=%s", orderNo)
		// 这里可以添加支付成功的业务逻辑
	} else {
		logx.Errorf("订单支付失败: orderNo=%s, tradeState=%s", orderNo, tradeState)
		// 这里可以添加支付失败的业务逻辑
	}

	return nil
}

// GetGlobalPaymentService 获取全局支付服务实例
var globalPaymentService *PaymentService

func GetGlobalPaymentService() *PaymentService {
	if globalPaymentService == nil {
		globalPaymentService = NewPaymentService()
	}
	return globalPaymentService
}
