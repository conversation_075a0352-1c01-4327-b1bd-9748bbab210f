package svc

import (
	"fmt"

	"github.com/go-redis/redis/v8"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"gorm.io/gorm"

	"yekaitai/internal/config"
	wxConfig "yekaitai/internal/config"
	appointmentModel "yekaitai/internal/modules/appointment/model"
	pharmacyModel "yekaitai/internal/modules/pharmacy/model"
	"yekaitai/internal/svc"
	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/adapters/chongqing"
	"yekaitai/pkg/adapters/his"
	"yekaitai/pkg/adapters/jushuitan"
	"yekaitai/pkg/adapters/medlinker"
	"yekaitai/pkg/adapters/wechat"
	userModel "yekaitai/pkg/common/model/user"
	redisInfra "yekaitai/pkg/infra/redis"
	wxConsultationModel "yekaitai/wx_internal/modules/consultation/model"
	"yekaitai/wx_internal/queue"
	"yekaitai/wx_internal/service"
)

// WxServiceContext 微信小程序服务上下文，继承自后台服务上下文
type WxServiceContext struct {
	*svc.ServiceContext                 // 继承后台服务上下文
	WxConfig            wxConfig.Config // 微信小程序配置
	AbcYunClient        *abcyun.AbcYunClient
	MedlinkerClient     *medlinker.MedlinkerClient
	MedlinkerAIService  *medlinker.AIService

	ChongqingClient *chongqing.Client
	HisFactory      *his.ClientFactory
	WechatClient    *wechat.Client
	JushuitanClient *jushuitan.Client // 聚水潭客户端
	UserService     *service.UserService
	DB              *gorm.DB
	TencentMapKey   string        // 腾讯地图API密钥
	RedisClient     *redis.Client // Redis客户端

	// 科室仓库
	DepartmentRepo appointmentModel.DepartmentRepository

	// 药品仓库
	MedicineRepo pharmacyModel.MedicineRepository

	// 药品分类仓库
	MedicineCategoryRepo pharmacyModel.MedicineCategoryRepository

	// 库存仓库
	StockRepo pharmacyModel.StockRepository

	// 微信用户仓库
	WxUserRepo userModel.WxUserRepository

	// 微信端聊天模型
	ChatStreamModel  wxConsultationModel.ChatStreamModel
	ChatSessionModel wxConsultationModel.ChatSessionModel
}

// NewWxServiceContext 创建微信小程序服务上下文
func NewWxServiceContext(c config.Config) *WxServiceContext {
	// 创建微信客户端
	wechatCfg := c.Wechat

	// 打印微信配置信息
	fmt.Printf("微信配置: AppID=%s, AppSecret=%s\n", wechatCfg.AppID, wechatCfg.AppSecret)

	wechatClient := wechat.NewClient(wechat.Config{
		AppID:     wechatCfg.AppID,
		AppSecret: wechatCfg.AppSecret,
	})

	// 检查微信客户端是否正确初始化
	if wechatClient == nil {
		fmt.Println("警告: 微信客户端初始化失败")
	} else {
		fmt.Println("微信客户端初始化成功")
	}

	// 创建服务上下文
	serviceCtx := svc.NewServiceContext(c)

	// 初始化数据库连接
	mysqlConn := sqlx.NewMysql(c.Mysql.Master.Addr)

	// 初始化用户服务
	userService := service.NewUserService(mysqlConn, c)
	fmt.Println("用户服务初始化成功")

	// 初始化微信用户仓库
	wxUserRepo := userModel.NewWxUserRepository(nil) // 不需要传入gorm.DB实例，使用全局mysql连接
	fmt.Println("微信用户仓库初始化成功")

	// 初始化Redis客户端
	var redisClient *redis.Client
	if c.Redis.Addr != "" {
		// 使用项目现有的Redis基础设施
		err := redisInfra.Init(config.RedisConfig{
			Addr:     c.Redis.Addr,
			Password: c.Redis.Password,
			DB:       c.Redis.DB,
			PoolSize: c.Redis.PoolSize,
		})
		if err != nil {
			fmt.Printf("Redis初始化失败: %v\n", err)
			redisClient = nil
		} else {
			redisClient = redisInfra.GetClient()
			fmt.Println("Redis客户端初始化成功")
		}
	} else {
		fmt.Println("Redis配置为空，跳过Redis客户端初始化")
	}

	// 初始化医联客户端
	medlinkerClient := medlinker.NewMedlinkerClientWithRedis(medlinker.Config{
		BaseURL:        c.Medlinker.BaseURL,
		AppID:          c.Medlinker.AppID,
		AppSecret:      c.Medlinker.AppSecret,
		ModelID:        c.Medlinker.ModelID,
		DailyCallLimit: c.Medlinker.DailyCallLimit,
	}, redisClient)

	// 打印医联配置信息
	fmt.Printf("医联配置: BaseURL=%s, AppID=%s, ModelID=%d, Phone=%s, DailyCallLimit=%d\n",
		c.Medlinker.BaseURL, c.Medlinker.AppID, c.Medlinker.ModelID, c.Medlinker.Phone, c.Medlinker.DailyCallLimit)

	// 创建医联AI服务
	medlinkerAIService := medlinker.NewAIService(medlinkerClient)

	// 初始化聊天模型
	chatStreamModel := wxConsultationModel.NewChatStreamModel()
	chatSessionModel := wxConsultationModel.NewChatSessionModel()

	// 获取聚水潭客户端
	jushuitanClient := jushuitan.DefaultClient
	fmt.Println("聚水潭客户端初始化成功")

	// 打印腾讯地图API密钥
	fmt.Printf("配置中的腾讯地图配置详情: %+v\n", c.TencentMap)
	fmt.Printf("配置中的腾讯地图API密钥: [%s]\n", c.TencentMap.Key)

	// 启动聊天流消费者（如果Kafka配置有效）
	if len(c.Kafka.Brokers) > 0 {
		go queue.StartChatStreamConsumer(chatStreamModel, chatSessionModel, c.Kafka)
	} else {
		logx.Info("Kafka配置为空，跳过聊天流消费者启动")
	}

	return &WxServiceContext{
		ServiceContext:     serviceCtx,   // 创建后台服务上下文
		WxConfig:           c,            // 使用同一个配置
		WechatClient:       wechatClient, // 设置微信客户端
		UserService:        userService,  // 设置用户服务
		MedlinkerClient:    medlinkerClient,
		MedlinkerAIService: medlinkerAIService,
		JushuitanClient:    jushuitanClient, // 设置聚水潭客户端
		ChatStreamModel:    chatStreamModel,
		ChatSessionModel:   chatSessionModel,
		WxUserRepo:         wxUserRepo,       // 设置微信用户仓库
		TencentMapKey:      c.TencentMap.Key, // 设置腾讯地图API密钥
		RedisClient:        redisClient,      // 设置Redis客户端
	}
}
