package types

import "encoding/json"

// --- Common Supporting Structs (Assume defined elsewhere if causing errors) ---

// EmployeeInfo 员工信息
type EmployeeInfo struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// PatientAge 患者年龄 - Removed, assumed defined elsewhere

// PatientSummaryInfo 患者摘要信息
type PatientSummaryInfo struct {
	ID       string     `json:"id,omitempty"`
	Name     string     `json:"name,omitempty"`
	Sex      string     `json:"sex,omitempty"`
	Birthday string     `json:"birthday,omitempty"`
	Mobile   string     `json:"mobile,omitempty"`
	IDCard   string     `json:"idCard,omitempty"`
	Age      PatientAge `json:"age,omitempty"` // Assumes PatientAge is defined elsewhere
}

// DeliveryInfo 快递信息 - Removed, assumed defined elsewhere

// MedicalRecordInfo 病历信息 (Simplified based on usage)
type MedicalRecordInfo struct {
	Diagnosis           string `json:"diagnosis,omitempty"`           // Example field from charge event
	ChiefComplaint      string `json:"chiefComplaint,omitempty"`      // Example field from outpatient event
	PastHistory         string `json:"pastHistory,omitempty"`         // Example field from outpatient event
	FamilyHistory       string `json:"familyHistory,omitempty"`       // Example field from outpatient event
	PresentHistory      string `json:"presentHistory,omitempty"`      // Example field from outpatient event
	PhysicalExamination string `json:"physicalExamination,omitempty"` // Example field from outpatient event
	DoctorAdvice        string `json:"doctorAdvice,omitempty"`        // Example field from outpatient event
	// Add other fields as needed from outpatient event data doc
}

// ChargeFormItem 收费项目明细 - Removed, assumed defined elsewhere

// ChargeTransaction 收退费交易记录
type ChargeTransaction struct {
	ID          string  `json:"id,omitempty"`
	Amount      float64 `json:"amount,omitempty"`
	Created     string  `json:"created,omitempty"` // (datetime)
	IsRefunded  int     `json:"isRefunded,omitempty"`
	PayModeName string  `json:"payModeName,omitempty"`
	PayModeID   int     `json:"payModeId,omitempty"`  // Added from doc desc
	PaySubMode  string  `json:"paySubMode,omitempty"` // Added from doc desc
}

// ChargeTransactionPaidRecord 收费项详细信息
type ChargeTransactionPaidRecord struct {
	// Define based on actual API response if needed, placeholder for now
	ID        string  `json:"id,omitempty"`        // Example
	Amount    float64 `json:"amount,omitempty"`    // Example
	PayModeID int     `json:"payModeId,omitempty"` // Example
}

// PrescriptionFormItem 处方项信息
type PrescriptionFormItem struct {
	// Define based on actual API response if needed, placeholder for now
	ProductID string  `json:"productId,omitempty"` // Example
	UnitCount float64 `json:"unitCount,omitempty"` // Example
	// ... other fields
}

// PrescriptionForm 处方信息
type PrescriptionForm struct {
	Items []PrescriptionFormItem `json:"prescriptionFormItems,omitempty"`
	// Define other fields based on actual API response if needed
}

// ProductForm 治疗单信息
type ProductForm struct {
	// Define based on actual API response if needed, placeholder for now
	ProductID string  `json:"productId,omitempty"` // Example
	UnitCount float64 `json:"unitCount,omitempty"` // Example
}

// PharmacyInfo 药房详情 - Removed, assumed defined elsewhere

// AddressInfo 地址信息 (Simplified)
type AddressInfo struct {
	Province string `json:"province,omitempty"`
	City     string `json:"city,omitempty"`
	District string `json:"district,omitempty"`
	Detail   string `json:"detail,omitempty"`
}

// PatientSourceInfo 患者来源
type PatientSourceInfo struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// DispensingFormItem 发药单处方项信息 - Removed, assumed defined elsewhere

// DispensingForm 发药单-处方信息 - Removed, assumed defined elsewhere

// DepartmentSummaryInfo 科室摘要信息
type DepartmentSummaryInfo struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// ExaminationProduct 检验项目摘要信息 - Removed, assumed defined elsewhere

// RegistrationFormItemInfo 挂号单基础信息 (Subset of RegistrationSheetDetail)
type RegistrationFormItemInfo struct {
	PatientOrderID      string `json:"patientOrderId,omitempty"`
	RegistrationSheetID string `json:"registrationSheetId,omitempty"`
	PatientID           string `json:"patientId,omitempty"`
	DepartmentID        string `json:"departmentId,omitempty"`
	DepartmentName      string `json:"departmentName,omitempty"`
	DoctorID            string `json:"doctorId,omitempty"`
	DoctorName          string `json:"doctorName,omitempty"`
	OrderNo             string `json:"orderNo,omitempty"`
	ReserveDate         string `json:"reserveDate,omitempty"`
	ReserveTime         string `json:"reserveTime,omitempty"`
	IsReserved          int    `json:"isReserved,omitempty"`
	Status              int    `json:"status,omitempty"`
	SignIn              int    `json:"signIn,omitempty"`
	ConsultingRoomID    string `json:"consultingRoomId,omitempty"`
	ConsultingRoomName  string `json:"consultingRoomName,omitempty"`
	Type                int    `json:"type,omitempty"`
	PayStatus           int    `json:"payStatus,omitempty"`
	Code                int    `json:"code,omitempty"`
	RegistrationType    int    `json:"registrationType,omitempty"`
	Created             string `json:"created,omitempty"`
}

// GoodsSPUInfo 商品SPU信息
type GoodsSPUInfo struct {
	ID        string `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	BrandName string `json:"brandName,omitempty"`
	Material  string `json:"material,omitempty"`
}

// GoodsSpecInfo 商品SPU规格属性
type GoodsSpecInfo struct {
	Color string `json:"color,omitempty"`
	Spec  string `json:"spec,omitempty"`
}

// ShebaoInfo 商品社保信息 - Removed, assumed defined elsewhere

// InOrderItem 入库单条目
type InOrderItem struct {
	// Define based on actual API response if needed
	ProductID string `json:"productId,omitempty"` // Example
	// ... other fields
}

// OutOrderItem 出库单条目
type OutOrderItem struct {
	// Define based on actual API response if needed
	ProductID string `json:"productId,omitempty"` // Example
	// ... other fields
}

// CheckOrderItem 盘点条目 - Removed, assumed defined elsewhere

// TransOrderItemEventData 商品库存调拨项
type TransOrderItemEventData struct { // Renamed
	// Define based on actual API response if needed
	ProductID string `json:"productId,omitempty"` // Example
	// ... other fields
}

// PurchaseOrderItem 采购单条目 - Removed, assumed defined elsewhere

// --- EventData Specific Struct Definitions ---

// ChargeSheetDetail 收费单详细信息 (EventData for Module 1)
type ChargeSheetDetail struct {
	ID                           string                        `json:"id"` // Required
	Status                       int                           `json:"status,omitempty"`
	Type                         int                           `json:"type,omitempty"`
	Created                      string                        `json:"created,omitempty"`
	ReceivableFee                float64                       `json:"receivableFee,omitempty"`
	ReceivedFee                  float64                       `json:"receivedFee,omitempty"`
	RefundFee                    float64                       `json:"refundFee,omitempty"`
	DiscountFee                  float64                       `json:"discountFee"` // Required
	RefundTime                   string                        `json:"refundTime,omitempty"`
	Charger                      *EmployeeInfo                 `json:"charger,omitempty"`      // Use pointer for optional struct
	DeliveryInfo                 *DeliveryInfo                 `json:"deliveryInfo,omitempty"` // Assumes DeliveryInfo defined elsewhere
	Doctor                       *EmployeeInfo                 `json:"doctor,omitempty"`
	Patient                      *PatientSummaryInfo           `json:"patient,omitempty"`
	MedicalRecord                *MedicalRecordInfo            `json:"medicalRecord,omitempty"`
	ChargeFormItems              []ChargeFormItem              `json:"chargeFormItems,omitempty"` // Assumes ChargeFormItem defined elsewhere
	ChargeTransactions           []ChargeTransaction           `json:"chargeTransactions,omitempty"`
	ChargeTransactionPaidRecords []ChargeTransactionPaidRecord `json:"chargeTransactionPaidRecords,omitempty"`
	SellerID                     string                        `json:"sellerId,omitempty"`
	SellerName                   string                        `json:"sellerName,omitempty"`
	RegistrationSheetID          string                        `json:"registrationSheetId,omitempty"`
	PatientOrderID               string                        `json:"patientOrderId,omitempty"`
	IsDraft                      int                           `json:"isDraft,omitempty"`
	IsClosed                     int                           `json:"isClosed,omitempty"`
	ChargedTime                  string                        `json:"chargedTime,omitempty"`
}

// OutpatientSheetDetail 门诊单信息 (EventData for Module 2, Type 201, 202)
type OutpatientSheetDetail struct {
	ID                        string             `json:"id,omitempty"`
	PatientID                 string             `json:"patientId,omitempty"`
	PatientOrderID            string             `json:"patientOrderId,omitempty"`
	RegistrationSheetID       string             `json:"registrationSheetId,omitempty"`
	DepartmentID              string             `json:"departmentId,omitempty"`
	DepartmentName            string             `json:"departmentName,omitempty"`
	DoctorID                  string             `json:"doctorId,omitempty"`
	DoctorName                string             `json:"doctorName,omitempty"`
	Created                   string             `json:"created,omitempty"`
	DiagnosedDate             string             `json:"diagnosedDate,omitempty"`
	Status                    int                `json:"status,omitempty"`
	StatusName                string             `json:"statusName,omitempty"`
	MedicalRecord             *MedicalRecordInfo `json:"medicalRecord,omitempty"`
	PrescriptionChineseForms  []PrescriptionForm `json:"prescriptionChineseForms,omitempty"`
	PrescriptionWesternForms  []PrescriptionForm `json:"prescriptionWesternForms,omitempty"`
	PrescriptionInfusionForms []PrescriptionForm `json:"prescriptionInfusionForms,omitempty"`
	PrescriptionExternalForms []PrescriptionForm `json:"prescriptionExternalForms,omitempty"`
	ProductForms              []ProductForm      `json:"productForms,omitempty"`
}

// RegistrationSheetDetail 挂号单信息 (EventData for Module 2, Type 203)
type RegistrationSheetDetail struct {
	PatientOrderID      string `json:"patientOrderId,omitempty"`
	RegistrationSheetID string `json:"registrationSheetId,omitempty"`
	PatientID           string `json:"patientId,omitempty"`
	DepartmentID        string `json:"departmentId,omitempty"`
	DepartmentName      string `json:"departmentName,omitempty"`
	DoctorID            string `json:"doctorId,omitempty"`
	DoctorName          string `json:"doctorName,omitempty"`
	OrderNo             string `json:"orderNo,omitempty"`
	ReserveDate         string `json:"reserveDate,omitempty"`
	ReserveTime         string `json:"reserveTime,omitempty"`
	IsReserved          int    `json:"isReserved,omitempty"`
	Status              int    `json:"status,omitempty"`
	SignIn              int    `json:"signIn,omitempty"`
	ConsultingRoomID    string `json:"consultingRoomId,omitempty"`
	ConsultingRoomName  string `json:"consultingRoomName,omitempty"`
	Type                int    `json:"type,omitempty"`
	PayStatus           int    `json:"payStatus,omitempty"`
	Code                int    `json:"code,omitempty"`
	RegistrationType    int    `json:"registrationType,omitempty"`
	Created             string `json:"created,omitempty"`
}

// ProductInfoEventData 商品信息 (EventData for Module 3, Type 301, 302, 303)
type ProductInfoEventData struct { // Renamed from ProductInfo
	Children       []ProductInfoEventData `json:"children,omitempty"` // Renamed recursive reference
	ID             string                 `json:"id,omitempty"`
	Name           string                 `json:"name,omitempty"`
	Shebao         *ShebaoInfo            `json:"shebao,omitempty"` // Assumes ShebaoInfo defined elsewhere
	DisableSell    int                    `json:"disableSell,omitempty"`
	TypeID         int                    `json:"typeId,omitempty"`
	TypeName       string                 `json:"typeName,omitempty"`
	CustomTypeID   string                 `json:"customTypeId,omitempty"`
	CustomTypeName string                 `json:"customTypeName,omitempty"`
	Manufacturer   string                 `json:"manufacturer,omitempty"`
	MedicineNmpn   string                 `json:"medicineNmpn,omitempty"`
	BarCode        string                 `json:"barCode,omitempty"`
	PieceUnit      string                 `json:"pieceUnit,omitempty"`
	PieceNum       float64                `json:"pieceNum,omitempty"`
	PiecePrice     float64                `json:"piecePrice,omitempty"`
	PackageUnit    string                 `json:"packageUnit,omitempty"`
	PackagePrice   float64                `json:"packagePrice,omitempty"`
	ShortID        string                 `json:"shortId,omitempty"`
	Specification  string                 `json:"specification,omitempty"`
	OtcType        int                    `json:"otcType,omitempty"`
	Status         int                    `json:"status,omitempty"`
	LastModified   string                 `json:"lastModified,omitempty"`
	GoodsSpu       *GoodsSPUInfo          `json:"goodsSpu,omitempty"`
	GoodsSpec      *GoodsSpecInfo         `json:"goodsSpec,omitempty"`
}

// StockInOrderDetailEventData 入库单信息 (EventData for Module 3, Type 311, 306)
type StockInOrderDetailEventData struct { // Renamed from StockInOrderDetail
	ID              int64         `json:"id,omitempty"`
	InOrderItemList []InOrderItem `json:"inOrderItemList,omitempty"`
	InOrderID       int64         `json:"inOrderId,omitempty"` // Note: type might differ based on actual use
	OrderNo         string        `json:"orderNo,omitempty"`
	Status          int           `json:"status,omitempty"`
	OrganName       string        `json:"organName,omitempty"`
	OrganID         string        `json:"organId,omitempty"`
	Amount          float64       `json:"amount,omitempty"`
	Count           float64       `json:"count,omitempty"`
	KindCount       int           `json:"kindCount,omitempty"`
	Operator        string        `json:"operator,omitempty"`
	OperatorID      string        `json:"operatorId,omitempty"`
	Supplier        string        `json:"supplier,omitempty"` // Use SupplierInfo if available
	SupplierID      string        `json:"supplierId,omitempty"`
	EffectiveDate   string        `json:"effectiveDate,omitempty"`
	LastModified    string        `json:"lastModified,omitempty"`
	Pharmacy        *PharmacyInfo `json:"pharmacy,omitempty"`
	Type            int           `json:"type,omitempty"`
}

// StockOutOrderDetailEventData 出库单信息 (EventData for Module 3, Type 321)
type StockOutOrderDetailEventData struct { // Renamed from StockOutOrderDetail
	ID               int64          `json:"id,omitempty"`
	OutOrderItemList []OutOrderItem `json:"outOrderItemList,omitempty"`
	OrderNo          string         `json:"orderNo,omitempty"`
	Status           int            `json:"status,omitempty"`
	OrganName        string         `json:"organName,omitempty"`
	Type             int            `json:"type,omitempty"`
	KindCount        int            `json:"kindCount,omitempty"`
	Count            float64        `json:"count,omitempty"`
	Amount           float64        `json:"amount,omitempty"`
	Receiver         string         `json:"receiver,omitempty"`
	Operator         string         `json:"operator,omitempty"`
	EffectiveDate    string         `json:"effectiveDate,omitempty"`
	LastModified     string         `json:"lastModified,omitempty"`
	InOrderID        string         `json:"inOrderId,omitempty"` // Note: type might differ
	Pharmacy         *PharmacyInfo  `json:"pharmacy,omitempty"`
}

// CheckOrderDetailEventData 盘点单信息 (EventData for Module 3, Type 331)
type CheckOrderDetailEventData struct { // Renamed from CheckOrderDetail
	CheckOrderItemList   []CheckOrderItem `json:"checkOrderItemList,omitempty"` // Assumes CheckOrderItem defined elsewhere
	ID                   int64            `json:"id,omitempty"`
	OrderNo              string           `json:"orderNo,omitempty"`
	Status               int              `json:"status,omitempty"`
	OrganID              string           `json:"organId,omitempty"`
	OrganName            string           `json:"organName,omitempty"`
	Count                float64          `json:"count,omitempty"`
	KindCount            int              `json:"kindCount,omitempty"`
	TotalCostPriceChange float64          `json:"totalCostPriceChange,omitempty"`
	TotalPriceChange     float64          `json:"totalPriceChange,omitempty"`
	Operator             string           `json:"operator,omitempty"`
	OperatorID           string           `json:"operatorId,omitempty"`
	EffectiveDate        string           `json:"effectiveDate,omitempty"`
	LastModified         string           `json:"lastModified,omitempty"`
}

// TransOrderDetailEventData 调拨单信息 (EventData for Module 3, Type 341)
type TransOrderDetailEventData struct { // Renamed
	ID                 int64                     `json:"id,omitempty"`
	Amount             float64                   `json:"amount,omitempty"`
	Count              float64                   `json:"count,omitempty"`
	KindCount          int                       `json:"kindCount,omitempty"`
	OrderNo            string                    `json:"orderNo,omitempty"`
	TransType          int                       `json:"transType,omitempty"`
	TransCostPriceType int                       `json:"transCostPriceType,omitempty"`
	CreatedUser        *EmployeeInfo             `json:"createdUser,omitempty"`
	Created            string                    `json:"created,omitempty"`
	ReviewDate         string                    `json:"reviewDate,omitempty"`
	ReviewUser         *EmployeeInfo             `json:"reviewUser,omitempty"`
	InConfirmDate      string                    `json:"inConfirmDate,omitempty"`
	InConfirmUser      *EmployeeInfo             `json:"inConfirmUser,omitempty"`
	ToOrgan            *DepartmentSummaryInfo    `json:"toOrgan,omitempty"`
	InPharmacy         *PharmacyInfo             `json:"inPharmacy,omitempty"` // Assumes PharmacyInfo defined elsewhere
	FromOrgan          *DepartmentSummaryInfo    `json:"fromOrgan,omitempty"`
	OutPharmacy        *PharmacyInfo             `json:"outPharmacy,omitempty"` // Assumes PharmacyInfo defined elsewhere
	LastModified       string                    `json:"lastModified,omitempty"`
	LastModifiedUser   *EmployeeInfo             `json:"lastModifiedUser,omitempty"`
	Status             int                       `json:"status"` // Required
	Items              []TransOrderItemEventData `json:"items,omitempty"`
}

// PurchaseOrderDetailEventData 采购单信息 (EventData for Module 3, Type 350)
type PurchaseOrderDetailEventData struct { // Renamed from PurchaseOrderDetail
	Comment               string              `json:"comment,omitempty"` // Assuming based on doc
	ID                    int64               `json:"id,omitempty"`
	OrderNo               string              `json:"orderNo,omitempty"`
	PurchaseOrderItemList []PurchaseOrderItem `json:"purchaseOrderItemList,omitempty"` // Assumes PurchaseOrderItem defined elsewhere
	Status                int                 `json:"status,omitempty"`
	OrderType             int                 `json:"orderType,omitempty"`
	PurchaseOrganName     string              `json:"purchaseOrganName,omitempty"`
	PurchaseOrganID       string              `json:"purchaseOrganId,omitempty"`
	ApplyEmployeeID       string              `json:"applyEmployeeId,omitempty"`
	ApplyEmployeeName     string              `json:"applyEmployeeName,omitempty"`
	PurchaseOrderDate     string              `json:"purchaseOrderDate,omitempty"`
	Created               string              `json:"created,omitempty"`
}

// PatientEventData 患者信息 (EventData for Module 4, Type 401, 402)
type PatientEventData struct {
	ID            string             `json:"id,omitempty"`
	Name          string             `json:"name,omitempty"`
	Birthday      string             `json:"birthday,omitempty"`
	Mobile        string             `json:"mobile,omitempty"`
	Sex           string             `json:"sex,omitempty"`
	IDCard        string             `json:"idCard,omitempty"`
	Address       *AddressInfo       `json:"address,omitempty"`
	SN            string             `json:"sn,omitempty"`
	Profession    string             `json:"profession,omitempty"`
	Company       string             `json:"company,omitempty"`
	PatientSource *PatientSourceInfo `json:"patientSource,omitempty"`
	Remark        string             `json:"remark,omitempty"`
}

// PatientMergeInfo 患者合并信息 (EventData for Module 4, Type 403)
type PatientMergeInfo struct {
	SourcePatientIds []string          `json:"sourcePatientIds"`
	MergedPatient    *PatientEventData `json:"mergedPatient"` // Use pointer
}

// DispensingOrderDetail 发药单信息 (EventData for Module 5)
type DispensingOrderDetail struct {
	ClinicID        string                 `json:"clinicId,omitempty"`
	ID              string                 `json:"id,omitempty"`
	SourceSheetID   string                 `json:"sourceSheetId,omitempty"`
	DoctorID        string                 `json:"doctorId,omitempty"`
	PatientOrderID  string                 `json:"patientOrderId,omitempty"`
	PatientOrderNo  string                 `json:"patientOrderNo,omitempty"`
	DiagnosedDate   string                 `json:"diagnosedDate,omitempty"`
	MedicalRecord   *MedicalRecordInfo     `json:"medicalRecord,omitempty"`
	PatientInfo     *PatientSummaryInfo    `json:"patientInfo,omitempty"`
	DeliveryInfo    *DeliveryInfo          `json:"deliveryInfo,omitempty"` // Assumes DeliveryInfo defined elsewhere
	DeliveredStatus int                    `json:"deliveredStatus"`        // Required
	DeliveryType    int                    `json:"deliveryType,omitempty"`
	Status          int                    `json:"status,omitempty"`
	StatusName      string                 `json:"statusName,omitempty"`
	DispensedBy     string                 `json:"dispensedBy,omitempty"`
	DispensedByName string                 `json:"dispensedByName,omitempty"`
	DispensedTime   string                 `json:"dispensedTime,omitempty"`
	Created         string                 `json:"created,omitempty"`
	Pharmacy        *PharmacyInfo          `json:"pharmacy,omitempty"`
	NetIncomeFee    float64                `json:"netIncomeFee,omitempty"`
	Doctor          *EmployeeInfo          `json:"doctor,omitempty"`
	Department      *DepartmentSummaryInfo `json:"department,omitempty"`
	IsShebaoPay     int                    `json:"isShebaoPay,omitempty"`
	DispensingForms []DispensingForm       `json:"dispensingForms,omitempty"` // Assumes DispensingForm defined elsewhere
}

// ExaminationDetailEventData 检查检验单信息 (EventData for Module 6, Type 601, 604)
// Needs full definition based on 7.293 - Assuming it exists elsewhere for now
type ExaminationDetailEventData struct { // Renamed from ExaminationDetail
	ID                   string                           `json:"id,omitempty"`
	OutpatientFormItemID string                           `json:"outpatientFormItemId,omitempty"`
	PatientOrderID       string                           `json:"patientOrderId,omitempty"`
	PatientOrderNumber   string                           `json:"patientOrderNumber,omitempty"`
	Patient              *ExaminationPatientInfoEventData `json:"patient,omitempty"` // Renamed reference
	DoctorID             string                           `json:"doctorId,omitempty"`
	DoctorName           string                           `json:"doctorName,omitempty"`
	// ... Add ALL fields from 7.293 ...
	Results              []ExaminationResultEventData     `json:"results,omitempty"`     // Renamed reference
	Reports              []ExaminationReportEventData     `json:"reports,omitempty"`     // Renamed reference
	Attachments          []ExaminationAttachmentEventData `json:"attachments,omitempty"` // Renamed reference
	EyeResults           []ExaminationEyeResultEventData  `json:"eyeResults,omitempty"`  // Renamed reference
	RegistrationFormItem *RegistrationFormItemInfo        `json:"registrationFormItem,omitempty"`
}

// ExaminationPatientInfoEventData (Placeholder based on 7.293 usage)
type ExaminationPatientInfoEventData struct { // Renamed from ExaminationPatientInfo
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
	// ... Add other fields based on 7.293 Patient object
}

// ExaminationResultEventData (Placeholder - Assuming definition exists in abcyun_examination.go or similar)
type ExaminationResultEventData struct { // Renamed from ExaminationResult
	ID    string          `json:"id,omitempty"`
	Value json.RawMessage `json:"value,omitempty"` // Keep as RawMessage if needed for flexibility
	// ... other fields
}

// ExaminationReportEventData (Placeholder - Assuming definition exists elsewhere)
type ExaminationReportEventData struct { // Renamed from ExaminationReport
	ID string `json:"id,omitempty"`
	// ... other fields
}

// ExaminationAttachmentEventData (Placeholder - Assuming definition exists elsewhere)
type ExaminationAttachmentEventData struct { // Renamed from ExaminationAttachment
	URL string `json:"url,omitempty"`
	// ... other fields
}

// ExaminationEyeResultEventData (Placeholder - Assuming definition exists elsewhere)
type ExaminationEyeResultEventData struct { // Renamed from ExaminationEyeResult
	ProductID string `json:"productId,omitempty"`
	// ... other fields
}

// ExaminationRefundSummary 检查检验退费摘要 (EventData for Module 6, Type 602)
type ExaminationRefundSummary struct {
	ID                     string               `json:"id,omitempty"`
	ChargeSheetID          string               `json:"chargeSheetId,omitempty"`
	ChargeFormItemID       string               `json:"chargeFormItemId,omitempty"`
	OutpatientFormItemID   string               `json:"outpatientFormItemId,omitempty"`
	PatientOrderID         string               `json:"patientOrderId,omitempty"`
	PatientID              string               `json:"patientId,omitempty"`
	DoctorID               string               `json:"doctorId,omitempty"`
	ProductID              string               `json:"productId,omitempty"`
	Products               []ExaminationProduct `json:"products"`   // Required; Assumes ExaminationProduct defined elsewhere
	ProductIDs             []string             `json:"productIds"` // Required
	ComposeParentProductID string               `json:"composeParentProductId,omitempty"`
	ComposeParentName      string               `json:"composeParentName,omitempty"`
	Name                   string               `json:"name,omitempty"`
	OrderNo                string               `json:"orderNo,omitempty"`
	Remark                 string               `json:"remark,omitempty"`
	TesterID               string               `json:"testerId,omitempty"`
	CheckerID              string               `json:"checkerId,omitempty"`
	Count                  int                  `json:"count,omitempty"`
	CreatedBy              string               `json:"createdBy,omitempty"`
	Created                string               `json:"created,omitempty"`
	Type                   int                  `json:"type,omitempty"`
	SubType                int                  `json:"subType,omitempty"`
	DeviceType             int                  `json:"deviceType,omitempty"`
	BusinessType           int                  `json:"businessType,omitempty"`
}

// RegistrationEvent 挂号事件 (EventData for Module 7, Type 701, 702)
type RegistrationEvent struct {
	PatientOrderID         string `json:"patientOrderId,omitempty"`
	VisitSourceParentID    string `json:"visitSourceParentId,omitempty"`
	RegistrationSheetID    string `json:"registrationSheetId,omitempty"`
	VisitSourceParentName  string `json:"visitSourceParentName,omitempty"`
	PatientID              string `json:"patientId,omitempty"`
	VisitSourceID          string `json:"visitSourceId,omitempty"`
	DepartmentID           string `json:"departmentId,omitempty"`
	VisitSourceName        string `json:"visitSourceName,omitempty"`
	DepartmentName         string `json:"departmentName,omitempty"`
	VisitSourceFromID      string `json:"visitSourceFromId,omitempty"`
	DoctorID               string `json:"doctorId,omitempty"`
	VisitSourceFromName    string `json:"visitSourceFromName,omitempty"`
	DoctorName             string `json:"doctorName,omitempty"`
	VisitSourceRelatedType int    `json:"visitSourceRelatedType,omitempty"`
	OrderNo                string `json:"orderNo,omitempty"`
	VisitSourceRelatedID   string `json:"visitSourceRelatedId,omitempty"`
	ReserveDate            string `json:"reserveDate,omitempty"`
	VisitSourceRelateName  string `json:"visitSourceRelateName,omitempty"`
	ReserveTime            string `json:"reserveTime,omitempty"`
	VisitSourceRemark      string `json:"visitSourceRemark,omitempty"`
	IsReserved             int    `json:"isReserved,omitempty"`
	Status                 int    `json:"status,omitempty"`
	SignIn                 int    `json:"signIn,omitempty"`
	ConsultingRoomID       string `json:"consultingRoomId,omitempty"`
	ConsultingRoomName     string `json:"consultingRoomName,omitempty"`
	Type                   int    `json:"type,omitempty"`
	PayStatus              int    `json:"payStatus,omitempty"`
	Code                   int    `json:"code,omitempty"`
	RegistrationType       int    `json:"registrationType,omitempty"`
	Created                string `json:"created,omitempty"`
}

// ExecuteEventItem 执行记录执行项目信息
type ExecuteEventItem struct {
	ID            string  `json:"id,omitempty"`
	ExecuteItemID string  `json:"executeItemId,omitempty"`
	ProductID     string  `json:"productId,omitempty"`
	ProductName   string  `json:"productName,omitempty"`
	ExecuteCount  float64 `json:"executeCount,omitempty"`
	Unit          string  `json:"unit,omitempty"`
	UnitPrice     float64 `json:"unitPrice,omitempty"`
	TotalPrice    float64 `json:"totalPrice,omitempty"`
	Status        int     `json:"status,omitempty"`
	Remark        string  `json:"remark,omitempty"`
	BatchNo       string  `json:"batchNo,omitempty"`
	ExpireDate    string  `json:"expireDate,omitempty"`
}

// ExecuteEvent 执行事件 (EventData for Module 8, Type 801, 802)
type ExecuteEvent struct {
	ID             string              `json:"id"`             // Required
	ExecuteSheetID string              `json:"executeSheetId"` // Required
	PatientOrderID string              `json:"patientOrderId"` // Required
	Patient        *PatientSummaryInfo `json:"patient,omitempty"`
	Executors      []EmployeeInfo      `json:"executors,omitempty"`
	Items          []ExecuteEventItem  `json:"items,omitempty"`
	Operator       *EmployeeInfo       `json:"operator,omitempty"`
	ExecuteTime    string              `json:"executeTime,omitempty"`
}

// --- Base Event Structs ---

// EventSubscriptionPayload ABC云事件订阅推送请求体结构
type EventSubscriptionPayload struct {
	ID            string          `json:"id"`
	EventName     string          `json:"eventName"`
	EventModule   int             `json:"eventModule"`
	EventType     int             `json:"eventType"`
	ClinicID      string          `json:"clinicId"`
	EventData     json.RawMessage `json:"eventData"`
	CallbackCount int             `json:"callbackCount"`
}

// EventHeader ABC云事件订阅推送请求头信息 (用于签名验证和记录)
type EventHeader struct {
	Sign     string `header:"sign"`
	Ts       string `header:"ts"`
	AppID    string `header:"appId"`
	ClinicID string `header:"clinicId"`
}
