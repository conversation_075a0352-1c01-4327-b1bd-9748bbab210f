package jushuitan

import (
	"time"

	"gorm.io/gorm"
)

// 以下是商品相关的数据模型

// ItemSkuUploadRequest 普通商品资料上传请求
type ItemSkuUploadRequest struct {
	Items []ItemSku `json:"items"` // 商品列表
}

// ItemSku 商品信息
type ItemSku struct {
	SkuID             string  `json:"sku_id" gorm:"primaryKey;not null;comment:商品编码"`                      // 商品编码
	IID               string  `json:"i_id" gorm:"index;not null;comment:款式编码"`                             // 款式编码
	Brand             string  `json:"brand,omitempty" gorm:"type:varchar(100);comment:品牌"`                 // 品牌
	VcName            string  `json:"vc_name,omitempty" gorm:"type:varchar(100);comment:虚拟分类"`             // 虚拟分类
	CName             string  `json:"c_name,omitempty" gorm:"type:varchar(100);comment:商品分类"`              // 商品分类
	SPrice            float64 `json:"s_price,omitempty" gorm:"comment:基本售价"`                               // 基本售价
	ItemType          string  `json:"item_type,omitempty" gorm:"type:varchar(50);comment:商品类型"`            // 商品类型
	L                 float64 `json:"l,omitempty" gorm:"comment:长度"`                                       // 长度
	W                 float64 `json:"w,omitempty" gorm:"comment:宽度"`                                       // 宽度
	H                 float64 `json:"h,omitempty" gorm:"comment:高度"`                                       // 高度
	Pic               string  `json:"pic,omitempty" gorm:"type:varchar(255);comment:图片"`                   // 图片
	PicBig            string  `json:"pic_big,omitempty" gorm:"type:varchar(255);comment:大图"`               // 大图
	SkuPic            string  `json:"sku_pic,omitempty" gorm:"type:varchar(255);comment:SKU图片"`            // SKU图片
	Name              string  `json:"name" gorm:"type:varchar(200);not null;comment:商品名称"`                 // 商品名称
	Remark            string  `json:"remark,omitempty" gorm:"type:text;comment:备注"`                        // 备注
	PropertiesValue   string  `json:"properties_value,omitempty" gorm:"type:varchar(200);comment:属性值"`     // 属性值
	ShortName         string  `json:"short_name,omitempty" gorm:"type:varchar(100);comment:简称"`            // 简称
	Weight            float64 `json:"weight,omitempty" gorm:"comment:重量"`                                  // 重量
	Enabled           int     `json:"enabled,omitempty" gorm:"default:1;comment:是否启用"`                     // 是否启用
	SupplierName      string  `json:"supplier_name,omitempty" gorm:"type:varchar(100);comment:供应商名称"`      // 供应商名称
	SkuCode           string  `json:"sku_code,omitempty" gorm:"type:varchar(50);comment:商品条码"`             // 商品条码
	SupplierSkuID     string  `json:"supplier_sku_id,omitempty" gorm:"type:varchar(50);comment:供应商商品编码"`   // 供应商商品编码
	SupplierIID       string  `json:"supplier_i_id,omitempty" gorm:"type:varchar(50);comment:供应商款式编码"`     // 供应商款式编码
	OtherPrice1       float64 `json:"other_price_1,omitempty" gorm:"comment:其他价格1"`                        // 其他价格1
	OtherPrice2       float64 `json:"other_price_2,omitempty" gorm:"comment:其他价格2"`                        // 其他价格2
	OtherPrice3       float64 `json:"other_price_3,omitempty" gorm:"comment:其他价格3"`                        // 其他价格3
	OtherPrice4       float64 `json:"other_price_4,omitempty" gorm:"comment:其他价格4"`                        // 其他价格4
	OtherPrice5       float64 `json:"other_price_5,omitempty" gorm:"comment:其他价格5"`                        // 其他价格5
	Other1            string  `json:"other_1,omitempty" gorm:"type:varchar(255);comment:其他属性1"`            // 其他属性1
	Other2            string  `json:"other_2,omitempty" gorm:"type:varchar(255);comment:其他属性2"`            // 其他属性2
	Other3            string  `json:"other_3,omitempty" gorm:"type:varchar(255);comment:其他属性3"`            // 其他属性3
	Other4            string  `json:"other_4,omitempty" gorm:"type:varchar(255);comment:其他属性4"`            // 其他属性4
	Other5            string  `json:"other_5,omitempty" gorm:"type:varchar(255);comment:其他属性5"`            // 其他属性5
	StockDisabled     int     `json:"stock_disabled,omitempty" gorm:"default:0;comment:是否禁用库存"`            // 是否禁用库存
	CPrice            float64 `json:"c_price,omitempty" gorm:"comment:成本价"`                                // 成本价
	MarketPrice       float64 `json:"market_price,omitempty" gorm:"comment:市场价"`                           // 市场价
	Unit              string  `json:"unit,omitempty" gorm:"type:varchar(20);comment:单位"`                   // 单位
	Labels            string  `json:"labels,omitempty" gorm:"type:varchar(500);comment:标签列表,逗号分隔"`         // 标签列表,逗号分隔
	BatchEnabled      bool    `json:"batch_enabled,omitempty" gorm:"default:false;comment:是否启用批次"`         // 是否启用批次
	IsSeriesNumber    bool    `json:"is_series_number,omitempty" gorm:"default:false;comment:是否序列号"`       // 是否序列号
	OtherCode         string  `json:"other_code,omitempty" gorm:"type:varchar(50);comment:其他编码"`           // 其他编码
	ShelfLife         int     `json:"shelf_life,omitempty" gorm:"comment:保质期(天)"`                          // 保质期(天)
	HandDay           int     `json:"hand_day,omitempty" gorm:"comment:备货期(天)"`                            // 备货期(天)
	RejectLifecycle   int     `json:"rejectLifecycle,omitempty" gorm:"comment:拒收期(天)"`                     // 拒收期(天)
	LockupLifecycle   int     `json:"lockupLifecycle,omitempty" gorm:"comment:锁定期(天)"`                     // 锁定期(天)
	AdventLifecycle   int     `json:"adventLifecycle,omitempty" gorm:"comment:到货期(天)"`                     // 到货期(天)
	CategoryPropertys string  `json:"CategoryPropertys,omitempty" gorm:"type:longtext;comment:分类属性"`       // 分类属性
	DeletedLabels     string  `json:"deletedlabels,omitempty" gorm:"type:longtext;comment:删除的标签"`          // 删除的标签
	ProductionLicence string  `json:"production_licence,omitempty" gorm:"type:varchar(100);comment:生产许可证"` // 生产许可证
	PurchasePrice     float64 `json:"purchase_price,omitempty" gorm:"comment:采购价"`                         // 采购价
	IsNormal          bool    `json:"is_normal,omitempty" gorm:"default:true;comment:是否普通商品"`              // 是否普通商品

	CreatedAt time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:删除时间"` // 软删除
}

// TableName 设置ItemSku表名
func (ItemSku) TableName() string {
	return "jst_item_sku"
}

// ItemSkuUploadResponse 普通商品资料上传响应
type ItemSkuUploadResponse struct {
	Code int                    `json:"code"` // 响应代码
	Msg  string                 `json:"msg"`  // 响应消息
	Data *ItemSkuUploadRespData `json:"data"` // 响应数据
}

// ItemSkuUploadRespData 上传响应数据
type ItemSkuUploadRespData struct {
	Datas []ItemSkuUploadRespItem `json:"datas"` // 上传结果列表
}

// ItemSkuUploadRespItem 上传响应项
type ItemSkuUploadRespItem struct {
	IsSuccess bool   `json:"is_success" gorm:"comment:是否成功"`              // 是否成功
	SkuID     string `json:"sku id" gorm:"type:varchar(50);comment:商品编码"` // 商品编码
	Msg       string `json:"msg" gorm:"type:varchar(255);comment:消息"`     // 消息

	CreatedAt time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:删除时间"` // 软删除
}

// TableName 设置ItemSkuUploadRespItem表名
func (ItemSkuUploadRespItem) TableName() string {
	return "jst_item_sku_upload_resp_item"
}

// ItemSkuQueryRequest 查询普通商品资料请求
type ItemSkuQueryRequest struct {
	SkuIDs        string   `json:"sku_ids,omitempty" gorm:"type:varchar(500);comment:商品编码,逗号分隔"`     // 商品编码,逗号分隔，最多20个
	OuterIDs      []string `json:"outer_ids,omitempty" gorm:"type:json;comment:ERP内部商品编码/国标码"`       // ERP内部商品编码/国标码
	PageIndex     int      `json:"page_index,omitempty" gorm:"default:1;comment:第几页"`                // 第几页，默认1
	PageSize      int      `json:"page_size,omitempty" gorm:"default:30;comment:每页大小"`               // 每页大小，默认30，最大100
	ModifiedBegin string   `json:"modified_begin,omitempty" gorm:"type:varchar(50);comment:修改起始时间"`  // 修改起始时间
	ModifiedEnd   string   `json:"modified_end,omitempty" gorm:"type:varchar(50);comment:修改结束时间"`    // 修改结束时间
	ExactlyName   string   `json:"exactly_name,omitempty" gorm:"type:varchar(100);comment:商品名称(精确)"` // 商品名称(精确查询)
	Name          string   `json:"name,omitempty" gorm:"type:varchar(100);comment:商品名称(模糊)"`         // 商品名称(模糊查询)
	Brands        []string `json:"brand,omitempty" gorm:"type:json;comment:品牌"`                      // 品牌
	IIDs          []string `json:"i_ids,omitempty" gorm:"type:json;comment:款式编码"`                    // 款式编码
	DateField     string   `json:"date_field,omitempty" gorm:"type:varchar(20);comment:日期字段"`        // 日期字段，可传created,modified
	Flds          string   `json:"flds,omitempty" gorm:"type:varchar(200);comment:自定义查询字段"`          // 自定义查询字段
	SkuCodes      string   `json:"sku_codes,omitempty" gorm:"type:varchar(500);comment:辅助码"`         // 辅助码
	Labels        []string `json:"labels,omitempty" gorm:"type:json;comment:包含标签"`                   // 包含标签
	NotLabels     []string `json:"not_labels,omitempty" gorm:"type:json;comment:排除标签"`               // 排除标签
}

// ItemSkuQueryResponse 查询普通商品资料响应
type ItemSkuQueryResponse struct {
	Code int               `json:"code"` // 响应代码
	Msg  string            `json:"msg"`  // 响应消息
	Data *ItemSkuQueryData `json:"data"` // 响应数据
}

// ItemSkuQueryData 查询响应数据
type ItemSkuQueryData struct {
	DataCount int       `json:"data_count"` // 总记录数
	PageSize  int       `json:"page_size"`  // 每页大小
	PageIndex int       `json:"page_index"` // 当前页码
	PageCount int       `json:"page_count"` // 总页数
	HasNext   bool      `json:"has_next"`   // 是否有下一页
	Datas     []ItemSku `json:"datas"`      // 商品列表
}

// MallItemQueryRequest 查询普通商品资料(按款)请求 comment:普通商品资料(按款)查询请求表
type MallItemQueryRequest struct {
	IIDs          []string `json:"i_ids,omitempty"`          // 款编码数组
	OuterIDs      []string `json:"outer_ids,omitempty"`      // ERP内部商品编码/国标码
	PageIndex     int      `json:"page_index,omitempty"`     // 第几页，从第一页开始，默认1
	PageSize      int      `json:"page_size,omitempty"`      // 每页多少条，默认30，最大50
	ModifiedBegin string   `json:"modified_begin,omitempty"` // 修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天，与款式编码不能同时为空
	ModifiedEnd   string   `json:"modified_end,omitempty"`   // 修改结束时间，和起始时间必须同时存在，时间间隔不能超过七天，与款式编码不能同时为空
	OnlyItem      bool     `json:"only_item,omitempty"`      // 是否只查询款式信息不返回商品明细
	ItemFlds      []string `json:"item_flds,omitempty"`      // 查询款字段，用于查询指定需要的字段
	ItemskuFlds   []string `json:"itemsku_flds,omitempty"`   // 查询商品字段，用于查询指定需要的字段
	DateField     string   `json:"date_field,omitempty"`     // 日期字段，可传created,modified。默认按照modified查询
	Onsale        int      `json:"onsale,omitempty"`         // 款的商品状态是否启用：-1=禁用,0=备用,1=启用
}

// MallItemQueryResponse 查询普通商品资料(按款)响应 comment:普通商品资料(按款)查询响应表
type MallItemQueryResponse struct {
	Code int           `json:"code"` // 响应代码
	Msg  string        `json:"msg"`  // 响应消息
	Data *MallItemData `json:"data"` // 响应数据
}

// MallItemData 查询响应数据 comment:按款查询数据表
type MallItemData struct {
	TotalCount int        `json:"data_count"` // 总记录数
	PageSize   int        `json:"page_size"`  // 每页大小
	PageIndex  int        `json:"page_index"` // 当前页码
	PageCount  int        `json:"page_count"` // 总页数
	HasNext    bool       `json:"has_next"`   // 是否有下一页
	Items      []MallItem `json:"datas"`      // 款列表
}

// MallItem 款式信息 comment:款信息表
type MallItem struct {
	IID    string    `json:"i_id" gorm:"primaryKey;type:varchar(50);not null;comment:款编码"` // 款编码
	Name   string    `json:"name" gorm:"type:varchar(200);not null;comment:款名称"`           // 款名称
	Brand  string    `json:"brand,omitempty" gorm:"type:varchar(100);comment:品牌"`          // 品牌
	CName  string    `json:"c_name,omitempty" gorm:"type:varchar(100);comment:商品分类"`       // 商品分类
	VcName string    `json:"vc_name,omitempty" gorm:"type:varchar(100);comment:虚拟分类"`      // 虚拟分类
	SPrice float64   `json:"s_price,omitempty" gorm:"comment:基本售价"`                        // 基本售价
	Pic    string    `json:"pic,omitempty" gorm:"type:varchar(255);comment:图片"`            // 图片
	Skus   []ItemSku `json:"skus,omitempty" gorm:"-"`                                      // SKU列表，使用gorm:"-"忽略该字段
}

// TableName 设置MallItem表名
func (MallItem) TableName() string {
	return "jst_mall_item"
}

// SkuMapUploadRequest 上传店铺商品请求 comment:店铺商品资料上传请求表
type SkuMapUploadRequest struct {
	ShopID int      `json:"shop_id"` // 店铺ID
	Maps   []SkuMap `json:"maps"`    // 映射列表
}

// SkuMap 店铺商品映射 comment:商品映射表
type SkuMap struct {
	SkuID     string `json:"sku_id" gorm:"type:varchar(50);not null;comment:商品编码"`         // 商品编码
	ShopSkuID string `json:"shop_sku_id" gorm:"type:varchar(50);not null;comment:平台商品编码"`  // 平台商品编码
	ShopName  string `json:"shop_name,omitempty" gorm:"type:varchar(200);comment:平台商品名称"`  // 平台商品名称
	ShopType  string `json:"shop_type,omitempty" gorm:"type:varchar(50);comment:平台类型"`     // 平台类型
	ShopCatID string `json:"shop_cat_id,omitempty" gorm:"type:varchar(50);comment:平台分类ID"` // 平台分类ID
	Status    int    `json:"status,omitempty" gorm:"default:0;comment:状态"`                 // 状态
}

// TableName 设置SkuMap表名
func (SkuMap) TableName() string {
	return "jst_sku_map"
}

// SkuMapUploadResponse 上传店铺商品响应 comment:店铺商品资料上传响应表
type SkuMapUploadResponse struct {
	Code int                   `json:"code"` // 响应代码
	Msg  string                `json:"msg"`  // 响应消息
	Data *SkuMapUploadRespData `json:"data"` // 响应数据
}

// SkuMapUploadRespData 上传响应数据 comment:上传响应数据表
type SkuMapUploadRespData struct {
	Count int `json:"count"` // 成功数量
}

// SkuMapQueryRequest 查询店铺商品请求 comment:店铺商品资料查询请求表
type SkuMapQueryRequest struct {
	PageIndex         int    `json:"page_index,omitempty"`          // 第几页，从第一页开始，默认1
	PageSize          int    `json:"page_size,omitempty"`           // 每页多少条，默认30，最大50
	ModifiedBegin     string `json:"modified_begin,omitempty"`      // 修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天，与商品编码不能同时为空
	ModifiedEnd       string `json:"modified_end,omitempty"`        // 修改结束时间，和起始时间必须同时存在，时间间隔不能超过七天，与商品编码不能同时为空
	LinkModifiedBegin string `json:"link_modified_begin,omitempty"` // 商品对应关系修改起始时间
	LinkModifiedEnd   string `json:"link_modified_end,omitempty"`   // 商品对应关系修改结束时间
	SkuIDs            string `json:"sku_ids,omitempty"`             // 商品编码（线上商品编码），与修改时间不能同时为空，最多20个，逗号分隔
	ShopID            int    `json:"shop_id,omitempty"`             // 店铺编号
	CreatedBegin      string `json:"created_begin,omitempty"`       // 创建开始时间，与结束时间不能同时为空
	CreatedEnd        string `json:"created_end,omitempty"`         // 创建结束时间，和开始时间不能同时为空
}

// SkuMapQueryResponse 查询店铺商品响应
type SkuMapQueryResponse struct {
	Code      int             `json:"code"`
	Msg       string          `json:"msg"`
	Data      SkuMapQueryData `json:"data"`
	RequestID string          `json:"request_id"`
}

// SkuMapQueryData 查询店铺商品响应数据
type SkuMapQueryData struct {
	DataCount int              `json:"data_count"`     // 符合条件的商品总数
	PageSize  int              `json:"page_size"`      // 每页数据条数
	PageIndex int              `json:"page_index"`     // 当前页号
	PageCount int              `json:"page_count"`     // 总页数
	HasNext   bool             `json:"has_next"`       // 是否有下一页
	Datas     []SkuMapDataItem `json:"datas" gorm:"-"` // 数据
}

// SkuMapDataItem 店铺商品数据项
type SkuMapDataItem struct {
	CoID            int     `json:"co_id"`            // 公司ID
	ShopID          int     `json:"shop_id"`          // 店铺ID
	Channel         string  `json:"channel"`          // 渠道
	IID             string  `json:"iid"`              // 商品ID
	SkuID           string  `json:"sku_id"`           // 商品编码
	ShopIID         string  `json:"shop_iid"`         // 店铺商品ID
	ShopSkuID       string  `json:"shop_sku_id"`      // 店铺商品编码
	Modified        string  `json:"modified"`         // 修改时间
	LinkModified    string  `json:"link_modified"`    // 关系修改时间
	Enabled         bool    `json:"enabled"`          // 是否有效
	CID             string  `json:"cid"`              // 类目ID
	RawSkuID        string  `json:"raw_sku_id"`       // 原始商品编码
	ShopPrice       float64 `json:"shop_price"`       // 店铺价格
	Name            string  `json:"name"`             // 商品名称
	PropertiesValue string  `json:"properties_value"` // 商品规格
	Pic             string  `json:"pic"`              // 商品图片
	Created         string  `json:"created"`          // 创建时间
	ShopCreated     string  `json:"shop_created"`     // 店铺创建时间
	PullOffTime     string  `json:"pull_off_time"`    // 下架时间
	OuterSkuCode    string  `json:"outer_sku_code"`   // 外部商品编码
	Type            int     `json:"type"`             // 类型
	ShopQty         int     `json:"shop_qty"`         // 店铺库存
	LinkSkuID       string  `json:"link_sku_id"`      // 关联商品编码
	SalePriceMin    float64 `json:"sale_price_min"`   // 最低销售价
	SalePriceMax    float64 `json:"sale_price_max"`   // 最高销售价
}

// TableName 表名
func (SkuMapDataItem) TableName() string {
	return "jst_sku_map"
}

// CombineSkuUploadRequest 组合装商品资料上传请求 comment:组合装商品资料上传请求表
type CombineSkuUploadRequest struct {
	Items []CombineSku `json:"items"` // 组合装商品列表
}

// CombineSku 组合装商品信息 comment:组合装商品信息表
type CombineSku struct {
	SkuID    string           `json:"sku_id" gorm:"primaryKey;type:varchar(50);not null;comment:商品编码"` // 商品编码
	Name     string           `json:"name" gorm:"type:varchar(200);not null;comment:商品名称"`             // 商品名称
	IID      string           `json:"i_id" gorm:"index;type:varchar(50);not null;comment:款式编码"`        // 款式编码
	CName    string           `json:"c_name,omitempty" gorm:"type:varchar(100);comment:商品分类"`          // 商品分类
	Brand    string           `json:"brand,omitempty" gorm:"type:varchar(100);comment:品牌"`             // 品牌
	PicPath  string           `json:"pic_path,omitempty" gorm:"type:varchar(255);comment:图片路径"`        // 图片路径
	Remark   string           `json:"remark,omitempty" gorm:"type:text;comment:备注"`                    // 备注
	Price    float64          `json:"price,omitempty" gorm:"comment:价格"`                               // 价格
	SkuItems []CombineSkuItem `json:"sku_items" gorm:"-"`                                              // 组合明细，使用gorm:"-"忽略该字段
}

// TableName 设置CombineSku表名
func (CombineSku) TableName() string {
	return "jst_combine_sku"
}

// CombineSkuItem 组合装商品子项 comment:组合装商品子项表
type CombineSkuItem struct {
	SkuID string  `json:"sku_id" gorm:"type:varchar(50);not null;comment:子商品编码"` // 子商品编码
	Qty   float64 `json:"qty" gorm:"comment:数量"`                                 // 数量
}

// TableName 设置CombineSkuItem表名
func (CombineSkuItem) TableName() string {
	return "jst_combine_sku_item"
}

// CombineSkuUploadResponse 组合装商品资料上传响应 comment:组合装商品资料上传响应表
type CombineSkuUploadResponse struct {
	Code int                       `json:"code"` // 响应代码
	Msg  string                    `json:"msg"`  // 响应消息
	Data *CombineSkuUploadRespData `json:"data"` // 响应数据
}

// CombineSkuUploadRespData 上传响应数据 comment:上传响应数据表
type CombineSkuUploadRespData struct {
	Datas []CombineSkuUploadRespItem `json:"datas"` // 上传结果列表
}

// CombineSkuUploadRespItem 上传响应项 comment:上传响应项表
type CombineSkuUploadRespItem struct {
	IsSuccess bool   `json:"is_success"` // 是否成功
	SkuID     string `json:"sku_id"`     // 商品编码
	Msg       string `json:"msg"`        // 消息
}

// CombineSkuQueryRequest 组合装商品资料查询请求 comment:组合装商品资料查询请求表
type CombineSkuQueryRequest struct {
	PageIndex          int      `json:"page_index,omitempty"`           // 第几页，从第一页开始，默认1
	PageSize           int      `json:"page_size,omitempty"`            // 每页多少条，默认30，最大50
	ModifiedBegin      string   `json:"modified_begin,omitempty"`       // 修改起始时间，与ModifiedEnd必须同时存在，时间间隔不能超过七天
	ModifiedEnd        string   `json:"modified_end,omitempty"`         // 修改结束时间，与ModifiedBegin必须同时存在，时间间隔不能超过七天
	SkuIDs             string   `json:"sku_ids,omitempty"`              // 组合装商品编码，与修改时间不能同时为空，最多20个，用逗号分隔
	IIDs               []string `json:"i_ids,omitempty"`                // 组合装款式编码
	CombineItemskuFlds []string `json:"combine_itemsku_flds,omitempty"` // 自定义返回参数数组
	EntySkuID          string   `json:"enty_sku_id,omitempty"`          // 实体编码
}

// CombineSkuQueryResponse 组合装商品资料查询响应 comment:组合装商品资料查询响应表
type CombineSkuQueryResponse struct {
	Code      int                  `json:"code"`      // 响应代码
	Msg       string               `json:"msg"`       // 响应消息
	Data      *CombineSkuQueryData `json:"data"`      // 响应数据
	IsSuccess bool                 `json:"issuccess"` // 是否成功
	RequestID string               `json:"requestId"` // 请求ID
}

// CombineSkuQueryData 查询数据 comment:查询数据表
type CombineSkuQueryData struct {
	PageSize  int                `json:"page_size"`  // 每页多少条
	PageIndex int                `json:"page_index"` // 第几页
	DataCount int                `json:"data_count"` // 总条数
	PageCount int                `json:"page_count"` // 总页数
	HasNext   bool               `json:"has_next"`   // 是否有下一页
	Datas     []CombineSkuDetail `json:"datas"`      // 数据集合
}

// CombineSkuDetail 组合装商品详情
type CombineSkuDetail struct {
	IID             string              `json:"i_id"`             // 组合装款式编码
	Name            string              `json:"name"`             // 组合装商品名称
	ShortName       string              `json:"short_name"`       // 组合装简称
	VcName          string              `json:"vc_name"`          // 虚拟分类
	Pic             string              `json:"pic"`              // 图片地址
	PropertiesValue string              `json:"properties_value"` // 组合装颜色及规格
	SalePrice       float64             `json:"sale_price"`       // 组合装售价
	Weight          float64             `json:"weight"`           // 组合装重量
	SkuID           string              `json:"sku_id"`           // 组合装商品编码
	Modified        string              `json:"modified"`         // 修改时间
	Created         string              `json:"created"`          // 创建时间
	EntySkuID       string              `json:"enty_sku_id"`      // 组合商品实体编码
	Labels          string              `json:"labels"`           // 标签
	Brand           string              `json:"brand"`            // 品牌
	CostPrice       float64             `json:"cost_price"`       // 组合装商品成本价
	Enabled         int                 `json:"enabled"`          // 是否启用
	SkuCode         string              `json:"sku_code"`         // 国标码
	OtherPrice1     float64             `json:"other_price_1"`    // 其它价格1
	OtherPrice2     float64             `json:"other_price_2"`    // 其它价格2
	OtherPrice3     float64             `json:"other_price_3"`    // 其它价格3
	OtherPrice4     float64             `json:"other_price_4"`    // 其它价格4
	OtherPrice5     float64             `json:"other_price_5"`    // 其它价格5
	Other1          string              `json:"other_1"`          // 其它属性1
	Other2          string              `json:"other_2"`          // 其它属性2
	Other3          string              `json:"other_3"`          // 其它属性3
	Other4          string              `json:"other_4"`          // 其它属性4
	Other5          string              `json:"other_5"`          // 其它属性5
	L               float64             `json:"l"`                // 长
	W               float64             `json:"w"`                // 宽
	H               float64             `json:"h"`                // 高
	Volume          float64             `json:"volume"`           // 体积
	ItemType        string              `json:"item_type"`        // 商品属性
	Remark          string              `json:"remark"`           // 备注
	SkuQty          int                 `json:"sku_qty"`          // 子商品数量
	Items           []CombineSkuSubItem `json:"items" gorm:"-"`   // 子商品列表，使用gorm:"-"忽略该字段
}

// TableName 设置CombineSkuDetail表名
func (CombineSkuDetail) TableName() string {
	return "jst_combine_sku_detail"
}

// CombineSkuSubItem 组合装子商品项
type CombineSkuSubItem struct {
	SrcSkuID     string  `json:"src_sku_id"`     // 子商品编码
	Qty          int     `json:"qty"`            // 子商品数量
	SalePrice    float64 `json:"sale_price"`     // 应占售价
	Modified     string  `json:"modified"`       // 修改时间
	CombineSkuID string  `json:"combine_sku_id"` // 组合装商品编码
}

// TableName 设置CombineSkuSubItem表名
func (CombineSkuSubItem) TableName() string {
	return "jst_combine_sku_sub_item"
}

// CategoryAddOrUpdateRequest 商品类目上传/更新请求 comment:商品类目上传/更新请求表
type CategoryAddOrUpdateRequest struct {
	CName     string   `json:"c_name"`                // 分类名
	ParentCID int      `json:"parent_c_id,omitempty"` // 父级分类id
	CID       int      `json:"c_id,omitempty"`        // 分类Id
	Sort      int      `json:"sort,omitempty"`        // 排序
	IsPV      bool     `json:"is_pv,omitempty"`       // 是否添加PV
	PVNames   []string `json:"pv_names,omitempty"`    // 商品类目
	Enable    *bool    `json:"enable,omitempty"`      // 是否启用
}

// CategoryAddOrUpdateResponse 商品类目上传/更新响应 comment:商品类目上传/更新响应表
type CategoryAddOrUpdateResponse struct {
	Code string                   `json:"code"` // 响应代码
	Msg  string                   `json:"msg"`  // 响应消息
	Data *CategoryAddOrUpdateData `json:"data"` // 响应数据
}

// CategoryAddOrUpdateData 类目上传/更新数据 comment:类目上传/更新数据表
type CategoryAddOrUpdateData struct {
	CID       int `json:"c_id"`        // 分类ID
	ParentCID int `json:"parent_c_id"` // 父级分类ID
}

// CategoryQueryRequest 商品类目查询请求 comment:商品类目查询请求表
type CategoryQueryRequest struct {
	CIDs          []string `json:"c_ids,omitempty"`          // 类目id集合
	ParentCIDs    []string `json:"parent_c_ids,omitempty"`   // 父级类目id集合
	PageIndex     int      `json:"page_index,omitempty"`     // 第几页
	PageSize      int      `json:"page_size,omitempty"`      // 每页记录数
	ModifiedBegin string   `json:"modified_begin,omitempty"` // 修改开始时间，格式：2020-01-12 18:34:13
	ModifiedEnd   string   `json:"modified_end,omitempty"`   // 修改结束时间，格式：2020-01-14 18:34:13
}

// CategoryQueryResponse 商品类目查询响应 comment:商品类目查询响应表
type CategoryQueryResponse struct {
	Code int                `json:"code"` // 响应代码
	Msg  string             `json:"msg"`  // 响应消息
	Data *CategoryQueryData `json:"data"` // 响应数据
}

// CategoryQueryData 商品类目查询数据
type CategoryQueryData struct {
	PageSize  int                 `json:"page_size"`  // 每页多少条
	PageIndex int                 `json:"page_index"` // 第几页
	DataCount int                 `json:"data_count"` // 总条数
	PageCount int                 `json:"page_count"` // 总页数
	HasNext   bool                `json:"has_next"`   // 是否有下一页
	Datas     []CategoryQueryItem `json:"datas"`      // 数据集合
}

// CategoryQueryItem 类目项 comment:类目项表
type CategoryQueryItem struct {
	CID       int                 `json:"c_id" gorm:"primaryKey;comment:分类ID"`                   // 分类ID
	CName     string              `json:"c_name" gorm:"type:varchar(100);not null;comment:分类名称"` // 分类名称
	ParentCID int                 `json:"parent_c_id" gorm:"index;comment:父级分类ID"`               // 父级分类ID
	Sort      int                 `json:"sort" gorm:"comment:排序"`                                // 排序
	Level     int                 `json:"level" gorm:"comment:级别"`                               // 级别
	IsShow    int                 `json:"is_show" gorm:"default:1;comment:是否显示"`                 // 是否显示
	Children  []CategoryQueryItem `json:"children,omitempty" gorm:"-"`                           // 子分类，使用gorm:"-"忽略该字段
}

// TableName 设置CategoryQueryItem表名
func (CategoryQueryItem) TableName() string {
	return "jst_category_query_item"
}

// BomSaveRequest BOM信息上传请求 comment:BOM信息上传请求表
type BomSaveRequest struct {
	BomList   []BomItem      `json:"bom_list,omitempty"`   // 商品主料集合
	MinorList []BomMinorItem `json:"minor_list,omitempty"` // 商品辅料集合
}

// BomItem 主料项 comment:主料项表
type BomItem struct {
	RmQty         float64 `json:"rm_qty,omitempty" gorm:"comment:主料数量"`                             // 主料数量
	OuterSkuID    string  `json:"outer_sku_id" gorm:"type:varchar(50);not null;comment:成品商品编码"`     // 成品商品编码
	MapOuterSkuID string  `json:"map_outer_sku_id" gorm:"type:varchar(50);not null;comment:主料商品编码"` // 主料商品编码
	IID           string  `json:"i_id" gorm:"type:varchar(50);not null;comment:款式编码"`               // 款式编码
	IsDelete      bool    `json:"is_delete,omitempty" gorm:"default:false;comment:是否删除主料"`          // 是否删除主料
}

// TableName 设置BomItem表名
func (BomItem) TableName() string {
	return "jst_bom_item"
}

// BomMinorItem 辅料项 comment:辅料项表
type BomMinorItem struct {
	OuterSkuID string `json:"outer_sku_id" gorm:"type:varchar(50);not null;comment:辅料商品编码"` // 辅料商品编码
	IID        string `json:"i_id" gorm:"type:varchar(50);not null;comment:款式编码"`           // 款式编码
	IsDelete   bool   `json:"is_delete,omitempty" gorm:"default:false;comment:是否删除辅料"`      // 是否删除辅料
}

// TableName 设置BomMinorItem表名
func (BomMinorItem) TableName() string {
	return "jst_bom_minor_item"
}

// BomSaveResponse BOM信息上传响应 comment:BOM信息上传响应表
type BomSaveResponse struct {
	Code int    `json:"code"` // 响应代码
	Msg  string `json:"msg"`  // 响应消息
	Data bool   `json:"data"` // 是否成功
}

// BomQueryRequest BOM信息查询请求 comment:BOM信息查询请求表
type BomQueryRequest struct {
	SkuIDs        []string `json:"sku_ids,omitempty"`        // 商品编码；与修改时间不能同时为空
	ModifiedStart string   `json:"modified_start,omitempty"` // 修改开始时间
	ModifiedEnd   string   `json:"modified_end,omitempty"`   // 修改结束时间
	Page          BomPage  `json:"page"`                     // 分页信息
}

// BomPage 分页信息
type BomPage struct {
	CurrentPage int `json:"current_page"` // 当前页
	PageSize    int `json:"page_size"`    // 页大小
}

// BomQueryResponse BOM信息查询响应
type BomQueryResponse struct {
	Code      int          `json:"code"`       // 响应代码
	Msg       string       `json:"msg"`        // 响应消息
	Data      BomQueryData `json:"data"`       // 响应数据
	RequestID string       `json:"request_id"` // 请求ID
}

// BomQueryData 查询数据 comment:查询数据表
type BomQueryData struct {
	Page BomPage        `json:"page" gorm:"-"` // 分页信息
	List []BomQueryList `json:"list" gorm:"-"` // BOM数据
}

// BomQueryList BOM数据
type BomQueryList struct {
	SkuID     string         `json:"sku_id"`     // 商品编码
	IID       string         `json:"i_id"`       // 款式编码
	Boms      []BomItem      `json:"boms"`       // 主料BOM信息
	BomMinors []BomMinorItem `json:"bom_minors"` // 辅料BOM信息
}

// ItemSkuCostPriceUploadRequest 商品历史成本价上传请求
type ItemSkuCostPriceUploadRequest struct {
	Datas []ItemSkuCostPrice `json:"datas"`
}

// ItemSkuCostPrice 商品历史成本价
type ItemSkuCostPrice struct {
	SkuID     string `json:"sku_id"`     // 商品编码
	EndDate   string `json:"end_date"`   // 截止时间
	CostPrice string `json:"cost_price"` // 历史成本价
}

// ItemSkuCostPriceUploadResponse 商品历史成本价上传响应
type ItemSkuCostPriceUploadResponse struct {
	Code      int    `json:"code"`
	IsSuccess bool   `json:"issuccess"`
	Msg       string `json:"msg"`
}

// HistoryCostPriceQueryRequest 商品历史成本价查询请求
type HistoryCostPriceQueryRequest struct {
	SkuIDs                []string `json:"sku_ids"`                    // 商品编码数组，必填
	WmsCoIDs              []int    `json:"wms_co_ids,omitempty"`       // 仓储方（入参仓储方没有，默认查询当前公司）
	GetWay                string   `json:"get_way,omitempty"`          // 获取方式：all（获取全部商品历史成本价），newest（获取最新商品历史成本价），默认：all
	IsUseItemSkuCostPrice bool     `json:"is_use_item_sku_cost_price"` // 是否使用普通商品资料成本价，默认：true
}

// HistoryCostPriceQueryResponse 商品历史成本价查询响应
type HistoryCostPriceQueryResponse struct {
	Code int                       `json:"code"`
	Msg  string                    `json:"msg"`
	Data HistoryCostPriceQueryData `json:"data"`
}

// HistoryCostPriceQueryData 商品历史成本价查询数据
type HistoryCostPriceQueryData struct {
	SkuHistoryCostPriceMaps map[string][]HistoryCostPriceItem `json:"sku_history_cost_price_maps"` // key是wmsCoId，值是历史成本价列表
}

// HistoryCostPriceItem 历史成本价项
type HistoryCostPriceItem struct {
	SkuID     string  `json:"sku_id"`     // 商品编码
	CostPrice float64 `json:"cost_price"` // 成本价
	BeginDate string  `json:"begin_date"` // 开始日期
	EndDate   string  `json:"end_date"`   // 结束日期
	Remark    string  `json:"remark"`     // 备注
}

// SupplierSkuSaveRequest 商品多供应商上传/更新请求
type SupplierSkuSaveRequest struct {
	SupplierID    int      `json:"supplier_id"`               // 供应商Id
	IID           string   `json:"i_id"`                      // 款式编码
	SkuID         string   `json:"sku_id"`                    // 商品编码
	SupplierSkuID string   `json:"supplier_sku_id,omitempty"` // 供应商商品编码
	SupplierIID   string   `json:"supplier_i_id,omitempty"`   // 供应商商品款号
	CostPrice     *float64 `json:"cost_price,omitempty"`      // 采购成本价
	PurchaseURL   string   `json:"purchase_url,omitempty"`    // 采购链接
	DeliveryDay   *int     `json:"delivery_day,omitempty"`    // 交货天数
	PackQty       *int     `json:"pack_qty,omitempty"`        // 装箱数
	Remark        string   `json:"remark,omitempty"`          // 备注
	IsDefault     *bool    `json:"is_default,omitempty"`      // 是否默认
}

// SupplierSkuSaveResponse 商品多供应商上传/更新响应
type SupplierSkuSaveResponse struct {
	Code      string `json:"code"`
	Msg       string `json:"msg"`
	RequestID string `json:"request_id"`
}

// SupplierSkuQueryRequest 商品多供应商查询请求
type SupplierSkuQueryRequest struct {
	SkuID string `json:"sku_id"` // 商品编码
}

// SupplierSkuQueryResponse 商品多供应商查询响应
type SupplierSkuQueryResponse struct {
	Code int                    `json:"code"`
	Msg  string                 `json:"msg"`
	Data []SupplierSkuQueryItem `json:"data"`
}

// SupplierSkuQueryItem 商品供应商项
type SupplierSkuQueryItem struct {
	SupplierID    int     `json:"supplier_id"`             // 供应商Id
	CoName        string  `json:"co_name"`                 // 供应商名称
	IID           string  `json:"i_id"`                    // 款式编码
	SkuID         string  `json:"sku_id"`                  // 商品编码
	SupplierSkuID string  `json:"supplier_sku_id"`         // 供应商商品编码
	SupplierIID   string  `json:"supplier_i_id"`           // 供应商款式编码
	CostPrice     float64 `json:"cost_price"`              // 成本价
	PurchaseURL   string  `json:"purchase_url"`            // 采购链接
	DeliveryDay   int     `json:"delivery_day"`            // 交货天数
	PackQty       int     `json:"pack_qty"`                // 装箱数
	Remark        string  `json:"remark"`                  // 备注
	IsDefault     bool    `json:"is_default"`              // 是否默认
	Created       string  `json:"created,omitempty"`       // 创建时间
	SupplierName  string  `json:"supplier_name,omitempty"` // 供应商名称
	SupplierCode  string  `json:"supplier_code,omitempty"` // 供应商编码
}

// ItemSkuBatchUploadRequest 批量上传/更新普通商品资料请求
type ItemSkuBatchUploadRequest struct {
	IsUpdate       bool      `json:"is_update,omitempty"`
	IsAddLabels    bool      `json:"is_add_labels,omitempty"`
	IsAddOtherCode bool      `json:"is_add_other_code,omitempty"`
	Items          []ItemSku `json:"items"`
}

// ItemSkuBatchUploadResponse 批量上传/更新普通商品资料响应
type ItemSkuBatchUploadResponse struct {
	Code int                        `json:"code"`
	Act  int                        `json:"act"`
	Data []ItemSkuBatchUploadResult `json:"data"`
	Msg  string                     `json:"msg"`
}

// ItemSkuBatchUploadResult 上传结果项
type ItemSkuBatchUploadResult struct {
	SkuID   string `json:"sku_id"`
	Message string `json:"message"`
}

// SkuLinkBindRequest 绑定/解绑商品对应关系请求
type SkuLinkBindRequest struct {
	Bind        bool          `json:"bind"`         // 绑定/解绑
	OutputLinks bool          `json:"output_links"` // 返回成功结果集
	Links       []SkuLinkItem `json:"links"`        // 商品数据集合
}

// SkuLinkItem 商品对应关系
type SkuLinkItem struct {
	ShopID    int    `json:"shop_id"`     // 店铺id
	ShopIID   string `json:"shop_i_id"`   // 平台店铺款式id
	ShopSkuID string `json:"shop_sku_id"` // 平台店铺商品编码
	LinkSkuID string `json:"link_sku_id"` // 对应商品编码
}

// SkuLinkBindResponse 绑定/解绑商品对应关系响应
type SkuLinkBindResponse struct {
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
	Data []SkuLinkItem `json:"data"`
}

// SkuBinsSetRequest 更新商品库容信息请求
type SkuBinsSetRequest struct {
	Items []SkuBinItem `json:"items"` // 商品项
}

// SkuBinItem 商品库容项
type SkuBinItem struct {
	SkuID       string   `json:"sku_id"`                 // 商品编码
	Bin         string   `json:"bin,omitempty"`          // 主仓位
	OtherBin    []string `json:"other_bin,omitempty"`    // 补充仓位
	MinQty      int      `json:"min_qty,omitempty"`      // 库容下限
	MaxQty      int      `json:"max_qty,omitempty"`      // 库容上限
	OverflowQty int      `json:"overflow_qty,omitempty"` // 溢出数量
	PackQty     int      `json:"pack_qty,omitempty"`     // 标准装箱数
	PackVolume  float64  `json:"pack_volume,omitempty"`  // 装箱体积
}

// SkuBinsSetResponse 更新商品库容信息响应
type SkuBinsSetResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data bool   `json:"data"`
}

// ItemUploadRequest 商品款式新增/更新请求
type ItemUploadRequest struct {
	Items []ItemUploadItem `json:"items"`
}

// ItemUploadItem 商品款式项
type ItemUploadItem struct {
	IID          string  `json:"i_id"`                    // 款式编码
	Name         string  `json:"name,omitempty"`          // 名称
	Brand        string  `json:"brand,omitempty"`         // 品牌
	Category     string  `json:"category,omitempty"`      // 分类
	VcName       string  `json:"vc_name,omitempty"`       // 虚拟分类
	SupplierName string  `json:"supplier_name,omitempty"` // 供应商名称
	SupplierIID  string  `json:"supplier_i_id,omitempty"` // 供应商款式编码
	SalePrice    float64 `json:"sale_price,omitempty"`    // 基本售价
	MarketPrice  float64 `json:"market_price,omitempty"`  // 市场价
	CostPrice    float64 `json:"cost_price,omitempty"`    // 成本价
	Weight       float64 `json:"weight,omitempty"`        // 重量
	Unit         string  `json:"unit,omitempty"`          // 单位
	ItemType     string  `json:"item_type,omitempty"`     // 商品属性
	L            float64 `json:"l,omitempty"`             // 长
	W            float64 `json:"w,omitempty"`             // 宽
	H            float64 `json:"h,omitempty"`             // 高
	Remark       string  `json:"remark,omitempty"`        // 备注
	Pic          string  `json:"pic,omitempty"`           // 主图
	Pic2         string  `json:"pic2,omitempty"`          // 辅图
}

// ItemUploadResponse 商品款式新增/更新响应
type ItemUploadResponse struct {
	Code int                `json:"code"`
	Msg  string             `json:"msg"`
	Data []ItemUploadResult `json:"data"`
}

// ItemUploadResult 上传结果项
type ItemUploadResult struct {
	IID     string `json:"i_id"`
	Message string `json:"message"`
}

// SupplierSkuQueryListRequest 商品多供应商查询请求
type SupplierSkuQueryListRequest struct {
	SkuID       string `json:"skuid,omitempty"`       // 商品编码
	IID         string `json:"iid,omitempty"`         // 款式编码
	SupplierID  int    `json:"supplier_id,omitempty"` // 供应商id
	Begin       string `json:"begin,omitempty"`       // 最早创建时间
	End         string `json:"end,omitempty"`         // 最晚创建时间
	CurrentPage int    `json:"current_page"`          // 当前分页
	PageSize    int    `json:"page_size"`             // 分页数
}

// SupplierSkuQueryListResponse 商品多供应商查询响应
type SupplierSkuQueryListResponse struct {
	Code      int                      `json:"code"`
	Msg       string                   `json:"msg"`
	Data      SupplierSkuQueryListData `json:"data"`
	RequestID string                   `json:"request_id"`
}

// SupplierSkuQueryListData 商品多供应商查询数据
type SupplierSkuQueryListData struct {
	Page SupplierSkuQueryPage   `json:"page"` // 分页信息
	List []SupplierSkuQueryItem `json:"list"` // 供应商列表
}

// SupplierSkuQueryPage 商品多供应商查询分页
type SupplierSkuQueryPage struct {
	Pages int `json:"pages"` // 总页数
	Count int `json:"count"` // 总条数
}

// SkuBinDB 商品仓位数据库模型
type SkuBinDB struct {
	ID          int64          `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	SkuID       string         `gorm:"column:sku_id;index;type:varchar(50);not null;comment:商品编码" json:"sku_id"` // 商品编码
	Bin         string         `gorm:"column:bin;type:varchar(50);comment:主仓位" json:"bin"`                       // 主仓位
	OtherBin    string         `gorm:"column:other_bin;type:text;comment:补充仓位" json:"other_bin"`                 // 补充仓位,JSON数组
	MinQty      int            `gorm:"column:min_qty;comment:库容下限" json:"min_qty"`                               // 库容下限
	MaxQty      int            `gorm:"column:max_qty;comment:库容上限" json:"max_qty"`                               // 库容上限
	OverflowQty int            `gorm:"column:overflow_qty;comment:溢出数量" json:"overflow_qty"`                     // 溢出数量
	PackQty     int            `gorm:"column:pack_qty;comment:标准装箱数" json:"pack_qty"`                            // 标准装箱数
	PackVolume  float64        `gorm:"column:pack_volume;comment:装箱体积" json:"pack_volume"`                       // 装箱体积
	CreatedAt   time.Time      `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`                       // 记录创建时间
	UpdatedAt   time.Time      `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`                       // 记录更新时间
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;index;comment:记录删除时间" json:"deleted_at"`                 // 记录删除时间
}

// TableName 指定表名
func (SkuBinDB) TableName() string {
	return "jst_sku_bin_db"
}
