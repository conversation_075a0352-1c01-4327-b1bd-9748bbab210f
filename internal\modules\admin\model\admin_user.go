package model

import (
	"time"
)

// AdminUser 管理员表
type AdminUser struct {
	AdminID   uint       `json:"admin_id" gorm:"primaryKey;autoIncrement;comment:管理员ID"`
	Username  string     `json:"username" gorm:"uniqueIndex;type:varchar(50);not null;comment:用户名"`
	Password  string     `json:"-" gorm:"type:varchar(100);not null;comment:密码"`
	StoreID   *uint      `json:"store_id" gorm:"index;default:0;comment:所属门店ID（店长专用）"`
	Email     string     `json:"email" gorm:"type:varchar(100);not null;comment:邮箱"`
	Mobile    string     `json:"mobile" gorm:"uniqueIndex;type:varchar(20);not null;comment:手机号"`
	Status    int        `json:"status" gorm:"default:1;not null;comment:状态：1-正常，0-禁用"`
	CreatorID uint       `json:"creator_id" gorm:"default:0;comment:创建人ID"`
	LastLogin time.Time  `json:"last_login" gorm:"comment:最后登录时间"`
	LastIP    string     `json:"last_ip" gorm:"type:varchar(50);not null;comment:最后登录IP"`
	IsDeleted bool       `json:"is_deleted" gorm:"default:false;not null;comment:是否删除"`
	DeletedAt *time.Time `json:"deleted_at" gorm:"comment:删除时间"`
	CreatedAt time.Time  `json:"created_at" gorm:"not null;comment:创建时间"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"not null;comment:更新时间"`
}
