package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/wx_redeemer/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterWxRedeemerRoutes 注册核销员管理相关路由
func RegisterWxRedeemerRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建处理器
	redeemerHandler := handler.NewRedeemerHandler()

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 核销员管理路由
	server.AddRoutes(
		[]rest.Route{
			// 获取核销员列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/wx-redeemer/list",
				Handler: adminAuthWrapper(redeemerHandler.GetRedeemerList),
			},
			// 创建核销员
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/wx-redeemer/create",
				Handler: adminAuthWrapper(redeemerHandler.CreateRedeemer),
			},
			// 更新核销员
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/wx-redeemer/update",
				Handler: adminAuthWrapper(redeemerHandler.UpdateRedeemer),
			},
			// 更新核销员状态
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/wx-redeemer/status",
				Handler: adminAuthWrapper(redeemerHandler.UpdateRedeemerStatus),
			},
			// 删除核销员
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/wx-redeemer/delete",
				Handler: adminAuthWrapper(redeemerHandler.DeleteRedeemer),
			},
		},
	)
}
