package bootstrap

import (
	"fmt"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/user/model"

	"github.com/zeromicro/go-zero/core/logx"
)

// WxUserAnnualConsumptionMigration 微信用户年度消费记录表迁移
func WxUserAnnualConsumptionMigration() error {
	db := mysql.GetDB()

	// 自动迁移，创建或更新表结构
	if err := db.AutoMigrate(&model.WxUserAnnualConsumption{}); err != nil {
		logx.Errorf("微信用户年度消费记录表迁移失败: %v", err)
		return fmt.Errorf("微信用户年度消费记录表迁移失败: %w", err)
	}

	// 创建复合索引
	indexName := "idx_wx_user_annual_consumption_user_id_year"
	if !db.Migrator().HasIndex(&model.WxUserAnnualConsumption{}, indexName) {
		if err := db.Exec("CREATE UNIQUE INDEX " + indexName + " ON wx_user_annual_consumption(user_id, year)").Error; err != nil {
			logx.Errorf("创建用户年度消费记录索引失败: %v", err)
			return fmt.Errorf("创建用户年度消费记录索引失败: %w", err)
		}
	}

	logx.Info("微信用户年度消费记录表迁移成功")
	return nil
} 