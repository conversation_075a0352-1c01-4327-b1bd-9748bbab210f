package handler

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"

	"yekaitai/pkg/response"
	"yekaitai/wx_internal/modules/registration/model"
)

// SourceQueryRequest 号源查询请求
type SourceQueryRequest struct {
	Pbrq   string `form:"pbrq"`          // 排班日期，格式：2025-05-14
	WsjgID int    `form:"wsjgid"`        // 卫生机构ID
	JgksID int    `form:"jgksid"`        // 机构科室ID
	YsID   string `form:"ysid,optional"` // 医生ID，可选参数，不传表示查询所有医生
}

// SourceRecordQueryRequest 号源记录查询请求
type SourceRecordQueryRequest struct {
	Pbrq   string `form:"pbrq"`          // 排班日期，格式：2025-05-14
	WsjgID int    `form:"wsjgid"`        // 卫生机构ID
	JgksID int    `form:"jgksid"`        // 机构科室ID
	YsID   string `form:"ysid,optional"` // 医生ID，可选参数，不传表示查询所有医生
	Period int    `form:"period"`        // 时间段 1:上午 2:下午 3:夜班
}

// SourceHandler 号源查询处理器
type SourceHandler struct {
	sourceRepo model.SourceRepository
}

// NewSourceHandler 创建号源查询处理器
func NewSourceHandler() *SourceHandler {
	return &SourceHandler{
		sourceRepo: model.NewSourceRepository(),
	}
}

// Query 查询号源
func (h *SourceHandler) Query(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[号源查询] 开始处理请求: %s %s", r.Method, r.URL.String())

	var req SourceQueryRequest
	if err := httpx.Parse(r, &req); err != nil {
		errMsg := fmt.Sprintf("[号源查询] 请求参数解析失败: %v", err)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("请求参数解析失败: %v", err))
		return
	}

	// 记录解析后的请求参数
	logx.Infof("[号源查询] 请求参数: pbrq=%s, wsjgid=%d, jgksid=%d, ysid=%s", req.Pbrq, req.WsjgID, req.JgksID, req.YsID)

	// 参数验证
	if req.Pbrq == "" {
		errMsg := "[号源查询] 参数验证失败: 排班日期不能为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "排班日期不能为空")
		return
	}
	if req.WsjgID <= 0 {
		errMsg := fmt.Sprintf("[号源查询] 参数验证失败: 卫生机构ID无效: %d", req.WsjgID)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("卫生机构ID无效: %d", req.WsjgID))
		return
	}
	if req.JgksID <= 0 {
		errMsg := fmt.Sprintf("[号源查询] 参数验证失败: 机构科室ID无效: %d", req.JgksID)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("机构科室ID无效: %d", req.JgksID))
		return
	}

	// 查询号源
	sources, err := h.sourceRepo.GetSources(r.Context(), req.Pbrq, req.WsjgID, req.JgksID, req.YsID)
	if err != nil {
		// 详细记录错误信息
		errMsg := fmt.Sprintf("[号源查询] 查询号源失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("查询号源失败: %v", err))
		}
		return
	}

	// 记录成功结果
	logx.Infof("[号源查询] 查询成功，返回号源数量: %d", len(sources))
	response.Success(w, sources)
}

// QueryRecords 查询号源记录
func (h *SourceHandler) QueryRecords(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[号源记录查询] 开始处理请求: %s %s", r.Method, r.URL.String())

	var req SourceRecordQueryRequest
	if err := httpx.Parse(r, &req); err != nil {
		errMsg := fmt.Sprintf("[号源记录查询] 请求参数解析失败: %v", err)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("请求参数解析失败: %v", err))
		return
	}

	// 记录解析后的请求参数
	logx.Infof("[号源记录查询] 请求参数: pbrq=%s, wsjgid=%d, jgksid=%d, ysid=%s, period=%d", req.Pbrq, req.WsjgID, req.JgksID, req.YsID, req.Period)

	// 参数验证
	if req.Pbrq == "" {
		errMsg := "[号源记录查询] 参数验证失败: 排班日期不能为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "排班日期不能为空")
		return
	}
	if req.WsjgID <= 0 {
		errMsg := fmt.Sprintf("[号源记录查询] 参数验证失败: 卫生机构ID无效: %d", req.WsjgID)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("卫生机构ID无效: %d", req.WsjgID))
		return
	}
	if req.JgksID <= 0 {
		errMsg := fmt.Sprintf("[号源记录查询] 参数验证失败: 机构科室ID无效: %d", req.JgksID)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("机构科室ID无效: %d", req.JgksID))
		return
	}
	if req.Period < 0 || req.Period > 3 {
		errMsg := fmt.Sprintf("[号源记录查询] 参数验证失败: 时间段参数无效: %d，应为：0(全部), 1(上午), 2(下午), 3(夜班)", req.Period)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("时间段参数无效: %d，应为：0(全部), 1(上午), 2(下午), 3(夜班)", req.Period))
		return
	}

	// 查询号源记录
	records, err := h.sourceRepo.GetSourceRecordsByPeriod(r.Context(), req.Pbrq, req.WsjgID, req.JgksID, req.YsID, req.Period)
	if err != nil {
		// 详细记录错误信息
		errMsg := fmt.Sprintf("[号源记录查询] 查询号源记录失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("查询号源记录失败: %v", err))
		}
		return
	}

	// 记录成功结果
	logx.Infof("[号源记录查询] 查询成功，返回记录数量: %d", len(records))
	response.Success(w, records)
}

// QueryCompleteRecords 查询完整号源记录信息
func (h *SourceHandler) QueryCompleteRecords(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[完整号源记录查询] 开始处理请求: %s %s", r.Method, r.URL.String())

	var req SourceRecordQueryRequest
	if err := httpx.Parse(r, &req); err != nil {
		errMsg := fmt.Sprintf("[完整号源记录查询] 请求参数解析失败: %v", err)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("请求参数解析失败: %v", err))
		return
	}

	// 记录解析后的请求参数
	logx.Infof("[完整号源记录查询] 请求参数: pbrq=%s, wsjgid=%d, jgksid=%d, ysid=%s, period=%d", req.Pbrq, req.WsjgID, req.JgksID, req.YsID, req.Period)

	// 参数验证
	if req.Pbrq == "" {
		errMsg := "[完整号源记录查询] 参数验证失败: 排班日期不能为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "排班日期不能为空")
		return
	}
	if req.WsjgID <= 0 {
		errMsg := fmt.Sprintf("[完整号源记录查询] 参数验证失败: 卫生机构ID无效: %d", req.WsjgID)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("卫生机构ID无效: %d", req.WsjgID))
		return
	}
	if req.JgksID <= 0 {
		errMsg := fmt.Sprintf("[完整号源记录查询] 参数验证失败: 机构科室ID无效: %d", req.JgksID)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("机构科室ID无效: %d", req.JgksID))
		return
	}
	if req.Period < 0 || req.Period > 3 {
		errMsg := fmt.Sprintf("[完整号源记录查询] 参数验证失败: 时间段参数无效: %d，应为：0(全部), 1(上午), 2(下午), 3(夜班)", req.Period)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("时间段参数无效: %d，应为：0(全部), 1(上午), 2(下午), 3(夜班)", req.Period))
		return
	}

	// 查询完整号源记录
	records, err := h.sourceRepo.GetCompleteSourceRecords(r.Context(), req.Pbrq, req.WsjgID, req.JgksID, req.YsID, req.Period)
	if err != nil {
		// 详细记录错误信息
		errMsg := fmt.Sprintf("[完整号源记录查询] 查询完整号源记录失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("查询完整号源记录失败: %v", err))
		}
		return
	}

	// 记录成功结果
	logx.Infof("[完整号源记录查询] 查询成功，返回记录数量: %d", len(records))
	response.Success(w, records)
}
