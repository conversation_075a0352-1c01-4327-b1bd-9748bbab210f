package handler

import (
	"net/http"

	"yekaitai/internal/types"
	userModel "yekaitai/pkg/common/model/user"
	"yekaitai/wx_internal/modules/goods/service"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type AddressGoZeroHandler struct {
	addressService *service.AddressService
}

func NewAddressGoZeroHandler() *AddressGoZeroHandler {
	return &AddressGoZeroHandler{
		addressService: service.NewAddressService(),
	}
}

// 地址列表请求
type AddressListRequest struct {
	types.PageRequest
}

// 地址详情请求
type AddressDetailRequest struct {
	AddressID uint `path:"id"` // 地址ID
}

// 地址更新请求
type AddressUpdateDetailRequest struct {
	AddressID uint `path:"id"` // 地址ID
	userModel.AddressUpdateRequest
}

// 地址删除请求
type AddressDeleteRequest struct {
	AddressID uint `path:"id"` // 地址ID
}

// 设置默认地址请求
type AddressSetDefaultRequest struct {
	AddressID uint `path:"id"` // 地址ID
}

// CreateAddress 创建收货地址
func (h *AddressGoZeroHandler) CreateAddress(w http.ResponseWriter, r *http.Request) {
	var req userModel.AddressCreateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	address, err := h.addressService.CreateAddress(r.Context(), userID, &req)
	if err != nil {
		logx.Errorf("创建收货地址失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(address))
}

// UpdateAddress 更新收货地址
func (h *AddressGoZeroHandler) UpdateAddress(w http.ResponseWriter, r *http.Request) {
	var req AddressUpdateDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	address, err := h.addressService.UpdateAddress(r.Context(), req.AddressID, userID, &req.AddressUpdateRequest)
	if err != nil {
		logx.Errorf("更新收货地址失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(address))
}

// DeleteAddress 删除收货地址
func (h *AddressGoZeroHandler) DeleteAddress(w http.ResponseWriter, r *http.Request) {
	var req AddressDeleteRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	err := h.addressService.DeleteAddress(r.Context(), req.AddressID, userID)
	if err != nil {
		logx.Errorf("删除收货地址失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// GetAddress 获取收货地址详情
func (h *AddressGoZeroHandler) GetAddress(w http.ResponseWriter, r *http.Request) {
	var req AddressDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	address, err := h.addressService.GetAddress(r.Context(), req.AddressID, userID)
	if err != nil {
		logx.Errorf("获取收货地址详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(address))
}

// ListAddresses 获取收货地址列表
func (h *AddressGoZeroHandler) ListAddresses(w http.ResponseWriter, r *http.Request) {
	var req types.PageRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	addresses, err := h.addressService.ListAddresses(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取收货地址列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 手动分页处理（地址数据通常不多，在内存中分页）
	total := int64(len(addresses))
	start := (req.Page - 1) * req.Size
	end := start + req.Size

	if start >= len(addresses) {
		addresses = []*userModel.AddressWithRegionNames{}
	} else {
		if end > len(addresses) {
			end = len(addresses)
		}
		addresses = addresses[start:end]
	}

	// 使用统一分页响应
	resp := types.NewPageResponse(addresses, total, &req)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// GetDefaultAddress 获取默认收货地址
func (h *AddressGoZeroHandler) GetDefaultAddress(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	address, err := h.addressService.GetDefaultAddress(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取默认收货地址失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(address))
}

// SetDefaultAddress 设置默认收货地址
func (h *AddressGoZeroHandler) SetDefaultAddress(w http.ResponseWriter, r *http.Request) {
	var req AddressSetDefaultRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	err := h.addressService.SetDefaultAddress(r.Context(), req.AddressID, userID)
	if err != nil {
		logx.Errorf("设置默认收货地址失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}
