package model

import (
	"yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/common/model/redeemer"
	"yekaitai/pkg/common/model/user"

	"gorm.io/gorm"
)

// 以下是为了维持向后兼容性的类型别名

// 重导出用户相关类型
type WxUser = user.WxUser

// 重导出仓库接口
type WxUserRepository = user.WxUserRepository

// 重导出患者相关类型
type WxPatient = patient.WxPatient
type WxDoctor = doctor.WxDoctor
type WxRedeemer = redeemer.WxRedeemer

// 重导出仓库接口
type WxPatientRepository = patient.WxPatientRepository
type WxDoctorRepository = doctor.WxDoctorRepository
type WxRedeemerRepository = redeemer.WxRedeemerRepository

// 重导出仓库创建函数
func NewWxUserRepository(db *gorm.DB) WxUserRepository {
	return user.NewWxUserRepository(db)
}

// 重导出患者仓库创建函数
func NewWxPatientRepository(db *gorm.DB) WxPatientRepository {
	return patient.NewWxPatientRepository(db)
}

// 重导出医生仓库创建函数
func NewWxDoctorRepository(db *gorm.DB) WxDoctorRepository {
	return doctor.NewWxDoctorRepository(db)
}

// 重导出核销员仓库创建函数
func NewWxRedeemerRepository() WxRedeemerRepository {
	return redeemer.NewWxRedeemerRepository()
}
