package wanliniu

import (
	"context"
	"sync"

	"github.com/zeromicro/go-zero/core/logx"
)

var (
	client     *Client
	service    *Service
	clientOnce sync.Once
)

// Init 初始化万里牛ERP客户端
func Init(config Config) {
	clientOnce.Do(func() {
		client = NewClient(&config)

		// 创建服务实例并关联客户端
		service = NewService()
		service.client = client

		// 测试连接
		ctx := context.Background()
		if err := client.TestConnection(ctx); err != nil {
			logx.Errorf("[WanLiNiu] 初始化连接测试失败: %v", err)
		} else {
			logx.Info("[WanLiNiu] 初始化成功")
		}
	})
}

// GetClient 获取万里牛ERP客户端实例
func GetClient() *Client {
	if client == nil {
		logx.Error("[WanLiNiu] 客户端未初始化，请先调用Init方法")
		return nil
	}
	return client
}

// GetService 获取万里牛ERP服务实例
func GetService() *Service {
	if service == nil {
		logx.Error("[WanLiNiu] 服务未初始化，请先调用Init方法")
		return nil
	}
	return service
}
