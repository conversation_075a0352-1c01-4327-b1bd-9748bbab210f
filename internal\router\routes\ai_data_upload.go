package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/ai_data_upload/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterAIDataUploadRoutes 注册AI数据上传相关路由
func RegisterAIDataUploadRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建处理器
	aiKnowledgeHandler := handler.NewAIKnowledgeBaseHandler(serverCtx)

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// AI知识库管理路由
	server.AddRoutes(
		[]rest.Route{
			// 上传AI知识库文件
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/ai-knowledge/upload",
				Handler: adminAuthWrapper(aiKnowledgeHandler.CreateAIKnowledge),
			},
			// 获取AI知识库列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/ai-knowledge/list",
				Handler: adminAuthWrapper(aiKnowledgeHandler.ListAIKnowledge),
			},
			// 获取AI知识库详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/ai-knowledge/:id",
				Handler: adminAuthWrapper(aiKnowledgeHandler.GetAIKnowledge),
			},
			// 查看文件内容
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/ai-knowledge/:id/content",
				Handler: adminAuthWrapper(aiKnowledgeHandler.ViewFileContent),
			},
			// 删除AI知识库
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/ai-knowledge/:id",
				Handler: adminAuthWrapper(aiKnowledgeHandler.DeleteAIKnowledge),
			},
		},
	)
}
