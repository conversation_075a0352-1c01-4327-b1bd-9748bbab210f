package redis

import (
	"sync"

	"yekaitai/pkg/infra/kafka"
)

var (
	kafkaProducer     kafka.Producer
	kafkaProducerOnce sync.Once
)

// GetKafkaProducer 获取Kafka生产者实例（单例模式）
func GetKafkaProducer() kafka.Producer {
	kafkaProducerOnce.Do(func() {
		// 在实际项目中，这里可以根据配置创建真正的Kafka生产者
		// 目前使用模拟实现
		kafkaProducer = kafka.NewMockProducer()
	})
	return kafkaProducer
}

// 为测试设置Kafka生产者
func SetKafkaProducer(producer kafka.Producer) {
	kafkaProducer = producer
}
