package kafka

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"sync"
	"time"

	"github.com/Shopify/sarama"
	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"

	"yekaitai/internal/config"
)

var (
	// 生产者
	producer sarama.SyncProducer
	// 消费者组
	consumerGroup sarama.ConsumerGroup
	// 锁
	lock sync.Mutex
)

// InitProducer 初始化生产者
func InitProducer(cfg config.KafkaConfig) error {
	lock.Lock()
	defer lock.Unlock()

	// 如果已经初始化，则关闭旧的生产者
	if producer != nil {
		producer.Close()
	}

	// 创建配置
	config := sarama.NewConfig()
	config.Version = sarama.V2_8_1_0 // 匹配 CKafka 2.8.1 版本
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 3
	config.Producer.Return.Successes = true

	// 配置自动创建Topic（如果启用）
	if cfg.AutoCreateTopic {
		config.Metadata.AllowAutoTopicCreation = true
		logx.Info("启用Kafka自动创建Topic")
	}

	// 配置SASL认证（仅在提供了用户名和密码时）
	if cfg.Username != "" && cfg.Password != "" {
		config.Net.SASL.Enable = true
		config.Net.SASL.User = cfg.Username
		config.Net.SASL.Password = cfg.Password

		// 根据配置选择SASL机制
		switch cfg.SASLMechanism {
		case "SCRAM-SHA-256":
			config.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
			logx.Info("启用Kafka SASL SCRAM-SHA-256认证", zap.String("username", cfg.Username))
		case "SCRAM-SHA-512":
			config.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA512
			logx.Info("启用Kafka SASL SCRAM-SHA-512认证", zap.String("username", cfg.Username))
		case "PLAIN", "":
			config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
			logx.Info("启用Kafka SASL PLAIN认证", zap.String("username", cfg.Username))
		default:
			logx.Error("未知的SASL机制，使用PLAIN", zap.String("mechanism", cfg.SASLMechanism))
			config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		}
	} else {
		logx.Info("使用Kafka无认证模式")
	}

	// 配置TLS（如果启用）
	if cfg.EnableTLS {
		config.Net.TLS.Enable = true

		tlsConfig := &tls.Config{}

		if cfg.InsecureSkipVerify {
			tlsConfig.InsecureSkipVerify = true
			logx.Info("启用Kafka TLS连接（跳过证书验证）")
		} else {
			// 生产环境：验证证书
			if cfg.CaCertPath != "" {
				logx.Info("启用Kafka TLS连接（使用指定CA证书）", zap.String("caCertPath", cfg.CaCertPath))
			} else {
				logx.Info("启用Kafka TLS连接（使用系统CA证书）")
			}
		}

		config.Net.TLS.Config = tlsConfig
	} else {
		logx.Info("使用Kafka明文连接（无TLS）")
	}

	// 检查网络连接
	logx.Info("检查Kafka Broker连接", zap.Strings("brokers", cfg.Brokers))
	for _, broker := range cfg.Brokers {
		if err := checkBrokerConnection(broker); err != nil {
			logx.Error("Kafka Broker连接检查失败", zap.String("broker", broker), zap.Error(err))
		} else {
			logx.Info("Kafka Broker连接检查成功", zap.String("broker", broker))
		}
	}

	// 创建生产者
	var err error
	producer, err = sarama.NewSyncProducer(cfg.Brokers, config)
	if err != nil {
		logx.Error("初始化Kafka生产者失败",
			zap.Error(err),
			zap.Strings("brokers", cfg.Brokers),
			zap.String("username", cfg.Username),
			zap.Bool("sasl_enabled", cfg.Username != ""))
		return err
	}

	logx.Info("初始化Kafka生产者成功", zap.Strings("brokers", cfg.Brokers))
	return nil
}

// InitConsumer 初始化消费者
func InitConsumer(cfg config.KafkaConfig, handler sarama.ConsumerGroupHandler) error {
	lock.Lock()
	defer lock.Unlock()

	// 如果已经初始化，则关闭旧的消费者组
	if consumerGroup != nil {
		consumerGroup.Close()
	}

	// 创建配置
	config := sarama.NewConfig()
	config.Version = sarama.V2_8_1_0 // 匹配 CKafka 2.8.1 版本
	config.Consumer.Return.Errors = true
	config.Consumer.Offsets.Initial = sarama.OffsetNewest

	// 配置自动创建Topic（如果启用）
	if cfg.AutoCreateTopic {
		config.Metadata.AllowAutoTopicCreation = true
		logx.Info("消费者启用Kafka自动创建Topic")
	}

	// 配置SASL认证（仅在提供了用户名和密码时）
	if cfg.Username != "" && cfg.Password != "" {
		config.Net.SASL.Enable = true
		config.Net.SASL.User = cfg.Username
		config.Net.SASL.Password = cfg.Password

		// 根据配置选择SASL机制
		switch cfg.SASLMechanism {
		case "SCRAM-SHA-256":
			config.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
			logx.Info("消费者启用Kafka SASL SCRAM-SHA-256认证", zap.String("username", cfg.Username))
		case "SCRAM-SHA-512":
			config.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA512
			logx.Info("消费者启用Kafka SASL SCRAM-SHA-512认证", zap.String("username", cfg.Username))
		case "PLAIN", "":
			config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
			logx.Info("消费者启用Kafka SASL PLAIN认证", zap.String("username", cfg.Username))
		default:
			logx.Error("消费者未知的SASL机制，使用PLAIN", zap.String("mechanism", cfg.SASLMechanism))
			config.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		}
	} else {
		logx.Info("消费者使用Kafka无认证模式")
	}

	// 配置TLS（如果启用）
	if cfg.EnableTLS {
		config.Net.TLS.Enable = true

		tlsConfig := &tls.Config{}

		if cfg.InsecureSkipVerify {
			tlsConfig.InsecureSkipVerify = true
			logx.Info("消费者启用Kafka TLS连接（跳过证书验证）")
		} else {
			// 生产环境：验证证书
			if cfg.CaCertPath != "" {
				logx.Info("消费者启用Kafka TLS连接（使用指定CA证书）", zap.String("caCertPath", cfg.CaCertPath))
			} else {
				logx.Info("消费者启用Kafka TLS连接（使用系统CA证书）")
			}
		}

		config.Net.TLS.Config = tlsConfig
	} else {
		logx.Info("消费者使用Kafka明文连接（无TLS）")
	}

	// 创建消费者组
	var err error
	consumerGroup, err = sarama.NewConsumerGroup(cfg.Brokers, cfg.Group, config)
	if err != nil {
		logx.Error("初始化Kafka消费者失败", zap.Error(err))
		return err
	}

	// 启动消费者
	ctx := context.Background()
	go func() {
		for {
			err := consumerGroup.Consume(ctx, []string{cfg.Topic}, handler)
			if err != nil {
				logx.Error("Kafka消费者错误", zap.Error(err))
			}
			// 检查上下文是否已取消
			if ctx.Err() != nil {
				return
			}
		}
	}()

	logx.Info("初始化Kafka消费者成功",
		zap.Strings("brokers", cfg.Brokers),
		zap.String("topic", cfg.Topic),
		zap.String("group", cfg.Group))
	return nil
}

// SendMessage 发送消息
func SendMessage(topic string, key, value []byte) (int32, int64, error) {
	if producer == nil {
		return 0, 0, fmt.Errorf("Kafka生产者未初始化")
	}

	msg := &sarama.ProducerMessage{
		Topic: topic,
		Key:   sarama.ByteEncoder(key),
		Value: sarama.ByteEncoder(value),
	}

	return producer.SendMessage(msg)
}

// IsProducerInitialized 检查生产者是否已初始化
func IsProducerInitialized() bool {
	return producer != nil
}

// Close 关闭连接
func Close() {
	lock.Lock()
	defer lock.Unlock()

	if producer != nil {
		producer.Close()
	}

	if consumerGroup != nil {
		consumerGroup.Close()
	}
}

// checkBrokerConnection 检查Broker连接
func checkBrokerConnection(broker string) error {
	// 使用net包检查TCP连接
	conn, err := net.DialTimeout("tcp", broker, 5*time.Second)
	if err != nil {
		return fmt.Errorf("无法连接到Kafka Broker %s: %v", broker, err)
	}
	defer conn.Close()
	return nil
}
