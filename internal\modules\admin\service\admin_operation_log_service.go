package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"yekaitai/internal/modules/admin/model"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/infra/redis"
)

// AdminOperationLogListRequest 操作日志列表请求
type AdminOperationLogListRequest struct {
	types.PageRequest
	AdminID   *uint  `form:"admin_id,optional" label:"操作人ID"`
	Module    string `form:"module,optional" label:"操作模块"`
	StartTime string `form:"start_time,optional" label:"开始时间,格式:2006-01-02"`
	EndTime   string `form:"end_time,optional" label:"结束时间,格式:2006-01-02"`
}

// AdminOperationLogResponse 操作日志响应
type AdminOperationLogResponse struct {
	*model.AdminOperationLog
	AdminName string `json:"admin_name"` // 操作人姓名
}

// AdminUserCache 管理员缓存结构
type AdminUserCache struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
}

// AdminOperationLogService 操作日志服务
type AdminOperationLogService struct {
	repo model.AdminOperationLogRepository
}

// NewAdminOperationLogService 创建操作日志服务实例
func NewAdminOperationLogService() *AdminOperationLogService {
	db := mysql.GetDB()
	return &AdminOperationLogService{
		repo: model.NewAdminOperationLogRepository(db),
	}
}

// CreateLog 创建操作日志
func (s *AdminOperationLogService) CreateLog(ctx context.Context, log *model.AdminOperationLog) error {
	return s.repo.Create(log)
}

// parseDate 解析日期字符串，支持2006-01-02格式
func (s *AdminOperationLogService) parseDate(dateStr string, isEndTime bool) (*time.Time, error) {
	if dateStr == "" {
		return nil, nil
	}

	// 解析 2006-01-02 格式
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return nil, fmt.Errorf("日期格式错误，请使用 2006-01-02 格式: %v", err)
	}

	// 如果是结束时间，设置为当天的23:59:59
	if isEndTime {
		date = date.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
	}

	return &date, nil
}

// getAdminUserMap 获取所有管理员用户映射，优先从Redis缓存获取
func (s *AdminOperationLogService) getAdminUserMap(ctx context.Context) (map[uint]string, error) {
	const adminUserCacheKey = "admin_user_cache"
	const adminUserCacheExpiration = 5 * time.Minute

	redisCli := redis.GetClient()

	// 尝试从Redis缓存获取
	cachedData, err := redisCli.Get(ctx, adminUserCacheKey).Result()
	if err == nil && cachedData != "" {
		var adminUsers []AdminUserCache
		if jsonErr := json.Unmarshal([]byte(cachedData), &adminUsers); jsonErr == nil {
			adminNameMap := make(map[uint]string, len(adminUsers))
			for _, admin := range adminUsers {
				adminNameMap[admin.ID] = admin.Username
			}
			return adminNameMap, nil
		}
	}

	// 缓存不存在或过期，从数据库查询所有管理员
	var adminUsers []AdminUserCache
	if err := mysql.GetDB().Table("admin_user").
		Select("admin_id as id, username").
		Where("is_deleted = ?", false).
		Find(&adminUsers).Error; err != nil {
		return nil, fmt.Errorf("查询管理员用户失败: %v", err)
	}

	// 构建映射
	adminNameMap := make(map[uint]string, len(adminUsers))
	for _, admin := range adminUsers {
		adminNameMap[admin.ID] = admin.Username
	}

	// 将结果缓存到Redis
	if jsonData, jsonErr := json.Marshal(adminUsers); jsonErr == nil {
		_ = redisCli.Set(ctx, adminUserCacheKey, string(jsonData), adminUserCacheExpiration).Err()
	}

	return adminNameMap, nil
}

// GetOperationLogList 获取操作日志列表
func (s *AdminOperationLogService) GetOperationLogList(ctx context.Context, req *AdminOperationLogListRequest) ([]*AdminOperationLogResponse, int64, error) {
	// 解析时间参数
	startTime, err := s.parseDate(req.StartTime, false)
	if err != nil {
		return nil, 0, err
	}

	endTime, err := s.parseDate(req.EndTime, true)
	if err != nil {
		return nil, 0, err
	}

	logs, total, err := s.repo.ListWithFilter(req.Page, req.Size, req.AdminID, startTime, endTime, req.Module)
	if err != nil {
		return nil, 0, err
	}

	// 获取所有管理员用户映射
	adminNameMap, err := s.getAdminUserMap(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("获取管理员信息失败: %v", err)
	}

	// 构建响应
	responses := make([]*AdminOperationLogResponse, 0, len(logs))
	for _, log := range logs {
		adminName := adminNameMap[log.AdminID]
		if adminName == "" {
			adminName = fmt.Sprintf("ID:%d", log.AdminID) // 显示ID而不是"未知用户"
		}

		response := &AdminOperationLogResponse{
			AdminOperationLog: log,
			AdminName:         adminName,
		}
		responses = append(responses, response)
	}

	return responses, total, nil
}
