package handler

import (
	"net/http"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/internal/modules/goods/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type ShippingGoZeroHandler struct {
	shippingService *service.ShippingService
}

func NewShippingGoZeroHandler() *ShippingGoZeroHandler {
	return &ShippingGoZeroHandler{
		shippingService: service.NewShippingService(),
	}
}

// SetShippingConfig 设置运费配置
func (h *ShippingGoZeroHandler) SetShippingConfig(w http.ResponseWriter, r *http.Request) {
	var req model.ShippingConfigRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	config, err := h.shippingService.SetShippingConfig(r.Context(), &req)
	if err != nil {
		logx.Errorf("设置运费配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(config))
}

// GetShippingConfig 获取运费配置
func (h *ShippingGoZeroHandler) GetShippingConfig(w http.ResponseWriter, r *http.Request) {
	config, err := h.shippingService.GetShippingConfig(r.Context())
	if err != nil {
		logx.Errorf("获取运费配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(config))
}

// CalculateShippingFee 计算运费
func (h *ShippingGoZeroHandler) CalculateShippingFee(w http.ResponseWriter, r *http.Request) {
	var req model.ShippingCalculateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	result, err := h.shippingService.CalculateShippingFee(r.Context(), &req)
	if err != nil {
		logx.Errorf("计算运费失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}
