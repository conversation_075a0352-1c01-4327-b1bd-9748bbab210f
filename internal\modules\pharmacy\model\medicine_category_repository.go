package model

import (
	"yekaitai/pkg/infra/mysql"
)

// Create 创建药品分类
func (r *medicineCategoryRepository) Create(category *MedicineCategory) error {
	return mysql.Master().Create(category).Error
}

// Update 更新药品分类
func (r *medicineCategoryRepository) Update(category *MedicineCategory) error {
	return mysql.Master().Save(category).Error
}

// Delete 删除药品分类
func (r *medicineCategoryRepository) Delete(id uint) error {
	return mysql.Master().Delete(&MedicineCategory{}, id).Error
}

// FindByID 根据ID查找药品分类
func (r *medicineCategoryRepository) FindByID(id uint) (*MedicineCategory, error) {
	var category MedicineCategory
	err := mysql.Slave().Where("id = ?", id).First(&category).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// List 获取药品分类列表
func (r *medicineCategoryRepository) List(page, size int) ([]*MedicineCategory, int64, error) {
	var categories []*MedicineCategory
	var total int64

	db := mysql.Slave()

	// 获取总数
	if err := db.Model(&MedicineCategory{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("sort DESC").Offset(offset).Limit(size).Find(&categories).Error; err != nil {
		return nil, 0, err
	}

	return categories, total, nil
}

// ListAll 获取所有药品分类
func (r *medicineCategoryRepository) ListAll() ([]*MedicineCategory, error) {
	var categories []*MedicineCategory
	err := mysql.Slave().Order("sort DESC").Find(&categories).Error
	if err != nil {
		return nil, err
	}
	return categories, nil
}

// ListByParentID 根据父级ID获取药品分类列表
func (r *medicineCategoryRepository) ListByParentID(parentID uint) ([]*MedicineCategory, error) {
	var categories []*MedicineCategory
	err := mysql.Slave().Where("parent_id = ?", parentID).Order("sort DESC").Find(&categories).Error
	if err != nil {
		return nil, err
	}
	return categories, nil
}

// UpdateStatus 更新药品分类状态
func (r *medicineCategoryRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&MedicineCategory{}).Where("id = ?", id).Update("status", status).Error
}
