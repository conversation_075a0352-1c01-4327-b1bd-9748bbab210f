package handler

import (
	"net/http"
	"strconv"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/admin/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 获取菜单请求结构
type GetMenusRequest struct {
	ParentId string `form:"parentId,optional"`
}

// 获取所有菜单处理函数
func GetAllMenusHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req GetMenusRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 创建RBAC仓库实例
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())

		var menus interface{}
		var err error

		// 检查是否需要只获取指定父级ID的菜单
		if req.ParentId != "" {
			parentId, err := strconv.ParseUint(req.ParentId, 10, 64)
			if err != nil {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的父级ID"))
				return
			}
			// 获取指定父级ID的菜单
			menus, err = rbacRepo.GetMenusByParentID(uint(parentId))
		} else {
			// 获取所有菜单
			menus, err = rbacRepo.GetMenus()
		}

		if err != nil {
			logx.Errorf("获取菜单失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取菜单失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(menus, "获取成功"))
	}
}

// 获取角色菜单请求结构
type RoleMenusRequest struct {
	RoleID string `path:"roleId"`
}

// 获取角色可访问菜单处理函数
func GetRoleMenusHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req RoleMenusRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.RoleID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "角色ID不能为空"))
			return
		}

		roleID, err := strconv.ParseUint(req.RoleID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的角色ID"))
			return
		}

		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		menus, err := rbacRepo.GetMenusByRole(uint(roleID))
		if err != nil {
			logx.Errorf("获取角色菜单失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取菜单失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(menus, "获取成功"))
	}
}

// 获取当前管理员可访问菜单处理函数
func GetCurrentAdminMenusHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从上下文中获取管理员ID
		adminIDStr := middleware.FromContext(r.Context(), "admin_id")
		logx.Infof("获取当前管理员菜单 - 当前管理员ID: %s", adminIDStr)

		if adminIDStr == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权"))
			return
		}

		adminID, err := strconv.ParseUint(adminIDStr, 10, 64)
		if err != nil {
			logx.Errorf("解析管理员ID失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的管理员ID"))
			return
		}

		// 创建RBAC仓库实例
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())

		// 获取管理员可访问的菜单
		menus, err := rbacRepo.GetMenusByAdmin(uint(adminID))
		if err != nil {
			logx.Errorf("获取管理员菜单失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取菜单失败"))
			return
		}

		logx.Infof("获取当前管理员菜单成功, 管理员ID: %d, 菜单数量: %d", adminID, len(menus))
		httpx.OkJson(w, types.NewSuccessResponse(menus, "获取成功"))
	}
}

// 获取指定管理员菜单请求结构
type AdminMenusRequest struct {
	AdminID string `path:"adminId"`
}

// 获取指定管理员可访问菜单处理函数
func GetAdminMenusHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req AdminMenusRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.AdminID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "管理员ID不能为空"))
			return
		}

		adminID, err := strconv.ParseUint(req.AdminID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的管理员ID"))
			return
		}

		// 创建RBAC仓库实例
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())

		// 获取管理员可访问的菜单
		menus, err := rbacRepo.GetMenusByAdmin(uint(adminID))
		if err != nil {
			logx.Errorf("获取管理员菜单失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取菜单失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(menus, "获取成功"))
	}
}

// 为角色分配菜单请求
type AssignMenusRequest struct {
	RoleID  string `path:"roleId"`
	MenuIDs []uint `json:"menuIds"`
}

// 为角色分配菜单处理函数
func AssignMenusToRoleHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req AssignMenusRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.RoleID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "角色ID不能为空"))
			return
		}

		roleID, err := strconv.ParseUint(req.RoleID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的角色ID"))
			return
		}

		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		// 分配菜单实际上是为角色分配菜单关联的权限
		if err := rbacRepo.AssignMenusToRole(uint(roleID), req.MenuIDs); err != nil {
			logx.Errorf("分配角色菜单失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "分配菜单失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(nil, "分配成功"))
	}
}

// 获取菜单详情请求结构
type MenuDetailRequest struct {
	MenuID string `path:"menuId"`
}

// 获取菜单及其权限详情处理函数
func GetMenuByIDHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req MenuDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.MenuID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "菜单ID不能为空"))
			return
		}

		menuID, err := strconv.ParseUint(req.MenuID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的菜单ID"))
			return
		}

		// 创建RBAC仓库实例
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())

		// 获取菜单及其对应的权限信息
		menuInfo, err := rbacRepo.GetMenuByID(uint(menuID))
		if err != nil {
			logx.Errorf("获取菜单详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取菜单详情失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(menuInfo, "获取成功"))
	}
}
