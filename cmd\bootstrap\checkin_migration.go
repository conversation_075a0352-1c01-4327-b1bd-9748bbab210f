package bootstrap

import (
	"time"

	"yekaitai/pkg/infra/mysql"
	checkinModel "yekaitai/wx_internal/modules/checkin/model"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateCheckinTables 迁移签到相关表
func MigrateCheckinTables() error {
	logx.Info("开始迁移签到相关表...")

	// 获取数据库连接
	db := mysql.Master()

	// 创建签到记录表
	if err := db.AutoMigrate(&checkinModel.CheckinRecord{}); err != nil {
		logx.Errorf("创建签到记录表失败: %v", err)
		return err
	}

	// 索引已在模型中定义，无需手动创建

	// 添加签到相关的叶小币规则（如果不存在）
	checkinRules := []map[string]interface{}{
		{
			"user_level_id": 1,
			"rule_type":     "CHECKIN",
			"rule_name":     "每日签到奖励",
			"description":   "每日签到获得叶小币奖励",
			"enabled":       true,
			"coins_awarded": 1,
			"is_one_time":   false,
			"require_share": false,
		},
		{
			"user_level_id": 1,
			"rule_type":     "ACTIVITY",
			"rule_name":     "分享签到奖励",
			"description":   "分享签到获得额外叶小币奖励",
			"enabled":       true,
			"coins_awarded": 1,
			"is_one_time":   true,
			"activity_type": "SHARE_CHECKIN",
		},
	}

	for _, rule := range checkinRules {
		// 检查规则是否已存在
		var count int64
		db.Table("coin_rules").Where("user_level_id = ? AND rule_type = ? AND rule_name = ?",
			rule["user_level_id"], rule["rule_type"], rule["rule_name"]).Count(&count)

		if count == 0 {
			rule["created_at"] = time.Now()
			rule["updated_at"] = time.Now()

			if err := db.Table("coin_rules").Create(rule).Error; err != nil {
				logx.Errorf("创建签到奖励规则失败: %v", err)
				return err
			}
			logx.Infof("创建签到奖励规则成功: %s", rule["rule_name"])
		}
	}

	// 为其他用户等级添加签到规则
	var userLevels []struct {
		ID uint `json:"id"`
	}
	db.Table("user_level_rules").Where("id > 1").Find(&userLevels)

	for _, level := range userLevels {
		for _, baseRule := range checkinRules {
			// 检查规则是否已存在
			var count int64
			db.Table("coin_rules").Where("user_level_id = ? AND rule_type = ? AND rule_name = ?",
				level.ID, baseRule["rule_type"], baseRule["rule_name"]).Count(&count)

			if count == 0 {
				rule := make(map[string]interface{})
				for k, v := range baseRule {
					rule[k] = v
				}
				rule["user_level_id"] = level.ID
				rule["created_at"] = time.Now()
				rule["updated_at"] = time.Now()

				if err := db.Table("coin_rules").Create(rule).Error; err != nil {
					logx.Errorf("为用户等级%d创建签到奖励规则失败: %v", level.ID, err)
				} else {
					logx.Infof("为用户等级%d创建签到奖励规则成功: %s", level.ID, rule["rule_name"])
				}
			}
		}
	}

	logx.Info("签到相关表迁移完成")
	return nil
}
