package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/sms_setup/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterSmsSetupRoutes 注册短信设置相关路由
func RegisterSmsSetupRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建短信模板处理器
	smsTemplateHandler := handler.NewSmsTemplateHandler()

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 注册路由
	server.AddRoutes(
		[]rest.Route{
			// 短信模板管理
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/sms-templates",
				Handler: adminAuthWrapper(smsTemplateHandler.CreateTemplate),
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/sms-templates",
				Handler: adminAuthWrapper(smsTemplateHandler.UpdateTemplate),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/sms-templates",
				Handler: adminAuthWrapper(smsTemplateHandler.DeleteTemplate),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/sms-templates/batch-delete",
				Handler: adminAuthWrapper(smsTemplateHandler.BatchDeleteTemplates),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/sms-templates/detail",
				Handler: adminAuthWrapper(smsTemplateHandler.GetTemplate),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/sms-templates",
				Handler: adminAuthWrapper(smsTemplateHandler.ListTemplates),
			},
		},
	)
}
