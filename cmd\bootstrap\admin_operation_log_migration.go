package bootstrap

import (
	"yekaitai/internal/modules/admin/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateAdminOperationLog 迁移管理员操作日志表
func MigrateAdminOperationLog() error {
	db := mysql.GetDB()
	if db == nil {
		logx.Error("数据库连接为空")
		return nil
	}

	logx.Info("开始迁移管理员操作日志表...")

	// 检查表是否存在
	if !db.Migrator().HasTable(&model.AdminOperationLog{}) {
		logx.Info("admin_operation_log 表不存在，创建表...")
		if err := db.AutoMigrate(&model.AdminOperationLog{}); err != nil {
			logx.Errorf("创建 admin_operation_log 表失败: %v", err)
			return err
		}
		logx.Info("成功创建 admin_operation_log 表")
		return nil
	}

	// 检查并添加新字段
	logx.Info("检查 admin_operation_log 表结构...")

	// 检查 duration 字段
	if !db.Migrator().HasColumn(&model.AdminOperationLog{}, "duration") {
		logx.Info("添加 duration 字段...")
		if err := db.Migrator().AddColumn(&model.AdminOperationLog{}, "duration"); err != nil {
			logx.Errorf("添加 duration 字段失败: %v", err)
			return err
		}
		logx.Info("成功添加 duration 字段")
	} else {
		logx.Info("duration 字段已存在")
	}

	// 检查 status_code 字段
	if !db.Migrator().HasColumn(&model.AdminOperationLog{}, "status_code") {
		logx.Info("添加 status_code 字段...")
		if err := db.Migrator().AddColumn(&model.AdminOperationLog{}, "status_code"); err != nil {
			logx.Errorf("添加 status_code 字段失败: %v", err)
			return err
		}
		logx.Info("成功添加 status_code 字段")
	} else {
		logx.Info("status_code 字段已存在")
	}

	// 确保表结构是最新的
	if err := db.AutoMigrate(&model.AdminOperationLog{}); err != nil {
		logx.Errorf("更新 admin_operation_log 表结构失败: %v", err)
		return err
	}

	logx.Info("管理员操作日志表迁移完成")
	return nil
}
