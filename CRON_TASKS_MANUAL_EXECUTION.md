# 定时任务手动执行功能

## 概述

在 main.go 中新增了 `run_cron_tasks` 功能，允许手动执行所有定时任务一次，方便测试和维护。

## 使用方法

### 命令格式
```bash
./main -f <配置文件> -tool_task run_cron_tasks
```

### 使用示例
```bash
# 开发环境
./main -f etc/yekaitai-dev.yaml -tool_task run_cron_tasks

# 测试环境
./main -f etc/yekaitai-test.yaml -tool_task run_cron_tasks

# 生产环境
./main -f etc/yekaitai-prod.yaml -tool_task run_cron_tasks
```

## 执行的任务列表

该命令会按顺序执行以下所有定时任务：

### 1. 基础数据同步任务
- **门诊收费信息同步**: 同步HIS系统的门诊收费数据
- **医生数据同步**: 同步HIS系统的医生信息
- **患者数据同步**: 同步HIS系统的患者信息

### 2. ABC云同步任务（如果配置了ABC云客户端）
- **ABC云门店同步**: 同步ABC云系统的门店信息
- **ABC云科室同步**: 同步ABC云系统的科室信息
- **ABC云医生同步**: 同步ABC云系统的医生信息
- **ABC云患者同步**: 同步ABC云系统的患者信息

### 3. 万里牛ERP同步任务
- **万里牛商品分类同步**: 同步商品分类数据
- **万里牛商品同步**: 同步商品基础信息
- **万里牛库存同步**: 同步商品库存数据
- **万里牛发货状态同步**: 同步订单发货状态
- **万里牛失败订单推送**: 重新推送失败的订单

### 4. 业务处理任务
- **优惠券发放任务处理**: 处理异步优惠券发放任务
- **订单自动关闭**: 关闭超时未支付订单
- **用户等级升级**: 处理用户等级升级检查

## 执行特点

### 1. 顺序执行
- 所有任务按照预定义的顺序依次执行
- 不会并发执行，避免资源冲突

### 2. 错误处理
- 单个任务失败不会影响其他任务的执行
- 会记录所有失败的任务和错误信息
- 最终返回汇总的执行结果

### 3. 详细日志
- 每个任务的开始和完成都有日志记录
- 失败的任务会记录详细的错误信息
- 便于问题排查和监控

### 4. 配置依赖
- 只有配置了相应客户端的任务才会执行
- 例如：ABC云任务需要配置ABC云客户端
- 万里牛任务需要配置万里牛ERP客户端

## 使用场景

### 1. 系统维护
- 在系统维护期间手动执行所有同步任务
- 确保数据的完整性和一致性

### 2. 故障恢复
- 当定时任务服务出现问题时，手动执行补偿
- 快速恢复数据同步状态

### 3. 测试验证
- 在部署新版本后验证所有同步功能
- 测试各个第三方系统的连接状态

### 4. 数据初始化
- 在新环境部署后进行初始数据同步
- 快速建立完整的数据基础

## 执行时间估算

根据数据量和网络状况，完整执行所有任务大约需要：

- **小数据量**: 5-10分钟
- **中等数据量**: 15-30分钟
- **大数据量**: 30-60分钟

具体时间取决于：
- 各个第三方系统的响应速度
- 需要同步的数据量
- 网络连接质量
- 服务器性能

## 注意事项

### 1. 执行环境
- 确保配置文件正确且可访问
- 确保数据库连接正常
- 确保第三方系统API可访问

### 2. 资源消耗
- 执行期间会消耗较多CPU和内存资源
- 建议在业务低峰期执行
- 避免与正常的定时任务冲突

### 3. 数据一致性
- 执行期间避免手动修改相关数据
- 建议在执行前备份重要数据
- 确保没有其他同步任务在运行

### 4. 监控告警
- 执行期间关注系统资源使用情况
- 监控第三方系统的API调用频率
- 注意数据库连接数和事务状态

## 错误排查

### 1. 配置问题
```bash
# 检查配置文件是否存在
ls -la etc/yekaitai-dev.yaml

# 检查配置文件格式
cat etc/yekaitai-dev.yaml | head -20
```

### 2. 网络连接问题
```bash
# 检查数据库连接
telnet <数据库IP> 3306

# 检查第三方API连接
curl -I <API_URL>
```

### 3. 权限问题
```bash
# 检查程序执行权限
ls -la main

# 检查日志目录权限
ls -la logs/
```

## 日志示例

### 成功执行的日志
```
2024-01-01 10:00:00 INFO 开始手动执行所有定时任务...
2024-01-01 10:00:01 INFO 手动执行：门诊收费信息同步...
2024-01-01 10:00:05 INFO 手动执行：门诊收费信息同步完成
2024-01-01 10:00:05 INFO 手动执行：医生数据同步...
2024-01-01 10:00:10 INFO 手动执行：医生数据同步完成
...
2024-01-01 10:15:30 INFO 所有定时任务手动执行成功完成
```

### 部分失败的日志
```
2024-01-01 10:00:00 INFO 开始手动执行所有定时任务...
...
2024-01-01 10:05:00 ERROR ABC云门店同步失败: connection timeout
...
2024-01-01 10:15:30 ERROR 手动执行定时任务完成，但有 1 个任务失败
2024-01-01 10:15:30 ERROR 错误 1: ABC云门店同步失败: connection timeout
```

## 与定时任务的关系

### 1. 独立执行
- 手动执行与自动定时任务完全独立
- 不会影响正常的定时任务调度
- 可以在定时任务运行期间执行

### 2. 相同逻辑
- 使用与定时任务完全相同的业务逻辑
- 确保手动执行和自动执行的一致性
- 便于测试和验证定时任务功能

### 3. 补偿机制
- 可以作为定时任务失败时的补偿手段
- 在定时任务服务异常时提供备用方案
- 支持灵活的维护和故障恢复

## 总结

`run_cron_tasks` 功能提供了一个便捷的方式来手动执行所有定时任务，特别适用于：

- ✅ 系统维护和故障恢复
- ✅ 新环境部署后的数据初始化
- ✅ 定时任务功能的测试验证
- ✅ 数据同步状态的快速恢复

通过统一的命令接口，管理员可以轻松地触发所有同步任务的执行，确保系统数据的完整性和一致性。
