package types

// 通用分页请求
type PageRequest struct {
	Page  int    `form:"page,optional,default=1"`  // 页码，默认第1页
	Size  int    `form:"size,optional,default=10"` // 每页条数，默认10条
	Query string `form:"query,optional"`           // 查询关键字，可选
}

// GetOffset 获取分页偏移量
func (p *PageRequest) GetOffset() int {
	if p.Page < 1 {
		p.Page = 1
	}
	if p.Size < 1 {
		p.Size = 10
	}
	return (p.Page - 1) * p.Size
}

// GetLimit 获取分页大小
func (p *PageRequest) GetLimit() int {
	if p.Size < 1 {
		return 10
	}
	return p.Size
}

// 分页响应结果
type PageResponse struct {
	List  interface{} `json:"list"`  // 分页数据列表
	Total int64       `json:"total"` // 总记录数
	Page  int         `json:"page"`  // 当前页码
	Size  int         `json:"size"`  // 每页大小
}

// NewPageResponse 创建分页响应结果
func NewPageResponse(list interface{}, total int64, req *PageRequest) *PageResponse {
	return &PageResponse{
		List:  list,
		Total: total,
		Page:  req.Page,
		Size:  req.Size,
	}
}
