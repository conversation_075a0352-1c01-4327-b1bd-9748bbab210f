package middleware

import (
	"net/http"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
)

// CorsMiddleware 跨域中间件
func CorsMiddleware() rest.Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 获取请求的Origin
			origin := r.Header.Get("Origin")
			if origin == "" {
				origin = "*"
			}

			logx.Infof("处理跨域请求: Method=%s, Path=%s, Origin=%s", r.Method, r.URL.Path, origin)

			// 根据请求来源设置CORS响应头
			w.Header().Set("Access-Control-Allow-Origin", origin)
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-CSRF-Token, X-Token, X-Api-Key, X-Auth-Token")
			w.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
			w.Header().Set("Access-Control-Allow-Credentials", "true")
			w.Header().Set("Access-Control-Max-Age", "86400") // 24小时

			// 显式处理OPTIONS请求并立即终止
			if r.Method == http.MethodOptions {
				logx.Info("拦截并处理OPTIONS预检请求")
				w.WriteHeader(http.StatusOK)
				return // 关键：不再调用next()
			}

			next(w, r)
		}
	}
}

// OptionsHandler 全局OPTIONS请求处理器
func OptionsHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 获取请求的Origin
		origin := r.Header.Get("Origin")
		if origin == "" {
			origin = "*"
		}

		logx.Infof("OPTIONS处理器: Path=%s, Origin=%s", r.URL.Path, origin)

		// 设置CORS响应头
		w.Header().Set("Access-Control-Allow-Origin", origin)
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-CSRF-Token, X-Token, X-Api-Key, X-Auth-Token")
		w.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		w.Header().Set("Access-Control-Max-Age", "86400") // 24小时

		w.WriteHeader(http.StatusOK)
	}
}

// LogMiddleware 日志中间件
func LogMiddleware() rest.Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		// 这里可以添加日志记录逻辑
		return func(w http.ResponseWriter, r *http.Request) {
			next(w, r)
		}
	}
}

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	svcCtx *svc.ServiceContext
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(svcCtx *svc.ServiceContext) *AuthMiddleware {
	return &AuthMiddleware{
		svcCtx: svcCtx,
	}
}

// Handle 处理认证
func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 这里简单实现，实际项目中应当验证token
		next(w, r)
	}
}

// OptionsInterceptor OPTIONS请求拦截器中间件 - 确保OPTIONS请求永远不会进入路由
func OptionsInterceptor() rest.Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 如果是OPTIONS请求，拦截并直接处理
			if r.Method == http.MethodOptions {
				origin := r.Header.Get("Origin")
				if origin == "" {
					origin = "*"
				}

				logx.Infof("[OptionsInterceptor] 强制拦截OPTIONS请求: Path=%s, Origin=%s", r.URL.Path, origin)

				// 设置CORS头
				w.Header().Set("Access-Control-Allow-Origin", origin)
				w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
				w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-CSRF-Token, X-Token, X-Api-Key, X-Auth-Token")
				w.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
				w.Header().Set("Access-Control-Allow-Credentials", "true")
				w.Header().Set("Access-Control-Max-Age", "86400") // 24小时

				// 1. 清除任何可能存在的状态码
				if w.Header().Get("Status") != "" {
					w.Header().Del("Status")
				}

				// 2. 覆盖或设置Allow头
				w.Header().Set("Allow", "GET, POST, PUT, DELETE, OPTIONS, PATCH")

				// 3. 确保写入正确的状态码
				// 注意：WriteHeader必须在最后调用，之后不能再设置头信息
				w.WriteHeader(http.StatusOK)

				// 4. 写入一个空响应体，确保请求完成
				w.Write([]byte(""))

				return // 不再执行后续中间件和路由处理
			}

			// 对于非OPTIONS请求，继续正常处理
			next(w, r)
		}
	}
}

// NotAllowedHandler 处理405错误的处理器，主要用于处理OPTIONS预检请求
func NotAllowedHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 专门处理OPTIONS预检请求
		if r.Method == http.MethodOptions {
			logx.Infof("NotAllowedHandler处理OPTIONS预检请求: %s", r.URL.Path)

			// 获取请求的Origin
			origin := r.Header.Get("Origin")
			if origin == "" {
				origin = "*"
			}

			// 设置CORS头
			w.Header().Set("Access-Control-Allow-Origin", origin)
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-CSRF-Token, X-Token, X-Api-Key, X-Auth-Token")
			w.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
			w.Header().Set("Access-Control-Allow-Credentials", "true")
			w.Header().Set("Access-Control-Max-Age", "86400") // 24小时

			// 覆盖Allow头，避免框架自动生成的只含已注册方法的Allow头
			w.Header().Set("Allow", "GET, POST, PUT, DELETE, OPTIONS, PATCH")

			// 返回成功状态
			w.WriteHeader(http.StatusOK)
			return
		}

		// 非OPTIONS请求，返回普通的405错误
		logx.Infof("方法不允许: %s %s", r.Method, r.URL.Path)
		w.WriteHeader(http.StatusMethodNotAllowed)
	}
}

// MiddlewareManager 中间件管理器
type MiddlewareManager struct {
	operationLogMiddleware *OperationLogMiddleware
}

// NewMiddlewareManager 创建中间件管理器
func NewMiddlewareManager() *MiddlewareManager {
	return &MiddlewareManager{
		operationLogMiddleware: NewOperationLogMiddleware(),
	}
}

// ApplyAdminMiddlewares 应用后台管理中间件
func (m *MiddlewareManager) ApplyAdminMiddlewares(handler http.HandlerFunc) http.HandlerFunc {
	// 应用操作日志中间件
	logx.Infof("中间件管理器: 应用操作日志中间件")
	handler = m.operationLogMiddleware.Handle(handler)

	// 可以在这里添加其他中间件
	// handler = m.authMiddleware.Handle(handler)
	// handler = m.rateLimitMiddleware.Handle(handler)

	return handler
}

// WrapAdminHandler 包装后台管理处理器
func (m *MiddlewareManager) WrapAdminHandler(handler http.HandlerFunc) http.HandlerFunc {
	logx.Infof("中间件管理器: 包装后台管理处理器")
	return m.ApplyAdminMiddlewares(handler)
}

// RegisterAdminRoutes 注册后台管理路由（带中间件）
func (m *MiddlewareManager) RegisterAdminRoutes(server *rest.Server, routes []rest.Route) {
	// 为所有后台路由应用中间件
	wrappedRoutes := make([]rest.Route, len(routes))
	for i, route := range routes {
		wrappedRoutes[i] = rest.Route{
			Method:  route.Method,
			Path:    route.Path,
			Handler: m.WrapAdminHandler(route.Handler),
		}
	}

	server.AddRoutes(wrappedRoutes)
}
