package handler

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"

	adminModel "yekaitai/internal/modules/admin/model"
	"yekaitai/internal/modules/area/model"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// AreaHandler 地区管理处理器
type AreaHandler struct {
	db         *gorm.DB
	areaRepo   model.EnabledAreaRepository
	regionRepo adminModel.RegionRepository // 引用admin模块的region仓库
}

// NewAreaHandler 创建地区管理处理器
func NewAreaHandler(db *gorm.DB) *AreaHandler {
	return &AreaHandler{
		db:         db,
		areaRepo:   model.NewEnabledAreaRepository(db),
		regionRepo: adminModel.NewRegionRepository(db),
	}
}

// 分页请求结构
type PageRequest struct {
	Page  int    `json:"page"`  // 页码
	Size  int    `json:"size"`  // 每页大小
	Query string `json:"query"` // 查询关键字
}

// 新增地区请求结构
type AddAreaRequest struct {
	Code string `json:"code"` // 地区编码
}

// GetEnabledAreaList 获取已开通地区列表
func (h *AreaHandler) GetEnabledAreaList(w http.ResponseWriter, r *http.Request) {
	var req PageRequest

	// 获取URL查询参数
	q := r.URL.Query()
	page, _ := strconv.Atoi(q.Get("page"))
	size, _ := strconv.Atoi(q.Get("size"))
	query := q.Get("query")

	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 20
	}

	req.Page = page
	req.Size = size
	req.Query = query

	// 获取已开通地区列表
	areas, total, err := h.areaRepo.List(req.Page, req.Size, req.Query)
	if err != nil {
		logx.Errorf("获取已开通地区列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取已开通地区列表失败"))
		return
	}

	// 返回分页结果
	result := &model.EnabledAreaList{
		Total: total,
		List:  areas,
	}
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取已开通地区列表成功"))
}

// AddEnabledArea 新增已开通地区
func (h *AreaHandler) AddEnabledArea(w http.ResponseWriter, r *http.Request) {
	// 获取当前管理员ID
	adminIDStr, ok := r.Context().Value("admin_id").(string)
	if !ok || adminIDStr == "" {
		logx.Errorf("获取管理员ID失败，Context中的admin_id类型错误或为空")
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未登录或登录已过期"))
		return
	}

	adminID, err := strconv.ParseUint(adminIDStr, 10, 64)
	if err != nil {
		logx.Errorf("解析管理员ID失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未登录或登录已过期"))
		return
	}

	// 解析请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logx.Errorf("读取请求体失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}
	defer r.Body.Close()

	var req AddAreaRequest
	if err := json.Unmarshal(body, &req); err != nil {
		logx.Errorf("解析请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	if req.Code == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "地区编码不能为空"))
		return
	}

	// 查找原始地区信息
	region, err := h.regionRepo.FindByCode(req.Code)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "地区不存在"))
			return
		}
		logx.Errorf("查询地区失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询地区失败"))
		return
	}

	// 检查地区级别是否在1-3级之间
	if region.Level < 1 || region.Level > 3 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "只能添加省市区三级地区"))
		return
	}

	// 检查是否已开通
	exists, err := h.areaRepo.IsEnabled(req.Code)
	if err != nil && err != gorm.ErrRecordNotFound {
		logx.Errorf("检查地区状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "检查地区状态失败"))
		return
	}

	if exists {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeDuplicateRecord, "该地区已开通"))
		return
	}

	// 创建已开通地区
	enabledArea := &model.EnabledArea{
		Code:       region.Code,
		Name:       region.Name,
		ParentCode: region.ParentCode,
		Level:      region.Level,
		CreatedBy:  uint(adminID),
	}

	if err := h.areaRepo.Create(enabledArea); err != nil {
		logx.Errorf("开通地区失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "开通地区失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "地区开通成功"))
}

// DeleteEnabledArea 删除已开通地区
func (h *AreaHandler) DeleteEnabledArea(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取code
	vars := r.URL.Query()
	code := vars.Get("code")
	if code == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "地区编码不能为空"))
		return
	}

	// 查询这个地区
	_, err := h.areaRepo.FindByCode(code)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 直接返回成功，因为最终目标是确保这个地区不在已开通列表中
			httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "地区删除成功"))
			return
		}
		logx.Errorf("查询地区失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询地区失败"))
		return
	}

	// 删除已开通地区
	if err := h.areaRepo.DeleteByCode(code); err != nil {
		logx.Errorf("删除地区失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除地区失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "地区删除成功"))
}

// GetEnabledProvinces 获取已开通的省份
func (h *AreaHandler) GetEnabledProvinces(w http.ResponseWriter, r *http.Request) {
	provinces, err := h.areaRepo.GetProvinces()
	if err != nil {
		logx.Errorf("获取已开通省份列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取已开通省份列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(provinces, "获取已开通省份列表成功"))
}

// GetEnabledCities 获取指定省份的已开通城市
func (h *AreaHandler) GetEnabledCities(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取provinceCode
	vars := r.URL.Query()
	provinceCode := vars.Get("provinceCode")
	if provinceCode == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "省份编码不能为空"))
		return
	}

	cities, err := h.areaRepo.GetCities(provinceCode)
	if err != nil {
		logx.Errorf("获取已开通城市列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取已开通城市列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(cities, "获取已开通城市列表成功"))
}

// GetEnabledAreas 获取指定城市的已开通区县
func (h *AreaHandler) GetEnabledAreas(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取cityCode
	vars := r.URL.Query()
	cityCode := vars.Get("cityCode")
	if cityCode == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "城市编码不能为空"))
		return
	}

	areas, err := h.areaRepo.GetAreas(cityCode)
	if err != nil {
		logx.Errorf("获取已开通区县列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取已开通区县列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(areas, "获取已开通区县列表成功"))
}
