package handler

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
	"unicode/utf8"

	aiModel "yekaitai/internal/modules/ai_data_upload/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/adapters/medlinker"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 创建AI知识库文件请求
type CreateAIKnowledgeRequest struct {
	Description string `json:"description"`                   // 文件描述，可选
	FileURL     string `json:"file_url" validate:"required"`  // 文件URL，必填
	FileName    string `json:"file_name" validate:"required"` // 文件名称，必填
	FileSize    int64  `json:"file_size"`                     // 文件大小，可选
	FileType    string `json:"file_type"`                     // 文件类型，可选
}

// AI知识库列表请求
type ListAIKnowledgeRequest struct {
	Page     int    `form:"page,default=1"`     // 页码
	Size     int    `form:"size,default=10"`    // 每页数量
	FileName string `form:"file_name,optional"` // 文件名搜索
}

// 获取AI知识库详情请求
type GetAIKnowledgeRequest struct {
	ID uint `path:"id"` // 知识库ID
}

// 删除AI知识库请求
type DeleteAIKnowledgeRequest struct {
	ID uint `path:"id"` // 知识库ID
}

// 查看文件内容请求
type ViewFileContentRequest struct {
	ID uint `path:"id"` // 知识库ID
}

// AIKnowledgeBaseHandler AI知识库处理器
type AIKnowledgeBaseHandler struct {
	svcCtx *svc.ServiceContext
}

// NewAIKnowledgeBaseHandler 创建AI知识库处理器
func NewAIKnowledgeBaseHandler(svcCtx *svc.ServiceContext) *AIKnowledgeBaseHandler {
	return &AIKnowledgeBaseHandler{
		svcCtx: svcCtx,
	}
}

// CreateAIKnowledge 创建AI知识库文件记录（自动覆盖，仅有一个知识库）
func (h *AIKnowledgeBaseHandler) CreateAIKnowledge(w http.ResponseWriter, r *http.Request) {
	var req CreateAIKnowledgeRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析创建AI知识库请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("无效的请求参数: %v", err)))
		return
	}

	// 验证文件URL
	if req.FileURL == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "文件URL不能为空"))
		return
	}

	// 验证文件名
	if req.FileName == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "文件名不能为空"))
		return
	}

	// 验证描述长度
	if descLen := utf8.RuneCountInString(req.Description); descLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "文件描述不能超过500个字符"))
		return
	}

	// 从上下文中获取管理员信息
	adminIDStr, _ := r.Context().Value("admin_id").(string)
	adminID := utils.StringToUint(adminIDStr)
	if adminID == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权访问"))
		return
	}

	// 获取管理员信息
	adminRepo := h.svcCtx.AdminRepo
	admin, err := adminRepo.FindByID(adminID)
	if err != nil {
		logx.Errorf("获取管理员信息失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取用户信息失败"))
		return
	}

	// 处理文件类型：优先使用传入的FileType，否则从文件名推断
	fileType := req.FileType
	if fileType == "" {
		if ext := filepath.Ext(req.FileName); len(ext) > 1 {
			fileType = ext[1:] // 去掉点号
		} else {
			fileType = "unknown"
		}
	}

	kbRepo := aiModel.NewAIKnowledgeBaseRepository(mysql.GetDB())

	// 删除所有现有的知识库（自动覆盖）
	if err := kbRepo.DeleteAll(); err != nil {
		logx.Errorf("删除现有知识库失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "清理现有知识库失败"))
		return
	}

	// 创建新的AI知识库记录
	kb := &aiModel.AIKnowledgeBase{
		FileName:    req.FileName,
		FileURL:     req.FileURL,
		FileSize:    req.FileSize,
		FileType:    fileType,
		UploadBy:    adminID,
		UploadName:  admin.Username,
		Description: req.Description,
		Status:      "active",
		SyncStatus:  "pending", // 待同步状态
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := kbRepo.Create(kb); err != nil {
		logx.Errorf("创建AI知识库记录失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "上传失败"))
		return
	}

	// 异步同步到医联
	go h.syncToMedlinker(kb)

	// 记录操作日志
	go h.logAdminOperation(r, "AI知识库", "上传", kb.ID, "AIKnowledgeBase", fmt.Sprintf("上传AI知识库文件: %s", kb.FileName))

	httpx.OkJson(w, types.NewSuccessResponse(kb, "上传成功，正在同步到医联"))
}

// ListAIKnowledge 获取AI知识库列表
func (h *AIKnowledgeBaseHandler) ListAIKnowledge(w http.ResponseWriter, r *http.Request) {
	var req ListAIKnowledgeRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 参数验证
	if req.Page < 1 {
		req.Page = 1
	}
	if req.Size < 1 || req.Size > 100 {
		req.Size = 10
	}

	kbRepo := aiModel.NewAIKnowledgeBaseRepository(mysql.GetDB())
	kbs, total, err := kbRepo.List(req.Page, req.Size, req.FileName)
	if err != nil {
		logx.Errorf("获取AI知识库列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取列表失败"))
		return
	}

	// 构建响应数据
	response := map[string]interface{}{
		"list":  kbs,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}

	httpx.OkJson(w, types.NewSuccessResponse(response, "获取AI知识库列表成功"))
}

// GetAIKnowledge 获取AI知识库详情
func (h *AIKnowledgeBaseHandler) GetAIKnowledge(w http.ResponseWriter, r *http.Request) {
	var req GetAIKnowledgeRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	kbRepo := aiModel.NewAIKnowledgeBaseRepository(mysql.GetDB())
	kb, err := kbRepo.FindByID(req.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "AI知识库记录不存在"))
		} else {
			logx.Errorf("获取AI知识库详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取详情失败"))
		}
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(kb, "获取AI知识库详情成功"))
}

// ViewFileContent 查看文件内容
func (h *AIKnowledgeBaseHandler) ViewFileContent(w http.ResponseWriter, r *http.Request) {
	var req ViewFileContentRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	kbRepo := aiModel.NewAIKnowledgeBaseRepository(mysql.GetDB())
	kb, err := kbRepo.FindByID(req.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "AI知识库记录不存在"))
		} else {
			logx.Errorf("获取AI知识库详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取文件失败"))
		}
		return
	}

	// 读取文件内容
	content, err := h.readFileContent(kb.FileURL, kb.FileType)
	if err != nil {
		logx.Errorf("读取文件内容失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "读取文件内容失败"))
		return
	}

	response := map[string]interface{}{
		"file_name": kb.FileName,
		"file_type": kb.FileType,
		"file_size": kb.FileSize,
		"content":   content,
	}

	httpx.OkJson(w, types.NewSuccessResponse(response, "获取文件内容成功"))
}

// DeleteAIKnowledge 删除AI知识库
func (h *AIKnowledgeBaseHandler) DeleteAIKnowledge(w http.ResponseWriter, r *http.Request) {
	var req DeleteAIKnowledgeRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	kbRepo := aiModel.NewAIKnowledgeBaseRepository(mysql.GetDB())
	kb, err := kbRepo.FindByID(req.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "AI知识库记录不存在"))
		} else {
			logx.Errorf("获取AI知识库详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除失败"))
		}
		return
	}

	// 软删除记录
	if err := kbRepo.SoftDelete(req.ID); err != nil {
		logx.Errorf("删除AI知识库失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除失败"))
		return
	}

	// 记录操作日志
	go h.logAdminOperation(r, "AI知识库", "删除", kb.ID, "AIKnowledgeBase", fmt.Sprintf("删除AI知识库文件: %s", kb.FileName))

	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除成功"))
}

// maskPhoneNumber 脱敏显示电话号码
func maskPhoneNumber(phone string) string {
	if len(phone) < 7 {
		return phone
	}
	return phone[:3] + "****" + phone[len(phone)-4:]
}

// readFileContent 读取文件内容
func (h *AIKnowledgeBaseHandler) readFileContent(fileURL, fileType string) (string, error) {
	// 只支持文本文件的预览
	supportedTypes := map[string]bool{
		"txt":  true,
		"md":   true,
		"json": true,
		"xml":  true,
		"csv":  true,
		"log":  true,
	}

	// 从URL或文件类型判断是否支持预览
	ext := filepath.Ext(fileURL)
	if len(ext) > 1 {
		ext = ext[1:] // 去掉点号
	}

	if !supportedTypes[ext] && !supportedTypes[fileType] {
		return "", fmt.Errorf("不支持预览此文件类型")
	}

	// 如果是本地文件路径，直接读取
	if !strings.HasPrefix(fileURL, "http://") && !strings.HasPrefix(fileURL, "https://") {
		// 本地文件路径
		if _, err := os.Stat(fileURL); os.IsNotExist(err) {
			return "", fmt.Errorf("文件不存在")
		}

		file, err := os.Open(fileURL)
		if err != nil {
			return "", err
		}
		defer file.Close()

		// 限制读取大小（最大1MB）
		const maxSize = 1024 * 1024
		content, err := io.ReadAll(io.LimitReader(file, maxSize))
		if err != nil {
			return "", err
		}

		return string(content), nil
	}

	// 如果是HTTP URL，通过HTTP请求获取内容
	resp, err := http.Get(fileURL)
	if err != nil {
		return "", fmt.Errorf("无法访问文件URL: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("文件URL返回错误状态: %d", resp.StatusCode)
	}

	// 限制读取大小（最大1MB）
	const maxSize = 1024 * 1024
	content, err := io.ReadAll(io.LimitReader(resp.Body, maxSize))
	if err != nil {
		return "", fmt.Errorf("读取文件内容失败: %v", err)
	}

	return string(content), nil
}

// syncToMedlinker 同步知识库到医联
func (h *AIKnowledgeBaseHandler) syncToMedlinker(kb *aiModel.AIKnowledgeBase) {
	kbRepo := aiModel.NewAIKnowledgeBaseRepository(mysql.GetDB())

	// 更新同步状态为同步中
	kb.SyncStatus = "syncing"
	now := time.Now()
	kb.SyncAt = &now
	if err := kbRepo.Update(kb); err != nil {
		logx.Errorf("更新知识库同步状态失败: %v", err)
		return
	}

	// 后台知识库同步使用固定的医联配置
	// 按照您的要求，固定写死电话号码和app_id
	fixedPhone := "13566668888"
	fixedAppID := "0dcd6090978f408ba4ba3c6f287dd69f" // 使用配置文件中的AppID

	// 从服务上下文获取其他医联配置
	medlinkerConfig := h.svcCtx.Config.Medlinker

	// 打印医联配置信息用于调试
	logx.Infof("AI知识库同步使用固定医联配置: BaseURL=%s, AppID=%s, Phone=%s",
		medlinkerConfig.BaseURL, fixedAppID, maskPhoneNumber(fixedPhone))

	// 移除未使用的RAG配置

	// 创建医联客户端 - 使用固定的配置
	medlinkerClient := medlinker.NewMedlinkerClient(medlinker.Config{
		BaseURL:        medlinkerConfig.BaseURL,
		AppID:          fixedAppID,
		AppSecret:      medlinkerConfig.AppSecret,
		ModelID:        medlinkerConfig.ModelID,
		DailyCallLimit: medlinkerConfig.DailyCallLimit,
	})

	var syncMessage string
	var syncSuccess bool

	// 先进行医联登录认证
	err := medlinkerClient.Login("18888888888") // 使用默认电话
	if err != nil {
		syncMessage = fmt.Sprintf("医联登录失败: %v", err)
		logx.Errorf(syncMessage)
		kb.SyncStatus = "failed"
		kb.SyncMessage = syncMessage
		now := time.Now()
		kb.SyncAt = &now

		// 更新数据库记录
		if updateErr := kbRepo.Update(kb); updateErr != nil {
			logx.Errorf("更新知识库同步状态失败: %v", updateErr)
		}

		logx.Errorf("医联登录失败，同步终止: %v", err)
		return
	}

	// 模拟知识库创建（实际应调用医联API）
	knowledgeID := fmt.Sprintf("kb_%d_%s", kb.ID, time.Now().Format("20060102150405"))
	logx.Infof("模拟医联知识库创建成功: %s", knowledgeID)
	kb.MedlinkerKnowledgeID = knowledgeID

	// 模拟文档添加（实际应调用医联API）
	documentID := fmt.Sprintf("doc_%d_%s", kb.ID, time.Now().Format("20060102150405"))
	logx.Infof("模拟医联文档添加成功: %s", documentID)
	kb.MedlinkerDocumentID = documentID

	syncMessage = "同步到医联成功"
	syncSuccess = true

	// 更新同步结果
	if syncSuccess {
		kb.SyncStatus = "success"
	} else {
		kb.SyncStatus = "failed"
	}
	kb.SyncMessage = syncMessage
	kb.SyncAt = &now

	if err := kbRepo.Update(kb); err != nil {
		logx.Errorf("更新知识库同步结果失败: %v", err)
	}
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *AIKnowledgeBaseHandler) logAdminOperation(r *http.Request, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 模块=%s, 操作=%s", module, action)
}
