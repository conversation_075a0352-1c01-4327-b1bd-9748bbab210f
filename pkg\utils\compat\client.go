package compat

import (
	"context"
	"yekaitai/pkg/adapters/yekaitai"
)

// YekaitaiClientWrapper 是叶开泰客户端的包装器
type YekaitaiClientWrapper struct {
	client *yekaitai.Client
	config *yekaitai.Config
}

// NewYekaitaiClientWrapper 创建一个叶开泰客户端包装器
func NewYekaitaiClientWrapper(client *yekaitai.Client, config *yekaitai.Config) *YekaitaiClientWrapper {
	return &YekaitaiClientWrapper{
		client: client,
		config: config,
	}
}

// Config 获取叶开泰客户端配置
func (c *YekaitaiClientWrapper) Config() *yekaitai.Config {
	return c.config
}

// Client 获取原始叶开泰客户端
func (c *YekaitaiClientWrapper) Client() *yekaitai.Client {
	return c.client
}

// Department 部门信息
type Department struct {
	Jgksid int    // 机构科室ID
	Jgksmc string // 机构科室名称
}

// Doctor 医生信息
type Doctor struct {
	Ysid int    // 医生ID
	Pblx string // 排班类型
}

// Schedule 排班信息
type Schedule struct {
	Ghf  float64  // 挂号费
	Zlf  float64  // 诊疗费
	Hyjl []Source // 号源记录
}

// Source 号源信息
type Source struct {
	Hyjlid int    // 号源记录ID
	Hyrq   string // 号源日期
	Hysj   string // 号源时间
	Hyzt   string // 号源状态
}

// Appointment 预约信息
type Appointment struct {
	Yyghid int    // 预约挂号ID
	Xm     string // 姓名
	Hyzt   string // 号源状态
}

// GetAvailableDepartments 获取可用科室列表
func (c *YekaitaiClientWrapper) GetAvailableDepartments(ctx context.Context, wsjgid string) ([]Department, error) {
	// 检查客户端是否初始化
	if c == nil || c.client == nil || c.config == nil {
		// 模拟返回空结果
		return []Department{}, nil
	}

	// 模拟返回科室列表
	return []Department{
		{Jgksid: 1, Jgksmc: "内科"},
		{Jgksid: 2, Jgksmc: "外科"},
		{Jgksid: 3, Jgksmc: "妇科"},
	}, nil
}

// GetDoctors 获取医生列表
func (c *YekaitaiClientWrapper) GetDoctors(ctx context.Context, wsjgid, date, departmentID string) ([]Doctor, error) {
	// 检查客户端是否初始化
	if c == nil || c.client == nil || c.config == nil {
		// 模拟返回空结果
		return []Doctor{}, nil
	}

	// 模拟返回医生列表
	return []Doctor{
		{Ysid: 101, Pblx: "1"},
		{Ysid: 102, Pblx: "2"},
	}, nil
}

// GetSchedules 获取排班信息
func (c *YekaitaiClientWrapper) GetSchedules(ctx context.Context, wsjgid, date, departmentID, doctorID string) ([]Schedule, error) {
	// 检查客户端是否初始化
	if c == nil || c.client == nil || c.config == nil {
		// 模拟返回空结果
		return []Schedule{}, nil
	}

	// 模拟返回排班信息
	return []Schedule{
		{
			Ghf: 20.0,
			Zlf: 10.0,
			Hyjl: []Source{
				{Hyjlid: 1001, Hyrq: "2025-03-28", Hysj: "09:00:00", Hyzt: "0"},
				{Hyjlid: 1002, Hyrq: "2025-03-28", Hysj: "10:00:00", Hyzt: "0"},
			},
		},
	}, nil
}

// GetAppointments 获取预约列表
func (c *YekaitaiClientWrapper) GetAppointments(ctx context.Context, wsjgid, startDate, endDate, departmentID, doctorID string) ([]Appointment, error) {
	// 检查客户端是否初始化
	if c == nil || c.client == nil || c.config == nil {
		// 模拟返回空结果
		return []Appointment{}, nil
	}

	// 模拟返回预约列表
	return []Appointment{
		{Yyghid: 10001, Xm: "张三", Hyzt: "1"},
		{Yyghid: 10002, Xm: "李四", Hyzt: "0"},
	}, nil
}
