package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	adminHandler "yekaitai/internal/modules/admin/handler"
	smsHandler "yekaitai/internal/modules/sms/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterAuthRoutes 注册认证相关路由
func RegisterAuthRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.ServiceContext) {
	// 获取JWT中间件
	jwtMiddleware := middleware.NewJWTMiddleware(serverCtx)

	// 无需认证的认证路由
	server.AddRoutes([]rest.Route{
		// 管理员登录
		{
			Method:  http.MethodPost,
			Path:    "/api/admin/auth/login",
			Handler: adminHandler.AdminLoginHandler(serverCtx, jwtMiddleware),
		},
		// 管理员刷新Token
		{
			Method:  http.MethodPost,
			Path:    "/api/admin/auth/refresh-token",
			Handler: adminHandler.AdminRefreshTokenHandler(serverCtx, jwtMiddleware),
		},
		// 发送短信验证码
		{
			Method:  http.MethodPost,
			Path:    "/api/admin/auth/send-sms-code",
			Handler: smsHandler.SendSmsCodeHandler(serverCtx),
		},
		// 验证短信验证码
		{
			Method:  http.MethodPost,
			Path:    "/api/admin/auth/verify-sms-code",
			Handler: smsHandler.VerifySmsCodeHandler(serverCtx),
		},
	})

	// 需要管理员JWT认证的路由
	adminAuthHandler := func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			middleware.AdminAuthMiddleware(serverCtx)(http.HandlerFunc(next)).ServeHTTP(w, r)
		}
	}

	server.AddRoutes(
		[]rest.Route{
			// 管理员登出
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/auth/logout",
				Handler: adminAuthHandler(adminHandler.AdminLogoutHandler(serverCtx)),
			},
		},
	)
}
