package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"

	"yekaitai/internal/modules/appointment/types"
	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// AbcYunExaminationQueryByPatientHandler 按患者查询检查检验单
func AbcYunExaminationQueryByPatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationQueryByPatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PatientID == "" {
		httpx.Error(w, errors.New("患者ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/examination/query-by-patient/%s", req.PatientID)
	queryParams := make(map[string]string)

	if req.StartTime != "" {
		queryParams["startTime"] = req.StartTime
	}
	if req.EndTime != "" {
		queryParams["endTime"] = req.EndTime
	}
	if req.Limit > 0 {
		queryParams["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset > 0 {
		queryParams["offset"] = strconv.Itoa(req.Offset)
	}
	if req.Type > 0 {
		queryParams["type"] = strconv.Itoa(req.Type)
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.ExaminationSummary `json:"rows"`
			Total  int                        `json:"total"`
			Offset int                        `json:"offset"`
			Limit  int                        `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "按患者查询检查检验单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationQueryByDateHandler 按时间查询检查检验单
func AbcYunExaminationQueryByDateHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationQueryByDateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/examination/all/query-by-date"
	queryParams := make(map[string]string)

	if req.DateFieldType > 0 {
		queryParams["dateFieldType"] = strconv.Itoa(req.DateFieldType)
	}
	if req.StartTime != "" {
		queryParams["startTime"] = req.StartTime
	}
	if req.EndTime != "" {
		queryParams["endTime"] = req.EndTime
	}
	if req.Limit > 0 {
		queryParams["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset > 0 {
		queryParams["offset"] = strconv.Itoa(req.Offset)
	}
	if req.Type > 0 {
		queryParams["type"] = strconv.Itoa(req.Type)
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.ExaminationAllSummary `json:"rows"`
			Total  int                           `json:"total"`
			Offset int                           `json:"offset"`
			Limit  int                           `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "按时间查询检查检验单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationQueryByPatientOrderNoHandler 按就诊号查询检查检验单
func AbcYunExaminationQueryByPatientOrderNoHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationQueryByPatientOrderNoRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PatientOrderNo == "" {
		httpx.Error(w, errors.New("就诊号不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/examination/query-by-patient-order-no"
	queryParams := map[string]string{
		"patientOrderNo": req.PatientOrderNo,
	}

	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                          `json:"code"`
		Message string                       `json:"message"`
		Data    types.ExaminationQueryResult `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "按就诊号查询检查检验单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationDetailHandler 查询检查检验单详情
func AbcYunExaminationDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("检查检验单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/examination/examination-sheet-detail/%s", req.ID)
	queryParams := make(map[string]string)

	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                     `json:"code"`
		Message string                  `json:"message"`
		Data    types.ExaminationDetail `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询检查检验单详情成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationByOrderNoHandler 按单号查询检查检验单(条形码)
func AbcYunExaminationByOrderNoHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationByOrderNoRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.OrderNo == "" {
		httpx.Error(w, errors.New("订单号不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/examination/by-order-no/%s/%d", req.OrderNo, req.Type)
	queryParams := make(map[string]string)

	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                     `json:"code"`
		Message string                  `json:"message"`
		Data    types.ExaminationDetail `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "按单号查询检查检验单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationUpdateByOrderNoHandler 根据检验单号修改检验单信息
func AbcYunExaminationUpdateByOrderNoHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationUpdateByOrderNoRequest
	if err := httpx.ParsePath(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// --- Manual Body Parsing Start ---
	bodyBytes, bodyErr := io.ReadAll(r.Body)
	if bodyErr != nil {
		log.Printf("[ERROR] Failed to read request body for UpdateByOrderNo: %v", bodyErr)
		httpx.Error(w, fmt.Errorf("读取请求体失败: %w", bodyErr))
		return
	}
	// Close the original body is good practice, though not strictly necessary here
	_ = r.Body.Close()
	log.Printf("[DEBUG] UpdateByOrderNo Raw Request Body: %s", string(bodyBytes))

	// Use standard json.Unmarshal instead of httpx.ParseJsonBody
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		log.Printf("[ERROR] Failed to unmarshal JSON body for UpdateByOrderNo: %v. Body was: %s", err, string(bodyBytes))
		// Use a more specific error message for bad requests
		httpx.Error(w, fmt.Errorf("请求体JSON格式错误: %w", err)) // Return 400 Bad Request potentially
		return
	}
	// --- Manual Body Parsing End ---

	// --- Logging Parsed Struct Start ---
	parsedReqBytes, _ := json.MarshalIndent(req, "", "  ")
	log.Printf("[DEBUG] UpdateByOrderNo Parsed Request Struct (Manual Unmarshal):\n%s", string(parsedReqBytes))
	// --- Logging Parsed Struct End ---

	// 参数验证
	if req.OrderNo == "" {
		httpx.Error(w, errors.New("订单号不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/examination/by-order-no/%s/%d", req.OrderNo, req.Type)

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 添加可选的clinicId参数
	if req.ClinicID != "" {
		path = fmt.Sprintf("%s?clinicId=%s", path, req.ClinicID)
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                     `json:"code"`
		Message string                  `json:"message"`
		Data    types.ExaminationDetail `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "根据检验单号修改检验单信息成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationUpdateHandler 保存检查检验单数据
func AbcYunExaminationUpdateHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationUpdateRequest
	if err := httpx.ParsePath(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// --- Manual Body Parsing Start ---
	bodyBytes, bodyErr := io.ReadAll(r.Body)
	if bodyErr != nil {
		log.Printf("[ERROR] Failed to read request body for Update: %v", bodyErr)
		httpx.Error(w, fmt.Errorf("读取请求体失败: %w", bodyErr))
		return
	}
	// Close the original body
	_ = r.Body.Close()
	log.Printf("[DEBUG] Update Raw Request Body: %s", string(bodyBytes))

	// Use standard json.Unmarshal instead of httpx.ParseJsonBody
	if err := json.Unmarshal(bodyBytes, &req); err != nil {
		log.Printf("[ERROR] Failed to unmarshal JSON body for Update: %v. Body was: %s", err, string(bodyBytes))
		httpx.Error(w, fmt.Errorf("请求体JSON格式错误: %w", err))
		return
	}
	// --- Manual Body Parsing End ---

	// --- Logging Parsed Struct Start ---
	parsedReqBytes, _ := json.MarshalIndent(req, "", "  ")
	log.Printf("[DEBUG] Update Parsed Request Struct (Manual Unmarshal):\n%s", string(parsedReqBytes))
	// --- Logging Parsed Struct End ---

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("检查检验单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/examination/examination-sheet-detail/%s", req.ID)

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 添加可选的clinicId参数
	if req.ClinicID != "" {
		path = fmt.Sprintf("%s?clinicId=%s", path, req.ClinicID)
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                     `json:"code"`
		Message string                  `json:"message"`
		Data    types.ExaminationDetail `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "保存检查检验单数据成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationCreateHandler 创建检查检验单
func AbcYunExaminationCreateHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationCreateRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PatientID == "" {
		httpx.Error(w, errors.New("患者ID不能为空"))
		return
	}
	if req.DoctorID == "" {
		httpx.Error(w, errors.New("医生ID不能为空"))
		return
	}
	if req.DoctorDepartmentID == "" {
		httpx.Error(w, errors.New("医生科室ID不能为空"))
		return
	}
	if req.OperatorID == "" {
		httpx.Error(w, errors.New("操作员ID不能为空"))
		return
	}
	if len(req.ExaminationFormItems) == 0 {
		httpx.Error(w, errors.New("检查项目不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/examination"

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 添加可选的clinicId参数
	if req.ClinicID != "" {
		path = fmt.Sprintf("%s?clinicId=%s", path, req.ClinicID)
	}

	respBody, err := client.Post(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                             `json:"code"`
		Message string                          `json:"message"`
		Data    types.ExaminationCreateResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "创建检查检验单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationRefundHandler 作废检查检验单
func AbcYunExaminationRefundHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationRefundRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("检查检验单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/examination/refund/%s", req.ID)

	// 添加可选的clinicId参数
	if req.ClinicID != "" {
		path = fmt.Sprintf("%s?clinicId=%s", path, req.ClinicID)
	}

	respBody, err := client.Put(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "作废检查检验单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunExaminationDevicesHandler 获取检查检验设备列表
func AbcYunExaminationDevicesHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.ExaminationDevicesRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/examination/devices/%d", req.Type)

	// 添加可选的clinicId参数
	queryParams := make(map[string]string)
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                             `json:"code"`
		Message string                          `json:"message"`
		Data    types.ExaminationDeviceResponse `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取检查检验设备列表成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}
