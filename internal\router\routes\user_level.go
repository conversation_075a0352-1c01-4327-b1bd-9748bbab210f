package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/user_level/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// UserLevelRoutes 用户等级管理路由
func UserLevelRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.ServiceContext) {
	userLevelHandler := handler.NewUserLevelHandler()

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 用户等级管理路由组（需要管理员认证）
	server.AddRoutes(
		[]rest.Route{
			// 等级列表
			{
				Method:  "GET",
				Path:    "/api/admin/user-levels",
				Handler: adminAuthWrapper(userLevelHandler.GetUserLevelList),
			},
			// 创建等级
			{
				Method:  "POST",
				Path:    "/api/admin/user-levels",
				Handler: adminAuthWrapper(userLevelHandler.CreateUserLevel),
			},
			// 获取等级详情
			{
				Method:  "GET",
				Path:    "/api/admin/user-levels/:id",
				Handler: adminAuthWrapper(userLevelHandler.GetUserLevelDetail),
			},
			// 更新等级
			{
				Method:  "PUT",
				Path:    "/api/admin/user-levels/:id",
				Handler: adminAuthWrapper(userLevelHandler.UpdateUserLevel),
			},
			// 删除等级
			{
				Method:  "DELETE",
				Path:    "/api/admin/user-levels/:id",
				Handler: adminAuthWrapper(userLevelHandler.DeleteUserLevel),
			},
			// 获取可选择的优惠券列表（用于等级权益配置）
			{
				Method:  "GET",
				Path:    "/api/admin/user-levels/available-coupons",
				Handler: adminAuthWrapper(userLevelHandler.GetAvailableCoupons),
			},
		},
	)
}
