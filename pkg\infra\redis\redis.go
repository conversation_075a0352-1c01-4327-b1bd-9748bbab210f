package redis

import (
	"context"
	"sync"
	"time"

	"yekaitai/internal/config"

	"github.com/go-redis/redis/v8"
)

var (
	// Redis客户端
	client *redis.Client
	// 上下文
	ctx = context.Background()
	// 初始化标志
	once sync.Once
)

// Init 初始化Redis客户端
func Init(cfg config.RedisConfig) error {
	once.Do(func() {
		client = redis.NewClient(&redis.Options{
			Addr:     cfg.Addr,
			Password: cfg.Password,
			DB:       cfg.DB,
			PoolSize: cfg.PoolSize,
		})
	})

	// 测试连接
	if err := client.Ping(ctx).Err(); err != nil {
		return err
	}
	return nil
}

// GetClient 获取Redis客户端
func GetClient() *redis.Client {
	if client == nil {
		// 默认配置，如果未初始化
		Init(config.RedisConfig{
			Addr:     "localhost:6379",
			Password: "",
			DB:       0,
		})
	}
	return client
}

// Get 获取字符串值
func Get(key string) (string, error) {
	if client == nil {
		return "", nil
	}
	return client.Get(ctx, key).Result()
}

// Set 设置字符串值
func Set(key string, value interface{}, expiration time.Duration) error {
	if client == nil {
		return nil
	}
	return client.Set(ctx, key, value, expiration).Err()
}

// Del 删除键
func Del(keys ...string) error {
	if client == nil {
		return nil
	}
	return client.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在
func Exists(keys ...string) (bool, error) {
	if client == nil {
		return false, nil
	}
	result, err := client.Exists(ctx, keys...).Result()
	if err != nil {
		return false, err
	}
	return result > 0, nil
}

// Expire 设置过期时间
func Expire(key string, expiration time.Duration) error {
	if client == nil {
		return nil
	}
	return client.Expire(ctx, key, expiration).Err()
}

// HGet 获取哈希字段
func HGet(key, field string) (string, error) {
	if client == nil {
		return "", nil
	}
	return client.HGet(ctx, key, field).Result()
}

// HSet 设置哈希字段
func HSet(key string, values ...interface{}) error {
	if client == nil {
		return nil
	}
	return client.HSet(ctx, key, values...).Err()
}

// HGetAll 获取所有哈希字段
func HGetAll(key string) (map[string]string, error) {
	if client == nil {
		return nil, nil
	}
	return client.HGetAll(ctx, key).Result()
}

// HDel 删除哈希字段
func HDel(key string, fields ...string) error {
	if client == nil {
		return nil
	}
	return client.HDel(ctx, key, fields...).Err()
}

// LPush 将值推到列表左侧
func LPush(key string, values ...interface{}) error {
	if client == nil {
		return nil
	}
	return client.LPush(ctx, key, values...).Err()
}

// RPush 将值推到列表右侧
func RPush(key string, values ...interface{}) error {
	if client == nil {
		return nil
	}
	return client.RPush(ctx, key, values...).Err()
}

// LPop 从列表左侧弹出值
func LPop(key string) (string, error) {
	if client == nil {
		return "", nil
	}
	return client.LPop(ctx, key).Result()
}

// RPop 从列表右侧弹出值
func RPop(key string) (string, error) {
	if client == nil {
		return "", nil
	}
	return client.RPop(ctx, key).Result()
}

// LRange 获取列表范围
func LRange(key string, start, stop int64) ([]string, error) {
	if client == nil {
		return nil, nil
	}
	return client.LRange(ctx, key, start, stop).Result()
}

// SAdd 添加集合成员
func SAdd(key string, members ...interface{}) error {
	if client == nil {
		return nil
	}
	return client.SAdd(ctx, key, members...).Err()
}

// SMembers 获取集合所有成员
func SMembers(key string) ([]string, error) {
	if client == nil {
		return nil, nil
	}
	return client.SMembers(ctx, key).Result()
}

// SRem 移除集合成员
func SRem(key string, members ...interface{}) error {
	if client == nil {
		return nil
	}
	return client.SRem(ctx, key, members...).Err()
}

// Close 关闭Redis连接
func Close() error {
	if client != nil {
		return client.Close()
	}
	return nil
}
