package tasks

import (
	"encoding/json"
	"fmt"

	"yekaitai/internal/modules/store/model"
	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// Employee 员工信息结构体
type Employee struct {
	ID             string         `json:"id"`
	Position       string         `json:"position"`
	HeadImgUrl     string         `json:"headImgUrl"`
	Name           string         `json:"name"`
	Mobile         string         `json:"mobile"`
	Sex            string         `json:"sex"`
	Birthday       string         `json:"birthday"`
	Role           string         `json:"role"`
	Introduction   string         `json:"introduction"`
	GoodAt         string         `json:"goodAt"`
	Roles          []int          `json:"roles"`
	DoctorTags     string         `json:"doctorTags"`
	PracticeInfos  []PracticeInfo `json:"practiceInfos"`
	PracticeImgUrl string         `json:"practiceImgUrl"`
}

// PracticeInfo 职称信息
type PracticeInfo struct {
	Type  string `json:"type"`
	Title string `json:"title"`
}

// EmployeeListResponse 员工列表响应
type EmployeeListResponse struct {
	EmployeeList []Employee `json:"employeeList"`
}

// AbcYunDoctorSyncService ABC云医生同步服务
type AbcYunDoctorSyncService struct {
	client *abcyun.AbcYunClient
	db     *gorm.DB
}

// NewAbcYunDoctorSyncService 创建ABC云医生同步服务
func NewAbcYunDoctorSyncService(client *abcyun.AbcYunClient) *AbcYunDoctorSyncService {
	return &AbcYunDoctorSyncService{
		client: client,
		db:     mysql.Master(),
	}
}

// SyncDoctors 同步医生信息
func (s *AbcYunDoctorSyncService) SyncDoctors() error {
	logx.Info("开始同步ABC云医生信息")

	// 首先获取门店信息
	clinicInfo, err := s.client.GetClinicInfo()
	if err != nil {
		return fmt.Errorf("获取门店信息失败: %w", err)
	}

	// 获取科室列表，用于匹配医生所属科室
	departments, err := s.client.GetDepartmentList()
	if err != nil {
		return fmt.Errorf("获取科室列表失败: %w", err)
	}

	// 调用获取人员列表接口
	employees, err := s.getEmployeeList()
	if err != nil {
		return fmt.Errorf("获取员工列表失败: %w", err)
	}

	var syncCount, createCount, updateCount int

	// 遍历员工列表，只同步角色为1,4,5的员工
	for _, emp := range employees {
		// 检查是否包含目标角色（1-医生 4-理疗师 5-医助）
		if !s.hasTargetRole(emp.Roles) {
			continue
		}

		// 直接使用员工ID作为ysID（ABC云返回的ID已经是字符串）
		ysIDStr := emp.ID

		// 检查医生是否已存在（通过ys_id字段）
		var existingDoctor doctor.WxDoctor
		err = s.db.Where("ys_id = ?", ysIDStr).First(&existingDoctor).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			logx.Errorf("查询医生失败: %v", err)
			continue
		}

		// 序列化职称信息
		practiceInfosJSON, _ := json.Marshal(emp.PracticeInfos)

		// 获取第一个科室作为默认科室（如果有的话）
		var departmentID, departmentName string
		if len(departments) > 0 {
			departmentID = departments[0].ID
			departmentName = departments[0].Name
		}

		doctorData := &doctor.WxDoctor{
			Name:           emp.Name,
			Mobile:         emp.Mobile,
			YsID:           ysIDStr, // 使用字符串类型
			Specialty:      emp.GoodAt,
			Title:          emp.Role,
			Introduction:   emp.Introduction,
			HeadImgUrl:     emp.HeadImgUrl,
			PracticeImgUrl: emp.PracticeImgUrl,
			DoctorTags:     emp.DoctorTags,
			PracticeInfos:  string(practiceInfosJSON),
			Status:         1,               // 正常状态
			CreatorID:      1,               // 系统创建
			WsjgID:         clinicInfo.ID,   // 门店ID
			JgksID:         departmentID,    // 科室ID
			Department:     departmentName,  // 科室名称
			Hospital:       clinicInfo.Name, // 门店名称
		}

		if err == nil { // 医生已存在
			// 更新现有医生，保留原有的创建时间
			doctorData.DoctorID = existingDoctor.DoctorID
			doctorData.CreatedAt = existingDoctor.CreatedAt
			err = s.db.Save(doctorData).Error
			if err != nil {
				logx.Errorf("更新医生失败: %v", err)
				continue
			}
			updateCount++
			logx.Infof("更新医生成功: %s (ID: %d)", doctorData.Name, doctorData.DoctorID)
		} else {
			// 创建新医生
			err = s.db.Create(doctorData).Error
			if err != nil {
				logx.Errorf("创建医生失败: %v", err)
				continue
			}
			createCount++
			logx.Infof("创建医生成功: %s (ID: %d)", doctorData.Name, doctorData.DoctorID)
		}

		// 关联门店
		if err := s.associateStore(doctorData); err != nil {
			logx.Errorf("关联医生门店失败: %v", err)
		}

		syncCount++
	}

	logx.Infof("ABC云医生信息同步完成，总计同步: %d, 新建: %d, 更新: %d",
		syncCount, createCount, updateCount)
	return nil
}

// getEmployeeList 获取员工列表
func (s *AbcYunDoctorSyncService) getEmployeeList() ([]Employee, error) {
	// 使用已有的API接口获取员工列表
	employees, err := s.client.GetEmployeeList()
	if err != nil {
		return nil, fmt.Errorf("调用获取员工列表接口失败: %w", err)
	}

	// 转换为本地Employee结构体
	var result []Employee
	for _, emp := range employees {
		// 转换DoctorTags数组为字符串
		doctorTagsStr := ""
		if len(emp.DoctorTags) > 0 {
			doctorTagsBytes, _ := json.Marshal(emp.DoctorTags)
			doctorTagsStr = string(doctorTagsBytes)
		}

		// 转换PracticeInfos
		var practiceInfos []PracticeInfo
		for _, pi := range emp.PracticeInfos {
			practiceInfos = append(practiceInfos, PracticeInfo{
				Type:  pi.Type,
				Title: pi.Title,
			})
		}

		result = append(result, Employee{
			ID:             emp.ID,
			Position:       emp.Position,
			HeadImgUrl:     emp.HeadImgURL,
			Name:           emp.Name,
			Mobile:         emp.Mobile,
			Sex:            emp.Sex,
			Birthday:       emp.Birthday,
			Role:           emp.Role,
			Introduction:   emp.Introduction,
			GoodAt:         emp.GoodAt,
			Roles:          emp.Roles,
			DoctorTags:     doctorTagsStr,
			PracticeInfos:  practiceInfos,
			PracticeImgUrl: emp.PracticeImgURL,
		})
	}

	return result, nil
}

// hasTargetRole 检查是否包含目标角色（1-医生 4-理疗师 5-医助）
func (s *AbcYunDoctorSyncService) hasTargetRole(roles []int) bool {
	targetRoles := map[int]bool{1: true, 4: true, 5: true}
	for _, role := range roles {
		if targetRoles[role] {
			return true
		}
	}
	return false
}

// associateStore 关联医生与门店
func (s *AbcYunDoctorSyncService) associateStore(wxDoctor *doctor.WxDoctor) error {
	// 根据wsjg_id查找门店
	var targetStore model.Store
	result := s.db.Where("wsjg_id = ?", wxDoctor.WsjgID).First(&targetStore)
	if result.Error != nil {
		logx.Errorf("未找到门店 wsjg_id='%s'，无法关联医生: %s", wxDoctor.WsjgID, wxDoctor.Name)
		return result.Error
	}

	// 先清除旧的关联
	if err := s.db.Exec("DELETE FROM doctor_store_relation WHERE doctor_id = ?", wxDoctor.DoctorID).Error; err != nil {
		logx.Errorf("清除医生与医疗机构关联失败，医生ID=%d: %v", wxDoctor.DoctorID, err)
		return err
	}

	// 添加新的关联
	if err := s.db.Exec("INSERT INTO doctor_store_relation(doctor_id, store_id) VALUES(?, ?)",
		wxDoctor.DoctorID, targetStore.ID).Error; err != nil {
		logx.Errorf("添加医生与医疗机构关联失败，医生ID=%d, 医疗机构ID=%d: %v",
			wxDoctor.DoctorID, targetStore.ID, err)
		return err
	}

	logx.Infof("成功关联医生: %s 到门店: %s", wxDoctor.Name, targetStore.Name)
	return nil
}
