package service

import (
	"github.com/robfig/cron/v3"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/cmd/cron/tasks"
	"yekaitai/internal/config"
	"yekaitai/internal/modules/store/model"

	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/adapters/hangzhou"
)

// EnhancedCronService 增强的定时任务服务，包含所有手动任务的定时执行
type EnhancedCronService struct {
	cron           *cron.Cron
	db             *gorm.DB
	active         bool
	hangzhouClient *hangzhou.Client
	abcYunClient   *abcyun.AbcYunClient
	config         *config.Config
}

// NewEnhancedCronService 创建增强的定时任务服务
func NewEnhancedCronService(db *gorm.DB, cfg *config.Config, clients ...interface{}) *EnhancedCronService {
	service := &EnhancedCronService{
		cron:   cron.New(cron.WithSeconds()),
		db:     db,
		active: false,
		config: cfg,
	}

	// 处理可选的客户端参数
	for _, client := range clients {
		if hangzhouClient, ok := client.(*hangzhou.Client); ok {
			service.hangzhouClient = hangzhouClient
		} else if abcClient, ok := client.(*abcyun.AbcYunClient); ok {
			service.abcYunClient = abcClient
		}
	}

	return service
}

// Start 启动所有定时任务
func (s *EnhancedCronService) Start() {
	if s.active {
		logx.Info("增强定时任务服务已在运行中")
		return
	}

	logx.Info("开始启动增强定时任务服务...")

	// 1. 添加基础数据同步任务
	s.addBasicSyncTasks()

	// 2. 添加ABC云同步任务
	s.addAbcYunSyncTasks()

	// 3. 添加万里牛ERP同步任务
	s.addWanLiNiuSyncTasks()

	// 4. 添加业务处理任务
	s.addBusinessTasks()

	// 启动定时任务
	s.cron.Start()
	s.active = true

	logx.Info("增强定时任务服务启动成功")
}

// addBasicSyncTasks 添加基础数据同步任务
func (s *EnhancedCronService) addBasicSyncTasks() {
	// 门诊收费信息同步
	chargeSyncService := tasks.NewChargeSyncService(s.db)
	_, err := s.cron.AddFunc("0 30 * * * ?", func() {
		logx.Info("定时任务：开始门诊收费信息同步...")
		chargeSyncService.SyncCharges()
		logx.Info("定时任务：门诊收费信息同步完成")
	})
	if err != nil {
		logx.Errorf("添加门诊收费信息同步任务失败: %v", err)
	} else {
		logx.Info("已添加门诊收费信息同步任务，每小时30分执行")
	}

	// 医生数据同步
	doctorSyncService := tasks.NewDoctorSyncService(s.db)
	_, err = s.cron.AddFunc("0 10 * * * ?", func() {
		logx.Info("定时任务：开始医生数据同步...")
		if syncErr := doctorSyncService.SyncDoctors(); syncErr != nil {
			logx.Errorf("医生数据同步失败: %v", syncErr)
		} else {
			logx.Info("定时任务：医生数据同步完成")
		}
	})
	if err != nil {
		logx.Errorf("添加医生数据同步任务失败: %v", err)
	} else {
		logx.Info("已添加医生数据同步任务，每小时10分执行")
	}

	// 患者数据同步
	patientSyncService := tasks.NewPatientSyncService(s.db)
	_, err = s.cron.AddFunc("0 20 * * * ?", func() {
		logx.Info("定时任务：开始患者数据同步...")
		if syncErr := patientSyncService.SyncPatients(); syncErr != nil {
			logx.Errorf("患者数据同步失败: %v", syncErr)
		} else {
			logx.Info("定时任务：患者数据同步完成")
		}
	})
	if err != nil {
		logx.Errorf("添加患者数据同步任务失败: %v", err)
	} else {
		logx.Info("已添加患者数据同步任务，每小时20分执行")
	}

	// 机构数据同步（如果杭州HIS客户端已初始化）
	if s.hangzhouClient != nil {
		storeRepo := model.NewStoreRepository(s.db)
		orgSyncService := tasks.NewOrgSyncService(s.hangzhouClient, storeRepo, true, 1)

		// 每天凌晨1点执行机构同步
		_, err = s.cron.AddFunc("0 0 1 * * ?", func() {
			logx.Info("定时任务：开始机构数据同步...")
			if syncErr := orgSyncService.Start(); syncErr != nil {
				logx.Errorf("机构数据同步失败: %v", syncErr)
			} else {
				logx.Info("定时任务：机构数据同步完成")
			}
		})
		if err != nil {
			logx.Errorf("添加机构数据同步任务失败: %v", err)
		} else {
			logx.Info("已添加机构数据同步任务，每天凌晨1点执行")
		}

		// 科室数据同步
		departmentSyncService := tasks.NewDepartmentSyncService(s.hangzhouClient, true, 1)
		_, err = s.cron.AddFunc("0 0 2 * * ?", func() {
			logx.Info("定时任务：开始科室数据同步...")
			departmentSyncService.SyncDepartments()
			logx.Info("定时任务：科室数据同步完成")
		})
		if err != nil {
			logx.Errorf("添加科室数据同步任务失败: %v", err)
		} else {
			logx.Info("已添加科室数据同步任务，每天凌晨2点执行")
		}
	}
}

// addAbcYunSyncTasks 添加ABC云同步任务
func (s *EnhancedCronService) addAbcYunSyncTasks() {
	if s.abcYunClient == nil {
		logx.Info("ABC云客户端未初始化，跳过ABC云同步任务")
		return
	}

	// ABC云门店同步
	abcYunStoreSyncService := tasks.NewAbcYunStoreSyncService(s.abcYunClient)
	_, err := s.cron.AddFunc("0 0 * * * ?", func() {
		logx.Info("定时任务：开始ABC云门店同步...")
		if syncErr := abcYunStoreSyncService.SyncStores(); syncErr != nil {
			logx.Errorf("ABC云门店同步失败: %v", syncErr)
		} else {
			logx.Info("定时任务：ABC云门店同步完成")
		}
	})
	if err != nil {
		logx.Errorf("添加ABC云门店同步任务失败: %v", err)
	} else {
		logx.Info("已添加ABC云门店同步任务，每小时整点执行")
	}

	// ABC云科室同步
	abcYunDeptSyncService := tasks.NewAbcYunDepartmentSyncService(s.abcYunClient)
	_, err = s.cron.AddFunc("0 5 * * * ?", func() {
		logx.Info("定时任务：开始ABC云科室同步...")
		if syncErr := abcYunDeptSyncService.SyncDepartments(); syncErr != nil {
			logx.Errorf("ABC云科室同步失败: %v", syncErr)
		} else {
			logx.Info("定时任务：ABC云科室同步完成")
		}
	})
	if err != nil {
		logx.Errorf("添加ABC云科室同步任务失败: %v", err)
	} else {
		logx.Info("已添加ABC云科室同步任务，每小时5分执行")
	}

	// ABC云医生同步
	abcYunDoctorSyncService := tasks.NewAbcYunDoctorSyncService(s.abcYunClient)
	_, err = s.cron.AddFunc("0 10 * * * ?", func() {
		logx.Info("定时任务：开始ABC云医生同步...")
		if syncErr := abcYunDoctorSyncService.SyncDoctors(); syncErr != nil {
			logx.Errorf("ABC云医生同步失败: %v", syncErr)
		} else {
			logx.Info("定时任务：ABC云医生同步完成")
		}
	})
	if err != nil {
		logx.Errorf("添加ABC云医生同步任务失败: %v", err)
	} else {
		logx.Info("已添加ABC云医生同步任务，每小时10分执行")
	}

	// ABC云患者同步
	abcYunPatientSyncService := tasks.NewAbcYunPatientSyncSimpleService(s.abcYunClient)
	_, err = s.cron.AddFunc("0 15 * * * ?", func() {
		logx.Info("定时任务：开始ABC云患者同步...")
		if syncErr := abcYunPatientSyncService.SyncPatients(); syncErr != nil {
			logx.Errorf("ABC云患者同步失败: %v", syncErr)
		} else {
			logx.Info("定时任务：ABC云患者同步完成")
		}
	})
	if err != nil {
		logx.Errorf("添加ABC云患者同步任务失败: %v", err)
	} else {
		logx.Info("已添加ABC云患者同步任务，每小时15分执行")
	}
}

// addWanLiNiuSyncTasks 添加万里牛ERP同步任务
func (s *EnhancedCronService) addWanLiNiuSyncTasks() {
	// 万里牛商品分类同步
	wanLiNiuCategorySyncService := tasks.NewWanLiNiuCategorySyncService()
	if wanLiNiuCategorySyncService != nil {
		_, err := s.cron.AddFunc("0 20 * * * ?", func() {
			logx.Info("定时任务：开始万里牛商品分类同步...")
			if syncErr := wanLiNiuCategorySyncService.SyncCategories(); syncErr != nil {
				logx.Errorf("万里牛商品分类同步失败: %v", syncErr)
			} else {
				logx.Info("定时任务：万里牛商品分类同步完成")
			}
		})
		if err != nil {
			logx.Errorf("添加万里牛商品分类同步任务失败: %v", err)
		} else {
			logx.Info("已添加万里牛商品分类同步任务，每小时20分执行")
		}
	}

	// 万里牛商品同步
	wanLiNiuGoodsSyncService := tasks.NewWanLiNiuGoodsSyncService()
	if wanLiNiuGoodsSyncService != nil {
		_, err := s.cron.AddFunc("0 25 * * * ?", func() {
			logx.Info("定时任务：开始万里牛商品同步...")
			if syncErr := wanLiNiuGoodsSyncService.SyncGoods(); syncErr != nil {
				logx.Errorf("万里牛商品同步失败: %v", syncErr)
			} else {
				logx.Info("定时任务：万里牛商品同步完成")
			}
		})
		if err != nil {
			logx.Errorf("添加万里牛商品同步任务失败: %v", err)
		} else {
			logx.Info("已添加万里牛商品同步任务，每小时25分执行")
		}
	}

	// 万里牛库存同步
	wanLiNiuInventorySyncService := tasks.NewWanLiNiuInventorySyncService()
	if wanLiNiuInventorySyncService != nil {
		_, err := s.cron.AddFunc("0 35 * * * ?", func() {
			logx.Info("定时任务：开始万里牛库存同步...")
			if syncErr := wanLiNiuInventorySyncService.SyncInventory(); syncErr != nil {
				logx.Errorf("万里牛库存同步失败: %v", syncErr)
			} else {
				logx.Info("定时任务：万里牛库存同步完成")
			}
		})
		if err != nil {
			logx.Errorf("添加万里牛库存同步任务失败: %v", err)
		} else {
			logx.Info("已添加万里牛库存同步任务，每小时35分执行")
		}
	}

	// 万里牛发货状态同步
	wanLiNiuShippingSyncService := tasks.NewWanLiNiuShippingSyncService()
	if wanLiNiuShippingSyncService != nil {
		_, err := s.cron.AddFunc("0 */20 * * * ?", func() {
			logx.Info("定时任务：开始万里牛发货状态同步...")
			if syncErr := wanLiNiuShippingSyncService.SyncShippingStatus(); syncErr != nil {
				logx.Errorf("万里牛发货状态同步失败: %v", syncErr)
			} else {
				logx.Info("定时任务：万里牛发货状态同步完成")
			}
		})
		if err != nil {
			logx.Errorf("添加万里牛发货状态同步任务失败: %v", err)
		} else {
			logx.Info("已添加万里牛发货状态同步任务，每20分钟执行")
		}
	}

	// 万里牛失败订单推送
	wanLiNiuFailedPushService := tasks.NewWanLiNiuFailedPushService()
	if wanLiNiuFailedPushService != nil {
		_, err := s.cron.AddFunc("0 40 * * * ?", func() {
			logx.Info("定时任务：开始万里牛失败订单推送...")
			if pushErr := wanLiNiuFailedPushService.PushFailedOrders(); pushErr != nil {
				logx.Errorf("万里牛失败订单推送失败: %v", pushErr)
			} else {
				logx.Info("定时任务：万里牛失败订单推送完成")
			}
		})
		if err != nil {
			logx.Errorf("添加万里牛失败订单推送任务失败: %v", err)
		} else {
			logx.Info("已添加万里牛失败订单推送任务，每小时40分执行")
		}
	}
}

// addBusinessTasks 添加业务处理任务
func (s *EnhancedCronService) addBusinessTasks() {
	// 优惠券发放任务处理
	couponIssueTaskProcessor := tasks.NewCouponIssueTaskProcessor()
	if couponIssueTaskProcessor != nil {
		_, err := s.cron.AddFunc("0 */2 * * * ?", func() {
			logx.Info("定时任务：开始处理优惠券发放任务...")
			couponIssueTaskProcessor.ProcessTasks()
			logx.Info("定时任务：优惠券发放任务处理完成")
		})
		if err != nil {
			logx.Errorf("添加优惠券发放任务处理器失败: %v", err)
		} else {
			logx.Info("已添加优惠券发放任务处理器，每2分钟执行")
		}
	}

	// 订单自动关闭任务
	orderAutoCloseService := tasks.NewOrderAutoCloseService()
	if orderAutoCloseService != nil {
		_, err := s.cron.AddFunc("0 */2 * * * ?", func() {
			logx.Info("定时任务：开始检查并关闭过期订单...")
			if closeErr := orderAutoCloseService.CloseExpiredOrders(); closeErr != nil {
				logx.Errorf("订单自动关闭任务失败: %v", closeErr)
			} else {
				logx.Info("定时任务：订单自动关闭任务完成")
			}
		})
		if err != nil {
			logx.Errorf("添加订单自动关闭任务失败: %v", err)
		} else {
			logx.Info("已添加订单自动关闭任务，每2分钟执行")
		}
	}

	// 用户等级升级任务
	userLevelUpgradeService := tasks.NewUserLevelUpgradeService()
	if userLevelUpgradeService != nil {
		_, err := s.cron.AddFunc("0 0 * * * ?", func() {
			logx.Info("定时任务：开始用户等级升级检查...")
			userLevelUpgradeService.RunUpgradeTask()
			logx.Info("定时任务：用户等级升级检查完成")
		})
		if err != nil {
			logx.Errorf("添加用户等级升级任务失败: %v", err)
		} else {
			logx.Info("已添加用户等级升级任务，每小时整点执行")
		}
	}
}
