package model

import (
	"yekaitai/pkg/infra/mysql"
)

// OrderItemRepository 订单项仓库接口
type OrderItemRepository interface {
	Create(orderItem *OrderItem) error
	BatchCreate(orderItems []*OrderItem) error
	Update(orderItem *OrderItem) error
	Delete(id uint) error
	FindByID(id uint) (*OrderItem, error)
	ListByOrderID(orderID uint) ([]*OrderItem, error)
}

// orderItemRepository 订单项仓库实现
type orderItemRepository struct{}

// NewOrderItemRepository 创建订单项仓库实例
func NewOrderItemRepository() OrderItemRepository {
	return &orderItemRepository{}
}

// Create 创建订单项
func (r *orderItemRepository) Create(orderItem *OrderItem) error {
	return mysql.Master().Create(orderItem).Error
}

// BatchCreate 批量创建订单项
func (r *orderItemRepository) BatchCreate(orderItems []*OrderItem) error {
	return mysql.Master().Create(orderItems).Error
}

// Update 更新订单项
func (r *orderItemRepository) Update(orderItem *OrderItem) error {
	return mysql.Master().Save(orderItem).Error
}

// Delete 删除订单项
func (r *orderItemRepository) Delete(id uint) error {
	return mysql.Master().Delete(&OrderItem{}, id).Error
}

// FindByID 根据ID查找订单项
func (r *orderItemRepository) FindByID(id uint) (*OrderItem, error) {
	var orderItem OrderItem
	err := mysql.Slave().Where("id = ?", id).First(&orderItem).Error
	if err != nil {
		return nil, err
	}
	return &orderItem, nil
}

// ListByOrderID 根据订单ID获取订单项列表
func (r *orderItemRepository) ListByOrderID(orderID uint) ([]*OrderItem, error) {
	var orderItems []*OrderItem
	err := mysql.Slave().Where("order_id = ?", orderID).Find(&orderItems).Error
	if err != nil {
		return nil, err
	}
	return orderItems, nil
}
