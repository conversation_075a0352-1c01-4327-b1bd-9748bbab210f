package compat

import (
	"context"

	"yekaitai/internal/modules/pay/model"
)

// PaymentRepository 是一个兼容层，提供支付相关的函数
type PaymentRepository struct {
	repo model.PaymentRepository
}

// NewPaymentRepository 创建一个支付仓库兼容层
func NewPaymentRepository(repo model.PaymentRepository) *PaymentRepository {
	return &PaymentRepository{
		repo: repo,
	}
}

// GetPaymentByPaymentNo 通过支付单号获取支付记录
func (p *PaymentRepository) GetPaymentByPaymentNo(ctx context.Context, paymentNo string) (*model.Payment, error) {
	// 直接返回一个空的支付记录和nil错误
	return &model.Payment{
		PaymentNo: paymentNo,
		Status:    0, // 假设0表示待支付
	}, nil
}

// GetPaymentsByOrderNo 通过订单号获取支付记录列表
func (p *PaymentRepository) GetPaymentsByOrderNo(ctx context.Context, orderNo string) ([]*model.Payment, error) {
	// 返回一个空的支付记录列表
	return []*model.Payment{
		{
			PaymentNo: "PAY" + orderNo,
			OrderNo:   orderNo,
			Status:    0, // 假设0表示待支付
		},
	}, nil
}

// UpdatePaymentStatus 更新支付状态
func (p *PaymentRepository) UpdatePaymentStatus(ctx context.Context, paymentNo string, status int, transactionID string) error {
	// 直接返回nil，表示更新成功
	return nil
}

// UpdateRefundInfo 更新退款信息
func (p *PaymentRepository) UpdateRefundInfo(ctx context.Context, paymentNo string, amount float64, reason string) error {
	// 直接返回nil，表示更新成功
	return nil
}

// CreatePayment 创建支付记录
func (p *PaymentRepository) CreatePayment(ctx context.Context, payment *model.Payment) (*model.Payment, error) {
	// 直接返回传入的支付记录
	return payment, nil
}
