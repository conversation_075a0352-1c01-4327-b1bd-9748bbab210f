package handler

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"yekaitai/pkg/adapters/wanliniu"
	orderModel "yekaitai/pkg/common/model/order"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// WanLiNiuCallbackHandler 万里牛回调处理器
type WanLiNiuCallbackHandler struct {
	db *gorm.DB
}

// NewWanLiNiuCallbackHandler 创建万里牛回调处理器
func NewWanLiNiuCallbackHandler() *WanLiNiuCallbackHandler {
	return &WanLiNiuCallbackHandler{
		db: mysql.GetDB(),
	}
}

// HandleTradeShipment 处理订单发货通知 (trade_send_msg)
func (h *WanLiNiuCallbackHandler) HandleTradeShipment(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	// 解析请求体
	var notification wanliniu.TradeShipmentNotification
	if err := httpx.Parse(r, &notification); err != nil {
		logx.Errorf("解析万里牛发货通知失败: %v", err)
		httpx.WriteJson(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "参数解析失败",
		})
		return
	}

	logx.Infof("收到万里牛发货通知: 订单号=%s, 快递公司=%s, 快递单号=%s",
		notification.TradeID, notification.DeliveryCode, notification.Waybill)

	// 更新订单发货状态
	err := h.updateOrderShipment(ctx, &notification)
	if err != nil {
		logx.Errorf("更新订单发货状态失败: %v", err)
		httpx.WriteJson(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"message": "更新订单状态失败",
		})
		return
	}

	// 返回成功响应
	httpx.WriteJson(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "发货通知处理成功",
	})
}

// HandleInventoryChange 处理库存变更通知 (item_quantity_upload_msg)
func (h *WanLiNiuCallbackHandler) HandleInventoryChange(w http.ResponseWriter, r *http.Request) {
	ctx := context.Background()

	// 解析请求体 - 库存变更通知是单个商品信息，不是数组
	var notification wanliniu.InventoryChangeNotification
	if err := httpx.Parse(r, &notification); err != nil {
		logx.Errorf("解析万里牛库存变更通知失败: %v", err)
		httpx.WriteJson(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"message": "参数解析失败",
		})
		return
	}

	logx.Infof("收到万里牛库存变更通知: 商品ID=%s, 库存=%f", notification.ItemID, notification.Quantity)

	// 更新商品库存
	err := h.updateSingleProductInventory(ctx, &notification)
	if err != nil {
		logx.Errorf("更新商品库存失败: %v", err)
		httpx.WriteJson(w, http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"message": "更新库存失败",
		})
		return
	}

	// 返回成功响应
	httpx.WriteJson(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "库存变更通知处理成功",
	})
}

// updateOrderShipment 更新订单发货状态
func (h *WanLiNiuCallbackHandler) updateOrderShipment(ctx context.Context, notification *wanliniu.TradeShipmentNotification) error {
	return h.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 查找订单
		var order orderModel.Order
		err := tx.Where("order_no = ?", notification.TradeID).First(&order).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				logx.Errorf("订单不存在: %s", notification.TradeID)
				return nil // 订单不存在，不处理
			}
			return fmt.Errorf("查询订单失败: %w", err)
		}

		// 检查订单状态
		if order.Status != orderModel.OrderStatusPaid {
			logx.Errorf("订单状态不符合发货条件: 订单号=%s, 状态=%s", order.OrderNo, order.Status)
			return nil
		}

		// 更新订单状态为已发货
		now := time.Now()
		updates := map[string]interface{}{
			"status":         orderModel.OrderStatusShipped,
			"ship_time":      &now,
			"logistics_code": notification.DeliveryCode,
			"logistics_no":   notification.Waybill,
			"logistics_name": notification.DeliveryName,
		}

		err = tx.Model(&order).Updates(updates).Error
		if err != nil {
			return fmt.Errorf("更新订单发货状态失败: %w", err)
		}

		logx.Infof("订单发货状态更新成功: 订单号=%s", order.OrderNo)
		return nil
	})
}

// updateSingleProductInventory 更新单个商品库存
func (h *WanLiNiuCallbackHandler) updateSingleProductInventory(ctx context.Context, notification *wanliniu.InventoryChangeNotification) error {
	return h.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 根据万里牛商品编码更新库存
		err := tx.Exec(`
			UPDATE goods 
			SET stock = ?, 
			    updated_at = NOW()
			WHERE wln_item_id = ?
		`, notification.Quantity, notification.ItemID).Error

		if err != nil {
			logx.Errorf("更新商品库存失败: 商品ID=%s, 错误=%v", notification.ItemID, err)
			return fmt.Errorf("更新商品库存失败: %w", err)
		}

		logx.Infof("商品库存更新成功: 商品ID=%s, 库存=%f", notification.ItemID, notification.Quantity)
		return nil
	})
}
