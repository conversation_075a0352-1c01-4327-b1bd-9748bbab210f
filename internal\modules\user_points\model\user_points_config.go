package model

import (
	"time"
)

// CoinGlobalConfig 叶小币全局配置
type CoinGlobalConfig struct {
	ID                uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:配置ID"`
	Enabled           bool      `json:"enabled" gorm:"default:true;comment:启用叶小币开关"`
	ExchangeRate      int       `json:"exchange_rate" gorm:"default:1;comment:抵现规则:1币1元"`
	CreatedAt         time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt         time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (cgc *CoinGlobalConfig) TableName() string {
	return "coin_global_config"
}

// UserCoins 用户叶小币余额
type UserCoins struct {
	ID              uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:记录ID"`
	UserID          uint      `json:"user_id" gorm:"not null;uniqueIndex:idx_user_coins_user_id;comment:用户ID"`
	TotalCoins      int       `json:"total_coins" gorm:"default:0;comment:总叶小币数量"`
	AvailableCoins  int       `json:"available_coins" gorm:"default:0;comment:可用叶小币数量"`
	FrozenCoins     int       `json:"frozen_coins" gorm:"default:0;comment:冻结叶小币数量"`
	UsedCoins       int       `json:"used_coins" gorm:"default:0;comment:已使用叶小币数量"`
	CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 返回表名
func (uc *UserCoins) TableName() string {
	return "user_coins"
}

// CoinTransactions 叶小币交易记录
type CoinTransactions struct {
	ID              uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:交易ID"`
	UserID          uint      `json:"user_id" gorm:"not null;index:idx_coin_transactions_user_id;comment:用户ID"`
	TransactionType string    `json:"transaction_type" gorm:"type:varchar(50);not null;comment:交易类型"`
	Amount          int       `json:"amount" gorm:"not null;comment:交易金额(正数为收入,负数为支出)"`
	Balance         int       `json:"balance" gorm:"not null;comment:交易后余额"`
	RuleID          uint      `json:"rule_id" gorm:"default:0;comment:关联规则ID"`
	OrderID         uint      `json:"order_id" gorm:"default:0;comment:关联订单ID"`
	Description     string    `json:"description" gorm:"type:varchar(255);comment:交易描述"`
	ExpiresAt       *time.Time `json:"expires_at" gorm:"comment:过期时间"`
	CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
}

// TableName 返回表名
func (ct *CoinTransactions) TableName() string {
	return "coin_transactions"
}

// CoinOneTimeRewards 一次性奖励记录
type CoinOneTimeRewards struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:记录ID"`
	UserID    uint      `json:"user_id" gorm:"not null;index:idx_coin_one_time_rewards_user_id;comment:用户ID"`
	RuleID    uint      `json:"rule_id" gorm:"not null;comment:规则ID"`
	RuleType  string    `json:"rule_type" gorm:"type:varchar(50);not null;comment:规则类型"`
	Awarded   bool      `json:"awarded" gorm:"default:false;comment:是否已奖励"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
}

// TableName 返回表名
func (cotr *CoinOneTimeRewards) TableName() string {
	return "coin_one_time_rewards"
}

// CoinCheckinRecords 打卡记录
type CoinCheckinRecords struct {
	ID          uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:记录ID"`
	UserID      uint      `json:"user_id" gorm:"not null;index:idx_coin_checkin_records_user_id;comment:用户ID"`
	CheckinDate string    `json:"checkin_date" gorm:"type:varchar(10);not null;comment:打卡日期(YYYY-MM-DD)"`
	Shared      bool      `json:"shared" gorm:"default:false;comment:是否已分享"`
	CoinsAwarded int      `json:"coins_awarded" gorm:"default:0;comment:奖励叶小币数量"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
}

// TableName 返回表名
func (ccr *CoinCheckinRecords) TableName() string {
	return "coin_checkin_records"
}

// 规则类型常量
const (
	RuleTypeRegister         = "REGISTER"           // 注册奖励
	RuleTypeCompleteProfile  = "COMPLETE_PROFILE"   // 完善信息
	RuleTypeActivity         = "ACTIVITY"           // 活动奖励
	RuleTypeCheckin          = "CHECKIN"            // 打卡奖励
	RuleTypeCheckinShare     = "CHECKIN_SHARE"      // 打卡分享奖励
	RuleTypeReferral         = "REFERRAL"           // 推荐奖励
	RuleTypeConsume          = "CONSUME"            // 消费奖励
	RuleTypeConsumeAccumulate = "CONSUME_ACCUMULATE" // 消费累计奖励
	RuleTypeReferralConsume  = "REFERRAL_CONSUME"   // 推荐人消费奖励
)

// 期限类型常量
const (
	ExpiryTypePermanent = "PERMANENT" // 永久
	ExpiryTypeYearly    = "YEARLY"    // 逐年
	ExpiryTypeMonthly   = "MONTHLY"   // 逐月
	ExpiryTypeCustom    = "CUSTOM"    // 自定义日期
)

// 交易类型常量
const (
	TransactionTypeEarn   = "EARN"   // 获得
	TransactionTypeSpend  = "SPEND"  // 消费
	TransactionTypeRefund = "REFUND" // 退款
	TransactionTypeExpire = "EXPIRE" // 过期
)

// 活动类型常量
const (
	ActivityTypeGeneral    = "GENERAL"     // 普通活动
	ActivityTypeSpecial    = "SPECIAL"     // 专项活动
	ActivityTypePromotion  = "PROMOTION"   // 促销活动
	ActivityTypeEducation  = "EDUCATION"   // 教育活动
)

// CreateCoinGlobalConfigRequest 创建全局配置请求
type CreateCoinGlobalConfigRequest struct {
	Enabled          bool   `json:"enabled" label:"启用叶小币开关"`
	ExchangeRate     int    `json:"exchange_rate" validate:"min=1" label:"抵现规则"`
}

// UpdateCoinGlobalConfigRequest 更新全局配置请求
type UpdateCoinGlobalConfigRequest struct {
	ID               uint   `json:"id" validate:"required" label:"配置ID"`
	Enabled          bool   `json:"enabled" label:"启用叶小币开关"`
	ExchangeRate     int    `json:"exchange_rate" validate:"min=1" label:"抵现规则"`
}

// CreateCoinRuleRequest 创建规则请求
type CreateCoinRuleRequest struct {
	UserLevelID      uint   `json:"user_level_id" validate:"required" label:"用户等级ID"`
	RuleType         string `json:"rule_type" validate:"required" label:"规则类型"`
	RuleName         string `json:"rule_name" validate:"required,max=100" label:"规则名称"`
	Description      string `json:"description,optional" label:"规则描述"`
	Enabled          bool   `json:"enabled" label:"是否启用"`
	CoinsAwarded     int    `json:"coins_awarded" validate:"min=0" label:"奖励叶小币数量"`
	MinAmount        int    `json:"min_amount,optional" validate:"min=0" label:"最小金额(分)"`
	AmountThreshold  int    `json:"amount_threshold,optional" validate:"min=0" label:"金额阈值(分)"`
	IsOneTime        bool   `json:"is_one_time" label:"是否一次性奖励"`
	RequireShare     bool   `json:"require_share" label:"是否需要分享"`
	ActivityType     string `json:"activity_type,optional" label:"活动类型"`
	ExpiryPolicyType string `json:"expiry_policy_type" validate:"required,oneof=PERMANENT YEARLY MONTHLY CUSTOM" label:"期限类型"`
	CustomYears      int    `json:"custom_years,optional" validate:"min=0" label:"自定义有效期(年)"`
}

// UpdateCoinRuleRequest 更新规则请求
type UpdateCoinRuleRequest struct {
	ID               uint   `json:"id" validate:"required" label:"规则ID"`
	UserLevelID      uint   `json:"user_level_id" validate:"required" label:"用户等级ID"`
	RuleType         string `json:"rule_type" validate:"required" label:"规则类型"`
	RuleName         string `json:"rule_name" validate:"required,max=100" label:"规则名称"`
	Description      string `json:"description,optional" label:"规则描述"`
	Enabled          bool   `json:"enabled" label:"是否启用"`
	CoinsAwarded     int    `json:"coins_awarded" validate:"min=0" label:"奖励叶小币数量"`
	MinAmount        int    `json:"min_amount,optional" validate:"min=0" label:"最小金额(分)"`
	AmountThreshold  int    `json:"amount_threshold,optional" validate:"min=0" label:"金额阈值(分)"`
	IsOneTime        bool   `json:"is_one_time" label:"是否一次性奖励"`
	RequireShare     bool   `json:"require_share" label:"是否需要分享"`
	ActivityType     string `json:"activity_type,optional" label:"活动类型"`
	ExpiryPolicyType string `json:"expiry_policy_type" validate:"required,oneof=PERMANENT YEARLY MONTHLY CUSTOM" label:"期限类型"`
	CustomYears      int    `json:"custom_years,optional" validate:"min=0" label:"自定义有效期(年)"`
}

// CoinRuleListRequest 规则列表请求
type CoinRuleListRequest struct {
	UserLevelID uint   `form:"user_level_id,optional" label:"用户等级ID"`
	RuleType    string `form:"rule_type,optional" label:"规则类型"`
	Enabled     *bool  `form:"enabled,optional" label:"是否启用"`
	Page        int    `form:"page,default=1" label:"页码"`
	Size        int    `form:"size,default=10" label:"每页数量"`
}

// CoinGlobalConfigResponse 全局配置响应
type CoinGlobalConfigResponse struct {
	ID               uint      `json:"id"`
	Enabled          bool      `json:"enabled"`
	ExchangeRate     int       `json:"exchange_rate"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// CoinRuleResponse 规则响应
type CoinRuleResponse struct {
	ID               uint      `json:"id"`
	UserLevelID      uint      `json:"user_level_id"`
	RuleType         string    `json:"rule_type"`
	RuleName         string    `json:"rule_name"`
	Description      string    `json:"description"`
	Enabled          bool      `json:"enabled"`
	CoinsAwarded     int       `json:"coins_awarded"`
	MinAmount        int       `json:"min_amount"`
	AmountThreshold  int       `json:"amount_threshold"`
	IsOneTime        bool      `json:"is_one_time"`
	RequireShare     bool      `json:"require_share"`
	ActivityType     string    `json:"activity_type"`
	ExpiryPolicyType string    `json:"expiry_policy_type"`
	CustomYears      int       `json:"custom_years"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// SyncToAllLevelsRequest 同步到所有等级请求
type SyncToAllLevelsRequest struct {
	SourceLevelID uint `json:"source_level_id" validate:"required" label:"源等级ID"`
}