package bootstrap

import (
	"fmt"
	"log"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// 运行积分配置表结构迁移
func RunPointsExpiryPolicyMigration() {
	log.Println("开始执行积分配置表结构迁移...")
	db := mysql.GetDB()

	// 在事务中执行迁移
	err := db.Transaction(func(tx *gorm.DB) error {
		// 添加新字段到 coin_rules 表
		if err := addColumnsIfNotExist(tx, "coin_rules", map[string]string{
			"expiry_policy_type": "VARCHAR(20) DEFAULT 'PERMANENT' COMMENT '期限类型:PERMANENT永久,YEARLY逐年,MONTHLY逐月,CUSTOM自定义'",
			"custom_years":       "INT DEFAULT 0 COMMENT '自定义有效期(年)'",
		}); err != nil {
			return err
		}

		log.Println("积分配置表结构迁移完成")
		return nil
	})

	if err != nil {
		log.Fatalf("积分配置表结构迁移失败: %v", err)
	}
}

// 如果列不存在则添加
func addColumnsIfNotExist(tx *gorm.DB, tableName string, columns map[string]string) error {
	for columnName, columnDef := range columns {
		var count int64
		tx.Raw(`SELECT COUNT(*) FROM information_schema.columns 
			WHERE table_schema = DATABASE() 
			AND table_name = ? 
			AND column_name = ?`, tableName, columnName).Scan(&count)

		if count == 0 {
			if err := tx.Exec(fmt.Sprintf("ALTER TABLE %s ADD COLUMN %s %s", tableName, columnName, columnDef)).Error; err != nil {
				return fmt.Errorf("添加列 %s 到表 %s 失败: %v", columnName, tableName, err)
			}
			log.Printf("已添加列 %s 到表 %s", columnName, tableName)
		}
	}
	return nil
} 