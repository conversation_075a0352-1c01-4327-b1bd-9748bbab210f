package types

import (
	"time"

	"yekaitai/wx_internal/types"
)

// WxPatientInfo 患者信息
type WxPatientInfo struct {
	PatientID      uint      `json:"patient_id"`      // 患者ID
	UserID         uint      `json:"user_id"`         // 用户ID
	OpenID         string    `json:"open_id"`         // 微信 OpenID
	Mobile         string    `json:"mobile"`          // 手机号
	Nickname       string    `json:"nickname"`        // 昵称
	Avatar         string    `json:"avatar"`          // 头像
	Gender         int       `json:"gender"`          // 性别 0-未知 1-男 2-女
	MedicalHistory string    `json:"medical_history"` // 病史
	Allergies      string    `json:"allergies"`       // 过敏史
	Status         int       `json:"status"`          // 状态 1-正常 0-禁用
	CreatedAt      time.Time `json:"created_at"`      // 创建时间
	UpdatedAt      time.Time `json:"updated_at"`      // 更新时间
}

// CreatePatientRequest 创建患者请求
type CreatePatientRequest struct {
	UserID         uint   `json:"user_id" binding:"required"` // 用户ID
	MedicalHistory string `json:"medical_history,omitempty"`  // 病史(可选)
	Allergies      string `json:"allergies,omitempty"`        // 过敏史(可选)
}

// UpdatePatientRequest 更新患者请求
type UpdatePatientRequest struct {
	MedicalHistory string `json:"medical_history,omitempty"` // 病史(可选)
	Allergies      string `json:"allergies,omitempty"`       // 过敏史(可选)
	Status         *int   `json:"status,omitempty"`          // 状态(可选) 1-正常 0-禁用
}

// GetPatientRequest 获取患者请求
type GetPatientRequest struct {
	PatientID uint `path:"patient_id" binding:"required"` // 患者ID
}

// GetPatientByUserIDRequest 通过用户ID获取患者请求
type GetPatientByUserIDRequest struct {
	UserID uint `path:"user_id" binding:"required"` // 用户ID
}

// ListPatientsRequest 患者列表请求
type ListPatientsRequest struct {
	types.PaginationRequest
	Nickname string `form:"nickname,optional"` // 昵称，模糊查询
	Mobile   string `form:"mobile,optional"`   // 手机号，精确查询
	Status   *int   `form:"status,optional"`   // 状态，可选 1-正常 0-禁用
}

// ChangePatientStatusRequest 变更患者状态请求
type ChangePatientStatusRequest struct {
	PatientID uint `json:"patient_id" binding:"required"` // 患者ID
	Status    int  `json:"status" binding:"required"`     // 状态 1-正常 0-禁用
}

// BatchImportPatientsRequest 批量导入患者请求
type BatchImportPatientsRequest struct {
	Patients []CreatePatientRequest `json:"patients" binding:"required"` // 患者列表
}

// PatientIDResponse 患者ID响应
type PatientIDResponse struct {
	PatientID uint `json:"patient_id"` // 患者ID
}
