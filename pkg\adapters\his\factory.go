package his

import (
	"fmt"
	"yekaitai/internal/config"
	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/adapters/chongqing"
)

// ClientFactory HIS客户端工厂实现
type ClientFactory struct {
	Config          *config.Config
	AbcYunClient    *abcyun.AbcYunClient
	ChongqingClient *chongqing.Client
}

// NewClientFactory 创建HIS客户端工厂
func NewClientFactory(config *config.Config, abcYunClient *abcyun.AbcYunClient) *ClientFactory {
	// 创建重庆HIS客户端
	var chongqingClient *chongqing.Client
	if config.Chongqing.BaseURL != "" {
		chongqingClient = chongqing.NewClient(&config.Chongqing)
	}

	return &ClientFactory{
		Config:          config,
		AbcYunClient:    abcYunClient,
		ChongqingClient: chongqingClient,
	}
}

// Create 根据提供商类型创建对应的HIS客户端
func (f *ClientFactory) Create(providerType ProviderType) (Client, error) {
	switch providerType {
	case ProviderABCYun:
		return NewABCClient(f.AbcYunClient), nil
	case ProviderChongQing:
		if f.ChongqingClient == nil {
			return nil, fmt.Errorf("重庆HIS客户端未初始化，请检查配置")
		}
		return NewChongQingClient(f.ChongqingClient), nil
	default:
		return nil, fmt.Errorf("unknown provider type: %s", providerType)
	}
}
