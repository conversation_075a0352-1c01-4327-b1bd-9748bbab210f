package abcyun

import (
	"yekaitai/internal/config"

	"github.com/zeromicro/go-zero/core/logx"
)

// Client 是ABC云API客户端，用于兼容旧代码
var Client *AbcYunClient

var (
	cfg config.AbcYunConfig
)

// Init 初始化ABC云客户端（保留用于向后兼容）
func Init(conf config.AbcYunConfig) {
	logx.Info("初始化ABC云客户端...")
	Client = NewClient(&conf)
	// 同时设置全局客户端，用于新API
	InitClient(Client)
	logx.Infof("ABC云客户端初始化成功 - baseURL: %s, appID: %s", conf.BaseURL, conf.AppID)
}

// GetClient 获取ABC云客户端实例
func GetClient() *AbcYunClient {
	return Client
}

// StartSimpleSync 启动简化版同步任务
func StartSimpleSync() {
	logx.Info("启动简化版同步任务")
	// 这里只是一个空实现，实际项目中应该实现具体的同步逻辑
}
