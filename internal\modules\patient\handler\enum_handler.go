package handler

import (
	"net/http"

	"yekaitai/internal/models/enum"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// EnumHandler 枚举处理器
type EnumHandler struct {
	svcCtx *svc.ServiceContext
}

// NewEnumHandler 创建枚举处理器
func NewEnumHandler(svcCtx *svc.ServiceContext) *EnumHandler {
	return &EnumHandler{
		svcCtx: svcCtx,
	}
}

// GetEthnicities 获取民族列表（包含编码和名称）
func (h *EnumHandler) GetEthnicities(w http.ResponseWriter, r *http.Request) {
	ethnicityInfos := enum.GetAllEthnicityInfos()

	// 转换为前端可用的格式
	result := make([]map[string]interface{}, 0, len(ethnicityInfos))
	for _, info := range ethnicityInfos {
		result = append(result, map[string]interface{}{
			"code":  string(info.Code), // 民族编码
			"name":  string(info.Name), // 民族名称
			"value": string(info.Name), // 兼容旧格式
			"label": string(info.Name), // 兼容旧格式
		})
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetRelationships 获取与本人关系列表
func (h *EnumHandler) GetRelationships(w http.ResponseWriter, r *http.Request) {
	relationships := enum.AllRelationships()

	// 转换为前端可用的格式
	result := make([]map[string]string, 0, len(relationships))
	for _, rel := range relationships {
		result = append(result, map[string]string{
			"value": string(rel),
			"label": string(rel),
		})
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}
