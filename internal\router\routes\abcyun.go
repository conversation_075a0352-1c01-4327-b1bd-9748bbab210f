package routes

import (
	"net/http"

	abcyunhandler "yekaitai/internal/modules/appointment/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterAbcYunRoutes 注册ABC云接口路由
func RegisterAbcYunRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建患者处理函数结构体实例
	patientHandler := abcyunhandler.NewAbcYunHandler(serverCtx)

	// 添加路由
	server.AddRoutes(
		[]rest.Route{
			// ABC云API路由
			{
				Method:  http.MethodGet,
				Path:    "/api/test/abcyun",
				Handler: patientHandler.AbcYunTestHandler,
			},
			// 基础信息接口
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/departments",
				Handler: patientHandler.AbcYunDepartmentsHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/department/:id",
				Handler: patientHandler.AbcYunDepartmentDetailHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/clinic",
				Handler: patientHandler.AbcYunClinicInfoHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/employees",
				Handler: patientHandler.AbcYunEmployeesHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/employee/:id",
				Handler: patientHandler.AbcYunEmployeeDetailHandler,
			},
			// 医生接口
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/doctor/mobile/:mobile",
				Handler: patientHandler.AbcYunDoctorByMobileHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/doctors",
				Handler: patientHandler.AbcYunDoctorListHandler,
			},
			// 同步接口
			{
				Method:  http.MethodPost,
				Path:    "/api/abcyun/sync/departments",
				Handler: patientHandler.AbcYunSyncDepartmentsHandler,
			},

			// 患者接口 - 使用结构体方法
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/patients",
				Handler: patientHandler.AbcYunPatientListHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/patient/query",
				Handler: patientHandler.AbcYunPatientQueryHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/patient/:id",
				Handler: patientHandler.AbcYunPatientDetailHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/patient/source/types",
				Handler: patientHandler.AbcYunPatientSourceTypesHandler,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/abcyun/patient",
				Handler: patientHandler.AbcYunCreatePatientHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/patient/:id",
				Handler: patientHandler.AbcYunUpdatePatientHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/patient/:patientId/family-member",
				Handler: patientHandler.AbcYunPatientFamilyMembersHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/patient/:patientId/attachments",
				Handler: patientHandler.AbcYunPatientAttachmentsHandler,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/abcyun/patient/:patientId/attachments",
				Handler: patientHandler.AbcYunAddPatientAttachmentsHandler,
			},
			{
				Method:  http.MethodDelete,
				Path:    "/api/abcyun/patient/:patientId/attachments/:attachmentId",
				Handler: patientHandler.AbcYunPatientDeleteAttachmentHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/patient/members/types",
				Handler: patientHandler.AbcYunPatientMemberTypesHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/patient/members/:memberCardId/pay",
				Handler: patientHandler.AbcYunMemberCardPayHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/patient/members/:memberCardId/refund",
				Handler: patientHandler.AbcYunMemberCardRefundHandler,
			},

			// 挂号接口 - 14个挂号相关接口
			// 5.5.1. 获取科室医生的号源详情
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/doctor/:doctorId",
				Handler: abcyunhandler.AbcYunDoctorScheduleHandler,
			},
			// 5.5.2. 创建挂号
			{
				Method:  http.MethodPost,
				Path:    "/api/abcyun/registration",
				Handler: abcyunhandler.AbcYunCreateRegistrationHandler,
			},
			// 5.5.3. 获取挂号详情
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/:registrationSheetId",
				Handler: abcyunhandler.AbcYunRegistrationDetailHandler,
			},
			// 5.5.4. 通过就诊单ID获取挂号详情
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/by-patient-order-id/:patientOrderId",
				Handler: abcyunhandler.AbcYunRegistrationByPatientOrderIDHandler,
			},
			// 5.5.5. 取消挂号
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/registration/:registrationSheetId/cancel",
				Handler: abcyunhandler.AbcYunCancelRegistrationHandler,
			},
			// 5.5.6. 查询预约备注模板
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/remark-templates",
				Handler: abcyunhandler.AbcYunRemarkTemplatesHandler,
			},
			// 5.5.7. 查询医生可预约项目列表
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/doctor/:doctorId/product",
				Handler: abcyunhandler.AbcYunDoctorRegistrationProductsHandler,
			},
			// 5.5.8. 查询门店可预约项目列表
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/product",
				Handler: abcyunhandler.AbcYunRegistrationProductsHandler,
			},
			// 5.5.9. 查询项目每日号源
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/product/:registrationProductId/section-status",
				Handler: abcyunhandler.AbcYunRegistrationProductSectionStatusHandler,
			},
			// 5.5.10. 查询项目指定日期排班信息
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/product/:registrationProductId/days-shifts",
				Handler: abcyunhandler.AbcYunRegistrationProductDaysShiftsHandler,
			},
			// 5.5.11. 查询患者预约列表
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/patient/:patientId",
				Handler: abcyunhandler.AbcYunPatientRegistrationsHandler,
			},
			// 5.5.12. 查询门店指定日期医生号源状态
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/doctors-shift-status",
				Handler: abcyunhandler.AbcYunDoctorsShiftStatusHandler,
			},
			// 5.5.13. 查询门店指定医生号源日期列表
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registration/doctor/:doctorId/shift",
				Handler: abcyunhandler.AbcYunDoctorShiftHandler,
			},
			// 5.5.14. 查询挂号单列表
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/registrations",
				Handler: abcyunhandler.AbcYunRegistrationsHandler,
			},
			// 门诊接口
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/outpatient/query-by-patient/:patientId",
				Handler: abcyunhandler.AbcYunOutpatientQueryByPatientHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/outpatient/:id",
				Handler: abcyunhandler.AbcYunOutpatientDetailHandler,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/abcyun/outpatient",
				Handler: abcyunhandler.AbcYunCreateOutpatientHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/outpatient/query-by-date",
				Handler: abcyunhandler.AbcYunOutpatientQueryByDateHandler,
			},
			// 收费相关路由
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/charge/query-by-date",
				Handler: abcyunhandler.AbcYunChargeQueryByDateHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/charge/query-detail/:id",
				Handler: abcyunhandler.AbcYunChargeQueryDetailHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/charge/:chargeSheetId/pay",
				Handler: abcyunhandler.AbcYunChargePayHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/charge/:chargeSheetId/refund",
				Handler: abcyunhandler.AbcYunChargeRefundHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/charge/:chargeSheetId/delivery",
				Handler: abcyunhandler.AbcYunChargeDeliveryHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/charge/by-patient-order-id/:patientOrderId",
				Handler: abcyunhandler.AbcYunChargeByPatientOrderIDHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/charge/patient/:patientId",
				Handler: abcyunhandler.AbcYunChargeByPatientHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/charge/:chargeSheetId/delivery/cancel",
				Handler: abcyunhandler.AbcYunChargeCancelDeliveryHandler,
			},

			// 发药相关路由
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/dispensing/:id",
				Handler: abcyunhandler.AbcYunDispensingDetailHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/dispensing/query-by-date",
				Handler: abcyunhandler.AbcYunDispensingQueryByDateHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/dispensing/:dispenseSheetId/dispense-all",
				Handler: abcyunhandler.AbcYunDispensingDispenseAllHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/dispensing/:dispenseSheetId/undispense-all",
				Handler: abcyunhandler.AbcYunDispensingUndispenseAllHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/dispensing/processing-status",
				Handler: abcyunhandler.AbcYunDispensingProcessingStatusHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/dispensing/delivery",
				Handler: abcyunhandler.AbcYunDispensingDeliveryHandler,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/dispensing/delivery/trace/:deliveryOrderNo",
				Handler: abcyunhandler.AbcYunDispensingDeliveryTraceHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/dispensing/by-charge-sheet-id/:id",
				Handler: abcyunhandler.AbcYunDispensingByChargeSheetIdHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/dispensing/patient/:patientId",
				Handler: abcyunhandler.AbcYunDispensingByPatientHandler,
			},
			// ABC云发药相关接口 - 结束

			// ABC云检验检查相关接口
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/examination/query-by-patient/:patientId",
				Handler: abcyunhandler.AbcYunExaminationQueryByPatientHandler,
			},
			// 按时间查询检查检验单
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/examination/all/query-by-date",
				Handler: abcyunhandler.AbcYunExaminationQueryByDateHandler,
			},
			// 按就诊号查询检查检验单
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/examination/query-by-patient-order-no",
				Handler: abcyunhandler.AbcYunExaminationQueryByPatientOrderNoHandler,
			},
			// 查询检查检验单详情
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/examination/examination-sheet-detail/:id",
				Handler: abcyunhandler.AbcYunExaminationDetailHandler,
			},
			// 按单号查询检查检验单(条形码)
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/examination/by-order-no/:orderNo/:type",
				Handler: abcyunhandler.AbcYunExaminationByOrderNoHandler,
			},
			// 根据检验单号修改检验单信息
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/examination/by-order-no/:orderNo/:type",
				Handler: abcyunhandler.AbcYunExaminationUpdateByOrderNoHandler,
			},
			// 保存检查检验单数据
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/examination/examination-sheet-detail/:id",
				Handler: abcyunhandler.AbcYunExaminationUpdateHandler,
			},
			// 创建检查检验单
			{
				Method:  http.MethodPost,
				Path:    "/api/abcyun/examination",
				Handler: abcyunhandler.AbcYunExaminationCreateHandler,
			},
			// 作废检查检验单
			{
				Method:  http.MethodPut,
				Path:    "/api/abcyun/examination/refund/:id",
				Handler: abcyunhandler.AbcYunExaminationRefundHandler,
			},
			// 获取检查检验设备列表
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/examination/devices/:type",
				Handler: abcyunhandler.AbcYunExaminationDevicesHandler,
			},

			// ABC云统计相关接口
			// 收费明细-单据
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/stat/revenue/detail/transaction",
				Handler: abcyunhandler.AbcYunStatRevenueDetailTransactionHandler,
			},
			// 收费明细-分类
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/stat/revenue/detail/classify",
				Handler: abcyunhandler.AbcYunStatRevenueDetailClassifyHandler,
			},
			// 收费明细-明细
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/stat/revenue/detail/items",
				Handler: abcyunhandler.AbcYunStatRevenueDetailItemsHandler,
			},
			// 执行业绩-明细
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/stat/execute/detail/items",
				Handler: abcyunhandler.AbcYunStatExecuteDetailItemsHandler,
			},
			// 开卡充值业绩-明细
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/stat/card-recharge/detail/items",
				Handler: abcyunhandler.AbcYunStatCardRechargeDetailItemsHandler,
			},
			// 会员充值业绩-明细
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/stat/member-recharge/detail/items",
				Handler: abcyunhandler.AbcYunStatMemberRechargeDetailItemsHandler,
			},
			// 事件推送接口
			{
				Method:  http.MethodGet,
				Path:    "/api/v2/open-agency/app-callback/:appId/fail",
				Handler: abcyunhandler.AbcYunAppCallbackFailHandler,
			},

			// 平台接口
			{
				Method:  http.MethodGet,
				Path:    "/api/v2/open-agency/property/address/china",
				Handler: abcyunhandler.AbcYunPropertyChinaAddressHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/v2/open-agency/property/oss/token",
				Handler: abcyunhandler.AbcYunPropertyOssTokenHandler,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v2/open-agency/property/report/alert",
				Handler: abcyunhandler.AbcYunPropertyReportAlertHandler,
			},

			// 执行接口
			{
				Method:  http.MethodGet,
				Path:    "/api/v2/open-agency/execute/query-by-date",
				Handler: abcyunhandler.AbcYunExecuteQueryByDateHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/v2/open-agency/execute/:id",
				Handler: abcyunhandler.AbcYunExecuteDetailHandler,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v2/open-agency/execute/:id/do",
				Handler: abcyunhandler.AbcYunExecuteDoHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/v2/open-agency/execute/:id/record",
				Handler: abcyunhandler.AbcYunExecuteRecordHandler,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v2/open-agency/execute/:id/record/:executeRecordId/undo",
				Handler: abcyunhandler.AbcYunExecuteUndoHandler,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/v2/open-agency/execute/patient/:patientId",
				Handler: abcyunhandler.AbcYunExecutePatientHandler,
			},

			// ABC云商品接口
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/types", // 获取商品分类
				Handler: abcyunhandler.AbcYunProductTypesHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/types
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/custom-types", // 获取商品自定义分类列表
				Handler: abcyunhandler.AbcYunProductCustomTypesHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/custom-types
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products", // 获取商品列表
				Handler: abcyunhandler.AbcYunProductListHandler(serverCtx),
				// API: GET /api/v2/open-agency/products
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/:id", // 获取商品详情
				Handler: abcyunhandler.AbcYunProductDetailHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/{id}
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/out-orders", // 获取出库单列表
				Handler: abcyunhandler.AbcYunProductOutOrderListHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/out-order-list
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/out-orders/:orderId", // 获取出库单详情
				Handler: abcyunhandler.AbcYunProductOutOrderDetailHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/out-order-detail/{orderId}
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/in-orders", // 获取入库单列表
				Handler: abcyunhandler.AbcYunProductInOrderListHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/in-order-list
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/in-orders/:orderId", // 获取入库单详情
				Handler: abcyunhandler.AbcYunProductInOrderDetailHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/in-order-detail/{orderId}
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/check-orders", // 获取盘点单列表
				Handler: abcyunhandler.AbcYunProductCheckOrderListHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/check-order-list
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/abcyun/products/check-orders/:orderId", // 获取盘点单详情
				Handler: abcyunhandler.AbcYunProductCheckOrderDetailHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/check-order-detail/{orderId}
			},
			// ABC云处方报警打印
			//server.AddRoutes([]rest.Route{
			//	{
			//		Method:  http.MethodPost,
			//		Path:    "/api/v2/open-agency/prescription/report/print",
			//		Handler: handler.AbcYunPrescriptionReportPrintHandler(serverCtx),
			//	},
			//})
			{
				Method:  http.MethodGet, // 5.3.11. 获取结算单列表
				Path:    "/api/abcyun/products/settlement-orders",
				Handler: abcyunhandler.AbcYunSettlementOrderListHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/settlement-order-list
			},
			{
				Method:  http.MethodGet, // 5.3.12. 获取结算单详情
				Path:    "/api/abcyun/products/settlement-orders/:orderId",
				Handler: abcyunhandler.AbcYunSettlementOrderDetailHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/settlement-order-detail/{orderId}
			},
			{
				Method:  http.MethodGet, // 5.3.13. 获取供应商列表
				Path:    "/api/abcyun/products/suppliers",
				Handler: abcyunhandler.AbcYunSupplierListHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/suppliers
			},
			{
				Method:  http.MethodPost, // 5.3.14. 获取进销存明细
				Path:    "/api/abcyun/products/inout-stock-details",
				Handler: abcyunhandler.AbcYunInOutStockDetailsHandler(serverCtx),
				// API: POST /api/v2/open-agency/products/inout-stock-details
			},
			{
				Method:  http.MethodPost, // 5.3.15. 查询商品库存
				Path:    "/api/abcyun/products/stock/query-by-id",
				Handler: abcyunhandler.AbcYunQueryProductStockHandler(serverCtx),
				// API: POST /api/v2/open-agency/products/stock/query-by-id
			},
			{
				Method:  http.MethodPost, // 5.3.16. 查询商品批次信息
				Path:    "/api/abcyun/products/batches/query-by-id",
				Handler: abcyunhandler.AbcYunQueryProductBatchesHandler(serverCtx),
				// API: POST /api/v2/open-agency/products/batches/query-by-id
			},
			{
				Method:  http.MethodPost, // 5.3.17. 新增供应商
				Path:    "/api/abcyun/products/suppliers",
				Handler: abcyunhandler.AbcYunCreateSupplierHandler(serverCtx),
				// API: POST /api/v2/open-agency/products/suppliers
			},
			{
				Method:  http.MethodPut, // 5.3.18. 修改供应商
				Path:    "/api/abcyun/products/suppliers/:id",
				Handler: abcyunhandler.AbcYunUpdateSupplierHandler(serverCtx),
				// API: PUT /api/v2/open-agency/products/suppliers/{id}
			},
			{
				Method:  http.MethodPost, // 5.3.19. 新增商品
				Path:    "/api/abcyun/products/create",
				Handler: abcyunhandler.AbcYunCreateProductHandler(serverCtx),
				// API: POST /api/v2/open-agency/products
			},
			{
				Method:  http.MethodPut, // 5.3.20. 修改商品
				Path:    "/api/abcyun/products/update/:id",
				Handler: abcyunhandler.AbcYunUpdateProductHandler(serverCtx),
				// API: PUT /api/v2/open-agency/products/{id}
			},
			{
				Method:  http.MethodGet, // 5.3.21. 获取采购单列表
				Path:    "/api/abcyun/products/purchase-orders",
				Handler: abcyunhandler.AbcYunPurchaseOrderListHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/purchase-orders
			},
			{
				Method:  http.MethodGet, // 5.3.22. 获取采购单详情
				Path:    "/api/abcyun/products/purchase-orders/:orderId",
				Handler: abcyunhandler.AbcYunPurchaseOrderDetailHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/purchase-orders/{orderId}
			},
			{
				Method:  http.MethodPut, // 5.3.23. 审核采购单
				Path:    "/api/abcyun/products/purchase-orders/:purchaseOrderId/review",
				Handler: abcyunhandler.AbcYunReviewPurchaseOrderHandler(serverCtx),
				// API: PUT /api/v2/open-agency/products/purchase-orders/{purchaseOrderId}/review
			},
			{
				Method:  http.MethodPut, // 5.3.24. 审核修正入库单
				Path:    "/api/abcyun/products/fixed-in-orders/:fixInOrderId/review",
				Handler: abcyunhandler.AbcYunReviewFixedInOrderHandler(serverCtx),
				// API: PUT /api/v2/open-agency/products/fixed-in-orders/{fixInOrderId}/review
			},
			{
				Method:  http.MethodPost, // 5.3.25. 创建入库单
				Path:    "/api/abcyun/products/in-orders/create",
				Handler: abcyunhandler.AbcYunCreateInOrderHandler(serverCtx),
				// API: POST /api/v2/open-agency/products/in-orders
			},
			{
				Method:  http.MethodPost, // 5.3.26. 创建退货出库单
				Path:    "/api/abcyun/products/out-orders/create-return",
				Handler: abcyunhandler.AbcYunCreateOutOrderHandler(serverCtx),
				// API: POST /api/v2/open-agency/products/out-orders
			},
			{
				Method:  http.MethodPut, // 5.3.27. 审核退货出库申请单
				Path:    "/api/abcyun/products/out-apply-orders/:outApplyOrderId/review",
				Handler: abcyunhandler.AbcYunReviewOutApplyOrderHandler(serverCtx),
				// API: PUT /api/v2/open-agency/products/out-apply-orders/{outApplyOrderId}/review
			},
			{
				Method:  http.MethodGet, // 5.3.28. 查询门店库存
				Path:    "/api/abcyun/products/batches",
				Handler: abcyunhandler.AbcYunQueryStoreBatchesHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/batches
			},
			{
				Method:  http.MethodGet, // 5.3.29. 获取调拨单列表
				Path:    "/api/abcyun/products/trans-order-list",
				Handler: abcyunhandler.AbcYunTransOrderListHandler(serverCtx),
				// API: GET /api/v2/open-agency/products/trans-order-list
			},
			{
				Method:  http.MethodGet, // 5.3.30. 获取调拨单详情
				Path:    "/api/abcyun/products/trans-order-detail/:orderId",
				Handler: abcyunhandler.AbcYunTransOrderDetailHandler(serverCtx),
				// 访问: GET /api/v2/open-agency/products/trans-order-detail/{orderId} - 【获取调拨单详情】
			},
		},
	)
}
