package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"yekaitai/internal/modules/coupon/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// WxCouponService 小程序端优惠券服务
type WxCouponService struct {
	couponRepo model.CouponRepository
}

// NewWxCouponService 创建小程序端优惠券服务实例
func NewWxCouponService() *WxCouponService {
	return &WxCouponService{
		couponRepo: model.NewCouponRepository(mysql.Master()),
	}
}

// PageData 分页数据
type PageData struct {
	List  interface{} `json:"list"`
	Total int64       `json:"total"`
	Page  int         `json:"page"`
	Size  int         `json:"size"`
}

// PageRequest 分页请求
type PageRequest struct {
	Page int `form:"page,default=1"`       // 页码，默认1
	Size int `form:"page_size,default=10"` // 每页数量，默认10
}

// 获取可领取的优惠券列表请求参数
type GetAvailableCouponsRequest struct {
	PageRequest
	Type      int    `form:"type,optional"`       // 优惠券类型:1-折扣券,2-满减券,3-立减券
	UserLevel *int   `form:"user_level,optional"` // 用户等级
	MinAmount *int   `form:"min_amount,optional"` // 最小使用金额
	ProductID string `form:"product_id,optional"` // 商品ID
	ServiceID string `form:"service_id,optional"` // 服务ID
}

// 用户优惠券列表请求参数
type GetUserCouponsRequest struct {
	PageRequest
	Status  string `form:"status,optional"`   // 状态：unused-未使用，used-已使用，expired-已过期
	Type    int    `form:"type,optional"`     // 优惠券类型:1-折扣券,2-满减券,3-立减券
	OrderID string `form:"order_id,optional"` // 订单ID
}

// 优惠券详情响应
type CouponDetailResponse struct {
	*model.Coupon
	IsReceived       bool   `json:"is_received"`                 // 是否已领取
	UserCouponID     *uint  `json:"user_coupon_id,optional"`     // 用户优惠券ID
	UserCouponStatus string `json:"user_coupon_status,optional"` // 用户优惠券状态
}

// 用户优惠券响应
type UserCouponResponse struct {
	ID                uint       `json:"id"`
	CouponID          uint       `json:"coupon_id"`
	CouponName        string     `json:"coupon_name"`
	CouponType        string     `json:"coupon_type"`
	DiscountAmount    float64    `json:"discount_amount"`
	MinOrderAmount    float64    `json:"min_order_amount"`
	MaxDiscountAmount *float64   `json:"max_discount_amount,optional"`
	Status            string     `json:"status"`
	ReceivedAt        time.Time  `json:"received_at"`
	UsedAt            *time.Time `json:"used_at,optional"`
	ExpiredAt         time.Time  `json:"expired_at"`
	OrderID           *uint      `json:"order_id,optional"`
	Description       string     `json:"description,optional"`
}

// 优惠券使用检查请求
type CheckCouponUsageRequest struct {
	CouponID    uint    `json:"coupon_id"`            // 优惠券ID
	OrderAmount float64 `json:"order_amount"`         // 订单金额
	ProductIDs  []uint  `json:"product_ids,optional"` // 商品ID列表
	ServiceIDs  []uint  `json:"service_ids,optional"` // 服务ID列表
}

// 优惠券使用检查响应
type CheckCouponUsageResponse struct {
	CanUse         bool    `json:"can_use"`         // 是否可以使用
	DiscountAmount float64 `json:"discount_amount"` // 优惠金额
	Reason         string  `json:"reason"`          // 不可用原因
}

// 自动选择最优优惠券请求
type AutoSelectBestCouponRequest struct {
	OrderAmount float64 `json:"order_amount"`         // 订单总金额
	ProductIDs  []uint  `json:"product_ids,optional"` // 商品ID列表
	ServiceIDs  []uint  `json:"service_ids,optional"` // 服务ID列表
}

// 自动选择最优优惠券响应
type AutoSelectBestCouponResponse struct {
	HasCoupon      bool                 `json:"has_coupon"`      // 是否有可用优惠券
	BestCoupon     *UserCouponResponse  `json:"best_coupon"`     // 最优优惠券
	DiscountAmount float64              `json:"discount_amount"` // 优惠金额
	FinalAmount    float64              `json:"final_amount"`    // 优惠后金额
	AllCoupons     []UserCouponResponse `json:"all_coupons"`     // 所有可用优惠券（按优惠金额排序）
}

// GetAvailableCoupons 获取可领取的优惠券列表
func (s *WxCouponService) GetAvailableCoupons(ctx context.Context, req *GetAvailableCouponsRequest, userID uint) (*PageData, error) {
	db := mysql.Master()

	// 构建查询条件
	query := db.WithContext(ctx).Model(&model.Coupon{}).
		Where("status = ?", model.CouponStatusActive).
		Where("total_quantity > received_quantity") // 还有库存

	// 按类型过滤
	if req.Type > 0 {
		query = query.Where("type = ?", req.Type)
	}

	// 按最小使用金额过滤
	if req.MinAmount != nil {
		query = query.Where("min_amount <= ?", *req.MinAmount)
	}

	// 总数查询
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logx.Errorf("统计可领取优惠券总数失败: %v", err)
		return nil, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.Size
	var coupons []model.Coupon
	if err := query.Offset(offset).Limit(req.Size).
		Order("created_at DESC").Find(&coupons).Error; err != nil {
		logx.Errorf("获取可领取优惠券列表失败: %v", err)
		return nil, err
	}

	// 检查用户是否已领取
	couponIDs := make([]uint, len(coupons))
	for i, coupon := range coupons {
		couponIDs[i] = coupon.ID
	}

	userCoupons := make(map[uint]*model.UserCoupon)
	if len(couponIDs) > 0 {
		var userCouponList []model.UserCoupon
		db.WithContext(ctx).Where("user_id = ? AND coupon_id IN ?", userID, couponIDs).
			Find(&userCouponList)

		for _, uc := range userCouponList {
			userCoupons[uc.CouponID] = &uc
		}
	}

	// 构建响应数据
	var result []CouponDetailResponse
	for _, coupon := range coupons {
		detail := CouponDetailResponse{
			Coupon:     &coupon,
			IsReceived: false,
		}

		if uc, exists := userCoupons[coupon.ID]; exists {
			detail.IsReceived = true
			detail.UserCouponID = &uc.ID
			detail.UserCouponStatus = string(uc.Status)
		}

		result = append(result, detail)
	}

	return &PageData{
		List:  result,
		Total: total,
		Page:  req.Page,
		Size:  req.Size,
	}, nil
}

// ReceiveCoupon 领取优惠券
func (s *WxCouponService) ReceiveCoupon(ctx context.Context, couponID, userID uint) error {
	_, err := s.couponRepo.IssueCoupon(userID, couponID)
	return err
}

// GetUserCoupons 获取用户优惠券列表
func (s *WxCouponService) GetUserCoupons(ctx context.Context, req *GetUserCouponsRequest, userID uint) (*PageData, error) {
	// 构建状态筛选条件
	var status *model.UserCouponStatus
	if req.Status != "" {
		switch req.Status {
		case "unused":
			s := model.UserCouponStatusUnused
			status = &s
		case "used":
			s := model.UserCouponStatusUsed
			status = &s
		case "expired":
			s := model.UserCouponStatusExpired
			status = &s
		}
	}

	// 调用仓库方法获取用户优惠券
	userCoupons, total, err := s.couponRepo.GetUserCoupons(userID, status, req.Page, req.Size)
	if err != nil {
		logx.Errorf("获取用户优惠券列表失败: %v", err)
		return nil, err
	}

	// 转换为响应格式
	var result []UserCouponResponse
	for _, uc := range userCoupons {
		response := UserCouponResponse{
			ID:         uc.ID,
			CouponID:   uc.CouponID,
			Status:     string(uc.Status),
			ReceivedAt: uc.CreatedAt,
			UsedAt:     uc.UsedAt,
			ExpiredAt:  uc.ValidUntil,
			OrderID:    uc.OrderID,
		}

		// 如果有关联优惠券信息，填充详细信息
		if uc.Coupon != nil {
			response.CouponName = uc.Coupon.Name
			response.CouponType = string(uc.Coupon.Type)
			response.DiscountAmount = uc.Coupon.Amount
			response.MinOrderAmount = uc.Coupon.MinAmount
			response.Description = uc.Coupon.Description
		}

		result = append(result, response)
	}

	return &PageData{
		List:  result,
		Total: total,
		Page:  req.Page,
		Size:  req.Size,
	}, nil
}

// GetUserCouponDetail 获取用户优惠券详情
func (s *WxCouponService) GetUserCouponDetail(ctx context.Context, userCouponID, userID uint) (*UserCouponResponse, error) {
	db := mysql.Master()

	var userCoupon model.UserCoupon
	err := db.WithContext(ctx).
		Preload("Coupon").
		Where("id = ? AND user_id = ?", userCouponID, userID).
		First(&userCoupon).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("优惠券不存在")
		}
		logx.Errorf("获取用户优惠券详情失败: %v", err)
		return nil, err
	}

	result := &UserCouponResponse{
		ID:         userCoupon.ID,
		CouponID:   userCoupon.CouponID,
		Status:     string(userCoupon.Status),
		ReceivedAt: userCoupon.CreatedAt,
		UsedAt:     userCoupon.UsedAt,
		ExpiredAt:  userCoupon.ValidUntil,
		OrderID:    userCoupon.OrderID,
	}

	if userCoupon.Coupon != nil {
		result.CouponName = userCoupon.Coupon.Name
		result.CouponType = string(userCoupon.Coupon.Type)
		result.DiscountAmount = userCoupon.Coupon.Amount
		result.MinOrderAmount = userCoupon.Coupon.MinAmount
		result.Description = userCoupon.Coupon.Description
	}

	return result, nil
}

// CheckCouponUsage 检查优惠券是否可用
func (s *WxCouponService) CheckCouponUsage(ctx context.Context, req *CheckCouponUsageRequest, userID uint) (*CheckCouponUsageResponse, error) {
	db := mysql.Master()

	// 获取用户优惠券
	var userCoupon model.UserCoupon
	err := db.WithContext(ctx).
		Preload("Coupon").
		Where("coupon_id = ? AND user_id = ?", req.CouponID, userID).
		First(&userCoupon).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &CheckCouponUsageResponse{
				CanUse: false,
				Reason: "优惠券不存在",
			}, nil
		}
		return nil, err
	}

	// 检查优惠券状态
	if userCoupon.Status != model.UserCouponStatusUnused {
		return &CheckCouponUsageResponse{
			CanUse: false,
			Reason: "优惠券已使用或已过期",
		}, nil
	}

	// 检查是否过期
	if time.Now().After(userCoupon.ValidUntil) {
		return &CheckCouponUsageResponse{
			CanUse: false,
			Reason: "优惠券已过期",
		}, nil
	}

	// 检查最小订单金额
	if userCoupon.Coupon != nil && req.OrderAmount < userCoupon.Coupon.MinAmount {
		return &CheckCouponUsageResponse{
			CanUse: false,
			Reason: fmt.Sprintf("订单金额需满足最低消费%.2f元", userCoupon.Coupon.MinAmount),
		}, nil
	}

	// 计算优惠金额
	discountAmount := s.calculateDiscountAmount(userCoupon.Coupon, req.OrderAmount)

	return &CheckCouponUsageResponse{
		CanUse:         true,
		DiscountAmount: discountAmount,
		Reason:         "",
	}, nil
}

// UseCoupon 使用优惠券
func (s *WxCouponService) UseCoupon(ctx context.Context, userCouponID, orderID uint) error {
	return s.couponRepo.UseCoupon(userCouponID, orderID)
}

// GetUsableCouponsForOrder 获取订单可用的优惠券列表
func (s *WxCouponService) GetUsableCouponsForOrder(ctx context.Context, userID uint, orderAmount float64, productIDs, serviceIDs []uint) ([]UserCouponResponse, error) {
	// 获取用户未使用的优惠券
	userCoupons, err := s.couponRepo.GetAvailableUserCoupons(userID, productIDs, orderAmount)
	if err != nil {
		logx.Errorf("获取用户可用优惠券失败: %v", err)
		return nil, err
	}

	// 转换为响应格式
	var result []UserCouponResponse
	for _, uc := range userCoupons {
		response := UserCouponResponse{
			ID:         uc.ID,
			CouponID:   uc.CouponID,
			Status:     string(uc.Status),
			ReceivedAt: uc.CreatedAt,
			UsedAt:     uc.UsedAt,
			ExpiredAt:  uc.ValidUntil,
			OrderID:    uc.OrderID,
		}

		if uc.Coupon != nil {
			response.CouponName = uc.Coupon.Name
			response.CouponType = string(uc.Coupon.Type)
			response.DiscountAmount = uc.Coupon.Amount
			response.MinOrderAmount = uc.Coupon.MinAmount
			response.Description = uc.Coupon.Description
		}

		result = append(result, response)
	}

	return result, nil
}

// calculateDiscountAmount 计算优惠金额
func (s *WxCouponService) calculateDiscountAmount(coupon *model.Coupon, orderAmount float64) float64 {
	if coupon == nil {
		return 0
	}

	switch coupon.Type {
	case model.CouponType(2): // 折扣券
		// 折扣券：订单金额 * (1 - 折扣/100)
		discountAmount := orderAmount * (1 - coupon.Discount/100)
		return discountAmount

	case model.CouponType(1): // 满减券
		// 满减券：直接减免指定金额
		if coupon.Amount > orderAmount {
			return orderAmount // 优惠金额不能超过订单金额
		}
		return coupon.Amount

	case model.CouponType(3): // 立减券
		// 立减券：直接减免指定金额
		if coupon.Amount > orderAmount {
			return orderAmount
		}
		return coupon.Amount

	default:
		return 0
	}
}

// AutoSelectBestCoupon 自动选择最优优惠券
func (s *WxCouponService) AutoSelectBestCoupon(ctx context.Context, req *AutoSelectBestCouponRequest, userID uint) (*AutoSelectBestCouponResponse, error) {
	// 获取用户所有可用的优惠券
	availableCoupons, err := s.couponRepo.GetAvailableUserCoupons(userID, req.ProductIDs, req.OrderAmount)
	if err != nil {
		logx.Errorf("获取用户可用优惠券失败: %v", err)
		return nil, err
	}

	response := &AutoSelectBestCouponResponse{
		HasCoupon:   false,
		FinalAmount: req.OrderAmount,
		AllCoupons:  []UserCouponResponse{},
	}

	if len(availableCoupons) == 0 {
		return response, nil
	}

	// 计算每个优惠券的优惠金额并排序
	type CouponDiscount struct {
		UserCoupon     *model.UserCoupon
		DiscountAmount float64
		FinalAmount    float64
	}

	var couponDiscounts []CouponDiscount

	for _, uc := range availableCoupons {
		discountAmount := s.calculateDiscountAmount(uc.Coupon, req.OrderAmount)
		finalAmount := req.OrderAmount - discountAmount

		couponDiscounts = append(couponDiscounts, CouponDiscount{
			UserCoupon:     uc,
			DiscountAmount: discountAmount,
			FinalAmount:    finalAmount,
		})
	}

	// 按优惠金额从大到小排序
	for i := 0; i < len(couponDiscounts); i++ {
		for j := i + 1; j < len(couponDiscounts); j++ {
			if couponDiscounts[j].DiscountAmount > couponDiscounts[i].DiscountAmount {
				couponDiscounts[i], couponDiscounts[j] = couponDiscounts[j], couponDiscounts[i]
			}
		}
	}

	// 构建响应数据
	response.HasCoupon = true
	if len(couponDiscounts) > 0 {
		// 最优优惠券
		best := couponDiscounts[0]
		response.BestCoupon = &UserCouponResponse{
			ID:         best.UserCoupon.ID,
			CouponID:   best.UserCoupon.CouponID,
			Status:     string(best.UserCoupon.Status),
			ReceivedAt: best.UserCoupon.CreatedAt,
			UsedAt:     best.UserCoupon.UsedAt,
			ExpiredAt:  best.UserCoupon.ValidUntil,
		}

		if best.UserCoupon.Coupon != nil {
			response.BestCoupon.CouponName = best.UserCoupon.Coupon.Name
			response.BestCoupon.CouponType = string(best.UserCoupon.Coupon.Type)
			response.BestCoupon.DiscountAmount = best.UserCoupon.Coupon.Amount
			response.BestCoupon.MinOrderAmount = best.UserCoupon.Coupon.MinAmount
			response.BestCoupon.Description = best.UserCoupon.Coupon.Description
		}

		response.DiscountAmount = best.DiscountAmount
		response.FinalAmount = best.FinalAmount
	}

	// 所有可用优惠券列表（已按优惠金额排序）
	for _, cd := range couponDiscounts {
		couponResponse := UserCouponResponse{
			ID:         cd.UserCoupon.ID,
			CouponID:   cd.UserCoupon.CouponID,
			Status:     string(cd.UserCoupon.Status),
			ReceivedAt: cd.UserCoupon.CreatedAt,
			UsedAt:     cd.UserCoupon.UsedAt,
			ExpiredAt:  cd.UserCoupon.ValidUntil,
		}

		if cd.UserCoupon.Coupon != nil {
			couponResponse.CouponName = cd.UserCoupon.Coupon.Name
			couponResponse.CouponType = string(cd.UserCoupon.Coupon.Type)
			couponResponse.DiscountAmount = cd.UserCoupon.Coupon.Amount
			couponResponse.MinOrderAmount = cd.UserCoupon.Coupon.MinAmount
			couponResponse.Description = cd.UserCoupon.Coupon.Description
		}

		response.AllCoupons = append(response.AllCoupons, couponResponse)
	}

	return response, nil
}
