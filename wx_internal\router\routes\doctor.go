package routes

import (
	"fmt"
	"net/http"

	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/middleware"
	doctorHandler "yekaitai/wx_internal/modules/doctor/handler"
	"yekaitai/wx_internal/svc"
)

// RegisterDoctorRoutes 注册医生相关路由
func RegisterDoctorRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 创建医生处理器
	fmt.Printf("注册医生路由，使用腾讯地图密钥: [%s]\n", serverCtx.TencentMapKey)
	handler := doctorHandler.NewDoctorHandler(serverCtx.TencentMapKey)

	// 使用微信认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 注册医生列表路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/doctors",
			Handler: wxAuthWrapper(handler.List),
		},
	)

	// 注册医生详情路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/doctors/:id",
			Handler: wxAuthWrapper(handler.Detail),
		},
	)
}
