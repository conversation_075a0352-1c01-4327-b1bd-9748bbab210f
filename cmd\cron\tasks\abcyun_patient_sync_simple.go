package tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/upload"
)

// Relationship 关系类型
type Relationship string

const (
	RelationshipSelf   Relationship = "本人"
	RelationshipSpouse Relationship = "配偶"
	RelationshipChild  Relationship = "子女"
	RelationshipParent Relationship = "父母"
	RelationshipOther  Relationship = "其他"
)

// OutpatientSummary 门诊单摘要信息
type OutpatientSummary struct {
	ID             string `json:"id"`             // 门诊单ID
	PatientOrderID string `json:"patientOrderId"` // 就诊单ID
	Patient        struct {
		ID       string `json:"id"`       // 患者ID
		Name     string `json:"name"`     // 姓名
		Mobile   string `json:"mobile"`   // 手机号
		Sex      string `json:"sex"`      // 性别
		Birthday string `json:"birthday"` // 生日
		Age      struct {
			Year  int `json:"year"`  // 年
			Month int `json:"month"` // 月
			Day   int `json:"day"`   // 日
		} `json:"age"`
		IDCard string `json:"idCard"` // 身份证号
	} `json:"patient"`
	Created        string `json:"created"`        // 创建时间
	DoctorID       string `json:"doctorId"`       // 医生ID
	DoctorName     string `json:"doctorName"`     // 医生姓名
	PatientOrderNo string `json:"patientOrderNo"` // 就诊单号
	Status         int    `json:"status"`         // 状态
	StatusName     string `json:"statusName"`     // 状态名称
}

// OutpatientDetail 门诊单详细信息
type OutpatientDetail struct {
	ID             string `json:"id"`             // 门诊单ID
	ClinicID       string `json:"clinicId"`       // 诊所ID
	PatientOrderID string `json:"patientOrderId"` // 就诊单ID
	PatientOrderNo string `json:"patientOrderNo"` // 就诊单号
	DepartmentID   string `json:"departmentId"`   // 科室ID
	DepartmentName string `json:"departmentName"` // 科室名称
	DoctorID       string `json:"doctorId"`       // 医生ID
	DoctorName     string `json:"doctorName"`     // 医生姓名
	Created        string `json:"created"`        // 创建时间
	DiagnosedDate  string `json:"diagnosedDate"`  // 就诊日期
	Status         int    `json:"status"`         // 状态
	StatusName     string `json:"statusName"`     // 状态名称
	RevisitStatus  int    `json:"revisitStatus"`  // 复诊状态
	Patient        struct {
		ID       string `json:"id"`       // 患者ID
		Name     string `json:"name"`     // 姓名
		Mobile   string `json:"mobile"`   // 手机号
		Sex      string `json:"sex"`      // 性别
		Birthday string `json:"birthday"` // 生日
		Age      struct {
			Year  int `json:"year"`  // 年
			Month int `json:"month"` // 月
			Day   int `json:"day"`   // 日
		} `json:"age"`
		IDCard string `json:"idCard"` // 身份证号
	} `json:"patient"`
	MedicalRecord             *MedicalRecordInfo `json:"medicalRecord"`
	PrescriptionChineseForms  []PrescriptionForm `json:"prescriptionChineseForms"`
	PrescriptionWesternForms  []PrescriptionForm `json:"prescriptionWesternForms"`
	PrescriptionInfusionForms []PrescriptionForm `json:"prescriptionInfusionForms"`
	PrescriptionExternalForms []PrescriptionForm `json:"prescriptionExternalForms"`
	ProductForms              []ProductForm      `json:"productForms"`
}

// MedicalRecordInfo 病历信息
type MedicalRecordInfo struct {
	ID                     string        `json:"id"`
	OutpatientSheetID      string        `json:"outpatientSheetId"`
	ChiefComplaint         string        `json:"chiefComplaint"`
	PastHistory            string        `json:"pastHistory"`
	FamilyHistory          string        `json:"familyHistory"`
	PresentHistory         string        `json:"presentHistory"`
	PhysicalExamination    string        `json:"physicalExamination"`
	Diagnosis              string        `json:"diagnosis"`
	DoctorAdvice           string        `json:"doctorAdvice"`
	Syndrome               string        `json:"syndrome"`
	Therapy                string        `json:"therapy"`
	ChineseExamination     string        `json:"chineseExamination"`
	WearGlassesHistory     string        `json:"wearGlassesHistory"`
	EpidemiologicalHistory string        `json:"epidemiologicalHistory"`
	ObstetricalHistory     string        `json:"obstetricalHistory"`
	EyeExamination         interface{}   `json:"eyeExamination"`
	AllergicHistory        string        `json:"allergicHistory"`
	AuxiliaryExamination   string        `json:"auxiliaryExamination"`
	Attachments            []interface{} `json:"attachments"`
}

// PrescriptionForm 处方信息
type PrescriptionForm struct {
	ID                    string                 `json:"id"`
	Type                  int                    `json:"type"`
	Specification         string                 `json:"specification"`
	DoseCount             int                    `json:"doseCount"`
	DailyDosage           string                 `json:"dailyDosage"`
	Usage                 string                 `json:"usage"`
	Freq                  string                 `json:"freq"`
	Requirement           string                 `json:"requirement"`
	UsageLevel            string                 `json:"usageLevel"`
	PrescriptionFormItems []PrescriptionFormItem `json:"prescriptionFormItems"`
}

// PrescriptionFormItem 处方单项信息
type PrescriptionFormItem struct {
	ID                 string      `json:"id"`
	PrescriptionFormID string      `json:"prescriptionFormId"`
	ProductID          string      `json:"productId"`
	Type               int         `json:"type"`
	SubType            int         `json:"subType"`
	Name               string      `json:"name"`
	Specification      string      `json:"specification"`
	Manufacturer       string      `json:"manufacturer"`
	Usage              string      `json:"usage"`
	Ivgtt              float64     `json:"ivgtt"`
	IvgttUnit          string      `json:"ivgttUnit"`
	Freq               string      `json:"freq"`
	Dosage             string      `json:"dosage"`
	DosageUnit         string      `json:"dosageUnit"`
	Days               int         `json:"days"`
	SpecialRequirement string      `json:"specialRequirement"`
	IsDismounting      int         `json:"isDismounting"`
	UnitCount          float64     `json:"unitCount"`
	Unit               string      `json:"unit"`
	UnitPrice          float64     `json:"unitPrice"`
	GroupID            int         `json:"groupId"`
	IsAst              int         `json:"isAst"`
	AstResult          interface{} `json:"astResult"`
	ProductInfo        interface{} `json:"productInfo"`
}

// ProductForm 治疗单信息
type ProductForm struct {
	ID               string            `json:"id"`
	Type             int               `json:"type"`
	ProductFormItems []ProductFormItem `json:"productFormItems"`
}

// ProductFormItem 治疗单子项信息
type ProductFormItem struct {
	ID            string  `json:"id"`
	ProductFormID string  `json:"productFormId"`
	ProductID     string  `json:"productId"`
	Name          string  `json:"name"`
	UnitCount     float64 `json:"unitCount"`
	Unit          string  `json:"unit"`
	UnitPrice     float64 `json:"unitPrice"`
	IsDismounting int     `json:"isDismounting"`
	Type          int     `json:"type"`
	SubType       int     `json:"subType"`
	Days          int     `json:"days"`
	DailyDosage   int     `json:"dailyDosage"`
	Remark        string  `json:"remark"`
}

// AbcYunPatientSyncSimpleService ABC云患者同步服务（简化版）
type AbcYunPatientSyncSimpleService struct {
	client   *abcyun.AbcYunClient
	db       *gorm.DB
	uploader *upload.QiniuUploader
}

// NewAbcYunPatientSyncSimpleService 创建ABC云患者同步服务
func NewAbcYunPatientSyncSimpleService(client *abcyun.AbcYunClient) *AbcYunPatientSyncSimpleService {
	// 初始化七牛云上传器
	uploader := upload.NewQiniuUploader(upload.GetQiniuConfig())

	return &AbcYunPatientSyncSimpleService{
		client:   client,
		db:       mysql.Master(),
		uploader: uploader,
	}
}

// SyncPatients 同步患者信息（定时任务使用）
func (s *AbcYunPatientSyncSimpleService) SyncPatients() error {
	logx.Info("开始定时同步ABC云患者信息...")

	// 获取最后成功同步的日期
	lastSyncDate := s.getLastSyncDate()

	if lastSyncDate == "" {
		// 首次运行，从2025年4月9日开始同步
		logx.Info("首次运行，开始从2025年4月9日同步历史数据...")
		return s.syncHistoricalData()
	} else {
		// 从最后同步日期开始同步到今天（包含当天）
		logx.Infof("继续同步，从 %s 开始（包含当天）...", lastSyncDate)
		return s.syncFromDate(lastSyncDate)
	}
}

// SyncPatientsManual 手动同步患者信息
func (s *AbcYunPatientSyncSimpleService) SyncPatientsManual() error {
	logx.Info("开始手动同步ABC云患者信息...")

	// 获取最后成功同步的日期
	lastSyncDate := s.getLastSyncDate()

	if lastSyncDate == "" {
		// 首次运行，从2025年4月9日开始同步
		logx.Info("手动同步：首次运行，开始从2025年4月9日同步历史数据...")
		return s.syncHistoricalData()
	} else {
		// 从最后同步日期开始同步到今天（包含当天）
		logx.Infof("手动同步：继续同步，从 %s 开始（包含当天）...", lastSyncDate)
		return s.syncFromDate(lastSyncDate)
	}
}

// getLastSyncDate 获取最后成功同步的日期
func (s *AbcYunPatientSyncSimpleService) getLastSyncDate() string {
	var progress patient.SyncProgress
	err := s.db.Where("sync_type = ? AND status = ?", "abcyun_patient_sync", 1).
		Order("sync_date DESC").
		First(&progress).Error

	if err != nil {
		return "" // 没有找到记录，返回空字符串
	}

	// 如果存储的是时间戳格式，尝试解析并转换为日期格式
	if strings.Contains(progress.SyncDate, "T") {
		if t, parseErr := time.Parse(time.RFC3339, progress.SyncDate); parseErr == nil {
			return t.Format("2006-01-02")
		}
		// 如果RFC3339解析失败，尝试其他格式
		if t, parseErr := time.Parse("2006-01-02T15:04:05Z07:00", progress.SyncDate); parseErr == nil {
			return t.Format("2006-01-02")
		}
	}

	return progress.SyncDate
}

// saveSyncProgress 保存同步进度
func (s *AbcYunPatientSyncSimpleService) saveSyncProgress(syncDate string, status int, records int, message string) {
	progress := patient.SyncProgress{
		SyncType: "abcyun_patient_sync",
		SyncDate: syncDate,
		Status:   status,
		Records:  records,
		Message:  message,
	}

	// 先查找是否已存在该日期的记录
	var existing patient.SyncProgress
	err := s.db.Where("sync_type = ? AND sync_date = ?", "abcyun_patient_sync", syncDate).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 不存在，创建新记录
		s.db.Create(&progress)
		logx.Infof("保存同步进度: 日期=%s, 状态=%d, 记录数=%d", syncDate, status, records)
	} else {
		// 存在，更新记录
		s.db.Model(&existing).Updates(map[string]interface{}{
			"status":  status,
			"records": records,
			"message": message,
		})
		logx.Infof("更新同步进度: 日期=%s, 状态=%d, 记录数=%d", syncDate, status, records)
	}
}

// syncHistoricalData 同步历史数据（从2025年4月9日到今天）
func (s *AbcYunPatientSyncSimpleService) syncHistoricalData() error {
	startDate := time.Date(2025, 4, 9, 0, 0, 0, 0, time.Local)
	endDate := time.Now()

	for currentDate := startDate; currentDate.Before(endDate) || currentDate.Equal(endDate.Truncate(24*time.Hour)); currentDate = currentDate.AddDate(0, 0, 1) {
		dateStr := currentDate.Format("2006-01-02")
		logx.Infof("同步日期: %s", dateStr)

		records, err := s.syncPatientsByDate(dateStr)
		if err != nil {
			logx.Errorf("同步日期 %s 的数据失败: %v", dateStr, err)
			s.saveSyncProgress(dateStr, 0, 0, err.Error())
			continue
		}

		s.saveSyncProgress(dateStr, 1, records, "同步成功")

		// 避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

// syncFromDate 从指定日期开始同步到今天（包含当天）
func (s *AbcYunPatientSyncSimpleService) syncFromDate(lastSyncDate string) error {
	lastDate, err := time.Parse("2006-01-02", lastSyncDate)
	if err != nil {
		logx.Errorf("解析最后同步日期失败: %v", err)
		return err
	}

	// 从最后同步日期开始（包含当天），确保当天的数据也能被同步
	startDate := lastDate
	endDate := time.Now()
	today := time.Now().Format("2006-01-02")

	for currentDate := startDate; currentDate.Before(endDate) || currentDate.Equal(endDate.Truncate(24*time.Hour)); currentDate = currentDate.AddDate(0, 0, 1) {
		dateStr := currentDate.Format("2006-01-02")
		logx.Infof("同步日期: %s", dateStr)

		records, err := s.syncPatientsByDate(dateStr)
		if err != nil {
			logx.Errorf("同步日期 %s 的数据失败: %v", dateStr, err)
			s.saveSyncProgress(dateStr, 0, 0, err.Error())
			continue
		}

		s.saveSyncProgress(dateStr, 1, records, "同步成功")

		// 如果是今天的数据，记录日志
		if dateStr == today {
			logx.Infof("今天的数据同步完成: %s，同步记录数: %d", dateStr, records)
		}

		// 避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

// syncPatientsByDate 按日期同步患者信息
func (s *AbcYunPatientSyncSimpleService) syncPatientsByDate(dateStr string) (int, error) {
	var totalRecords int
	offset := 0
	limit := 50 // 每次查询50条记录

	for {
		// 调用GetPatientList接口
		result, err := s.client.GetPatientList(dateStr, limit, offset)
		if err != nil {
			return totalRecords, err
		}

		if len(result.Rows) == 0 {
			break // 没有更多数据
		}

		logx.Infof("获取到 %d 条患者记录 (日期: %s, offset: %d)", len(result.Rows), dateStr, offset)

		// 处理每个患者
		for _, pt := range result.Rows {
			err := s.processPatient(pt, dateStr)
			if err != nil {
				logx.Errorf("处理患者失败: %v", err)
				continue
			}
			totalRecords++
		}

		// 如果已经获取了所有数据，退出循环
		if offset+len(result.Rows) >= result.Total {
			break
		}

		offset += limit
		time.Sleep(50 * time.Millisecond) // 避免请求过于频繁
	}

	return totalRecords, nil
}

// processPatient 处理单个患者
func (s *AbcYunPatientSyncSimpleService) processPatient(pt abcyun.PatientSummary, dateStr string) error {
	// 检查患者是否已存在（通过ABC云ID判重）
	var existingPatient patient.WxPatient
	err := s.db.Where("abcyun_patient_id = ?", pt.ID).First(&existingPatient).Error
	if err == nil {
		// 患者已存在，跳过
		logx.Infof("患者已存在，跳过: %s (ID: %s)", pt.Name, pt.ID)
		// 患者已存在时，也要处理家庭成员、报告和门诊记录
		s.processFamilyMembers(pt.ID, existingPatient.UserID)
		s.processPatientReports(pt.ID, existingPatient.PatientID)
		s.processOutpatientRecords(pt.ID, existingPatient.PatientID, dateStr)
		return nil
	}
	if err != gorm.ErrRecordNotFound {
		return err
	}

	// 创建基础的患者信息同步
	userID, patientID, err := s.createBasicPatient(pt)
	if err != nil {
		return err
	}

	// 处理家庭成员信息
	if err := s.processFamilyMembers(pt.ID, userID); err != nil {
		logx.Errorf("处理患者家庭成员失败: %v", err)
	}

	// 处理患者报告
	if err := s.processPatientReports(pt.ID, patientID); err != nil {
		logx.Errorf("处理患者报告失败: %v", err)
	}

	// 处理门诊记录（按同步日期查询）
	if err := s.processOutpatientRecords(pt.ID, patientID, dateStr); err != nil {
		logx.Errorf("处理患者门诊记录失败: %v", err)
	}

	logx.Infof("创建患者记录成功: %s (ID: %s)", pt.Name, pt.ID)
	return nil
}

// createBasicPatient 创建基础的患者记录
func (s *AbcYunPatientSyncSimpleService) createBasicPatient(pt abcyun.PatientSummary) (uint, uint, error) {
	// 先创建或查找用户记录
	var wxUser user.WxUser
	err := s.db.Where("mobile = ?", pt.Mobile).First(&wxUser).Error
	if err == gorm.ErrRecordNotFound {
		// 创建新用户
		wxUser = user.WxUser{
			Mobile:       pt.Mobile,
			Nickname:     pt.Name,
			Gender:       s.convertGender(pt.Sex),
			Status:       1,
			RegisterDate: time.Now(),
		}
		err = s.db.Create(&wxUser).Error
		if err != nil {
			return 0, 0, fmt.Errorf("创建用户记录失败: %w", err)
		}
		logx.Infof("创建用户记录成功: %s (手机号: %s)", pt.Name, pt.Mobile)
	} else if err != nil {
		return 0, 0, fmt.Errorf("查询用户记录失败: %w", err)
	}

	// 创建患者记录
	wxPatient := patient.WxPatient{
		UserID:               wxUser.UserID,
		Name:                 pt.Name,
		Gender:               s.convertGender(pt.Sex),
		BirthDate:            pt.Birthday,
		IdCard:               pt.IDCard,
		Mobile:               pt.Mobile,
		RelationshipWithUser: "本人",  // 默认为本人
		AbcyunPatientID:      pt.ID, // 直接存储ABC云患者ID字符串
		Status:               1,
	}

	err = s.db.Create(&wxPatient).Error
	if err != nil {
		return 0, 0, fmt.Errorf("创建患者记录失败: %w", err)
	}

	return wxUser.UserID, wxPatient.PatientID, nil
}

// processFamilyMembers 处理患者家庭成员信息
func (s *AbcYunPatientSyncSimpleService) processFamilyMembers(patientID string, mainUserID uint) error {
	// 调用家庭成员查询接口
	familyResponse, err := s.client.GetPatientFamilyMembers(patientID)
	if err != nil {
		return fmt.Errorf("查询患者家庭成员失败: %w", err)
	}

	// 获取家庭成员列表
	familyPatients := familyResponse.FamilyPatients
	if len(familyPatients) == 0 {
		logx.Infof("患者 %s 没有家庭成员信息", patientID)
		return nil
	}

	// 处理每个家庭成员
	for _, member := range familyPatients {
		memberID := member.ID
		memberName := member.Name
		relation := member.Relation
		mobile := member.Mobile
		sex := member.Sex
		birthday := member.Birthday
		idCard := member.IDCard

		// 判断是否是本人
		if memberID == patientID {
			// 是本人，已经在前面处理过，跳过
			continue
		}

		// 不是本人，创建家庭成员患者记录
		relationship := s.convertRelationship(relation)

		// 检查该家庭成员是否已存在
		var existingMember patient.WxPatient
		err = s.db.Where("abcyun_patient_id = ?", memberID).First(&existingMember).Error
		if err == nil {
			// 家庭成员已存在，跳过
			logx.Infof("家庭成员已存在，跳过: %s (ID: %s)", memberName, memberID)
			continue
		}

		// 创建家庭成员患者记录
		memberPatient := patient.WxPatient{
			UserID:               mainUserID, // 使用主患者的用户ID
			Name:                 memberName,
			Gender:               s.convertGender(sex),
			BirthDate:            birthday,
			IdCard:               idCard,
			Mobile:               mobile,
			RelationshipWithUser: string(relationship),
			AbcyunPatientID:      memberID,
			Status:               1,
		}

		err = s.db.Create(&memberPatient).Error
		if err != nil {
			logx.Errorf("创建家庭成员记录失败: %v", err)
			continue
		}

		logx.Infof("成功创建家庭成员记录: %s (关系: %s)", memberName, relationship)
	}

	return nil
}

// processPatientReports 处理患者报告
func (s *AbcYunPatientSyncSimpleService) processPatientReports(patientID string, wxPatientID uint) error {
	// 调用患者报告查询接口
	reportsResponse, err := s.client.GetPatientAttachments(patientID, 10, 0)
	if err != nil {
		return fmt.Errorf("查询患者报告失败: %w", err)
	}

	// 获取报告列表
	rows := reportsResponse.Rows
	if len(rows) == 0 {
		logx.Infof("患者 %s 没有报告信息", patientID)
		return nil
	}

	// 处理每个报告
	for _, report := range rows {
		reportID := report.ID
		url := report.URL
		fileName := report.FileName
		displayName := report.DisplayName
		created := report.Created
		businessCategory := report.BusinessCategory

		// 检查报告是否已存在
		var existingReport patient.AbcYunPatientReport
		err = s.db.Where("abc_yun_report_id = ?", reportID).First(&existingReport).Error
		if err == nil {
			// 报告已存在，跳过
			logx.Infof("患者报告已存在，跳过: %s (ID: %s)", displayName, reportID)
			continue
		}

		// 创建报告记录
		patientReport := patient.AbcYunPatientReport{
			PatientID:        wxPatientID,
			AbcYunReportID:   reportID,
			FileName:         fileName,
			DisplayName:      displayName,
			OriginalURL:      url,
			BusinessCategory: businessCategory,
			ReportDate:       created,
			UploadStatus:     0, // 初始状态为未上传
		}

		// 如果有文件URL，尝试上传到七牛云
		if url != "" && s.uploader != nil {
			qiniuURL, err := s.uploadFileToQiniu(url, fileName)
			if err != nil {
				logx.Errorf("上传文件到七牛云失败: %v", err)
				patientReport.UploadStatus = 2 // 上传失败
			} else {
				patientReport.QiniuURL = qiniuURL
				patientReport.UploadStatus = 1 // 上传成功
				logx.Infof("成功上传文件到七牛云: %s", qiniuURL)
			}
		}

		err = s.db.Create(&patientReport).Error
		if err != nil {
			logx.Errorf("创建患者报告记录失败: %v", err)
			continue
		}

		logx.Infof("成功创建患者报告记录: %s", displayName)
	}

	return nil
}

// uploadFileToQiniu 上传文件到七牛云
func (s *AbcYunPatientSyncSimpleService) uploadFileToQiniu(fileURL, fileName string) (string, error) {
	if s.uploader == nil {
		return "", fmt.Errorf("七牛云上传器未初始化")
	}

	// 下载文件
	resp, err := http.Get(fileURL)
	if err != nil {
		return "", fmt.Errorf("下载文件失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载文件失败，状态码: %d", resp.StatusCode)
	}

	// 读取文件内容
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取文件内容失败: %w", err)
	}

	// 生成唯一文件名
	uniqueFileName := fmt.Sprintf("patient_reports/%d_%s", time.Now().Unix(), fileName)

	// 上传到七牛云
	return s.uploader.UploadBytes(context.Background(), fileContent, uniqueFileName)
}

// convertRelationship 转换关系字符串为标准关系类型
func (s *AbcYunPatientSyncSimpleService) convertRelationship(relation string) Relationship {
	switch relation {
	case "配偶", "丈夫", "妻子", "老公", "老婆":
		return RelationshipSpouse
	case "子女", "儿子", "女儿", "孩子":
		return RelationshipChild
	case "父母", "父亲", "母亲", "爸爸", "妈妈":
		return RelationshipParent
	case "本人", "自己":
		return RelationshipSelf
	default:
		return RelationshipOther
	}
}

// convertGender 转换性别
func (s *AbcYunPatientSyncSimpleService) convertGender(sex string) int {
	switch sex {
	case "男":
		return 1
	case "女":
		return 2
	default:
		return 0 // 未知
	}
}

// processOutpatientRecords 处理患者门诊记录
func (s *AbcYunPatientSyncSimpleService) processOutpatientRecords(patientID string, wxPatientID uint, dateStr string) error {
	// 1. 调用按患者查询门诊单接口
	outpatientSummaries, err := s.getOutpatientsByPatient(patientID, dateStr)
	if err != nil {
		return fmt.Errorf("查询患者门诊单失败: %w", err)
	}

	if len(outpatientSummaries) == 0 {
		logx.Infof("患者 %s 在日期 %s 没有门诊记录", patientID, dateStr)
		return nil
	}

	// 2. 对每个门诊单ID调用详情接口获取完整信息
	for _, summary := range outpatientSummaries {
		err := s.processOutpatientDetail(summary.ID, wxPatientID)
		if err != nil {
			logx.Errorf("处理门诊单详情失败 (ID: %s): %v", summary.ID, err)
			continue
		}
	}

	logx.Infof("处理患者 %s 门诊记录完成，共处理 %d 条记录", patientID, len(outpatientSummaries))
	return nil
}

// getOutpatientsByPatient 查询患者门诊单
func (s *AbcYunPatientSyncSimpleService) getOutpatientsByPatient(patientID, dateStr string) ([]OutpatientSummary, error) {
	// 构建查询路径
	path := fmt.Sprintf("/api/v2/open-agency/outpatient/query-by-patient/%s", patientID)

	// 构建查询参数
	queryParams := map[string]string{
		"beginDate": dateStr,
		"endDate":   dateStr,
		"limit":     "50",
		"offset":    "0",
	}

	// 调用API
	respBody, err := s.client.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("调用门诊查询API失败: %w", err)
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []OutpatientSummary `json:"rows"`
			Total  int                 `json:"total"`
			Offset int                 `json:"offset"`
			Limit  int                 `json:"limit"`
		} `json:"data"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析门诊查询响应失败: %w", err)
	}

	if result.Code != 0 {
		return nil, fmt.Errorf("门诊查询API返回错误: %s", result.Message)
	}

	return result.Data.Rows, nil
}

// processOutpatientDetail 处理门诊单详情
func (s *AbcYunPatientSyncSimpleService) processOutpatientDetail(outpatientID string, wxPatientID uint) error {
	// 获取门诊单详情
	detail, err := s.getOutpatientDetail(outpatientID)
	if err != nil {
		return fmt.Errorf("获取门诊单详情失败: %w", err)
	}

	// TODO: 实现门诊数据保存功能
	logx.Infof("获取到门诊单详情 (ID: %s), 包含:", detail.ID)

	// 处理病历记录
	if detail.MedicalRecord != nil {
		logx.Infof("- 病历记录: %s", detail.MedicalRecord.ID)
		err = s.saveMedicalRecord(detail.MedicalRecord, detail.ID, wxPatientID)
		if err != nil {
			logx.Errorf("保存病历记录失败: %v", err)
		}
	}

	// 处理中药处方
	if len(detail.PrescriptionChineseForms) > 0 {
		logx.Infof("- 中药处方: %d 条", len(detail.PrescriptionChineseForms))
		for _, chinesePrescription := range detail.PrescriptionChineseForms {
			err = s.saveChinesePrescription(&chinesePrescription, detail.ID, wxPatientID)
			if err != nil {
				logx.Errorf("保存中药处方失败: %v", err)
			}
		}
	}

	// 处理西药处方
	if len(detail.PrescriptionWesternForms) > 0 {
		logx.Infof("- 西药处方: %d 条", len(detail.PrescriptionWesternForms))
		for _, westernPrescription := range detail.PrescriptionWesternForms {
			err = s.saveWesternPrescription(&westernPrescription, detail.ID, wxPatientID, 1) // type=1表示西药
			if err != nil {
				logx.Errorf("保存西药处方失败: %v", err)
			}
		}
	}

	// 处理输液处方
	if len(detail.PrescriptionInfusionForms) > 0 {
		logx.Infof("- 输液处方: %d 条", len(detail.PrescriptionInfusionForms))
		for _, infusionPrescription := range detail.PrescriptionInfusionForms {
			err = s.saveWesternPrescription(&infusionPrescription, detail.ID, wxPatientID, 2) // type=2表示输液
			if err != nil {
				logx.Errorf("保存输液处方失败: %v", err)
			}
		}
	}

	// 处理外治处方
	if len(detail.PrescriptionExternalForms) > 0 {
		logx.Infof("- 外治处方: %d 条", len(detail.PrescriptionExternalForms))
		for _, externalPrescription := range detail.PrescriptionExternalForms {
			err = s.saveWesternPrescription(&externalPrescription, detail.ID, wxPatientID, 4) // type=4表示外治
			if err != nil {
				logx.Errorf("保存外治处方失败: %v", err)
			}
		}
	}

	// 处理治疗单
	if len(detail.ProductForms) > 0 {
		logx.Infof("- 治疗单: %d 条", len(detail.ProductForms))
		for _, treatmentForm := range detail.ProductForms {
			err = s.saveTreatmentPrescription(&treatmentForm, detail.ID, wxPatientID)
			if err != nil {
				logx.Errorf("保存治疗单失败: %v", err)
			}
		}
	}

	return nil
}

// getOutpatientDetail 获取门诊单详情
func (s *AbcYunPatientSyncSimpleService) getOutpatientDetail(outpatientID string) (*OutpatientDetail, error) {
	path := fmt.Sprintf("/api/v2/open-agency/outpatient/%s", outpatientID)

	respBody, err := s.client.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("调用门诊详情API失败: %w", err)
	}

	var result struct {
		Code    int              `json:"code"`
		Message string           `json:"message"`
		Data    OutpatientDetail `json:"data"`
	}

	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析门诊详情响应失败: %w", err)
	}

	if result.Code != 0 {
		return nil, fmt.Errorf("门诊详情API返回错误: %s", result.Message)
	}

	return &result.Data, nil
}

// saveMedicalRecord 保存病历记录
func (s *AbcYunPatientSyncSimpleService) saveMedicalRecord(medicalRecord *MedicalRecordInfo, outpatientSheetID string, wxPatientID uint) error {
	// 检查病历是否已存在（通过ABC云病历ID判重）
	var existingRecord patient.AbcYunMedicalRecord
	err := s.db.Where("external_id = ?", medicalRecord.ID).First(&existingRecord).Error
	if err == nil {
		// 病历已存在，更新记录
		logx.Infof("病历记录已存在，更新: %s", medicalRecord.ID)
		return s.updateMedicalRecord(&existingRecord, medicalRecord, outpatientSheetID, wxPatientID)
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询病历记录失败: %w", err)
	}

	// 创建新的病历记录
	return s.createMedicalRecord(medicalRecord, outpatientSheetID, wxPatientID)
}

// createMedicalRecord 创建病历记录
func (s *AbcYunPatientSyncSimpleService) createMedicalRecord(medicalRecord *MedicalRecordInfo, outpatientSheetID string, wxPatientID uint) error {
	// 将JSON字段转换为字符串
	eyeExaminationJSON := ""
	if medicalRecord.EyeExamination != nil {
		if jsonBytes, err := json.Marshal(medicalRecord.EyeExamination); err == nil {
			eyeExaminationJSON = string(jsonBytes)
		}
	}

	attachmentsJSON := ""
	if len(medicalRecord.Attachments) > 0 {
		if jsonBytes, err := json.Marshal(medicalRecord.Attachments); err == nil {
			attachmentsJSON = string(jsonBytes)
		}
	}

	// 创建病历记录
	record := patient.AbcYunMedicalRecord{
		PatientID:              wxPatientID,
		ExternalID:             medicalRecord.ID,
		OutpatientSheetID:      outpatientSheetID,
		ChiefComplaint:         medicalRecord.ChiefComplaint,
		PastHistory:            medicalRecord.PastHistory,
		FamilyHistory:          medicalRecord.FamilyHistory,
		PresentHistory:         medicalRecord.PresentHistory,
		PhysicalExamination:    medicalRecord.PhysicalExamination,
		Diagnosis:              medicalRecord.Diagnosis,
		DoctorAdvice:           medicalRecord.DoctorAdvice,
		Syndrome:               medicalRecord.Syndrome,
		Therapy:                medicalRecord.Therapy,
		ChineseExamination:     medicalRecord.ChineseExamination,
		WearGlassesHistory:     medicalRecord.WearGlassesHistory,
		EpidemiologicalHistory: medicalRecord.EpidemiologicalHistory,
		ObstetricalHistory:     medicalRecord.ObstetricalHistory,
		EyeExamination:         eyeExaminationJSON,
		AllergicHistory:        medicalRecord.AllergicHistory,
		AuxiliaryExamination:   medicalRecord.AuxiliaryExamination,
		Attachments:            attachmentsJSON,
	}

	err := s.db.Create(&record).Error
	if err != nil {
		return fmt.Errorf("创建病历记录失败: %w", err)
	}

	logx.Infof("成功创建病历记录: %s (患者ID: %d)", medicalRecord.ID, wxPatientID)
	return nil
}

// updateMedicalRecord 更新病历记录
func (s *AbcYunPatientSyncSimpleService) updateMedicalRecord(existingRecord *patient.AbcYunMedicalRecord, medicalRecord *MedicalRecordInfo, outpatientSheetID string, wxPatientID uint) error {
	// 将JSON字段转换为字符串
	eyeExaminationJSON := ""
	if medicalRecord.EyeExamination != nil {
		if jsonBytes, err := json.Marshal(medicalRecord.EyeExamination); err == nil {
			eyeExaminationJSON = string(jsonBytes)
		}
	}

	attachmentsJSON := ""
	if len(medicalRecord.Attachments) > 0 {
		if jsonBytes, err := json.Marshal(medicalRecord.Attachments); err == nil {
			attachmentsJSON = string(jsonBytes)
		}
	}

	// 更新字段
	updates := map[string]interface{}{
		"outpatient_sheet_id":     outpatientSheetID,
		"chief_complaint":         medicalRecord.ChiefComplaint,
		"past_history":            medicalRecord.PastHistory,
		"family_history":          medicalRecord.FamilyHistory,
		"present_history":         medicalRecord.PresentHistory,
		"physical_examination":    medicalRecord.PhysicalExamination,
		"diagnosis":               medicalRecord.Diagnosis,
		"doctor_advice":           medicalRecord.DoctorAdvice,
		"syndrome":                medicalRecord.Syndrome,
		"therapy":                 medicalRecord.Therapy,
		"chinese_examination":     medicalRecord.ChineseExamination,
		"wear_glasses_history":    medicalRecord.WearGlassesHistory,
		"epidemiological_history": medicalRecord.EpidemiologicalHistory,
		"obstetrical_history":     medicalRecord.ObstetricalHistory,
		"eye_examination":         eyeExaminationJSON,
		"allergic_history":        medicalRecord.AllergicHistory,
		"auxiliary_examination":   medicalRecord.AuxiliaryExamination,
		"attachments":             attachmentsJSON,
	}

	err := s.db.Model(existingRecord).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新病历记录失败: %w", err)
	}

	logx.Infof("成功更新病历记录: %s (患者ID: %d)", medicalRecord.ID, wxPatientID)
	return nil
}

// saveChinesePrescription 保存中药处方
func (s *AbcYunPatientSyncSimpleService) saveChinesePrescription(prescription *PrescriptionForm, outpatientSheetID string, wxPatientID uint) error {
	// 检查中药处方是否已存在（通过ABC云处方ID判重）
	var existingPrescription patient.AbcYunChinesePrescription
	err := s.db.Where("external_id = ?", prescription.ID).First(&existingPrescription).Error
	if err == nil {
		// 处方已存在，更新记录
		logx.Infof("中药处方已存在，更新: %s", prescription.ID)
		return s.updateChinesePrescription(&existingPrescription, prescription, outpatientSheetID, wxPatientID)
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询中药处方失败: %w", err)
	}

	// 创建新的中药处方记录
	return s.createChinesePrescription(prescription, outpatientSheetID, wxPatientID)
}

// createChinesePrescription 创建中药处方记录
func (s *AbcYunPatientSyncSimpleService) createChinesePrescription(prescription *PrescriptionForm, outpatientSheetID string, wxPatientID uint) error {
	// 创建中药处方主记录
	chinesePrescription := patient.AbcYunChinesePrescription{
		PatientID:         wxPatientID,
		ExternalID:        prescription.ID,
		OutpatientSheetID: outpatientSheetID,
		Type:              prescription.Type,
		Specification:     prescription.Specification,
		DoseCount:         prescription.DoseCount,
		DailyDosage:       prescription.DailyDosage,
		Usage:             prescription.Usage,
		Freq:              prescription.Freq,
		Requirement:       prescription.Requirement,
		UsageLevel:        prescription.UsageLevel,
	}

	err := s.db.Create(&chinesePrescription).Error
	if err != nil {
		return fmt.Errorf("创建中药处方记录失败: %w", err)
	}

	// 保存处方单项
	for _, item := range prescription.PrescriptionFormItems {
		err = s.createChinesePrescriptionItem(&item, chinesePrescription.ID, wxPatientID)
		if err != nil {
			logx.Errorf("保存中药处方单项失败: %v", err)
			continue
		}
	}

	logx.Infof("成功创建中药处方记录: %s (患者ID: %d), 包含 %d 个单项", prescription.ID, wxPatientID, len(prescription.PrescriptionFormItems))
	return nil
}

// updateChinesePrescription 更新中药处方记录
func (s *AbcYunPatientSyncSimpleService) updateChinesePrescription(existingPrescription *patient.AbcYunChinesePrescription, prescription *PrescriptionForm, outpatientSheetID string, wxPatientID uint) error {
	// 更新主处方记录
	updates := map[string]interface{}{
		"outpatient_sheet_id": outpatientSheetID,
		"type":                prescription.Type,
		"specification":       prescription.Specification,
		"dose_count":          prescription.DoseCount,
		"daily_dosage":        prescription.DailyDosage,
		"usage":               prescription.Usage,
		"freq":                prescription.Freq,
		"requirement":         prescription.Requirement,
		"usage_level":         prescription.UsageLevel,
	}

	err := s.db.Model(existingPrescription).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新中药处方记录失败: %w", err)
	}

	// 删除已有的处方单项
	err = s.db.Where("prescription_id = ?", existingPrescription.ID).Delete(&patient.AbcYunChinesePrescriptionItem{}).Error
	if err != nil {
		logx.Errorf("删除旧的中药处方单项失败: %v", err)
	}

	// 重新创建处方单项
	for _, item := range prescription.PrescriptionFormItems {
		err = s.createChinesePrescriptionItem(&item, existingPrescription.ID, wxPatientID)
		if err != nil {
			logx.Errorf("保存中药处方单项失败: %v", err)
			continue
		}
	}

	logx.Infof("成功更新中药处方记录: %s (患者ID: %d), 包含 %d 个单项", prescription.ID, wxPatientID, len(prescription.PrescriptionFormItems))
	return nil
}

// createChinesePrescriptionItem 创建中药处方单项记录
func (s *AbcYunPatientSyncSimpleService) createChinesePrescriptionItem(item *PrescriptionFormItem, prescriptionID uint, wxPatientID uint) error {
	// 将JSON字段转换为字符串
	astResultJSON := ""
	if item.AstResult != nil {
		if jsonBytes, err := json.Marshal(item.AstResult); err == nil {
			astResultJSON = string(jsonBytes)
		}
	}

	productInfoJSON := ""
	if item.ProductInfo != nil {
		if jsonBytes, err := json.Marshal(item.ProductInfo); err == nil {
			productInfoJSON = string(jsonBytes)
		}
	}

	// 创建处方单项记录
	prescriptionItem := patient.AbcYunChinesePrescriptionItem{
		PatientID:          wxPatientID,
		PrescriptionID:     prescriptionID,
		ExternalID:         item.ID,
		PrescriptionFormID: item.PrescriptionFormID,
		ProductID:          item.ProductID,
		Type:               item.Type,
		SubType:            item.SubType,
		Name:               item.Name,
		Specification:      item.Specification,
		Manufacturer:       item.Manufacturer,
		Usage:              item.Usage,
		Ivgtt:              item.Ivgtt,
		IvgttUnit:          item.IvgttUnit,
		Freq:               item.Freq,
		Dosage:             item.Dosage,
		DosageUnit:         item.DosageUnit,
		Days:               item.Days,
		SpecialRequirement: item.SpecialRequirement,
		IsDismounting:      item.IsDismounting,
		UnitCount:          item.UnitCount,
		Unit:               item.Unit,
		UnitPrice:          item.UnitPrice,
		GroupID:            item.GroupID,
		IsAst:              item.IsAst,
		AstResult:          astResultJSON,
		ProductInfo:        productInfoJSON,
	}

	err := s.db.Create(&prescriptionItem).Error
	if err != nil {
		return fmt.Errorf("创建中药处方单项记录失败: %w", err)
	}

	return nil
}

// saveWesternPrescription 保存西药处方（包含西药、输液、外治）
func (s *AbcYunPatientSyncSimpleService) saveWesternPrescription(prescription *PrescriptionForm, outpatientSheetID string, wxPatientID uint, prescriptionType int) error {
	// 检查西药处方是否已存在（通过ABC云处方ID判重）
	var existingPrescription patient.AbcYunWesternPrescription
	err := s.db.Where("external_id = ?", prescription.ID).First(&existingPrescription).Error
	if err == nil {
		// 处方已存在，更新记录
		logx.Infof("西药处方已存在，更新: %s", prescription.ID)
		return s.updateWesternPrescription(&existingPrescription, prescription, outpatientSheetID, wxPatientID, prescriptionType)
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询西药处方失败: %w", err)
	}

	// 创建新的西药处方记录
	return s.createWesternPrescription(prescription, outpatientSheetID, wxPatientID, prescriptionType)
}

// createWesternPrescription 创建西药处方记录
func (s *AbcYunPatientSyncSimpleService) createWesternPrescription(prescription *PrescriptionForm, outpatientSheetID string, wxPatientID uint, prescriptionType int) error {
	// 创建西药处方主记录
	westernPrescription := patient.AbcYunWesternPrescription{
		PatientID:         wxPatientID,
		ExternalID:        prescription.ID,
		OutpatientSheetID: outpatientSheetID,
		Type:              prescriptionType, // 1:西药, 2:输液, 4:外治
		Specification:     prescription.Specification,
		DoseCount:         prescription.DoseCount,
		DailyDosage:       prescription.DailyDosage,
		Usage:             prescription.Usage,
		Freq:              prescription.Freq,
		Requirement:       prescription.Requirement,
		UsageLevel:        prescription.UsageLevel,
	}

	err := s.db.Create(&westernPrescription).Error
	if err != nil {
		return fmt.Errorf("创建西药处方记录失败: %w", err)
	}

	// 保存处方单项
	for _, item := range prescription.PrescriptionFormItems {
		err = s.createWesternPrescriptionItem(&item, westernPrescription.ID, wxPatientID)
		if err != nil {
			logx.Errorf("保存西药处方单项失败: %v", err)
			continue
		}
	}

	prescriptionTypeName := s.getPrescriptionTypeName(prescriptionType)
	logx.Infof("成功创建%s处方记录: %s (患者ID: %d), 包含 %d 个单项", prescriptionTypeName, prescription.ID, wxPatientID, len(prescription.PrescriptionFormItems))
	return nil
}

// updateWesternPrescription 更新西药处方记录
func (s *AbcYunPatientSyncSimpleService) updateWesternPrescription(existingPrescription *patient.AbcYunWesternPrescription, prescription *PrescriptionForm, outpatientSheetID string, wxPatientID uint, prescriptionType int) error {
	// 更新主处方记录
	updates := map[string]interface{}{
		"outpatient_sheet_id": outpatientSheetID,
		"type":                prescriptionType,
		"specification":       prescription.Specification,
		"dose_count":          prescription.DoseCount,
		"daily_dosage":        prescription.DailyDosage,
		"usage":               prescription.Usage,
		"freq":                prescription.Freq,
		"requirement":         prescription.Requirement,
		"usage_level":         prescription.UsageLevel,
	}

	err := s.db.Model(existingPrescription).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新西药处方记录失败: %w", err)
	}

	// 删除已有的处方单项
	err = s.db.Where("prescription_id = ?", existingPrescription.ID).Delete(&patient.AbcYunWesternPrescriptionItem{}).Error
	if err != nil {
		logx.Errorf("删除旧的西药处方单项失败: %v", err)
	}

	// 重新创建处方单项
	for _, item := range prescription.PrescriptionFormItems {
		err = s.createWesternPrescriptionItem(&item, existingPrescription.ID, wxPatientID)
		if err != nil {
			logx.Errorf("保存西药处方单项失败: %v", err)
			continue
		}
	}

	prescriptionTypeName := s.getPrescriptionTypeName(prescriptionType)
	logx.Infof("成功更新%s处方记录: %s (患者ID: %d), 包含 %d 个单项", prescriptionTypeName, prescription.ID, wxPatientID, len(prescription.PrescriptionFormItems))
	return nil
}

// createWesternPrescriptionItem 创建西药处方单项记录
func (s *AbcYunPatientSyncSimpleService) createWesternPrescriptionItem(item *PrescriptionFormItem, prescriptionID uint, wxPatientID uint) error {
	// 将JSON字段转换为字符串
	astResultJSON := ""
	if item.AstResult != nil {
		if jsonBytes, err := json.Marshal(item.AstResult); err == nil {
			astResultJSON = string(jsonBytes)
		}
	}

	productInfoJSON := ""
	if item.ProductInfo != nil {
		if jsonBytes, err := json.Marshal(item.ProductInfo); err == nil {
			productInfoJSON = string(jsonBytes)
		}
	}

	// 创建处方单项记录
	prescriptionItem := patient.AbcYunWesternPrescriptionItem{
		PatientID:          wxPatientID,
		PrescriptionID:     prescriptionID,
		ExternalID:         item.ID,
		PrescriptionFormID: item.PrescriptionFormID,
		ProductID:          item.ProductID,
		Type:               item.Type,
		SubType:            item.SubType,
		Name:               item.Name,
		Specification:      item.Specification,
		Manufacturer:       item.Manufacturer,
		Usage:              item.Usage,
		Ivgtt:              item.Ivgtt,
		IvgttUnit:          item.IvgttUnit,
		Freq:               item.Freq,
		Dosage:             item.Dosage,
		DosageUnit:         item.DosageUnit,
		Days:               item.Days,
		SpecialRequirement: item.SpecialRequirement,
		IsDismounting:      item.IsDismounting,
		UnitCount:          item.UnitCount,
		Unit:               item.Unit,
		UnitPrice:          item.UnitPrice,
		GroupID:            item.GroupID,
		IsAst:              item.IsAst,
		AstResult:          astResultJSON,
		ProductInfo:        productInfoJSON,
	}

	err := s.db.Create(&prescriptionItem).Error
	if err != nil {
		return fmt.Errorf("创建西药处方单项记录失败: %w", err)
	}

	return nil
}

// getPrescriptionTypeName 获取处方类型名称
func (s *AbcYunPatientSyncSimpleService) getPrescriptionTypeName(prescriptionType int) string {
	switch prescriptionType {
	case 1:
		return "西药"
	case 2:
		return "输液"
	case 3:
		return "中药"
	case 4:
		return "外治"
	default:
		return "未知"
	}
}

// saveTreatmentPrescription 保存治疗单
func (s *AbcYunPatientSyncSimpleService) saveTreatmentPrescription(productForm *ProductForm, outpatientSheetID string, wxPatientID uint) error {
	// 检查治疗单是否已存在（通过ABC云治疗单ID判重）
	var existingTreatment patient.AbcYunTreatmentPrescription
	err := s.db.Where("external_id = ?", productForm.ID).First(&existingTreatment).Error
	if err == nil {
		// 治疗单已存在，更新记录
		logx.Infof("治疗单已存在，更新: %s", productForm.ID)
		return s.updateTreatmentPrescription(&existingTreatment, productForm, outpatientSheetID, wxPatientID)
	}
	if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询治疗单失败: %w", err)
	}

	// 创建新的治疗单记录
	return s.createTreatmentPrescription(productForm, outpatientSheetID, wxPatientID)
}

// createTreatmentPrescription 创建治疗单记录
func (s *AbcYunPatientSyncSimpleService) createTreatmentPrescription(productForm *ProductForm, outpatientSheetID string, wxPatientID uint) error {
	// 创建治疗单主记录
	treatmentPrescription := patient.AbcYunTreatmentPrescription{
		PatientID:         wxPatientID,
		ExternalID:        productForm.ID,
		OutpatientSheetID: outpatientSheetID,
		Type:              productForm.Type,
	}

	err := s.db.Create(&treatmentPrescription).Error
	if err != nil {
		return fmt.Errorf("创建治疗单记录失败: %w", err)
	}

	// 保存治疗单子项
	for _, item := range productForm.ProductFormItems {
		err = s.createTreatmentPrescriptionItem(&item, treatmentPrescription.ID, wxPatientID)
		if err != nil {
			logx.Errorf("保存治疗单子项失败: %v", err)
			continue
		}
	}

	treatmentTypeName := s.getTreatmentTypeName(productForm.Type)
	logx.Infof("成功创建%s记录: %s (患者ID: %d), 包含 %d 个子项", treatmentTypeName, productForm.ID, wxPatientID, len(productForm.ProductFormItems))
	return nil
}

// updateTreatmentPrescription 更新治疗单记录
func (s *AbcYunPatientSyncSimpleService) updateTreatmentPrescription(existingTreatment *patient.AbcYunTreatmentPrescription, productForm *ProductForm, outpatientSheetID string, wxPatientID uint) error {
	// 更新主治疗单记录
	updates := map[string]interface{}{
		"outpatient_sheet_id": outpatientSheetID,
		"type":                productForm.Type,
	}

	err := s.db.Model(existingTreatment).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新治疗单记录失败: %w", err)
	}

	// 删除已有的治疗单子项
	err = s.db.Where("prescription_id = ?", existingTreatment.ID).Delete(&patient.AbcYunTreatmentPrescriptionItem{}).Error
	if err != nil {
		logx.Errorf("删除旧的治疗单子项失败: %v", err)
	}

	// 重新创建治疗单子项
	for _, item := range productForm.ProductFormItems {
		err = s.createTreatmentPrescriptionItem(&item, existingTreatment.ID, wxPatientID)
		if err != nil {
			logx.Errorf("保存治疗单子项失败: %v", err)
			continue
		}
	}

	treatmentTypeName := s.getTreatmentTypeName(productForm.Type)
	logx.Infof("成功更新%s记录: %s (患者ID: %d), 包含 %d 个子项", treatmentTypeName, productForm.ID, wxPatientID, len(productForm.ProductFormItems))
	return nil
}

// createTreatmentPrescriptionItem 创建治疗单子项记录
func (s *AbcYunPatientSyncSimpleService) createTreatmentPrescriptionItem(item *ProductFormItem, prescriptionID uint, wxPatientID uint) error {
	// 创建治疗单子项记录
	treatmentItem := patient.AbcYunTreatmentPrescriptionItem{
		PatientID:      wxPatientID,
		PrescriptionID: prescriptionID,
		ExternalID:     item.ID,
		ProductFormID:  item.ProductFormID,
		ProductID:      item.ProductID,
		Name:           item.Name,
		UnitCount:      item.UnitCount,
		Unit:           item.Unit,
		UnitPrice:      item.UnitPrice,
		IsDismounting:  item.IsDismounting,
		Type:           item.Type,
		SubType:        item.SubType,
		Days:           item.Days,
		DailyDosage:    item.DailyDosage,
		Remark:         item.Remark,
	}

	err := s.db.Create(&treatmentItem).Error
	if err != nil {
		return fmt.Errorf("创建治疗单子项记录失败: %w", err)
	}

	return nil
}

// getTreatmentTypeName 获取治疗类型名称
func (s *AbcYunPatientSyncSimpleService) getTreatmentTypeName(treatmentType int) string {
	switch treatmentType {
	case 2:
		return "检查检验"
	case 3:
		return "治疗理疗"
	case 8:
		return "商品"
	case 9:
		return "材料"
	default:
		return "治疗单"
	}
}
