package response

import (
	"net/http"

	jsoniter "github.com/json-iterator/go"
)

// 创建json-iterator实例
var json = jsoniter.ConfigCompatibleWithStandardLibrary

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	List       interface{} `json:"list"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// Success 成功响应
func Success(w http.ResponseWriter, data interface{}) {
	w.<PERSON>er().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(Response{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// Error 错误响应
func Error(w http.ResponseWriter, code int, message string) {
	w.Head<PERSON>().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(Response{
		Code:    code,
		Message: message,
	})
}

// // GinSuccess Gin框架成功响应
// func GinSuccess(c *gin.Context, data interface{}) {
// 	c.JSON(http.StatusOK, Response{
// 		Code:    CodeSuccess,
// 		Message: "success",
// 		Data:    data,
// 	})
// }

// // GinError Gin框架错误响应
// func GinError(c *gin.Context, code int, message string) {
// 	c.JSON(http.StatusOK, Response{
// 		Code:    code,
// 		Message: message,
// 	})
// }

// // GinSuccessWithPage Gin框架分页响应
// func GinSuccessWithPage(c *gin.Context, list interface{}, total int64, page, pageSize int) {
// 	totalPages := int(total) / pageSize
// 	if int(total)%pageSize > 0 {
// 		totalPages++
// 	}

// 	pageResp := PageResponse{
// 		List:       list,
// 		Total:      total,
// 		Page:       page,
// 		PageSize:   pageSize,
// 		TotalPages: totalPages,
// 	}

// 	c.JSON(http.StatusOK, Response{
// 		Code:    CodeSuccess,
// 		Message: "success",
// 		Data:    pageResp,
// 	})
// }

// 预定义错误码常量
const (
	CodeSuccess            = 200 // 成功
	CodeInvalidParams      = 400 // 参数错误
	CodeUnauthorized       = 401 // 未授权
	CodeForbidden          = 403 // 权限不足
	CodeNotFound           = 404 // 资源不存在
	CodeMethodNotAllowed   = 405 // 方法不允许
	CodeInternalError      = 500 // 服务器内部错误
	CodeServerError        = 500 // 服务器错误（别名）
	CodeServiceUnavailable = 503 // 服务不可用

	// 业务错误码（1000+）
	CodeUserNotFound    = 1001 // 用户不存在
	CodePasswordError   = 1002 // 密码错误
	CodeTokenExpired    = 1003 // 令牌已过期
	CodeTokenInvalid    = 1004 // 无效的令牌
	CodeDatabaseError   = 1005 // 数据库操作失败
	CodeDuplicateRecord = 1006 // 记录已存在
)
