package routes

import (
	"net/http"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/appointment/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterAbcYunRoutes 注册ABC云相关路由
func RegisterAbcYunRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 添加路由
	server.AddRoutes([]rest.Route{

		// 患者接口 - 9个患者相关接口
		// // 4.1.1. 获取患者分页列表
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/patient",
		// 	Handler: wxAuthWrapper(handler.AbcYunPatientListHandler),
		// },
		// // 4.1.2. 查询患者
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/patient/query",
		// 	Handler: wxAuthWrapper(handler.AbcYunPatientQueryHandler),
		// },
		// 4.1.3. 创建患者
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/abcyun/patient",
			Handler: wxAuthWrapper(handler.AbcYunCreatePatientHandler),
		},
		// 4.1.4. 获取患者详情
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/patient/:id",
		// 	Handler: wxAuthWrapper(handler.AbcYunPatientDetailHandler),
		// },
		// // 4.1.5. 更新患者
		// {
		// 	Method:  http.MethodPut,
		// 	Path:    "/api/abcyun/patient/:id",
		// 	Handler: wxAuthWrapper(handler.AbcYunUpdatePatientHandler),
		// },
		// // 4.1.6. 获取患者附件列表
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/patient/:patientId/attachment",
		// 	Handler: wxAuthWrapper(handler.AbcYunPatientAttachmentListHandler),
		// },
		// // 4.1.7. 获取患者附件
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/patient/:patientId/attachment/:attachmentId",
		// 	Handler: wxAuthWrapper(handler.AbcYunPatientAttachmentHandler),
		// },
		// // 4.1.8. 添加患者附件
		// {
		// 	Method:  http.MethodPost,
		// 	Path:    "/api/abcyun/patient/:patientId/attachment",
		// 	Handler: wxAuthWrapper(handler.AbcYunAddPatientAttachmentsHandler),
		// },
		// // 4.1.9. 获取患者家庭成员
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/patient/:patientId/family-member",
		// 	Handler: wxAuthWrapper(handler.AbcYunPatientFamilyMemberHandler),
		// },

		// 挂号接口 - 14个挂号相关接口
		// 5.5.1. 获取科室医生的号源详情
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/doctor/:doctorId",
		// 	Handler: wxAuthWrapper(handler.AbcYunDoctorScheduleHandler),
		// },
		// 5.5.2. 创建挂号
		// {
		// 	Method:  http.MethodPost,
		// 	Path:    "/api/abcyun/registration",
		// 	Handler: wxAuthWrapper(handler.AbcYunCreateRegistrationHandler),
		// },
		// // 5.5.3. 获取挂号详情
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/:registrationSheetId",
		// 	Handler: wxAuthWrapper(handler.AbcYunRegistrationDetailHandler),
		// },
		// // 5.5.4. 通过就诊单ID获取挂号详情
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/by-patient-order-id/:patientOrderId",
		// 	Handler: wxAuthWrapper(handler.AbcYunRegistrationByPatientOrderIDHandler),
		// },
		// // 5.5.5. 取消挂号
		// {
		// 	Method:  http.MethodPut,
		// 	Path:    "/api/abcyun/registration/:registrationSheetId/cancel",
		// 	Handler: wxAuthWrapper(handler.AbcYunCancelRegistrationHandler),
		// },
		// 5.5.6. 查询预约备注模板
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/remark-templates",
		// 	Handler: wxAuthWrapper(handler.AbcYunRemarkTemplatesHandler),
		// },
		// // 5.5.7. 查询医生可预约项目列表
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/doctor/:doctorId/product",
		// 	Handler: wxAuthWrapper(handler.AbcYunDoctorRegistrationProductsHandler),
		// },
		// // 5.5.8. 查询门店可预约项目列表
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/product",
		// 	Handler: wxAuthWrapper(handler.AbcYunRegistrationProductsHandler),
		// },
		// // 5.5.9. 查询项目每日号源
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/product/:registrationProductId/section-status",
		// 	Handler: wxAuthWrapper(handler.AbcYunRegistrationProductSectionStatusHandler),
		// },
		// // 5.5.10. 查询项目指定日期排班信息
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/product/:registrationProductId/days-shifts",
		// 	Handler: wxAuthWrapper(handler.AbcYunRegistrationProductDaysShiftsHandler),
		// },
		// // 5.5.11. 查询患者预约列表
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/patient/:patientId",
		// 	Handler: wxAuthWrapper(handler.AbcYunPatientRegistrationsHandler),
		// },
		// // 5.5.12. 查询门店指定日期医生号源状态
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration/doctors-shift-status",
		// 	Handler: wxAuthWrapper(handler.AbcYunDoctorsShiftStatusHandler),
		// },
		// 5.5.13. 查询门店指定医生号源日期列表
		{
			Method:  http.MethodGet,
			Path:    "/api/abcyun/registration/doctor/:doctorId/shift",
			Handler: wxAuthWrapper(handler.AbcYunDoctorShiftHandler),
		},
		// 5.5.14. 查询挂号单列表
		// {
		// 	Method:  http.MethodGet,
		// 	Path:    "/api/abcyun/registration",
		// 	Handler: wxAuthWrapper(handler.AbcYunRegistrationsHandler),
		// },
	})
}
