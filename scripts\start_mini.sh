#!/bin/bash

# 获取环境参数，默认为dev
ENV=${RUN_ENV:-${DEPLOY_ENV:-dev}}

# 创建日志目录
mkdir -p $LOG_PATH
chmod 755 -R $LOG_PATH

echo "启动服务: $APP_NAME"
echo "日志路径: $LOG_PATH"
echo "环境: $ENV"

# 切换到应用目录
cd ${APP_PATH:-$(pwd)}

# 打印调试信息
echo "当前工作目录: $(pwd)"
echo "配置文件检查:"
ls -la etc/ 2>/dev/null || echo "etc目录不存在"
echo "可执行文件检查:"
ls -la main 2>/dev/null || echo "main文件不存在"

# 执行编译好的可执行文件
# 只启动后台管理服务（端口 8889）
# ./main -env prod -service admin

# # 只启动小程序服务（端口 8888）
# ./main -env prod -service mini

# # 启动所有服务（默认模式）
# ./main -env prod -service all
./main -env $ENV -service mini
