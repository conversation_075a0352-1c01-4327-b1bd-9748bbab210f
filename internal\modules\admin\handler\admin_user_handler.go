package handler

import (
	"errors"
	"fmt"
	"net/http"
	"time"

	"yekaitai/internal/modules/admin/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 管理员列表请求
type AdminListRequest struct {
	types.PageRequest
}

// 创建管理员请求
type CreateAdminRequest struct {
	Username  string `json:"username"`   // 用户名
	Password  string `json:"password"`   // 密码
	Email     string `json:"email"`      // 邮箱
	Mobile    string `json:"mobile"`     // 手机号
	Status    int    `json:"status"`     // 状态：1-正常，0-禁用
	CreatorID uint   `json:"creator_id"` // 创建者ID
	RoleIDs   []uint `json:"roleIds"`    // 角色ID列表
}

// 更新管理员请求
type UpdateAdminRequest struct {
	AdminID  uint   `path:"adminId"`           // 管理员ID
	Username string `json:"username,optional"` // 用户名
	Password string `json:"password,optional"` // 密码，可选
	Email    string `json:"email,optional"`    // 邮箱
	Mobile   string `json:"mobile,optional"`   // 手机号
	Status   int    `json:"status,optional"`   // 状态：1-正常，0-禁用
	RoleIDs  []uint `json:"roleIds,optional"`  // 角色ID列表
	StoreID  *uint  `json:"store_id,optional"` // 门店ID
}

// 管理员详情请求
type AdminDetailRequest struct {
	AdminID uint `path:"adminId"` // 管理员ID
}

// 管理员禁用/启用请求
type UpdateAdminStatusRequest struct {
	AdminID uint `path:"adminId"` // 管理员ID
	Status  int  `json:"status"`  // 状态：1-正常，0-禁用
}

// 删除管理员请求
type DeleteAdminRequest struct {
	AdminID uint `path:"adminId"` // 管理员ID
}

// AdminUserHandler 管理员用户处理器
type AdminUserHandler struct {
	svcCtx *svc.ServiceContext
}

// NewAdminUserHandler 创建管理员用户处理器
func NewAdminUserHandler(svcCtx *svc.ServiceContext) *AdminUserHandler {
	return &AdminUserHandler{
		svcCtx: svcCtx,
	}
}

// GetAdminUsersHandler 获取管理员列表
func GetAdminUsersHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req AdminListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		adminRepo := model.NewAdminUserRepository()
		admins, total, err := adminRepo.List(req.Page, req.Size, req.Query)
		if err != nil {
			logx.Errorf("获取管理员列表失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取管理员列表失败"))
			return
		}

		// 获取每个管理员的角色
		rbacRepo := model.NewAdminRBACRepository(nil)
		adminWithRoles := make([]map[string]interface{}, len(admins))

		for i, admin := range admins {
			roles, err := rbacRepo.GetAdminRoles(admin.AdminID)
			if err != nil {
				logx.Errorf("获取管理员角色失败: %v", err)
				roles = []model.AdminRole{} // 使用空数组代替nil
			}

			adminWithRoles[i] = map[string]interface{}{
				"adminId":   admin.AdminID,
				"username":  admin.Username,
				"email":     admin.Email,
				"mobile":    admin.Mobile,
				"status":    admin.Status,
				"lastLogin": admin.LastLogin,
				"lastIP":    admin.LastIP,
				"createdAt": admin.CreatedAt,
				"updatedAt": admin.UpdatedAt,
				"roles":     roles,
			}
		}

		result := map[string]interface{}{
			"list":  adminWithRoles,
			"total": total,
			"page":  req.Page,
			"size":  req.Size,
		}

		httpx.OkJson(w, types.NewSuccessResponse(result, "获取管理员列表成功"))
	}
}

// GetAdminUserHandler 获取管理员详情
func GetAdminUserHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req AdminDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		adminRepo := model.NewAdminUserRepository()
		admin, err := adminRepo.FindByID(req.AdminID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "管理员不存在"))
			} else {
				logx.Errorf("获取管理员详情失败: %v", err)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取管理员详情失败"))
			}
			return
		}

		// 获取管理员角色
		rbacRepo := model.NewAdminRBACRepository(nil)
		roles, err := rbacRepo.GetAdminRoles(admin.AdminID)
		if err != nil {
			logx.Errorf("获取管理员角色失败: %v", err)
			roles = []model.AdminRole{} // 使用空数组代替nil
		}

		result := map[string]interface{}{
			"adminId":   admin.AdminID,
			"username":  admin.Username,
			"email":     admin.Email,
			"mobile":    admin.Mobile,
			"status":    admin.Status,
			"lastLogin": admin.LastLogin,
			"lastIP":    admin.LastIP,
			"createdAt": admin.CreatedAt,
			"updatedAt": admin.UpdatedAt,
			"roles":     roles,
		}

		httpx.OkJson(w, types.NewSuccessResponse(result, "获取管理员详情成功"))
	}
}

// CreateAdminUserHandler 创建管理员
func CreateAdminUserHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req CreateAdminRequest
		if err := httpx.ParseJsonBody(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 参数验证
		if req.Username == "" || req.Password == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "用户名和密码不能为空"))
			return
		}

		// 检查用户名是否已存在
		adminRepo := model.NewAdminUserRepository()
		existingAdmin, err := adminRepo.FindByUsername(req.Username)
		if err == nil && existingAdmin != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUserExists, "用户名已存在"))
			return
		}

		// 密码加密
		hashedPassword, err := utils.HashPassword(req.Password)
		if err != nil {
			logx.Errorf("密码加密失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建管理员失败"))
			return
		}

		// 创建管理员
		now := time.Now()
		admin := model.AdminUser{
			Username:  req.Username,
			Password:  hashedPassword,
			Email:     req.Email,
			Mobile:    req.Mobile,
			Status:    req.Status,
			CreatorID: req.CreatorID,
			LastLogin: now,
			CreatedAt: now,
			UpdatedAt: now,
		}

		if err := adminRepo.Create(&admin); err != nil {
			logx.Errorf("创建管理员失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建管理员失败"))
			return
		}

		// 分配角色
		if len(req.RoleIDs) > 0 {
			rbacRepo := model.NewAdminRBACRepository(nil)
			if err := rbacRepo.AssignRolesToUser(admin.AdminID, req.RoleIDs); err != nil {
				logx.Errorf("分配管理员角色失败: %v", err)
				// 不返回错误，继续处理
			}
		}

		// 记录操作日志
		log := &model.AdminOperationLog{
			AdminID:    admin.AdminID,
			Module:     "admin_user",
			Action:     "create",
			TargetID:   admin.AdminID,
			TargetType: "admin_user",
			Content:    "创建管理员",
			IP:         r.RemoteAddr,
			CreatedAt:  time.Now(),
		}
		serverCtx.AdminOperationLogRepo.Create(log)

		httpx.OkJson(w, types.NewSuccessResponse(admin, "创建管理员成功"))
	}
}

// UpdateAdminUserHandler 更新管理员
func UpdateAdminUserHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req UpdateAdminRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		adminRepo := model.NewAdminUserRepository()
		admin, err := adminRepo.FindByID(req.AdminID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "管理员不存在"))
			} else {
				logx.Errorf("获取管理员失败: %v", err)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员失败"))
			}
			return
		}

		// 检查用户名是否已被其他用户使用
		if req.Username != admin.Username {
			existingAdmin, err := adminRepo.FindByUsername(req.Username)
			if err == nil && existingAdmin != nil && existingAdmin.AdminID != req.AdminID {
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUserExists, "用户名已存在"))
				return
			}
		}

		// 更新管理员信息
		admin.Username = req.Username
		admin.Email = req.Email
		admin.Mobile = req.Mobile
		admin.Status = req.Status
		admin.UpdatedAt = time.Now()

		// 如果提供了密码，则更新密码
		if req.Password != "" {
			hashedPassword, err := utils.HashPassword(req.Password)
			if err != nil {
				logx.Errorf("密码加密失败: %v", err)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员失败"))
				return
			}
			admin.Password = hashedPassword
		}

		if err := adminRepo.Update(admin); err != nil {
			logx.Errorf("更新管理员失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员失败"))
			return
		}

		// 更新角色
		if len(req.RoleIDs) > 0 {
			rbacRepo := model.NewAdminRBACRepository(nil)
			if err := rbacRepo.AssignRolesToUser(admin.AdminID, req.RoleIDs); err != nil {
				logx.Errorf("更新管理员角色失败: %v", err)
				// 不返回错误，继续处理
			}
		}

		// 记录操作日志
		log := &model.AdminOperationLog{
			AdminID:    admin.AdminID,
			Module:     "admin_user",
			Action:     "update",
			TargetID:   admin.AdminID,
			TargetType: "admin_user",
			Content:    "更新管理员信息",
			IP:         r.RemoteAddr,
			CreatedAt:  time.Now(),
		}
		serverCtx.AdminOperationLogRepo.Create(log)

		httpx.OkJson(w, types.NewSuccessResponse(nil, "更新管理员成功"))
	}
}

// UpdateAdminUserStatusHandler 更新管理员状态
func UpdateAdminUserStatusHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req UpdateAdminStatusRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 验证状态值
		if req.Status != 0 && req.Status != 1 {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "状态值无效，只能是0或1"))
			return
		}

		adminRepo := model.NewAdminUserRepository()

		// 禁止修改系统管理员状态
		if req.AdminID == 1 {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "不能修改系统管理员状态"))
			return
		}

		// 更新状态
		if err := adminRepo.UpdateStatus(req.AdminID, req.Status); err != nil {
			logx.Errorf("更新管理员状态失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员状态失败"))
			return
		}

		statusText := "启用"
		if req.Status == 0 {
			statusText = "禁用"
		}

		// 记录操作日志
		log := &model.AdminOperationLog{
			AdminID:    req.AdminID,
			Module:     "admin_user",
			Action:     "update_status",
			TargetID:   req.AdminID,
			TargetType: "admin_user",
			Content:    "管理员" + statusText + "成功",
			IP:         r.RemoteAddr,
			CreatedAt:  time.Now(),
		}
		serverCtx.AdminOperationLogRepo.Create(log)

		httpx.OkJson(w, types.NewSuccessResponse(nil, "管理员"+statusText+"成功"))
	}
}

// DeleteAdminUserHandler 删除管理员
func DeleteAdminUserHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req DeleteAdminRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 禁止删除系统管理员
		if req.AdminID == 1 {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "不能删除系统管理员"))
			return
		}

		adminRepo := model.NewAdminUserRepository()
		if err := adminRepo.Delete(req.AdminID); err != nil {
			logx.Errorf("删除管理员失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除管理员失败"))
			return
		}

		// 记录操作日志
		log := &model.AdminOperationLog{
			AdminID:    req.AdminID,
			Module:     "admin_user",
			Action:     "delete",
			TargetID:   req.AdminID,
			TargetType: "admin_user",
			Content:    "删除管理员",
			IP:         r.RemoteAddr,
			CreatedAt:  time.Now(),
		}
		serverCtx.AdminOperationLogRepo.Create(log)

		httpx.OkJson(w, types.NewSuccessResponse(nil, "删除管理员成功"))
	}
}

// GetAdmin 获取管理员详情
func (h *AdminUserHandler) GetAdmin(w http.ResponseWriter, r *http.Request) {
	var req AdminDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	adminRepo := model.NewAdminUserRepository()
	admin, err := adminRepo.FindByID(req.AdminID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "管理员不存在"))
		} else {
			logx.Errorf("获取管理员详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取管理员详情失败"))
		}
		return
	}

	// 获取管理员角色
	rbacRepo := model.NewAdminRBACRepository(nil)
	roles, err := rbacRepo.GetAdminRoles(admin.AdminID)
	if err != nil {
		logx.Errorf("获取管理员角色失败: %v", err)
		roles = []model.AdminRole{} // 使用空数组代替nil
	}

	result := map[string]interface{}{
		"adminId":   admin.AdminID,
		"username":  admin.Username,
		"email":     admin.Email,
		"mobile":    admin.Mobile,
		"status":    admin.Status,
		"lastLogin": admin.LastLogin,
		"lastIP":    admin.LastIP,
		"createdAt": admin.CreatedAt,
		"updatedAt": admin.UpdatedAt,
		"roles":     roles,
	}

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取管理员详情成功"))
}

// UpdateAdmin 更新管理员
func (h *AdminUserHandler) UpdateAdmin(w http.ResponseWriter, r *http.Request) {
	var req UpdateAdminRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("更新管理员请求参数解析失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 检查用户是否存在
	adminRepo := model.NewAdminUserRepository()
	admin, err := adminRepo.FindByID(req.AdminID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logx.Errorf("管理员不存在: ID=%d", req.AdminID)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "管理员不存在"))
		} else {
			logx.Errorf("获取管理员失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员失败"))
		}
		return
	}

	// 检查用户名是否已被使用
	if req.Username != "" && req.Username != admin.Username {
		logx.Infof("检查用户名是否被占用: %s", req.Username)
		existingAdmin, err := adminRepo.FindByUsername(req.Username)
		if err == nil && existingAdmin != nil && existingAdmin.AdminID != admin.AdminID {
			logx.Errorf("用户名已被其他管理员使用: %s", req.Username)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeDuplicateRecord, "用户名已被其他用户使用"))
			return
		}
	}

	// 更新标志
	updatePerformed := false

	// 更新用户名
	if req.Username != "" && req.Username != admin.Username {
		logx.Infof("检查用户名是否被占用: %s", req.Username)
		existingAdmin, err := adminRepo.FindByUsername(req.Username)
		if err == nil && existingAdmin != nil && existingAdmin.AdminID != admin.AdminID {
			logx.Errorf("用户名已被其他管理员使用: %s", req.Username)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeDuplicateRecord, "用户名已被其他用户使用"))
			return
		}
		admin.Username = req.Username
		logx.Infof("用户名将更新为: %s", admin.Username)
		updatePerformed = true
	}

	// 更新邮箱
	if req.Email != "" && req.Email != admin.Email {
		admin.Email = req.Email
		logx.Infof("邮箱将更新为: %s", admin.Email)
		updatePerformed = true
	}

	// 更新手机号
	if req.Mobile != "" && req.Mobile != admin.Mobile {
		admin.Mobile = req.Mobile
		logx.Infof("手机号将更新为: %s", admin.Mobile)
		updatePerformed = true
	}

	// 更新状态
	if req.Status != 0 || admin.Status != 0 { // 考虑零值问题
		// 禁止将系统管理员的状态更改为禁用
		if admin.AdminID == 1 && req.Status != 1 {
			logx.Infof("尝试将系统管理员状态修改为禁用，操作被拒绝")
		} else if req.Status != admin.Status {
			admin.Status = req.Status
			logx.Infof("状态将更新为: %d", admin.Status)
			updatePerformed = true
		}
	}

	// 更新门店ID
	if req.StoreID != nil {
		// 检查是否真的需要更新
		needUpdate := false
		if admin.StoreID == nil {
			needUpdate = true
			logx.Infof("门店ID将从nil更新为: %d", *req.StoreID)
		} else if *req.StoreID != *admin.StoreID {
			needUpdate = true
			logx.Infof("门店ID将从%d更新为: %d", *admin.StoreID, *req.StoreID)
		}

		if needUpdate {
			admin.StoreID = req.StoreID
			updatePerformed = true
		}
	}

	// 处理密码更新
	if req.Password != "" {
		hashedPassword, err := utils.HashPassword(req.Password)
		if err != nil {
			logx.Errorf("密码处理错误: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员失败: 密码处理错误"))
			return
		}
		admin.Password = hashedPassword
		logx.Infof("密码将被更新")
		updatePerformed = true
	}

	// 仅在有实际字段更新时更新数据库
	if updatePerformed {
		admin.UpdatedAt = time.Now()
		if err := h.svcCtx.AdminUserRepo.Update(admin); err != nil {
			logx.Errorf("更新管理员失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员失败: 数据库操作错误"))
			return
		}
		logx.Infof("管理员信息更新成功: ID=%d", admin.AdminID)
	} else {
		logx.Infof("没有需要更新的字段: ID=%d", admin.AdminID)
	}

	// 更新角色
	if req.RoleIDs != nil && len(req.RoleIDs) > 0 {
		logx.Infof("更新管理员角色: ID=%d, 角色=%v", admin.AdminID, req.RoleIDs)
		rbacRepo := model.NewAdminRBACRepository(nil)
		if err := rbacRepo.AssignRolesToUser(admin.AdminID, req.RoleIDs); err != nil {
			logx.Errorf("更新管理员角色失败: %v", err)
			// 不返回错误，继续处理
		} else {
			logx.Infof("角色更新成功")
		}
	}

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// var logContent string
	// if admin.AdminID == 1 {
	// 	logContent = "更新系统管理员信息"
	// } else {
	// 	logContent = fmt.Sprintf("更新管理员信息: %s", admin.Username)
	// }
	// go h.logAdminOperation(r, "管理员", "更新", admin.AdminID, "AdminUser", logContent)

	// 返回更新后的管理员信息
	httpx.OkJson(w, types.NewSuccessResponse(admin, "更新管理员成功"))
}

// ToggleAdminStatus 启用或禁用管理员
func (h *AdminUserHandler) ToggleAdminStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdateAdminStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 验证状态值
	if req.Status != 0 && req.Status != 1 {
		logx.Errorf("无效的状态值: %d", req.Status)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "状态值无效，只能是0或1"))
		return
	}

	logx.Infof("解析状态值成功: %d", req.Status)

	// 检查是否是系统管理员（ID为1）
	if req.AdminID == 1 {
		logx.Errorf("尝试修改系统管理员状态，操作被拒绝")
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "不能修改系统管理员状态"))
		return
	}

	// 检查管理员是否存在
	adminRepo := model.NewAdminUserRepository()
	admin, err := adminRepo.FindByID(req.AdminID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logx.Errorf("管理员不存在: ID=%d", req.AdminID)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "管理员不存在"))
		} else {
			logx.Errorf("获取管理员失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员状态失败"))
		}
		return
	}

	// 如果状态没有变化，则不需要更新
	if admin.Status == req.Status {
		logx.Infof("管理员状态已经是 %d，无需更新", req.Status)
		statusText := "启用"
		if req.Status == 0 {
			statusText = "禁用"
		}
		httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
			"message": fmt.Sprintf("管理员已处于%s状态", statusText),
			"status":  req.Status,
		}, fmt.Sprintf("管理员已处于%s状态", statusText)))
		return
	}

	statusText := "启用"
	if req.Status == 0 {
		statusText = "禁用"
	}

	logx.Infof("准备%s管理员: ID=%d, 用户名=%s", statusText, admin.AdminID, admin.Username)

	// 更新状态
	if err := adminRepo.UpdateStatus(req.AdminID, req.Status); err != nil {
		logx.Errorf("更新管理员状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新管理员状态失败"))
		return
	}

	logx.Infof("管理员状态更新成功: ID=%d, 用户名=%s, 状态=%d", admin.AdminID, admin.Username, req.Status)

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// logContent := fmt.Sprintf("%s管理员: %s (ID: %d)", statusText, admin.Username, admin.AdminID)
	// go h.logAdminOperation(r, "管理员", "更新状态", admin.AdminID, "AdminUser", logContent)

	// 返回成功响应
	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"message": fmt.Sprintf("管理员%s成功", statusText),
		"status":  req.Status,
	}, fmt.Sprintf("管理员%s成功", statusText)))
}

// DeleteAdmin 删除管理员
func (h *AdminUserHandler) DeleteAdmin(w http.ResponseWriter, r *http.Request) {
	var req DeleteAdminRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("收到删除管理员请求: adminID=%d", req.AdminID)

	// 检查是否是系统管理员（ID为1）
	if req.AdminID == 1 {
		logx.Errorf("尝试删除系统管理员(ID=1)，操作被拒绝")
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "不能删除系统管理员"))
		return
	}

	// 检查管理员是否存在
	adminRepo := model.NewAdminUserRepository()
	admin, err := adminRepo.FindByID(req.AdminID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logx.Errorf("管理员不存在: ID=%d", req.AdminID)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "管理员不存在"))
		} else {
			logx.Errorf("查询管理员失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除管理员失败"))
		}
		return
	}

	logx.Infof("准备删除管理员: ID=%d, 用户名=%s", admin.AdminID, admin.Username)

	// 执行删除操作
	if err := adminRepo.Delete(req.AdminID); err != nil {
		logx.Errorf("删除管理员失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除管理员失败"))
		return
	}

	logx.Infof("管理员删除成功: ID=%d, 用户名=%s", admin.AdminID, admin.Username)

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// logContent := fmt.Sprintf("删除管理员: %s (ID: %d)", admin.Username, req.AdminID)
	// go h.logAdminOperation(r, "管理员", "删除", admin.AdminID, "AdminUser", logContent)

	// 返回成功响应
	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"message": "管理员删除成功",
	}, "管理员删除成功"))
}

// ListAdmins 获取管理员列表
func (h *AdminUserHandler) ListAdmins(w http.ResponseWriter, r *http.Request) {
	var req AdminListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取管理员列表: page=%d, size=%d, query=%s", req.Page, req.Size, req.Query)

	admins, total, err := h.svcCtx.AdminUserRepo.List(req.Page, req.Size, req.Query)
	if err != nil {
		logx.Errorf("获取管理员列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取管理员列表失败"))
		return
	}

	// 获取管理员角色信息
	if len(admins) > 0 {
		logx.Infof("开始获取管理员角色信息")
		rbacRepo := model.NewAdminRBACRepository(nil)
		for _, admin := range admins {
			roles, err := rbacRepo.GetAdminRoles(admin.AdminID)
			if err != nil {
				logx.Errorf("获取管理员角色失败: adminID=%d, error=%v", admin.AdminID, err)
			} else if len(roles) > 0 {
				logx.Debugf("管理员 %s 的角色: %v", admin.Username, roles)
			}
		}
	}

	logx.Infof("获取管理员列表成功: 共%d条记录", total)

	pageResponse := types.NewPageResponse(admins, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取管理员列表成功"))
}

// ListOperationLogs 获取操作日志列表
func (h *AdminUserHandler) ListOperationLogs(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Page  int    `form:"page,optional,default=1"`
		Size  int    `form:"size,optional,default=10"`
		Query string `form:"query,optional"`
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取操作日志列表: page=%d, size=%d, query=%s", req.Page, req.Size, req.Query)

	if req.Page < 1 {
		req.Page = 1
	}
	if req.Size < 1 {
		req.Size = 10
	}

	logs, total, err := h.svcCtx.AdminOperationLogRepo.List(req.Page, req.Size, req.Query)
	if err != nil {
		logx.Errorf("获取操作日志列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取操作日志列表失败"))
		return
	}

	logx.Infof("获取操作日志列表成功: 共%d条记录", total)

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"list":  logs,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}, "获取操作日志列表成功"))
}

// CreateAdmin 创建管理员
func (h *AdminUserHandler) CreateAdmin(w http.ResponseWriter, r *http.Request) {
	var req CreateAdminRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 参数验证
	if req.Username == "" || req.Password == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "用户名和密码不能为空"))
		return
	}

	// 检查用户名是否已存在
	existingAdmin, err := h.svcCtx.AdminUserRepo.FindByUsername(req.Username)
	if err == nil && existingAdmin != nil {
		logx.Errorf("用户名已存在: %s", req.Username)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUserExists, "用户名已存在"))
		return
	}

	// 密码加密
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		logx.Errorf("密码加密失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建管理员失败"))
		return
	}

	// 创建管理员
	now := time.Now()
	admin := &model.AdminUser{
		Username:  req.Username,
		Password:  hashedPassword,
		Email:     req.Email,
		Mobile:    req.Mobile,
		Status:    1, // 默认启用
		CreatorID: req.CreatorID,
		LastLogin: now, // 确保LastLogin有有效初始值
		LastIP:    utils.GetClientIP(r),
		CreatedAt: now,
		UpdatedAt: now,
	}

	if err := h.svcCtx.AdminUserRepo.Create(admin); err != nil {
		logx.Errorf("创建管理员失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建管理员失败"))
		return
	}

	logx.Infof("管理员创建成功: ID=%d, 用户名=%s", admin.AdminID, admin.Username)

	// 分配角色
	if len(req.RoleIDs) > 0 {
		logx.Infof("为管理员分配角色: adminID=%d, roleIDs=%v", admin.AdminID, req.RoleIDs)
		rbacRepo := model.NewAdminRBACRepository(nil)
		if err := rbacRepo.AssignRolesToUser(admin.AdminID, req.RoleIDs); err != nil {
			logx.Errorf("分配管理员角色失败: %v", err)
			// 不返回错误，继续处理
		} else {
			logx.Infof("角色分配成功")
		}
	}

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// logContent := fmt.Sprintf("创建管理员: %s (ID: %d)", admin.Username, admin.AdminID)
	// go h.logAdminOperation(r, "管理员", "创建", admin.AdminID, "AdminUser", logContent)

	// 返回创建的管理员信息
	httpx.OkJson(w, types.NewSuccessResponse(admin, "创建管理员成功"))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *AdminUserHandler) logAdminOperation(r *http.Request, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 模块=%s, 操作=%s", module, action)
}
