package evaluation

import (
	"time"

	"gorm.io/gorm"
)

// ServiceEvaluation 服务评价表
type ServiceEvaluation struct {
	ID          uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	UserID      uint           `gorm:"column:user_id;not null;index;comment:用户ID" json:"user_id"`
	Content     string         `gorm:"column:content;type:text;comment:评价内容" json:"content"`
	IsAnonymous bool           `gorm:"column:is_anonymous;default:false;comment:是否匿名" json:"is_anonymous"`
	Status      int            `gorm:"column:status;default:1;comment:状态(0-隐藏，1-显示)" json:"status"`
	CreatedAt   time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (ServiceEvaluation) TableName() string {
	return "service_evaluations"
}

// ServiceEvaluationInfo 服务评价信息（用于列表展示）
type ServiceEvaluationInfo struct {
	ID          uint   `json:"id"`
	UserID      uint   `json:"user_id"`
	UserName    string `json:"user_name"`
	UserPhone   string `json:"user_phone"`
	Content     string `json:"content"`
	IsAnonymous bool   `json:"is_anonymous"`
	Status      int    `json:"status"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// ServiceEvaluationQueryParams 服务评价查询参数
type ServiceEvaluationQueryParams struct {
	Page      int    `json:"page" validate:"min=1"`
	PageSize  int    `json:"page_size" validate:"min=1,max=100"`
	StartDate string `json:"start_date,optional"` // 开始日期 YYYY-MM-DD
	EndDate   string `json:"end_date,optional"`   // 结束日期 YYYY-MM-DD
	Status    *int   `json:"status,optional"`     // 状态筛选
}

// GetDisplayUserName 获取显示用的用户名（处理匿名）
func GetDisplayUserName(userName string, isAnonymous bool) string {
	if isAnonymous {
		if len(userName) > 0 {
			// 名称首字****
			return string([]rune(userName)[0]) + "****"
		}
		return "匿名用户"
	}
	return userName
}

// GetDisplayUserPhone 获取显示用的手机号（处理匿名）
func GetDisplayUserPhone(userPhone string, isAnonymous bool) string {
	if isAnonymous && len(userPhone) >= 7 {
		// 123****2222 格式
		return userPhone[:3] + "****" + userPhone[len(userPhone)-4:]
	}
	return userPhone
}

// TruncateContent 截取评价内容（前端显示用）
func (e *ServiceEvaluation) TruncateContent(maxLength int) string {
	runes := []rune(e.Content)
	if len(runes) <= maxLength {
		return e.Content
	}
	return string(runes[:maxLength]) + "..."
}
