# 聚水潭 API 适配器

这个包提供了与聚水潭 OpenAPI 接口交互的 Go 客户端实现。

## 功能

该适配器支持以下功能：

1. **基础信息**
   - 店铺查询
   - 物流公司查询
   - 仓库查询
   - 用户信息查询
   - 供应商查询
   - 分销商查询

2. **商品管理**
   - 商品查询
   - 商品创建
   - 商品更新

3. **订单管理** (后续实现)
   - 订单查询
   - 订单创建/更新
   - 订单取消

4. **库存管理** (后续实现)
   - 库存查询
   - 库存调整

5. **售后管理** (后续实现)
   - 售后单查询
   - 售后单处理

6. **其他功能** (后续实现)
   - 采购
   - 入库
   - 出库
   - 其它出入库
   - 调拨
   - 分销版商品
   - 虚拟仓
   - 播种

## 使用方法

### 初始化客户端

```go
import (
    "context"
    "fmt"
    "yekaitai/pkg/adapters/jushuitan"
)

func main() {
    // 创建配置
    config := jushuitan.Config{
        BaseURL:     "https://openapi.jushuitan.com", // 正式环境
        AppKey:      "your_app_key",
        AppSecret:   "your_app_secret",
        AccessToken: "your_access_token",
    }
    
    // 初始化客户端
    client := jushuitan.NewClient(config)
    
    // 或者使用全局客户端
    jushuitan.Init(config)
    // 然后使用 jushuitan.DefaultClient
}
```

### 查询店铺

```go
// 创建上下文
ctx := context.Background()

// 查询店铺
shopsResp, err := client.QueryShops(ctx, &jushuitan.ShopQueryRequest{
    PageIndex: 1,
    PageSize:  10,
})
if err != nil {
    fmt.Printf("查询店铺失败: %v\n", err)
    return
}

// 遍历结果
for _, shop := range shopsResp.Datas {
    fmt.Printf("店铺: ID=%d, 名称=%s\n", shop.ShopID, shop.ShopName)
}
```

### 查询商品

```go
// 查询商品
productsResp, err := client.QueryProducts(ctx, &jushuitan.ProductQueryRequest{
    PageIndex: 1,
    PageSize:  10,
    Name:      "测试商品", // 按名称模糊查询
})
if err != nil {
    fmt.Printf("查询商品失败: %v\n", err)
    return
}

// 遍历结果
for _, product := range productsResp.Data {
    fmt.Printf("商品: ID=%d, 名称=%s\n", product.ID, product.ItemName)
    
    // 查看商品规格
    for _, sku := range product.Skus {
        fmt.Printf("  规格: ID=%d, 名称=%s, 价格=%.2f\n", 
            sku.ID, sku.SkuName, sku.RetailPrice)
    }
}
```

## 测试环境

聚水潭提供了沙箱环境用于开发测试，可以通过以下方式使用：

```go
// 使用测试环境
config := jushuitan.Config{
    BaseURL:     "https://dev-api.jushuitan.com", // 测试环境
    AppKey:      "b0b7d1db226d4216a3d58df9ffa2dde5",
    AppSecret:   "99c4cef262f34ca882975a7064de0b87",
    AccessToken: "b7e3b1e24e174593af8ca5c397e53dad", // 企业版token
}

// 初始化客户端
client := jushuitan.NewClient(config)
```

## 错误处理

所有API调用都会返回标准的错误接口，可以通过以下方式处理错误：

```go
resp, err := client.QueryShops(ctx, req)
if err != nil {
    // 处理错误
    fmt.Printf("API调用失败: %v\n", err)
    return
}
```

## 注意事项

1. 请确保在使用API前已获取有效的`access_token`。
2. 测试环境与正式环境的数据是相互隔离的。
3. 注意遵循聚水潭API的调用频率限制：
   - 并发量限制：5次/秒
   - 调用频率限制：100次/分钟 