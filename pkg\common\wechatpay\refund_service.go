package wechatpay

import (
	"context"
	"fmt"
	"time"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"
	"github.com/zeromicro/go-zero/core/logx"
)

// WechatRefundService 微信退款服务
type WechatRefundService struct {
	client *core.Client
	config *WechatPayConfig
}

// NewWechatRefundService 创建退款服务
func NewWechatRefundService(client *core.Client, config *WechatPayConfig) *WechatRefundService {
	return &WechatRefundService{
		client: client,
		config: config,
	}
}

// CreateRefundRequest 申请退款请求
type CreateRefundRequest struct {
	OutRefundNo   string `json:"out_refund_no"`            // 商户退款单号
	OutTradeNo    string `json:"out_trade_no,omitempty"`   // 商户订单号
	TransactionID string `json:"transaction_id,omitempty"` // 微信支付订单号
	Reason        string `json:"reason"`                   // 退款原因
	RefundAmount  int64  `json:"refund_amount"`            // 退款金额，单位：分
	TotalAmount   int64  `json:"total_amount"`             // 原订单金额，单位：分
	NotifyURL     string `json:"notify_url,omitempty"`     // 退款回调URL
}

// CreateRefundResponse 申请退款响应
type CreateRefundResponse struct {
	RefundID      string `json:"refund_id"`      // 微信退款单号
	OutRefundNo   string `json:"out_refund_no"`  // 商户退款单号
	TransactionID string `json:"transaction_id"` // 微信支付订单号
	OutTradeNo    string `json:"out_trade_no"`   // 商户订单号
	Status        string `json:"status"`         // 退款状态
	RefundAmount  int64  `json:"refund_amount"`  // 退款金额
	CreateTime    string `json:"create_time"`    // 退款创建时间
}

// CreateRefund 申请退款
func (s *WechatRefundService) CreateRefund(ctx context.Context, req *CreateRefundRequest) (*CreateRefundResponse, error) {
	svc := refunddomestic.RefundsApiService{Client: s.client}

	// 构建退款请求，参考官方示例
	refundReq := refunddomestic.CreateRequest{
		OutRefundNo: core.String(req.OutRefundNo),
		Reason:      core.String(req.Reason),
		NotifyUrl:   core.String(s.config.RefundNotifyURL), // 设置退款回调URL
		Amount: &refunddomestic.AmountReq{
			Refund:   core.Int64(req.RefundAmount),
			Total:    core.Int64(req.TotalAmount),
			Currency: core.String("CNY"), // 人民币，这是必填字段
		},
	}

	// 设置订单号（商户订单号或微信支付订单号二选一）
	if req.OutTradeNo != "" {
		refundReq.OutTradeNo = core.String(req.OutTradeNo)
	} else if req.TransactionID != "" {
		refundReq.TransactionId = core.String(req.TransactionID)
	} else {
		return nil, fmt.Errorf("商户订单号和微信支付订单号至少需要提供一个")
	}

	// 设置退款回调URL（可选）
	if req.NotifyURL != "" {
		refundReq.NotifyUrl = core.String(req.NotifyURL)
	}

	// 详细日志：请求参数
	logx.Infof("===== 微信支付申请退款请求 =====")
	logx.Infof("[微信退款] 请求类型: 申请退款")
	logx.Infof("[微信退款] 商户退款单号: %s", req.OutRefundNo)
	logx.Infof("[微信退款] 商户订单号: %s", req.OutTradeNo)
	logx.Infof("[微信退款] 微信支付订单号: %s", req.TransactionID)
	logx.Infof("[微信退款] 退款原因: %s", req.Reason)
	logx.Infof("[微信退款] 退款金额: %d分 (%.2f元)", req.RefundAmount, float64(req.RefundAmount)/100)
	logx.Infof("[微信退款] 原订单金额: %d分 (%.2f元)", req.TotalAmount, float64(req.TotalAmount)/100)
	logx.Infof("[微信退款] 退款回调URL: %s", s.config.RefundNotifyURL)

	// 调用微信支付退款API
	resp, result, err := svc.Create(ctx, refundReq)

	// 详细日志：响应结果
	logx.Infof("===== 微信支付申请退款响应 =====")
	if err != nil {
		logx.Errorf("[微信退款] 申请失败: %v", err)
		logx.Errorf("[微信退款] 商户退款单号: %s", req.OutRefundNo)
		logx.Errorf("[微信退款] 商户订单号: %s", req.OutTradeNo)
		return nil, fmt.Errorf("申请微信退款失败: %w", err)
	}

	logx.Infof("[微信退款] 申请成功")
	logx.Infof("[微信退款] HTTP状态码: %d", result.Response.StatusCode)
	logx.Infof("[微信退款] 响应头: %+v", result.Response.Header)
	logx.Infof("[微信退款] 商户退款单号: %s", req.OutRefundNo)
	logx.Infof("[微信退款] 微信退款单号: %s", *resp.RefundId)
	logx.Infof("[微信退款] 微信支付订单号: %s", *resp.TransactionId)
	logx.Infof("[微信退款] 商户订单号: %s", *resp.OutTradeNo)
	logx.Infof("[微信退款] 退款状态: %s", string(*resp.Status))
	logx.Infof("[微信退款] 退款金额: %d分 (%.2f元)", *resp.Amount.Refund, float64(*resp.Amount.Refund)/100)

	// 构建响应
	return &CreateRefundResponse{
		RefundID:      *resp.RefundId,
		OutRefundNo:   *resp.OutRefundNo,
		TransactionID: *resp.TransactionId,
		OutTradeNo:    *resp.OutTradeNo,
		Status:        string(*resp.Status),
		RefundAmount:  *resp.Amount.Refund,
		CreateTime:    resp.CreateTime.Format(time.RFC3339),
	}, nil
}

// QueryRefundRequest 查询退款请求
type QueryRefundRequest struct {
	OutRefundNo string `json:"out_refund_no"` // 商户退款单号
}

// QueryRefundResponse 查询退款响应
type QueryRefundResponse struct {
	RefundID            string     `json:"refund_id"`              // 微信退款单号
	OutRefundNo         string     `json:"out_refund_no"`          // 商户退款单号
	TransactionID       string     `json:"transaction_id"`         // 微信支付订单号
	OutTradeNo          string     `json:"out_trade_no"`           // 商户订单号
	Status              string     `json:"status"`                 // 退款状态
	RefundAmount        int64      `json:"refund_amount"`          // 退款金额
	CreateTime          time.Time  `json:"create_time"`            // 退款创建时间
	SuccessTime         *time.Time `json:"success_time,omitempty"` // 退款成功时间
	UserReceivedAccount string     `json:"user_received_account"`  // 退款入账账户
}

// QueryRefund 查询退款
func (s *WechatRefundService) QueryRefund(ctx context.Context, req *QueryRefundRequest) (*QueryRefundResponse, error) {
	svc := refunddomestic.RefundsApiService{Client: s.client}

	// 详细日志：请求参数
	logx.Infof("===== 微信支付查询退款请求 =====")
	logx.Infof("[微信退款] 请求类型: 查询退款状态")
	logx.Infof("[微信退款] 商户退款单号: %s", req.OutRefundNo)

	queryReq := refunddomestic.QueryByOutRefundNoRequest{
		OutRefundNo: core.String(req.OutRefundNo),
	}

	resp, _, err := svc.QueryByOutRefundNo(ctx, queryReq)

	// 详细日志：响应结果
	logx.Infof("===== 微信支付查询退款响应 =====")
	if err != nil {
		logx.Errorf("[微信退款] 查询失败: %v", err)
		logx.Errorf("[微信退款] 商户退款单号: %s", req.OutRefundNo)
		return nil, fmt.Errorf("查询微信退款失败: %w", err)
	}

	result := &QueryRefundResponse{
		RefundID:            *resp.RefundId,
		OutRefundNo:         *resp.OutRefundNo,
		TransactionID:       *resp.TransactionId,
		OutTradeNo:          *resp.OutTradeNo,
		Status:              string(*resp.Status),
		RefundAmount:        *resp.Amount.Refund,
		CreateTime:          *resp.CreateTime,
		UserReceivedAccount: *resp.UserReceivedAccount,
	}

	// 如果有退款成功时间
	if resp.SuccessTime != nil {
		result.SuccessTime = resp.SuccessTime
	}

	// 详细日志：响应内容
	logx.Infof("[微信退款] 查询成功")
	logx.Infof("[微信退款] 微信退款单号: %s", result.RefundID)
	logx.Infof("[微信退款] 商户退款单号: %s", result.OutRefundNo)
	logx.Infof("[微信退款] 微信支付订单号: %s", result.TransactionID)
	logx.Infof("[微信退款] 商户订单号: %s", result.OutTradeNo)
	logx.Infof("[微信退款] 退款状态: %s", result.Status)
	logx.Infof("[微信退款] 退款金额: %d分 (%.2f元)", result.RefundAmount, float64(result.RefundAmount)/100)
	logx.Infof("[微信退款] 创建时间: %s", result.CreateTime.Format("2006-01-02 15:04:05"))
	logx.Infof("[微信退款] 退款入账账户: %s", result.UserReceivedAccount)
	if result.SuccessTime != nil {
		logx.Infof("[微信退款] 退款成功时间: %s", result.SuccessTime.Format("2006-01-02 15:04:05"))
	}

	return result, nil
}

// 全局退款服务实例
var globalRefundService *WechatRefundService

// InitGlobalRefundService 初始化全局退款服务
func InitGlobalRefundService(client *core.Client, config *WechatPayConfig) {
	globalRefundService = NewWechatRefundService(client, config)
}

// GetGlobalRefundService 获取全局退款服务实例
func GetGlobalRefundService() *WechatRefundService {
	return globalRefundService
}
