# 用户等级管理模块

## 功能概述

用户等级管理模块提供完整的用户等级体系管理功能，包括等级规则配置、权益设置、自动升级计算等功能。

## 主要功能

### 1. 等级管理（图1）
- **等级列表**: 显示所有等级规则，支持按等级名称和日期区间筛选
- **等级创建**: 创建新的用户等级，等级名称不允许重复
- **等级编辑**: 修改等级信息，修改后会触发用户等级重新计算
- **等级删除**: 删除等级规则，删除后会重新分配用户等级
- **状态管理**: 启用/禁用等级规则

### 2. 等级权益配置（图2）
- **升级条件设置**:
  - 注册成功
  - 消费累计达到指定金额
  - 单日消费达到指定金额

- **会员权益**:
  - 活动报名费免费
  - 额外赠送叶小币
  - 商品享受折扣

- **赠送内容**:
  - 赠送优惠券（支持选择优惠券和数量）
  - 赠送服务（支持选择服务和次数）

### 3. 优惠券选择（图3）
- 支持按类型筛选优惠券（代金券、立减券、折扣券）
- 显示优惠券基本信息（名称、类型、使用门槛、面额、状态、有效期）
- 支持选择多张优惠券并设置赠送数量

## API接口

### 等级管理接口
- `GET /api/admin/user-levels` - 获取等级列表
- `POST /api/admin/user-levels` - 创建等级
- `GET /api/admin/user-levels/:id` - 获取等级详情
- `PUT /api/admin/user-levels/:id` - 更新等级
- `DELETE /api/admin/user-levels/:id` - 删除等级
- `PATCH /api/admin/user-levels/:id/status` - 更新等级状态

### 辅助接口
- `GET /api/admin/user-levels/available-coupons` - 获取可选择的优惠券列表

## 数据模型

### 用户等级规则表 (user_level_rules)
```sql
- id: 等级规则ID
- level_name: 等级名称（唯一）
- level_order: 等级顺序（数字越大等级越高）
- description: 等级描述

-- 升级条件
- require_register: 需要注册成功
- require_consumption: 需要消费累计
- consumption_amount: 消费累计金额
- require_daily_consume: 需要单日消费
- daily_consume_amount: 单日消费金额

-- 会员权益
- free_activity_signup: 活动报名费免费
- extra_coins: 额外赠送叶小币
- product_discount: 商品折扣比例

-- 赠送配置
- gift_coupons: 赠送优惠券配置(JSON)
- gift_services: 赠送服务配置(JSON)

-- 状态信息
- is_active: 是否启用
- creator_id: 创建人ID
- created_at: 创建时间
- updated_at: 更新时间
- deleted_at: 删除时间
```

### 用户等级升级日志表 (user_level_upgrade_logs)
```sql
- id: 升级日志ID
- user_id: 用户ID
- from_level_id: 原等级ID
- to_level_id: 目标等级ID
- from_level_name: 原等级名称
- to_level_name: 目标等级名称
- upgrade_reason: 升级原因
- created_at: 升级时间
```

## 特殊说明

1. **等级名称唯一性**: 系统确保等级名称不重复
2. **权益同步**: 修改等级规则后，系统会异步重新计算所有用户的等级
3. **级联影响**: 删除等级后，使用该等级的用户会被重新分配到合适的等级
4. **操作记录**: 所有等级管理操作都会记录到管理员操作日志中
5. **缓存优化**: 管理员信息使用Redis缓存，避免频繁查询数据库

## 使用流程

1. **创建等级**: 设置等级名称、顺序和描述
2. **配置升级条件**: 设置用户达到该等级的条件
3. **设置权益**: 配置该等级用户享受的权益
4. **配置赠送**: 设置升级到该等级时赠送的优惠券和服务
5. **启用等级**: 激活等级规则，系统开始自动计算用户等级

## 注意事项

- 等级顺序决定了等级的高低，数字越大等级越高
- 修改等级规则会触发全用户等级重新计算，请谨慎操作
- 删除等级前请确认没有用户正在使用该等级
- 优惠券和服务的配置以JSON格式存储，便于扩展 