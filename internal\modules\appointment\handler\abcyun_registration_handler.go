package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// 挂号相关请求结构体

// DoctorScheduleRequest 医生排班请求
type DoctorScheduleRequest struct {
	DoctorID         string `path:"doctorId"`                            // 医生ID
	WorkingDate      string `form:"workingDate"`                         // 工作日期，格式yyyy-MM-dd
	DepartmentID     string `form:"departmentId,optional"`               // 科室ID，门诊预约时必填
	RegistrationType int    `form:"registrationType,optional,default=0"` // 预约类型，0:门诊预约 1:理疗预约，默认为0
}

// RegistrationCreateRequest 创建挂号请求
type RegistrationCreateRequest struct {
	PatientID              string `json:"patientId"`                           // 患者ID
	DepartmentID           string `json:"departmentId"`                        // 科室ID
	DoctorID               string `json:"doctorId"`                            // 医生ID
	OrderNo                int    `json:"orderNo"`                             // 排队号
	ReserveDate            string `json:"reserveDate"`                         // 预约日期
	ReserveStart           string `json:"reserveStart"`                        // 预约开始时间
	ReserveEnd             string `json:"reserveEnd"`                          // 预约结束时间
	SourceID               string `json:"sourceId,optional"`                   // 来源ID
	SourceFromID           string `json:"sourceFromId,optional"`               // 来源From ID
	SourceRemark           string `json:"sourceRemark,optional"`               // 来源备注
	RegistrationType       int    `json:"registrationType,optional,default=0"` // 预约类型，0:门诊预约 1:理疗预约，默认为0
	RegistrationProductIDs []int  `json:"registrationProductIds,optional"`     // 预约项目ID列表
}

// RegistrationDetailRequest 挂号详情请求
type RegistrationDetailRequest struct {
	RegistrationSheetID string `path:"registrationSheetId"` // 挂号单ID
}

// RegistrationByPatientOrderIDRequest 通过就诊单ID获取挂号详情请求
type RegistrationByPatientOrderIDRequest struct {
	PatientOrderID string `path:"patientOrderId"` // 就诊单ID
}

// RegistrationCancelRequest 取消挂号请求
type RegistrationCancelRequest struct {
	RegistrationSheetID string `path:"registrationSheetId"` // 挂号单ID
}

// PatientRegistrationsRequest 患者预约列表请求
type PatientRegistrationsRequest struct {
	PatientID        string `path:"patientId"`                           // 患者ID
	BeginDate        string `form:"beginDate,optional"`                  // 开始日期，可选
	EndDate          string `form:"endDate,optional"`                    // 结束日期，可选
	Limit            int    `form:"limit,optional,default=10"`           // 分页大小，默认10
	Offset           int    `form:"offset,optional,default=0"`           // 分页偏移，默认0
	RegistrationType int    `form:"registrationType,optional,default=0"` // 预约类型，0:门诊预约 1:理疗预约，默认为0
}

// RegistrationsRequest 挂号单列表请求
type RegistrationsRequest struct {
	RegistrationType int    `form:"registrationType"`          // 预约类型，0:门诊预约 1:理疗预约
	ReserveDate      string `form:"reserveDate,optional"`      // 预约日期，可选
	Limit            int    `form:"limit,optional,default=10"` // 分页大小，默认10
	Offset           int    `form:"offset,optional,default=0"` // 分页偏移，默认0
}

// DoctorsShiftStatusRequest 医生排班状态请求
type DoctorsShiftStatusRequest struct {
	ReserveDate      string `form:"reserveDate"`                         // 预约日期，格式yyyy-MM-dd
	RegistrationType int    `form:"registrationType,optional,default=0"` // 预约类型，0:门诊预约 1:理疗预约，默认为0
}

// DoctorShiftRequest 医生号源日期列表请求
type DoctorShiftRequest struct {
	DoctorID         string `path:"doctorId"`                            // 医生ID
	BeginDate        string `form:"beginDate,optional"`                  // 开始日期，可选
	EndDate          string `form:"endDate,optional"`                    // 结束日期，可选
	RegistrationType int    `form:"registrationType,optional,default=0"` // 预约类型，0:门诊预约 1:理疗预约，默认为0
}

// RemarkTemplateRequest 备注模板请求
type RemarkTemplateRequest struct {
	RegistrationType int `form:"registrationType,optional,default=0"` // 挂号类型，0:门诊预约 1:理疗预约，默认为0
}

// RegistrationProductRequest 挂号产品请求
type RegistrationProductRequest struct {
	RegistrationType int `form:"registrationType,optional,default=0"` // 挂号类型，0:门诊预约 1:理疗预约，默认为0
}

// RegistrationProductSectionStatusRequest 查询项目每日号源状态请求
type RegistrationProductSectionStatusRequest struct {
	RegistrationProductID int `path:"registrationProductId"` // 预约项目ID
}

// DoctorRegistrationProductsRequest 查询医生可预约项目列表请求
type DoctorRegistrationProductsRequest struct {
	DoctorID string `path:"doctorId"` // 医生ID
}

// RegistrationProductDaysShiftsRequest 查询项目指定日期排班信息请求
type RegistrationProductDaysShiftsRequest struct {
	RegistrationProductID int    `path:"registrationProductId"` // 预约项目ID
	ReserveDate           string `form:"reserveDate"`           // 预约日期（yyyy-MM-dd）
}

// 5.5.1. 获取科室医生的号源详情
func AbcYunDoctorScheduleHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req DoctorScheduleRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.DoctorID == "" {
		httpx.Error(w, errors.New("缺少医生ID参数"))
		return
	}

	if req.WorkingDate == "" {
		httpx.Error(w, errors.New("缺少工作日期参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/doctor/%s", req.DoctorID)
	queryParams := map[string]string{
		"workingDate": req.WorkingDate,
	}

	if req.DepartmentID != "" {
		queryParams["departmentId"] = req.DepartmentID
	}
	if req.RegistrationType > 0 {
		queryParams["registrationType"] = strconv.Itoa(req.RegistrationType)
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取科室医生的号源详情成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.2. 创建挂号
func AbcYunCreateRegistrationHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RegistrationCreateRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 验证必填参数
	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}
	if req.DepartmentID == "" {
		httpx.Error(w, errors.New("缺少科室ID参数"))
		return
	}
	if req.DoctorID == "" {
		httpx.Error(w, errors.New("缺少医生ID参数"))
		return
	}
	if req.ReserveDate == "" {
		httpx.Error(w, errors.New("缺少预约日期参数"))
		return
	}
	if req.ReserveStart == "" {
		httpx.Error(w, errors.New("缺少预约开始时间参数"))
		return
	}
	if req.ReserveEnd == "" {
		httpx.Error(w, errors.New("缺少预约结束时间参数"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/registration"
	respBody, err := client.Post(path, req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "创建挂号成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.3. 获取挂号详情
func AbcYunRegistrationDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RegistrationDetailRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.RegistrationSheetID == "" {
		httpx.Error(w, errors.New("缺少挂号单ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/%s", req.RegistrationSheetID)
	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取挂号详情成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.4. 通过就诊单ID获取挂号详情
func AbcYunRegistrationByPatientOrderIDHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RegistrationByPatientOrderIDRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientOrderID == "" {
		httpx.Error(w, errors.New("缺少就诊单ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/by-patient-order-id/%s", req.PatientOrderID)
	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "通过就诊单ID获取挂号详情成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.5. 取消挂号
func AbcYunCancelRegistrationHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RegistrationCancelRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.RegistrationSheetID == "" {
		httpx.Error(w, errors.New("缺少挂号单ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/%s/cancel", req.RegistrationSheetID)
	respBody, err := client.Put(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "取消挂号成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.6. 查询预约备注模板
func AbcYunRemarkTemplatesHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RemarkTemplateRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/registration/remark-templates"
	queryParams := map[string]string{}
	if req.RegistrationType > 0 {
		queryParams["registrationType"] = strconv.Itoa(req.RegistrationType)
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取备注模板成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.7. 查询医生可预约项目列表
func AbcYunDoctorRegistrationProductsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req DoctorRegistrationProductsRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.DoctorID == "" {
		httpx.Error(w, errors.New("缺少医生ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/doctor/%s/product", req.DoctorID)
	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询医生可预约项目列表成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.8. 查询门店可预约项目列表
func AbcYunRegistrationProductsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RegistrationProductRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/registration/product"
	queryParams := map[string]string{}
	if req.RegistrationType > 0 {
		queryParams["registrationType"] = strconv.Itoa(req.RegistrationType)
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取挂号产品成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.9. 查询项目每日号源
func AbcYunRegistrationProductSectionStatusHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RegistrationProductSectionStatusRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.RegistrationProductID == 0 {
		httpx.Error(w, errors.New("缺少预约项目ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/product/%d/section-status", req.RegistrationProductID)
	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取项目每日号源状态成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.10. 查询项目指定日期排班信息
func AbcYunRegistrationProductDaysShiftsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RegistrationProductDaysShiftsRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.RegistrationProductID == 0 {
		httpx.Error(w, errors.New("缺少预约项目ID参数"))
		return
	}

	if req.ReserveDate == "" {
		httpx.Error(w, errors.New("缺少预约日期参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/product/%d/days-shifts", req.RegistrationProductID)
	queryParams := map[string]string{
		"reserveDate": req.ReserveDate,
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询项目指定日期排班信息成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.11. 查询患者预约列表
func AbcYunPatientRegistrationsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientRegistrationsRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/patient/%s", req.PatientID)
	queryParams := map[string]string{
		"limit":  strconv.Itoa(req.Limit),
		"offset": strconv.Itoa(req.Offset),
	}

	if req.BeginDate != "" {
		queryParams["beginDate"] = req.BeginDate
	}
	if req.EndDate != "" {
		queryParams["endDate"] = req.EndDate
	}
	if req.RegistrationType > 0 {
		queryParams["registrationType"] = strconv.Itoa(req.RegistrationType)
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询患者预约列表成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.12. 查询门店指定日期医生号源状态
func AbcYunDoctorsShiftStatusHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req DoctorsShiftStatusRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.ReserveDate == "" {
		httpx.Error(w, errors.New("缺少预约日期参数"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/registration/doctors-shift-status"
	queryParams := map[string]string{
		"reserveDate": req.ReserveDate,
	}

	if req.RegistrationType > 0 {
		queryParams["registrationType"] = strconv.Itoa(req.RegistrationType)
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询门店指定日期医生号源状态成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.13. 查询门店指定医生号源日期列表
func AbcYunDoctorShiftHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req DoctorShiftRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.DoctorID == "" {
		httpx.Error(w, errors.New("缺少医生ID参数"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/registration/doctor/%s/shift", req.DoctorID)
	queryParams := map[string]string{}

	if req.BeginDate != "" {
		queryParams["beginDate"] = req.BeginDate
	}
	if req.EndDate != "" {
		queryParams["endDate"] = req.EndDate
	}
	if req.RegistrationType > 0 {
		queryParams["registrationType"] = strconv.Itoa(req.RegistrationType)
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询门店指定医生号源日期列表成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// 5.5.14. 查询挂号单列表
func AbcYunRegistrationsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req RegistrationsRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/registration"
	queryParams := map[string]string{
		"registrationType": strconv.Itoa(req.RegistrationType),
		"limit":            strconv.Itoa(req.Limit),
		"offset":           strconv.Itoa(req.Offset),
	}

	if req.ReserveDate != "" {
		queryParams["reserveDate"] = req.ReserveDate
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询挂号单列表成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}
