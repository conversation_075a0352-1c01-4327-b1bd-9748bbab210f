package model

import (
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// Create 创建药品
func (r *medicineRepository) Create(medicine *Medicine) error {
	return mysql.Master().Create(medicine).Error
}

// Update 更新药品
func (r *medicineRepository) Update(medicine *Medicine) error {
	return mysql.Master().Save(medicine).Error
}

// Delete 删除药品
func (r *medicineRepository) Delete(id uint) error {
	return mysql.Master().Delete(&Medicine{}, id).Error
}

// FindByID 根据ID查找药品
func (r *medicineRepository) FindByID(id uint) (*Medicine, error) {
	var medicine Medicine
	err := mysql.Slave().Where("id = ?", id).First(&medicine).Error
	if err != nil {
		return nil, err
	}
	return &medicine, nil
}

// FindByCode 根据编码查找药品
func (r *medicineRepository) FindByCode(code string) (*Medicine, error) {
	var medicine Medicine
	err := mysql.Slave().Where("code = ?", code).First(&medicine).Error
	if err != nil {
		return nil, err
	}
	return &medicine, nil
}

// List 获取药品列表
func (r *medicineRepository) List(page, size int) ([]*Medicine, int64, error) {
	var medicines []*Medicine
	var total int64

	db := mysql.Slave()

	// 获取总数
	if err := db.Model(&Medicine{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&medicines).Error; err != nil {
		return nil, 0, err
	}

	return medicines, total, nil
}

// ListByCategory 根据分类获取药品列表
func (r *medicineRepository) ListByCategory(categoryID uint, page, size int) ([]*Medicine, int64, error) {
	var medicines []*Medicine
	var total int64

	db := mysql.Slave().Where("category_id = ?", categoryID)

	// 获取总数
	if err := db.Model(&Medicine{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&medicines).Error; err != nil {
		return nil, 0, err
	}

	return medicines, total, nil
}

// Search 搜索药品
func (r *medicineRepository) Search(keyword string, page, size int) ([]*Medicine, int64, error) {
	var medicines []*Medicine
	var total int64

	db := mysql.Slave().Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")

	// 获取总数
	if err := db.Model(&Medicine{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&medicines).Error; err != nil {
		return nil, 0, err
	}

	return medicines, total, nil
}

// UpdateStock 更新药品库存
func (r *medicineRepository) UpdateStock(id uint, stock int) error {
	return mysql.Master().Model(&Medicine{}).Where("id = ?", id).Update("stock", stock).Error
}

// UpdateStatus 更新药品状态
func (r *medicineRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&Medicine{}).Where("id = ?", id).Update("status", status).Error
}

// UpdateSalesCount 更新药品销量
func (r *medicineRepository) UpdateSalesCount(id uint, count int) error {
	return mysql.Master().Model(&Medicine{}).Where("id = ?", id).UpdateColumn("sales_count", gorm.Expr("sales_count + ?", count)).Error
}
