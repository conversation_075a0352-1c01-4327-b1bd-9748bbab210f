package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/admin/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 获取当前管理员角色处理函数
func GetCurrentAdminRolesHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从上下文中获取管理员ID
		adminIDStr := middleware.FromContext(r.Context(), "admin_id")
		if adminIDStr == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权"))
			return
		}

		adminID, err := strconv.ParseUint(adminIDStr, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的管理员ID"))
			return
		}

		// 获取管理员角色
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		roles, err := rbacRepo.GetAdminRoles(uint(adminID))
		if err != nil {
			logx.Errorf("获取管理员角色失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取角色失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(roles, "获取成功"))
	}
}

// 获取所有角色处理函数
func GetAllRolesHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 获取所有角色
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		roles, err := rbacRepo.GetAllRoles()
		if err != nil {
			logx.Errorf("获取所有角色失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取角色失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(roles, "获取成功"))
	}
}

// 创建角色请求结构
type CreateRoleRequest struct {
	RoleName string `json:"roleName"`
}

// 创建角色处理函数
func CreateRoleHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req CreateRoleRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.RoleName == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "角色名称不能为空"))
			return
		}

		// 创建角色
		role := model.AdminRole{
			RoleName: req.RoleName,
			IsSystem: false,
		}

		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		if err := rbacRepo.CreateRole(&role); err != nil {
			logx.Errorf("创建角色失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建角色失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(role, "创建成功"))
	}
}

// 更新角色请求结构
type UpdateRoleRequest struct {
	RoleID      string `path:"roleId"`
	RoleName    string `json:"roleName"`
	Description string `json:"description"`
	Status      int    `json:"status"`
}

// 更新角色处理函数
func UpdateRoleHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req UpdateRoleRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.RoleID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "角色ID不能为空"))
			return
		}

		if req.RoleName == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "角色名称不能为空"))
			return
		}

		roleID, err := strconv.ParseUint(req.RoleID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的角色ID"))
			return
		}

		// 更新角色
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		role, err := rbacRepo.FindRoleByID(uint(roleID))
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "角色不存在"))
			return
		}

		if role.IsSystem {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "系统角色不能修改"))
			return
		}

		// 更新角色字段
		role.RoleName = req.RoleName
		role.Description = req.Description

		// 只有状态值在合法范围内才更新
		if req.Status == 0 || req.Status == 1 {
			role.Status = req.Status
		}

		if err := rbacRepo.UpdateRole(role); err != nil {
			logx.Errorf("更新角色失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新角色失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(role, "更新成功"))
	}
}

// 删除角色请求结构
type DeleteRoleRequest struct {
	RoleID string `path:"roleId"`
}

// 删除角色处理函数
func DeleteRoleHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req DeleteRoleRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.RoleID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "角色ID不能为空"))
			return
		}

		roleID, err := strconv.ParseUint(req.RoleID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的角色ID"))
			return
		}

		// 删除角色
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		role, err := rbacRepo.FindRoleByID(uint(roleID))
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "角色不存在"))
			return
		}

		if role.IsSystem {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "系统角色不能删除"))
			return
		}

		if err := rbacRepo.DeleteRole(uint(roleID)); err != nil {
			logx.Errorf("删除角色失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除角色失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(nil, "删除成功"))
	}
}

// 获取角色权限请求结构
type RolePermissionsRequest struct {
	RoleID string `path:"roleId"`
}

// 获取角色权限处理函数
func GetRolePermissionsHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req RolePermissionsRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.RoleID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "角色ID不能为空"))
			return
		}

		roleID, err := strconv.ParseUint(req.RoleID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的角色ID"))
			return
		}

		// 获取角色权限
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		permissions, err := rbacRepo.GetRolePermissions(uint(roleID))
		if err != nil {
			logx.Errorf("获取角色权限失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取权限失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(permissions, "获取成功"))
	}
}

// 分配权限请求结构
type AssignPermissionsRequest struct {
	RoleID  string `path:"roleId"`
	PermIDs []uint `json:"permIds"`
}

// 分配角色权限处理函数
func AssignPermissionsToRoleHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req AssignPermissionsRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.RoleID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "角色ID不能为空"))
			return
		}

		roleID, err := strconv.ParseUint(req.RoleID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的角色ID"))
			return
		}

		// 分配权限
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		if err := rbacRepo.AssignPermissionsToRole(uint(roleID), req.PermIDs); err != nil {
			logx.Errorf("分配角色权限失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "分配权限失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(nil, "分配成功"))
	}
}

// 获取权限树处理函数
func GetPermissionsTreeHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 获取权限树
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		permissionsTree, err := rbacRepo.GetPermissionTreeView()
		if err != nil {
			logx.Errorf("获取权限树失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取权限树失败"))
			return
		}

		// 直接返回树形结构
		httpx.OkJson(w, types.NewSuccessResponse(permissionsTree, "获取成功"))
	}
}

// 获取所有权限处理函数
func GetAllPermissionsHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 获取所有权限
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		permissions, err := rbacRepo.GetAllPermissions()
		if err != nil {
			logx.Errorf("获取所有权限失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取权限失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(permissions, "获取成功"))
	}
}

// 分配用户角色请求
type AssignRolesRequest struct {
	UserID  string `path:"userId"`  // 用户ID
	RoleIDs []uint `json:"roleIds"` // 角色ID列表
}

// 分配用户角色处理函数
func AssignRolesToUserHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req AssignRolesRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.UserID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "用户ID不能为空"))
			return
		}

		userID, err := strconv.ParseUint(req.UserID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的用户ID"))
			return
		}

		// 分配角色
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		if err := rbacRepo.AssignRolesToUser(uint(userID), req.RoleIDs); err != nil {
			logx.Errorf("分配用户角色失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "分配角色失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(nil, "分配成功"))
	}
}

// 移除用户角色请求
type RemoveRoleRequest struct {
	UserID string `path:"userId"` // 用户ID
	RoleID string `path:"roleId"` // 角色ID
}

// 移除用户角色处理函数
func RemoveRoleFromUserHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req RemoveRoleRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.UserID == "" || req.RoleID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "用户ID和角色ID不能为空"))
			return
		}

		userID, err := strconv.ParseUint(req.UserID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的用户ID"))
			return
		}

		roleID, err := strconv.ParseUint(req.RoleID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的角色ID"))
			return
		}

		// 移除角色
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		if err := rbacRepo.RemoveRoleFromUser(uint(userID), uint(roleID)); err != nil {
			logx.Errorf("移除用户角色失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "移除角色失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(nil, "移除成功"))
	}
}

// 获取用户角色请求
type UserRolesRequest struct {
	UserID string `path:"userId"` // 用户ID
}

// 获取用户角色处理函数
func GetUserRolesHandler(serverCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req UserRolesRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
			return
		}

		if req.UserID == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "用户ID不能为空"))
			return
		}

		userID, err := strconv.ParseUint(req.UserID, 10, 64)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的用户ID"))
			return
		}

		// 获取用户角色
		rbacRepo := model.NewAdminRBACRepository(mysql.Master())
		roles, err := rbacRepo.GetUserRoles(uint(userID))
		if err != nil {
			logx.Errorf("获取用户角色失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取角色失败"))
			return
		}

		httpx.OkJson(w, types.NewSuccessResponse(roles, "获取成功"))
	}
}
