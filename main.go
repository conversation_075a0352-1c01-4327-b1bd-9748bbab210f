package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"yekaitai/internal/config"
	"yekaitai/internal/middleware"
	admin_router "yekaitai/internal/router"
	"yekaitai/internal/service"
	"yekaitai/internal/svc"
	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/adapters/wanliniu"
	"yekaitai/pkg/common/wechatpay"
	"yekaitai/pkg/infra/kafka"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/infra/redis"
	wx_router "yekaitai/wx_internal/router"
	wxSvc "yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"

	"time"

	"yekaitai/cmd/bootstrap"
	"yekaitai/cmd/cron/tasks"
	couponService "yekaitai/internal/modules/coupon/service"
	"yekaitai/internal/modules/store/model"
	"yekaitai/internal/service/cron"
)

var (
	configFile  = flag.String("f", "", "配置文件路径")
	envFlag     = flag.String("env", "dev", "environment (dev/test/prod)")
	serviceType = flag.String("service", "all", "service type (all/admin/mini)")

	// 工具任务参数
	toolTask     = flag.String("tool_task", "", "要执行的任务名称")
	toolItemID   = flag.String("item_id", "", "商品编码（用于库存查询）")
	toolSpecCode = flag.String("spec_code", "", "规格编码（用于库存查询，可选）")
	toolTaskID   = flag.Uint("task_id", 0, "任务ID（用于手动执行优惠券发放任务）")
)

// CombinedConfig 定义一个包含所有所需配置的结构体
type CombinedConfig struct {
	Name    string        `json:"name,optional"`
	Mini    rest.RestConf `json:"mini"`    // 小程序服务配置
	Backend rest.RestConf `json:"backend"` // 后台服务配置
	// 嵌入其他配置
	LogConf    logx.LogConf            `json:"logConf,optional"`
	Mysql      config.MysqlConfig      `json:"mysql,optional"`
	Redis      config.RedisConfig      `json:"redis,optional"`
	JWT        config.JWTConfig        `json:"jwt,optional"`
	AbcYun     config.AbcYunConfig     `json:"abcYun,optional"`
	Medlinker  config.MedlinkerConfig  `json:"medlinker,optional"`
	Kafka      config.KafkaConfig      `json:"kafka,optional"`
	Wechat     config.WechatConfig     `json:"wechat,optional"`     // 微信小程序配置
	WanLiNiu   config.WanLiNiuConfig   `json:"wanliniu,optional"`   // 万里牛ERP配置
	TencentMap config.TencentMapConfig `json:"tencentMap,optional"` // 腾讯地图配置
	TencentSms config.TencentSmsConfig `json:"TencentSms,optional"` // 腾讯云短信配置
	Email      config.EmailConfig      `json:"Email,optional"`      // 邮件配置
	// 工具任务所需配置
	Cron config.CronConfig `json:"cron,optional"` // 定时任务配置
	// 微信支付配置
	WechatPay struct {
		AppID           string `json:"appID"`
		AppSecret       string `json:"appSecret"`
		MchID           string `json:"mchID"`
		ApiV3Key        string `json:"apiV3Key"`
		Certificate     string `json:"certificate"`
		PrivateKey      string `json:"privateKey"`      // 商户私钥
		SerialNo        string `json:"serialNo"`        // 证书序列号
		NotifyURL       string `json:"notifyURL"`       // 支付回调URL
		RefundNotifyURL string `json:"refundNotifyURL"` // 退款回调URL
	} `json:"wechatPay,optional"`
	// 添加杭州HIS同步相关配置
	HangzhouHIS config.HangzhouConfig `json:"HangzhouHIS,optional"` // 杭州HIS配置
}

func main() {
	flag.Parse()

	// 根据环境变量确定配置文件路径
	var finalConfigFile string
	if *configFile != "" {
		// 如果指定了配置文件，直接使用
		finalConfigFile = *configFile
	} else {
		// 根据环境变量自动选择配置文件
		finalConfigFile = fmt.Sprintf("etc/yekaitai-%s.yaml", *envFlag)
	}

	// 如果有工具任务，优先执行工具任务
	if *toolTask != "" {
		executeToolTask(finalConfigFile)
		os.Exit(0)
	}

	// 确保logs目录存在（默认目录）
	ensureLogDirectory()

	// 打印当前工作目录用于调试
	if wd, err := os.Getwd(); err == nil {
		fmt.Printf("当前工作目录: %s\n", wd)
	}
	fmt.Printf("使用配置文件: %s\n", finalConfigFile)

	// 检查配置文件是否存在
	if _, err := os.Stat(finalConfigFile); os.IsNotExist(err) {
		fmt.Printf("错误：配置文件不存在: %s\n", finalConfigFile)
		fmt.Printf("请确保配置文件存在或使用正确的工作目录\n")
		os.Exit(1)
	}

	// 加载统一配置
	var c CombinedConfig
	conf.MustLoad(finalConfigFile, &c)

	// 动态设置日志路径
	setupLogPath(&c.LogConf)

	// 设置统一日志配置 (使用go-zero的logx)
	logx.MustSetup(c.LogConf)

	// 调试：打印配置信息
	logx.Infof("=== 配置加载调试 ===")
	logx.Infof("配置文件路径: %s", finalConfigFile)
	logx.Infof("日志配置: Mode=%s, Level=%s, Path=%s", c.LogConf.Mode, c.LogConf.Level, c.LogConf.Path)
	logx.Infof("日志轮转: Rotation=%s, KeepDays=%d, Compress=%t", c.LogConf.Rotation, c.LogConf.KeepDays, c.LogConf.Compress)
	logx.Infof("腾讯云短信配置: %+v", c.TencentSms)
	logx.Infof("SecretId: '%s'", c.TencentSms.SecretId)
	logx.Infof("SecretKey: '%s'", c.TencentSms.SecretKey)
	logx.Infof("SdkAppId: '%s'", c.TencentSms.SdkAppId)
	logx.Infof("SignName: '%s'", c.TencentSms.SignName)
	logx.Infof("TemplateId: '%s'", c.TencentSms.TemplateId)
	logx.Infof("Region: '%s'", c.TencentSms.Region)
	logx.Infof("=== 配置加载调试结束 ===")

	// 修改小程序服务配置，设置合理的超时时间
	c.Mini.Timeout = 60000 // 设置为60秒超时，而非禁用超时

	// 创建后台管理配置对象
	adminCfg := config.Config{
		RestConf: c.Backend,
		LogConf:  c.LogConf,
		Mysql:    c.Mysql,
		Redis: struct {
			Addr     string
			Password string
			DB       int
			PoolSize int
		}{
			Addr:     c.Redis.Addr,
			Password: c.Redis.Password,
			DB:       c.Redis.DB,
			PoolSize: c.Redis.PoolSize,
		},
		JWT:        c.JWT,
		AbcYun:     c.AbcYun,
		Medlinker:  c.Medlinker,
		Kafka:      c.Kafka,
		Wechat:     c.Wechat,
		WanLiNiu:   c.WanLiNiu,
		TencentMap: c.TencentMap,
		TencentSms: c.TencentSms,
	}

	// 创建小程序配置对象
	wxCfg := config.Config{
		RestConf: c.Mini,
		LogConf:  c.LogConf,
		Mysql:    c.Mysql,
		Redis: struct {
			Addr     string
			Password string
			DB       int
			PoolSize int
		}{
			Addr:     c.Redis.Addr,
			Password: c.Redis.Password,
			DB:       c.Redis.DB,
			PoolSize: c.Redis.PoolSize,
		},
		JWT:        c.JWT,
		AbcYun:     c.AbcYun,
		Medlinker:  c.Medlinker,
		Kafka:      c.Kafka,
		Wechat:     c.Wechat,
		WanLiNiu:   c.WanLiNiu,
		TencentMap: c.TencentMap,
		TencentSms: c.TencentSms,
		Email:      c.Email, // 添加邮件配置
	}

	// 初始化MySQL连接
	if err := mysql.Init(adminCfg.Mysql); err != nil {
		panic(fmt.Sprintf("初始化MySQL失败: %v", err))
	}

	// 初始化Redis连接
	redisConfig := config.RedisConfig{
		Addr:     adminCfg.Redis.Addr,
		Password: adminCfg.Redis.Password,
		DB:       adminCfg.Redis.DB,
		PoolSize: adminCfg.Redis.PoolSize,
	}
	if err := redis.Init(redisConfig); err != nil {
		panic(fmt.Sprintf("初始化Redis失败: %v", err))
	}

	// 初始化微信支付
	if c.WechatPay.MchID != "" && c.WechatPay.ApiV3Key != "" && c.WechatPay.PrivateKey != "" {
		ctx := context.Background()

		wechatPayConfig := &wechatpay.Config{
			AppID:       c.WechatPay.AppID,
			AppSecret:   c.WechatPay.AppSecret,
			MchID:       c.WechatPay.MchID,
			ApiV3Key:    c.WechatPay.ApiV3Key,
			Certificate: c.WechatPay.PrivateKey, // 直接使用配置中的私钥内容
			SerialNo:    c.WechatPay.SerialNo,   // 传入证书序列号
		}

		fmt.Printf("正在初始化微信支付服务，商户ID: %s\n", c.WechatPay.MchID)
		logx.Infof("微信支付配置: AppID=%s, MchID=%s, SerialNo=%s",
			c.WechatPay.AppID, c.WechatPay.MchID, c.WechatPay.SerialNo)

		if err := wechatpay.InitWechatPay(ctx, wechatPayConfig, c.WechatPay.NotifyURL, c.WechatPay.RefundNotifyURL); err != nil {
			logx.Errorf("初始化微信支付失败: %v", err)
			fmt.Printf("错误：初始化微信支付失败: %v\n", err)
			// 不要退出程序，继续运行但记录错误
		} else {
			fmt.Println("✓ 微信支付服务初始化成功")
			logx.Info("微信支付初始化完成")
		}
	} else {
		fmt.Printf("微信支付配置不完整: MchID=%s, ApiV3Key长度=%d, PrivateKey长度=%d\n",
			c.WechatPay.MchID, len(c.WechatPay.ApiV3Key), len(c.WechatPay.PrivateKey))
		logx.Info("微信支付配置不完整，跳过初始化")
	}

	// 初始化ABC云客户端
	var abcYunClient *abcyun.AbcYunClient
	if adminCfg.AbcYun.BaseURL != "" && adminCfg.AbcYun.AppID != "" {
		abcYunClient = abcyun.NewClient(&adminCfg.AbcYun)
		fmt.Println("✓ ABC云客户端初始化成功:", adminCfg.AbcYun.BaseURL)
		fmt.Printf("ABC云配置: AppID=%s, ClinicID=%s\n", adminCfg.AbcYun.AppID, adminCfg.AbcYun.ClinicID)
		logx.Infof("ABC云客户端初始化完成: %s", adminCfg.AbcYun.BaseURL)
	} else {
		fmt.Println("⚠ 未初始化ABC云客户端，配置不完整")
		fmt.Printf("ABC云配置状态: BaseURL=%s, AppID=%s\n", adminCfg.AbcYun.BaseURL, adminCfg.AbcYun.AppID)
		logx.Error("ABC云客户端配置不完整，跳过初始化")
	}

	// 初始化万里牛ERP客户端
	if adminCfg.WanLiNiu.BaseURL != "" && adminCfg.WanLiNiu.AppKey != "" {
		wanliniu.Init(wanliniu.Config{
			BaseURL:   adminCfg.WanLiNiu.BaseURL,
			AppKey:    adminCfg.WanLiNiu.AppKey,
			AppSecret: adminCfg.WanLiNiu.AppSecret,
			ShopNick:  adminCfg.WanLiNiu.ShopNick,
			ShopType:  adminCfg.WanLiNiu.ShopType,
		})
		fmt.Println("已初始化万里牛ERP客户端:", adminCfg.WanLiNiu.BaseURL)
		fmt.Printf("店铺配置: 昵称=%s, 类型=%d\n", adminCfg.WanLiNiu.ShopNick, adminCfg.WanLiNiu.ShopType)
	} else {
		fmt.Println("未初始化万里牛ERP客户端，配置不完整")
	}

	// 初始化Kafka生产者
	if len(adminCfg.Kafka.Brokers) > 0 {
		fmt.Printf("正在初始化Kafka生产者: Brokers=%v, Username=%s\n",
			adminCfg.Kafka.Brokers, adminCfg.Kafka.Username)
		if err := kafka.InitProducer(adminCfg.Kafka); err != nil {
			logx.Errorf("初始化Kafka生产者失败，消息无法发送到Kafka: %v", err)
			fmt.Printf("⚠ Kafka生产者初始化失败: %v\n", err)
			fmt.Println("程序将继续运行，但Kafka功能不可用")
		} else {
			fmt.Printf("✓ Kafka生产者初始化成功: Brokers=%v\n", adminCfg.Kafka.Brokers)
		}
	} else {
		fmt.Println("未初始化Kafka生产者，配置不完整或未启用")
	}

	// 初始化杭州HIS客户端
	var hangzhouClient *hangzhou.Client
	if c.HangzhouHIS.BaseURL != "" {
		hangzhouConfig := hangzhou.Config{
			BaseURL:       c.HangzhouHIS.BaseURL,
			Port:          c.HangzhouHIS.Port,
			ClientSecret:  c.HangzhouHIS.ClientSecret,
			UserName:      c.HangzhouHIS.UserName,
			Password:      c.HangzhouHIS.Password,
			CurrentJsdj:   c.HangzhouHIS.CurrentJsdj,
			CurrentJtid:   c.HangzhouHIS.CurrentJtid,
			CurrentWsjgid: c.HangzhouHIS.CurrentWsjgid,
			CurrentYhjsid: c.HangzhouHIS.CurrentYhjsid,
		}
		hangzhouClient = hangzhou.NewClient(hangzhouConfig)
		hangzhou.DefaultClient = hangzhouClient
		fmt.Println("已初始化杭州HIS客户端:", c.HangzhouHIS.BaseURL)
	} else {
		fmt.Println("未初始化杭州HIS客户端，配置不完整")
	}

	// 执行数据库迁移和初始化
	fmt.Println("开始自动迁移数据库表结构...")
	if err := bootstrap.Bootstrap(); err != nil {
		panic(fmt.Sprintf("数据库迁移失败: %v", err))
	}
	fmt.Println("数据库表结构迁移完成")

	// 初始化定时任务管理服务
	cronService := service.NewCronService(mysql.Master(), hangzhouClient, abcYunClient)

	// 根据服务类型决定是否启动定时任务（只在admin或all模式下启动）
	if *serviceType == "admin" || *serviceType == "all" {
		cronService.Start()
		logx.Info("定时任务服务已启动")
	} else {
		logx.Info("当前服务类型不启动定时任务", logx.Field("serviceType", *serviceType))
	}

	// 根据服务类型启动不同的服务
	fmt.Printf("正在启动Web服务，服务类型: %s\n", *serviceType)

	var adminServer *rest.Server
	var wxServer *rest.Server

	// 根据服务类型创建相应的服务
	switch *serviceType {
	case "admin":
		// 只启动后台管理服务
		ctx := svc.NewServiceContext(adminCfg)
		adminServer = rest.MustNewServer(adminCfg.RestConf,
			rest.WithCors("*"), // 添加CORS支持，允许所有域名
			rest.WithNotAllowedHandler(middleware.NotAllowedHandler()), // 处理OPTIONS预检请求
		)
		admin_router.RegisterHandlers(adminServer, ctx)
		fmt.Printf("后台管理服务地址: %s:%d\n", adminCfg.RestConf.Host, adminCfg.RestConf.Port)

	case "mini":
		// 只启动小程序服务
		wxCtx := wxSvc.NewWxServiceContext(wxCfg)
		wxServer = rest.MustNewServer(wxCfg.RestConf,
			rest.WithCors("*"), // 添加CORS支持，允许所有域名
		)
		wx_router.RegisterHandlers(wxServer, wxCtx)
		fmt.Printf("小程序服务地址: %s:%d\n", wxCfg.RestConf.Host, wxCfg.RestConf.Port)

	case "all":
		// 启动两个HTTP服务（默认模式）
		ctx := svc.NewServiceContext(adminCfg)
		adminServer = rest.MustNewServer(adminCfg.RestConf,
			rest.WithCors("*"), // 添加CORS支持，允许所有域名
			rest.WithNotAllowedHandler(middleware.NotAllowedHandler()), // 处理OPTIONS预检请求
		)
		admin_router.RegisterHandlers(adminServer, ctx)

		wxCtx := wxSvc.NewWxServiceContext(wxCfg)
		wxServer = rest.MustNewServer(wxCfg.RestConf,
			rest.WithCors("*"), // 添加CORS支持，允许所有域名
		)
		wx_router.RegisterHandlers(wxServer, wxCtx)
		fmt.Printf("后台管理服务地址: %s:%d\n", adminCfg.RestConf.Host, adminCfg.RestConf.Port)
		fmt.Printf("小程序服务地址: %s:%d\n", wxCfg.RestConf.Host, wxCfg.RestConf.Port)

	default:
		fmt.Printf("错误：未知的服务类型: %s\n", *serviceType)
		fmt.Println("支持的服务类型: all, admin, mini")
		os.Exit(1)
	}

	// 注册关闭钩子（仅在启动所有服务时需要）
	if *serviceType == "all" {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

		go func() {
			<-sigChan
			fmt.Println("收到中断信号，准备关闭服务...")
			// 关闭定时任务（如果已启动）
			if *serviceType == "admin" || *serviceType == "all" {
				cronService.Stop()
			}

			// 关闭服务器
			if adminServer != nil {
				adminServer.Stop()
			}
			if wxServer != nil {
				wxServer.Stop()
			}

			// 关闭数据库连接
			if err := mysql.Close(); err != nil {
				logx.Errorf("关闭MySQL连接失败: %v", err)
			}

			// 关闭Redis连接
			if err := redis.Close(); err != nil {
				logx.Errorf("关闭Redis连接失败: %v", err)
			}

			// 关闭Kafka相关资源
			kafka.Close()
			logx.Info("Kafka相关资源已关闭")

			logx.Info("所有资源已关闭")
			os.Exit(0)
		}()
	}

	// 根据服务类型启动HTTP服务器
	switch *serviceType {
	case "admin":
		if adminServer != nil {
			fmt.Println("启动后台管理服务...")
			adminServer.Start() // 阻塞启动
		}
	case "mini":
		if wxServer != nil {
			fmt.Println("启动小程序服务...")
			wxServer.Start() // 阻塞启动
		}
	case "all":
		if adminServer != nil && wxServer != nil {
			fmt.Println("启动所有服务...")
			go adminServer.Start()
			go wxServer.Start()
			// 阻塞主线程
			select {}
		}
	}
}

// setupLogPath 动态设置日志路径
func setupLogPath(logConf *logx.LogConf) {
	// 检查是否有环境变量设置的日志路径
	if logPath := os.Getenv("LOG_PATH"); logPath != "" {
		logConf.Path = logPath
		fmt.Printf("使用环境变量LOG_PATH: %s\n", logPath)
	} else if logRoot := os.Getenv("LOG_ROOT"); logRoot != "" {
		// 如果有LOG_ROOT，构建完整路径
		appName := os.Getenv("APP_NAME")
		if appName == "" {
			appName = "yekaitai" // 默认应用名
		}
		logConf.Path = fmt.Sprintf("%s/%s", logRoot, appName)
		fmt.Printf("使用环境变量LOG_ROOT构建路径: %s\n", logConf.Path)
	}

	// 确保日志目录存在
	ensureLogDirectory(logConf.Path)
}

// 确保日志目录存在
func ensureLogDirectory(logDir ...string) {
	dir := "logs"
	if len(logDir) > 0 && logDir[0] != "" {
		dir = logDir[0]
	}

	if _, err := os.Stat(dir); os.IsNotExist(err) {
		err = os.MkdirAll(dir, 0755)
		if err != nil {
			fmt.Printf("无法创建日志目录: %s, 错误: %s\n", dir, err.Error())
		} else {
			fmt.Printf("已创建日志目录: %s\n", dir)
		}
	}
}

// executeToolTask 执行工具任务
func executeToolTask(configFile string) {
	// 加载配置
	var c CombinedConfig
	conf.MustLoad(configFile, &c)

	// 初始化日志
	logx.MustSetup(logx.LogConf{
		ServiceName: "tools",
		Mode:        "console",
	})

	// 初始化数据库连接
	if err := mysql.Init(c.Mysql); err != nil {
		fmt.Printf("初始化数据库连接失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化杭州HIS客户端
	if err := hangzhou.InitDefaultClient(&c.HangzhouHIS); err != nil {
		fmt.Printf("初始化杭州HIS客户端失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化ABC云客户端
	var abcYunClient *abcyun.AbcYunClient
	if c.AbcYun.BaseURL != "" && c.AbcYun.AppID != "" {
		abcYunClient = abcyun.NewClient(&c.AbcYun)
		fmt.Println("已初始化ABC云客户端:", c.AbcYun.BaseURL)
	}

	// 初始化万里牛ERP客户端
	if c.WanLiNiu.BaseURL != "" && c.WanLiNiu.AppKey != "" {
		wanliniu.Init(wanliniu.Config{
			BaseURL:   c.WanLiNiu.BaseURL,
			AppKey:    c.WanLiNiu.AppKey,
			AppSecret: c.WanLiNiu.AppSecret,
			ShopNick:  c.WanLiNiu.ShopNick,
			ShopType:  c.WanLiNiu.ShopType,
		})
		fmt.Println("已初始化万里牛ERP客户端:", c.WanLiNiu.BaseURL)
		fmt.Printf("店铺配置: 昵称=%s, 类型=%d\n", c.WanLiNiu.ShopNick, c.WanLiNiu.ShopType)
	}

	// 根据任务名称执行相应的同步任务
	switch *toolTask {
	case "store_geo":
		// 同步门店地理编码
		service := cron.NewStoreGeoSyncService(&c.Cron, c.TencentMap.Key)
		if err := service.SyncAll(); err != nil {
			fmt.Printf("同步门店地理编码失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("门店地理编码同步完成")
	case "doctor_sync":
		// 同步医生数据
		db := mysql.Master()
		doctorSyncService := tasks.NewDoctorSyncService(db)
		if err := doctorSyncService.SyncDoctors(); err != nil {
			fmt.Printf("同步医生数据失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("医生数据同步完成")
	case "patient_sync":
		// 同步患者数据
		db := mysql.Master()
		patientSyncService := tasks.NewPatientSyncService(db)
		if err := patientSyncService.SyncPatients(); err != nil {
			fmt.Printf("同步患者数据失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("患者数据同步完成")
	case "org_sync":
		// 同步机构数据
		db := mysql.Master()
		storeRepo := model.NewStoreRepository(db)
		orgSyncService := tasks.NewOrgSyncService(hangzhou.DefaultClient, storeRepo, true, 1)
		if err := orgSyncService.Start(); err != nil {
			fmt.Printf("同步机构数据失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("机构数据同步完成")
	case "department_sync":
		// 同步科室数据
		departmentSyncService := tasks.NewDepartmentSyncService(hangzhou.DefaultClient, true, 1)
		departmentSyncService.SyncDepartments()
		fmt.Println("科室数据同步完成")
	case "abcyun_store_sync":
		// ABC云门店同步
		if abcYunClient == nil {
			fmt.Printf("ABC云客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		storeSyncService := tasks.NewAbcYunStoreSyncService(abcYunClient)
		if err := storeSyncService.SyncStores(); err != nil {
			fmt.Printf("ABC云门店同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("ABC云门店同步完成")
	case "abcyun_department_sync":
		// ABC云科室同步
		if abcYunClient == nil {
			fmt.Printf("ABC云客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		deptSyncService := tasks.NewAbcYunDepartmentSyncService(abcYunClient)
		if err := deptSyncService.SyncDepartments(); err != nil {
			fmt.Printf("ABC云科室同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("ABC云科室同步完成")
	case "abcyun_doctor_sync":
		// ABC云医生同步
		if abcYunClient == nil {
			fmt.Printf("ABC云客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		doctorSyncService := tasks.NewAbcYunDoctorSyncService(abcYunClient)
		if err := doctorSyncService.SyncDoctors(); err != nil {
			fmt.Printf("ABC云医生同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("ABC云医生同步完成")
	case "abcyun_patient_sync":
		// ABC云患者同步
		if abcYunClient == nil {
			fmt.Printf("ABC云客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		patientSyncService := tasks.NewAbcYunPatientSyncSimpleService(abcYunClient)
		if err := patientSyncService.SyncPatientsManual(); err != nil {
			fmt.Printf("ABC云患者同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("ABC云患者同步完成")
	case "charge_sync":
		// 同步门诊收费信息
		db := mysql.Master()
		chargeSyncService := tasks.NewChargeSyncService(db)
		chargeSyncService.SyncChargesManual()
		fmt.Println("门诊收费信息同步完成")
	case "wanliniu_goods_sync":
		// 万里牛商品同步
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		goodsSyncService := tasks.NewWanLiNiuGoodsSyncService()
		if err := goodsSyncService.SyncGoodsManual(); err != nil {
			fmt.Printf("万里牛商品同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛商品同步完成")
	case "wanliniu_shipping_sync":
		// 万里牛发货状态同步
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		shippingSyncService := tasks.NewWanLiNiuShippingSyncService()
		if err := shippingSyncService.SyncShippingStatusManual(); err != nil {
			fmt.Printf("万里牛发货状态同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛发货状态同步完成")
	case "wanliniu_failed_push":
		// 万里牛失败订单推送（定时任务逻辑）
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行推送\n")
			os.Exit(1)
		}
		failedPushService := tasks.NewWanLiNiuFailedPushService()
		if err := failedPushService.PushFailedOrders(); err != nil {
			fmt.Printf("万里牛失败订单推送失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛失败订单推送完成")
	case "wanliniu_inventory_sync":
		// 手动同步万里牛库存
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}

		// 创建万里牛库存同步服务
		inventorySyncService := tasks.NewWanLiNiuInventorySyncService(&c.WanLiNiu)
		if inventorySyncService == nil {
			fmt.Printf("万里牛库存同步服务创建失败\n")
			os.Exit(1)
		}

		// 同步万里牛库存
		fmt.Println("开始同步万里牛库存...")
		if err := inventorySyncService.SyncInventoryManual(); err != nil {
			fmt.Printf("万里牛库存同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛库存同步完成")
	case "wanliniu_inventory_query":
		// 查询ERP库存单笔
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行查询\n")
			os.Exit(1)
		}

		if *toolItemID == "" {
			fmt.Printf("请提供商品编码参数: -item_id \"商品编码\"\n")
			fmt.Printf("用法示例: ./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_query -item_id \"4b60170\" -spec_code \"001\"\n")
			os.Exit(1)
		}

		// 获取万里牛服务实例
		wanLiNiuService := wanliniu.GetService()
		if wanLiNiuService == nil {
			fmt.Printf("万里牛ERP服务未初始化，无法执行查询\n")
			os.Exit(1)
		}

		// 查询单笔库存
		fmt.Printf("正在查询商品库存...\n")
		fmt.Printf("商品编码: %s\n", *toolItemID)
		if *toolSpecCode != "" {
			fmt.Printf("规格编码: %s\n", *toolSpecCode)
		}
		fmt.Printf("店铺昵称: %s\n", c.WanLiNiu.ShopNick)
		fmt.Printf("店铺类型: %d (B2C平台)\n\n", c.WanLiNiu.ShopType)

		response, err := wanLiNiuService.QueryInventorySingle(context.Background(), *toolItemID, *toolSpecCode, "")
		if err != nil {
			fmt.Printf("查询ERP库存失败: %v\n", err)
			os.Exit(1)
		}

		// 输出查询结果
		fmt.Printf("=== 查询结果 ===\n")
		fmt.Printf("接口调用成功: %t\n", response.Data.Success)
		if !response.Data.Success {
			fmt.Printf("业务错误信息: %s\n", response.Data.ErrorMsg)
		} else {
			fmt.Printf("库存数据:\n%s\n", response.Data.Response)
		}
		fmt.Println("ERP库存查询完成")
	case "wanliniu_inventory_query_v2":
		// 查询ERP库存V2（新接口）并同步更新本地库存
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行查询\n")
			os.Exit(1)
		}

		if *toolSpecCode == "" {
			fmt.Printf("请提供规格编码参数: -spec_code \"规格编码\"\n")
			fmt.Printf("用法示例: ./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_query_v2 -spec_code \"SPEC_456\"\n")
			fmt.Printf("注意：新接口主要通过规格编码查询，也可以通过修改时间范围查询\n")
			os.Exit(1)
		}

		// 获取万里牛服务实例
		wanLiNiuService := wanliniu.GetService()
		if wanLiNiuService == nil {
			fmt.Printf("万里牛ERP服务未初始化，无法执行查询\n")
			os.Exit(1)
		}

		// 创建库存同步服务
		inventorySyncService := tasks.NewWanLiNiuInventorySyncService(&c.WanLiNiu)
		if inventorySyncService == nil {
			fmt.Printf("万里牛库存同步服务创建失败\n")
			os.Exit(1)
		}

		// 查询库存V2
		fmt.Printf("正在查询商品库存V2并同步本地库存...\n")
		fmt.Printf("规格编码: %s\n", *toolSpecCode)
		fmt.Printf("店铺昵称: %s\n", c.WanLiNiu.ShopNick)
		fmt.Printf("店铺类型: %d (B2C平台)\n\n", c.WanLiNiu.ShopType)

		var response *wanliniu.InventoryQueryV2Response
		var err error

		ctx := context.Background()
		// 使用规格编码查询
		response, err = wanLiNiuService.QueryInventoryV2BySkuCode(ctx, *toolSpecCode, "")

		if err != nil {
			fmt.Printf("查询ERP库存V2失败: %v\n", err)
			os.Exit(1)
		}

		// 输出查询结果并同步库存
		fmt.Printf("=== 查询结果V2 ===\n")
		fmt.Printf("返回记录数: %d\n", len(response.Data))
		updatedRecords := 0

		for i, inventory := range response.Data {
			// 计算可用库存 = 实际库存 - 锁定库存
			availableStock := inventory.Quantity - inventory.LockSize
			if availableStock < 0 {
				availableStock = 0
			}

			fmt.Printf("  %d. 商品编码: %s, 规格编码: %s, 实际库存: %.0f, 锁定库存: %.0f, 可用库存: %.0f\n",
				i+1, inventory.GoodsCode, inventory.SkuCode, inventory.Quantity, inventory.LockSize, availableStock)

			// 显示详细库存信息
			fmt.Printf("    库存信息: 货号=%s, 条码=%s, 仓库=%s, 规格名称=%s\n",
				inventory.ArticleNumber, inventory.BarCode, inventory.StorageCode, inventory.SpecName)
			fmt.Printf("    其他信息: 次品数量=%.0f, 在途库存=%.0f, 成本=%.2f\n",
				inventory.DefectNum, inventory.Underway, inventory.Cost)
			updatedRecords++
		}

		fmt.Printf("\n=== 同步完成 ===\n")
		fmt.Printf("总计查询 %d 条记录，显示 %d 条库存信息\n", len(response.Data), updatedRecords)
	case "wanliniu_inventory_query_v2_time":
		// 查询ERP库存V2（通过时间范围）并同步更新本地库存
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行查询\n")
			os.Exit(1)
		}

		// 获取万里牛服务实例
		wanLiNiuService := wanliniu.GetService()
		if wanLiNiuService == nil {
			fmt.Printf("万里牛ERP服务未初始化，无法执行查询\n")
			os.Exit(1)
		}

		// 创建库存同步服务
		inventorySyncService := tasks.NewWanLiNiuInventorySyncService(&c.WanLiNiu)
		if inventorySyncService == nil {
			fmt.Printf("万里牛库存同步服务创建失败\n")
			os.Exit(1)
		}

		// 设置查询时间范围（最近7天）
		endTime := time.Now()
		startTime := endTime.AddDate(0, 0, -7)

		fmt.Printf("正在查询万里牛库存V2（时间范围）...\n")
		fmt.Printf("查询时间范围: %s 到 %s\n", startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
		fmt.Printf("店铺昵称: %s\n", c.WanLiNiu.ShopNick)
		fmt.Printf("店铺类型: %d (B2C平台)\n\n", c.WanLiNiu.ShopType)

		ctx := context.Background()
		page := 1
		pageSize := 50
		totalRecords := 0
		updatedRecords := 0

		for {
			fmt.Printf("正在查询第 %d 页...\n", page)
			response, err := wanLiNiuService.QueryInventoryV2ByModifyTime(ctx, page, pageSize,
				startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), "")

			if err != nil {
				fmt.Printf("查询ERP库存V2失败: %v\n", err)
				os.Exit(1)
			}

			if len(response.Data) == 0 {
				fmt.Printf("第 %d 页没有数据，查询结束\n", page)
				break
			}

			fmt.Printf("第 %d 页查询到 %d 条库存记录\n", page, len(response.Data))
			totalRecords += len(response.Data)

			for i, inventory := range response.Data {
				// 计算可用库存 = 实际库存 - 锁定库存
				availableStock := inventory.Quantity - inventory.LockSize
				if availableStock < 0 {
					availableStock = 0
				}

				fmt.Printf("  %d. 商品编码: %s, 规格编码: %s, 实际库存: %.0f, 可用库存: %.0f\n",
					i+1, inventory.GoodsCode, inventory.SkuCode, inventory.Quantity, availableStock)
				updatedRecords++
			}

			// 如果返回的记录数少于页大小，说明已经是最后一页
			if len(response.Data) < pageSize {
				break
			}

			page++
		}

		fmt.Printf("\n=== 同步完成 ===\n")
		fmt.Printf("总计查询 %d 条记录，显示 %d 条库存信息\n", totalRecords, updatedRecords)
	case "wanliniu_inventory_sync_test":
		// 测试万里牛库存同步
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步测试\n")
			os.Exit(1)
		}

		// 创建万里牛库存同步服务
		inventorySyncService := tasks.NewWanLiNiuInventorySyncService(&c.WanLiNiu)
		if inventorySyncService == nil {
			fmt.Printf("万里牛库存同步服务创建失败\n")
			os.Exit(1)
		}

		fmt.Println("开始测试万里牛库存同步...")
		if err := inventorySyncService.SyncInventoryManual(); err != nil {
			fmt.Printf("万里牛库存同步测试失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛库存同步测试完成")
	case "wanliniu_categories_test":
		// 万里牛商品分类API测试
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行测试\n")
			os.Exit(1)
		}
		// 获取万里牛服务实例
		wanLiNiuService := wanliniu.GetService()
		if wanLiNiuService == nil {
			fmt.Printf("万里牛ERP服务未初始化，无法执行测试\n")
			os.Exit(1)
		}

		// 测试获取商品分类
		fmt.Println("开始测试万里牛商品分类API...")
		response, err := wanLiNiuService.GetCategories(context.Background(), 1, 10)
		if err != nil {
			fmt.Printf("万里牛商品分类API测试失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Printf("商品分类API测试成功！返回 %d 条分类数据:\n", response.Total)
		for i, category := range response.Data {
			hasChildStr := "否"
			if category.HasChild {
				hasChildStr = "是"
			}
			fmt.Printf("  %d. ID: %s, 名称: %s, 父节点: %s, 有子节点: %s\n",
				i+1, category.CategoryID, category.CategoryName, category.ParentID, hasChildStr)
		}

		// 测试获取所有分类
		fmt.Println("\n开始测试获取所有商品分类...")
		allResponse, err := wanLiNiuService.GetAllCategories(context.Background())
		if err != nil {
			fmt.Printf("万里牛获取所有商品分类失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("获取所有分类成功！总共 %d 条分类数据\n", allResponse.Total)
		fmt.Println("万里牛商品分类API测试完成")
	case "wanliniu_categories_sync":
		// 手动同步万里牛商品分类
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}

		// 创建万里牛分类同步服务
		categorySyncService := tasks.NewWanLiNiuCategorySyncService()
		if categorySyncService == nil {
			fmt.Printf("万里牛分类同步服务创建失败\n")
			os.Exit(1)
		}

		// 同步万里牛商品分类
		fmt.Println("开始同步万里牛商品分类...")
		if err := categorySyncService.SyncCategoriesManual(); err != nil {
			fmt.Printf("万里牛商品分类同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛商品分类同步完成")
	case "init_default_category":
		// 初始化默认商品分类
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行初始化\n")
			os.Exit(1)
		}

		// 创建万里牛分类同步服务
		categorySyncService := tasks.NewWanLiNiuCategorySyncService()
		if categorySyncService == nil {
			fmt.Printf("万里牛分类同步服务创建失败\n")
			os.Exit(1)
		}

		// 初始化默认商品分类
		fmt.Println("开始初始化默认商品分类...")
		if err := categorySyncService.SyncCategoriesManual(); err != nil {
			fmt.Printf("初始化默认商品分类失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("默认商品分类初始化完成")
	case "coupon_issue_execute":
		// 手动执行优惠券发放任务
		if *toolTaskID == 0 {
			fmt.Printf("请提供任务ID参数: -task_id 任务ID\n")
			fmt.Printf("用法示例: ./main -f etc/yekaitai-dev.yaml -tool_task coupon_issue_execute -task_id 123\n")
			os.Exit(1)
		}

		// 创建优惠券服务
		couponSvc := couponService.NewCouponService()
		if couponSvc == nil {
			fmt.Printf("优惠券服务创建失败\n")
			os.Exit(1)
		}

		fmt.Printf("开始执行优惠券发放任务...\n")
		fmt.Printf("任务ID: %d\n", *toolTaskID)

		// 手动执行任务，执行者ID设为0（系统执行）
		ctx := context.Background()
		if err := couponSvc.ExecuteIssueTask(ctx, uint(*toolTaskID), 0); err != nil {
			fmt.Printf("执行优惠券发放任务失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Printf("优惠券发放任务执行成功\n")
	case "user_level_upgrade":
		// 手动触发用户等级升级任务
		fmt.Println("开始手动执行用户等级升级任务...")

		// 创建用户等级升级服务
		userLevelUpgradeService := tasks.NewUserLevelUpgradeService()
		if userLevelUpgradeService == nil {
			fmt.Printf("用户等级升级服务创建失败\n")
			os.Exit(1)
		}

		// 执行用户等级升级任务
		userLevelUpgradeService.RunUpgradeTask()

		fmt.Println("用户等级升级任务执行完成")
	case "coin_reward_tasks":
		// 手动触发叶小币奖励任务
		fmt.Println("开始手动执行叶小币奖励任务...")

		// 创建叶小币奖励任务服务
		coinRewardTaskService := tasks.NewCoinRewardTaskService()
		if coinRewardTaskService == nil {
			fmt.Printf("叶小币奖励任务服务创建失败\n")
			os.Exit(1)
		}

		// 执行所有叶小币奖励任务
		coinRewardTaskService.RunAllCoinRewardTasks()

		fmt.Println("叶小币奖励任务执行完成")
	case "activity_expired_check":
		// 手动触发过期活动检查
		fmt.Println("开始手动执行过期活动检查任务...")

		activityTaskService := tasks.NewActivityTaskService()
		if activityTaskService == nil {
			fmt.Printf("活动任务服务创建失败\n")
			os.Exit(1)
		}

		if err := activityTaskService.ProcessExpiredActivities(); err != nil {
			fmt.Printf("过期活动检查失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Println("过期活动检查任务执行完成")
	case "activity_signup_update":
		// 手动触发活动报名人数更新
		fmt.Println("开始手动执行活动报名人数更新任务...")

		activityTaskService := tasks.NewActivityTaskService()
		if activityTaskService == nil {
			fmt.Printf("活动任务服务创建失败\n")
			os.Exit(1)
		}

		if err := activityTaskService.UpdateActivitySignUpCounts(); err != nil {
			fmt.Printf("活动报名人数更新失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Println("活动报名人数更新任务执行完成")
	case "activity_batch_refund":
		// 手动触发批量退款过期订单
		fmt.Println("开始手动执行批量退款过期订单任务...")

		activityTaskService := tasks.NewActivityTaskService()
		if activityTaskService == nil {
			fmt.Printf("活动任务服务创建失败\n")
			os.Exit(1)
		}

		batchSize := 100 // 默认批次大小
		if err := activityTaskService.BatchRefundExpiredOrders(batchSize); err != nil {
			fmt.Printf("批量退款过期订单失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Println("批量退款过期订单任务执行完成")
	case "activity_qrcode_cleanup":
		// 手动触发过期二维码清理
		fmt.Println("开始手动执行过期二维码清理任务...")

		activityTaskService := tasks.NewActivityTaskService()
		if activityTaskService == nil {
			fmt.Printf("活动任务服务创建失败\n")
			os.Exit(1)
		}

		if err := activityTaskService.CleanupExpiredQRCodes(); err != nil {
			fmt.Printf("过期二维码清理失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Println("过期二维码清理任务执行完成")
	case "activity_statistics":
		// 手动生成活动统计报告
		fmt.Println("开始生成活动统计报告...")

		activityTaskService := tasks.NewActivityTaskService()
		if activityTaskService == nil {
			fmt.Printf("活动任务服务创建失败\n")
			os.Exit(1)
		}

		if err := activityTaskService.GenerateActivityStatistics(); err != nil {
			fmt.Printf("生成活动统计报告失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Println("活动统计报告生成完成")
	case "activity_data_fix":
		// 手动修复活动数据
		fmt.Println("开始手动修复活动数据...")

		activityTaskService := tasks.NewActivityTaskService()
		if activityTaskService == nil {
			fmt.Printf("活动任务服务创建失败\n")
			os.Exit(1)
		}

		if err := activityTaskService.FixActivityData(); err != nil {
			fmt.Printf("修复活动数据失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Println("活动数据修复完成")
	default:
		printToolTaskUsage()
		os.Exit(1)
	}
}

// printToolTaskUsage 打印工具任务使用说明
func printToolTaskUsage() {
	fmt.Println("可用的任务列表：")
	fmt.Println("  store_geo                - 同步门店地理编码")
	fmt.Println("  doctor_sync              - 同步医生数据")
	fmt.Println("  patient_sync             - 同步患者数据")
	fmt.Println("  org_sync                 - 同步机构数据")
	fmt.Println("  department_sync          - 同步科室数据")
	fmt.Println("  abcyun_store_sync        - ABC云门店同步")
	fmt.Println("  abcyun_department_sync   - ABC云科室同步")
	fmt.Println("  abcyun_doctor_sync       - ABC云医生同步")
	fmt.Println("  abcyun_patient_sync      - ABC云患者同步")
	fmt.Println("  charge_sync              - 同步门诊收费信息")
	fmt.Println("  wanliniu_goods_sync       - 万里牛商品同步")
	fmt.Println("  wanliniu_shipping_sync    - 万里牛发货状态同步")
	fmt.Println("  wanliniu_failed_push      - 万里牛失败订单推送（定时任务逻辑）")
	fmt.Println("  wanliniu_inventory_sync   - 万里牛库存同步")
	fmt.Println("  wanliniu_inventory_query  - 查询ERP库存单笔")
	fmt.Println("  wanliniu_inventory_query_v2 - 查询ERP库存V2（新接口）")
	fmt.Println("  wanliniu_inventory_query_v2_time - 查询ERP库存V2（时间范围）")
	fmt.Println("  wanliniu_inventory_sync_test - 测试万里牛库存同步")
	fmt.Println("  wanliniu_categories_test  - 万里牛商品分类API测试")
	fmt.Println("  wanliniu_categories_sync  - 万里牛商品分类同步")
	fmt.Println("  init_default_category    - 初始化默认商品分类(万里牛商品分类同步)")
	fmt.Println("  coupon_issue_execute     - 手动执行优惠券发放任务")
	fmt.Println("  user_level_upgrade       - 手动触发用户等级升级任务")
	fmt.Println("  coin_reward_tasks        - 手动触发叶小币奖励任务")
	fmt.Println("  activity_expired_check   - 手动触发过期活动检查")
	fmt.Println("  activity_signup_update   - 手动触发活动报名人数更新")
	fmt.Println("  activity_batch_refund    - 手动触发批量退款过期订单")
	fmt.Println("  activity_qrcode_cleanup  - 手动触发过期二维码清理")
	fmt.Println("  activity_statistics      - 手动生成活动统计报告")
	fmt.Println("  activity_data_fix        - 手动修复活动数据")
}
