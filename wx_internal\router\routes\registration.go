package routes

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/registration/handler"
	"yekaitai/wx_internal/svc"
)

// RegisterRegistrationRoutes 注册挂号相关路由
func RegisterRegistrationRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 初始化处理器
	sourceHandler := handler.NewSourceHandler()
	appointmentHandler := handler.NewAppointmentHandler()
	// patientHandler := handler.NewPatientHandler()

	// 使用微信认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 注册号源查询路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/consumer-ylfw/ylfw/yspb/hyjl/list",
			Handler: wxAuthWrapper(sourceHandler.Query),
		},
	)

	// 注册获取指定时间段号源记录的路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/consumer-ylfw/ylfw/yspb/hyjl/period",
			Handler: wxAuthWrapper(sourceHandler.QueryRecords),
		},
	)

	// 注册获取完整号源记录信息的路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/consumer-ylfw/ylfw/yspb/hyjl/complete",
			Handler: wxAuthWrapper(sourceHandler.QueryCompleteRecords),
		},
	)

	// 注册预约挂号路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodPost,
			Path:    "/api/wx/consumer-ylfw/ylfw/yygh",
			Handler: wxAuthWrapper(appointmentHandler.Create),
		},
	)

	// 注册取消预约挂号路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodPost,
			Path:    "/api/wx/registration/appointment/cancel",
			Handler: wxAuthWrapper(appointmentHandler.Cancel),
		},
	)

	// 注册挂号订单列表查询路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/registration/appointment/list",
			Handler: wxAuthWrapper(appointmentHandler.GetAppointmentList),
		},
	)

	// 注册挂号订单详情查询路由
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/registration/appointment/detail/:id",
			Handler: wxAuthWrapper(appointmentHandler.GetAppointmentDetail),
		},
	)

	// 注册挂号订单详情查询路由（支持yyghid和registration_sheet_id查询）
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/registration/appointment/detail-by-params",
			Handler: wxAuthWrapper(appointmentHandler.GetAppointmentDetailByParams),
		},
	)
}
