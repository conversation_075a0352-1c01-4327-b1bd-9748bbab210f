package jushuitan

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

// 定义用于处理单层和双层响应的结构体
type apiResponse struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}

// UploadItemSku 上传普通商品资料
func (c *Client) UploadItemSku(ctx context.Context, req *ItemSkuUploadRequest) (*BaseResp, error) {
	// 准备API路径
	path := ItemSkuUploadAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("上传普通商品资料请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析上传普通商品资料响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryItemSku 查询普通商品资料(按sku)
func (c *Client) QueryItemSku(ctx context.Context, req *ItemSkuQueryRequest) (*BaseResp, error) {
	// 准备API路径
	path := ItemSkuQueryAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询普通商品资料请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析查询普通商品资料响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryMallItem 查询普通商品资料(按款)
func (c *Client) QueryMallItem(ctx context.Context, req *MallItemQueryRequest) (*BaseResp, error) {
	// 准备API路径
	path := MallItemQueryAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询普通商品资料(按款)请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析查询普通商品资料(按款)响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// UploadSkuMap 上传店铺商品资料
func (c *Client) UploadSkuMap(ctx context.Context, req *SkuMapUploadRequest) (*BaseResp, error) {
	// 准备API路径
	path := SkuMapUploadAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("上传店铺商品资料请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析上传店铺商品资料响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QuerySkuMap 查询店铺商品资料
func (c *Client) QuerySkuMap(ctx context.Context, req *SkuMapQueryRequest) (*BaseResp, error) {
	// 准备API路径
	path := SkuMapQueryAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询店铺商品资料请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析查询店铺商品资料响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// UploadCombineSku 上传组合装商品资料
func (c *Client) UploadCombineSku(ctx context.Context, req *CombineSkuUploadRequest) (*BaseResp, error) {
	// 准备API路径
	path := CombineSkuUploadAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("上传组合装商品资料请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析上传组合装商品资料响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryCombineSku 查询组合装商品资料
func (c *Client) QueryCombineSku(ctx context.Context, req *CombineSkuQueryRequest) (*BaseResp, error) {
	// 准备API路径
	path := CombineSkuQueryAPIPath

	// 设置默认值
	if req.PageIndex == 0 {
		req.PageIndex = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 30
	}

	// 格式化时间
	if req.ModifiedBegin != "" {
		t, err := time.Parse("2006-01-02 15:04:05", req.ModifiedBegin)
		if err == nil {
			req.ModifiedBegin = t.Format("2006-01-02 15:04:05")
		}
	}

	if req.ModifiedEnd != "" {
		t, err := time.Parse("2006-01-02 15:04:05", req.ModifiedEnd)
		if err == nil {
			req.ModifiedEnd = t.Format("2006-01-02 15:04:05")
		}
	}

	// 验证必填参数
	if req.ModifiedBegin == "" || req.ModifiedEnd == "" {
		if req.SkuIDs == "" {
			return nil, fmt.Errorf("修改时间区间和商品编码至少需要提供一项")
		}
	}

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询组合装商品资料请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析查询组合装商品资料响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// AddOrUpdateCategory 上传/更新商品类目
func (c *Client) AddOrUpdateCategory(ctx context.Context, req *CategoryAddOrUpdateRequest) (*BaseResp, error) {
	// 准备API路径
	path := CategoryAddOrUpdateAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("上传/更新商品类目请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析上传/更新商品类目响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryCategory 查询商品类目
func (c *Client) QueryCategory(ctx context.Context, req *CategoryQueryRequest) (*BaseResp, error) {
	// 准备API路径
	path := CategoryQueryAPIPath

	// 设置默认值
	if req.PageIndex == 0 {
		req.PageIndex = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	// 格式化时间
	if req.ModifiedBegin != "" {
		t, err := time.Parse("2006-01-02 15:04:05", req.ModifiedBegin)
		if err == nil {
			req.ModifiedBegin = t.Format("2006-01-02 15:04:05")
		}
	}

	if req.ModifiedEnd != "" {
		t, err := time.Parse("2006-01-02 15:04:05", req.ModifiedEnd)
		if err == nil {
			req.ModifiedEnd = t.Format("2006-01-02 15:04:05")
		}
	}

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询商品类目请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析查询商品类目响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// SaveBom 上传BOM信息
func (c *Client) SaveBom(ctx context.Context, req *BomSaveRequest) (*BaseResp, error) {
	// 准备API路径
	path := BomSaveAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("上传BOM信息请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析上传BOM信息响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// GetSkuBomPageList 查询BOM信息
func (c *Client) GetSkuBomPageList(ctx context.Context, req *BomQueryRequest) (*BaseResp, error) {
	// 准备API路径
	path := BomQueryAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询BOM信息请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析查询BOM信息响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// UploadItemSkuCostPrice 上传商品历史成本价
func (c *Client) UploadItemSkuCostPrice(ctx context.Context, req *ItemSkuCostPriceUploadRequest) (*BaseResp, error) {
	// 准备API路径
	path := CostPriceUploadAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("上传商品历史成本价请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析上传商品历史成本价响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// GetHistoryCostPriceV2 查询商品历史成本价
func (c *Client) GetHistoryCostPriceV2(ctx context.Context, req *HistoryCostPriceQueryRequest) (*BaseResp, error) {
	// 准备API路径
	path := HistoryCostPriceQueryAPIPath

	// 确保sku_ids非空
	if len(req.SkuIDs) == 0 {
		return nil, fmt.Errorf("商品编码不能为空")
	}

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询商品历史成本价请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析查询商品历史成本价响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// SaveSupplierSku 上传/更新商品多供应商
func (c *Client) SaveSupplierSku(ctx context.Context, req *SupplierSkuSaveRequest) (*SupplierSkuSaveResponse, error) {
	// 准备API路径
	path := SupplierSkuSaveAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("上传/更新商品多供应商请求失败: %w", err)
	}

	// 解析响应
	var apiResp apiResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return nil, fmt.Errorf("解析上传/更新商品多供应商响应失败: %w", err)
	}

	// 转换code
	if apiResp.Code == 0 {
		apiResp.Code = 200
	}

	// 检查是否是双层结构
	var result SupplierSkuSaveResponse
	if err := json.Unmarshal(apiResp.Data, &result); err != nil {
		// 单层结构，直接解析
		if err := json.Unmarshal(resp, &result); err != nil {
			return nil, fmt.Errorf("解析上传/更新商品多供应商响应失败: %w", err)
		}
	}

	// 将字符串code转换
	if result.Code == "0" {
		result.Code = "200"
	}

	return &result, nil
}

// GetSupplierSkuList 查询商品多供应商列表
// 文档: https://open.jushuitan.com/document/2029.html
func (c *Client) GetSupplierSkuList(ctx context.Context, req *SupplierSkuQueryRequest) (*SupplierSkuQueryResponse, error) {
	// 准备API路径
	path := SupplierSkuQueryAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询商品多供应商请求失败: %w", err)
	}

	// 解析响应
	var apiResp apiResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return nil, fmt.Errorf("解析查询商品多供应商响应失败: %w", err)
	}

	// 转换code
	if apiResp.Code == 0 {
		apiResp.Code = 200
	}

	// 检查是否是双层结构
	var result SupplierSkuQueryResponse
	if err := json.Unmarshal(apiResp.Data, &result); err != nil {
		// 单层结构，直接解析
		if err := json.Unmarshal(resp, &result); err != nil {
			return nil, fmt.Errorf("解析查询商品多供应商响应失败: %w", err)
		}
	}

	result.Code = apiResp.Code
	result.Msg = apiResp.Msg
	return &result, nil
}

// GetSupplierSkuQueryList 查询商品多供应商列表(完整参数)
// 文档: https://open.jushuitan.com/document/2029.html
func (c *Client) GetSupplierSkuQueryList(ctx context.Context, req *SupplierSkuQueryListRequest) (*SupplierSkuQueryListResponse, error) {
	// 准备API路径
	path := SupplierSkuQueryAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("查询商品多供应商请求失败: %w", err)
	}

	// 解析响应
	var apiResp apiResponse
	if err := json.Unmarshal(resp, &apiResp); err != nil {
		return nil, fmt.Errorf("解析查询商品多供应商响应失败: %w", err)
	}

	// 转换code
	if apiResp.Code == 0 {
		apiResp.Code = 200
	}

	// 检查是否是双层结构
	var result SupplierSkuQueryListResponse
	if err := json.Unmarshal(apiResp.Data, &result); err != nil {
		// 单层结构，直接解析
		if err := json.Unmarshal(resp, &result); err != nil {
			return nil, fmt.Errorf("解析查询商品多供应商响应失败: %w", err)
		}
	}

	result.Code = apiResp.Code
	result.Msg = apiResp.Msg
	return &result, nil
}
