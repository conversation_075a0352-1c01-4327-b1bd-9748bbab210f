package types

import (
	"time"
)

// WxRedeemerInfo 核销用户信息
type WxRedeemerInfo struct {
	RedeemerID uint      `json:"redeemer_id"` // 核销用户ID
	UserID     uint      `json:"user_id"`     // 用户ID
	OpenID     string    `json:"open_id"`     // 微信 OpenID
	Mobile     string    `json:"mobile"`      // 手机号
	Nickname   string    `json:"nickname"`    // 昵称
	Avatar     string    `json:"avatar"`      // 头像
	Gender     int       `json:"gender"`      // 性别 0-未知 1-男 2-女
	StoreID    uint      `json:"store_id"`    // 门店ID
	StoreName  string    `json:"store_name"`  // 门店名称
	Status     int       `json:"status"`      // 状态 1-正常 0-禁用
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`  // 更新时间
}

// GetRedeemerByUserIDRequest 通过用户ID获取核销用户请求
type GetRedeemerByUserIDRequest struct {
	UserID uint `path:"user_id" binding:"required"` // 用户ID
}
