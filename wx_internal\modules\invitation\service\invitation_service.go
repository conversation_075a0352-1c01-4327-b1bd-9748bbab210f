package service

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"yekaitai/internal/service"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/invitation/model"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type InvitationService struct {
	db                *gorm.DB
	coinRewardService *service.CoinRewardService
}

func NewInvitationService() *InvitationService {
	return &InvitationService{
		db:                mysql.GetDB(),
		coinRewardService: service.NewCoinRewardService(),
	}
}

// GetInvitationCenter 获取邀请中心数据
func (s *InvitationService) GetInvitationCenter(ctx context.Context, userID uint, page, pageSize int) (*model.InvitationCenterResponse, int64, error) {
	// 1. 获取用户总邀请叶小币
	totalCoins, err := s.getUserTotalInvitationCoins(ctx, userID)
	if err != nil {
		logx.Errorf("获取用户邀请叶小币失败: %v", err)
		totalCoins = 0
	}

	// 2. 获取邀请成功总数
	var invitationCount int64
	err = s.db.WithContext(ctx).Model(&model.InvitationRecord{}).
		Where("inviter_id = ? AND status = ?", userID, model.InvitationStatusCompleted).
		Count(&invitationCount).Error
	if err != nil {
		logx.Errorf("获取邀请成功总数失败: %v", err)
		invitationCount = 0
	}

	// 3. 获取邀请奖励规则
	invitationRules, err := s.GetInvitationRules(ctx)
	if err != nil {
		logx.Errorf("获取邀请奖励规则失败: %v", err)
		invitationRules = []model.InvitationRule{}
	}

	// 4. 获取邀请记录列表（关联查询用户信息）
	var records []struct {
		model.InvitationRecord
		InviteeNickname string `json:"invitee_nickname"`
		InviteePhone    string `json:"invitee_phone"`
	}
	offset := (page - 1) * pageSize
	err = s.db.WithContext(ctx).Table("invitation_records ir").
		Select("ir.*, wu.nickname as invitee_nickname, wu.mobile as invitee_phone").
		Joins("LEFT JOIN wx_users wu ON ir.invitee_id = wu.id").
		Where("ir.inviter_id = ?", userID).
		Order("ir.created_at DESC").Offset(offset).Limit(pageSize).Scan(&records).Error
	if err != nil {
		return nil, 0, fmt.Errorf("查询邀请记录失败: %w", err)
	}

	// 5. 获取总记录数
	var total int64
	err = s.db.WithContext(ctx).Model(&model.InvitationRecord{}).
		Where("inviter_id = ?", userID).Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("统计邀请记录失败: %w", err)
	}

	// 6. 转换记录格式
	var recordInfos []model.InvitationRecordInfo
	for _, record := range records {
		var completedAt time.Time
		if record.InvitationRecord.CompletedAt != nil {
			completedAt = *record.InvitationRecord.CompletedAt
		}

		// 优先使用从wx_users表查询的信息，如果为空则使用邀请记录中的信息
		inviteeName := record.InviteeNickname
		if inviteeName == "" {
			inviteeName = record.InvitationRecord.InviteeName
		}

		inviteePhone := record.InviteePhone
		if inviteePhone == "" {
			inviteePhone = record.InvitationRecord.InviteePhone
		}

		recordInfos = append(recordInfos, model.InvitationRecordInfo{
			ID:           record.InvitationRecord.ID,
			InviteeName:  inviteeName,
			InviteePhone: s.maskPhone(inviteePhone),
			RewardType:   record.InvitationRecord.RewardType,
			CoinsAwarded: record.InvitationRecord.CoinsAwarded,
			CompletedAt:  completedAt,
			Status:       record.InvitationRecord.Status,
		})
	}

	// 7. 计算分页信息
	totalPages := (total + int64(pageSize) - 1) / int64(pageSize)
	hasMore := page < int(totalPages)

	return &model.InvitationCenterResponse{
		TotalCoins:      totalCoins,
		InvitationCount: int(invitationCount),
		InvitationRules: invitationRules,
		Records:         recordInfos,
		HasMore:         hasMore,
		CurrentPage:     page,
		TotalPages:      int(totalPages),
	}, total, nil
}

// CreateInvitation 创建邀请
func (s *InvitationService) CreateInvitation(ctx context.Context, userID uint) (*model.CreateInvitationResponse, error) {
	// 1. 生成邀请码
	invitationCode, err := s.generateInvitationCode()
	if err != nil {
		return nil, fmt.Errorf("生成邀请码失败: %w", err)
	}

	// 2. 检查邀请码是否已存在
	var existingRecord model.InvitationRecord
	err = s.db.WithContext(ctx).Where("invitation_code = ?", invitationCode).First(&existingRecord).Error
	if err == nil {
		// 如果邀请码已存在，重新生成
		return s.CreateInvitation(ctx, userID)
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("检查邀请码失败: %w", err)
	}

	// 3. 创建邀请记录
	invitationRecord := &model.InvitationRecord{
		InviterID:      userID,
		InvitationCode: invitationCode,
		Status:         model.InvitationStatusPending,
		CoinsAwarded:   0,
		RewardType:     "",
	}

	if err := s.db.WithContext(ctx).Create(invitationRecord).Error; err != nil {
		return nil, fmt.Errorf("创建邀请记录失败: %w", err)
	}

	// 4. 构建分享信息
	shareUrl := fmt.Sprintf("pages/index/index?invitation_code=%s", invitationCode)
	shareTitle := "叶开泰邀请您加入"
	shareDesc := "注册即可获得叶小币奖励，快来加入我们吧！"

	logx.Infof("创建邀请成功: userID=%d, invitationCode=%s", userID, invitationCode)

	return &model.CreateInvitationResponse{
		InvitationCode: invitationCode,
		ShareUrl:       shareUrl,
		ShareTitle:     shareTitle,
		ShareDesc:      shareDesc,
	}, nil
}

// AcceptInvitation 接受邀请
func (s *InvitationService) AcceptInvitation(ctx context.Context, userID uint, invitationCode string) (*model.AcceptInvitationResponse, error) {
	// 1. 查询邀请记录
	var invitationRecord model.InvitationRecord
	err := s.db.WithContext(ctx).Where("invitation_code = ?", invitationCode).First(&invitationRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("邀请码不存在或已失效")
		}
		return nil, fmt.Errorf("查询邀请记录失败: %w", err)
	}

	// 2. 检查邀请状态
	if invitationRecord.Status != model.InvitationStatusPending {
		return &model.AcceptInvitationResponse{
			Success:     false,
			Message:     "邀请码已使用或已失效",
			InviterName: "",
			Reward:      0,
		}, nil
	}

	// 3. 检查是否自己邀请自己
	if invitationRecord.InviterID == userID {
		return &model.AcceptInvitationResponse{
			Success:     false,
			Message:     "不能接受自己的邀请",
			InviterName: "",
			Reward:      0,
		}, nil
	}

	// 4. 检查用户是否已经被邀请过
	var existingInvitation model.InvitationRecord
	err = s.db.WithContext(ctx).Where("invitee_id = ? AND status = ?", userID, model.InvitationStatusCompleted).First(&existingInvitation).Error
	if err == nil {
		return &model.AcceptInvitationResponse{
			Success:     false,
			Message:     "您已经接受过邀请",
			InviterName: "",
			Reward:      0,
		}, nil
	}

	// 5. 获取邀请人信息
	var inviter struct {
		Nickname string `json:"nickname"`
	}
	err = s.db.WithContext(ctx).Table("wx_users").Select("nickname").
		Where("id = ?", invitationRecord.InviterID).First(&inviter).Error
	if err != nil {
		logx.Errorf("获取邀请人信息失败: %v", err)
		inviter.Nickname = "邀请人"
	}

	// 6. 获取用户信息
	var invitee struct {
		Nickname string `json:"nickname"`
		Phone    string `json:"phone"`
	}
	err = s.db.WithContext(ctx).Table("wx_users").Select("nickname, phone").
		Where("id = ?", userID).First(&invitee).Error
	if err != nil {
		logx.Errorf("获取被邀请人信息失败: %v", err)
		invitee.Nickname = "新用户"
		invitee.Phone = ""
	}

	// 7. 开始事务处理邀请
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 8. 更新邀请记录
	err = tx.Model(&invitationRecord).Updates(map[string]interface{}{
		"invitee_id":    userID,
		"status":        model.InvitationStatusCompleted,
		"invitee_name":  invitee.Nickname,
		"invitee_phone": invitee.Phone,
		"reward_type":   model.RewardTypeRegistration,
		"completed_at":  time.Now(),
		"updated_at":    time.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新邀请记录失败: %w", err)
	}

	// 9. 发放邀请奖励
	reward := 0
	err = s.coinRewardService.ProcessActivityReward(ctx, invitationRecord.InviterID, "INVITATION_REGISTER", fmt.Sprintf("%d", invitationRecord.ID))
	if err != nil {
		logx.Errorf("发放邀请奖励失败: %v", err)
		// 不回滚事务，继续处理
	} else {
		// 获取奖励金额
		reward = s.getInvitationReward("INVITATION_REGISTER")

		// 更新邀请记录的奖励金额
		tx.Model(&invitationRecord).Update("coins_awarded", reward)
	}

	// 10. 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交邀请事务失败: %w", err)
	}

	logx.Infof("接受邀请成功: userID=%d, inviterID=%d, invitationCode=%s, reward=%d",
		userID, invitationRecord.InviterID, invitationCode, reward)

	return &model.AcceptInvitationResponse{
		Success:     true,
		Message:     fmt.Sprintf("接受邀请成功！邀请人获得%d叶小币奖励", reward),
		InviterName: inviter.Nickname,
		Reward:      reward,
	}, nil
}

// GetInvitationStats 获取邀请统计
func (s *InvitationService) GetInvitationStats(ctx context.Context, userID uint) (*model.InvitationStatsResponse, error) {
	// 1. 获取总邀请数
	var totalInvitations int64
	err := s.db.WithContext(ctx).Model(&model.InvitationRecord{}).
		Where("inviter_id = ?", userID).Count(&totalInvitations).Error
	if err != nil {
		return nil, fmt.Errorf("查询总邀请数失败: %w", err)
	}

	// 2. 获取成功邀请数
	var successInvitations int64
	err = s.db.WithContext(ctx).Model(&model.InvitationRecord{}).
		Where("inviter_id = ? AND status = ?", userID, model.InvitationStatusCompleted).
		Count(&successInvitations).Error
	if err != nil {
		return nil, fmt.Errorf("查询成功邀请数失败: %w", err)
	}

	// 3. 获取总获得叶小币
	totalCoinsEarned, err := s.getUserTotalInvitationCoins(ctx, userID)
	if err != nil {
		logx.Errorf("获取总获得叶小币失败: %v", err)
		totalCoinsEarned = 0
	}

	// 4. 获取今日邀请数
	today := time.Now().Format("2006-01-02")
	var todayInvitations int64
	err = s.db.WithContext(ctx).Model(&model.InvitationRecord{}).
		Where("inviter_id = ? AND DATE(created_at) = ?", userID, today).
		Count(&todayInvitations).Error
	if err != nil {
		return nil, fmt.Errorf("查询今日邀请数失败: %w", err)
	}

	// 5. 获取本周邀请数
	weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday())).Format("2006-01-02")
	var weekInvitations int64
	err = s.db.WithContext(ctx).Model(&model.InvitationRecord{}).
		Where("inviter_id = ? AND DATE(created_at) >= ?", userID, weekStart).
		Count(&weekInvitations).Error
	if err != nil {
		return nil, fmt.Errorf("查询本周邀请数失败: %w", err)
	}

	// 6. 获取本月邀请数
	monthStart := time.Date(time.Now().Year(), time.Now().Month(), 1, 0, 0, 0, 0, time.Now().Location()).Format("2006-01-02")
	var monthInvitations int64
	err = s.db.WithContext(ctx).Model(&model.InvitationRecord{}).
		Where("inviter_id = ? AND DATE(created_at) >= ?", userID, monthStart).
		Count(&monthInvitations).Error
	if err != nil {
		return nil, fmt.Errorf("查询本月邀请数失败: %w", err)
	}

	return &model.InvitationStatsResponse{
		TotalInvitations:   int(totalInvitations),
		SuccessInvitations: int(successInvitations),
		TotalCoinsEarned:   totalCoinsEarned,
		TodayInvitations:   int(todayInvitations),
		WeekInvitations:    int(weekInvitations),
		MonthInvitations:   int(monthInvitations),
	}, nil
}

// GetInvitationRules 获取邀请奖励规则
func (s *InvitationService) GetInvitationRules(ctx context.Context) ([]model.InvitationRule, error) {
	// 从 coin_rules 表查询邀请相关规则
	var rules []struct {
		RuleType     string `json:"rule_type"`
		RuleName     string `json:"rule_name"`
		Description  string `json:"description"`
		CoinsAwarded int    `json:"coins_awarded"`
	}

	err := s.db.WithContext(ctx).Table("coin_rules").
		Select("rule_type, rule_name, description, coins_awarded").
		Where("rule_type IN (?, ?) AND enabled = ?", "INVITATION_REGISTER", "INVITATION_ORDER", true).
		Find(&rules).Error

	if err != nil {
		// 如果查询失败，返回默认规则
		return []model.InvitationRule{
			{
				RuleType:     "INVITATION_REGISTER",
				RuleName:     "邀请新人注册",
				Description:  "邀请新用户注册成功获得叶小币奖励",
				CoinsAwarded: 200,
			},
			{
				RuleType:     "INVITATION_ORDER",
				RuleName:     "邀请新人首单",
				Description:  "被邀请人完成首单获得叶小币奖励",
				CoinsAwarded: 50,
			},
		}, nil
	}

	// 转换为响应格式
	var invitationRules []model.InvitationRule
	for _, rule := range rules {
		invitationRules = append(invitationRules, model.InvitationRule{
			RuleType:     rule.RuleType,
			RuleName:     rule.RuleName,
			Description:  rule.Description,
			CoinsAwarded: rule.CoinsAwarded,
		})
	}

	// 如果没有规则，返回默认规则
	if len(invitationRules) == 0 {
		return []model.InvitationRule{
			{
				RuleType:     "INVITATION_REGISTER",
				RuleName:     "邀请新人注册",
				Description:  "邀请新用户注册成功获得叶小币奖励",
				CoinsAwarded: 200,
			},
			{
				RuleType:     "INVITATION_ORDER",
				RuleName:     "邀请新人首单",
				Description:  "被邀请人完成首单获得叶小币奖励",
				CoinsAwarded: 50,
			},
		}, nil
	}

	return invitationRules, nil
}

// ProcessInvitationOrderReward 处理邀请首单奖励
func (s *InvitationService) ProcessInvitationOrderReward(ctx context.Context, userID uint, orderNo string) error {
	// 1. 查询用户是否是被邀请人
	var invitationRecord model.InvitationRecord
	err := s.db.WithContext(ctx).Where("invitee_id = ? AND status = ?", userID, model.InvitationStatusCompleted).First(&invitationRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户不是被邀请人，无需处理
			return nil
		}
		return fmt.Errorf("查询邀请记录失败: %w", err)
	}

	// 2. 检查是否已经处理过首单奖励
	var existingReward model.InvitationRecord
	err = s.db.WithContext(ctx).Where("inviter_id = ? AND invitee_id = ? AND reward_type = ?",
		invitationRecord.InviterID, userID, model.RewardTypeFirstOrder).First(&existingReward).Error
	if err == nil {
		// 已经处理过首单奖励
		return nil
	}

	// 3. 发放邀请首单奖励
	err = s.coinRewardService.ProcessActivityReward(ctx, invitationRecord.InviterID, "INVITATION_ORDER", orderNo)
	if err != nil {
		return fmt.Errorf("发放邀请首单奖励失败: %w", err)
	}

	// 4. 创建首单奖励记录
	orderReward := &model.InvitationRecord{
		InviterID:      invitationRecord.InviterID,
		InviteeID:      userID,
		InvitationCode: invitationRecord.InvitationCode,
		Status:         model.InvitationStatusCompleted,
		InviteeName:    invitationRecord.InviteeName,
		InviteePhone:   invitationRecord.InviteePhone,
		CoinsAwarded:   s.getInvitationReward("INVITATION_ORDER"),
		RewardType:     model.RewardTypeFirstOrder,
		CompletedAt:    &[]time.Time{time.Now()}[0],
	}

	if err := s.db.WithContext(ctx).Create(orderReward).Error; err != nil {
		logx.Errorf("创建首单奖励记录失败: %v", err)
		// 不返回错误，因为奖励已经发放
	}

	logx.Infof("处理邀请首单奖励成功: inviterID=%d, inviteeID=%d, orderNo=%s",
		invitationRecord.InviterID, userID, orderNo)

	return nil
}

// 辅助方法

// getUserTotalInvitationCoins 获取用户总邀请叶小币
func (s *InvitationService) getUserTotalInvitationCoins(ctx context.Context, userID uint) (int, error) {
	var totalCoins struct {
		TotalCoins int `json:"total_coins"`
	}

	err := s.db.WithContext(ctx).Table("invitation_records").
		Select("COALESCE(SUM(coins_awarded), 0) as total_coins").
		Where("inviter_id = ? AND status = ?", userID, model.InvitationStatusCompleted).
		First(&totalCoins).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, fmt.Errorf("查询用户邀请叶小币失败: %w", err)
	}

	return totalCoins.TotalCoins, nil
}

// maskPhone 手机号脱敏
func (s *InvitationService) maskPhone(phone string) string {
	if len(phone) < 7 {
		return phone
	}
	return phone[:3] + "****" + phone[len(phone)-4:]
}

// generateInvitationCode 生成邀请码
func (s *InvitationService) generateInvitationCode() (string, error) {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 8

	code := make([]byte, length)
	for i := range code {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		code[i] = charset[num.Int64()]
	}

	return string(code), nil
}

// getInvitationReward 获取邀请奖励金额
func (s *InvitationService) getInvitationReward(ruleType string) int {
	var rule struct {
		CoinsAwarded int `json:"coins_awarded"`
	}

	err := s.db.Table("coin_rules").Select("coins_awarded").
		Where("rule_type = ? AND enabled = ?", ruleType, true).
		First(&rule).Error

	if err != nil {
		// 返回默认值
		switch ruleType {
		case "INVITATION_REGISTER":
			return 200
		case "INVITATION_ORDER":
			return 50
		default:
			return 0
		}
	}

	return rule.CoinsAwarded
}
