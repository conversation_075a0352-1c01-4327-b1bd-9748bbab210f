package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// LogConfig 是日志配置结构体
type LogConfig struct {
	ServiceName      string   `json:",optional"`
	Level            string   `json:",optional"`
	Encoding         string   `json:",optional"`
	OutputPaths      []string `json:",optional"`
	ErrorOutputPaths []string `json:",optional"`
	// 新增的日志轮转配置
	MaxSize     int  `json:",optional"` // 单个日志文件最大大小(MB)，0表示不限制
	MaxAge      int  `json:",optional"` // 日志文件保留天数
	Compress    bool `json:",optional"` // 是否压缩旧日志文件
	LocalTime   bool `json:",optional"` // 是否使用本地时间
	RotateDaily bool `json:",optional"` // 是否按天切割
	Console     bool `json:",optional"` // 是否输出到控制台
}

var (
	logger *zap.Logger
	once   sync.Once
)

// DailyRotateWriter 按天轮转的日志写入器
type DailyRotateWriter struct {
	filename    string
	currentDate string
	file        *os.File
	maxAge      int
	compress    bool
	localTime   bool
	mu          sync.Mutex
}

// NewDailyRotateWriter 创建按天轮转的写入器
func NewDailyRotateWriter(filename string, maxAge int, compress bool, localTime bool) *DailyRotateWriter {
	return &DailyRotateWriter{
		filename:  filename,
		maxAge:    maxAge,
		compress:  compress,
		localTime: localTime,
	}
}

// Write 实现io.Writer接口
func (w *DailyRotateWriter) Write(p []byte) (n int, err error) {
	w.mu.Lock()
	defer w.mu.Unlock()

	now := time.Now()
	if w.localTime {
		now = now.Local()
	} else {
		now = now.UTC()
	}

	currentDate := now.Format("2006-01-02")

	// 如果日期变了或者文件还没打开，需要轮转
	if w.currentDate != currentDate || w.file == nil {
		if err := w.rotate(currentDate); err != nil {
			return 0, err
		}
	}

	return w.file.Write(p)
}

// rotate 执行日志轮转
func (w *DailyRotateWriter) rotate(newDate string) error {
	// 关闭当前文件
	if w.file != nil {
		w.file.Close()
	}

	// 确保目录存在
	dir := filepath.Dir(w.filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// 如果有旧文件，重命名为带日期的文件
	if w.currentDate != "" && w.currentDate != newDate {
		oldName := w.filename
		newName := fmt.Sprintf("%s.%s", w.filename, w.currentDate)

		// 重命名旧文件
		if _, err := os.Stat(oldName); err == nil {
			os.Rename(oldName, newName)

			// 如果需要压缩
			if w.compress {
				go w.compressFile(newName)
			}
		}
	}

	// 打开新文件
	file, err := os.OpenFile(w.filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}

	w.file = file
	w.currentDate = newDate

	// 清理过期文件
	if w.maxAge > 0 {
		go w.cleanOldFiles()
	}

	return nil
}

// compressFile 压缩文件
func (w *DailyRotateWriter) compressFile(filename string) {
	// 这里可以实现压缩逻辑，暂时跳过
	// 可以使用gzip等压缩算法
}

// cleanOldFiles 清理过期文件
func (w *DailyRotateWriter) cleanOldFiles() {
	if w.maxAge <= 0 {
		return
	}

	dir := filepath.Dir(w.filename)
	base := filepath.Base(w.filename)

	cutoff := time.Now().AddDate(0, 0, -w.maxAge)
	if w.localTime {
		cutoff = cutoff.Local()
	} else {
		cutoff = cutoff.UTC()
	}

	filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		// 检查是否是我们的日志文件
		if filepath.Base(path) == base {
			return nil // 跳过当前文件
		}

		// 检查是否是带日期的日志文件
		if len(filepath.Base(path)) > len(base)+1 &&
			filepath.Base(path)[:len(base)+1] == base+"." {

			if info.ModTime().Before(cutoff) {
				os.Remove(path)
			}
		}

		return nil
	})
}

// Close 关闭写入器
func (w *DailyRotateWriter) Close() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.file != nil {
		return w.file.Close()
	}
	return nil
}

// InitLogger 初始化日志配置
func InitLogger(cfg LogConfig) {
	once.Do(func() {
		// 设置日志级别
		level := zap.InfoLevel
		if cfg.Level != "" {
			switch cfg.Level {
			case "debug":
				level = zap.DebugLevel
			case "info":
				level = zap.InfoLevel
			case "warn":
				level = zap.WarnLevel
			case "error":
				level = zap.ErrorLevel
			}
		}

		// 配置编码器
		encoderConfig := zapcore.EncoderConfig{
			TimeKey:        "time",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		}

		// 编码方式
		encoding := "json"
		if cfg.Encoding != "" {
			encoding = cfg.Encoding
		}

		// 创建编码器
		var encoder zapcore.Encoder
		if encoding == "json" {
			encoder = zapcore.NewJSONEncoder(encoderConfig)
		} else {
			encoder = zapcore.NewConsoleEncoder(encoderConfig)
		}

		// 创建写入器
		var writers []zapcore.WriteSyncer

		// 文件输出
		outputPaths := []string{"logs/app.log"}
		if len(cfg.OutputPaths) > 0 {
			outputPaths = cfg.OutputPaths
		}

		for _, path := range outputPaths {
			if cfg.RotateDaily {
				// 使用按天轮转
				rotateWriter := NewDailyRotateWriter(path, cfg.MaxAge, cfg.Compress, cfg.LocalTime)
				writers = append(writers, zapcore.AddSync(rotateWriter))
			} else {
				// 普通文件输出
				file, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
				if err == nil {
					writers = append(writers, zapcore.AddSync(file))
				}
			}
		}

		// 控制台输出
		if cfg.Console {
			writers = append(writers, zapcore.AddSync(os.Stdout))
		}

		// 错误日志输出
		errorOutputPaths := []string{"logs/error.log"}
		if len(cfg.ErrorOutputPaths) > 0 {
			errorOutputPaths = cfg.ErrorOutputPaths
		}

		var errorWriters []zapcore.WriteSyncer
		for _, path := range errorOutputPaths {
			if cfg.RotateDaily {
				// 使用按天轮转
				rotateWriter := NewDailyRotateWriter(path, cfg.MaxAge, cfg.Compress, cfg.LocalTime)
				errorWriters = append(errorWriters, zapcore.AddSync(rotateWriter))
			} else {
				// 普通文件输出
				file, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
				if err == nil {
					errorWriters = append(errorWriters, zapcore.AddSync(file))
				}
			}
		}

		// 控制台错误输出
		if cfg.Console {
			errorWriters = append(errorWriters, zapcore.AddSync(os.Stderr))
		}

		// 创建核心
		var cores []zapcore.Core

		// 普通日志核心
		if len(writers) > 0 {
			core := zapcore.NewCore(
				encoder,
				zapcore.NewMultiWriteSyncer(writers...),
				level,
			)
			cores = append(cores, core)
		}

		// 错误日志核心 (只记录错误级别)
		if len(errorWriters) > 0 {
			errorCore := zapcore.NewCore(
				encoder,
				zapcore.NewMultiWriteSyncer(errorWriters...),
				zap.ErrorLevel,
			)
			cores = append(cores, errorCore)
		}

		// 创建logger
		var core zapcore.Core
		if len(cores) > 1 {
			core = zapcore.NewTee(cores...)
		} else if len(cores) == 1 {
			core = cores[0]
		} else {
			// 默认输出到控制台
			core = zapcore.NewCore(
				encoder,
				zapcore.AddSync(os.Stdout),
				level,
			)
		}

		logger = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zap.ErrorLevel))
		if cfg.ServiceName != "" {
			logger = logger.With(zap.String("service", cfg.ServiceName))
		}
	})
}

// Sync 同步日志
func Sync() {
	if logger != nil {
		logger.Sync()
	}
}

// Debug 调试日志
func Debug(msg string, fields ...zap.Field) {
	if logger != nil {
		logx.Info(msg)
	}
}

// Info 信息日志
func Info(msg string, fields ...zap.Field) {
	if logger != nil {
		logx.Info(msg)
	}
}

// Warn 警告日志
func Warn(msg string, fields ...zap.Field) {
	if logger != nil {
		logx.Error(msg)
	}
}

// Error 错误日志
func Error(msg string, fields ...zap.Field) {
	if logger != nil {
		logx.Error(msg)
	}
}

// Fatal 致命错误日志
func Fatal(msg string, fields ...zap.Field) {
	if logger != nil {
		logger.Fatal(msg, fields...)
	}
}
