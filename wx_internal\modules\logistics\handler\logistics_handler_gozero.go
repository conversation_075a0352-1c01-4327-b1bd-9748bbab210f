package handler

import (
	"net/http"
	"strconv"
	"strings"

	"yekaitai/internal/types"
	"yekaitai/pkg/adapters/wanliniu"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type LogisticsGoZeroHandler struct {
	wanliniu *wanliniu.Client
}

func NewLogisticsGoZeroHandler() *LogisticsGoZeroHandler {
	config := wanliniu.GetDefaultConfig()
	// TODO: 从配置文件或环境变量中获取实际的AppKey和Secret
	config.AppKey = "your-app-key"
	config.AppSecret = "your-secret"

	return &LogisticsGoZeroHandler{
		wanliniu: wanliniu.NewClient(config),
	}
}

// LogisticsTraceRequest 物流轨迹查询请求
type LogisticsTraceRequest struct {
	ExpressNo string `form:"express_no" validate:"required"` // 快递单号
}

// QueryLogisticsTrace 查询物流轨迹
func (h *LogisticsGoZeroHandler) QueryLogisticsTrace(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID（验证用户身份）
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 解析请求参数
	var req LogisticsTraceRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	if req.ExpressNo == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "快递单号不能为空"))
		return
	}

	// TODO: 验证用户是否有权限查询该快递单号（检查订单归属）

	// 调用万里牛接口查询物流轨迹
	wlnReq := &wanliniu.LogisticsTraceRequest{
		ExpressNos: req.ExpressNo,
		Page:       1,
		Limit:      50,
	}

	resp, err := h.wanliniu.QueryLogisticsTrace(r.Context(), wlnReq)
	if err != nil {
		logx.Errorf("查询物流轨迹失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询物流信息失败"))
		return
	}

	// 检查响应结果
	if resp.Code != 0 {
		logx.Errorf("万里牛API返回错误: %d", resp.Code)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询物流信息失败"))
		return
	}

	// 构建响应
	var traces []map[string]interface{}
	if len(resp.Data) > 0 && len(resp.Data[0].Traces) > 0 {
		for _, trace := range resp.Data[0].Traces {
			traces = append(traces, map[string]interface{}{
				"time":        trace.AcceptTime,
				"description": trace.AcceptStation,
				"remark":      trace.Remark,
			})
		}
	}

	result := map[string]interface{}{
		"express_no": req.ExpressNo,
		"traces":     traces,
	}

	logx.Infof("用户 %d 查询物流轨迹: %s", userID, req.ExpressNo)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// QueryOrderLogistics 查询订单物流信息
func (h *LogisticsGoZeroHandler) QueryOrderLogistics(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 从URL路径中获取订单ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderIDStr := parts[len(parts)-2] // /orders/:id/logistics
	_, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的订单ID"))
		return
	}

	// TODO: 根据订单ID查询订单信息，获取快递单号，验证用户权限
	// 这里需要调用订单服务获取订单详情和快递单号

	logx.Infof("用户 %d 查询订单物流: %s", userID, orderIDStr)
	httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "功能正在开发中"))
}
