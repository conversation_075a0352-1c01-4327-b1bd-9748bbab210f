package upload

import (
	"context"
	"fmt"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// UploadType 上传类型
type UploadType string

const (
	// UploadTypeImage 图片上传
	UploadTypeImage UploadType = "image"
	// UploadTypeDocument 文档上传
	UploadTypeDocument UploadType = "document"
	// UploadTypeVideo 视频上传
	UploadTypeVideo UploadType = "video"
	// UploadTypeGeneral 通用上传
	UploadTypeGeneral UploadType = "general"

	// DefaultImagePath 默认图片存储路径
	DefaultImagePath = "images"
	// DefaultDocumentPath 默认文档存储路径
	DefaultDocumentPath = "documents"
	// DefaultVideoPath 默认视频存储路径
	DefaultVideoPath = "videos"
	// DefaultGeneralPath 默认通用存储路径
	DefaultGeneralPath = "files"

	// 默认有效期 (24小时)
	defaultExpireTime = 24 * time.Hour
)

// UploadResult 上传结果
type UploadResult struct {
	URL       string    `json:"url"`        // 文件URL
	Key       string    `json:"key"`        // 存储Key
	Size      int64     `json:"size"`       // 文件大小
	Filename  string    `json:"filename"`   // 原始文件名
	Extension string    `json:"extension"`  // 文件扩展名
	MimeType  string    `json:"mime_type"`  // MIME类型
	CreatedAt time.Time `json:"created_at"` // 创建时间
}

// Service 上传服务接口
type Service interface {
	UploadFile(ctx context.Context, file *multipart.FileHeader, uploadType UploadType, customPath string) (*UploadResult, error)
	UploadImage(ctx context.Context, file *multipart.FileHeader, customPath string) (*UploadResult, error)
	UploadDocument(ctx context.Context, file *multipart.FileHeader, customPath string) (*UploadResult, error)
	UploadVideo(ctx context.Context, file *multipart.FileHeader, customPath string) (*UploadResult, error)
}

// serviceImpl 上传服务实现
type serviceImpl struct {
	uploader *QiniuUploader
	config   UploadConfig
}

// NewService 创建上传服务
func NewService(uploader *QiniuUploader) Service {
	logx.Info("创建上传服务")
	return &serviceImpl{
		uploader: uploader,
		config:   GetUploadConfig(),
	}
}

// DefaultService 默认上传服务
var DefaultService Service

// InitDefaultService 初始化默认上传服务
func InitDefaultService() {
	logx.Info("初始化默认上传服务")
	if DefaultQiniuUploader == nil {
		InitDefaultQiniuUploader()
	}
	DefaultService = NewService(DefaultQiniuUploader)
}

// UploadFile 上传文件
func (s *serviceImpl) UploadFile(ctx context.Context, file *multipart.FileHeader, uploadType UploadType, customPath string) (*UploadResult, error) {
	// 根据上传类型确定路径和验证方法
	var path string
	var err error
	var maxSize int64

	logx.Infof("开始处理文件上传请求: %s, 类型: %s", file.Filename, uploadType)

	switch uploadType {
	case UploadTypeImage:
		if err = CheckImageType(file); err != nil {
			return nil, err
		}
		path = DefaultImagePath
		maxSize = s.config.MaxImageSize
		if maxSize == 0 {
			maxSize = DefaultMaxImageSize
		}
	case UploadTypeDocument:
		if err = CheckDocumentType(file); err != nil {
			return nil, err
		}
		path = DefaultDocumentPath
		maxSize = s.config.MaxDocumentSize
		if maxSize == 0 {
			maxSize = DefaultMaxDocumentSize
		}
	case UploadTypeVideo:
		if err = CheckVideoType(file); err != nil {
			return nil, err
		}
		path = DefaultVideoPath
		maxSize = s.config.MaxVideoSize
		if maxSize == 0 {
			maxSize = DefaultMaxVideoSize
		}
	case UploadTypeGeneral:
		path = DefaultGeneralPath
		maxSize = s.config.MaxGeneralSize
		if maxSize == 0 {
			maxSize = DefaultMaxGeneralSize
		}
	default:
		logx.Errorf("不支持的上传类型: %s", uploadType)
		return nil, fmt.Errorf("不支持的上传类型: %s", uploadType)
	}

	// 检查文件大小
	if err := CheckFileSize(file, maxSize); err != nil {
		return nil, err
	}

	// 使用自定义路径
	if customPath != "" {
		path = customPath
		logx.Infof("使用自定义路径: %s", customPath)
	}

	// 生成唯一文件名
	key := fmt.Sprintf("%s/%d-%s%s",
		path,
		time.Now().UnixNano(),
		RandomString(8),
		filepath.Ext(file.Filename),
	)
	logx.Infof("生成文件存储路径: %s", key)

	// 上传文件
	url, err := s.uploader.UploadFile(ctx, file, key)
	if err != nil {
		logx.Errorf("上传文件失败: %v", err)
		return nil, err
	}

	// 构建上传结果
	result := &UploadResult{
		URL:       url,
		Key:       key,
		Size:      file.Size,
		Filename:  file.Filename,
		Extension: strings.ToLower(filepath.Ext(file.Filename)),
		MimeType:  file.Header.Get("Content-Type"),
		CreatedAt: time.Now(),
	}

	logx.Infof("文件上传成功: %s -> %s", file.Filename, result.URL)
	return result, nil
}

// UploadImage 上传图片
func (s *serviceImpl) UploadImage(ctx context.Context, file *multipart.FileHeader, customPath string) (*UploadResult, error) {
	logx.Infof("处理图片上传: %s", file.Filename)
	return s.UploadFile(ctx, file, UploadTypeImage, customPath)
}

// UploadDocument 上传文档
func (s *serviceImpl) UploadDocument(ctx context.Context, file *multipart.FileHeader, customPath string) (*UploadResult, error) {
	logx.Infof("处理文档上传: %s", file.Filename)
	return s.UploadFile(ctx, file, UploadTypeDocument, customPath)
}

// UploadVideo 上传视频
func (s *serviceImpl) UploadVideo(ctx context.Context, file *multipart.FileHeader, customPath string) (*UploadResult, error) {
	logx.Infof("处理视频上传: %s", file.Filename)
	return s.UploadFile(ctx, file, UploadTypeVideo, customPath)
}

// 初始化默认服务
func init() {
	Init()
	InitDefaultService()
}
