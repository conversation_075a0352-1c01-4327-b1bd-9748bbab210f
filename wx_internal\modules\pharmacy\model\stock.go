package model

import (
	"time"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// Stock 库存模型
type Stock struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
	MedicineID   uint           `gorm:"index" json:"medicine_id"`  // 药品ID
	WarehouseID  uint           `gorm:"index" json:"warehouse_id"` // 仓库ID
	Quantity     int            `json:"quantity"`                  // 库存数量
	LockQuantity int            `json:"lock_quantity"`             // 锁定库存
	BatchNo      string         `gorm:"size:50" json:"batch_no"`   // 批次号
	ExpiryDate   time.Time      `json:"expiry_date"`               // 有效期
	Status       int            `gorm:"default:1" json:"status"`   // 状态 1:正常 0:禁用
}

// TableName 设置表名
func (Stock) TableName() string {
	return "stocks"
}

// StockRepository 库存仓库接口
type StockRepository interface {
	Create(stock *Stock) error
	Update(stock *Stock) error
	Delete(id uint) error
	FindByID(id uint) (*Stock, error)
	FindByMedicineAndWarehouse(medicineID, warehouseID uint) (*Stock, error)
	List(page, size int) ([]*Stock, int64, error)
	ListByMedicine(medicineID uint) ([]*Stock, error)
	UpdateQuantity(id uint, quantity int) error
	LockStock(id uint, quantity int) error
	UnlockStock(id uint, quantity int) error
}

// stockRepository 库存仓库实现
type stockRepository struct{}

// NewStockRepository 创建库存仓库
func NewStockRepository() StockRepository {
	return &stockRepository{}
}

// Create 创建库存
func (r *stockRepository) Create(stock *Stock) error {
	return mysql.Master().Create(stock).Error
}

// Update 更新库存
func (r *stockRepository) Update(stock *Stock) error {
	return mysql.Master().Save(stock).Error
}

// Delete 删除库存
func (r *stockRepository) Delete(id uint) error {
	return mysql.Master().Delete(&Stock{}, id).Error
}

// FindByID 根据ID查询库存
func (r *stockRepository) FindByID(id uint) (*Stock, error) {
	var stock Stock
	err := mysql.Slave().Where("id = ?", id).First(&stock).Error
	if err != nil {
		return nil, err
	}
	return &stock, nil
}

// FindByMedicineAndWarehouse 根据药品ID和仓库ID查询库存
func (r *stockRepository) FindByMedicineAndWarehouse(medicineID, warehouseID uint) (*Stock, error) {
	var stock Stock
	err := mysql.Slave().Where("medicine_id = ? AND warehouse_id = ?", medicineID, warehouseID).First(&stock).Error
	if err != nil {
		return nil, err
	}
	return &stock, nil
}

// List 查询库存列表
func (r *stockRepository) List(page, size int) ([]*Stock, int64, error) {
	var stocks []*Stock
	var count int64

	offset := (page - 1) * size

	err := mysql.Slave().Model(&Stock{}).
		Count(&count).
		Order("id DESC").
		Offset(offset).
		Limit(size).
		Find(&stocks).Error

	return stocks, count, err
}

// ListByMedicine 根据药品ID查询库存列表
func (r *stockRepository) ListByMedicine(medicineID uint) ([]*Stock, error) {
	var stocks []*Stock
	err := mysql.Slave().Where("medicine_id = ?", medicineID).Find(&stocks).Error
	return stocks, err
}

// UpdateQuantity 更新库存数量
func (r *stockRepository) UpdateQuantity(id uint, quantity int) error {
	return mysql.Master().Model(&Stock{}).Where("id = ?", id).Update("quantity", quantity).Error
}

// LockStock 锁定库存
func (r *stockRepository) LockStock(id uint, quantity int) error {
	tx := mysql.Master().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var stock Stock
	if err := tx.Where("id = ?", id).First(&stock).Error; err != nil {
		tx.Rollback()
		return err
	}

	if stock.Quantity-stock.LockQuantity < quantity {
		tx.Rollback()
		return gorm.ErrRecordNotFound
	}

	if err := tx.Model(&stock).Update("lock_quantity", stock.LockQuantity+quantity).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// UnlockStock 解锁库存
func (r *stockRepository) UnlockStock(id uint, quantity int) error {
	tx := mysql.Master().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var stock Stock
	if err := tx.Where("id = ?", id).First(&stock).Error; err != nil {
		tx.Rollback()
		return err
	}

	if stock.LockQuantity < quantity {
		quantity = stock.LockQuantity
	}

	if err := tx.Model(&stock).Update("lock_quantity", stock.LockQuantity-quantity).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
