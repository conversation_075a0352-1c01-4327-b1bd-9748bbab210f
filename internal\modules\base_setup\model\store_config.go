package model

import (
	"time"
)

// StoreConfig 店铺配置
type StoreConfig struct {
	ID                   uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:配置ID"`
	StoreName            string    `json:"store_name" gorm:"type:varchar(100);comment:店铺名称"`
	StoreDescription     string    `json:"store_description" gorm:"type:text;comment:店铺介绍"`
	StoreAddress         string    `json:"store_address" gorm:"type:varchar(255);comment:店铺地址"`
	StorePhone           string    `json:"store_phone" gorm:"type:varchar(20);comment:店铺电话"`
	ProvinceID           string    `json:"province_id" gorm:"type:varchar(20);comment:省份编码"`
	CityID               string    `json:"city_id" gorm:"type:varchar(20);comment:城市编码"`
	AreaID               string    `json:"area_id" gorm:"type:varchar(20);comment:区县编码"`
	BusinessLicense      string    `json:"business_license" gorm:"type:varchar(255);comment:营业执照图片"`
	MedicalLicense       string    `json:"medical_license" gorm:"type:varchar(255);comment:药品经营许可证图片"`
	InternetLicense      string    `json:"internet_license" gorm:"type:varchar(255);comment:医联器械经营许可证图片"`
	SecondClassLicense   string    `json:"second_class_license" gorm:"type:varchar(255);comment:第二类医疗器械经营备案凭证图片"`
	ThirdClassLicense    string    `json:"third_class_license" gorm:"type:varchar(255);comment:第三类医疗器械经营许可证图片"`
	InternetSalesLicense string    `json:"internet_sales_license" gorm:"type:varchar(255);comment:医疗器械网络销售备案证明图片"`
	LogoImage            string    `json:"logo_image" gorm:"type:varchar(255);comment:店铺logo"`
	Status               int       `json:"status" gorm:"default:1;comment:状态(0-禁用,1-启用)"`
	CreatedAt            time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt            time.Time `json:"updated_at" gorm:"comment:更新时间"`
}

// TableName 表名
func (StoreConfig) TableName() string {
	return "store_config"
}

// StoreConfigCreateRequest 创建店铺配置请求
type StoreConfigCreateRequest struct {
	StoreName            string `json:"store_name,optional" validate:"max=100"`
	StoreDescription     string `json:"store_description,optional"`
	StoreAddress         string `json:"store_address,optional" validate:"max=255"`
	StorePhone           string `json:"store_phone,optional" validate:"max=20"`
	ProvinceID           string `json:"province_id,optional" validate:"max=20"`
	CityID               string `json:"city_id,optional" validate:"max=20"`
	AreaID               string `json:"area_id,optional" validate:"max=20"`
	BusinessLicense      string `json:"business_license,optional"`
	MedicalLicense       string `json:"medical_license,optional"`
	InternetLicense      string `json:"internet_license,optional"`
	SecondClassLicense   string `json:"second_class_license,optional"`
	ThirdClassLicense    string `json:"third_class_license,optional"`
	InternetSalesLicense string `json:"internet_sales_license,optional"`
	LogoImage            string `json:"logo_image,optional"`
	Status               int    `json:"status,optional" validate:"oneof=0 1"`
}

// StoreConfigUpdateRequest 更新店铺配置请求
type StoreConfigUpdateRequest struct {
	StoreName            string `json:"store_name,optional" validate:"max=100"`
	StoreDescription     string `json:"store_description,optional"`
	StoreAddress         string `json:"store_address,optional" validate:"max=255"`
	StorePhone           string `json:"store_phone,optional" validate:"max=20"`
	ProvinceID           string `json:"province_id,optional" validate:"max=20"`
	CityID               string `json:"city_id,optional" validate:"max=20"`
	AreaID               string `json:"area_id,optional" validate:"max=20"`
	BusinessLicense      string `json:"business_license,optional"`
	MedicalLicense       string `json:"medical_license,optional"`
	InternetLicense      string `json:"internet_license,optional"`
	SecondClassLicense   string `json:"second_class_license,optional"`
	ThirdClassLicense    string `json:"third_class_license,optional"`
	InternetSalesLicense string `json:"internet_sales_license,optional"`
	LogoImage            string `json:"logo_image,optional"`
	Status               int    `json:"status,optional" validate:"oneof=0 1"`
}

// StoreConfigResponse 店铺配置响应
type StoreConfigResponse struct {
	ID                   uint      `json:"id"`
	StoreName            string    `json:"store_name"`
	StoreDescription     string    `json:"store_description"`
	StoreAddress         string    `json:"store_address"`
	StorePhone           string    `json:"store_phone"`
	ProvinceID           string    `json:"province_id"`
	CityID               string    `json:"city_id"`
	AreaID               string    `json:"area_id"`
	BusinessLicense      string    `json:"business_license"`
	MedicalLicense       string    `json:"medical_license"`
	InternetLicense      string    `json:"internet_license"`
	SecondClassLicense   string    `json:"second_class_license"`
	ThirdClassLicense    string    `json:"third_class_license"`
	InternetSalesLicense string    `json:"internet_sales_license"`
	LogoImage            string    `json:"logo_image"`
	Status               int       `json:"status"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}
