package patient

import (
	"time"

	"gorm.io/gorm"
)

// SyncProgress 同步进度记录模型
type SyncProgress struct {
	ID       uint   `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	SyncType string `gorm:"column:sync_type;type:varchar(50);index;comment:同步类型" json:"sync_type"`
	SyncDate string `gorm:"column:sync_date;type:date;index;comment:同步日期" json:"sync_date"`
	Status   int    `gorm:"column:status;comment:同步状态 1:成功 0:失败" json:"status"`
	Records  int    `gorm:"column:records;comment:同步记录数" json:"records"`
	Message  string `gorm:"column:message;type:text;comment:同步消息" json:"message"`

	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (SyncProgress) TableName() string {
	return "sync_progress"
}
