package model

import (
	"database/sql"
	"time"

	"gorm.io/gorm"
)

// Region 行政区域模型
type Region struct {
	ID         int64        `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	Code       string       `json:"code" gorm:"type:varchar(20);uniqueIndex;comment:行政区划代码"`                // 行政区划代码
	Name       string       `json:"name" gorm:"type:varchar(50);not null;comment:名称"`                       // 名称
	ParentCode string       `json:"parent_code" gorm:"type:varchar(20);index;comment:父级行政区划代码"`             // 父级行政区划代码
	Level      int          `json:"level" gorm:"type:tinyint;not null;comment:级别:1-省级 2-地级 3-县级 4-乡级 5-村级"` // 级别: 1-省级 2-地级 3-县级 4-乡级 5-村级
	CreatedAt  time.Time    `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt  time.Time    `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt  sql.NullTime `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (r *Region) TableName() string {
	return "t_regions"
}

// RegionTree 地区树形结构
type RegionTree struct {
	Region   *Region       `json:"region"`
	Children []*RegionTree `json:"children,omitempty"`
}

// RegionRepository 行政区域仓库接口
type RegionRepository interface {
	// 创建行政区域
	Create(region *Region) error
	// 批量创建行政区域
	BatchCreate(regions []*Region) error
	// 根据编码查找行政区域
	FindByCode(code string) (*Region, error)
	// 获取所有省份
	GetProvinces() ([]*Region, error)
	// 获取指定省份的所有城市
	GetCities(provinceCode string) ([]*Region, error)
	// 获取指定城市的所有区县
	GetAreas(cityCode string) ([]*Region, error)
	// 根据编码删除行政区域
	DeleteByCode(code string) error
	// 清空表
	TruncateTable() error
	// 获取指定区县的所有乡镇
	GetTowns(areaCode string) ([]*Region, error)
	// 获取指定乡镇的所有村街道
	GetVillages(townCode string) ([]*Region, error)
}

// regionRepository 实现了RegionRepository接口
type regionRepository struct {
	db *gorm.DB
}

// NewRegionRepository 创建行政区域仓库实例
func NewRegionRepository(db *gorm.DB) RegionRepository {
	return &regionRepository{db: db}
}

// Create 创建行政区域
func (r *regionRepository) Create(region *Region) error {
	return r.db.Create(region).Error
}

// BatchCreate 批量创建行政区域
func (r *regionRepository) BatchCreate(regions []*Region) error {
	return r.db.CreateInBatches(regions, 100).Error
}

// FindByCode 根据编码查找行政区域
func (r *regionRepository) FindByCode(code string) (*Region, error) {
	var region Region
	err := r.db.Where("code = ?", code).First(&region).Error
	if err != nil {
		return nil, err
	}
	return &region, nil
}

// GetProvinces 获取所有省份
func (r *regionRepository) GetProvinces() ([]*Region, error) {
	var provinces []*Region
	err := r.db.Where("level = ?", 1).Find(&provinces).Error
	return provinces, err
}

// GetCities 获取指定省份的所有城市
func (r *regionRepository) GetCities(provinceCode string) ([]*Region, error) {
	var cities []*Region
	err := r.db.Where("parent_code = ? AND level = ?", provinceCode, 2).Find(&cities).Error
	return cities, err
}

// GetAreas 获取指定城市的所有区县
func (r *regionRepository) GetAreas(cityCode string) ([]*Region, error) {
	var areas []*Region
	err := r.db.Where("parent_code = ? AND level = ?", cityCode, 3).Find(&areas).Error
	return areas, err
}

// DeleteByCode 根据编码删除行政区域
func (r *regionRepository) DeleteByCode(code string) error {
	return r.db.Where("code = ?", code).Delete(&Region{}).Error
}

// TruncateTable 清空表
func (r *regionRepository) TruncateTable() error {
	return r.db.Exec("TRUNCATE TABLE " + (&Region{}).TableName()).Error
}

// GetTowns 获取指定区县的所有乡镇
func (r *regionRepository) GetTowns(areaCode string) ([]*Region, error) {
	var towns []*Region
	err := r.db.Where("parent_code = ? AND level = ?", areaCode, 4).Find(&towns).Error
	return towns, err
}

// GetVillages 获取指定乡镇的所有村街道
func (r *regionRepository) GetVillages(townCode string) ([]*Region, error) {
	var villages []*Region
	err := r.db.Where("parent_code = ? AND level = ?", townCode, 5).Find(&villages).Error
	return villages, err
}
