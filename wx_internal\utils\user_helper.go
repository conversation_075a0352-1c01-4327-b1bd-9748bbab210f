package utils

import (
	"context"
	"errors"
	"net/http"

	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// OpenID相关的context key常量
const (
	OpenIDKey = "openid" // 与 wx_internal/middleware/jwt.go 中的常量保持一致
)

// GetUserIDFromRequest 从请求context中获取用户ID
// 现在直接从认证中间件设置的context中获取，无需再查询数据库
func GetUserIDFromRequest(w http.ResponseWriter, r *http.Request) (uint, bool) {
	// 从context中获取用户ID
	userIDValue := r.Context().Value(types.UserIDKey)
	if userIDValue == nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return 0, false
	}

	userID, ok := userIDValue.(uint)
	if !ok {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "无效的用户ID"))
		return 0, false
	}

	return userID, true
}

// GetUserIDFromContext 从上下文中获取用户ID (不需要ResponseWriter)
// 适用于在服务层或其他不需要直接返回HTTP响应的地方使用
func GetUserIDFromContext(ctx context.Context) (uint, error) {
	userIDValue := ctx.Value(types.UserIDKey)
	if userIDValue == nil {
		return 0, errors.New("用户未登录")
	}

	userID, ok := userIDValue.(uint)
	if !ok {
		return 0, errors.New("无效的用户ID")
	}

	return userID, nil
}

// GetOpenIDFromRequest 从请求context中获取微信OpenID
// 专门用于小程序端，获取微信用户的OpenID
func GetOpenIDFromRequest(w http.ResponseWriter, r *http.Request) (string, bool) {
	// 从context中获取OpenID，使用正确的OpenID key
	openIDValue := r.Context().Value(OpenIDKey)
	if openIDValue == nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return "", false
	}

	openID, ok := openIDValue.(string)
	if !ok {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "无效的用户标识"))
		return "", false
	}

	return openID, true
}

// GetOpenIDFromContext 从上下文中获取微信OpenID (不需要ResponseWriter)
// 适用于在服务层或其他不需要直接返回HTTP响应的地方使用
func GetOpenIDFromContext(ctx context.Context) (string, error) {
	// 使用正确的 OpenID key
	openIDValue := ctx.Value(OpenIDKey)
	if openIDValue == nil {
		return "", errors.New("用户未登录")
	}

	openID, ok := openIDValue.(string)
	if !ok {
		return "", errors.New("无效的用户标识")
	}

	return openID, nil
}
