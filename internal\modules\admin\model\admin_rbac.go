package model

import (
	"time"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// AdminUser 管理员表
// type AdminUser struct {
// 	AdminID   uint      `json:"admin_id" gorm:"primaryKey;autoIncrement"`
// 	Username  string    `json:"username" gorm:"uniqueIndex;type:varchar(50);not null"`
// 	Password  string    `json:"-" gorm:"type:varchar(100);not null"`
// 	StoreID   *uint     `json:"store_id" gorm:"index"` // 所属门店ID（店长专用）
// 	Email     string    `json:"email" gorm:"type:varchar(100)"`
// 	Mobile    string    `json:"mobile" gorm:"type:varchar(20)"`
// 	Status    int       `json:"status" gorm:"default:1"` // 1-正常，0-禁用
// 	LastLogin time.Time `json:"last_login"`
// 	LastIP    string    `json:"last_ip" gorm:"type:varchar(50)"` // 最后登录IP
// 	CreatedAt time.Time `json:"created_at"`
// 	UpdatedAt time.Time `json:"updated_at"`
// }

// AdminRole 管理员角色表
type AdminRole struct {
	RoleID      uint      `json:"role_id" gorm:"primaryKey;autoIncrement;comment:角色ID"`
	RoleName    string    `json:"role_name" gorm:"type:varchar(50);not null;comment:角色名称"`
	Description string    `json:"description" gorm:"type:varchar(200);comment:角色描述"`
	IsSystem    bool      `json:"is_system" gorm:"default:false;comment:是否系统内置角色"`
	Status      int       `json:"status" gorm:"default:1;not null;comment:状态：1-正常，0-禁用"`
	CreatedAt   time.Time `json:"created_at" gorm:"not null;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"not null;comment:更新时间"`
}

// AdminPermission 管理员权限表
type AdminPermission struct {
	PermID      uint      `json:"perm_id" gorm:"primaryKey;autoIncrement;comment:权限ID"`
	Module      string    `json:"module" gorm:"type:varchar(50);not null;comment:模块名称"`
	Action      string    `json:"action" gorm:"type:varchar(50);not null;comment:操作名称"`
	Description string    `json:"description" gorm:"type:varchar(200);comment:权限描述"`
	Status      int       `json:"status" gorm:"default:1;not null;comment:状态：1-正常，0-禁用"`
	CreatedAt   time.Time `json:"created_at" gorm:"not null;comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"not null;comment:更新时间"`
}

// AdminUserRole 用户角色关联表
type AdminUserRole struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	AdminID   uint      `json:"admin_id" gorm:"index;not null;default:0;comment:管理员ID"`
	RoleID    uint      `json:"role_id" gorm:"index;not null;default:0;comment:角色ID"`
	CreatedAt time.Time `json:"created_at" gorm:"not null;comment:创建时间"`
}

// AdminRolePermission 角色权限关联表
type AdminRolePermission struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	RoleID    uint      `json:"role_id" gorm:"index;not null;default:0;comment:角色ID"`
	PermID    uint      `json:"perm_id" gorm:"index;not null;default:0;comment:权限ID"`
	CreatedAt time.Time `json:"created_at" gorm:"not null;comment:创建时间"`
}

// TableName 设置AdminUser表名
func (AdminUser) TableName() string {
	return "admin_user"
}

// TableName 设置AdminRole表名
func (AdminRole) TableName() string {
	return "admin_role"
}

// TableName 设置AdminPermission表名
func (AdminPermission) TableName() string {
	return "admin_permission"
}

// TableName 设置AdminUserRole表名
func (AdminUserRole) TableName() string {
	return "admin_user_role"
}

// TableName 设置AdminRolePermission表名
func (AdminRolePermission) TableName() string {
	return "admin_role_permission"
}

// AdminRBACRepository 后台RBAC仓库接口
type AdminRBACRepository interface {
	// 用户角色管理
	AssignRoleToAdmin(adminID, roleID uint) error
	RemoveRoleFromAdmin(adminID, roleID uint) error
	GetAdminRoles(adminID uint) ([]AdminRole, error)

	// 用户角色管理 (普通用户，非管理员)
	AssignRolesToUser(userID uint, roleIDs []uint) error
	RemoveRoleFromUser(userID, roleID uint) error
	GetUserRoles(userID uint) ([]AdminRole, error)

	// 权限管理
	CheckAdminPermission(adminID uint, module, action string) (bool, error)
	GetRolePermissions(roleID uint) ([]AdminPermission, error)
	GetAllPermissions() ([]AdminPermission, error)

	// 角色管理
	CreateRole(role *AdminRole) error
	UpdateRole(role *AdminRole) error
	DeleteRole(roleID uint) error
	GetAllRoles() ([]AdminRole, error)
	FindRoleByID(roleID uint) (*AdminRole, error)

	// 角色权限管理
	AssignPermissionToRole(roleID, permID uint) error
	RemovePermissionFromRole(roleID, permID uint) error
	BatchAssignPermissionsToRole(roleID uint, permIDs []uint) error
	AssignPermissionsToRole(roleID uint, permIDs []uint) error // 批量分配权限的别名方法

	// 菜单管理
	GetMenus() ([]AdminMenu, error)                        // 获取所有菜单
	GetMenusByParentID(parentID uint) ([]AdminMenu, error) // 获取指定父级ID的菜单
	GetMenusByRole(roleID uint) ([]AdminMenu, error)       // 获取指定角色可访问的菜单
	GetMenusByAdmin(adminID uint) ([]AdminMenu, error)     // 获取指定管理员可访问的菜单
	AssignMenusToRole(roleID uint, menuIDs []uint) error   // 为角色分配菜单

	// 获取权限树视图
	GetPermissionTreeView() ([]map[string]interface{}, error) // 动态获取权限树

	// 获取菜单信息
	GetMenuByID(menuID uint) (*struct {
		Menu        AdminMenu         `json:"menu"`
		Permission  AdminPermission   `json:"permission"`
		Permissions []AdminPermission `json:"permissions"`
	}, error)
}

// adminRBACRepository 后台RBAC仓库实现
type adminRBACRepository struct {
	db *gorm.DB
}

// NewAdminRBACRepository 创建后台RBAC仓库
func NewAdminRBACRepository(db *gorm.DB) AdminRBACRepository {
	if db == nil {
		db = mysql.Slave()
	}
	return &adminRBACRepository{db: db}
}

// AssignRoleToAdmin 为管理员分配角色
func (r *adminRBACRepository) AssignRoleToAdmin(adminID, roleID uint) error {
	userRole := AdminUserRole{
		AdminID:   adminID,
		RoleID:    roleID,
		CreatedAt: time.Now(),
	}
	return r.db.Create(&userRole).Error
}

// RemoveRoleFromAdmin 移除管理员角色
func (r *adminRBACRepository) RemoveRoleFromAdmin(adminID, roleID uint) error {
	return r.db.Where("admin_id = ? AND role_id = ?", adminID, roleID).Delete(&AdminUserRole{}).Error
}

// GetAdminRoles 获取管理员所有角色
func (r *adminRBACRepository) GetAdminRoles(adminID uint) ([]AdminRole, error) {
	var roles []AdminRole
	err := r.db.Joins("JOIN admin_user_role ON admin_role.role_id = admin_user_role.role_id").
		Where("admin_user_role.admin_id = ?", adminID).
		Find(&roles).Error
	return roles, err
}

// CheckAdminPermission 检查管理员是否拥有权限
func (r *adminRBACRepository) CheckAdminPermission(adminID uint, module, action string) (bool, error) {
	var count int64
	// 查询指定模块和操作的权限ID
	var permID uint
	err := r.db.Model(&AdminPermission{}).
		Where("module = ? AND action = ?", module, action).
		Select("perm_id").
		Scan(&permID).Error
	if err != nil {
		return false, err
	}

	// 检查管理员通过角色是否拥有该权限
	err = r.db.Table("admin_role_permission").
		Joins("JOIN admin_user_role ON admin_role_permission.role_id = admin_user_role.role_id").
		Where("admin_user_role.admin_id = ? AND admin_role_permission.perm_id = ?", adminID, permID).
		Count(&count).Error

	// 如果是超级管理员，直接返回true
	if count == 0 {
		var isSuperAdmin int64
		r.db.Table("admin_user_role").
			Where("admin_id = ? AND role_id = 1001", adminID).
			Count(&isSuperAdmin)
		if isSuperAdmin > 0 {
			return true, nil
		}
	}

	return count > 0, err
}

// GetRolePermissions 获取角色的所有权限
func (r *adminRBACRepository) GetRolePermissions(roleID uint) ([]AdminPermission, error) {
	var permissions []AdminPermission
	err := r.db.Joins("JOIN admin_role_permission ON admin_permission.perm_id = admin_role_permission.perm_id").
		Where("admin_role_permission.role_id = ?", roleID).
		Find(&permissions).Error
	return permissions, err
}

// GetAllPermissions 获取所有权限
func (r *adminRBACRepository) GetAllPermissions() ([]AdminPermission, error) {
	var permissions []AdminPermission
	err := r.db.Find(&permissions).Error
	return permissions, err
}

// CreateRole 创建角色
func (r *adminRBACRepository) CreateRole(role *AdminRole) error {
	role.CreatedAt = time.Now()
	role.UpdatedAt = time.Now()
	return r.db.Create(role).Error
}

// UpdateRole 更新角色
func (r *adminRBACRepository) UpdateRole(role *AdminRole) error {
	role.UpdatedAt = time.Now()
	return r.db.Save(role).Error
}

// DeleteRole 删除角色
func (r *adminRBACRepository) DeleteRole(roleID uint) error {
	// 系统角色不允许删除
	var role AdminRole
	if err := r.db.First(&role, roleID).Error; err != nil {
		return err
	}
	if role.IsSystem {
		return gorm.ErrInvalidTransaction // 使用gorm错误表示非法操作
	}

	// 使用事务确保删除角色时一并删除相关联的用户角色关系和角色权限关系
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 首先删除角色权限关系
		if err := tx.Where("role_id = ?", roleID).Delete(&AdminRolePermission{}).Error; err != nil {
			return err
		}
		// 删除用户角色关系
		if err := tx.Where("role_id = ?", roleID).Delete(&AdminUserRole{}).Error; err != nil {
			return err
		}
		// 最后删除角色
		return tx.Delete(&AdminRole{}, roleID).Error
	})
}

// GetAllRoles 获取所有角色
func (r *adminRBACRepository) GetAllRoles() ([]AdminRole, error) {
	var roles []AdminRole
	err := r.db.Find(&roles).Error
	return roles, err
}

// AssignPermissionToRole 为角色分配权限
func (r *adminRBACRepository) AssignPermissionToRole(roleID, permID uint) error {
	rolePermission := AdminRolePermission{
		RoleID:    roleID,
		PermID:    permID,
		CreatedAt: time.Now(),
	}
	return r.db.Create(&rolePermission).Error
}

// RemovePermissionFromRole 移除角色权限
func (r *adminRBACRepository) RemovePermissionFromRole(roleID, permID uint) error {
	return r.db.Where("role_id = ? AND perm_id = ?", roleID, permID).Delete(&AdminRolePermission{}).Error
}

// BatchAssignPermissionsToRole 批量为角色分配权限
func (r *adminRBACRepository) BatchAssignPermissionsToRole(roleID uint, permIDs []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 先删除该角色的所有权限
		if err := tx.Where("role_id = ?", roleID).Delete(&AdminRolePermission{}).Error; err != nil {
			return err
		}

		// 批量添加新权限
		for _, permID := range permIDs {
			rolePermission := AdminRolePermission{
				RoleID:    roleID,
				PermID:    permID,
				CreatedAt: time.Now(),
			}
			if err := tx.Create(&rolePermission).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// FindRoleByID 通过ID查找角色
func (r *adminRBACRepository) FindRoleByID(roleID uint) (*AdminRole, error) {
	var role AdminRole
	err := r.db.First(&role, roleID).Error
	if err != nil {
		return nil, err
	}
	return &role, nil
}

// AssignRolesToUser 为普通用户分配角色（批量）
func (r *adminRBACRepository) AssignRolesToUser(userID uint, roleIDs []uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 先删除该用户的所有角色
		if err := tx.Where("admin_id = ?", userID).Delete(&AdminUserRole{}).Error; err != nil {
			return err
		}

		// 批量添加新角色
		for _, roleID := range roleIDs {
			userRole := AdminUserRole{
				AdminID:   userID,
				RoleID:    roleID,
				CreatedAt: time.Now(),
			}
			if err := tx.Create(&userRole).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// RemoveRoleFromUser 移除普通用户的一个角色
func (r *adminRBACRepository) RemoveRoleFromUser(userID, roleID uint) error {
	// 复用移除管理员角色的方法，因为底层表结构相同
	return r.RemoveRoleFromAdmin(userID, roleID)
}

// GetUserRoles 获取普通用户的所有角色
func (r *adminRBACRepository) GetUserRoles(userID uint) ([]AdminRole, error) {
	// 复用获取管理员角色的方法，因为底层表结构相同
	return r.GetAdminRoles(userID)
}

// AssignPermissionsToRole 为角色分配权限 (BatchAssignPermissionsToRole的别名)
func (r *adminRBACRepository) AssignPermissionsToRole(roleID uint, permIDs []uint) error {
	return r.BatchAssignPermissionsToRole(roleID, permIDs)
}

// GetMenus 获取所有菜单
func (r *adminRBACRepository) GetMenus() ([]AdminMenu, error) {
	var menus []AdminMenu
	err := r.db.Order("sort asc").Find(&menus).Error
	return menus, err
}

// GetMenusByParentID 获取指定父级ID的菜单
func (r *adminRBACRepository) GetMenusByParentID(parentID uint) ([]AdminMenu, error) {
	var menus []AdminMenu
	err := r.db.Where("parent_id = ?", parentID).Order("sort asc").Find(&menus).Error
	return menus, err
}

// GetMenusByRole 获取指定角色可访问的菜单
func (r *adminRBACRepository) GetMenusByRole(roleID uint) ([]AdminMenu, error) {
	var menus []AdminMenu

	// 如果是超级管理员角色，返回所有菜单
	if roleID == 1001 {
		return r.GetMenus()
	}

	// 获取角色的所有权限ID
	var permIDs []uint
	err := r.db.Model(&AdminRolePermission{}).
		Where("role_id = ?", roleID).
		Pluck("perm_id", &permIDs).Error
	if err != nil {
		return nil, err
	}

	// 查询匹配权限ID的菜单
	if len(permIDs) > 0 {
		err = r.db.Where("perm_id IN (?) AND is_show = 1 AND status = 1", permIDs).
			Order("parent_id, sort").
			Find(&menus).Error
	} else {
		// 没有权限时返回空菜单列表
		menus = []AdminMenu{}
	}

	return menus, err
}

// GetMenusByAdmin 获取指定管理员可访问的菜单
func (r *adminRBACRepository) GetMenusByAdmin(adminID uint) ([]AdminMenu, error) {
	// 检查是否为超级管理员
	var isSuperAdmin int64
	r.db.Table("admin_user_role").
		Where("admin_id = ? AND role_id = 1001", adminID).
		Count(&isSuperAdmin)

	if isSuperAdmin > 0 {
		// 超级管理员返回所有可见菜单
		var menus []AdminMenu
		err := r.db.Where("is_show = 1 AND status = 1").
			Order("parent_id, sort").
			Find(&menus).Error
		return menus, err
	}

	// 获取管理员的所有角色ID
	var roleIDs []uint
	err := r.db.Table("admin_user_role").
		Where("admin_id = ?", adminID).
		Pluck("role_id", &roleIDs).Error
	if err != nil {
		return nil, err
	}

	if len(roleIDs) == 0 {
		return []AdminMenu{}, nil
	}

	// 获取这些角色拥有的所有权限ID
	var permIDs []uint
	err = r.db.Table("admin_role_permission").
		Where("role_id IN ?", roleIDs).
		Pluck("perm_id", &permIDs).Error
	if err != nil {
		return nil, err
	}

	if len(permIDs) == 0 {
		return []AdminMenu{}, nil
	}

	// 获取这些权限ID对应的所有菜单
	var menus []AdminMenu
	err = r.db.Where("perm_id IN ? AND is_show = 1 AND status = 1", permIDs).
		Order("parent_id, sort").
		Find(&menus).Error

	return menus, err
}

// AssignMenusToRole 为角色分配菜单
func (r *adminRBACRepository) AssignMenusToRole(roleID uint, menuIDs []uint) error {
	// 获取菜单对应的权限ID
	var menuPermMap map[uint]uint = make(map[uint]uint)
	var menus []struct {
		MenuID uint
		PermID uint
	}

	err := r.db.Model(&AdminMenu{}).
		Select("menu_id, perm_id").
		Where("menu_id IN ?", menuIDs).
		Find(&menus).Error
	if err != nil {
		return err
	}

	for _, menu := range menus {
		menuPermMap[menu.MenuID] = menu.PermID
	}

	// 收集所有权限ID
	var permIDs []uint
	for _, permID := range menuPermMap {
		// 避免重复
		found := false
		for _, id := range permIDs {
			if id == permID {
				found = true
				break
			}
		}
		if !found && permID > 0 {
			permIDs = append(permIDs, permID)
		}
	}

	// 为角色分配这些权限
	return r.BatchAssignPermissionsToRole(roleID, permIDs)
}

// GetPermissionTreeView 动态生成权限树视图，以树形结构返回
func (r *adminRBACRepository) GetPermissionTreeView() ([]map[string]interface{}, error) {
	// 1. 获取所有菜单和对应的权限
	rows, err := r.db.Raw(`
		SELECT 
			m.menu_id AS id,
			m.title,
			m.parent_id,
			p.module,
			p.action,
			p.description AS perm_desc,
			m.sort,
			m.is_show,
			m.status
		FROM admin_menu m
		JOIN admin_permission p ON m.perm_id = p.perm_id
		WHERE m.status = 1
		ORDER BY m.parent_id, m.sort
	`).Rows()

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 2. 读取所有行并构建平铺的节点结构
	var allNodes []map[string]interface{}
	nodeMap := make(map[uint]map[string]interface{})

	for rows.Next() {
		var (
			id       uint
			title    string
			parentID uint
			module   string
			action   string
			permDesc string
			sort     int
			isShow   bool
			status   int
		)

		if err := rows.Scan(&id, &title, &parentID, &module, &action, &permDesc, &sort, &isShow, &status); err != nil {
			return nil, err
		}

		node := map[string]interface{}{
			"id":        id,
			"title":     title,
			"parent_id": parentID,
			"module":    module,
			"action":    action,
			"perm_desc": permDesc,
			"sort":      sort,
			"is_show":   isShow,
			"status":    status,
			"key":       module + ":" + action,
			"children":  []map[string]interface{}{},
		}

		allNodes = append(allNodes, node)
		nodeMap[id] = node
	}

	// 3. 构建树形结构
	var result []map[string]interface{}
	for _, node := range allNodes {
		parentID := node["parent_id"].(uint)
		if parentID == 0 {
			// 根节点
			result = append(result, node)
		} else {
			// 子节点，添加到父节点的children中
			if parent, exists := nodeMap[parentID]; exists {
				children := parent["children"].([]map[string]interface{})
				parent["children"] = append(children, node)
			}
		}
	}

	// 4. 返回构建好的树
	return result, nil
}

// GetMenuByID 根据菜单ID获取菜单及其对应的权限信息
func (r *adminRBACRepository) GetMenuByID(menuID uint) (*struct {
	Menu        AdminMenu         `json:"menu"`
	Permission  AdminPermission   `json:"permission"`
	Permissions []AdminPermission `json:"permissions"`
}, error) {
	var menu AdminMenu
	err := r.db.Where("menu_id = ?", menuID).First(&menu).Error
	if err != nil {
		return nil, err
	}

	// 获取主权限
	var permission AdminPermission
	err = r.db.Where("perm_id = ?", menu.PermID).First(&permission).Error
	if err != nil {
		return nil, err
	}

	// 获取同一模块的所有权限（增删改查）
	var permissions []AdminPermission
	err = r.db.Where("module = ?", permission.Module).Find(&permissions).Error
	if err != nil {
		return nil, err
	}

	result := &struct {
		Menu        AdminMenu         `json:"menu"`
		Permission  AdminPermission   `json:"permission"`
		Permissions []AdminPermission `json:"permissions"`
	}{
		Menu:        menu,
		Permission:  permission,
		Permissions: permissions,
	}

	return result, nil
}
