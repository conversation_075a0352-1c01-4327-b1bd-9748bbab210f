package bootstrap

import (
	"yekaitai/internal/modules/user_points/model"
	"yekaitai/pkg/infra/mysql"
)

// CoinRulesMigration 叶小币规则数据库迁移
func CoinRulesMigration() error {
	db := mysql.GetDB()

	// 自动迁移所有叶小币相关表
	err := db.AutoMigrate(
		&model.CoinGlobalConfig{},
		&model.CoinRules{},
		&model.UserCoins{},
		&model.CoinTransactions{},
		&model.CoinOneTimeRewards{},
	)
	if err != nil {
		return err
	}

	return nil
}
