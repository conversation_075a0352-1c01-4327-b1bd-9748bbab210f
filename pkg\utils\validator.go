package utils

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
)

var (
	validate   *validator.Validate
	translator ut.Translator
)

func init() {
	// 初始化验证器
	validate = validator.New()
	
	// 注册字段名称
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("label"), ",", 2)[0]
		if name == "" {
			name = strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		}
		if name == "-" {
			return ""
		}
		return name
	})

	// 初始化中文翻译器
	zhTrans := zh.New()
	uni := ut.New(zhTrans, zhTrans)
	translator, _ = uni.GetTranslator("zh")
	
	// 注册中文翻译
	_ = zh_translations.RegisterDefaultTranslations(validate, translator)
}

// ValidateStruct 验证结构体
func ValidateStruct(s interface{}) error {
	err := validate.Struct(s)
	if err == nil {
		return nil
	}

	// 翻译错误信息
	validationErrors, ok := err.(validator.ValidationErrors)
	if !ok {
		return err
	}

	// 返回第一个错误
	if len(validationErrors) > 0 {
		return errors.New(validationErrors[0].Translate(translator))
	}

	return fmt.Errorf("验证失败: %w", err)
} 