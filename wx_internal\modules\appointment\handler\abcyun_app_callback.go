package handler

import (
	"errors"
	"net/http"

	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// AppCallbackFailRequest 查询推送失败事件列表请求
type AppCallbackFailRequest struct {
	AppId  string `path:"appId"`            // 应用id
	Date   string `form:"date"`             // 日期（格式yyyy-MM-dd）
	Limit  int    `form:"limit,default=20"` // 每页显示条数，最大值为500
	Offset int    `form:"offset,default=0"` // 分页起始下标
}

// AbcYunAppCallbackFailHandler 查询推送失败事件列表
func AbcYunAppCallbackFailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req AppCallbackFailRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.AppId == "" {
		httpx.Error(w, errors.New("缺少应用id参数"))
		return
	}
	if req.Date == "" {
		httpx.Error(w, errors.New("缺少日期参数"))
		return
	}

	// 调用ABC云API获取推送失败事件列表
	eventList, err := client.GetAppCallbackFailList(req.AppId, req.Date, req.Limit, req.Offset)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, eventList)
}
