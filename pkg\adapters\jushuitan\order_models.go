package jushuitan

import (
	"time"
)

// OrderUploadRequest 订单上传请求
type OrderUploadRequest struct {
	ShopID             int               `json:"shop_id"`                        // 店铺编号
	OAID               string            `json:"oaid,omitempty"`                 // oaid（从淘宝平台拿到的的密文串）
	WmsCoID            int               `json:"wms_co_id,omitempty"`            // 分仓编号
	PlanDeliveryDate   string            `json:"plan_delivery_date,omitempty"`   // 最晚发货时间
	OuterSoID          string            `json:"outer_so_id,omitempty"`          // 外部交易单号
	FullReceiveEnJSON  string            `json:"full_receive_en_json,omitempty"` // 收件人密文串json
	ReceiverMobileEn   string            `json:"receiver_mobile_en,omitempty"`   // 收件人密文手机号
	ReceiverNameEn     string            `json:"receiver_name_en,omitempty"`     // 收件人密文姓名
	BuyerPaidAmount    float64           `json:"buyer_paid_amount,omitempty"`    // 总买家实付
	SellerIncomeAmount float64           `json:"seller_income_amount,omitempty"` // 总卖家实收
	SoID               string            `json:"so_id"`                          // 自研商城系统订单号
	OrderDate          string            `json:"order_date"`                     // 订单日期
	ShopStatus         string            `json:"shop_status"`                    // 自研商城系统订单状态
	ShopBuyerID        string            `json:"shop_buyer_id"`                  // 买家帐号
	ReceiverState      string            `json:"receiver_state,omitempty"`       // 收货省份
	ReceiverCity       string            `json:"receiver_city,omitempty"`        // 收货市
	ReceiverDistrict   string            `json:"receiver_district,omitempty"`    // 收货区/街道
	ReceiverAddress    string            `json:"receiver_address"`               // 收货地址
	ReceiverName       string            `json:"receiver_name"`                  // 收件人
	ReceiverPhone      string            `json:"receiver_phone,omitempty"`       // 联系电话
	ReceiverMobile     string            `json:"receiver_mobile,omitempty"`      // 手机
	ReceiverEmail      string            `json:"receiver_email,omitempty"`       // 收货邮箱
	ReceiverTown       string            `json:"receiver_town,omitempty"`        // 收货街道
	ReceiverCountry    string            `json:"receiver_country,omitempty"`     // 收货国家
	ReceiverZip        string            `json:"receiver_zip,omitempty"`         // 邮政编码
	PayAmount          float64           `json:"pay_amount"`                     // 应付金额
	Freight            float64           `json:"freight"`                        // 运费
	BuyerMessage       string            `json:"buyer_message,omitempty"`        // 买家留言
	ReferrerID         string            `json:"referrer_id,omitempty"`          // 达人id
	ReferrerName       string            `json:"referrer_name,omitempty"`        // 达人名称
	Remark             string            `json:"remark,omitempty"`               // 卖家备注
	Shipment           string            `json:"shipment,omitempty"`             // 跨境买家指定物流
	IsCod              bool              `json:"is_cod,omitempty"`               // 是否货到付款
	ShopModified       string            `json:"shop_modified,omitempty"`        // 订单修改日期
	LID                string            `json:"l_id,omitempty"`                 // 快递单号
	LogisticsCompany   string            `json:"logistics_company,omitempty"`    // 快递公司名称
	QuestionDesc       string            `json:"question_desc,omitempty"`        // 订单异常描述
	SellerFlag         int               `json:"seller_flag,omitempty"`          // 卖家标签（旗帜）
	Items              []OrderItemDetail `json:"items"`                          // 商品明细
	Pay                *OrderPayDetail   `json:"pay,omitempty"`                  // 支付明细
	Card               *OrderCardDetail  `json:"card,omitempty"`                 // 身份证信息
	SendDate           string            `json:"send_date,omitempty"`            // 发货日期
	Labels             string            `json:"labels,omitempty"`               // 标记｜多标签
	LcID               string            `json:"lc_id,omitempty"`                // 快递公司编码
	Currency           string            `json:"currency,omitempty"`             // 货币代码
	InvoiceType        string            `json:"invoice_type,omitempty"`         // 发票类型
	InvoiceTitle       string            `json:"invoice_title,omitempty"`        // 发票抬头
	BuyerTaxNo         string            `json:"buyer_tax_no,omitempty"`         // 发票税号
	Invoice            *OrderInvoice     `json:"invoice,omitempty"`              // 发票信息
	Node               string            `json:"node,omitempty"`                 // 线下备注
	FinanceData        *OrderFinanceData `json:"finance_data,omitempty"`         // 跨境财务数据
	Creator            int               `json:"creator,omitempty"`              // 业务员id
	CreatorName        string            `json:"creator_name,omitempty"`         // 业务员名称
	OrderExt           *OrderExtData     `json:"order_ext,omitempty"`            // 跨境扩展信息
	ExtDatas           OrderExtDatas     `json:"ext_datas"`                      // 扩展数据
}

// OrderItemDetail 订单商品明细
type OrderItemDetail struct {
	SkuID           string  `json:"sku_id"`                     // 商品编码
	ShopSkuID       string  `json:"shop_sku_id"`                // 店铺商品编码
	ShopIID         string  `json:"shop_i_id,omitempty"`        // 店铺商品款式编码
	IID             string  `json:"i_id,omitempty"`             // ERP内款号/货号
	Pic             string  `json:"pic,omitempty"`              // 图片地址
	PropertiesValue string  `json:"properties_value,omitempty"` // 商品属性
	Amount          float64 `json:"amount"`                     // 成交总额
	Price           float64 `json:"price,omitempty"`            // 单价
	BasePrice       float64 `json:"base_price"`                 // 原价
	Qty             int     `json:"qty"`                        // 数量
	RefundQty       int     `json:"refund_qty,omitempty"`       // 退货数量
	Name            string  `json:"name"`                       // 商品名称
	RefundStatus    string  `json:"refund_status,omitempty"`    // 退款状态
	OuterOiID       string  `json:"outer_oi_id"`                // 商家系统订单商品明细主键
	Remark          string  `json:"remark,omitempty"`           // 订单明细备注
	BatchID         string  `json:"batch_id,omitempty"`         // 生产批次号
	ProducedDate    string  `json:"produced_date,omitempty"`    // 生产日期
	IsGift          bool    `json:"is_gift,omitempty"`          // 是否赠品
	BuID            int     `json:"bu_id,omitempty"`            // 虚拟仓编码
	ReferrerID      string  `json:"referrer_id,omitempty"`      // 达人id
	ReferrerName    string  `json:"referrer_name,omitempty"`    // 达人名称
}

// OrderPayDetail 订单支付明细
type OrderPayDetail struct {
	OuterPayID    string  `json:"outer_pay_id"`   // 外部支付单号
	PayDate       string  `json:"pay_date"`       // 支付日期
	Payment       string  `json:"payment"`        // 支付方式
	SellerAccount string  `json:"seller_account"` // 收款账户
	BuyerAccount  string  `json:"buyer_account"`  // 买家支付账号
	Amount        float64 `json:"amount"`         // 支付总额
}

// OrderCardDetail 订单身份证信息
type OrderCardDetail struct {
	Name   string `json:"name"`    // 姓名
	CardID string `json:"card_id"` // 身份证号
}

// OrderInvoice 订单发票信息
type OrderInvoice struct {
	Address     string `json:"address,omitempty"`     // 专票地址
	Bank        string `json:"bank,omitempty"`        // 专票银行
	Phone       string `json:"phone,omitempty"`       // 专票电话
	Account     string `json:"account,omitempty"`     // 专票账户
	Email       string `json:"email,omitempty"`       // 专票邮箱
	UserName    string `json:"userName,omitempty"`    // 专票人姓名
	UserPhone   string `json:"userPhone,omitempty"`   // 专票人电话
	UserAddress string `json:"userAddress,omitempty"` // 专票人地址
}

// OrderFinanceData 订单跨境财务数据
type OrderFinanceData struct {
	RebateFee          float64 `json:"rebate_fee,omitempty"`          // 回扣金额
	ProductTax         float64 `json:"product_tax,omitempty"`         // 收入税费
	ShippingTax        float64 `json:"shipping_tax,omitempty"`        // 快递税
	OtherIncome        float64 `json:"other_income,omitempty"`        // 其他收入
	VoucherFromSeller  float64 `json:"voucher_from_seller,omitempty"` // 促销优惠金额
	PlatformCommission float64 `json:"platform_commission,omitempty"` // 平台佣金
	TransitionFee      float64 `json:"transition_fee,omitempty"`      // 转账费
	TransactionFee     float64 `json:"transaction_fee,omitempty"`     // 交易手续费
	OpaqueBaggingFee   float64 `json:"opaque_bagging_fee,omitempty"`  // 支出税费
	OtherExpense       float64 `json:"other_expense,omitempty"`       // 其他费用
}

// OrderExtDatas 订单扩展数据
type OrderExtDatas struct {
	DoorPlate   string `json:"door_plate"`   // 门牌号
	CompanyName string `json:"company_name"` // 公司名称
}

// OrderExtData 订单跨境扩展信息
type OrderExtData struct {
	TrackingNo   string         `json:"tracking_no,omitempty"`   // 国际运单号
	TrackingType string         `json:"tracking_type,omitempty"` // 运单类型
	PdfURL       string         `json:"pdf_url,omitempty"`       // PDF面单地址
	Datas        []OrderExtItem `json:"datas,omitempty"`         // 扩展数据项
}

// OrderExtItem 订单扩展数据项
type OrderExtItem struct {
	AutoId         string `json:"AutoId,omitempty"`         // 自增ID
	Iid            string `json:"Iid,omitempty"`            // 款式编码
	SkuId          string `json:"SkuId,omitempty"`          // 商品编码
	SkuCode        string `json:"SkuCode,omitempty"`        // 商品Code
	Name           string `json:"Name,omitempty"`           // 商品名称
	DeclareNameZN  string `json:"DeclareNameZN,omitempty"`  // 报关中文名称
	DeclareNameEN  string `json:"DeclareNameEN,omitempty"`  // 报关英文名称
	DeclareAmount  string `json:"DeclareAmount,omitempty"`  // 报关商品单价
	DeclareWeight  string `json:"DeclareWeight,omitempty"`  // 报关商品重量
	HsCode         string `json:"HsCode,omitempty"`         // 海关编码
	PostTaxNum     string `json:"PostTaxNum,omitempty"`     // 行邮税号
	IsBattery      string `json:"IsBattery,omitempty"`      // 是否带电
	IsLiquid       string `json:"IsLiquid,omitempty"`       // 是否液体
	IsDisable      string `json:"IsDisable,omitempty"`      // 是否禁用
	Creator        string `json:"Creator,omitempty"`        // 创建人
	Created        string `json:"Created,omitempty"`        // 创建时间
	Modifier       string `json:"Modifier,omitempty"`       // 修改人
	Modified       string `json:"Modified,omitempty"`       // 修改时间
	Unit           string `json:"Unit,omitempty"`           // 单位
	IsSetSkubin    string `json:"isSetSkubin,omitempty"`    // 是否拼接仓位
	IsJoinSkuid    string `json:"IsJoinSkuid,omitempty"`    // 是否拼接商品编码
	IsPaste        bool   `json:"IsPaste,omitempty"`        // 是否粘贴
	IsMagnetic     string `json:"IsMagnetic,omitempty"`     // 是否带磁
	IsFluid        string `json:"IsFluid,omitempty"`        // 是否液体
	IsGetPrice     string `json:"IsGetPrice,omitempty"`     // 是否取实际售价
	IsGetWeight    string `json:"IsGetWeight,omitempty"`    // 是否取实际重量
	SpecialAttr    string `json:"SpecialAttr,omitempty"`    // 特殊属性
	Country        string `json:"Country,omitempty"`        // 国家
	TempName       string `json:"TempName,omitempty"`       // 模板名称
	TempId         string `json:"TempId,omitempty"`         // 模板ID
	IsDefault      string `json:"IsDefault,omitempty"`      // 是否默认
	ShopId         string `json:"ShopId,omitempty"`         // 店铺编码
	IsOAmount      string `json:"IsOAmount,omitempty"`      // 是否整单金额
	Cid            string `json:"Cid,omitempty"`            // 类目ID
	IsPowder       string `json:"IsPowder,omitempty"`       // 是否是粉末
	IsSpecialCargo string `json:"IsSpecialCargo,omitempty"` // 是否特货
	IsJoinSkubin   string `json:"IsJoinSkubin,omitempty"`   // 是否拼接商品仓位
	SaveType       string `json:"SaveType,omitempty"`       // 保存类型
	DeclareType    string `json:"DeclareType,omitempty"`    // 报关类别
}

// OrderUploadResponse 订单上传响应
type OrderUploadResponse struct {
	Code int                     `json:"code"`
	Msg  string                  `json:"msg"`
	Data OrderUploadResponseData `json:"data"`
}

// OrderUploadResponseData 订单上传响应数据
type OrderUploadResponseData struct {
	Datas []OrderUploadResponseItem `json:"datas"`
}

// OrderUploadResponseItem 订单上传响应项
type OrderUploadResponseItem struct {
	OID       int    `json:"o_id"`      // ERP订单界面-内部单号
	SoID      string `json:"so_id"`     // ERP订单界面-线上单号
	IsSuccess bool   `json:"issuccess"` // 是否成功
	Msg       string `json:"msg"`       // 返回结果描述
}

// OrderQueryRequest 订单查询请求
type OrderQueryRequest struct {
	ShopID         int      `json:"shop_id,omitempty"`          // 店铺编号
	IsOfflineShop  bool     `json:"is_offline_shop,omitempty"`  // 是否为线下店铺
	SoIDs          []string `json:"so_ids,omitempty"`           // 线上单号列表，最多20条
	ModifiedBegin  string   `json:"modified_begin,omitempty"`   // 起始修改时间
	ModifiedEnd    string   `json:"modified_end,omitempty"`     // 结束修改时间
	DateType       int      `json:"date_type,omitempty"`        // 日期类型：0-修改时间，2-订单日期，3-发货时间
	Status         string   `json:"status,omitempty"`           // 订单状态
	PageIndex      int      `json:"page_index,omitempty"`       // 页码，从1开始
	PageSize       int      `json:"page_size,omitempty"`        // 每页条数，最大100
	StartTS        int64    `json:"start_ts,omitempty"`         // 时间戳，用于增量查询
	IsGetTotal     bool     `json:"is_get_total,omitempty"`     // 是否获取总条数
	OIDs           []int    `json:"o_ids,omitempty"`            // 内部订单号列表
	IsGetCBFinance bool     `json:"is_get_cbfinance,omitempty"` // 是否查询跨境财务信息
	OrderFlds      []string `json:"order_flds,omitempty"`       // 订单自定义字段
	OrderItemFlds  []string `json:"order_item_flds,omitempty"`  // 订单明细自定义字段
	OrderTypes     []string `json:"order_types,omitempty"`      // 订单类型
}

// OrderPayInfo 订单支付信息
type OrderPayInfo struct {
	PayID        string  `json:"pay_id"`        // 支付单ID
	OuterPayID   string  `json:"outer_pay_id"`  // 外部支付单号
	PayDate      string  `json:"pay_date"`      // 支付时间
	Amount       float64 `json:"amount"`        // 支付金额
	Payment      string  `json:"payment"`       // 支付方式
	BuyerAccount string  `json:"buyer_account"` // 买家支付账号
	IsOrderPay   bool    `json:"is_order_pay"`  // 是否支付
	Status       string  `json:"status"`        // 支付状态
	PayType      string  `json:"pay_type"`      // 支付类型
	OID          int     `json:"o_id"`          // 内部订单号
}

// OrderItemInfo 订单商品信息
type OrderItemInfo struct {
	OiID               int     `json:"oi_id"`                // 子订单号
	SkuID              string  `json:"sku_id"`               // 商品编码
	IID                string  `json:"i_id"`                 // 款式编码
	ShopSkuID          string  `json:"shop_sku_id"`          // 店铺商品编码
	ShopIID            string  `json:"shop_i_id"`            // 店铺款式编码
	PropertiesValue    string  `json:"properties_value"`     // 商品属性
	Amount             float64 `json:"amount"`               // 总额
	BasePrice          float64 `json:"base_price"`           // 原价
	Qty                int     `json:"qty"`                  // 数量
	Name               string  `json:"name"`                 // 商品名称
	Price              float64 `json:"price"`                // 单价
	OuterOiID          string  `json:"outer_oi_id"`          // 子订单号
	RefundID           string  `json:"refund_id"`            // 退款单号
	RefundQty          int     `json:"refund_qty"`           // 退款数量
	RefundStatus       string  `json:"refund_status"`        // 退款状态
	RawSoID            string  `json:"raw_so_id"`            // 原始线上单号
	IsPresale          bool    `json:"is_presale"`           // 是否预售
	IsGift             bool    `json:"is_gift"`              // 是否赠品
	ItemStatus         string  `json:"item_status"`          // 商品状态
	Pic                string  `json:"pic"`                  // 商品图片
	SkuType            string  `json:"sku_type"`             // 商品类型
	Remark             string  `json:"remark"`               // 备注
	BuyerPaidAmount    float64 `json:"buyer_paid_amount"`    // 买家实付
	SellerIncomeAmount float64 `json:"seller_income_amount"` // 卖家实收
	OID                int     `json:"o_id"`                 // 内部订单号
	ReferrerID         string  `json:"referrer_id"`          // 主播ID
	ItemExtData        string  `json:"item_ext_data"`        // 订单明细扩展字段
}

// OrderCBFinance 跨境订单财务数据
type OrderCBFinance struct {
	OID                int     `json:"o_id"`                // 内部订单号
	RebateFee          float64 `json:"rebate_fee"`          // 回扣金额
	ProductTax         float64 `json:"product_tax"`         // 收入税费
	ShippingTax        float64 `json:"shipping_tax"`        // 快递税
	OtherIncome        float64 `json:"other_income"`        // 其他收入
	VoucherFromSeller  float64 `json:"voucher_from_seller"` // 促销优惠金额
	PlatformCommission float64 `json:"platform_commission"` // 平台佣金
	TransitionFee      float64 `json:"transition_fee"`      // 转账费
	TransactionFee     float64 `json:"transaction_fee"`     // 交易手续费
	OpaqueBaggingFee   float64 `json:"opaque_bagging_fee"`  // 支出税费
	OtherExpense       float64 `json:"other_expense"`       // 其他费用
}

// OrderInfo 订单信息
type OrderInfo struct {
	IsCod              bool             `json:"is_cod"`               // 是否货到付款
	LID                string           `json:"l_id"`                 // 快递单号
	SendDate           string           `json:"send_date"`            // 发货日期
	PayDate            string           `json:"pay_date"`             // 支付时间
	Freight            float64          `json:"freight"`              // 买家支付运费
	ReceiverAddress    string           `json:"receiver_address"`     // 收货地址
	ReceiverDistrict   string           `json:"receiver_district"`    // 区
	WmsCoID            int              `json:"wms_co_id"`            // 发货仓编号
	LogisticsCompany   string           `json:"logistics_company"`    // 快递公司
	AsID               int              `json:"as_id"`                // 售后单号
	FreeAmount         float64          `json:"free_amount"`          // 抵扣金额
	ShopName           string           `json:"shop_name"`            // 店铺名称
	QuestionType       string           `json:"question_type"`        // 问题类型
	OuterPayID         string           `json:"outer_pay_id"`         // 外部支付单号
	SoID               string           `json:"so_id"`                // 线上订单号
	Type               string           `json:"type"`                 // 订单类型
	OrderFrom          string           `json:"order_from"`           // 订单来源
	Status             string           `json:"status"`               // 聚水潭订单状态
	PayAmount          float64          `json:"pay_amount"`           // 应付金额
	ShopBuyerID        string           `json:"shop_buyer_id"`        // 买家昵称
	OpenID             string           `json:"open_id"`              // 平台买家唯一值
	ShopStatus         string           `json:"shop_status"`          // 平台订单状态
	ReceiverMobile     string           `json:"receiver_mobile"`      // 手机
	ReceiverPhone      string           `json:"receiver_phone"`       // 电话
	OrderDate          string           `json:"order_date"`           // 订单日期
	QuestionDesc       string           `json:"question_desc"`        // 问题描述
	ReceiverCity       string           `json:"receiver_city"`        // 市
	ReceiverState      string           `json:"receiver_state"`       // 省
	ReceiverName       string           `json:"receiver_name"`        // 收件人
	OID                int              `json:"o_id"`                 // 内部订单号
	ShopID             int              `json:"shop_id"`              // 店铺编号
	CoID               int              `json:"co_id"`                // 公司编号
	Remark             string           `json:"remark"`               // 订单备注
	Modified           string           `json:"modified"`             // 修改时间
	Labels             string           `json:"labels"`               // 多标签
	PaidAmount         float64          `json:"paid_amount"`          // 实际支付金额
	Currency           string           `json:"currency"`             // 币种
	BuyerMessage       string           `json:"buyer_message"`        // 买家留言
	LcID               string           `json:"lc_id"`                // 物流公司编码
	CBTotalTax         float64          `json:"cb_total_tax"`         // 跨境平台税金
	InvoiceTitle       string           `json:"invoice_title"`        // 发票抬头
	InvoiceType        string           `json:"invoice_type"`         // 发票类型
	BuyerTaxNo         string           `json:"buyer_tax_no"`         // 发票税号
	CreatorName        string           `json:"creator_name"`         // 订单业务员
	PlanDeliveryDate   string           `json:"plan_delivery_date"`   // 计划发货时间
	Node               string           `json:"node"`                 // 线下备注
	ReceiverTown       string           `json:"receiver_town"`        // 街道
	ShopSite           string           `json:"shop_site"`            // 店铺站点信息
	EndTime            string           `json:"end_time"`             // 确认收货时间
	ReceiverCountry    string           `json:"receiver_country"`     // 国家代码
	ReceiverZip        string           `json:"receiver_zip"`         // 邮编
	SellerFlag         int              `json:"seller_flag"`          // 旗帜
	ReceiverEmail      string           `json:"receiver_email"`       // 收货邮箱
	ReferrerID         string           `json:"referrer_id"`          // 主播id
	ReferrerName       string           `json:"referrer_name"`        // 主播名称
	Created            string           `json:"created"`              // 订单创建时间
	Pays               []OrderPayInfo   `json:"pays"`                 // 支付信息
	Items              []OrderItemInfo  `json:"items"`                // 商品信息
	CBFinances         []OrderCBFinance `json:"cb_finances"`          // 跨境订单财务数据
	TS                 int64            `json:"ts"`                   // 数据库行版本号
	BuyerPaidAmount    float64          `json:"buyer_paid_amount"`    // 买家实付
	SellerIncomeAmount float64          `json:"seller_income_amount"` // 卖家实收
}

// OrderQueryResponse 订单查询响应
type OrderQueryResponse struct {
	Code      int         `json:"code"`       // 错误码
	Msg       string      `json:"msg"`        // 错误描述
	PageSize  int         `json:"page_size"`  // 每页条数
	PageIndex int         `json:"page_index"` // 页码
	DataCount int         `json:"data_count"` // 总条数
	PageCount int         `json:"page_count"` // 总页数
	HasNext   bool        `json:"has_next"`   // 是否有下一页
	Orders    []OrderInfo `json:"orders"`     // 订单列表
	IsSuccess bool        `json:"issuccess"`  // 是否成功
	RequestID string      `json:"requestId"`  // 请求ID
}

// OrderCancelRequest 订单取消请求
type OrderCancelRequest struct {
	OIDs       []int  `json:"o_ids"`            // 内部订单号列表，一次最大支持50条
	CancelType string `json:"cancel_type"`      // 取消类型，例如"不需要了"
	Remark     string `json:"remark,omitempty"` // 备注
}

// OrderCancelResponse 订单取消响应
type OrderCancelResponse struct {
	Code int    `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data string `json:"data"` // 数据
}

// OrderModifyWMSRequest 订单指定发货仓请求项
type OrderModifyWMSRequest struct {
	OID     int `json:"o_id"`      // ERP内部订单号
	WmsCoID int `json:"wms_co_id"` // 仓库编码
}

// OrderModifyWMSResponse 订单指定发货仓响应
type OrderModifyWMSResponse struct {
	Code      string `json:"code"`      // 返回码
	IsSuccess string `json:"issuccess"` // 是否执行成功
	Msg       string `json:"msg"`       // 返回信息
}

// OrderExceptionRequest 订单转为异常状态请求
type OrderExceptionRequest struct {
	ShopID        int64  `json:"shop_id"`        // 店铺编号
	SoID          string `json:"so_id"`          // 订单编号
	Exception     string `json:"exception"`      // 异常原因
	ExceptionType int    `json:"exception_type"` // 异常类型：1-物流异常，2-商品异常，3-买家异常，4-其他异常
}

// OrderExceptionResponse 订单转为异常状态响应
type OrderExceptionResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Success bool `json:"success"`
	} `json:"data"`
}

// OrderRemarkRequest 修改订单卖家备注请求
type OrderRemarkRequest struct {
	ShopID int64  `json:"shop_id"` // 店铺编号
	SoID   string `json:"so_id"`   // 订单编号
	Remark string `json:"remark"`  // 卖家备注
}

// OrderRemarkResponse 修改订单卖家备注响应
type OrderRemarkResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Success bool `json:"success"`
	} `json:"data"`
}

// OrderNodeRequest 修改线下备注请求
type OrderNodeRequest struct {
	ShopID int64           `json:"shop_id"` // 店铺编号
	Items  []OrderNodeItem `json:"items"`   // 订单列表
}

// OrderNodeItem 修改线下备注请求项
type OrderNodeItem struct {
	OID  int64  `json:"o_id,omitempty"`  // 内部订单号
	SoID string `json:"so_id,omitempty"` // 线上单号
	Node string `json:"node"`            // 线下备注
}

// OrderNodeResponse 修改线下备注响应
type OrderNodeResponse struct {
	Code      int    `json:"code"`      // 返回码
	IsSuccess bool   `json:"issuccess"` // 是否执行成功
	Msg       string `json:"msg"`       // 返回信息
}

// OrderLabelRequest 修改订单标签请求
type OrderLabelRequest struct {
	OID        int64    `json:"o_id"`        // 内部订单号
	ShopID     int64    `json:"shop_id"`     // 店铺编号
	SoID       string   `json:"so_id"`       // 线上单号
	Labels     []string `json:"labels"`      // 标签列表
	ActionType int      `json:"action_type"` // 操作类型，1=新增，2=移除
	OwnerCoID  int64    `json:"owner_co_id"` // 货主编码（改版三方仓授权情况需要指定货主）
	WmsCoID    int64    `json:"wms_co_id"`   // 仓储编码（货主授权情况下需要指定三方仓）
	CoID       int64    `json:"co_id"`       // 货主编码（新版三方仓授权情况需要指定货主，货主编码传负数）
}

// OrderLabelResponse 修改订单标签响应
type OrderLabelResponse struct {
	Code      int    `json:"code"`      // 状态码
	IsSuccess bool   `json:"issuccess"` // 是否成功
	Msg       string `json:"msg"`       // 执行结果描述
}

// OrderSentRequest 订单发货请求
type OrderSentRequest struct {
	Items []OrderSentItem `json:"items"` // 订单列表
}

// OrderSentItem 订单发货请求项
type OrderSentItem struct {
	OID           int64  `json:"o_id"`             // 内部订单号
	ShopID        int64  `json:"shop_id"`          // 店铺编号
	SoID          string `json:"so_id"`            // 线上单号
	LcName        string `json:"lc_name"`          // 快递公司
	LID           string `json:"l_id"`             // 快递单号；如果选择了跨境物流表示国际运单号
	SendByUsedLid bool   `json:"send_by_used_lid"` // 是否允许传重复物流单号；默认false不重复
	LcID          string `json:"lc_id"`            // 快递公司编码
	IsUnLid       bool   `json:"is_un_lid"`        // 是否跨境物流；选true渠道信息必填
	TrackingCode  string `json:"tracking_code"`    // 渠道编码
	TrackingInfo  string `json:"tracking_info"`    // 渠道名称
	TrackingType  int    `json:"tracking_type"`    // 货代id（货代设置中的ID）；默认值可以传1000
}

// OrderSentResponse 订单发货响应
type OrderSentResponse struct {
	Code int `json:"code"` // 错误码
	// IsSuccess bool   `json:"issuccess"` // 是否执行成功
	Msg  string `json:"msg"` // 执行描述
	Data struct {
		Data []OrderSentResponseItem `json:"data"` // 数据
	} `json:"data"`
}

// OrderSentResponseItem 订单发货响应项
type OrderSentResponseItem struct {
	AsID      int    `json:"as_id"`       // 售后单号
	ID        int    `json:"id"`          // ID
	IsSuccess bool   `json:"issuccess"`   // 是否成功
	Msg       string `json:"msg"`         // 消息
	OID       int    `json:"o_id"`        // 内部订单号
	SoID      string `json:"so_id"`       // 线上单号
	OuterAsID string `json:"outer_as_id"` // 外部售后单号
	Oaid      string `json:"oaid"`        // oaid
	OrderType string `json:"order_type"`  // 订单类型
}

// OrderActionQueryRequest 订单操作日志查询请求
type OrderActionQueryRequest struct {
	ModifiedEnd   string `json:"modified_end,omitempty"`   // 日志结束时间,起始时间和结束时间必须同时存在，时间间隔不能超过1天
	PageIndex     int    `json:"page_index,omitempty"`     // 第几页，从1开始
	PageSize      int    `json:"page_size,omitempty"`      // 默认50，最大不超过500
	ModifiedBegin string `json:"modified_begin,omitempty"` // 日志起始时间,起始时间和结束时间必须同时存在，时间间隔不能超过1天
	OID           int    `json:"o_id,omitempty"`           // 内部订单号
	ActionName    string `json:"action_name"`              // 操作类型（对应返回数据中的name进行筛选）
}

// OrderActionQueryResponse 订单操作日志查询响应
type OrderActionQueryResponse struct {
	Code int    `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data struct {
		PageSize  int                    `json:"page_size"`  // 每页多少条
		PageIndex int                    `json:"page_index"` // 第几页
		DataCount int                    `json:"data_count"` // 总条数
		PageCount int                    `json:"page_count"` // 总页数
		HasNext   bool                   `json:"has_next"`   // 是否有下一页
		Datas     []OrderActionQueryItem `json:"datas"`      // 数据集合
	} `json:"data"`
}

// OrderActionQueryItem 订单操作日志查询项
type OrderActionQueryItem struct {
	OaID        int    `json:"oa_id"`        // 日志id
	OID         int    `json:"o_id"`         // 订单id
	Name        string `json:"name"`         // 操作名称
	Remark      string `json:"remark"`       // 备注
	Created     string `json:"created"`      // 操作时间
	CreatorName string `json:"creator_name"` // 创建人
}

// OrderInfoDB 订单信息数据库模型
type OrderInfoDB struct {
	ID                 int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	OID                int       `gorm:"column:o_id;index;comment:内部订单号" json:"o_id"`                          // 内部订单号
	ShopID             int       `gorm:"column:shop_id;index;comment:店铺编号" json:"shop_id"`                     // 店铺编号
	SoID               string    `gorm:"column:so_id;index;comment:线上单号" json:"so_id"`                         // 线上单号
	OrderDate          string    `gorm:"column:order_date;comment:订单日期" json:"order_date"`                     // 订单日期
	Status             string    `gorm:"column:status;comment:聚水潭订单状态" json:"status"`                          // 聚水潭订单状态
	ShopStatus         string    `gorm:"column:shop_status;comment:自研商城系统订单状态" json:"shop_status"`             // 自研商城系统订单状态
	ShopBuyerID        string    `gorm:"column:shop_buyer_id;comment:买家帐号" json:"shop_buyer_id"`               // 买家帐号
	ReceiverName       string    `gorm:"column:receiver_name;comment:收件人" json:"receiver_name"`                // 收件人
	ReceiverMobile     string    `gorm:"column:receiver_mobile;comment:手机" json:"receiver_mobile"`             // 手机
	ReceiverPhone      string    `gorm:"column:receiver_phone;comment:电话" json:"receiver_phone"`               // 电话
	ReceiverAddress    string    `gorm:"column:receiver_address;comment:收货地址" json:"receiver_address"`         // 收货地址
	ReceiverState      string    `gorm:"column:receiver_state;comment:省" json:"receiver_state"`                // 省
	ReceiverCity       string    `gorm:"column:receiver_city;comment:市" json:"receiver_city"`                  // 市
	ReceiverDistrict   string    `gorm:"column:receiver_district;comment:区" json:"receiver_district"`          // 区
	ReceiverTown       string    `gorm:"column:receiver_town;comment:街道" json:"receiver_town"`                 // 街道
	ReceiverCountry    string    `gorm:"column:receiver_country;comment:国家" json:"receiver_country"`           // 国家
	ReceiverZip        string    `gorm:"column:receiver_zip;comment:邮编" json:"receiver_zip"`                   // 邮编
	ReceiverEmail      string    `gorm:"column:receiver_email;comment:邮箱" json:"receiver_email"`               // 邮箱
	PayAmount          float64   `gorm:"column:pay_amount;comment:应付金额" json:"pay_amount"`                     // 应付金额
	Freight            float64   `gorm:"column:freight;comment:运费" json:"freight"`                             // 运费
	BuyerMessage       string    `gorm:"column:buyer_message;comment:买家留言" json:"buyer_message"`               // 买家留言
	Remark             string    `gorm:"column:remark;comment:卖家备注" json:"remark"`                             // 卖家备注
	Node               string    `gorm:"column:node;comment:线下备注" json:"node"`                                 // 线下备注
	LID                string    `gorm:"column:l_id;comment:快递单号" json:"l_id"`                                 // 快递单号
	LogisticsCompany   string    `gorm:"column:logistics_company;comment:快递公司" json:"logistics_company"`       // 快递公司
	LcID               string    `gorm:"column:lc_id;comment:快递公司编码" json:"lc_id"`                             // 快递公司编码
	SendDate           string    `gorm:"column:send_date;comment:发货日期" json:"send_date"`                       // 发货日期
	Type               string    `gorm:"column:type;comment:订单类型" json:"type"`                                 // 订单类型
	OrderFrom          string    `gorm:"column:order_from;comment:订单来源" json:"order_from"`                     // 订单来源
	PlatformStatus     string    `gorm:"column:platform_status;comment:平台订单状态" json:"platform_status"`         // 平台订单状态
	PaidAmount         float64   `gorm:"column:paid_amount;comment:实际支付金额" json:"paid_amount"`                 // 实际支付金额
	BuyerPaidAmount    float64   `gorm:"column:buyer_paid_amount;comment:买家实付" json:"buyer_paid_amount"`       // 买家实付
	SellerIncomeAmount float64   `gorm:"column:seller_income_amount;comment:卖家实收" json:"seller_income_amount"` // 卖家实收
	Labels             string    `gorm:"column:labels;comment:多标签" json:"labels"`                              // 多标签
	SellerFlag         int       `gorm:"column:seller_flag;comment:旗帜" json:"seller_flag"`                     // 旗帜
	Created            string    `gorm:"column:created;comment:订单创建时间" json:"created"`                         // 订单创建时间
	Modified           string    `gorm:"column:modified;comment:修改时间" json:"modified"`                         // 修改时间
	CreatedAt          time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`                   // 记录创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`                   // 记录更新时间
}

// TableName 指定表名
func (OrderInfoDB) TableName() string {
	return "jst_order_info"
}

// OrderItemDB 订单商品明细数据库模型
type OrderItemDB struct {
	ID                 int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	OID                int       `gorm:"column:o_id;index;comment:内部订单号" json:"o_id"`                          // 内部订单号
	OiID               int       `gorm:"column:oi_id;index;comment:子订单号" json:"oi_id"`                         // 子订单号
	SkuID              string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`                       // 商品编码
	IID                string    `gorm:"column:i_id;comment:款式编码" json:"i_id"`                                 // 款式编码
	ShopSkuID          string    `gorm:"column:shop_sku_id;comment:店铺商品编码" json:"shop_sku_id"`                 // 店铺商品编码
	ShopIID            string    `gorm:"column:shop_i_id;comment:店铺款式编码" json:"shop_i_id"`                     // 店铺款式编码
	PropertiesValue    string    `gorm:"column:properties_value;comment:商品属性" json:"properties_value"`         // 商品属性
	Amount             float64   `gorm:"column:amount;comment:总额" json:"amount"`                               // 总额
	BasePrice          float64   `gorm:"column:base_price;comment:原价" json:"base_price"`                       // 原价
	Qty                int       `gorm:"column:qty;comment:数量" json:"qty"`                                     // 数量
	Name               string    `gorm:"column:name;comment:商品名称" json:"name"`                                 // 商品名称
	Price              float64   `gorm:"column:price;comment:单价" json:"price"`                                 // 单价
	OuterOiID          string    `gorm:"column:outer_oi_id;comment:子订单号" json:"outer_oi_id"`                   // 子订单号
	RefundID           string    `gorm:"column:refund_id;comment:退款单号" json:"refund_id"`                       // 退款单号
	RefundQty          int       `gorm:"column:refund_qty;comment:退款数量" json:"refund_qty"`                     // 退款数量
	RefundStatus       string    `gorm:"column:refund_status;comment:退款状态" json:"refund_status"`               // 退款状态
	RawSoID            string    `gorm:"column:raw_so_id;comment:原始线上单号" json:"raw_so_id"`                     // 原始线上单号
	IsPresale          bool      `gorm:"column:is_presale;comment:是否预售" json:"is_presale"`                     // 是否预售
	IsGift             bool      `gorm:"column:is_gift;comment:是否赠品" json:"is_gift"`                           // 是否赠品
	ItemStatus         string    `gorm:"column:item_status;comment:商品状态" json:"item_status"`                   // 商品状态
	Pic                string    `gorm:"column:pic;comment:商品图片" json:"pic"`                                   // 商品图片
	SkuType            string    `gorm:"column:sku_type;comment:商品类型" json:"sku_type"`                         // 商品类型
	Remark             string    `gorm:"column:remark;comment:备注" json:"remark"`                               // 备注
	BuyerPaidAmount    float64   `gorm:"column:buyer_paid_amount;comment:买家实付" json:"buyer_paid_amount"`       // 买家实付
	SellerIncomeAmount float64   `gorm:"column:seller_income_amount;comment:卖家实收" json:"seller_income_amount"` // 卖家实收
	ReferrerID         string    `gorm:"column:referrer_id;comment:主播ID" json:"referrer_id"`                   // 主播ID
	ItemExtData        string    `gorm:"column:item_ext_data;comment:订单明细扩展字段" json:"item_ext_data"`           // 订单明细扩展字段
	CreatedAt          time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`                   // 记录创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`                   // 记录更新时间
}

// TableName 指定表名
func (OrderItemDB) TableName() string {
	return "jst_order_item"
}

// OrderPayDB 订单支付信息数据库模型
type OrderPayDB struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	OID          int       `gorm:"column:o_id;index;comment:内部订单号" json:"o_id"`              // 内部订单号
	PayID        string    `gorm:"column:pay_id;comment:支付单ID" json:"pay_id"`                // 支付单ID
	OuterPayID   string    `gorm:"column:outer_pay_id;comment:外部支付单号" json:"outer_pay_id"`   // 外部支付单号
	PayDate      string    `gorm:"column:pay_date;comment:支付时间" json:"pay_date"`             // 支付时间
	Amount       float64   `gorm:"column:amount;comment:支付金额" json:"amount"`                 // 支付金额
	Payment      string    `gorm:"column:payment;comment:支付方式" json:"payment"`               // 支付方式
	BuyerAccount string    `gorm:"column:buyer_account;comment:买家支付账号" json:"buyer_account"` // 买家支付账号
	IsOrderPay   bool      `gorm:"column:is_order_pay;comment:是否支付" json:"is_order_pay"`     // 是否支付
	Status       string    `gorm:"column:status;comment:支付状态" json:"status"`                 // 支付状态
	PayType      string    `gorm:"column:pay_type;comment:支付类型" json:"pay_type"`             // 支付类型
	CreatedAt    time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`       // 记录创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`       // 记录更新时间
}

// TableName 指定表名
func (OrderPayDB) TableName() string {
	return "jst_order_pay"
}

// OrderActionDB 订单操作日志数据库模型
type OrderActionDB struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	OaID        int       `gorm:"column:oa_id;index;comment:日志id" json:"oa_id"`        // 日志id
	OID         int       `gorm:"column:o_id;index;comment:订单id" json:"o_id"`          // 订单id
	Name        string    `gorm:"column:name;comment:操作名称" json:"name"`                // 操作名称
	Remark      string    `gorm:"column:remark;comment:备注" json:"remark"`              // 备注
	Created     string    `gorm:"column:created;comment:操作时间" json:"created"`          // 操作时间
	CreatorName string    `gorm:"column:creator_name;comment:创建人" json:"creator_name"` // 创建人
	CreatedAt   time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`  // 记录创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`  // 记录更新时间
}

// TableName 指定表名
func (OrderActionDB) TableName() string {
	return "jst_order_action"
}
