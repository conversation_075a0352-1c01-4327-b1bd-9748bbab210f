package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Shopify/sarama"
	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/internal/config"
	"yekaitai/pkg/adapters/medlinker"
	"yekaitai/pkg/infra/kafka"
	"yekaitai/wx_internal/modules/consultation/model"
)

// ChatStreamConsumer 聊天流消费者
type ChatStreamConsumer struct {
	chatStreamModel  model.ChatStreamModel
	chatSessionModel model.ChatSessionModel
}

// NewChatStreamConsumer 创建聊天流消费者
func NewChatStreamConsumer(chatStreamModel model.ChatStreamModel, chatSessionModel model.ChatSessionModel) *ChatStreamConsumer {
	return &ChatStreamConsumer{
		chatStreamModel:  chatStreamModel,
		chatSessionModel: chatSessionModel,
	}
}

// Setup 实现 sarama.ConsumerGroupHandler 接口
func (c *ChatStreamConsumer) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

// Cleanup 实现 sarama.ConsumerGroupHandler 接口
func (c *ChatStreamConsumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim 处理消息
func (c *ChatStreamConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for msg := range claim.Messages() {
		logx.Infof("收到Kafka消息: topic=%s, partition=%d, offset=%d", msg.Topic, msg.Partition, msg.Offset)

		// 解析消息
		var kafkaMessage medlinker.KafkaMessage
		if err := json.Unmarshal(msg.Value, &kafkaMessage); err != nil {
			logx.Errorf("解析Kafka消息失败: %v", err)
			session.MarkMessage(msg, "")
			continue
		}

		// 保存到数据库
		stream := &model.ChatStream{
			SessionID:  kafkaMessage.SessionID,
			ChunkIndex: kafkaMessage.ChunkIndex,
			Content:    kafkaMessage.Content,
			IsFinal:    kafkaMessage.IsFinal,
			CreatedAt:  time.Unix(kafkaMessage.Timestamp, 0),
		}

		err := c.chatStreamModel.Insert(context.Background(), stream)
		if err != nil {
			logx.Errorf("保存聊天流数据失败: %v", err)
			// 失败继续处理下一条
		}

		// 如果是最终消息，则合并会话内容
		if kafkaMessage.IsFinal {
			go c.mergeSession(kafkaMessage.SessionID)
		}

		// 标记消息已处理
		session.MarkMessage(msg, "")
	}

	return nil
}

// 合并会话内容
func (c *ChatStreamConsumer) mergeSession(sessionID string) {
	ctx := context.Background()

	// 查询所有分块数据
	streams, err := c.chatStreamModel.FindBySessionID(ctx, sessionID)
	if err != nil {
		logx.Errorf("查询聊天流数据失败: %v", err)
		return
	}

	if len(streams) == 0 {
		logx.Errorf("未找到会话数据: %s", sessionID)
		return
	}

	// 按msgGroupId分组并按顺序合并内容
	groupMap := make(map[string][]*model.ChatStream)
	for _, stream := range streams {
		// 解析Content中的msgGroupId
		var medResponse medlinker.MedResponseFrame
		if err := json.Unmarshal([]byte(stream.Content), &medResponse); err != nil {
			logx.Errorf("解析医联响应数据失败: %v", err)
			continue
		}

		msgGroupID := medResponse.MsgGroupID
		if msgGroupID == "" {
			logx.Errorf("消息组ID为空: %s", stream.Content)
			continue
		}

		groupMap[msgGroupID] = append(groupMap[msgGroupID], stream)
	}

	// 合并每个消息组的内容
	var fullContent strings.Builder
	for _, group := range groupMap {
		// 解析组内每条消息，提取实际内容
		var groupContent strings.Builder
		for _, stream := range group {
			var medResponse medlinker.MedResponseFrame
			if err := json.Unmarshal([]byte(stream.Content), &medResponse); err != nil {
				continue
			}

			if len(medResponse.Data.Answer) > 0 {
				answer := medResponse.Data.Answer[0]

				// 根据消息类型处理内容
				if answer.MsgType == 1 { // 普通文本
					content := ""
					if err := json.Unmarshal(answer.Content, &content); err == nil {
						groupContent.WriteString(content)
					}
				} else if answer.MsgType == 366 { // 病历卡片
					fullContent.WriteString(fmt.Sprintf("【病历卡片】\n%s\n\n", stream.Content))
				}
			}
		}

		// 添加到完整内容
		if groupContent.Len() > 0 {
			fullContent.WriteString(groupContent.String())
			fullContent.WriteString("\n\n")
		}
	}

	// 查询会话是否存在
	session, err := c.chatSessionModel.FindBySessionID(ctx, sessionID)
	if err != nil {
		logx.Errorf("查询聊天会话失败: %v", err)
		return
	}

	// 更新或创建会话
	if session != nil {
		// 更新会话内容
		session.FullContent = fullContent.String()
		session.EndTime = time.Now()

		if err := c.chatSessionModel.Update(ctx, session); err != nil {
			logx.Errorf("更新聊天会话失败: %v", err)
		}
	} else {
		// 创建新会话
		session = &model.ChatSession{
			SessionID:   sessionID,
			UserID:      0, // 需要从上下文或其他地方获取
			FullContent: fullContent.String(),
			StartTime:   streams[0].CreatedAt,
			EndTime:     time.Now(),
		}

		if err := c.chatSessionModel.Insert(ctx, session); err != nil {
			logx.Errorf("创建聊天会话失败: %v", err)
		}
	}

	logx.Infof("会话合并完成: %s, 内容长度: %d", sessionID, len(fullContent.String()))
}

// 启动消费者服务
func StartChatStreamConsumer(chatStreamModel model.ChatStreamModel, chatSessionModel model.ChatSessionModel, kafkaConfig config.KafkaConfig) {
	// 检查Kafka配置是否有效
	if len(kafkaConfig.Brokers) == 0 {
		logx.Info("Kafka配置为空，跳过聊天流消费者启动")
		return
	}

	consumer := NewChatStreamConsumer(chatStreamModel, chatSessionModel)

	// 初始化消费者
	err := kafka.InitConsumer(kafkaConfig, consumer)
	if err != nil {
		logx.Errorf("初始化Kafka消费者失败: %v", err)
		return
	}

	logx.Info("聊天流消费者服务已启动")
}
