package bootstrap

import (
	"log"
	"time"

	"yekaitai/pkg/infra/mysql"
)

// AddSpecialActivityCoinRules 添加专项活动积分奖励规则
func AddSpecialActivityCoinRules() {
	db := mysql.Master()

	// 检查是否已存在专项活动积分规则
	var count int64
	err := db.Table("coin_rules").
		Where("activity_type = ?", "SPECIAL_ACTIVITY_SIGNUP").
		Count(&count).Error

	if err != nil {
		log.Printf("检查专项活动积分规则失败: %v", err)
		return
	}

	if count > 0 {
		log.Println("专项活动积分规则已存在，跳过创建")
		return
	}

	// 获取所有用户等级
	var userLevels []struct {
		ID uint `json:"id"`
	}

	err = db.Table("user_level_rules").Select("id").Find(&userLevels).Error
	if err != nil {
		log.Printf("查询用户等级失败: %v", err)
		return
	}

	if len(userLevels) == 0 {
		log.Println("未找到用户等级，跳过创建专项活动积分规则")
		return
	}

	// 为每个用户等级创建专项活动积分规则
	now := time.Now()
	for _, level := range userLevels {
		rule := map[string]interface{}{
			"user_level_id": level.ID,
			"rule_type":     "ACTIVITY",
			"activity_type": "SPECIAL_ACTIVITY_SIGNUP",
			"coins_awarded": 10, // 专项活动额外奖励10积分
			"description":   "专项活动报名额外奖励",
			"enabled":       true,
			"created_at":    now,
			"updated_at":    now,
		}

		err = db.Table("coin_rules").Create(rule).Error
		if err != nil {
			log.Printf("创建专项活动积分规则失败: userLevelID=%d, error=%v", level.ID, err)
		} else {
			log.Printf("创建专项活动积分规则成功: userLevelID=%d", level.ID)
		}
	}

	log.Println("专项活动积分规则创建完成")
}

// 在 main.go 中调用此函数进行配置
// func main() {
//     // 初始化数据库连接
//     mysql.InitDB()
//
//     // 添加专项活动积分规则
//     AddSpecialActivityCoinRules()
// }
