package mysql

import (
	"context"
	"errors"
	"log"
	"os"
	"sync"
	"time"

	"yekaitai/internal/config"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	glogger "gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var (
	// DB 全局GORM数据库连接
	db *gorm.DB
	// 主库连接
	masterDB *gorm.DB
	// 从库连接池
	slaveDBs []*gorm.DB
	// 读写锁
	lock sync.RWMutex

	// ErrExhausted 资源耗尽错误
	ErrExhausted = errors.New("resource exhausted")
)

// LogxGormLogger 自定义GORM日志器，集成到logx
type LogxGormLogger struct {
	LogLevel glogger.LogLevel
}

// NewLogxGormLogger 创建新的logx GORM日志器
func NewLogxGormLogger(level glogger.LogLevel) *LogxGormLogger {
	return &LogxGormLogger{
		LogLevel: level,
	}
}

// LogMode 设置日志级别
func (l *LogxGormLogger) LogMode(level glogger.LogLevel) glogger.Interface {
	return &LogxGormLogger{LogLevel: level}
}

// Info 信息日志
func (l *LogxGormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= glogger.Info {
		logx.Infof("[GORM] "+msg, data...)
	}
}

// Warn 警告日志
func (l *LogxGormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= glogger.Warn {
		logx.Errorf("[GORM] "+msg, data...)
	}
}

// Error 错误日志
func (l *LogxGormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= glogger.Error {
		logx.Errorf("[GORM] "+msg, data...)
	}
}

// Trace SQL执行日志
func (l *LogxGormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= glogger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	switch {
	case err != nil && l.LogLevel >= glogger.Error:
		// SQL执行错误
		logx.Errorf("[GORM] SQL错误 [%.3fms] [rows:%d] %s | 错误: %v",
			float64(elapsed.Nanoseconds())/1e6, rows, sql, err)
	case elapsed > 200*time.Millisecond && l.LogLevel >= glogger.Warn:
		// 慢查询 (超过200ms)
		logx.Errorf("[GORM] 慢查询 [%.3fms] [rows:%d] %s",
			float64(elapsed.Nanoseconds())/1e6, rows, sql)
	case l.LogLevel >= glogger.Info:
		// 正常SQL
		logx.Infof("[GORM] SQL执行 [%.3fms] [rows:%d] %s",
			float64(elapsed.Nanoseconds())/1e6, rows, sql)
	}
}

// Init 初始化MySQL连接
func Init(cfg config.MysqlConfig) error {
	var err error

	// 连接主库
	masterDB, err = connect(cfg.Master.Addr)
	if err != nil {
		return errors.New("连接主库失败: " + err.Error())
	}

	// 设置为全局DB
	db = masterDB

	return nil
}

// getLogLevel 根据环境获取GORM日志级别
func getLogLevel() glogger.LogLevel {
	// 优先从现有环境变量获取，保持与项目一致
	env := os.Getenv("RUN_ENV")
	if env == "" {
		env = os.Getenv("DEPLOY_ENV")
	}
	if env == "" {
		env = os.Getenv("ENV_FLAG")
	}

	switch env {
	case "production", "prod":
		// 生产环境：只显示错误
		return glogger.Error
	case "test":
		// 测试环境：显示错误和慢查询
		return glogger.Warn
	case "development", "dev", "":
		// 开发环境：显示所有SQL（默认）
		return glogger.Info
	default:
		// 其他环境：只显示错误
		return glogger.Error
	}
}

// connect 连接数据库
func connect(addr string) (*gorm.DB, error) {
	// 配置GORM
	config := &gorm.Config{
		Logger: NewLogxGormLogger(getLogLevel()), // 使用自定义logx日志器
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
		// 禁用外键约束
		DisableForeignKeyConstraintWhenMigrating: true,
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(addr), config)
	if err != nil {
		return nil, err
	}

	// 获取底层SQL连接以配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)           // 空闲连接池大小
	sqlDB.SetMaxOpenConns(100)          // 最大连接数
	sqlDB.SetConnMaxLifetime(time.Hour) // 连接最大生命周期

	log.Println("MySQL连接初始化成功")
	return db, nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return db
}

// Master 返回主数据库连接
func Master() *gorm.DB {
	return masterDB
}

// Slave 返回从数据库连接(目前与主库相同)
func Slave() *gorm.DB {
	return masterDB
}

// Close 关闭数据库连接
func Close() error {
	if db != nil {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// AutoMigrate 自动迁移表结构
func AutoMigrate(models ...interface{}) error {
	if masterDB == nil {
		return errors.New("数据库未初始化")
	}
	return masterDB.AutoMigrate(models...)
}

// BeginTx 开始一个数据库事务
// 返回事务对象和错误
func BeginTx() (*gorm.DB, error) {
	if masterDB == nil {
		return nil, errors.New("数据库未初始化")
	}
	return masterDB.Begin(), nil
}
