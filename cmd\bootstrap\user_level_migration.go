package bootstrap

import (
	"fmt"
	userModel "yekaitai/internal/modules/user_level/model"
	wxUserModel "yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// MigrateUserLevelTables 执行用户等级管理模块表结构迁移
func MigrateUserLevelTables() error {
	logx.Info("开始用户等级表迁移...")

	// 获取数据库连接
	db := mysql.Master()

	// 创建用户等级规则表
	if err := db.AutoMigrate(&userModel.UserLevelRule{}); err != nil {
		logx.Errorf("用户等级规则表迁移失败: %v", err)
		return fmt.Errorf("用户等级规则表迁移失败: %v", err)
	}
	logx.Info("用户等级规则表迁移成功")

	// 创建用户等级升级日志表
	if err := db.AutoMigrate(&userModel.UserLevelUpgradeLog{}); err != nil {
		logx.Errorf("用户等级升级日志表迁移失败: %v", err)
		return fmt.Errorf("用户等级升级日志表迁移失败: %v", err)
	}
	logx.Info("用户等级升级日志表迁移成功")

	// 为wx_user表添加用户等级相关字段
	if err := addUserLevelFieldsToWxUser(db); err != nil {
		logx.Errorf("为wx_user表添加用户等级字段失败: %v", err)
		return fmt.Errorf("为wx_user表添加用户等级字段失败: %v", err)
	}
	logx.Info("wx_user表用户等级字段添加成功")

	// 检查是否已存在数据
	var count int64
	if err := db.Model(&userModel.UserLevelRule{}).Count(&count).Error; err != nil {
		logx.Errorf("检查用户等级规则数据失败: %v", err)
		return fmt.Errorf("检查用户等级规则数据失败: %v", err)
	}

	// 如果没有数据则插入初始数据
	if count == 0 {
		if err := insertInitialUserLevels(db); err != nil {
			logx.Errorf("插入初始用户等级数据失败: %v", err)
			return fmt.Errorf("插入初始用户等级数据失败: %v", err)
		}
		logx.Info("初始用户等级数据插入成功")
	} else {
		logx.Info("用户等级数据已存在，跳过初始数据插入")
	}

	logx.Info("用户等级表迁移完成")
	return nil
}

// addUserLevelFieldsToWxUser 为wx_user表添加用户等级相关字段
func addUserLevelFieldsToWxUser(db *gorm.DB) error {
	// 检查字段是否已存在，避免重复添加
	if db.Migrator().HasColumn(&wxUserModel.WxUser{}, "user_level_id") {
		logx.Info("user_level_id字段已存在")
	} else {
		if err := db.Migrator().AddColumn(&wxUserModel.WxUser{}, "user_level_id"); err != nil {
			return fmt.Errorf("添加user_level_id字段失败: %v", err)
		}
		logx.Info("user_level_id字段添加成功")
	}


	// 创建索引
	if !db.Migrator().HasIndex(&wxUserModel.WxUser{}, "idx_user_level_id") {
		if err := db.Migrator().CreateIndex(&wxUserModel.WxUser{}, "idx_user_level_id"); err != nil {
			logx.Errorf("创建user_level_id索引失败: %v", err)
		} else {
			logx.Info("user_level_id索引创建成功")
		}
	}



	// 设置默认值 - 为所有没有用户等级的用户设置为等级1
	if err := db.Model(&wxUserModel.WxUser{}).Where("user_level_id IS NULL").Update("user_level_id", 1).Error; err != nil {
		logx.Errorf("设置默认用户等级失败: %v", err)
	} else {
		logx.Info("为所有用户设置默认等级1")
	}

	return nil
}

// insertInitialUserLevels 插入初始等级数据
func insertInitialUserLevels(db *gorm.DB) error {
	// 检查是否已经有数据
	var count int64
	if err := db.Model(&userModel.UserLevelRule{}).Count(&count).Error; err != nil {
		return err
	}

	// 如果已有数据则跳过
	if count > 0 {
		logx.Info("用户等级数据已存在，跳过初始化")
		return nil
	}

	// 初始化6个等级，按照累积消费递增的逻辑
	initialLevels := []*userModel.UserLevelRule{
		{
			LevelName:             "LV1",
			LevelOrder:            1,
			Description:           "新用户等级 - 注册登录成功即可获得",
			RequireRegister:       true,
			RequirePatientProfile: false,
			RequireConsumption:    false,
			ConsumptionAmount:     0,
		},
		{
			LevelName:             "LV2",
			LevelOrder:            2,
			Description:           "进阶用户等级 - 需要完善就诊人档案及累积消费满2000元",
			RequireRegister:       true,
			RequirePatientProfile: true,
			RequireConsumption:    true,
			ConsumptionAmount:     2000,
		},
		{
			LevelName:             "LV3",
			LevelOrder:            3,
			Description:           "高级用户等级 - 累积消费满4000元",
			RequireRegister:       true,
			RequirePatientProfile: true,
			RequireConsumption:    true,
			ConsumptionAmount:     4000,
		},
		{
			LevelName:             "LV4",
			LevelOrder:            4,
			Description:           "白金用户等级 - 累积消费满8000元",
			RequireRegister:       true,
			RequirePatientProfile: true,
			RequireConsumption:    true,
			ConsumptionAmount:     8000,
		},
		{
			LevelName:             "LV5",
			LevelOrder:            5,
			Description:           "钻石用户等级 - 累积消费满15000元",
			RequireRegister:       true,
			RequirePatientProfile: true,
			RequireConsumption:    true,
			ConsumptionAmount:     15000,
		},
		{
			LevelName:             "LV6",
			LevelOrder:            6,
			Description:           "VIP用户等级 - 累积消费满30000元",
			RequireRegister:       true,
			RequirePatientProfile: true,
			RequireConsumption:    true,
			ConsumptionAmount:     30000,
		},
	}

	// 批量插入
	if err := db.Create(&initialLevels).Error; err != nil {
		return err
	}

	logx.Info("已插入6个初始用户等级")
	return nil
}
