package handler

import (
	"net/http"

	"yekaitai/internal/modules/admin/model"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// RegionHandler 行政区域处理器
type RegionHandler struct {
	db         *gorm.DB
	regionRepo model.RegionRepository
}

// NewRegionHandler 创建行政区域处理器
func NewRegionHandler(db *gorm.DB) *RegionHandler {
	return &RegionHandler{
		db:         db,
		regionRepo: model.NewRegionRepository(db),
	}
}

// GetProvinces 获取所有省份
func (h *RegionHandler) GetProvinces(w http.ResponseWriter, r *http.Request) {
	provinces, err := h.regionRepo.GetProvinces()
	if err != nil {
		logx.Errorf("获取省份列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取省份列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(provinces, "获取省份列表成功"))
}

// GetCities 获取指定省份的所有城市
func (h *RegionHandler) GetCities(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取provinceCode
	vars := r.URL.Query()
	provinceCode := vars.Get("provinceCode")
	if provinceCode == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "省份编码不能为空"))
		return
	}

	cities, err := h.regionRepo.GetCities(provinceCode)
	if err != nil {
		logx.Errorf("获取城市列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取城市列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(cities, "获取城市列表成功"))
}

// GetAreas 获取指定城市的所有区县
func (h *RegionHandler) GetAreas(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取cityCode
	vars := r.URL.Query()
	cityCode := vars.Get("cityCode")
	if cityCode == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "城市编码不能为空"))
		return
	}

	areas, err := h.regionRepo.GetAreas(cityCode)
	if err != nil {
		logx.Errorf("获取区县列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取区县列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(areas, "获取区县列表成功"))
}

// GetRegionTree 获取地区树形结构
// func (h *RegionHandler) GetRegionTree(w http.ResponseWriter, r *http.Request) {
// 	// 获取省份列表
// 	provinces, err := h.regionRepo.GetProvinces()
// 	if err != nil {
// 		logx.Errorf("获取省份列表失败: %v", err)
// 		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取省份列表失败"))
// 		return
// 	}

// 	var tree []*model.RegionTree
// 	for _, province := range provinces {
// 		// 获取省份下的城市
// 		cities, err := h.regionRepo.GetCities(province.Code)
// 		if err != nil {
// 			logx.Errorf("获取城市列表失败: %v", err)
// 			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取城市列表失败"))
// 			return
// 		}

// 		provinceNode := &model.RegionTree{
// 			Region:   province,
// 			Children: []*model.RegionTree{},
// 		}

// 		for _, city := range cities {
// 			// 获取城市下的区县
// 			areas, err := h.regionRepo.GetAreas(city.Code)
// 			if err != nil {
// 				logx.Errorf("获取区县列表失败: %v", err)
// 				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取区县列表失败"))
// 				return
// 			}

// 			cityNode := &model.RegionTree{
// 				Region:   city,
// 				Children: []*model.RegionTree{},
// 			}

// 			for _, area := range areas {
// 				areaNode := &model.RegionTree{
// 					Region:   area,
// 					Children: []*model.RegionTree{},
// 				}
// 				cityNode.Children = append(cityNode.Children, areaNode)
// 			}

// 			provinceNode.Children = append(provinceNode.Children, cityNode)
// 		}

// 		tree = append(tree, provinceNode)
// 	}

// 	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(tree, "获取地区树形结构成功"))
// }

// GetTowns 获取指定区县的所有乡级（乡镇、街道）
func (h *RegionHandler) GetTowns(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取areaCode
	vars := r.URL.Query()
	areaCode := vars.Get("areaCode")
	if areaCode == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "区县编码不能为空"))
		return
	}

	towns, err := h.regionRepo.GetTowns(areaCode)
	if err != nil {
		logx.Errorf("获取乡镇列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取乡镇列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(towns, "获取乡镇列表成功"))
}

// GetVillages 获取指定乡镇的所有村级（村委会、居委会）
func (h *RegionHandler) GetVillages(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取townCode
	vars := r.URL.Query()
	townCode := vars.Get("townCode")
	if townCode == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "乡镇编码不能为空"))
		return
	}

	villages, err := h.regionRepo.GetVillages(townCode)
	if err != nil {
		logx.Errorf("获取村庄列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取村庄列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(villages, "获取村庄列表成功"))
}
