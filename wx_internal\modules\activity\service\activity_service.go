package service

import (
	"fmt"
	"time"

	"yekaitai/internal/modules/content/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// ActivityService 活动服务
type ActivityService struct{}

// NewActivityService 创建活动服务
func NewActivityService() *ActivityService {
	return &ActivityService{}
}

// CheckActivityExpired 检查活动是否过期并自动退款
func (s *ActivityService) CheckActivityExpired() error {
	logx.Info("开始检查过期活动")

	// 查询所有已报名但活动已过期的订单
	var orders []model.ContentSignUpOrder
	err := mysql.Master().
		Table("t_content_sign_up_orders o").
		Joins("JOIN t_contents c ON o.content_id = c.id").
		Where("o.status = ? AND c.sign_up_deadline < ? AND o.verification_time IS NULL", 1, time.Now()).
		Find(&orders).Error

	if err != nil {
		logx.Errorf("查询过期活动订单失败: %v", err)
		return err
	}

	logx.Infof("找到 %d 个过期活动订单", len(orders))

	// 处理每个过期订单
	for _, order := range orders {
		if err := s.processExpiredOrder(&order); err != nil {
			logx.Errorf("处理过期订单失败: orderID=%d, error=%v", order.ID, err)
			continue
		}
	}

	return nil
}

// processExpiredOrder 处理过期订单
func (s *ActivityService) processExpiredOrder(order *model.ContentSignUpOrder) error {
	logx.Infof("处理过期订单: orderID=%d, orderNo=%s", order.ID, order.OrderNo)

	// 更新订单状态为已过期已退款
	updates := map[string]interface{}{
		"status":     4, // 已过期已退款
		"updated_at": time.Now(),
	}

	// 如果是付费活动，设置退款信息
	if order.Amount > 0 {
		updates["refund_time"] = time.Now()
		updates["refund_amount"] = order.Amount
		
		// 这里应该调用实际的退款接口
		if err := s.processRefund(order); err != nil {
			logx.Errorf("退款处理失败: orderID=%d, error=%v", order.ID, err)
			return err
		}
	}

	// 更新订单状态
	if err := mysql.Master().Model(order).Updates(updates).Error; err != nil {
		logx.Errorf("更新订单状态失败: orderID=%d, error=%v", order.ID, err)
		return err
	}

	logx.Infof("过期订单处理完成: orderID=%d", order.ID)
	return nil
}

// processRefund 处理退款
func (s *ActivityService) processRefund(order *model.ContentSignUpOrder) error {
	// 这里应该调用实际的退款接口
	// 暂时只记录日志
	logx.Infof("处理退款: orderID=%d, amount=%d", order.ID, order.Amount)
	
	// TODO: 集成实际的退款接口
	// 例如微信支付退款接口
	
	return nil
}

// UpdateActivitySignUpCount 更新活动报名人数
func (s *ActivityService) UpdateActivitySignUpCount(contentID uint) error {
	// 统计该活动的有效报名人数（状态为1的订单）
	var count int64
	err := mysql.Slave().Model(&model.ContentSignUpOrder{}).
		Where("content_id = ? AND status = ?", contentID, 1).
		Count(&count).Error
	
	if err != nil {
		return fmt.Errorf("统计报名人数失败: %v", err)
	}

	// 更新活动的报名人数
	err = mysql.Master().Model(&model.Content{}).
		Where("id = ?", contentID).
		Update("sign_up_count", count).Error
	
	if err != nil {
		return fmt.Errorf("更新报名人数失败: %v", err)
	}

	logx.Infof("更新活动报名人数: contentID=%d, count=%d", contentID, count)
	return nil
}

// MarkActivityAsShown 标记活动为已显示（用于首次登录弹框）
func (s *ActivityService) MarkActivityAsShown(contentID uint) error {
	err := mysql.Master().Model(&model.Content{}).
		Where("id = ?", contentID).
		Updates(map[string]interface{}{
			"is_new_activity": false,
			"updated_at":      time.Now(),
		}).Error
	
	if err != nil {
		return fmt.Errorf("标记活动为已显示失败: %v", err)
	}

	logx.Infof("标记活动为已显示: contentID=%d", contentID)
	return nil
}

// GetActivityStatistics 获取活动统计信息
func (s *ActivityService) GetActivityStatistics(contentID uint) (map[string]interface{}, error) {
	// 统计报名人数
	var signUpCount int64
	err := mysql.Slave().Model(&model.ContentSignUpOrder{}).
		Where("content_id = ? AND status IN (1, 4)", contentID).
		Count(&signUpCount).Error
	
	if err != nil {
		return nil, fmt.Errorf("统计报名人数失败: %v", err)
	}

	// 统计已核销人数
	var verifiedCount int64
	err = mysql.Slave().Model(&model.ContentSignUpOrder{}).
		Where("content_id = ? AND verification_time IS NOT NULL", contentID).
		Count(&verifiedCount).Error
	
	if err != nil {
		return nil, fmt.Errorf("统计核销人数失败: %v", err)
	}

	// 统计取消人数
	var cancelledCount int64
	err = mysql.Slave().Model(&model.ContentSignUpOrder{}).
		Where("content_id = ? AND status IN (2, 3)", contentID).
		Count(&cancelledCount).Error
	
	if err != nil {
		return nil, fmt.Errorf("统计取消人数失败: %v", err)
	}

	return map[string]interface{}{
		"sign_up_count":   signUpCount,
		"verified_count":  verifiedCount,
		"cancelled_count": cancelledCount,
	}, nil
}

// ValidateSignUpData 验证报名数据
func (s *ActivityService) ValidateSignUpData(name, phone string) error {
	if name == "" {
		return fmt.Errorf("姓名不能为空")
	}
	
	if len(name) > 50 {
		return fmt.Errorf("姓名长度不能超过50个字符")
	}
	
	if phone == "" {
		return fmt.Errorf("手机号不能为空")
	}
	
	// 简单的手机号格式验证
	if len(phone) != 11 {
		return fmt.Errorf("手机号格式不正确")
	}
	
	return nil
}
