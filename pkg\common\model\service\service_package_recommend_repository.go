package service

import (
	"time"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// ServicePackageRecommendRepository 推荐服务套餐仓库接口
type ServicePackageRecommendRepository interface {
	// 基础操作
	Create(recommend *ServicePackageRecommend) error
	Update(recommend *ServicePackageRecommend) error
	Delete(id uint) error
	FindByID(id uint) (*ServicePackageRecommend, error)
	List(params *ServicePackageRecommendQueryParams) ([]*ServicePackageRecommendInfo, int64, error)

	// 业务操作
	SetTop(id uint) error
	CancelTop(id uint) error
	GetTopRecommend() (*ServicePackageRecommend, error)
	CountRecommends() (int64, error)
	BatchCreate(recommends []*ServicePackageRecommend) error
	BatchDelete(ids []uint) error

	// 服务套餐选择相关
	GetServicePackageSelectList(params *ServicePackageSelectQueryParams) ([]*ServicePackageSelectInfo, int64, error)
	IsRecommended(servicePackageID uint) bool

	// 推荐逻辑相关
	GetRecommendedServicePackages(limit int) ([]*ServicePackageRecommend, error)
}

// servicePackageRecommendRepository 推荐服务套餐仓库实现
type servicePackageRecommendRepository struct {
	db *gorm.DB
}

// NewServicePackageRecommendRepository 创建推荐服务套餐仓库
func NewServicePackageRecommendRepository(db *gorm.DB) ServicePackageRecommendRepository {
	return &servicePackageRecommendRepository{
		db: db,
	}
}

// Create 创建推荐服务套餐
func (r *servicePackageRecommendRepository) Create(recommend *ServicePackageRecommend) error {
	return mysql.Master().Create(recommend).Error
}

// Update 更新推荐服务套餐
func (r *servicePackageRecommendRepository) Update(recommend *ServicePackageRecommend) error {
	return mysql.Master().Save(recommend).Error
}

// Delete 删除推荐服务套餐
func (r *servicePackageRecommendRepository) Delete(id uint) error {
	return mysql.Master().Delete(&ServicePackageRecommend{}, id).Error
}

// FindByID 根据ID查找推荐服务套餐
func (r *servicePackageRecommendRepository) FindByID(id uint) (*ServicePackageRecommend, error) {
	var recommend ServicePackageRecommend
	err := mysql.Slave().Where("id = ?", id).First(&recommend).Error
	if err != nil {
		return nil, err
	}
	return &recommend, nil
}

// List 获取推荐服务套餐列表
func (r *servicePackageRecommendRepository) List(params *ServicePackageRecommendQueryParams) ([]*ServicePackageRecommendInfo, int64, error) {
	var total int64
	var results []*ServicePackageRecommendInfo

	// 计算偏移量
	offset := (params.Page - 1) * params.PageSize
	if offset < 0 {
		offset = 0
	}

	// 基础查询
	db := mysql.Slave().Table("service_package_recommends spr").
		Select(`spr.id, spr.service_package_id, spr.is_top, spr.sort_order, spr.status, spr.creator_id, spr.created_at, spr.updated_at,
				sp.name as service_package_name, st.name as tag_name, sp.price, sp.images,
				CASE WHEN sp.is_all_stores = 1 THEN '全部' 
				     ELSE GROUP_CONCAT(s.name SEPARATOR ',') 
				END as store_names`).
		Joins("LEFT JOIN service_packages sp ON spr.service_package_id = sp.id").
		Joins("LEFT JOIN service_tags st ON sp.tag_id = st.id").
		Joins("LEFT JOIN service_package_store_relations spsr ON sp.id = spsr.service_package_id AND sp.is_all_stores = 0").
		Joins("LEFT JOIN t_stores s ON spsr.store_id = s.id").
		Where("spr.deleted_at IS NULL").
		Group("spr.id, spr.service_package_id, spr.is_top, spr.sort_order, spr.status, spr.creator_id, spr.created_at, spr.updated_at, sp.name, st.name, sp.price, sp.images, sp.is_all_stores")

	// 按服务套餐名称筛选
	if params.ServicePackageName != "" {
		db = db.Where("sp.name LIKE ?", "%"+params.ServicePackageName+"%")
	}

	// 按门店筛选
	if params.StoreID > 0 {
		db = db.Where("(sp.is_all_stores = 1 OR spsr.store_id = ?)", params.StoreID)
	}

	// 按置顶状态筛选
	if params.IsTop != nil {
		db = db.Where("spr.is_top = ?", *params.IsTop)
	}

	// 按状态筛选
	if params.Status != nil {
		db = db.Where("spr.status = ?", *params.Status)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表数据，按置顶状态和创建时间排序
	err := db.Order("spr.is_top DESC, spr.created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Scan(&results).Error

	if err != nil {
		return nil, 0, err
	}

	// 格式化时间
	for _, result := range results {
		if createdAt, err := time.Parse("2006-01-02 15:04:05", result.CreatedAt); err == nil {
			result.CreatedAt = createdAt.Format("2006-01-02 15:04:05")
		}
		if updatedAt, err := time.Parse("2006-01-02 15:04:05", result.UpdatedAt); err == nil {
			result.UpdatedAt = updatedAt.Format("2006-01-02 15:04:05")
		}
	}

	return results, total, nil
}

// SetTop 设置置顶
func (r *servicePackageRecommendRepository) SetTop(id uint) error {
	return mysql.Master().Transaction(func(tx *gorm.DB) error {
		// 先取消其他置顶
		if err := tx.Model(&ServicePackageRecommend{}).Where("is_top = true").Update("is_top", false).Error; err != nil {
			return err
		}

		// 设置当前为置顶
		return tx.Model(&ServicePackageRecommend{}).Where("id = ?", id).Update("is_top", true).Error
	})
}

// CancelTop 取消置顶
func (r *servicePackageRecommendRepository) CancelTop(id uint) error {
	return mysql.Master().Model(&ServicePackageRecommend{}).Where("id = ?", id).Update("is_top", false).Error
}

// GetTopRecommend 获取置顶推荐
func (r *servicePackageRecommendRepository) GetTopRecommend() (*ServicePackageRecommend, error) {
	var recommend ServicePackageRecommend
	err := mysql.Slave().Where("is_top = true AND status = 1").First(&recommend).Error
	if err != nil {
		return nil, err
	}
	return &recommend, nil
}

// CountRecommends 统计推荐服务套餐数量
func (r *servicePackageRecommendRepository) CountRecommends() (int64, error) {
	var count int64
	err := mysql.Slave().Model(&ServicePackageRecommend{}).Where("status = 1").Count(&count).Error
	return count, err
}

// BatchCreate 批量创建推荐服务套餐
func (r *servicePackageRecommendRepository) BatchCreate(recommends []*ServicePackageRecommend) error {
	if len(recommends) == 0 {
		return nil
	}

	// 分批插入，每次100条
	batchSize := 100
	for i := 0; i < len(recommends); i += batchSize {
		end := i + batchSize
		if end > len(recommends) {
			end = len(recommends)
		}

		batch := recommends[i:end]
		if err := mysql.Master().Create(&batch).Error; err != nil {
			return err
		}
	}

	return nil
}

// BatchDelete 批量删除
func (r *servicePackageRecommendRepository) BatchDelete(ids []uint) error {
	return mysql.Master().Delete(&ServicePackageRecommend{}, ids).Error
}

// GetServicePackageSelectList 获取服务套餐选择列表
func (r *servicePackageRecommendRepository) GetServicePackageSelectList(params *ServicePackageSelectQueryParams) ([]*ServicePackageSelectInfo, int64, error) {
	var total int64
	var results []*ServicePackageSelectInfo

	// 计算偏移量
	offset := (params.Page - 1) * params.PageSize
	if offset < 0 {
		offset = 0
	}

	// 基础查询 - 排除已被推荐的服务套餐
	db := mysql.Slave().Table("service_packages sp").
		Select(`sp.id as service_package_id, sp.name, st.name as tag_name, sp.price, sp.images,
				CASE WHEN sp.is_all_stores = 1 THEN '全部' 
				     ELSE GROUP_CONCAT(s.name SEPARATOR ',') 
				END as store_names,
				CASE WHEN spr.id IS NOT NULL THEN true ELSE false END as is_recommended`).
		Joins("LEFT JOIN service_tags st ON sp.tag_id = st.id").
		Joins("LEFT JOIN service_package_store_relations spsr ON sp.id = spsr.service_package_id AND sp.is_all_stores = 0").
		Joins("LEFT JOIN t_stores s ON spsr.store_id = s.id").
		Joins("LEFT JOIN service_package_recommends spr ON sp.id = spr.service_package_id AND spr.deleted_at IS NULL AND spr.status = 1").
		Where("sp.deleted_at IS NULL AND sp.status = 'active'").
		Where("spr.id IS NULL"). // 排除已被推荐的服务套餐
		Group("sp.id, sp.name, st.name, sp.price, sp.images, sp.is_all_stores, spr.id")

	// 按门店筛选
	if params.StoreID > 0 {
		db = db.Where("(sp.is_all_stores = 1 OR spsr.store_id = ?)", params.StoreID)
	}

	// 按服务套餐名称筛选
	if params.ServicePackageName != "" {
		db = db.Where("sp.name LIKE ?", "%"+params.ServicePackageName+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表数据
	err := db.Order("sp.created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Scan(&results).Error

	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

// IsRecommended 检查服务套餐是否已被推荐
func (r *servicePackageRecommendRepository) IsRecommended(servicePackageID uint) bool {
	var count int64
	mysql.Slave().Model(&ServicePackageRecommend{}).Where("service_package_id = ? AND status = 1", servicePackageID).Count(&count)
	return count > 0
}

// GetRecommendedServicePackages 获取推荐服务套餐列表
func (r *servicePackageRecommendRepository) GetRecommendedServicePackages(limit int) ([]*ServicePackageRecommend, error) {
	var recommends []*ServicePackageRecommend

	err := mysql.Slave().Where("status = 1").
		Order("is_top DESC, created_at DESC").
		Limit(limit).
		Find(&recommends).Error

	return recommends, err
}
