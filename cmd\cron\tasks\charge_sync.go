package tasks

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/common/model/user"
)

// ChargeSyncService 门诊收费信息同步服务
type ChargeSyncService struct {
	db     *gorm.DB
	wsjgID int // 卫生机构ID
}

// NewChargeSyncService 创建门诊收费信息同步服务
func NewChargeSyncService(db *gorm.DB) *ChargeSyncService {
	return &ChargeSyncService{
		db:     db,
		wsjgID: 5424, // 杭州余杭叶开泰综合门诊部
	}
}

// SyncCharges 同步门诊收费信息
func (s *ChargeSyncService) SyncCharges() {
	logx.Info("开始同步门诊收费信息...")

	// 获取最后成功同步的日期
	lastSyncDate := s.getLastSyncDate()

	if lastSyncDate == "" {
		// 首次运行，从2025年1月1日开始同步
		logx.Info("首次运行，开始从2025年1月1日同步历史数据...")
		s.syncHistoricalData()
	} else {
		// 从最后同步日期开始同步到今天（包含当天）
		logx.Infof("继续同步，从 %s 开始（包含当天）...", lastSyncDate)
		s.syncFromDate(lastSyncDate)
	}

	logx.Info("门诊收费信息同步完成")
}

// SyncChargesManual 手动同步门诊收费信息（与自动同步使用相同的逻辑）
func (s *ChargeSyncService) SyncChargesManual() {
	logx.Info("开始手动同步门诊收费信息...")

	// 获取最后成功同步的日期
	lastSyncDate := s.getLastSyncDate()

	if lastSyncDate == "" {
		// 首次运行，从2025年1月1日开始同步
		logx.Info("手动同步：首次运行，开始从2025年1月1日同步历史数据...")
		s.syncHistoricalData()
	} else {
		// 从最后同步日期开始同步到今天（包含当天）
		logx.Infof("手动同步：继续同步，从 %s 开始（包含当天）...", lastSyncDate)
		s.syncFromDate(lastSyncDate)
	}

	logx.Info("手动门诊收费信息同步完成")
}

// getLastSyncDate 获取最后成功同步的日期
func (s *ChargeSyncService) getLastSyncDate() string {
	var progress patient.SyncProgress
	err := s.db.Where("sync_type = ? AND status = ?", "charge_sync", 1).
		Order("sync_date DESC").
		First(&progress).Error

	if err != nil {
		return "" // 没有找到记录，返回空字符串
	}

	// 如果存储的是时间戳格式，尝试解析并转换为日期格式
	if strings.Contains(progress.SyncDate, "T") {
		if t, parseErr := time.Parse(time.RFC3339, progress.SyncDate); parseErr == nil {
			return t.Format("2006-01-02")
		}
	}

	return progress.SyncDate
}

// saveSyncProgress 保存同步进度
func (s *ChargeSyncService) saveSyncProgress(syncDate string, status int, records int, message string) {
	// 确保syncDate是标准日期格式（YYYY-MM-DD）
	if t, err := time.Parse("2006-01-02", syncDate); err == nil {
		syncDate = t.Format("2006-01-02") // 标准化日期格式
	}

	progress := patient.SyncProgress{
		SyncType: "charge_sync",
		SyncDate: syncDate,
		Status:   status,
		Records:  records,
		Message:  message,
	}

	// 先查找是否已存在该日期的记录
	var existing patient.SyncProgress
	err := s.db.Where("sync_type = ? AND sync_date = ?", "charge_sync", syncDate).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 不存在，创建新记录
		s.db.Create(&progress)
		logx.Infof("保存同步进度: 日期=%s, 状态=%d, 记录数=%d", syncDate, status, records)
	} else {
		// 存在，更新记录
		s.db.Model(&existing).Updates(map[string]interface{}{
			"status":  status,
			"records": records,
			"message": message,
		})
		logx.Infof("更新同步进度: 日期=%s, 状态=%d, 记录数=%d", syncDate, status, records)
	}
}

// syncHistoricalData 同步历史数据（从2025年1月1日到今天）
func (s *ChargeSyncService) syncHistoricalData() {
	startDate := time.Date(2024, 10, 06, 0, 0, 0, 0, time.Local)
	endDate := time.Now()

	for currentDate := startDate; currentDate.Before(endDate) || currentDate.Equal(endDate.Truncate(24*time.Hour)); currentDate = currentDate.AddDate(0, 0, 1) {
		dateStr := currentDate.Format("2006-01-02")
		logx.Infof("同步日期: %s", dateStr)

		records, err := s.syncChargesByDate(dateStr, dateStr)
		if err != nil {
			logx.Errorf("同步日期 %s 的数据失败: %v", dateStr, err)
			s.saveSyncProgress(dateStr, 0, 0, err.Error())
			continue
		}

		s.saveSyncProgress(dateStr, 1, records, "同步成功")

		// 避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}
}

// syncFromDate 从指定日期开始同步到今天（包含当天）
func (s *ChargeSyncService) syncFromDate(lastSyncDate string) {
	lastDate, err := time.Parse("2006-01-02", lastSyncDate)
	if err != nil {
		logx.Errorf("解析最后同步日期失败: %v", err)
		return
	}

	// 从最后同步日期开始（包含当天），确保当天的数据也能被同步
	startDate := lastDate
	endDate := time.Now()
	today := time.Now().Format("2006-01-02")

	for currentDate := startDate; currentDate.Before(endDate) || currentDate.Equal(endDate.Truncate(24*time.Hour)); currentDate = currentDate.AddDate(0, 0, 1) {
		dateStr := currentDate.Format("2006-01-02")
		logx.Infof("同步日期: %s", dateStr)

		records, err := s.syncChargesByDate(dateStr, dateStr)
		if err != nil {
			logx.Errorf("同步日期 %s 的数据失败: %v", dateStr, err)
			s.saveSyncProgress(dateStr, 0, 0, err.Error())
			continue
		}

		s.saveSyncProgress(dateStr, 1, records, "同步成功")

		// 如果是今天的数据，记录日志
		if dateStr == today {
			logx.Infof("今天的门诊收费数据同步完成: %s，同步记录数: %d", dateStr, records)
		}

		// 避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}
}

// syncTodayData 同步当天数据
func (s *ChargeSyncService) syncTodayData() {
	today := time.Now().Format("2006-01-02")
	records, err := s.syncChargesByDate(today, today)
	if err != nil {
		logx.Errorf("同步当天数据失败: %v", err)
		s.saveSyncProgress(today, 0, 0, err.Error())
	} else {
		s.saveSyncProgress(today, 1, records, "当天数据同步成功")
	}
}

// syncChargesByDate 按日期同步门诊收费信息
func (s *ChargeSyncService) syncChargesByDate(dateFrom, dateTo string) (int, error) {
	// 调用杭州HIS接口获取门诊收费信息
	ctx := context.Background()
	resp, err := hangzhou.DefaultClient.GetOutpatientChargeInfo(ctx, dateFrom, dateTo)
	if err != nil {
		return 0, fmt.Errorf("获取门诊收费信息失败: %w", err)
	}

	// 从响应中提取数据
	charges, ok := resp.Data.([]hangzhou.OutpatientCharge)
	if !ok {
		return 0, fmt.Errorf("门诊收费信息数据格式错误")
	}

	logx.Infof("获取到 %d 条门诊收费记录 (日期: %s - %s)", len(charges), dateFrom, dateTo)

	successCount := 0
	errorCount := 0

	for _, charge := range charges {
		// 检查记录是否已存在（通过mzsfid判断）
		var existingRecord patient.DiagnosisRecord
		err := s.db.Where("mzsfid = ?", charge.MzsfID).First(&existingRecord).Error
		if err == nil {
			// 记录已存在，跳过
			logx.Infof("门诊收费记录已存在，跳过: mzsfid=%d", charge.MzsfID)
			continue
		}
		if err != gorm.ErrRecordNotFound {
			logx.Errorf("查询诊断记录失败: %v", err)
			errorCount++
			continue
		}

		// 处理患者信息
		patientID, err := s.processPatientInfo(charge)
		if err != nil {
			logx.Errorf("处理患者信息失败: %v", err)
			errorCount++
			continue
		}

		// 创建诊断记录
		diagnosisRecord := s.convertToDignosisRecord(charge)
		diagnosisRecord.PatientID = patientID
		err = s.db.Create(&diagnosisRecord).Error
		if err != nil {
			logx.Errorf("创建诊断记录失败: %v", err)
			errorCount++
			continue
		}

		successCount++
		logx.Infof("成功创建门诊收费记录: mzsfid=%d, 患者=%s", charge.MzsfID, charge.XM)
	}

	logx.Infof("门诊收费信息同步完成: 成功 %d 条, 失败 %d 条", successCount, errorCount)
	return successCount, nil
}

// processPatientInfo 处理患者信息，如果不存在则创建，返回患者ID
func (s *ChargeSyncService) processPatientInfo(charge hangzhou.OutpatientCharge) (uint, error) {
	// 检查患者是否已存在（通过杭州HIS ID或手机号查询）
	var existingPatient patient.WxPatient
	grxxidStr := strconv.Itoa(charge.GrxxID)
	err := s.db.Where("hangzhou_his_id = ? OR mobile = ?", grxxidStr, charge.SJHM).First(&existingPatient).Error

	if err == nil {
		// 患者已存在，返回患者ID
		return existingPatient.PatientID, nil
	}

	if err != gorm.ErrRecordNotFound {
		return 0, fmt.Errorf("查询患者信息失败: %w", err)
	}

	// 患者不存在，需要创建
	// 首先创建用户记录
	wxUser := &user.WxUser{
		Mobile:       charge.SJHM,
		Nickname:     charge.XM,
		Gender:       s.convertGender(charge.XbDM), // 转换性别
		Status:       1,                            // 正常状态
		RegisterDate: time.Now(),
	}

	err = s.db.Create(wxUser).Error
	if err != nil {
		return 0, fmt.Errorf("创建用户记录失败: %w", err)
	}

	// 然后创建患者记录
	wxPatient := &patient.WxPatient{
		UserID:               wxUser.UserID,
		Name:                 charge.XM,
		Mobile:               charge.SJHM,
		Gender:               s.convertGender(charge.XbDM), // 转换性别
		BirthDate:            s.convertBirthday(charge.CSSJ),
		IdCard:               charge.ZJHM,
		HangzhouHisID:        grxxidStr, // 转换为字符串
		Status:               1,         // 正常状态
	}

	err = s.db.Create(wxPatient).Error
	if err != nil {
		return 0, fmt.Errorf("创建患者记录失败: %w", err)
	}

	logx.Infof("自动创建患者记录: %s (手机号: %s, 杭州HIS ID: %d)", charge.XM, charge.SJHM, charge.GrxxID)
	return wxPatient.PatientID, nil
}

// convertToDignosisRecord 将门诊收费信息转换为诊断记录
func (s *ChargeSyncService) convertToDignosisRecord(charge hangzhou.OutpatientCharge) patient.DiagnosisRecord {
	record := patient.DiagnosisRecord{}

	// 基础信息
	if charge.PYM != "" {
		record.PYM = charge.PYM
	}
	if charge.CzrID != 0 {
		record.CzrID = charge.CzrID
	}
	if charge.CZSJ != "" && charge.CZSJ != "1970-01-01 00:00:00" {
		record.CzSJ = charge.CZSJ
	}
	if charge.CzrMC != "" {
		record.CzrMC = charge.CzrMC
	}
	record.ZfBZ = charge.ZfBZ
	if charge.BZ != "" {
		record.BZ = charge.BZ
	}

	// 患者信息 - 只保留病案号和姓名
	if charge.BAH != "" {
		record.BAH = charge.BAH
	}
	if charge.XM != "" {
		record.XM = charge.XM
	}
	// PatientID 在 processPatientInfo 方法中设置

	// 门诊收费信息
	if charge.MzsfID != 0 {
		record.MzsfID = charge.MzsfID
	}
	if charge.WsjgID != 0 {
		record.WsjgID = charge.WsjgID
	}
	if charge.WsjgMC != "" {
		record.WsjgMC = charge.WsjgMC
	}
	if charge.MzsfdjH != "" {
		record.MzsfdjH = charge.MzsfdjH
	}
	if charge.MzghID != 0 {
		record.MzghID = charge.MzghID
	}
	if charge.MzghdjH != "" {
		record.MzghdjH = charge.MzghdjH
	}
	if charge.MzblID != 0 {
		record.MzblID = charge.MzblID
	}
	if charge.MzYZIDs != "" {
		record.MzyzIDs = charge.MzYZIDs
	}
	if charge.MzhjID != 0 {
		record.MzhjID = charge.MzhjID
	}

	// 费用信息
	if charge.FylbDM != "" {
		record.FylbDM = charge.FylbDM
	}
	if charge.FylbMC != "" {
		record.FylbMC = charge.FylbMC
	}

	// 科室医生信息
	if charge.JgksID != 0 {
		record.JgksID = charge.JgksID
	}
	if charge.JgksMC != "" {
		record.JgksMC = charge.JgksMC
	}
	if charge.YsID != 0 {
		record.YsID = charge.YsID
	}
	if charge.YsMC != "" {
		record.YsMC = charge.YsMC
	}

	// 金额信息
	record.YzJE = charge.YzJE
	record.ZJE = charge.ZJE

	// 票据信息
	if charge.PJH != "" {
		record.PJH = charge.PJH
	}
	if charge.PJLSH != "" {
		record.PJLSH = charge.PJLSH
	}
	if charge.PJLx != "" {
		record.PJLx = charge.PJLx
	}
	if charge.PJURL != "" {
		record.PJURL = charge.PJURL
	}
	if charge.KPRQ != "" {
		record.KPRQ = charge.KPRQ
	}

	// 疾病信息
	if charge.JbDM != "" {
		record.JbDM = charge.JbDM
	}
	if charge.JbMC != "" {
		record.JbMC = charge.JbMC
	}

	// 医保信息
	if charge.YbmzlxDM != "" {
		record.YbmzlxDM = charge.YbmzlxDM
	}
	if charge.YbksDM != "" {
		record.YbksDM = charge.YbksDM
	}

	// 门诊类型
	if charge.MzlxDM != "" {
		record.MzlxDM = charge.MzlxDM
	}
	if charge.MzlxMC != "" {
		record.MzlxMC = charge.MzlxMC
	}

	return record
}

// convertGender 转换性别代码（暂时不使用，因为OutpatientCharge没有性别字段）
func (s *ChargeSyncService) convertGender(genderCode string) int {
	switch genderCode {
	case "1":
		return 1 // 男
	case "2":
		return 2 // 女
	default:
		return 0 // 未知
	}
}

// convertBirthday 转换生日格式
func (s *ChargeSyncService) convertBirthday(birthday string) string {
	if birthday == "" || birthday == "1970-01-01 00:00:00" {
		return ""
	}

	// 尝试解析时间格式
	if t, err := time.Parse("2006-01-02 15:04:05", birthday); err == nil {
		return t.Format("2006-01-02")
	}

	// 如果解析失败，返回原始值
	return birthday
}
