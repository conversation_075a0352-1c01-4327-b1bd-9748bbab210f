package registration

import (
	"time"

	"gorm.io/gorm"
)

// AppointPeriodType 预约时段类型
type AppointPeriodType int

const (
	AppointPeriodDefault   AppointPeriodType = 0 // 默认
	AppointPeriodMorning   AppointPeriodType = 1 // 上午
	AppointPeriodAfternoon AppointPeriodType = 2 // 下午
	AppointPeriodEvening   AppointPeriodType = 3 // 夜班
)

// GetAppointPeriodText 获取预约时段文本描述
func GetAppointPeriodText(period AppointPeriodType) string {
	switch period {
	case AppointPeriodMorning:
		return "上午"
	case AppointPeriodAfternoon:
		return "下午"
	case AppointPeriodEvening:
		return "夜班"
	default:
		return "默认"
	}
}

// HisType HIS系统类型
type HisType int

const (
	HisTypeHangzhou HisType = 1 // 杭州HIS
	HisTypeAbcyun   HisType = 2 // ABC云
)

// OrderStatus 订单状态
type OrderStatus int

const (
	OrderStatusWaitDiagnosis OrderStatus = 1 // 待就诊
	OrderStatusWaitPay       OrderStatus = 2 // 待支付
	OrderStatusCompleted     OrderStatus = 3 // 已完成
	OrderStatusCanceled      OrderStatus = 4 // 已取消
	OrderStatusRefunded      OrderStatus = 5 // 已取消已退款
)

// VerificationStatus 核销状态
type VerificationStatus int

const (
	VerificationStatusPending  VerificationStatus = 0 // 待核销
	VerificationStatusVerified VerificationStatus = 1 // 已核销
	VerificationStatusCanceled VerificationStatus = 2 // 已取消
)

// SyncStatus 同步状态
type SyncStatus int

const (
	SyncStatusPending SyncStatus = 0 // 待同步
	SyncStatusSuccess SyncStatus = 1 // 已同步
	SyncStatusFailed  SyncStatus = 2 // 同步失败
)

// AppointmentOrder 预约挂号订单表
type AppointmentOrder struct {
	ID                 uint              `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	OrderNo            string            `gorm:"column:order_no;type:varchar(50);index;comment:订单编号" json:"order_no"`                                          // 订单编号
	AppointDate        string            `gorm:"column:appoint_date;type:varchar(20);comment:预约日期" json:"appoint_date"`                                        // 预约日期
	AppointPeriod      AppointPeriodType `gorm:"column:appoint_period;type:int;default:0;comment:预约时段(0-默认,1-上午,2-下午,3-夜班)" json:"appoint_period"`             // 预约时段
	SyncStatus         SyncStatus        `gorm:"column:sync_status;type:tinyint;default:0;comment:同步状态(0-待同步,1-已同步,2-同步失败)" json:"sync_status"`                // 同步状态
	OrderStatus        OrderStatus       `gorm:"column:order_status;type:tinyint;default:1;comment:订单状态(1-待就诊,2-待支付,3-已完成,4-已取消,5-已退款)" json:"order_status"`   // 订单状态
	HisType            HisType           `gorm:"column:his_type;type:tinyint;default:1;comment:HIS系统类型" json:"his_type"`                                       // HIS系统类型
	YyghID             int               `gorm:"column:yyghid;comment:预约挂号ID" json:"yyghid"`                                                                   // 预约挂号ID
	GrxxID             int               `gorm:"column:grxxid;comment:个人信息ID" json:"grxxid"`                                                                   // 个人信息ID
	HyjlID             int               `gorm:"column:hyjlid;comment:号源记录ID" json:"hyjlid"`                                                                   // 号源记录ID
	YylX               int               `gorm:"column:yylx;comment:预约类型" json:"yylx"`                                                                         // 预约类型
	JgksID             int               `gorm:"column:jgksid;comment:机构科室ID" json:"jgksid"`                                                                   // 机构科室ID
	DepartmentID       uint              `gorm:"column:department_id;index;comment:科室ID(关联t_departments表)" json:"department_id"`                               // 科室ID(关联t_departments表)
	WxDoctorID         int               `gorm:"column:wx_doctor_id;index;comment:医生ID" json:"wx_doctor_id"`                                                   // 关联本地医生ID
	WxPatientID        int               `gorm:"column:wx_patient_id;index;comment:患者ID" json:"wx_patient_id"`                                                 // 关联本地患者ID
	StoreID            int               `gorm:"column:store_id;index;comment:门店ID" json:"store_id"`                                                           // 门店ID
	RequestData        string            `gorm:"column:request_data;type:text;comment:请求数据" json:"request_data"`                                               // 调用API的请求参数
	ResponseData       string            `gorm:"column:response_data;type:text;comment:响应数据" json:"response_data"`                                             // API的返回结果
	ZfrID              int               `gorm:"column:zfrid;comment:作废人ID" json:"zfrid"`                                                                      // 作废人ID
	ZfrMC              string            `gorm:"column:zfrmc;type:varchar(50);comment:作废人名称" json:"zfrmc"`                                                     // 作废人名称
	ZfYY               string            `gorm:"column:zfyy;type:varchar(200);comment:作废原因" json:"zfyy"`                                                       // 作废原因
	ZfSJ               string            `gorm:"column:zfsj;type:varchar(20);comment:作废时间" json:"zfsj"`                                                        // 作废时间
	CzrID              int               `gorm:"column:czrid;comment:创建人ID" json:"czrid"`                                                                      // 创建人ID
	Czr                string            `gorm:"column:czr;type:varchar(50);comment:创建人名称" json:"czr"`                                                         // 创建人名称
	QRCodeURL          string            `gorm:"column:qr_code_url;type:varchar(255);comment:二维码URL" json:"qr_code_url"`                                       // 二维码URL
	VerificationCode   string            `gorm:"column:verification_code;type:varchar(50);index;comment:核销码" json:"verification_code"`                         // 核销码
	VerificationStatus int               `gorm:"column:verification_status;type:tinyint;default:0;comment:核销状态(0-待核销,1-已核销,2-已取消)" json:"verification_status"` // 核销状态
	VerifiedAt         string            `gorm:"column:verified_at;type:varchar(20);comment:核销时间" json:"verified_at"`                                          // 核销时间
	VerifierID         int               `gorm:"column:verifier_id;comment:核销人ID" json:"verifier_id"`                                                          // 核销人ID
	VerifierName       string            `gorm:"column:verifier_name;type:varchar(50);comment:核销人姓名" json:"verifier_name"`                                     // 核销人姓名

	// ABC云相关字段
	RegistrationSheetID string `gorm:"column:registration_sheet_id;type:varchar(100);index;comment:ABC云挂号单ID" json:"registration_sheet_id"` // ABC云挂号单ID
	PatientOrderID      string `gorm:"column:patient_order_id;type:varchar(100);index;comment:ABC云就诊单ID" json:"patient_order_id"`           // ABC云就诊单ID

	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"` // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`    // 删除时间
}

// TableName 设置表名
func (AppointmentOrder) TableName() string {
	return "registration_appointment_orders"
}
