package types

import "encoding/json"

// ExaminationQueryByPatientRequest 按患者查询检查检验单请求
type ExaminationQueryByPatientRequest struct {
	PatientID string `path:"patientId"`          // 患者ID
	StartTime string `form:"startTime,optional"` // 开始时间
	EndTime   string `form:"endTime,optional"`   // 结束时间
	Limit     int    `form:"limit,optional"`     // 每页显示条数
	Offset    int    `form:"offset,optional"`    // 分页起始下标
	Type      int    `form:"type,optional"`      // 类型 1:检验 2:检查
	ClinicID  string `form:"clinicId,optional"`  // 诊所ID
}

// ExaminationQueryByDateRequest 按时间查询检查检验单请求
type ExaminationQueryByDateRequest struct {
	DateFieldType int    `form:"dateFieldType,optional"` // 查询日期类型
	StartTime     string `form:"startTime,optional"`     // 开始时间
	EndTime       string `form:"endTime,optional"`       // 结束时间
	Limit         int    `form:"limit,optional"`         // 每页显示条数
	Offset        int    `form:"offset,optional"`        // 分页起始下标
	Type          int    `form:"type,optional"`          // 类型 1:检验 2:检查
	ClinicID      string `form:"clinicId,optional"`      // 诊所ID
}

// ExaminationQueryByPatientOrderNoRequest 按就诊号查询检查检验单请求
type ExaminationQueryByPatientOrderNoRequest struct {
	PatientOrderNo string `form:"patientOrderNo"`    // 就诊号
	ClinicID       string `form:"clinicId,optional"` // 诊所ID
}

// ExaminationDetailRequest 查询检查检验单详情请求
type ExaminationDetailRequest struct {
	ID       string `path:"id"`                // 检查检验单id
	ClinicID string `form:"clinicId,optional"` // 诊所ID
}

// ExaminationByOrderNoRequest 按单号查询检查检验单(条形码)请求
type ExaminationByOrderNoRequest struct {
	OrderNo  string `path:"orderNo"`           // 订单号
	Type     int    `path:"type"`              // 类型 1:检验 2:检查
	ClinicID string `form:"clinicId,optional"` // 诊所ID
}

// ExaminationResultWithEmptyObject 检查结果，针对Value字段为空对象的情况做特殊处理
type ExaminationResultWithEmptyObject struct {
	ID                 string          `json:"id,omitempty"`                 // 结果ID
	Type               int             `json:"type,omitempty"`               // 类型
	Name               string          `json:"name,omitempty"`               // 名称
	EnName             string          `json:"enName,omitempty"`             // 英文名
	Unit               string          `json:"unit,omitempty"`               // 单位
	ItemCode           string          `json:"itemCode,omitempty"`           // 项目代码
	Ref                string          `json:"ref,omitempty"`                // 参考值
	ProductID          string          `json:"productId,omitempty"`          // 产品ID
	ResultDisplayScale int             `json:"resultDisplayScale,omitempty"` // 结果显示比例
	Value              json.RawMessage `json:"value,omitempty"`              // 值，使用json.RawMessage延迟解析
	ValueType          string          `json:"valueType,omitempty"`          // 值类型
	AbnormalFlag       string          `json:"abnormalFlag,omitempty"`       // 异常标志
	AbnormalText       string          `json:"abnormalText,omitempty"`       // 异常文本
}

// ExaminationUpdateByOrderNoRequest 根据检验单号修改检验单信息请求
type ExaminationUpdateByOrderNoRequest struct {
	OrderNo                string                             `path:"orderNo"`                          // 订单号
	Type                   int                                `path:"type"`                             // 类型 1:检验 2:检查
	Attachments            []ExaminationAttachment            `json:"attachments"`                      // 附件
	Reports                []ExaminationReport                `json:"reports"`                          // 报告
	Results                []ExaminationResultWithEmptyObject `json:"results"`                          // 检查结果
	EyeResults             []ExaminationEyeResult             `json:"eyeResults"`                       // 眼科检查结果
	DeviceModelID          int                                `json:"deviceModelId,omitempty"`          // 设备型号ID
	DeviceID               int                                `json:"deviceId,omitempty"`               // 设备ID
	SubType                int                                `json:"subType,omitempty"`                // 子类型
	Remark                 string                             `json:"remark,omitempty"`                 // 备注
	TesterID               string                             `json:"testerId,omitempty"`               // 检验人ID
	TesterName             string                             `json:"testerName,omitempty"`             // 检验人姓名
	CheckerID              string                             `json:"checkerId,omitempty"`              // 审核人ID
	CheckerName            string                             `json:"checkerName,omitempty"`            // 审核人姓名
	SampleType             string                             `json:"sampleType,omitempty"`             // 样本类型
	TestTime               string                             `json:"testTime,omitempty"`               // 检验时间
	CheckTime              string                             `json:"checkTime,omitempty"`              // 审核时间
	ReportTime             string                             `json:"reportTime,omitempty"`             // 报告时间
	Status                 int                                `json:"status,omitempty"`                 // 状态
	RelationPatientOrderID string                             `json:"relationPatientOrderId,omitempty"` // 关联就诊单ID
	ClinicID               string                             `form:"clinicId,optional"`                // 诊所ID
}

// ExaminationUpdateRequest 保存检查检验单数据请求
type ExaminationUpdateRequest struct {
	ID                     string                             `path:"id"`                               // 检查检验单id
	Attachments            []ExaminationAttachment            `json:"attachments"`                      // 附件
	Reports                []ExaminationReport                `json:"reports"`                          // 报告
	Results                []ExaminationResultWithEmptyObject `json:"results"`                          // 检查结果
	EyeResults             []ExaminationEyeResult             `json:"eyeResults"`                       // 眼科检查结果
	DeviceModelID          int                                `json:"deviceModelId,omitempty"`          // 设备型号ID
	DeviceID               int                                `json:"deviceId,omitempty"`               // 设备ID
	SubType                int                                `json:"subType,omitempty"`                // 子类型
	Remark                 string                             `json:"remark,omitempty"`                 // 备注
	TesterID               string                             `json:"testerId,omitempty"`               // 检验人ID
	TesterName             string                             `json:"testerName,omitempty"`             // 检验人姓名
	CheckerID              string                             `json:"checkerId,omitempty"`              // 审核人ID
	CheckerName            string                             `json:"checkerName,omitempty"`            // 审核人姓名
	SampleType             string                             `json:"sampleType,omitempty"`             // 样本类型
	TestTime               string                             `json:"testTime,omitempty"`               // 检验时间
	CheckTime              string                             `json:"checkTime,omitempty"`              // 审核时间
	ReportTime             string                             `json:"reportTime,omitempty"`             // 报告时间
	Status                 int                                `json:"status,omitempty"`                 // 状态
	RelationPatientOrderID string                             `json:"relationPatientOrderId,omitempty"` // 关联就诊单ID
	ClinicID               string                             `form:"clinicId,optional"`                // 诊所ID
}

// ExaminationCreateRequest 创建检查检验单请求
type ExaminationCreateRequest struct {
	PatientID            string                `json:"patientId"`            // 患者ID
	ProductSubType       int                   `json:"productSubType"`       // 产品子类型
	ExaminationFormItems []ExaminationFormItem `json:"examinationFormItems"` // 检查项目
	DoctorID             string                `json:"doctorId"`             // 医生ID
	DoctorDepartmentID   string                `json:"doctorDepartmentId"`   // 医生科室ID
	OperatorID           string                `json:"operatorId"`           // 操作员ID
	ClinicID             string                `form:"clinicId,optional"`    // 诊所ID
}

// ExaminationRefundRequest 作废检查检验单请求
type ExaminationRefundRequest struct {
	ID       string `path:"id"`                // 检查检验单id
	ClinicID string `form:"clinicId,optional"` // 诊所ID
}

// ExaminationDevicesRequest 获取检查检验设备列表请求
type ExaminationDevicesRequest struct {
	Type     int    `path:"type"`              // 类型 1:检验 2:检查
	ClinicID string `form:"clinicId,optional"` // 诊所ID
}

// ExaminationAttachment 附件信息
type ExaminationAttachment struct {
	URL      string `json:"url"`      // 附件URL
	FileName string `json:"fileName"` // 文件名
	FileSize int    `json:"fileSize"` // 文件大小
}

// DiagnosisEntryItem 诊断条目
type DiagnosisEntryItem struct {
	ID           int    `json:"id"`           // ID
	Name         string `json:"name"`         // 名称
	AbnormalFlag int    `json:"abnormalFlag"` // 异常标志
}

// ExaminationReport 检查报告
type ExaminationReport struct {
	ID                   string                  `json:"id,omitempty"`                   // 报告ID
	ImageFiles           []ExaminationAttachment `json:"imageFiles"`                     // 图片文件
	VideoDescription     string                  `json:"videoDescription,omitempty"`     // 视频描述
	Suggestion           string                  `json:"suggestion,omitempty"`           // 建议
	DiagnosisFlag        int                     `json:"diagnosisFlag,omitempty"`        // 诊断标志
	RecordDoctorID       string                  `json:"recordDoctorId,omitempty"`       // 记录医生ID
	ConsultationDoctorID string                  `json:"consultationDoctorId,omitempty"` // 会诊医生ID
	InspectionSite       string                  `json:"inspectionSite,omitempty"`       // 检查部位
	DeviceModelDesc      string                  `json:"deviceModelDesc,omitempty"`      // 设备型号描述
	IsDeleted            int                     `json:"isDeleted,omitempty"`            // 是否删除
	DiagnosisEntryItems  []DiagnosisEntryItem    `json:"diagnosisEntryItems"`            // 诊断条目
}

// ExaminationResult 检查结果
type ExaminationResult struct {
	ID                 string      `json:"id,omitempty"`                 // 结果ID
	Type               int         `json:"type,omitempty"`               // 类型
	Name               string      `json:"name,omitempty"`               // 名称
	EnName             string      `json:"enName,omitempty"`             // 英文名
	Unit               string      `json:"unit,omitempty"`               // 单位
	ItemCode           string      `json:"itemCode,omitempty"`           // 项目代码
	Ref                string      `json:"ref,omitempty"`                // 参考值
	ProductID          string      `json:"productId,omitempty"`          // 产品ID
	ResultDisplayScale int         `json:"resultDisplayScale,omitempty"` // 结果显示比例
	Value              interface{} `json:"value,omitempty"`              // 值，使用 interface{} 允许任何类型
	ValueType          string      `json:"valueType,omitempty"`          // 值类型
	AbnormalFlag       string      `json:"abnormalFlag,omitempty"`       // 异常标志
	AbnormalText       string      `json:"abnormalText,omitempty"`       // 异常文本
}

// Constraint 约束条件
type Constraint struct {
	Message      string `json:"message"`      // 消息
	Required     bool   `json:"required"`     // 是否必填
	Precision    int    `json:"precision"`    // 精度
	Scale        int    `json:"scale"`        // 刻度
	Min          int    `json:"min"`          // 最小值
	Max          int    `json:"max"`          // 最大值
	Size         int    `json:"size"`         // 大小
	Unit         string `json:"unit"`         // 单位
	ValidateType int    `json:"validateType"` // 验证类型
}

// ExaminationItem 检查项目
type ExaminationItem struct {
	ID                 string       `json:"id"`                 // ID
	ProductID          string       `json:"productId"`          // 产品ID
	Type               int          `json:"type"`               // 类型
	Name               string       `json:"name"`               // 名称
	EnName             string       `json:"enName"`             // 英文名
	Unit               string       `json:"unit"`               // 单位
	Ref                string       `json:"ref"`                // 参考值
	ResultDisplayScale int          `json:"resultDisplayScale"` // 结果显示比例
	InspectType        int          `json:"inspectType"`        // 检查类型
	ComponentType      int          `json:"componentType"`      // 组件类型
	Options            []string     `json:"options"`            // 选项
	Constraints        []Constraint `json:"constraints"`        // 约束条件
	Value              string       `json:"value"`              // 值
	Sort               int          `json:"sort"`               // 排序
	GroupID            int          `json:"groupId"`            // 分组ID
	DisplayName        string       `json:"displayName"`        // 显示名称
}

// ExaminationInspectChild 检查子项
type ExaminationInspectChild struct {
	Items []ExaminationItem `json:"items"` // 检查项目
}

// ExaminationInspectType 检查类型
type ExaminationInspectType struct {
	InspectType int                       `json:"inspectType"` // 检查类型
	Children    []ExaminationInspectChild `json:"children"`    // 子项
}

// ExaminationNameChild 命名子项
type ExaminationNameChild struct {
	Name     string                   `json:"name"`     // 名称
	Children []ExaminationInspectType `json:"children"` // 子项
}

// ExaminationNameParent 命名父项
type ExaminationNameParent struct {
	Name     string                 `json:"name"`     // 名称
	Children []ExaminationNameChild `json:"children"` // 子项
}

// ExaminationChecker 检查员
type ExaminationChecker struct {
	ID   string `json:"id"`   // ID
	Name string `json:"name"` // 名称
	Date string `json:"date"` // 日期
}

// ExaminationEyeResult 眼科检查结果
type ExaminationEyeResult struct {
	ProductID string                  `json:"productId"` // 产品ID
	Name      string                  `json:"name"`      // 名称
	Children  []ExaminationNameParent `json:"children"`  // 子项
	Checker   ExaminationChecker      `json:"checker"`   // 检查员
}

// ExaminationFormItem 检查表单项目
type ExaminationFormItem struct {
	ProductID string  `json:"productId"` // 产品ID
	Name      string  `json:"name"`      // 名称
	UnitCount float64 `json:"unitCount"` // 单位数量
}

// ExaminationPatientInfo 检查患者信息
type ExaminationPatientInfo struct {
	ID       string     `json:"id"`           // ID
	SN       string     `json:"sn,omitempty"` // 编号
	Name     string     `json:"name"`         // 姓名
	Mobile   string     `json:"mobile"`       // 手机号
	Sex      string     `json:"sex"`          // 性别
	Birthday string     `json:"birthday"`     // 生日
	Age      PatientAge `json:"age"`          // 年龄
	IDCard   string     `json:"idCard"`       // 身份证号
}

// ExaminationProduct 检查产品
type ExaminationProduct struct {
	ProductID          string `json:"productId"`          // 产品ID
	ProductName        string `json:"productName"`        // 产品名称
	ExaminationSheetID string `json:"examinationSheetId"` // 检查单ID
}

// ExaminationSummary 检查检验单摘要信息
type ExaminationSummary struct {
	ID             string                 `json:"id"`             // ID
	PatientOrderID string                 `json:"patientOrderId"` // 就诊单ID
	Patient        ExaminationPatientInfo `json:"patient"`        // 患者信息
	Name           string                 `json:"name"`           // 名称
	Type           int                    `json:"type"`           // 类型
	DeviceType     int                    `json:"deviceType"`     // 设备类型
	BusinessType   int                    `json:"businessType"`   // 业务类型
	Created        string                 `json:"created"`        // 创建时间
	Status         int                    `json:"status"`         // 状态
	StatusName     string                 `json:"statusName"`     // 状态名称
}

// ExaminationAllSummary 检查检验摘要信息
type ExaminationAllSummary struct {
	ID               string                 `json:"id"`                  // ID
	OrderNo          string                 `json:"orderNo"`             // 订单号
	ProductID        string                 `json:"productId"`           // 产品ID
	Products         []ExaminationProduct   `json:"products"`            // 产品
	ProductIDs       []string               `json:"productIds"`          // 产品ID列表
	Name             string                 `json:"name"`                // 名称
	Type             int                    `json:"type"`                // 类型
	DeviceType       int                    `json:"deviceType"`          // 设备类型
	BusinessType     int                    `json:"businessType"`        // 业务类型
	SampleType       string                 `json:"sampleType"`          // 样本类型
	PatientOrderNo   string                 `json:"patientOrderNo"`      // 就诊单号
	PatientOrderType string                 `json:"patientOrderType"`    // 就诊单类型
	DepartmentID     string                 `json:"departmentId"`        // 部门ID
	DepartmentName   string                 `json:"departmentName"`      // 部门名称
	DoctorID         string                 `json:"doctorId"`            // 医生ID
	DoctorName       string                 `json:"doctorName"`          // 医生名称
	CreatedTime      string                 `json:"createdTime"`         // 创建时间
	PatientInfo      ExaminationPatientInfo `json:"patientInfo"`         // 患者信息
	Status           int                    `json:"status"`              // 状态
	StatusName       string                 `json:"statusName"`          // 状态名称
	Diagnosis        string                 `json:"diagnosis,omitempty"` // 诊断
}

// RegistrationFormItem 挂号表单项
type RegistrationFormItem struct {
	PatientOrderID      string `json:"patientOrderId"`      // 就诊单ID
	RegistrationSheetID string `json:"registrationSheetId"` // 挂号单ID
	PatientID           string `json:"patientId"`           // 患者ID
	DepartmentID        string `json:"departmentId"`        // 部门ID
	DepartmentName      string `json:"departmentName"`      // 部门名称
	DoctorID            string `json:"doctorId"`            // 医生ID
	DoctorName          string `json:"doctorName"`          // 医生名称
	OrderNo             string `json:"orderNo"`             // 订单号
	ReserveDate         string `json:"reserveDate"`         // 预约日期
	ReserveTime         string `json:"reserveTime"`         // 预约时间
	IsReserved          int    `json:"isReserved"`          // 是否预约
	Status              int    `json:"status"`              // 状态
	SignIn              int    `json:"signIn"`              // 签到
	ConsultingRoomID    string `json:"consultingRoomId"`    // 诊室ID
	ConsultingRoomName  string `json:"consultingRoomName"`  // 诊室名称
	Type                int    `json:"type"`                // 类型
	PayStatus           int    `json:"payStatus"`           // 支付状态
	Code                int    `json:"code"`                // 代码
	RegistrationType    int    `json:"registrationType"`    // 挂号类型
	Created             string `json:"created"`             // 创建时间
}

// ExaminationDetail 检查检验单详情信息
type ExaminationDetail struct {
	ID                      string                  `json:"id"`                      // ID
	OutpatientFormItemID    string                  `json:"outpatientFormItemId"`    // 门诊表单项ID
	PatientOrderID          string                  `json:"patientOrderId"`          // 就诊单ID
	PatientOrderNumber      string                  `json:"patientOrderNumber"`      // 就诊单号
	Patient                 ExaminationPatientInfo  `json:"patient"`                 // 患者信息
	DoctorID                string                  `json:"doctorId"`                // 医生ID
	DoctorName              string                  `json:"doctorName"`              // 医生名称
	DoctorDepartmentID      string                  `json:"doctorDepartmentId"`      // 医生部门ID
	DoctorDepartmentName    string                  `json:"doctorDepartmentName"`    // 医生部门名称
	SellerID                string                  `json:"sellerId"`                // 销售ID
	SellerName              string                  `json:"sellerName"`              // 销售名称
	SellerDepartmentID      string                  `json:"sellerDepartmentId"`      // 销售部门ID
	SellerDepartmentName    string                  `json:"sellerDepartmentName"`    // 销售部门名称
	ProductID               string                  `json:"productId"`               // 产品ID
	Products                []ExaminationProduct    `json:"products"`                // 产品
	ProductIDs              []string                `json:"productIds"`              // 产品ID列表
	ComposeParentProductID  string                  `json:"composeParentProductId"`  // 组合父产品ID
	ComposeParentName       string                  `json:"composeParentName"`       // 组合父名称
	Name                    string                  `json:"name"`                    // 名称
	OrderNo                 string                  `json:"orderNo"`                 // 订单号
	Status                  int                     `json:"status"`                  // 状态
	StatusName              string                  `json:"statusName"`              // 状态名称
	SampleType              string                  `json:"sampleType"`              // 样本类型
	SamplerID               string                  `json:"samplerId"`               // 取样人ID
	SamplerName             string                  `json:"samplerName"`             // 取样人名称
	SampleTime              string                  `json:"sampleTime"`              // 取样时间
	CollectorID             string                  `json:"collectorId"`             // 收集人ID
	CollectorName           string                  `json:"collectorName"`           // 收集人名称
	CollectTime             string                  `json:"collectTime"`             // 收集时间
	Results                 []ExaminationResult     `json:"results"`                 // 结果
	EyeResults              []ExaminationEyeResult  `json:"eyeResults"`              // 眼科结果
	Reports                 []ExaminationReport     `json:"reports"`                 // 报告
	Attachments             []ExaminationAttachment `json:"attachments"`             // 附件
	Remark                  string                  `json:"remark"`                  // 备注
	TesterID                string                  `json:"testerId"`                // 检验人ID
	TesterName              string                  `json:"testerName"`              // 检验人名称
	TestTime                string                  `json:"testTime"`                // 检验时间
	CheckerID               string                  `json:"checkerId"`               // 审核人ID
	CheckerName             string                  `json:"checkerName"`             // 审核人名称
	CheckTime               string                  `json:"checkTime"`               // 审核时间
	ReportTime              string                  `json:"reportTime"`              // 报告时间
	Created                 string                  `json:"created"`                 // 创建时间
	Type                    int                     `json:"type"`                    // 类型
	SubType                 int                     `json:"subType"`                 // 子类型
	Diagnosis               string                  `json:"diagnosis"`               // 诊断
	ReportURL               string                  `json:"reportUrl"`               // 报告URL
	ExaminationApplySheetNo string                  `json:"examinationApplySheetNo"` // 检查申请单号
	BedNo                   string                  `json:"bedNo"`                   // 床号
	DeviceType              int                     `json:"deviceType"`              // 设备类型
	BusinessType            int                     `json:"businessType"`            // 业务类型
	RelationPatientOrderID  string                  `json:"relationPatientOrderId"`  // 关联就诊单ID
	RegistrationFormItem    RegistrationFormItem    `json:"registrationFormItem"`    // 挂号表单项
}

// ExaminationPageData 分页查询结果
type ExaminationPageData struct {
	Rows   interface{} `json:"rows"`   // 行
	Total  int         `json:"total"`  // 总数
	Offset int         `json:"offset"` // 偏移
	Limit  int         `json:"limit"`  // 限制
}

// ExaminationQueryResult 查询检查检验数据响应
type ExaminationQueryResult struct {
	Rows []ExaminationAllSummary `json:"rows"` // 行
}

// ExaminationCreateResponse 创建检查检验单响应
type ExaminationCreateResponse struct {
	ExaminationSheets []ExaminationAllSummary `json:"examinationSheets"` // 检查检验单
}

// ExaminationDevice 检验检查设备
type ExaminationDevice struct {
	DeviceID       int        `json:"deviceId"`       // 设备ID
	DeviceShortID  string     `json:"deviceShortId"`  // 设备短ID
	DeviceModelID  int        `json:"deviceModelId"`  // 设备型号ID
	Name           string     `json:"name"`           // 名称
	Model          string     `json:"model"`          // 型号
	DeviceUuid     string     `json:"deviceUuid"`     // 设备UUID
	Manufacture    string     `json:"manufacture"`    // 制造商
	IconURL        string     `json:"iconUrl"`        // 图标URL
	BuyURL         string     `json:"buyUrl"`         // 购买URL
	InstallURL     string     `json:"installUrl"`     // 安装URL
	Remarks        string     `json:"remarks"`        // 备注
	Type           int        `json:"type"`           // 类型
	ExtendSpec     string     `json:"extendSpec"`     // 扩展规格
	DeviceType     int        `json:"deviceType"`     // 设备类型
	UsageType      int        `json:"usageType"`      // 使用类型
	SalePrice      float64    `json:"salePrice"`      // 销售价格
	DeviceNamePy   string     `json:"deviceNamePy"`   // 设备名称拼音
	DeviceStatus   int        `json:"deviceStatus"`   // 设备状态
	DeviceRoomID   int        `json:"deviceRoomId"`   // 设备室ID
	DeviceRoomName string     `json:"deviceRoomName"` // 设备室名称
	Department     Department `json:"department"`     // 部门
}

// ExaminationDeviceResponse 检验检查设备列表响应
type ExaminationDeviceResponse struct {
	Devices []ExaminationDevice `json:"devices"` // 设备
}
