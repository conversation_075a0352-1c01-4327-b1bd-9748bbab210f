package handler

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"yekaitai/internal/modules/appointment/types"
	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// AbcYunDispensingDetailHandler 根据发药单ID查询发药单详情
func AbcYunDispensingDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.DispensingDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("发药单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/dispensing/%s", req.ID)

	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    types.DispensingDetail `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询发药单详情成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunDispensingQueryByDateHandler 按天查询发药单
func AbcYunDispensingQueryByDateHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.DispensingByDateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Date == "" {
		httpx.Error(w, errors.New("日期不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/dispensing/query-by-date"
	queryParams := map[string]string{
		"date": req.Date,
	}

	if req.DateType != 0 {
		queryParams["dateType"] = strconv.Itoa(req.DateType)
	}
	if req.Limit != 0 {
		queryParams["limit"] = strconv.Itoa(req.Limit)
	}
	if req.Offset != 0 {
		queryParams["offset"] = strconv.Itoa(req.Offset)
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.DispensingSummary `json:"rows"`
			Total  int                       `json:"total"`
			Offset int                       `json:"offset"`
			Limit  int                       `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "按天查询发药单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunDispensingDispenseAllHandler 发药
func AbcYunDispensingDispenseAllHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.DispensingDispenseAllRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.DispensingSheetID == "" {
		httpx.Error(w, errors.New("发药单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/dispensing/%s/dispense-all", req.DispensingSheetID)
	queryParams := map[string]string{}

	if req.OperatorID != "" {
		queryParams["operatorId"] = req.OperatorID
	}

	// 构建url参数
	url := path
	if len(queryParams) > 0 {
		url += "?"
		for k, v := range queryParams {
			url += fmt.Sprintf("%s=%s&", k, v)
		}
		url = url[:len(url)-1] // 移除最后的&
	}

	respBody, err := client.Put(url, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "发药成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunDispensingUndispenseAllHandler 退药
func AbcYunDispensingUndispenseAllHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.DispensingUndispenseAllRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.DispensingSheetID == "" {
		httpx.Error(w, errors.New("发药单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/dispensing/%s/undispense-all", req.DispensingSheetID)
	queryParams := map[string]string{}

	if req.OperatorID != "" {
		queryParams["operatorId"] = req.OperatorID
	}

	// 构建url参数
	url := path
	if len(queryParams) > 0 {
		url += "?"
		for k, v := range queryParams {
			url += fmt.Sprintf("%s=%s&", k, v)
		}
		url = url[:len(url)-1] // 移除最后的&
	}

	respBody, err := client.Put(url, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "退药成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunDispensingProcessingStatusHandler 修改加工状态
func AbcYunDispensingProcessingStatusHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.DispensingProcessingStatusRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.DispensingSheetID == "" {
		httpx.Error(w, errors.New("发药单ID不能为空"))
		return
	}
	if req.DispensingFormID == "" {
		httpx.Error(w, errors.New("发药单处方ID不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/dispensing/processing-status"

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "修改加工状态成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunDispensingDeliveryHandler 修改快递信息
func AbcYunDispensingDeliveryHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.DispensingDeliveryRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.DispensingSheetID == "" {
		httpx.Error(w, errors.New("发药单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := "/api/v2/open-agency/dispensing/delivery"

	// 将请求转为JSON字节数组
	reqData, err := json.Marshal(req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "修改快递信息成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunDispensingDeliveryTraceHandler 更新快递物流信息
func AbcYunDispensingDeliveryTraceHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 只从路径中获取快递单号参数
	parts := strings.Split(r.URL.Path, "/")
	deliveryOrderNo := ""
	for i, part := range parts {
		if part == "trace" && i < len(parts)-1 {
			deliveryOrderNo = parts[i+1]
			break
		}
	}

	if deliveryOrderNo == "" {
		httpx.Error(w, errors.New("快递单号不能为空"))
		return
	}

	// 只解析请求体一次
	var reqBody struct {
		DeliveryTraceData []struct {
			Context string `json:"context"`
			Ftime   string `json:"ftime"`
		} `json:"deliveryTraceData"`
	}
	if err := json.NewDecoder(r.Body).Decode(&reqBody); err != nil {
		httpx.Error(w, errors.New("解析请求体失败: "+err.Error()))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/dispensing/delivery/trace/%s", deliveryOrderNo)

	// 请求体数据直接使用解析到的JSON对象
	reqData, err := json.Marshal(reqBody)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	respBody, err := client.Put(path, reqData)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int                   `json:"code"`
		Message string                `json:"message"`
		Data    types.OperationResult `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "更新快递物流信息成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunDispensingByChargeSheetIdHandler 根据收费单ID查询发药单详情列表
func AbcYunDispensingByChargeSheetIdHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.DispensingByChargeSheetIDRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.ID == "" {
		httpx.Error(w, errors.New("收费单ID不能为空"))
		return
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/dispensing/by-charge-sheet-id/%s", req.ID)

	respBody, err := client.Get(path, nil)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			DispensingSheets []types.DispensingDetail `json:"dispensingSheets"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "根据收费单ID查询发药单详情列表成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}

// AbcYunDispensingByPatientHandler 查询患者发药单
func AbcYunDispensingByPatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req types.DispensingByPatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.PatientID == "" {
		httpx.Error(w, errors.New("患者ID不能为空"))
		return
	}

	// 设置默认值
	if req.BeginDate == "" {
		// 默认3个月前
		req.BeginDate = time.Now().AddDate(0, -3, 0).Format("2006-01-02")
	}
	if req.EndDate == "" {
		// 默认当前日期
		req.EndDate = time.Now().Format("2006-01-02")
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	// 调用ABC云API
	path := fmt.Sprintf("/api/v2/open-agency/dispensing/patient/%s", req.PatientID)

	queryParams := map[string]string{
		"beginDate": req.BeginDate,
		"endDate":   req.EndDate,
		"limit":     strconv.Itoa(req.Limit),
		"offset":    strconv.Itoa(req.Offset),
	}

	if req.Status != 0 {
		queryParams["status"] = strconv.Itoa(req.Status)
	}
	if req.ClinicID != "" {
		queryParams["clinicId"] = req.ClinicID
	}

	respBody, err := client.Get(path, queryParams)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 解析响应
	var result struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Rows   []types.DispensingPatientSummary `json:"rows"`
			Total  int                              `json:"total"`
			Offset int                              `json:"offset"`
			Limit  int                              `json:"limit"`
		} `json:"data"`
	}
	if err := json.Unmarshal(respBody, &result); err != nil {
		httpx.Error(w, err)
		return
	}

	if result.Code != 0 {
		httpx.Error(w, errors.New(result.Message))
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询患者发药单成功",
		"data":    result.Data,
	}
	httpx.OkJson(w, response)
}
