package main

import (
	"fmt"
	"log"

	"yekaitai/cmd/cron/tasks"
	"yekaitai/internal/modules/medlinker_sync/service"
	"yekaitai/pkg/adapters/medlinker"
)

// 医联数据同步使用示例
func main() {
	fmt.Println("医联数据同步功能示例")

	// 1. 创建医联客户端配置
	config := medlinker.Config{
		BaseURL:        "https://api.medlinker.com", // 医联API地址
		AppID:          "yekaitai_app",               // 应用ID
		AppSecret:      "your_app_secret",           // 应用密钥
		ModelID:        403,                         // 智能导诊模型ID
		DailyCallLimit: 1000,                       // 每日调用限制
	}

	// 2. 创建医联客户端
	medlinkerClient := medlinker.NewMedlinkerClient(config)

	// 3. 测试医联连接
	fmt.Println("\n=== 测试医联连接 ===")
	err := medlinkerClient.Login("18888888888")
	if err != nil {
		log.Printf("医联连接测试失败: %v", err)
	} else {
		fmt.Println("医联连接测试成功")
		
		// 获取token信息
		token := medlinkerClient.GetToken()
		if len(token) > 10 {
			fmt.Printf("获取到Token: %s...\n", token[:10])
		}

		// 获取调用次数信息
		count, limit, date := medlinkerClient.GetDailyCallInfo()
		fmt.Printf("今日调用次数: %d/%d (日期: %s)\n", count, limit, date)
	}

	// 4. 创建同步服务
	fmt.Println("\n=== 创建同步服务 ===")
	syncService := service.NewSyncService(medlinkerClient)

	// 5. 获取同步状态
	fmt.Println("\n=== 获取同步状态 ===")
	status, err := syncService.GetSyncStatus()
	if err != nil {
		log.Printf("获取同步状态失败: %v", err)
	} else {
		fmt.Printf("门店数量: %d\n", status.TotalStores)
		fmt.Printf("科室数量: %d\n", status.TotalDepartments)
		fmt.Printf("医生数量: %d\n", status.TotalDoctors)
		fmt.Printf("同步状态: %v\n", status.SyncEnabled)
	}

	// 6. 演示数据结构
	fmt.Println("\n=== 数据结构示例 ===")
	
	// 医院数据示例
	hospitalData := medlinker.HospitalData{
		HospitalID: "ykty_store_1",
		Name:       "叶开泰中医医院",
		Address:    "湖北省武汉市汉口中山大道1号",
		Longitude:  "114.298572",
		Latitude:   "30.584355",
	}
	fmt.Printf("医院数据: %+v\n", hospitalData)

	// 科室数据示例
	sectionData := medlinker.SectionData{
		SectionID:   "ykty_section_1_001",
		Name:        "内科",
		Description: "内科科室，主治内科疾病",
	}
	fmt.Printf("科室数据: %+v\n", sectionData)

	// 医生数据示例
	doctorData := medlinker.DoctorData{
		DoctorID:          "ykty_doctor_1",
		Name:              "张医生",
		Title:             "主任医师",
		Gender:            1,
		Birthday:          "1980-01-01",
		PracticeStartDate: "2000-01-01",
		Specialty:         "中医内科，擅长治疗呼吸系统疾病",
		Bio:               "从事中医内科临床工作20年，具有丰富的临床经验",
		Sections:          []string{"ykty_section_1_001"},
	}
	fmt.Printf("医生数据: %+v\n", doctorData)

	// 7. 演示同步请求
	fmt.Println("\n=== 同步请求示例 ===")
	
	// 创建医院请求
	createHospitalReq := medlinker.CreateHospitalRequest{
		HospitalID: hospitalData.HospitalID,
		Name:       hospitalData.Name,
		Address:    hospitalData.Address,
		Longitude:  hospitalData.Longitude,
		Latitude:   hospitalData.Latitude,
	}
	fmt.Printf("创建医院请求: %+v\n", createHospitalReq)

	// 创建科室请求
	createSectionReq := medlinker.CreateSectionRequest{
		HospitalID:  hospitalData.HospitalID,
		SectionID:   sectionData.SectionID,
		Name:        sectionData.Name,
		Description: sectionData.Description,
	}
	fmt.Printf("创建科室请求: %+v\n", createSectionReq)

	// 创建医生请求
	createDoctorReq := medlinker.CreateDoctorRequest{
		HospitalID:        hospitalData.HospitalID,
		DoctorID:          doctorData.DoctorID,
		Name:              doctorData.Name,
		Title:             doctorData.Title,
		Gender:            doctorData.Gender,
		Birthday:          doctorData.Birthday,
		PracticeStartDate: doctorData.PracticeStartDate,
		Specialty:         doctorData.Specialty,
		Bio:               doctorData.Bio,
		Sections:          doctorData.Sections,
		DataID:            "ykty_data_20240101",
	}
	fmt.Printf("创建医生请求: %+v\n", createDoctorReq)

	// 8. 演示定时任务
	fmt.Println("\n=== 定时任务示例 ===")
	
	// 初始化定时任务
	tasks.InitMedlinkerSyncTask()
	
	// 获取任务状态
	taskStatus := tasks.GetMedlinkerSyncStatus()
	fmt.Printf("任务状态: %+v\n", taskStatus)

	// 设置任务配置
	tasks.SetMedlinkerSyncEnabled(true)
	tasks.SetMedlinkerSyncInterval(15) // 15分钟间隔
	fmt.Println("任务配置已更新")

	// 9. 演示API调用（注意：这些调用需要真实的医联API）
	fmt.Println("\n=== API调用示例（仅演示，不实际调用）===")
	
	if false { // 设置为false避免实际调用
		// 创建医院
		resp, err := medlinkerClient.CreateHospital(&createHospitalReq)
		if err != nil {
			log.Printf("创建医院失败: %v", err)
		} else {
			fmt.Printf("创建医院响应: %+v\n", resp)
		}

		// 创建科室
		resp2, err := medlinkerClient.CreateSection(&createSectionReq)
		if err != nil {
			log.Printf("创建科室失败: %v", err)
		} else {
			fmt.Printf("创建科室响应: %+v\n", resp2)
		}

		// 创建医生
		resp3, err := medlinkerClient.CreateDoctor(&createDoctorReq)
		if err != nil {
			log.Printf("创建医生失败: %v", err)
		} else {
			fmt.Printf("创建医生响应: %+v\n", resp3)
		}
	}

	// 10. 使用建议
	fmt.Println("\n=== 使用建议 ===")
	fmt.Println("1. 首次使用建议先执行全量同步")
	fmt.Println("2. 日常使用建议配置定时增量同步")
	fmt.Println("3. 定期检查同步状态和日志")
	fmt.Println("4. 注意医联API调用次数限制")
	fmt.Println("5. 在业务低峰期执行大量数据同步")

	fmt.Println("\n医联数据同步功能示例完成")
}

// 演示如何手动执行同步任务
func demonstrateManualSync() {
	fmt.Println("\n=== 手动同步示例 ===")
	
	// 执行全量同步
	fmt.Println("执行全量同步...")
	tasks.RunMedlinkerFullSync()
	
	// 执行增量同步
	fmt.Println("执行增量同步...")
	tasks.RunMedlinkerIncrementalSync()
	
	// 测试连接
	fmt.Println("测试医联连接...")
	err := tasks.TestMedlinkerConnection()
	if err != nil {
		log.Printf("连接测试失败: %v", err)
	} else {
		fmt.Println("连接测试成功")
	}
}

// 演示错误处理
func demonstrateErrorHandling() {
	fmt.Println("\n=== 错误处理示例 ===")
	
	// 创建无效配置的客户端
	invalidConfig := medlinker.Config{
		BaseURL:        "https://invalid-url.com",
		AppID:          "invalid_app",
		AppSecret:      "invalid_secret",
		ModelID:        999,
		DailyCallLimit: 0,
	}
	
	invalidClient := medlinker.NewMedlinkerClient(invalidConfig)
	
	// 尝试登录（预期失败）
	err := invalidClient.Login("invalid_phone")
	if err != nil {
		fmt.Printf("预期的登录失败: %v\n", err)
	}
	
	// 演示如何处理不同类型的错误
	fmt.Println("错误处理建议:")
	fmt.Println("1. 网络错误：重试机制")
	fmt.Println("2. 认证错误：检查配置")
	fmt.Println("3. 限流错误：延迟重试")
	fmt.Println("4. 数据错误：记录日志并跳过")
}
