package routes

import (
	"net/http"
	"yekaitai/wx_internal/svc"
	"yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// RegisterAbcYunCallbackRoutes 注册ABC云回调相关路由
func RegisterAbcYunCallbackRoutes(server *rest.Server, serverCtx *svc.WxServiceContext) {
	// ABC云回调路由 (不需要认证，因为是外部系统回调)
	server.AddRoutes(
		[]rest.Route{
			// ABC云回调接口
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/abcyun/callback",
				Handler: AbcYunCallbackHandler(serverCtx),
			},
		},
	)
}

// AbcYunCallbackHandler 处理ABC云回调
func AbcYunCallbackHandler(serverCtx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 简单返回成功
		httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse("ABC云回调接收成功"))
	}
}
