package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
)

// 重用cron.go中的TencentMapConfig
// 不要重复定义结构体，当前两个包在同一个包下，可以直接使用

// AbcYunConfig ABC云诊所管家配置
type AbcYunConfig struct {
	BaseURL   string `json:"baseURL,optional"`
	AppID     string `json:"appID,optional"`
	AppSecret string `json:"appSecret,optional"`
	ClinicID  string `json:"clinicID,optional:true"` // 诊所ID
}

// MedlinkerConfig 医联配置
type MedlinkerConfig struct {
	BaseURL        string `json:"baseURL,optional"`
	AppID          string `json:"appID,optional"`
	AppSecret      string `json:"appSecret,optional"`
	ModelID        int    `json:"modelID,optional"`
	Phone          string `json:"phone,optional"`          // 医联登录电话号码
	DailyCallLimit int    `json:"dailyCallLimit,optional"` // 每日调用次数限制，0表示无限制
}

// YeKaiTaiConfig 叶开泰配置
type YeKaiTaiConfig struct {
	BaseURL       string `json:"baseURL,optional"`
	Username      string `json:"username,optional"`
	Password      string `json:"password,optional"`
	Wsjgid        string `json:"wsjgid,optional"`
	Wsjgmc        string `json:"wsjgmc,optional"`
	CurrentJsdj   string `json:"currentJsdj,optional"`
	CurrentJtid   string `json:"currentJtid,optional"`
	CurrentWsjgid string `json:"currentWsjgid,optional"`
	CurrentYhjsid string `json:"currentYhjsid,optional"`
	Czrid         string `json:"czrid,optional"`
	Czr           string `json:"czr,optional"`
	Zfrid         string `json:"zfrid,optional"`
	Zfrmc         string `json:"zfrmc,optional"`
}

// JushuitanConfig 聚水潭配置
type JushuitanConfig struct {
	BaseURL        string `json:"baseURL,optional"`        // API基础URL
	AppKey         string `json:"appKey,optional"`         // 开发者应用Key
	AppSecret      string `json:"appSecret,optional"`      // 开发者应用Secret
	AccessToken    string `json:"accessToken,optional"`    // 企业版商户授权token值
	AccessTokenPro string `json:"accessTokenPro,optional"` // 专业版商户授权token值
	IsEnterprise   bool   `json:"isEnterprise,optional"`   // 是否为企业版(true:企业版，false:专业版)
}

// ChongqingConfig 重庆HIS配置
type ChongqingConfig struct {
	BaseURL     string   `json:"baseURL,optional"`
	AppID       string   `json:"appID,optional"`
	AppSecret   string   `json:"appSecret,optional"`
	HospitalIDs []string `json:"hospitalIDs,optional"`
}

// WechatConfig 微信小程序配置
type WechatConfig struct {
	AppID     string `json:"appID,optional"`     // 小程序AppID
	AppSecret string `json:"appSecret,optional"` // 小程序AppSecret
}

// WechatPayConfig 微信支付配置
type WechatPayConfig struct {
	AppID       string `json:"appID,optional"`       // 微信AppID
	AppSecret   string `json:"appSecret,optional"`   // 微信AppSecret
	MchID       string `json:"mchID,optional"`       // 商户ID
	ApiV3Key    string `json:"apiV3Key,optional"`    // APIv3密钥
	Certificate string `json:"certificate,optional"` // 商户证书（PEM格式）
	PrivateKey  string `json:"privateKey,optional"`  // 商户私钥（PEM格式）
	SerialNo    string `json:"serialNo,optional"`    // 证书序列号
	NotifyURL   string `json:"notifyURL,optional"`   // 支付回调地址
}

// QiniuConfig 七牛云对象存储配置
type QiniuConfig struct {
	AccessKey string `json:"accessKey,optional"` // 七牛云 AccessKey
	SecretKey string `json:"secretKey,optional"` // 七牛云 SecretKey
	Bucket    string `json:"bucket,optional"`    // 七牛云存储空间名称
	Domain    string `json:"domain,optional"`    // 七牛云CDN域名
	Zone      string `json:"zone,optional"`      // 七牛云存储区域
}

// UploadConfig 上传配置
type UploadConfig struct {
	Qiniu  QiniuConfig       `json:"qiniu,optional"`  // 七牛云配置
	Config UploadFilesConfig `json:"config,optional"` // 文件上传配置
}

// UploadFilesConfig 文件上传配置
type UploadFilesConfig struct {
	MaxImageSize    int64    `json:"maxImageSize,optional"`    // 最大图片大小
	MaxDocumentSize int64    `json:"maxDocumentSize,optional"` // 最大文档大小
	MaxVideoSize    int64    `json:"maxVideoSize,optional"`    // 最大视频大小
	MaxGeneralSize  int64    `json:"maxGeneralSize,optional"`  // 最大通用文件大小
	AllowedDomains  []string `json:"allowedDomains,optional"`  // 允许的跨域来源
}

// WanLiNiuConfig 万里牛ERP配置
type WanLiNiuConfig struct {
	BaseURL    string `json:"baseURL,optional"`     // API基础URL
	AppKey     string `json:"appKey,optional"`      // 应用Key
	AppSecret  string `json:"appSecret,optional"`   // 应用密钥
	ShopNick   string `json:"shop_nick,optional"`   // 万里牛ERP中B2C店铺昵称
	ShopType   int    `json:"shop_type,optional"`   // 店铺类型，B2C平台：100
	ModifyTime string `json:"modify_time,optional"` // 库存同步起始时间
}

// Config 应用配置
type Config struct {
	rest.RestConf
	LogConf logx.LogConf
	Mysql   MysqlConfig
	Redis   struct {
		Addr     string
		Password string
		DB       int
		PoolSize int
	}
	JWT        JWTConfig `json:"jwt,optional"`
	AbcYun     AbcYunConfig
	Medlinker  MedlinkerConfig
	YeKaiTai   YeKaiTaiConfig
	Chongqing  ChongqingConfig
	Jushuitan  JushuitanConfig  `json:"jushuitan,optional"` // 聚水潭配置
	WanLiNiu   WanLiNiuConfig   `json:"wanLiNiu,optional"`  // 万里牛ERP配置
	Kafka      KafkaConfig      `json:"kafka,optional"`
	Wechat     WechatConfig     `json:"wechat,optional"`     // 微信小程序配置
	WechatPay  WechatPayConfig  `json:"wechatPay,optional"`  // 微信支付配置
	Upload     UploadConfig     `json:"upload,optional"`     // 上传配置
	TencentMap TencentMapConfig `json:"tencentMap,optional"` // 腾讯地图配置，注意是小写的tencentMap
	TencentSms TencentSmsConfig `json:"TencentSms,optional"` // 腾讯云短信配置
	Email      EmailConfig      `json:"Email,optional"`      // 邮件配置
}

// 日志配置 (使用go-zero的logx.LogConf)
type LogConf = logx.LogConf

// MySQL 配置
type MysqlConfig struct {
	Master struct {
		Addr            string
		MaxIdleConns    int
		MaxOpenConns    int
		ConnMaxLifetime int
	}
	Slave struct {
		Addr            string
		MaxIdleConns    int
		MaxOpenConns    int
		ConnMaxLifetime int
	}
}

// Redis 配置
type RedisConfig struct {
	Addr     string `json:"addr,optional"`
	Password string `json:"password,optional"`
	DB       int    `json:"db,optional"`
	PoolSize int    `json:"poolSize,optional"`
}

// 其他服务配置
type ApiConfig struct {
	BaseURL   string `json:"baseURL,optional"`
	AppID     string `json:"appID,optional"`
	AppSecret string `json:"appSecret,optional"`
}

type Medlinker struct {
	ApiConfig
	ModelID int    `json:"modelID,optional"`
	Phone   string `json:"phone,optional"` // 医联登录电话号码
}

type Chongqing struct {
	ApiConfig
	HospitalIDs []string `json:"hospitalIDs,optional"`
}

// Kafka 配置
type KafkaConfig struct {
	Brokers            []string `json:"brokers,optional"`
	Topic              string   `json:"topic,optional"`
	Group              string   `json:"group,optional"`
	Username           string   `json:"username,optional"`           // SASL用户名（可选）
	Password           string   `json:"password,optional"`           // SASL密码（可选）
	SASLMechanism      string   `json:"saslMechanism,optional"`      // SASL机制: PLAIN, SCRAM-SHA-256, SCRAM-SHA-512
	EnableTLS          bool     `json:"enableTLS,optional"`          // 是否启用TLS
	InsecureSkipVerify bool     `json:"insecureSkipVerify,optional"` // 跳过TLS证书验证
	CaCertPath         string   `json:"caCertPath,optional"`         // CA证书路径
	AutoCreateTopic    bool     `json:"autoCreateTopic,optional"`    // 自动创建Topic
}

// JWTConfig JWT配置
type JWTConfig struct {
	// 原有通用配置
	AccessSecret     string `json:"accessSecret,optional"`
	AccessExpire     int64  `json:"accessExpire,optional"`
	RefreshSecret    string `json:"refreshSecret,optional"`
	RefreshExpire    int64  `json:"refreshExpire,optional"`
	RefreshTokenKey  string `json:"refreshTokenKey,optional"`
	BlacklistPrefKey string `json:"blacklistPrefKey,optional"`

	// 管理系统专用密钥
	AdminAccessSecret  string `json:"adminAccessSecret,optional"`
	AdminAccessExpire  int64  `json:"adminAccessExpire,optional"`
	AdminRefreshSecret string `json:"adminRefreshSecret,optional"`
	AdminRefreshExpire int64  `json:"adminRefreshExpire,optional"`

	// 微信小程序专用密钥
	WxAccessSecret  string `json:"wxAccessSecret,optional"`
	WxAccessExpire  int64  `json:"wxAccessExpire,optional"`
	WxRefreshSecret string `json:"wxRefreshSecret,optional"`
	WxRefreshExpire int64  `json:"wxRefreshExpire,optional"`
}

// HangzhouConfig 杭州HIS系统配置
type HangzhouConfig struct {
	BaseURL       string `json:"baseURL"`       // HIS系统基础URL
	Port          int    `json:"port"`          // HIS系统端口
	ClientSecret  string `json:"clientSecret"`  // 客户端密钥
	UserName      string `json:"userName"`      // 用户名
	Password      string `json:"password"`      // 密码
	CurrentJsdj   string `json:"currentJsdj"`   // His提供的参数
	CurrentJtid   string `json:"currentJtid"`   // His提供的参数
	CurrentWsjgid string `json:"currentWsjgid"` // His提供的参数
	CurrentYhjsid string `json:"currentYhjsid"` // His提供的参数
}

// TencentSmsConfig 腾讯云短信配置
type TencentSmsConfig struct {
	SecretId   string `json:"SecretId,optional"`   // 腾讯云SecretId
	SecretKey  string `json:"SecretKey,optional"`  // 腾讯云SecretKey
	SdkAppId   string `json:"SdkAppId,optional"`   // 短信应用ID
	SignName   string `json:"SignName,optional"`   // 短信签名
	TemplateId string `json:"TemplateId,optional"` // 短信模板ID
	Region     string `json:"Region,optional"`     // 地域参数，默认ap-guangzhou
}

// EmailConfig 邮件配置
type EmailConfig struct {
	Host           string `json:"Host,optional" mapstructure:"Host"`                     // SMTP服务器地址
	Username       string `json:"Username,optional" mapstructure:"Username"`             // 邮箱用户名
	Password       string `json:"Password,optional" mapstructure:"Password"`             // 邮箱密码
	EmailPort      int    `json:"EmailPort,optional" mapstructure:"EmailPort"`           // SMTP端口
	RecipientEmail string `json:"RecipientEmail,optional" mapstructure:"RecipientEmail"` // 收件人邮箱
}
