package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/user_points/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// UserPointsRoutes 叶小币管理路由
func UserPointsRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.ServiceContext) {
	userPointsHandler := handler.NewUserPointsHandler()
	userPointsRulesHandler := handler.NewUserPointsRulesHandler()

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 叶小币管理路由组（需要管理员认证）
	server.AddRoutes(
		[]rest.Route{
			// 全局配置相关
			{
				Method:  "GET",
				Path:    "/api/admin/user-points/global-config",
				Handler: adminAuthWrapper(userPointsHandler.GetGlobalConfig),
			},
			{
				Method:  "POST",
				Path:    "/api/admin/user-points/global-config",
				Handler: adminAuthWrapper(userPointsHandler.CreateGlobalConfig),
			},
			{
				Method:  "PUT",
				Path:    "/api/admin/user-points/global-config/:id",
				Handler: adminAuthWrapper(userPointsHandler.UpdateGlobalConfig),
			},

			// 批量规则管理相关
			{
				Method:  "POST",
				Path:    "/api/admin/user-points/rules/batch",
				Handler: adminAuthWrapper(userPointsRulesHandler.SaveRules),
			},

			// 获取规则列表
			{
				Method:  "GET",
				Path:    "/api/admin/user-points/rules/batch",
				Handler: adminAuthWrapper(userPointsRulesHandler.GetRules),
			},

			// 获取规则参数映射
			{
				Method:  "GET",
				Path:    "/api/admin/user-points/rules/parameters",
				Handler: adminAuthWrapper(userPointsRulesHandler.GetRuleParameters),
			},

			// 辅助接口
			{
				Method:  "GET",
				Path:    "/api/admin/user-points/user-levels",
				Handler: adminAuthWrapper(userPointsHandler.GetUserLevels),
			},
			{
				Method:  "GET",
				Path:    "/api/admin/user-points/rule-types",
				Handler: adminAuthWrapper(userPointsHandler.GetRuleTypes),
			},
			{
				Method:  "GET",
				Path:    "/api/admin/user-points/expiry-types",
				Handler: adminAuthWrapper(userPointsHandler.GetExpiryTypes),
			},
			{
				Method:  "GET",
				Path:    "/api/admin/user-points/activity-types",
				Handler: adminAuthWrapper(userPointsHandler.GetActivityTypes),
			},
		},
	)
}
