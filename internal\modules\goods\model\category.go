package model

import (
	"time"
)

// Category 商品分类表
type Category struct {
	ID             uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:分类ID"`
	CategoryID     string    `json:"category_id" gorm:"type:varchar(50);uniqueIndex;comment:分类编码，对应万里牛分类ID"`
	ParentID       uint      `json:"parent_id" gorm:"default:0;not null;comment:父分类ID，0为顶级分类"`
	CategoryName   string    `json:"category_name" gorm:"type:varchar(100);not null;comment:分类名称"`
	CategoryIcon   string    `json:"category_icon" gorm:"type:varchar(255);comment:分类图标"`
	CategoryImage  string    `json:"category_image" gorm:"type:varchar(255);comment:分类图片"`
	Description    string    `json:"description" gorm:"type:text;comment:分类描述"`
	SortOrder      int       `json:"sort_order" gorm:"default:0;comment:排序"`
	IsRecommended  int       `json:"is_recommended" gorm:"type:tinyint(1);default:0;comment:是否推荐"`
	RecommendOrder int       `json:"recommend_order" gorm:"default:0;comment:推荐排序，从1开始，数字越小越靠前"`
	Status         int       `json:"status" gorm:"default:1;not null;comment:状态(0-禁用，1-启用)"`
	Level          int       `json:"level" gorm:"default:1;not null;comment:分类层级"`
	Path           string    `json:"path" gorm:"type:varchar(500);comment:分类路径，如1,2,3"`
	CreatedAt      time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"comment:更新时间"`

	// 关联字段
	Children   []Category `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Parent     *Category  `json:"parent,omitempty" gorm:"foreignKey:ParentID;references:ID"`
	GoodsCount int64      `json:"goods_count,omitempty" gorm:"-"` // 商品数量，不存储在数据库
}

// CategoryTree 分类树形结构
type CategoryTree struct {
	ID             uint            `json:"id"`
	CategoryID     string          `json:"category_id"`
	ParentID       uint            `json:"parent_id"`
	CategoryName   string          `json:"category_name"`
	CategoryIcon   string          `json:"category_icon"`
	CategoryImage  string          `json:"category_image"`
	Description    string          `json:"description"`
	SortOrder      int             `json:"sort_order"`
	IsRecommended  int             `json:"is_recommended"`
	RecommendOrder int             `json:"recommend_order"`
	Status         int             `json:"status"`
	Level          int             `json:"level"`
	Path           string          `json:"path"`
	GoodsCount     int64           `json:"goods_count"`
	Children       []*CategoryTree `json:"children,omitempty"`
	CreatedAt      time.Time       `json:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at"`
}

// CategoryQueryParams 分类查询参数
type CategoryQueryParams struct {
	ID            uint   `form:"id"`
	ParentID      *uint  `form:"parent_id"`
	CategoryName  string `form:"category_name"`
	Status        *int   `form:"status"`
	IsRecommended *int   `form:"is_recommended"`
	Level         *int   `form:"level"`
	Page          int    `form:"page"`
	PageSize      int    `form:"page_size"`
}

// CategoryCreateRequest 创建分类请求
type CategoryCreateRequest struct {
	ParentID       uint   `json:"parent_id,optional" validate:"min=0"`
	CategoryName   string `json:"category_name" validate:"required,max=100"`
	CategoryIcon   string `json:"category_icon,optional"`
	CategoryImage  string `json:"category_image,optional"`
	Description    string `json:"description,optional"`
	SortOrder      int    `json:"sort_order,optional"`
	IsRecommended  int    `json:"is_recommended,optional" validate:"oneof=0 1"`
	RecommendOrder int    `json:"recommend_order,optional"`
	Status         int    `json:"status,optional" validate:"oneof=0 1"`
}

// CategoryUpdateRequest 更新分类请求
type CategoryUpdateRequest struct {
	CategoryName   string `json:"category_name" validate:"required,max=100"`
	CategoryIcon   string `json:"category_icon,optional"`
	CategoryImage  string `json:"category_image,optional"`
	Description    string `json:"description,optional"`
	SortOrder      int    `json:"sort_order,optional"`
	IsRecommended  int    `json:"is_recommended,optional" validate:"oneof=0 1"`
	RecommendOrder int    `json:"recommend_order,optional"`
	Status         int    `json:"status,optional" validate:"oneof=0 1"`
}

// TableName 表名
func (Category) TableName() string {
	return "goods_category"
}

// GetFullPath 获取完整分类路径名称
func (c *Category) GetFullPath() string {
	// 这个方法需要根据实际业务逻辑实现
	return c.CategoryName
}

// CategoryResponse 分类响应结构
type CategoryResponse struct {
	ID             uint      `json:"id"`
	CategoryID     string    `json:"category_id"`
	ParentID       uint      `json:"parent_id"`
	CategoryName   string    `json:"category_name"`
	CategoryIcon   string    `json:"category_icon"`
	CategoryImage  string    `json:"category_image"`
	Description    string    `json:"description"`
	SortOrder      int       `json:"sort_order"`
	IsRecommended  int       `json:"is_recommended"`
	RecommendOrder int       `json:"recommend_order"`
	Status         int       `json:"status"`
	Level          int       `json:"level"`
	Path           string    `json:"path"`
	GoodsCount     int64     `json:"goods_count"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// 预定义的默认分类常量
const (
	DefaultCategoryID   = "default_other" // 默认分类ID
	DefaultCategoryName = "其他"            // 默认分类名称
)

// IsDefaultCategory 判断是否为默认分类（不可删除的分类）
func (c *Category) IsDefaultCategory() bool {
	return c.CategoryID == DefaultCategoryID || c.CategoryName == DefaultCategoryName
}

// InitDefaultCategory 初始化默认分类
func InitDefaultCategory(db interface{}) error {
	// 这里用interface{}是为了兼容不同的数据库接口
	// 在实际使用时会传入*gorm.DB
	return nil // 具体实现在service层
}
