package user

import (
	"database/sql"
	"time"
)

// WxUser 微信用户表
type WxUser struct {
	UserID       uint         `json:"user_id" gorm:"primaryKey;autoIncrement;comment:用户ID"`
	OpenID       string       `json:"open_id" gorm:"type:varchar(64);not null;comment:微信开放平台ID"`
	UnionID      string       `json:"union_id" gorm:"type:varchar(64);comment:微信开放平台唯一ID"`
	Mobile       string       `json:"mobile" gorm:"type:varchar(20);comment:手机号"`
	Nickname     string       `json:"nickname" gorm:"type:varchar(50);comment:用户昵称"`
	Avatar       string       `json:"avatar" gorm:"type:varchar(255);comment:用户头像"`
	Gender       int          `json:"gender" gorm:"default:0;not null;comment:性别(0-未知，1-男，2-女)"`
	Status       int          `json:"status" gorm:"default:1;not null;comment:状态(1-正常，0-禁用)"`
	UserLevelID  *uint        `json:"user_level_id" gorm:"default:1;comment:用户等级ID;index:idx_user_level_id"`
	RegisterDate time.Time    `json:"register_date" gorm:"not null;default:CURRENT_TIMESTAMP;type:timestamp;comment:注册日期"`
	Tags         []Tag        `json:"tags" gorm:"-"` // 手动管理，不使用many2many
	CreatedAt    time.Time    `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt    time.Time    `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt    sql.NullTime `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 设置WxUser表名
func (WxUser) TableName() string {
	return "wx_user"
}
