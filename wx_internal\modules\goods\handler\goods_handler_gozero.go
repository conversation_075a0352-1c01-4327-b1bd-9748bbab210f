package handler

import (
	"net/http"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/internal/modules/goods/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// WxGoodsGoZeroHandler 小程序端go-zero格式的商品处理器
type WxGoodsGoZeroHandler struct {
	goodsService    *service.GoodsService
	categoryService *service.CategoryService
}

func NewWxGoodsGoZeroHandler() *WxGoodsGoZeroHandler {
	return &WxGoodsGoZeroHandler{
		goodsService:    service.NewGoodsService(),
		categoryService: service.NewCategoryService(),
	}
}

// 商品详情请求
type WxGoodsDetailRequest struct {
	GoodsID uint `path:"id"` // 商品ID
}

// 分类详情请求
type WxCategoryDetailRequest struct {
	ID uint `path:"id"` // 分类ID
}

// GetCategories 获取分类列表
func (h *WxGoodsGoZeroHandler) GetCategories(w http.ResponseWriter, r *http.Request) {
	var req struct {
		types.PageRequest
		IsRecommended *int `form:"is_recommended,optional"` // 是否推荐
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	// 转换为service需要的参数
	params := &model.CategoryQueryParams{
		Page:          req.Page,
		PageSize:      req.Size,
		Status:        &[]int{1}[0], // 状态为启用
		IsRecommended: req.IsRecommended,
	}

	categories, total, err := h.categoryService.ListCategories(r.Context(), params)
	if err != nil {
		logx.Errorf("获取分类列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 使用统一分页响应
	resp := types.NewPageResponse(categories, total, &req.PageRequest)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// GetCategoryDetail 获取分类详情（包含子分类）
func (h *WxGoodsGoZeroHandler) GetCategoryDetail(w http.ResponseWriter, r *http.Request) {
	var req WxCategoryDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取分类基本信息
	category, err := h.categoryService.GetCategory(r.Context(), req.ID)
	if err != nil {
		logx.Errorf("获取分类详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 获取子分类树
	children, err := h.categoryService.GetCategoryTree(r.Context(), req.ID)
	if err != nil {
		logx.Errorf("获取子分类失败: %v", err)
		// 子分类获取失败不影响主分类返回，设置为空数组
		children = []*model.CategoryTree{}
	}

	// 构建响应数据
	result := map[string]interface{}{
		"id":              category.ID,
		"category_id":     category.CategoryID,
		"parent_id":       category.ParentID,
		"category_name":   category.CategoryName,
		"category_icon":   category.CategoryIcon,
		"category_image":  category.CategoryImage,
		"description":     category.Description,
		"sort_order":      category.SortOrder,
		"is_recommended":  category.IsRecommended,
		"recommend_order": category.RecommendOrder,
		"status":          category.Status,
		"level":           category.Level,
		"path":            category.Path,
		"goods_count":     category.GoodsCount,
		"children":        children,
		"created_at":      category.CreatedAt,
		"updated_at":      category.UpdatedAt,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetHomePage 获取首页商品列表
func (h *WxGoodsGoZeroHandler) GetHomePage(w http.ResponseWriter, r *http.Request) {
	var req struct {
		types.PageRequest
		CategoryID    *string `form:"category_id,optional"`    // 分类ID
		IsRecommended *int    `form:"is_recommended,optional"` // 是否推荐
		Keyword       string  `form:"keyword,optional"`        // 搜索关键词
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 转换为service需要的参数
	params := &model.GoodsQueryParams{
		Page:       req.Page,
		PageSize:   req.Size,
		CategoryID: "",
		GoodsName:  req.Keyword,
		IsOnSale:   &[]int{1}[0], // 只显示上架商品
		Status:     &[]int{1}[0], // 只显示启用商品
	}

	if req.CategoryID != nil {
		params.CategoryID = *req.CategoryID
	}
	if req.IsRecommended != nil {
		params.IsRecommended = req.IsRecommended
	}

	goods, total, err := h.goodsService.ListGoods(r.Context(), params)
	if err != nil {
		logx.Errorf("获取商品列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	resp := types.NewPageResponse(goods, total, &req.PageRequest)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// GetGoodsDetail 获取商品详情
func (h *WxGoodsGoZeroHandler) GetGoodsDetail(w http.ResponseWriter, r *http.Request) {
	var req WxGoodsDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	goods, err := h.goodsService.GetGoods(r.Context(), req.GoodsID)
	if err != nil {
		logx.Errorf("获取商品详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 增加浏览次数（异步处理，不影响响应）
	go func() {
		// 这里可以调用浏览量统计服务
		logx.Infof("商品ID %d 被访问", req.GoodsID)
	}()

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(goods))
}

// SearchGoods 搜索商品
func (h *WxGoodsGoZeroHandler) SearchGoods(w http.ResponseWriter, r *http.Request) {
	var req struct {
		types.PageRequest
		Keyword string `form:"keyword,optional"` // 搜索关键词
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 转换为service需要的参数
	params := &model.GoodsQueryParams{
		Page:       req.Page,
		PageSize:   req.Size,
		CategoryID: "",
		GoodsName:  req.Keyword,
		IsOnSale:   &[]int{1}[0], // 只显示上架商品
		Status:     &[]int{1}[0], // 只显示启用商品
	}

	goods, total, err := h.goodsService.ListGoods(r.Context(), params)
	if err != nil {
		logx.Errorf("搜索商品失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	resp := types.NewPageResponse(goods, total, &req.PageRequest)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}
