package routes

import (
	"net/http"

	areaHandlerPkg "yekaitai/internal/modules/area/handler"
	"yekaitai/internal/svc"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterAreaRoutes 注册区域管理相关路由
func RegisterAreaRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 初始化区域管理处理器
	areaHandler := areaHandlerPkg.NewAreaHandler(mysql.GetDB())

	// 区域管理路由（外层已有认证中间件，无需重复添加）
	server.AddRoutes(
		[]rest.Route{
			// 获取已开通地区列表（分页）
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/areas",
				Handler: areaHandler.GetEnabledAreaList,
			},
			// 添加已开通地区
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/areas",
				Handler: areaHandler.AddEnabledArea,
			},
			// 删除已开通地区
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/areas",
				Handler: areaHandler.DeleteEnabledArea,
			},
			// 获取已开通的所有省份
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/areas/enabled/provinces",
				Handler: areaHandler.GetEnabledProvinces,
			},
			// 获取指定省份下已开通的所有城市
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/areas/enabled/cities",
				Handler: areaHandler.GetEnabledCities,
			},
			// 获取指定城市下已开通的所有区县
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/areas/enabled/districts",
				Handler: areaHandler.GetEnabledAreas,
			},
		},
	)
}
