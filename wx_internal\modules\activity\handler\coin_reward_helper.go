package handler

import (
	"context"

	"yekaitai/internal/service"

	"github.com/zeromicro/go-zero/core/logx"
)

// CoinRewardHelper 积分奖励助手
type CoinRewardHelper struct {
	coinRewardService *service.CoinRewardService
}

// NewCoinRewardHelper 创建积分奖励助手
func NewCoinRewardHelper() *CoinRewardHelper {
	return &CoinRewardHelper{
		coinRewardService: service.NewCoinRewardService(),
	}
}

// ProcessActivitySignUpReward 处理活动报名积分奖励
func (h *CoinRewardHelper) ProcessActivitySignUpReward(userID uint, orderNo, description string) {
	// 处理普通活动报名奖励
	err := h.coinRewardService.ProcessActivityReward(context.Background(), userID, "ACTIVITY_SIGNUP", orderNo)
	if err != nil {
		logx.Errorf("处理活动报名积分奖励失败: userID=%d, orderNo=%s, error=%v", userID, orderNo, err)
	} else {
		logx.Infof("活动报名积分奖励处理成功: userID=%d, orderNo=%s", userID, orderNo)
	}
}

// ProcessSpecialActivitySignUpReward 处理专项活动报名积分奖励
func (h *CoinRewardHelper) ProcessSpecialActivitySignUpReward(userID uint, orderNo, description string) {
	// 处理专项活动报名奖励
	err := h.coinRewardService.ProcessActivityReward(context.Background(), userID, "SPECIAL_ACTIVITY_SIGNUP", orderNo)
	if err != nil {
		logx.Errorf("处理专项活动报名积分奖励失败: userID=%d, orderNo=%s, error=%v", userID, orderNo, err)
	} else {
		logx.Infof("专项活动报名积分奖励处理成功: userID=%d, orderNo=%s", userID, orderNo)
	}
}

// ProcessActivitySignUpRewardRefund 处理活动报名积分奖励回收
func (h *CoinRewardHelper) ProcessActivitySignUpRewardRefund(userID uint, orderNo string) {
	// 回收活动报名奖励积分
	err := h.coinRewardService.RefundCoinsForOrder(context.Background(), userID, orderNo)
	if err != nil {
		logx.Errorf("回收活动报名积分奖励失败: userID=%d, orderNo=%s, error=%v", userID, orderNo, err)
	} else {
		logx.Infof("活动报名积分奖励回收成功: userID=%d, orderNo=%s", userID, orderNo)
	}
}
