package model

import (
	"time"

	"yekaitai/pkg/infra/mysql"
)

// Create 创建预约
func (r *appointmentRepository) Create(appointment *Appointment) error {
	return mysql.Master().Create(appointment).Error
}

// Update 更新预约
func (r *appointmentRepository) Update(appointment *Appointment) error {
	return mysql.Master().Save(appointment).Error
}

// Delete 删除预约
func (r *appointmentRepository) Delete(id uint) error {
	return mysql.Master().Delete(&Appointment{}, id).Error
}

// FindByID 根据ID查找预约
func (r *appointmentRepository) FindByID(id uint) (*Appointment, error) {
	var appointment Appointment
	err := mysql.Slave().Where("id = ?", id).First(&appointment).Error
	if err != nil {
		return nil, err
	}
	return &appointment, nil
}

// FindByOrderNo 根据订单号查找预约
func (r *appointmentRepository) FindByOrderNo(orderNo string) (*Appointment, error) {
	var appointment Appointment
	err := mysql.Slave().Where("order_no = ?", orderNo).First(&appointment).Error
	if err != nil {
		return nil, err
	}
	return &appointment, nil
}

// List 获取预约列表
func (r *appointmentRepository) List(page, size int) ([]*Appointment, int64, error) {
	var appointments []*Appointment
	var total int64

	db := mysql.Slave()

	// 获取总数
	if err := db.Model(&Appointment{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&appointments).Error; err != nil {
		return nil, 0, err
	}

	return appointments, total, nil
}

// ListByUser 根据用户ID获取预约列表
func (r *appointmentRepository) ListByUser(userID uint, page, size int) ([]*Appointment, int64, error) {
	var appointments []*Appointment
	var total int64

	db := mysql.Slave().Where("user_id = ?", userID)

	// 获取总数
	if err := db.Model(&Appointment{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&appointments).Error; err != nil {
		return nil, 0, err
	}

	return appointments, total, nil
}

// ListByDoctor 根据医生ID获取预约列表
func (r *appointmentRepository) ListByDoctor(doctorID uint, page, size int) ([]*Appointment, int64, error) {
	var appointments []*Appointment
	var total int64

	db := mysql.Slave().Where("doctor_id = ?", doctorID)

	// 获取总数
	if err := db.Model(&Appointment{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&appointments).Error; err != nil {
		return nil, 0, err
	}

	return appointments, total, nil
}

// ListByDate 根据日期获取医生的预约列表
func (r *appointmentRepository) ListByDate(doctorID uint, date time.Time) ([]*Appointment, error) {
	var appointments []*Appointment

	// 获取指定日期的预约
	start := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	end := start.Add(24 * time.Hour)

	err := mysql.Slave().Where("doctor_id = ? AND date >= ? AND date < ?", doctorID, start, end).Find(&appointments).Error
	if err != nil {
		return nil, err
	}

	return appointments, nil
}

// UpdateStatus 更新预约状态
func (r *appointmentRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&Appointment{}).Where("id = ?", id).Update("status", status).Error
}

// UpdatePayStatus 更新支付状态
func (r *appointmentRepository) UpdatePayStatus(id uint, payStatus int, payMethod int, payTime time.Time) error {
	return mysql.Master().Model(&Appointment{}).Where("id = ?", id).Updates(map[string]interface{}{
		"pay_status": payStatus,
		"pay_method": payMethod,
		"pay_time":   payTime,
	}).Error
}

// UpdateRefundStatus 更新退款状态
func (r *appointmentRepository) UpdateRefundStatus(id uint, refundStatus int, refundTime time.Time) error {
	return mysql.Master().Model(&Appointment{}).Where("id = ?", id).Updates(map[string]interface{}{
		"refund_status": refundStatus,
		"refund_time":   refundTime,
	}).Error
}
