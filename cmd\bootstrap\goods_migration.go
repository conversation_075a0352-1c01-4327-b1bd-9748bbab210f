package bootstrap

import (
	"yekaitai/internal/modules/goods/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateGoodsTables 执行商品管理模块表结构迁移
func MigrateGoodsTables() error {
	db := mysql.Master()

	logx.Info("开始执行商品管理模块表结构迁移...")

	// 自动迁移商品分类表
	if err := db.AutoMigrate(&model.Category{}); err != nil {
		logx.Errorf("商品分类表迁移失败: %v", err)
		return err
	}
	logx.Info("商品分类表迁移完成")

	// 自动迁移商品表
	if err := db.AutoMigrate(&model.Goods{}); err != nil {
		logx.Errorf("商品表迁移失败: %v", err)
		return err
	}
	logx.Info("商品表迁移完成")

	// 自动迁移商品规格表
	if err := db.AutoMigrate(&model.GoodsSpec{}); err != nil {
		logx.Errorf("商品规格表迁移失败: %v", err)
		return err
	}
	logx.Info("商品规格表迁移完成")

	logx.Info("商品管理模块表结构迁移完成")
	return nil
}
