package tasks

import (
	"context"
	"fmt"
	"time"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/internal/modules/goods/service"
	"yekaitai/pkg/adapters/wanliniu"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// WanLiNiuCategorySyncService 万里牛商品分类同步服务
type WanLiNiuCategorySyncService struct {
	client          *wanliniu.Client
	service         *wanliniu.Service
	db              *gorm.DB
	categoryService *service.CategoryService
}

// NewWanLiNiuCategorySyncService 创建万里牛商品分类同步服务
func NewWanLiNiuCategorySyncService() *WanLiNiuCategorySyncService {
	return &WanLiNiuCategorySyncService{
		client:          wanliniu.GetClient(),
		service:         wanliniu.GetService(),
		db:              mysql.Master(),
		categoryService: service.NewCategoryService(),
	}
}

// SyncCategories 定时同步商品分类
func (s *WanLiNiuCategorySyncService) SyncCategories() error {
	logx.Info("[WanLiNiu] 开始定时同步商品分类...")

	// 确保默认分类存在
	if err := s.ensureDefaultCategory(); err != nil {
		logx.Errorf("[WanLiNiu] 初始化默认分类失败: %v", err)
		return fmt.Errorf("初始化默认分类失败: %w", err)
	}

	// 获取最后成功同步的时间
	lastSyncTime := s.getLastSyncTime()

	// 如果是首次同步或者距离上次同步超过1小时，进行同步
	if lastSyncTime == nil || time.Since(*lastSyncTime) > time.Hour {
		return s.syncCategoriesFromWanLiNiu()
	}

	logx.Info("[WanLiNiu] 距离上次分类同步时间不足1小时，跳过本次同步")
	return nil
}

// SyncCategoriesManual 手动同步商品分类
func (s *WanLiNiuCategorySyncService) SyncCategoriesManual() error {
	logx.Info("[WanLiNiu] 开始手动同步商品分类...")

	// 确保默认分类存在
	if err := s.ensureDefaultCategory(); err != nil {
		logx.Errorf("[WanLiNiu] 初始化默认分类失败: %v", err)
		return fmt.Errorf("初始化默认分类失败: %w", err)
	}

	return s.syncCategoriesFromWanLiNiu()
}

// ensureDefaultCategory 确保默认分类存在
func (s *WanLiNiuCategorySyncService) ensureDefaultCategory() error {
	ctx := context.Background()
	return s.categoryService.InitDefaultCategory(ctx)
}

// syncCategoriesFromWanLiNiu 从万里牛同步商品分类
func (s *WanLiNiuCategorySyncService) syncCategoriesFromWanLiNiu() error {
	if s.service == nil {
		return fmt.Errorf("万里牛服务未初始化")
	}

	ctx := context.Background()
	syncTime := time.Now()
	totalSynced := 0

	// 获取所有分类
	allCategoriesResp, err := s.service.GetAllCategories(ctx)
	if err != nil {
		s.saveSyncProgress(syncTime, 0, 0, fmt.Sprintf("获取万里牛分类失败: %v", err))
		return fmt.Errorf("获取万里牛分类失败: %w", err)
	}

	logx.Infof("[WanLiNiu] 从万里牛获取到 %d 个分类", allCategoriesResp.Total)

	// 同步每个分类
	for _, category := range allCategoriesResp.Data {
		if err := s.syncSingleCategory(ctx, category); err != nil {
			logx.Errorf("[WanLiNiu] 同步分类失败: ID=%s, 名称=%s, 错误=%v",
				category.CategoryID, category.CategoryName, err)
			continue
		}
		totalSynced++
	}

	// 保存同步进度
	s.saveSyncProgress(syncTime, 1, totalSynced, "同步成功")
	logx.Infof("[WanLiNiu] 分类同步完成，总共同步 %d 个分类", totalSynced)

	return nil
}

// syncSingleCategory 同步单个分类
func (s *WanLiNiuCategorySyncService) syncSingleCategory(ctx context.Context, categoryData wanliniu.CategoryDataSimple) error {
	// 检查分类是否已存在
	var existingCategory model.Category
	err := s.db.WithContext(ctx).Where("category_id = ?", categoryData.CategoryID).First(&existingCategory).Error

	if err == gorm.ErrRecordNotFound {
		// 分类不存在，创建新分类
		newCategory := model.Category{
			CategoryID:   categoryData.CategoryID,
			CategoryName: categoryData.CategoryName,
			ParentID:     0, // 万里牛的父级ID需要转换为本地ID，这里简化处理设为0
			Status:       1, // 默认启用
			Level:        1, // 默认一级分类
			Path:         "0",
			SortOrder:    0,
			Description:  "", // 不自动添加描述内容
		}

		// 处理父级分类
		if categoryData.ParentID != "" && categoryData.ParentID != "0" {
			// 查找父级分类
			var parentCategory model.Category
			if err := s.db.WithContext(ctx).Where("category_id = ?", categoryData.ParentID).First(&parentCategory).Error; err == nil {
				newCategory.ParentID = parentCategory.ID
				newCategory.Level = parentCategory.Level + 1
				newCategory.Path = fmt.Sprintf("%s,%d", parentCategory.Path, parentCategory.ID)
			}
		}

		if err := s.db.WithContext(ctx).Create(&newCategory).Error; err != nil {
			return fmt.Errorf("创建分类失败: %w", err)
		}
		logx.Infof("[WanLiNiu] 创建新分类: %s - %s", categoryData.CategoryID, categoryData.CategoryName)
	} else if err != nil {
		return fmt.Errorf("查询分类失败: %w", err)
	} else {
		// 分类存在，检查是否需要更新名称
		if existingCategory.CategoryName != categoryData.CategoryName {
			existingCategory.CategoryName = categoryData.CategoryName
			if err := s.db.WithContext(ctx).Save(&existingCategory).Error; err != nil {
				return fmt.Errorf("更新分类名称失败: %w", err)
			}
			logx.Infof("[WanLiNiu] 更新分类名称: %s - %s", categoryData.CategoryID, categoryData.CategoryName)
		}
	}

	return nil
}

// getLastSyncTime 获取最后成功同步的时间
func (s *WanLiNiuCategorySyncService) getLastSyncTime() *time.Time {
	var progress patient.SyncProgress
	err := s.db.Where("sync_type = ? AND status = ?", "wanliniu_category_sync", 1).
		Order("sync_date DESC").
		First(&progress).Error

	if err != nil {
		return nil // 没有找到记录，返回nil
	}

	// 解析同步时间
	if syncTime, parseErr := time.Parse("2006-01-02 15:04:05", progress.SyncDate); parseErr == nil {
		return &syncTime
	}

	// 如果解析失败，返回nil
	return nil
}

// saveSyncProgress 保存同步进度
func (s *WanLiNiuCategorySyncService) saveSyncProgress(syncTime time.Time, status int, records int, message string) {
	syncTimeStr := syncTime.Format("2006-01-02 15:04:05")

	progress := patient.SyncProgress{
		SyncType: "wanliniu_category_sync",
		SyncDate: syncTimeStr,
		Status:   status,
		Records:  records,
		Message:  message,
	}

	// 先查找是否已存在该时间的记录
	var existing patient.SyncProgress
	err := s.db.Where("sync_type = ? AND sync_date = ?", "wanliniu_category_sync", syncTimeStr).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 不存在，创建新记录
		s.db.Create(&progress)
		logx.Infof("[WanLiNiu] 保存分类同步进度: 时间=%s, 状态=%d, 记录数=%d", syncTimeStr, status, records)
	} else {
		// 存在，更新记录
		s.db.Model(&existing).Updates(map[string]interface{}{
			"status":  status,
			"records": records,
			"message": message,
		})
		logx.Infof("[WanLiNiu] 更新分类同步进度: 时间=%s, 状态=%d, 记录数=%d", syncTimeStr, status, records)
	}
}
