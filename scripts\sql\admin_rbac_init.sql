INSERT INTO `admin_permission` (`module`, `action`, `description`, `status`, `created_at`, `updated_at`) VALUES
('home', 'view', '首页', 1, NOW(), NOW()),

('user', 'view', '用户查看', 1, NOW(), NOW()),
('user', 'create', '用户创建', 1, NOW(), NOW()),
('user', 'update', '用户更新', 1, NOW(), NOW()),
('user', 'delete', '用户删除', 1, NOW(), NOW()),

('role', 'view', '角色查看', 1, NOW(), NOW()),
('role', 'create', '角色创建', 1, NOW(), NOW()),
('role', 'update', '角色更新', 1, NOW(), NOW()),
('role', 'delete', '角色删除', 1, NOW(), NOW()),

('region', 'view', '地区查看', 1, NOW(), NOW()),
('region', 'create', '地区创建', 1, NOW(), NOW()),
('region', 'update', '地区更新', 1, NOW(), NOW()),
('region', 'delete', '地区删除', 1, NOW(), NOW()),

('store', 'view', '门店查看', 1, NOW(), NOW()),
('store', 'create', '门店创建', 1, NOW(), NOW()),
('store', 'update', '门店更新', 1, NOW(), NOW()),
('store', 'delete', '门店删除', 1, NOW(), NOW()),

('package', 'view', '套餐查看', 1, NOW(), NOW()),
('package', 'create', '套餐创建', 1, NOW(), NOW()),
('package', 'update', '套餐更新', 1, NOW(), NOW()),
('package', 'delete', '套餐删除', 1, NOW(), NOW()),

('doctor', 'view', '医生查看', 1, NOW(), NOW()),
('doctor', 'create', '医生创建', 1, NOW(), NOW()),
('doctor', 'update', '医生更新', 1, NOW(), NOW()),
('doctor', 'delete', '医生删除', 1, NOW(), NOW()),

('patient', 'view', '患者查看', 1, NOW(), NOW()),
('patient', 'create', '患者创建', 1, NOW(), NOW()),
('patient', 'update', '患者更新', 1, NOW(), NOW()),
('patient', 'delete', '患者删除', 1, NOW(), NOW()),

('order', 'view', '订单查看', 1, NOW(), NOW()),
('order', 'create', '订单创建', 1, NOW(), NOW()),
('order', 'update', '订单更新', 1, NOW(), NOW()),
('order', 'delete', '订单删除', 1, NOW(), NOW()),

('coupon', 'view', '优惠券查看', 1, NOW(), NOW()),
('coupon', 'create', '优惠券创建', 1, NOW(), NOW()),
('coupon', 'update', '优惠券更新', 1, NOW(), NOW()),
('coupon', 'delete', '优惠券删除', 1, NOW(), NOW()),

('content', 'view', '内容查看', 1, NOW(), NOW()),
('content', 'create', '内容创建', 1, NOW(), NOW()),
('content', 'update', '内容更新', 1, NOW(), NOW()),
('content', 'delete', '内容删除', 1, NOW(), NOW()),

('report', 'view', '报表查看', 1, NOW(), NOW()),
('report', 'create', '报表创建', 1, NOW(), NOW()),
('report', 'update', '报表更新', 1, NOW(), NOW()),
('report', 'delete', '报表删除', 1, NOW(), NOW()),

('log', 'view', '日志查看', 1, NOW(), NOW()),
('log', 'create', '日志创建', 1, NOW(), NOW()),
('log', 'update', '日志更新', 1, NOW(), NOW()),
('log', 'delete', '日志删除', 1, NOW(), NOW());

INSERT INTO `admin_role` (`role_id`, `role_name`, `description`, `is_system`, `status`, `created_at`, `updated_at`) VALUES
(1001, '超级管理员', '拥有所有权限的管理员', 1, 1, NOW(), NOW());

INSERT INTO `admin_role_permission` (`role_id`, `perm_id`, `created_at`)
SELECT 1001, `perm_id`, NOW() FROM `admin_permission`;

INSERT INTO `admin_menu` (`parent_id`, `title`, `icon`, `path`, `component`, `perm_id`, `sort`, `is_show`, `status`, `created_at`, `updated_at`)
VALUES
(0, '首页', 'home', '/dashboard', 'dashboard/index', 1, 1, 1, 1, NOW(), NOW()),
(0, '用户管理', 'user', '/user', 'user/index', 5, 2, 1, 1, NOW(), NOW()),
(0, '角色权限管理', 'key', '/rbac', 'rbac/index', 9, 3, 1, 1, NOW(), NOW()),
(0, '地区管理', 'environment', '/region', 'region/index', 13, 4, 1, 1, NOW(), NOW()),
(0, '门店管理', 'shop', '/store', 'store/index', 17, 5, 1, 1, NOW(), NOW()),
(0, '套餐管理', 'appstore', '/package', 'package/index', 21, 6, 0, 1, NOW(), NOW()),
(0, '医生管理', 'medicine-box', '/doctor', 'doctor/index', 25, 7, 0, 1, NOW(), NOW()),
(0, '患者管理', 'team', '/patient', 'patient/index', 29, 8, 0, 1, NOW(), NOW()),
(0, '订单管理', 'shopping-cart', '/order', 'order/index', 33, 9, 0, 1, NOW(), NOW()),
(0, '优惠券管理', 'gift', '/coupon', 'coupon/index', 37, 10, 0, 1, NOW(), NOW()),
(0, '内容管理', 'read', '/content', 'content/index', 41, 11, 0, 1, NOW(), NOW()),
(0, '统计报表', 'bar-chart', '/report', 'report/index', 45, 12, 0, 1, NOW(), NOW()),
(0, '操作记录', 'history', '/log', 'log/index', 49, 13, 0, 1, NOW(), NOW());

SET @dashboard_id = (SELECT menu_id FROM admin_menu WHERE title = '首页' AND parent_id = 0 LIMIT 1);
SET @user_id = (SELECT menu_id FROM admin_menu WHERE title = '用户管理' AND parent_id = 0 LIMIT 1);
SET @rbac_id = (SELECT menu_id FROM admin_menu WHERE title = '角色权限管理' AND parent_id = 0 LIMIT 1);
SET @region_id = (SELECT menu_id FROM admin_menu WHERE title = '地区管理' AND parent_id = 0 LIMIT 1);
SET @store_id = (SELECT menu_id FROM admin_menu WHERE title = '门店管理' AND parent_id = 0 LIMIT 1);
SET @package_id = (SELECT menu_id FROM admin_menu WHERE title = '套餐管理' AND parent_id = 0 LIMIT 1);
SET @doctor_id = (SELECT menu_id FROM admin_menu WHERE title = '医生管理' AND parent_id = 0 LIMIT 1);
SET @patient_id = (SELECT menu_id FROM admin_menu WHERE title = '患者管理' AND parent_id = 0 LIMIT 1);
SET @order_id = (SELECT menu_id FROM admin_menu WHERE title = '订单管理' AND parent_id = 0 LIMIT 1);
SET @coupon_id = (SELECT menu_id FROM admin_menu WHERE title = '优惠券管理' AND parent_id = 0 LIMIT 1);
SET @content_id = (SELECT menu_id FROM admin_menu WHERE title = '内容管理' AND parent_id = 0 LIMIT 1);
SET @report_id = (SELECT menu_id FROM admin_menu WHERE title = '统计报表' AND parent_id = 0 LIMIT 1);
SET @log_id = (SELECT menu_id FROM admin_menu WHERE title = '操作记录' AND parent_id = 0 LIMIT 1);

INSERT INTO `admin_menu` (`parent_id`, `title`, `icon`, `path`, `component`, `perm_id`, `sort`, `is_show`, `status`, `created_at`, `updated_at`)
VALUES
(@user_id, '用户创建', '', '/user/create', 'user/create', 6, 1, 1, 1, NOW(), NOW()),
(@user_id, '用户更新', '', '/user/update', 'user/update', 7, 2, 1, 1, NOW(), NOW()),
(@user_id, '用户删除', '', '/user/delete', 'user/delete', 8, 3, 1, 1, NOW(), NOW()),

(@rbac_id, '角色创建', '', '/rbac/create', 'rbac/create', 10, 1, 1, 1, NOW(), NOW()),
(@rbac_id, '角色更新', '', '/rbac/update', 'rbac/update', 11, 2, 1, 1, NOW(), NOW()),
(@rbac_id, '角色删除', '', '/rbac/delete', 'rbac/delete', 12, 3, 1, 1, NOW(), NOW()),

(@region_id, '地区创建', '', '/region/create', 'region/create', 14, 1, 1, 1, NOW(), NOW()),
(@region_id, '地区更新', '', '/region/update', 'region/update', 15, 2, 1, 1, NOW(), NOW()),
(@region_id, '地区删除', '', '/region/delete', 'region/delete', 16, 3, 1, 1, NOW(), NOW()),

(@store_id, '门店创建', '', '/store/create', 'store/create', 18, 1, 1, 1, NOW(), NOW()),
(@store_id, '门店更新', '', '/store/update', 'store/update', 19, 2, 1, 1, NOW(), NOW()),
(@store_id, '门店删除', '', '/store/delete', 'store/delete', 20, 3, 1, 1, NOW(), NOW()),

(@package_id, '套餐创建', '', '/package/create', 'package/create', 22, 1, 1, 1, NOW(), NOW()),
(@package_id, '套餐更新', '', '/package/update', 'package/update', 23, 2, 1, 1, NOW(), NOW()),
(@package_id, '套餐删除', '', '/package/delete', 'package/delete', 24, 3, 1, 1, NOW(), NOW()),

(@doctor_id, '医生创建', '', '/doctor/create', 'doctor/create', 26, 1, 1, 1, NOW(), NOW()),
(@doctor_id, '医生更新', '', '/doctor/update', 'doctor/update', 27, 2, 1, 1, NOW(), NOW()),
(@doctor_id, '医生删除', '', '/doctor/delete', 'doctor/delete', 28, 3, 1, 1, NOW(), NOW()),

(@patient_id, '患者创建', '', '/patient/create', 'patient/create', 30, 1, 1, 1, NOW(), NOW()),
(@patient_id, '患者更新', '', '/patient/update', 'patient/update', 31, 2, 1, 1, NOW(), NOW()),
(@patient_id, '患者删除', '', '/patient/delete', 'patient/delete', 32, 3, 1, 1, NOW(), NOW()),

(@order_id, '订单创建', '', '/order/create', 'order/create', 34, 1, 1, 1, NOW(), NOW()),
(@order_id, '订单更新', '', '/order/update', 'order/update', 35, 2, 1, 1, NOW(), NOW()),
(@order_id, '订单删除', '', '/order/delete', 'order/delete', 36, 3, 1, 1, NOW(), NOW()),

(@coupon_id, '优惠券创建', '', '/coupon/create', 'coupon/create', 38, 1, 1, 1, NOW(), NOW()),
(@coupon_id, '优惠券更新', '', '/coupon/update', 'coupon/update', 39, 2, 1, 1, NOW(), NOW()),
(@coupon_id, '优惠券删除', '', '/coupon/delete', 'coupon/delete', 40, 3, 1, 1, NOW(), NOW()),

(@content_id, '内容创建', '', '/content/create', 'content/create', 42, 1, 1, 1, NOW(), NOW()),
(@content_id, '内容更新', '', '/content/update', 'content/update', 43, 2, 1, 1, NOW(), NOW()),
(@content_id, '内容删除', '', '/content/delete', 'content/delete', 44, 3, 1, 1, NOW(), NOW()),

(@report_id, '报表创建', '', '/report/create', 'report/create', 46, 1, 1, 1, NOW(), NOW()),
(@report_id, '报表更新', '', '/report/update', 'report/update', 47, 2, 1, 1, NOW(), NOW()),
(@report_id, '报表删除', '', '/report/delete', 'report/delete', 48, 3, 1, 1, NOW(), NOW()),

(@log_id, '记录创建', '', '/log/create', 'log/create', 50, 1, 1, 1, NOW(), NOW()),
(@log_id, '记录更新', '', '/log/update', 'log/update', 51, 2, 1, 1, NOW(), NOW()),
(@log_id, '记录删除', '', '/log/delete', 'log/delete', 52, 3, 1, 1, NOW(), NOW());

CREATE OR REPLACE VIEW `v_permission_tree` AS
SELECT 
  m.menu_id AS id,
  m.title,
  m.icon,
  m.path,
  m.component,
  m.parent_id,
  p.module,
  p.action,
  p.description AS perm_desc,
  m.sort,
  m.is_show,
  m.status
FROM admin_menu m
JOIN admin_permission p ON m.perm_id = p.perm_id
WHERE m.status = 1
ORDER BY m.parent_id, m.sort;

ALTER TABLE `admin_menu` ADD INDEX `idx_perm_id` (`perm_id`);
ALTER TABLE `admin_role_permission` ADD INDEX `idx_role_perm` (`role_id`, `perm_id`);

INSERT INTO `admin_user` (`username`, `password`, `email`, `mobile`, `status`, `last_ip`,`last_login`, `created_at`, `updated_at`)
VALUES ('admin', '$10$dCFplVqp46JG.Y.SdOBYlehkyWWGpI0d3P4iT431K2Go95dryeWDq', '<EMAIL>', '13800000000', 1,'127.0.0.1', NOW(), NOW(), NOW());

INSERT INTO `admin_user_role` (`admin_id`, `role_id`, `created_at`)
SELECT a.admin_id, 1001, NOW()
FROM admin_user a
WHERE a.username = 'admin'
LIMIT 1;