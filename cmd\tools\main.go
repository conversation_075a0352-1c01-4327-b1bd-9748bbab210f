package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/cmd/cron/tasks"
	"yekaitai/internal/config"
	"yekaitai/internal/modules/coupon/service"
	"yekaitai/internal/modules/store/model"
	"yekaitai/internal/service/cron"
	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/adapters/wanliniu"
	"yekaitai/pkg/infra/mysql"
)

// Config 工具配置
type Config struct {
	Cron       config.CronConfig       `json:"cron"`
	TencentMap config.TencentMapConfig `json:"tencentMap"`
	Mysql      config.MysqlConfig      `json:"mysql"`
	Hangzhou   config.HangzhouConfig   `json:"HangzhouHIS"`
	AbcYun     config.AbcYunConfig     `json:"abcYun"`
	WanLiNiu   config.WanLiNiuConfig   `json:"wanliniu"`
}

// 工具命令行程序，用于执行各种同步和测试任务
//
// 使用示例：
// # 同步门店地理编码（每个门店调用腾讯地图API获取经纬度）
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task store_geo

// # 同步医生数据
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task doctor_sync

// # 同步患者数据
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task patient_sync

// # 同步门诊收费信息（从2024年10月6日开始到今天的所有数据）
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task charge_sync

// # 同步机构数据
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task org_sync

// # 同步科室数据
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task department_sync

// # ABC云同步任务
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task abcyun_store_sync
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task abcyun_department_sync
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task abcyun_doctor_sync
// # 同步ABC云患者数据（从2025年4月9日开始到今天的所有数据）
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task abcyun_patient_sync

// # 万里牛ERP同步任务
// # 手动同步万里牛商品数据（首次全量同步或增量同步）
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_goods_sync
// # 手动同步万里牛库存数据
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_inventory_sync
// # 查询ERP库存单笔（废弃）
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_inventory_query -item_id "商品编码" [-sku_id "规格编码"]
// # 查询万里牛库存V2（新接口）
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_inventory_query_v2 -spec_code "规格编码"
// # 查询万里牛库存V2（通过时间范围）
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_inventory_query_v2_time
// # 测试万里牛库存同步
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_inventory_sync_test
// # 测试万里牛商品分类API
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_categories_test
// # 手动同步万里牛商品分类
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_categories_sync
// # 初始化默认商品分类-使用这个初始化万里牛商品分类
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task init_default_category

// # 手动执行优惠券发放任务
// go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task coupon_issue_execute -task_id 任务ID

var (
	configFile = flag.String("f", "etc/config.yaml", "配置文件路径")
	task       = flag.String("task", "", "要执行的任务名称")
	itemID     = flag.String("item_id", "", "商品编码（用于库存查询）")
	specCode   = flag.String("spec_code", "", "规格编码（用于库存查询，可选）")
	taskID     = flag.Uint("task_id", 0, "任务ID（用于手动执行优惠券发放任务）")
)

func main() {
	flag.Parse()

	var c Config
	conf.MustLoad(*configFile, &c)

	// 初始化日志
	logx.MustSetup(logx.LogConf{
		ServiceName: "tools",
		Mode:        "console",
	})

	// 初始化数据库连接
	if err := mysql.Init(c.Mysql); err != nil {
		fmt.Printf("初始化数据库连接失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化杭州HIS客户端
	if err := hangzhou.InitDefaultClient(&c.Hangzhou); err != nil {
		fmt.Printf("初始化杭州HIS客户端失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化ABC云客户端
	var abcYunClient *abcyun.AbcYunClient
	if c.AbcYun.BaseURL != "" && c.AbcYun.AppID != "" {
		abcYunClient = abcyun.NewClient(&c.AbcYun)
		fmt.Println("已初始化ABC云客户端:", c.AbcYun.BaseURL)
	}

	// 初始化万里牛ERP客户端
	if c.WanLiNiu.BaseURL != "" && c.WanLiNiu.AppKey != "" {
		wanliniu.Init(wanliniu.Config{
			BaseURL:   c.WanLiNiu.BaseURL,
			AppKey:    c.WanLiNiu.AppKey,
			AppSecret: c.WanLiNiu.AppSecret,
			ShopNick:  c.WanLiNiu.ShopNick,
			ShopType:  c.WanLiNiu.ShopType,
		})
		fmt.Println("已初始化万里牛ERP客户端:", c.WanLiNiu.BaseURL)
		fmt.Printf("店铺配置: 昵称=%s, 类型=%d\n", c.WanLiNiu.ShopNick, c.WanLiNiu.ShopType)
	}

	// 根据任务名称执行相应的同步任务
	switch *task {
	case "store_geo":
		// 同步门店地理编码
		service := cron.NewStoreGeoSyncService(&c.Cron, c.TencentMap.Key)
		if err := service.SyncAll(); err != nil {
			fmt.Printf("同步门店地理编码失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("门店地理编码同步完成")
	case "doctor_sync":
		// 同步医生数据
		db := mysql.Master()
		doctorSyncService := tasks.NewDoctorSyncService(db)
		if err := doctorSyncService.SyncDoctors(); err != nil {
			fmt.Printf("同步医生数据失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("医生数据同步完成")
	case "patient_sync":
		// 同步患者数据
		db := mysql.Master()
		patientSyncService := tasks.NewPatientSyncService(db)
		if err := patientSyncService.SyncPatients(); err != nil {
			fmt.Printf("同步患者数据失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("患者数据同步完成")
	case "org_sync":
		// 同步机构数据
		db := mysql.Master()
		storeRepo := model.NewStoreRepository(db)
		orgSyncService := tasks.NewOrgSyncService(hangzhou.DefaultClient, storeRepo, true, 1)
		if err := orgSyncService.Start(); err != nil {
			fmt.Printf("同步机构数据失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("机构数据同步完成")
	case "department_sync":
		// 同步科室数据
		departmentSyncService := tasks.NewDepartmentSyncService(hangzhou.DefaultClient, true, 1)
		departmentSyncService.SyncDepartments()
		fmt.Println("科室数据同步完成")
	case "abcyun_store_sync":
		// ABC云门店同步
		if abcYunClient == nil {
			fmt.Printf("ABC云客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		storeSyncService := tasks.NewAbcYunStoreSyncService(abcYunClient)
		if err := storeSyncService.SyncStores(); err != nil {
			fmt.Printf("ABC云门店同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("ABC云门店同步完成")
	case "abcyun_department_sync":
		// ABC云科室同步
		if abcYunClient == nil {
			fmt.Printf("ABC云客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		deptSyncService := tasks.NewAbcYunDepartmentSyncService(abcYunClient)
		if err := deptSyncService.SyncDepartments(); err != nil {
			fmt.Printf("ABC云科室同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("ABC云科室同步完成")
	case "abcyun_doctor_sync":
		// ABC云医生同步
		if abcYunClient == nil {
			fmt.Printf("ABC云客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		doctorSyncService := tasks.NewAbcYunDoctorSyncService(abcYunClient)
		if err := doctorSyncService.SyncDoctors(); err != nil {
			fmt.Printf("ABC云医生同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("ABC云医生同步完成")
	case "abcyun_patient_sync":
		// ABC云患者同步
		if abcYunClient == nil {
			fmt.Printf("ABC云客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		patientSyncService := tasks.NewAbcYunPatientSyncSimpleService(abcYunClient)
		if err := patientSyncService.SyncPatientsManual(); err != nil {
			fmt.Printf("ABC云患者同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("ABC云患者同步完成")
	case "charge_sync":
		// 同步门诊收费信息
		db := mysql.Master()
		chargeSyncService := tasks.NewChargeSyncService(db)
		chargeSyncService.SyncChargesManual()
		fmt.Println("门诊收费信息同步完成")
	case "wanliniu_goods_sync":
		// 万里牛商品同步
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		goodsSyncService := tasks.NewWanLiNiuGoodsSyncService()
		if err := goodsSyncService.SyncGoodsManual(); err != nil {
			fmt.Printf("万里牛商品同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛商品同步完成")
	case "wanliniu_shipping_sync":
		// 万里牛发货状态同步
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}
		shippingSyncService := tasks.NewWanLiNiuShippingSyncService()
		if err := shippingSyncService.SyncShippingStatusManual(); err != nil {
			fmt.Printf("万里牛发货状态同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛发货状态同步完成")

	case "wanliniu_failed_push":
		// 万里牛失败订单推送（定时任务逻辑）
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行推送\n")
			os.Exit(1)
		}
		failedPushService := tasks.NewWanLiNiuFailedPushService()
		if err := failedPushService.PushFailedOrders(); err != nil {
			fmt.Printf("万里牛失败订单推送失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛失败订单推送完成")
	case "wanliniu_inventory_sync":
		// 手动同步万里牛库存
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}

		// 创建万里牛库存同步服务
		inventorySyncService := tasks.NewWanLiNiuInventorySyncService(&c.WanLiNiu)
		if inventorySyncService == nil {
			fmt.Printf("万里牛库存同步服务创建失败\n")
			os.Exit(1)
		}

		// 同步万里牛库存
		fmt.Println("开始同步万里牛库存...")
		if err := inventorySyncService.SyncInventoryManual(); err != nil {
			fmt.Printf("万里牛库存同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛库存同步完成")
	case "wanliniu_inventory_query":
		// 查询ERP库存单笔
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行查询\n")
			os.Exit(1)
		}

		if *itemID == "" {
			fmt.Printf("请提供商品编码参数: -item_id \"商品编码\"\n")
			fmt.Printf("用法示例: go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_inventory_query -item_id \"4b60170\" -sku_id \"001\"\n")
			os.Exit(1)
		}

		// 获取万里牛服务实例
		wanLiNiuService := wanliniu.GetService()
		if wanLiNiuService == nil {
			fmt.Printf("万里牛ERP服务未初始化，无法执行查询\n")
			os.Exit(1)
		}

		// 查询单笔库存
		fmt.Printf("正在查询商品库存...\n")
		fmt.Printf("商品编码: %s\n", *itemID)
		if *specCode != "" {
			fmt.Printf("规格编码: %s\n", *specCode)
		}
		fmt.Printf("店铺昵称: %s\n", c.WanLiNiu.ShopNick)
		fmt.Printf("店铺类型: %d (B2C平台)\n\n", c.WanLiNiu.ShopType)

		response, err := wanLiNiuService.QueryInventorySingle(context.Background(), *itemID, *specCode, "")
		if err != nil {
			fmt.Printf("查询ERP库存失败: %v\n", err)
			os.Exit(1)
		}

		// 输出查询结果
		fmt.Printf("=== 查询结果 ===\n")
		fmt.Printf("接口调用成功: %t\n", response.Data.Success)
		if !response.Data.Success {
			fmt.Printf("业务错误信息: %s\n", response.Data.ErrorMsg)
		} else {
			fmt.Printf("库存数据:\n%s\n", response.Data.Response)
		}
		fmt.Println("ERP库存查询完成")
	case "wanliniu_inventory_query_v2":
		// 查询ERP库存V2（新接口）并同步更新本地库存
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行查询\n")
			os.Exit(1)
		}

		if *specCode == "" {
			fmt.Printf("请提供规格编码参数: -spec_code \"规格编码\"\n")
			fmt.Printf("用法示例: go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task wanliniu_inventory_query_v2 -spec_code \"SPEC_456\"\n")
			fmt.Printf("注意：新接口主要通过规格编码查询，也可以通过修改时间范围查询\n")
			os.Exit(1)
		}

		// 获取万里牛服务实例
		wanLiNiuService := wanliniu.GetService()
		if wanLiNiuService == nil {
			fmt.Printf("万里牛ERP服务未初始化，无法执行查询\n")
			os.Exit(1)
		}

		// 创建库存同步服务
		inventorySyncService := tasks.NewWanLiNiuInventorySyncService(&c.WanLiNiu)
		if inventorySyncService == nil {
			fmt.Printf("万里牛库存同步服务创建失败\n")
			os.Exit(1)
		}

		// 查询库存V2
		fmt.Printf("正在查询商品库存V2并同步本地库存...\n")
		fmt.Printf("规格编码: %s\n", *specCode)
		fmt.Printf("店铺昵称: %s\n", c.WanLiNiu.ShopNick)
		fmt.Printf("店铺类型: %d (B2C平台)\n\n", c.WanLiNiu.ShopType)

		var response *wanliniu.InventoryQueryV2Response
		var err error

		ctx := context.Background()
		// 使用规格编码查询
		response, err = wanLiNiuService.QueryInventoryV2BySkuCode(ctx, *specCode, "")

		if err != nil {
			fmt.Printf("查询ERP库存V2失败: %v\n", err)
			os.Exit(1)
		}

		// 输出查询结果并同步库存
		fmt.Printf("=== 查询结果V2 ===\n")
		fmt.Printf("返回记录数: %d\n", len(response.Data))
		updatedRecords := 0

		for i, item := range response.Data {
			fmt.Printf("\n--- 库存记录 %d ---\n", i+1)
			fmt.Printf("货号: %s\n", item.ArticleNumber)
			fmt.Printf("条码: %s\n", item.BarCode)
			fmt.Printf("规格编码: %s\n", item.SkuCode)
			fmt.Printf("规格名称: %s\n", item.SpecName)
			fmt.Printf("商品编码: %s\n", item.GoodsCode)
			fmt.Printf("仓库编码: %s\n", item.StorageCode)
			fmt.Printf("实际库存: %.2f\n", item.Quantity)
			fmt.Printf("锁定库存: %.2f\n", item.LockSize)
			fmt.Printf("可用库存: %.2f\n", item.Quantity-item.LockSize)
			fmt.Printf("在途库存: %.2f\n", item.Underway)
			fmt.Printf("次品数量: %.2f\n", item.DefectNum)
			fmt.Printf("成本价: %.2f\n", item.Cost)
			fmt.Printf("最后采购价: %.2f\n", item.LastStock)

			// 同步更新本地库存
			if item.SkuCode != "" {
				stock := int(item.Quantity - item.LockSize) // 可用库存 = 实际库存 - 锁定库存
				originalStock := stock
				if stock < 0 {
					fmt.Printf("   ⚠ 检测到负数库存: 实际库存=%.2f, 锁定库存=%.2f, 计算结果=%d, 自动调整为0\n",
						item.Quantity, item.LockSize, originalStock)
					stock = 0
				}

				if err := inventorySyncService.UpdateLocalInventoryBySkuCode(item.SkuCode, stock); err != nil {
					fmt.Printf("   ✗ 更新本地库存失败: %v\n", err)
				} else {
					fmt.Printf("   ✓ 已更新本地库存: %d\n", stock)
					updatedRecords++
				}
			} else {
				fmt.Printf("   - 规格编码为空，跳过库存更新\n")
			}

			if len(item.Batchs) > 0 {
				fmt.Printf("批次信息:\n")
				for j, batch := range item.Batchs {
					var produceDate, expiredDate string
					if batch.ProduceDate > 0 {
						produceDate = time.Unix(batch.ProduceDate/1000, 0).Format("2006-01-02 15:04:05")
					} else {
						produceDate = "未设置"
					}
					if batch.ExpiredDate > 0 {
						expiredDate = time.Unix(batch.ExpiredDate/1000, 0).Format("2006-01-02 15:04:05")
					} else {
						expiredDate = "未设置"
					}
					fmt.Printf("  批次%d: 批次号=%s, 数量=%.2f, 锁定=%.2f, 生产日期=%s, 过期日期=%s\n",
						j+1, batch.BatchNo, batch.Num, batch.LockSize, produceDate, expiredDate)
				}
			}
		}

		fmt.Printf("\n=== 同步完成 ===\n")
		fmt.Printf("总计查询 %d 条记录，成功更新 %d 条本地库存\n", len(response.Data), updatedRecords)
	case "wanliniu_inventory_query_v2_time":
		// 查询ERP库存V2（通过时间范围）并同步更新本地库存
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行查询\n")
			os.Exit(1)
		}

		// 获取万里牛服务实例
		wanLiNiuService := wanliniu.GetService()
		if wanLiNiuService == nil {
			fmt.Printf("万里牛ERP服务未初始化，无法执行查询\n")
			os.Exit(1)
		}

		// 创建库存同步服务
		inventorySyncService := tasks.NewWanLiNiuInventorySyncService(&c.WanLiNiu)
		if inventorySyncService == nil {
			fmt.Printf("万里牛库存同步服务创建失败\n")
			os.Exit(1)
		}

		// 自动设置起始时间为当前时间往前推3天
		now := time.Now()
		startTimeObj := now.AddDate(0, 0, -3) // 往前推3天
		startTime := startTimeObj.Format("2006-01-02 15:04:05")

		// 结束时间为当前时间
		endTime := now.Format("2006-01-02 15:04:05")

		fmt.Printf("正在查询商品库存V2（时间范围）并同步本地库存...\n")
		fmt.Printf("起始时间: %s\n", startTime)
		fmt.Printf("结束时间: %s\n", endTime)
		fmt.Printf("店铺昵称: %s\n", c.WanLiNiu.ShopNick)
		fmt.Printf("店铺类型: %d (B2C平台)\n\n", c.WanLiNiu.ShopType)

		ctx := context.Background()

		// 分页查询所有数据并同步库存
		page := 1
		pageSize := 200 // 使用最大页面大小
		totalRecords := 0
		updatedRecords := 0

		for {
			fmt.Printf("正在查询第 %d 页...\n", page)
			response, err := wanLiNiuService.QueryInventoryV2ByModifyTime(ctx, page, pageSize, startTime, endTime, "")

			if err != nil {
				fmt.Printf("查询ERP库存V2失败: %v\n", err)
				os.Exit(1)
			}

			if len(response.Data) == 0 {
				fmt.Printf("第 %d 页没有数据，查询结束\n", page)
				break
			}

			fmt.Printf("第 %d 页查询到 %d 条库存记录\n", page, len(response.Data))
			totalRecords += len(response.Data)

			// 输出查询结果并同步库存
			for i, item := range response.Data {
				fmt.Printf("\n--- 库存记录 %d (页%d) ---\n", i+1, page)
				fmt.Printf("货号: %s\n", item.ArticleNumber)
				fmt.Printf("条码: %s\n", item.BarCode)
				fmt.Printf("规格编码: %s\n", item.SkuCode)
				fmt.Printf("规格名称: %s\n", item.SpecName)
				fmt.Printf("商品编码: %s\n", item.GoodsCode)
				fmt.Printf("仓库编码: %s\n", item.StorageCode)
				fmt.Printf("实际库存: %.2f\n", item.Quantity)
				fmt.Printf("锁定库存: %.2f\n", item.LockSize)
				fmt.Printf("可用库存: %.2f\n", item.Quantity-item.LockSize)
				fmt.Printf("在途库存: %.2f\n", item.Underway)
				fmt.Printf("次品数量: %.2f\n", item.DefectNum)
				fmt.Printf("成本价: %.2f\n", item.Cost)
				fmt.Printf("最后采购价: %.2f\n", item.LastStock)

				// 同步更新本地库存
				if item.SkuCode != "" {
					stock := int(item.Quantity - item.LockSize) // 可用库存 = 实际库存 - 锁定库存
					originalStock := stock
					if stock < 0 {
						fmt.Printf("   ⚠ 检测到负数库存: 实际库存=%.2f, 锁定库存=%.2f, 计算结果=%d, 自动调整为0\n",
							item.Quantity, item.LockSize, originalStock)
						stock = 0
					}

					if err := inventorySyncService.UpdateLocalInventoryBySkuCode(item.SkuCode, stock); err != nil {
						fmt.Printf("   ✗ 更新本地库存失败: %v\n", err)
					} else {
						fmt.Printf("   ✓ 已更新本地库存: %d\n", stock)
						updatedRecords++
					}
				} else {
					fmt.Printf("   - 规格编码为空，跳过库存更新\n")
				}

				if len(item.Batchs) > 0 {
					fmt.Printf("批次信息:\n")
					for j, batch := range item.Batchs {
						var produceDate, expiredDate string
						if batch.ProduceDate > 0 {
							produceDate = time.Unix(batch.ProduceDate/1000, 0).Format("2006-01-02 15:04:05")
						} else {
							produceDate = "未设置"
						}
						if batch.ExpiredDate > 0 {
							expiredDate = time.Unix(batch.ExpiredDate/1000, 0).Format("2006-01-02 15:04:05")
						} else {
							expiredDate = "未设置"
						}
						fmt.Printf("  批次%d: 批次号=%s, 数量=%.2f, 锁定=%.2f, 生产日期=%s, 过期日期=%s\n",
							j+1, batch.BatchNo, batch.Num, batch.LockSize, produceDate, expiredDate)
					}
				}
			}

			// 如果返回的记录数少于页面大小，说明是最后一页
			if len(response.Data) < pageSize {
				fmt.Printf("已查询完所有数据\n")
				break
			}

			page++
		}

		fmt.Printf("\n=== 同步完成 ===\n")
		fmt.Printf("总计查询 %d 条记录，成功更新 %d 条本地库存\n", totalRecords, updatedRecords)
	case "wanliniu_inventory_sync_test":
		// 测试万里牛库存同步
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步测试\n")
			os.Exit(1)
		}

		// 创建万里牛库存同步服务
		inventorySyncService := tasks.NewWanLiNiuInventorySyncService(&c.WanLiNiu)
		if inventorySyncService == nil {
			fmt.Printf("万里牛库存同步服务创建失败\n")
			os.Exit(1)
		}

		fmt.Println("开始测试万里牛库存同步...")
		if err := inventorySyncService.SyncInventoryManual(); err != nil {
			fmt.Printf("万里牛库存同步测试失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛库存同步测试完成")
	case "wanliniu_categories_test":
		// 万里牛商品分类API测试
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行测试\n")
			os.Exit(1)
		}
		// 获取万里牛服务实例
		wanLiNiuService := wanliniu.GetService()
		if wanLiNiuService == nil {
			fmt.Printf("万里牛ERP服务未初始化，无法执行测试\n")
			os.Exit(1)
		}

		// 测试获取商品分类
		fmt.Println("开始测试万里牛商品分类API...")
		response, err := wanLiNiuService.GetCategories(context.Background(), 1, 10)
		if err != nil {
			fmt.Printf("万里牛商品分类API测试失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Printf("商品分类API测试成功！返回 %d 条分类数据:\n", response.Total)
		for i, category := range response.Data {
			hasChildStr := "否"
			if category.HasChild {
				hasChildStr = "是"
			}
			fmt.Printf("  %d. ID: %s, 名称: %s, 父节点: %s, 有子节点: %s\n",
				i+1, category.CategoryID, category.CategoryName, category.ParentID, hasChildStr)
		}

		// 测试获取所有分类
		fmt.Println("\n开始测试获取所有商品分类...")
		allResponse, err := wanLiNiuService.GetAllCategories(context.Background())
		if err != nil {
			fmt.Printf("万里牛获取所有商品分类失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("获取所有分类成功！总共 %d 条分类数据\n", allResponse.Total)
		fmt.Println("万里牛商品分类API测试完成")
	case "wanliniu_categories_sync":
		// 手动同步万里牛商品分类
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行同步\n")
			os.Exit(1)
		}

		// 创建万里牛分类同步服务
		categorySyncService := tasks.NewWanLiNiuCategorySyncService()
		if categorySyncService == nil {
			fmt.Printf("万里牛分类同步服务创建失败\n")
			os.Exit(1)
		}

		// 同步万里牛商品分类
		fmt.Println("开始同步万里牛商品分类...")
		if err := categorySyncService.SyncCategoriesManual(); err != nil {
			fmt.Printf("万里牛商品分类同步失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("万里牛商品分类同步完成")
	case "init_default_category":
		// 初始化默认商品分类
		if c.WanLiNiu.BaseURL == "" || c.WanLiNiu.AppKey == "" {
			fmt.Printf("万里牛ERP客户端未初始化，无法执行初始化\n")
			os.Exit(1)
		}

		// 创建万里牛分类同步服务
		categorySyncService := tasks.NewWanLiNiuCategorySyncService()
		if categorySyncService == nil {
			fmt.Printf("万里牛分类同步服务创建失败\n")
			os.Exit(1)
		}

		// 初始化默认商品分类
		fmt.Println("开始初始化默认商品分类...")
		if err := categorySyncService.SyncCategoriesManual(); err != nil {
			fmt.Printf("初始化默认商品分类失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("默认商品分类初始化完成")
	case "coupon_issue_execute":
		// 手动执行优惠券发放任务
		if *taskID == 0 {
			fmt.Printf("请提供任务ID参数: -task_id 任务ID\n")
			fmt.Printf("用法示例: go run cmd/tools/main.go -f etc/yekaitai-dev.yaml -task coupon_issue_execute -task_id 123\n")
			os.Exit(1)
		}

		// 创建优惠券服务
		couponService := service.NewCouponService()
		if couponService == nil {
			fmt.Printf("优惠券服务创建失败\n")
			os.Exit(1)
		}

		fmt.Printf("开始执行优惠券发放任务...\n")
		fmt.Printf("任务ID: %d\n", *taskID)

		// 手动执行任务，执行者ID设为0（系统执行）
		ctx := context.Background()
		if err := couponService.ExecuteIssueTask(ctx, uint(*taskID), 0); err != nil {
			fmt.Printf("执行优惠券发放任务失败: %v\n", err)
			os.Exit(1)
		}

		fmt.Printf("优惠券发放任务执行成功\n")
	default:
		fmt.Println("可用的任务列表：")
		fmt.Println("  store_geo                - 同步门店地理编码")
		fmt.Println("  doctor_sync              - 同步医生数据")
		fmt.Println("  patient_sync             - 同步患者数据")
		fmt.Println("  org_sync                 - 同步机构数据")
		fmt.Println("  department_sync          - 同步科室数据")
		fmt.Println("  abcyun_store_sync        - ABC云门店同步")
		fmt.Println("  abcyun_department_sync   - ABC云科室同步")
		fmt.Println("  abcyun_doctor_sync       - ABC云医生同步")
		fmt.Println("  abcyun_patient_sync      - ABC云患者同步")
		fmt.Println("  charge_sync              - 同步门诊收费信息")
		fmt.Println("  wanliniu_goods_sync       - 万里牛商品同步")
		fmt.Println("  wanliniu_shipping_sync    - 万里牛发货状态同步")
		fmt.Println("  wanliniu_failed_push      - 万里牛失败订单推送（定时任务逻辑）")
		fmt.Println("  wanliniu_inventory_sync   - 万里牛库存同步")
		fmt.Println("  wanliniu_inventory_query  - 查询ERP库存单笔")
		fmt.Println("  wanliniu_inventory_query_v2 - 查询ERP库存V2（新接口）")
		fmt.Println("  wanliniu_inventory_query_v2_time - 查询ERP库存V2（时间范围）")
		fmt.Println("  wanliniu_inventory_sync_test - 测试万里牛库存同步")
		fmt.Println("  wanliniu_categories_test  - 万里牛商品分类API测试")
		fmt.Println("  wanliniu_categories_sync  - 万里牛商品分类同步")
		fmt.Println("  init_default_category    - 初始化默认商品分类")
		fmt.Println("  coupon_issue_execute     - 手动执行优惠券发放任务")
		os.Exit(1)
	}
}
