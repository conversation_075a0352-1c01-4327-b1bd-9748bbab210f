package bootstrap

import (
	"fmt"

	"yekaitai/internal/modules/content/model"
	"yekaitai/pkg/infra/mysql"
)

// AddSignUpAmountField 为 t_contents 表添加报名费用字段
func AddSignUpAmountField() error {
	fmt.Println("开始为 t_contents 表添加报名费用字段...")

	db := mysql.Master()

	// 检查是否存在 sign_up_amount 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.Content{}, "sign_up_amount") {
		fmt.Println("添加报名费用字段...")
		if err := db.Migrator().AddColumn(&model.Content{}, "sign_up_amount"); err != nil {
			return fmt.Errorf("添加报名费用字段失败: %v", err)
		}
		
		// 为现有活动数据设置默认值为 0（免费）
		if err := db.Model(&model.Content{}).Where("type = ? AND sign_up_amount IS NULL", "activity").Update("sign_up_amount", 0).Error; err != nil {
			return fmt.Errorf("更新现有活动报名费用默认值失败: %v", err)
		}
		
		fmt.Println("报名费用字段添加完成")
	} else {
		fmt.Println("报名费用字段已存在，跳过添加")
	}

	fmt.Println("t_contents 表报名费用字段迁移完成")
	return nil
}
