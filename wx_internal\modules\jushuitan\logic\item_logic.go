package logic

import (
	"context"
	jst "yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

// ItemLogic 商品逻辑处理
type ItemLogic struct {
	ctx    context.Context
	svcCtx *svc.WxServiceContext
	client *jst.Client
	logger logx.Logger
}

// NewItemLogic 创建商品逻辑处理实例
func NewItemLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *ItemLogic {
	return &ItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		client: jst.DefaultClient,
		logger: logx.WithContext(ctx),
	}
}

// 上传普通商品
func (l *ItemLogic) UploadItemSku(req *jst.ItemSkuUploadRequest) (*jst.BaseResp, error) {
	logx.Infof("上传普通商品: %+v", req)
	resp, err := l.client.UploadItemSku(l.ctx, req)
	if err != nil {
		logx.Errorf("上传普通商品失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询普通商品
func (l *ItemLogic) QueryItemSku(req *jst.ItemSkuQueryRequest) (*jst.BaseResp, error) {
	logx.Infof("查询普通商品: %+v", req)
	resp, err := l.client.QueryItemSku(l.ctx, req)
	if err != nil {
		logx.Errorf("查询普通商品失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询普通商品(按款)
func (l *ItemLogic) QueryMallItem(req *jst.MallItemQueryRequest) (*jst.BaseResp, error) {
	logx.Infof("查询普通商品(按款): %+v", req)
	resp, err := l.client.QueryMallItem(l.ctx, req)
	if err != nil {
		logx.Errorf("查询普通商品(按款)失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 上传店铺商品
func (l *ItemLogic) UploadSkuMap(req *jst.SkuMapUploadRequest) (*jst.BaseResp, error) {
	logx.Infof("上传店铺商品: %+v", req)
	resp, err := l.client.UploadSkuMap(l.ctx, req)
	if err != nil {
		logx.Errorf("上传店铺商品失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询店铺商品
func (l *ItemLogic) QuerySkuMap(req *jst.SkuMapQueryRequest) (*jst.BaseResp, error) {
	logx.Infof("查询店铺商品: %+v", req)
	resp, err := l.client.QuerySkuMap(l.ctx, req)
	if err != nil {
		logx.Errorf("查询店铺商品失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 上传商品分类
func (l *ItemLogic) AddOrUpdateCategory(req *jst.CategoryAddOrUpdateRequest) (*jst.BaseResp, error) {
	logx.Infof("上传商品分类: %+v", req)
	resp, err := l.client.AddOrUpdateCategory(l.ctx, req)
	if err != nil {
		logx.Errorf("上传商品分类失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询商品分类
func (l *ItemLogic) QueryCategory(req *jst.CategoryQueryRequest) (*jst.BaseResp, error) {
	logx.Infof("查询商品分类: %+v", req)
	resp, err := l.client.QueryCategory(l.ctx, req)
	if err != nil {
		logx.Errorf("查询商品分类失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 上传BOM信息
func (l *ItemLogic) SaveBom(req *jst.BomSaveRequest) (*jst.BaseResp, error) {
	logx.Infof("上传BOM信息: %+v", req)
	resp, err := l.client.SaveBom(l.ctx, req)
	if err != nil {
		logx.Errorf("上传BOM信息失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询BOM信息
func (l *ItemLogic) GetSkuBomPageList(req *jst.BomQueryRequest) (*jst.BaseResp, error) {
	logx.Infof("查询BOM信息: %+v", req)
	resp, err := l.client.GetSkuBomPageList(l.ctx, req)
	if err != nil {
		logx.Errorf("查询BOM信息失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 上传商品历史成本价
func (l *ItemLogic) UploadItemSkuCostPrice(req *jst.ItemSkuCostPriceUploadRequest) (*jst.BaseResp, error) {
	logx.Infof("上传商品历史成本价: %+v", req)
	resp, err := l.client.UploadItemSkuCostPrice(l.ctx, req)
	if err != nil {
		logx.Errorf("上传商品历史成本价失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询商品历史成本价
func (l *ItemLogic) GetHistoryCostPriceV2(req *jst.HistoryCostPriceQueryRequest) (*jst.BaseResp, error) {
	logx.Infof("查询商品历史成本价: %+v", req)
	resp, err := l.client.GetHistoryCostPriceV2(l.ctx, req)
	if err != nil {
		logx.Errorf("查询商品历史成本价失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 上传商品多供应商
func (l *ItemLogic) SaveSupplierSku(req *jst.SupplierSkuSaveRequest) (*jst.SupplierSkuSaveResponse, error) {
	logx.Infof("上传商品多供应商: %+v", req)
	resp, err := l.client.SaveSupplierSku(l.ctx, req)
	if err != nil {
		logx.Errorf("上传商品多供应商失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询商品多供应商
func (l *ItemLogic) GetSupplierSkuList(req *jst.SupplierSkuQueryRequest) (*jst.SupplierSkuQueryResponse, error) {
	logx.Infof("查询商品多供应商: %+v", req)
	resp, err := l.client.GetSupplierSkuList(l.ctx, req)
	if err != nil {
		logx.Errorf("查询商品多供应商失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询商品多供应商(完整参数)
func (l *ItemLogic) GetSupplierSkuQueryList(req *jst.SupplierSkuQueryListRequest) (*jst.SupplierSkuQueryListResponse, error) {
	logx.Infof("查询商品多供应商(完整参数): %+v", req)
	resp, err := l.client.GetSupplierSkuQueryList(l.ctx, req)
	if err != nil {
		logx.Errorf("查询商品多供应商(完整参数)失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 上传组合装商品
func (l *ItemLogic) UploadCombineSku(req *jst.CombineSkuUploadRequest) (*jst.BaseResp, error) {
	logx.Infof("上传组合装商品: %+v", req)
	resp, err := l.client.UploadCombineSku(l.ctx, req)
	if err != nil {
		logx.Errorf("上传组合装商品失败: %v", err)
		return nil, err
	}
	return resp, nil
}

// 查询组合装商品
func (l *ItemLogic) QueryCombineSku(req *jst.CombineSkuQueryRequest) (*jst.BaseResp, error) {
	logx.Infof("查询组合装商品: %+v", req)
	resp, err := l.client.QueryCombineSku(l.ctx, req)
	if err != nil {
		logx.Errorf("查询组合装商品失败: %v", err)
		return nil, err
	}
	return resp, nil
}
