package service

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// ActivityCronService 活动定时任务服务
type ActivityCronService struct {
	refundService *ActivityRefundService
	activityService *ActivityService
	stopChan      chan struct{}
}

// NewActivityCronService 创建活动定时任务服务
func NewActivityCronService() *ActivityCronService {
	return &ActivityCronService{
		refundService:   NewActivityRefundService(),
		activityService: NewActivityService(),
		stopChan:        make(chan struct{}),
	}
}

// StartCronJobs 启动定时任务
func (s *ActivityCronService) StartCronJobs() {
	logx.Info("启动活动定时任务服务")

	// 启动过期活动检查任务（每小时执行一次）
	go s.startExpiredActivityChecker()

	// 启动活动报名人数更新任务（每10分钟执行一次）
	go s.startSignUpCountUpdater()

	logx.Info("活动定时任务服务启动完成")
}

// StopCronJobs 停止定时任务
func (s *ActivityCronService) StopCronJobs() {
	logx.Info("停止活动定时任务服务")
	close(s.stopChan)
}

// startExpiredActivityChecker 启动过期活动检查器
func (s *ActivityCronService) startExpiredActivityChecker() {
	ticker := time.NewTicker(time.Hour) // 每小时检查一次
	defer ticker.Stop()

	logx.Info("过期活动检查器已启动，每小时执行一次")

	for {
		select {
		case <-ticker.C:
			s.checkExpiredActivities()
		case <-s.stopChan:
			logx.Info("过期活动检查器已停止")
			return
		}
	}
}

// startSignUpCountUpdater 启动报名人数更新器
func (s *ActivityCronService) startSignUpCountUpdater() {
	ticker := time.NewTicker(10 * time.Minute) // 每10分钟更新一次
	defer ticker.Stop()

	logx.Info("报名人数更新器已启动，每10分钟执行一次")

	for {
		select {
		case <-ticker.C:
			s.updateSignUpCounts()
		case <-s.stopChan:
			logx.Info("报名人数更新器已停止")
			return
		}
	}
}

// checkExpiredActivities 检查过期活动
func (s *ActivityCronService) checkExpiredActivities() {
	ctx := context.Background()
	logx.Info("开始检查过期活动")

	startTime := time.Now()

	// 批量处理过期订单退款
	err := s.refundService.BatchRefundExpiredOrders(ctx, 100) // 每批处理100个订单
	if err != nil {
		logx.Errorf("批量处理过期活动订单失败: %v", err)
		return
	}

	duration := time.Since(startTime)
	logx.Infof("过期活动检查完成，耗时: %v", duration)
}

// updateSignUpCounts 更新报名人数
func (s *ActivityCronService) updateSignUpCounts() {
	ctx := context.Background()
	logx.Info("开始更新活动报名人数")

	startTime := time.Now()

	// 获取所有活跃的活动ID
	activeActivityIDs, err := s.getActiveActivityIDs(ctx)
	if err != nil {
		logx.Errorf("获取活跃活动ID失败: %v", err)
		return
	}

	successCount := 0
	for _, activityID := range activeActivityIDs {
		if err := s.activityService.UpdateActivitySignUpCount(activityID); err != nil {
			logx.Errorf("更新活动报名人数失败: activityID=%d, error=%v", activityID, err)
			continue
		}
		successCount++
	}

	duration := time.Since(startTime)
	logx.Infof("活动报名人数更新完成，成功: %d/%d，耗时: %v", successCount, len(activeActivityIDs), duration)
}

// getActiveActivityIDs 获取活跃的活动ID列表
func (s *ActivityCronService) getActiveActivityIDs(ctx context.Context) ([]uint, error) {
	// 这里应该查询数据库获取活跃的活动ID
	// 暂时返回空列表，实际实现时需要查询数据库
	return []uint{}, nil
}

// RunOnceExpiredCheck 手动执行一次过期检查（用于测试或手动触发）
func (s *ActivityCronService) RunOnceExpiredCheck() error {
	ctx := context.Background()
	logx.Info("手动执行过期活动检查")

	return s.refundService.RefundExpiredOrders(ctx)
}

// RunOnceSignUpCountUpdate 手动执行一次报名人数更新（用于测试或手动触发）
func (s *ActivityCronService) RunOnceSignUpCountUpdate() error {
	logx.Info("手动执行报名人数更新")

	activeActivityIDs, err := s.getActiveActivityIDs(context.Background())
	if err != nil {
		return err
	}

	for _, activityID := range activeActivityIDs {
		if err := s.activityService.UpdateActivitySignUpCount(activityID); err != nil {
			logx.Errorf("更新活动报名人数失败: activityID=%d, error=%v", activityID, err)
		}
	}

	return nil
}

// GetCronStatus 获取定时任务状态
func (s *ActivityCronService) GetCronStatus() map[string]interface{} {
	return map[string]interface{}{
		"expired_checker_interval": "1 hour",
		"signup_updater_interval":  "10 minutes",
		"status":                   "running",
		"last_check_time":          time.Now().Format("2006-01-02 15:04:05"),
	}
}

// ScheduleCustomTask 调度自定义任务
func (s *ActivityCronService) ScheduleCustomTask(taskName string, interval time.Duration, taskFunc func() error) {
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		logx.Infof("自定义任务 %s 已启动，间隔: %v", taskName, interval)

		for {
			select {
			case <-ticker.C:
				if err := taskFunc(); err != nil {
					logx.Errorf("自定义任务 %s 执行失败: %v", taskName, err)
				} else {
					logx.Infof("自定义任务 %s 执行成功", taskName)
				}
			case <-s.stopChan:
				logx.Infof("自定义任务 %s 已停止", taskName)
				return
			}
		}
	}()
}

// SetRefundBatchSize 设置退款批次大小
func (s *ActivityCronService) SetRefundBatchSize(batchSize int) {
	// 这里可以设置退款批次大小的配置
	logx.Infof("设置退款批次大小: %d", batchSize)
}

// GetTaskStatistics 获取任务统计信息
func (s *ActivityCronService) GetTaskStatistics() map[string]interface{} {
	return map[string]interface{}{
		"total_tasks":           2,
		"running_tasks":         2,
		"last_expired_check":    time.Now().Add(-30 * time.Minute).Format("2006-01-02 15:04:05"),
		"last_signup_update":    time.Now().Add(-5 * time.Minute).Format("2006-01-02 15:04:05"),
		"next_expired_check":    time.Now().Add(30 * time.Minute).Format("2006-01-02 15:04:05"),
		"next_signup_update":    time.Now().Add(5 * time.Minute).Format("2006-01-02 15:04:05"),
	}
}
