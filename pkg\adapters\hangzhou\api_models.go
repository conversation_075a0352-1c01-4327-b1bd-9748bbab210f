package hangzhou

// 登录认证响应
type AuthResponse struct {
	AccessToken string `json:"access_token"`
}

// 卫生机构信息
type HealthOrganization struct {
	WsjgID int    `json:"wsjgid"` // 卫生机构id
	WsjgMC string `json:"wsjgmc"` // 卫生机构名称
	PYM    string `json:"pym"`    // 拼音码
	DhHm   string `json:"dhhm"`   // 电话号码
	JgDz   string `json:"jgdz"`   // 机构地址
}

// 科室信息
type Department struct {
	JgksID int    `json:"jgksid"` // 机构科室id
	JgksMC string `json:"jgksmc"` // 机构科室名称
	PYM    string `json:"pym"`    // 拼音码
	ZfBz   string `json:"zfbz"`   // 作废标志
	FwFw   string `json:"fwfw"`   // 服务范围
	FwFwMC string `json:"fwfwmc"` // 服务范围名称
	GlLb   string `json:"gllb"`   // 管理类别
	GlLbMC string `json:"gllbmc"` // 管理类别名称
	KsFLDM string `json:"ksfldm"` // 科室分类代码
	KsFLMC string `json:"ksflmc"` // 科室分类名称
	KsLx   string `json:"kslx"`   // 科室类型
	PbKsBz string `json:"pbksbz"` // 排班科室标志
}

// 用户信息
type User struct {
	YhID int    `json:"yhid"` // 用户id
	YhMC string `json:"yhmc"` // 用户名称
	PYM  string `json:"pym"`  // 拼音码
	SjHm string `json:"sjhm"` // 电话号码
}

// 用户角色信息
type UserRole struct {
	YhjsID int    `json:"yhjsid"` // 用户角色id
	JsBm   string `json:"jsbm"`   // 角色名称
}

// 用户病区信息
type UserWard struct {
	YhbqID int    `json:"yhbqid"` // 用户病区id
	BqID   int    `json:"bqid"`   // 病区id
	BqMC   string `json:"bqmc"`   // 病区名称
}

// 用户科室信息
type UserDepartment struct {
	YhksID   int    `json:"yhksid"`   // 用户科室id
	JgksID   int    `json:"jgksid"`   // 机构科室id
	JgksMC   string `json:"jgksmc"`   // 机构科室名称
	WsrylbDM string `json:"wsrylbdm"` // 卫生人员类别代码
	WsrylbMC string `json:"wsrylbmc"` // 卫生人员类别名称
}

// HealthPerson 卫生人员信息
type HealthPerson struct {
	WsryID     int         `json:"wsryid"`     // 卫生人员id
	YhID       int         `json:"yhid"`       // 用户id
	YhMC       string      `json:"yhmc"`       // 医生名称
	YhZh       string      `json:"yhzh"`       // 用户账号
	PYM        string      `json:"pym"`        // 拼音码
	XbDM       string      `json:"xbdm"`       // 性别代码
	XbMC       string      `json:"xbmc"`       // 性别名称
	YsLb       string      `json:"yslb"`       // 医师类别
	Csrq       string      `json:"csrq"`       // 出生日期
	ZyjszwMC   interface{} `json:"zyjszwmc"`   // 医生职称
	ZyjszwlbMC string      `json:"zyjszwlbmc"` // 医生等级
	Grjj       string      `json:"grjj"`       // 医生简介
	Zytc       string      `json:"zytc"`       // 擅长领域
	Zfbz       int         `json:"zfbz"`       // 作废标志
	CzrMC      string      `json:"czrmc"`      // 创建人名称
	XgrMC      string      `json:"xgrmc"`      // 修改人名称
	ZfrMC      string      `json:"zfrmc"`      // 作废人名称
	Count      int         `json:"count"`      // 总数
}

// 个人信息
type PersonalInfo struct {
	GrxxID int    `json:"grxxid"` // 个人信息id
	Xm     string `json:"xm"`     // 姓名
	Bah    string `json:"bah"`    // 病案号
	XbDM   string `json:"xbdm"`   // 性别代码
	XbMC   string `json:"xbmc"`   // 性别名称
	ZjHm   string `json:"zjhm"`   // 证件号码
	ZjLx   string `json:"zjlx"`   // 证件类型
	ZjLxMC string `json:"zjlxmc"` // 证件类型名称
	SjHm   string `json:"sjhm"`   // 手机号码
	LxR    string `json:"lxr"`    // 联系人
	LxRDh  string `json:"lxrdh"`  // 联系人电话
	LxRGx  string `json:"lxrgx"`  // 联系人关系
	CsSj   string `json:"cssj"`   // 出生日期
	Nl     string `json:"nl"`     // 年龄
}

// 医生排班信息
type DoctorSchedule struct {
	YspbID int    `json:"yspbid"` // 医生排班id
	JgksID int    `json:"jgksid"` // 机构科室id
	JgksMC string `json:"jgksmc"` // 机构科室名称
	YsID   int    `json:"ysid"`   // 医生id
	YsMC   string `json:"ysmc"`   // 医生名称
	XqXh   int    `json:"xqxh"`   // 星期序号 1:周一-7:周日
	PbLx   int    `json:"pblx"`   // 排班类型 0:普通 1:专家 2:急诊
	KsSj   string `json:"kssj"`   // 开始时间
	JsSj   string `json:"jssj"`   // 结束时间
	Pbsj   string `json:"pbsj"`   // 排班时间
}

// 号源记录信息
type AppointmentSource struct {
	HyjlID int    `json:"hyjlid"` // 号源记录id
	YspbID int    `json:"yspbid"` // 医生排班id
	YsID   int    `json:"ysid"`   // 医生id
	YsMC   string `json:"ysmc"`   // 医生名称
	Pbsj   string `json:"pbsj"`   // 排班时间
	JgksID int    `json:"jgksid"` // 机构科室id
	JgksMC string `json:"jgksmc"` // 机构科室名称
	HyZt   int    `json:"hyzt"`   // 号源状态 0:未预约 1:已预约 2:已取号 3:已停诊
}

// 门诊挂号信息
type OutpatientRegistration struct {
	WsjgID int    `json:"wsjgid"` // 卫生机构id
	WsjgMC string `json:"wsjgmc"` // 卫生机构名称
	MzghID int    `json:"mzghid"` // 门诊挂号id
	Bah    string `json:"bah"`    // 病案号
	Xm     string `json:"xm"`     // 姓名
	XbDM   string `json:"xbdm"`   // 性别代码
	XbMC   string `json:"xbmc"`   // 性别名称
	Nl     int    `json:"nl"`     // 年龄
	ZjHm   string `json:"zjhm"`   // 证件号码
	JgksID int    `json:"jgksid"` // 机构科室id
	JgksMC string `json:"jgksmc"` // 机构科室名称
	YsID   int    `json:"ysid"`   // 医生id
	YsMC   string `json:"ysmc"`   // 医生名称
	GhSj   string `json:"ghsj"`   // 挂号时间
	ZfBz   string `json:"zfbz"`   // 作废标志 0:未作废 1:已作废
	GhLx   string `json:"ghlx"`   // 挂号类型
}

// 预约挂号信息
type AppointmentRegistration struct {
	YyghID int    `json:"yyghid"` // 预约挂号id
	GrxxID int    `json:"grxxid"` // 个人信息id
	Xm     string `json:"xm"`     // 患者姓名
	XbMC   string `json:"xbmc"`   // 性别名称
	HyjlID int    `json:"hyjlid"` // 号源记录id
	ZjHm   string `json:"zjhm"`   // 患者身份证号
	Nl     int    `json:"nl"`     // 年龄
	WsjgMC string `json:"wsjgmc"` // 卫生机构名称
	HyZt   int    `json:"hyzt"`   // 号源状态 0:未预约 1:已预约 2:已取号 3:已停诊
	YyLx   int    `json:"yylx"`   // 预约类型 1:现场预约 2:自助机预约挂号 3:电话预约 4:网上预约
	YsjgID int    `json:"ysjgid"` // 医生机构id
	YsMC   string `json:"ysmc"`   // 医生姓名
	JgksMC string `json:"jgksmc"` // 机构科室名称
	YyksID int    `json:"yyksid"` // 预约科室id
	Pbsj   string `json:"pbsj"`   // 排班时间
	Sjhm   string `json:"sjhm"`   // 手机号码
}

// 诊断记录信息
type DiagnosisRecord struct {
	ZdjlID  int    `json:"zdjlid"`  // 诊断记录id
	MzghID  int    `json:"mzghid"`  // 门诊挂号id
	Zdsj    string `json:"zdsj"`    // 诊断时间
	GrxxID  int    `json:"grxxid"`  // 个人信息id
	Xm      string `json:"xm"`      // 姓名
	XbMC    string `json:"xbmc"`    // 性别名称
	Nl      int    `json:"nl"`      // 年龄
	Tz      string `json:"tz"`      // 体重
	Sg      string `json:"sg"`      // 身高
	Zs      string `json:"zs"`      // 主诉
	JwsMain string `json:"jwsmain"` // 既往史主要内容
	Jws     string `json:"jws"`     // 既往史
	Grs     string `json:"grs"`     // 个人史
	Fzjc    string `json:"fzjc"`    // 辅助检查
	Zd      string `json:"zd"`      // 诊断
	Jy      string `json:"jy"`      // 建议
}
