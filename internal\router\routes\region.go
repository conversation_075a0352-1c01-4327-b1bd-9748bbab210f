package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	adminHandlerPkg "yekaitai/internal/modules/admin/handler"
	"yekaitai/internal/svc"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterRegionRoutes 注册地区管理相关路由
func RegisterRegionRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 初始化地区管理处理器
	regionHandler := adminHandlerPkg.NewRegionHandler(mysql.GetDB())

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 地区管理路由
	server.AddRoutes(
		[]rest.Route{
			// 获取所有省份
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/regions/provinces",
				Handler: adminAuthWrapper(regionHandler.GetProvinces),
			},
			// 获取指定省份的城市列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/regions/cities",
				Handler: adminAuthWrapper(regionHandler.GetCities),
			},
			// 获取指定城市的区县列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/regions/areas",
				Handler: adminAuthWrapper(regionHandler.GetAreas),
			},
			// 获取指定区县的乡镇列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/regions/towns",
				Handler: adminAuthWrapper(regionHandler.GetTowns),
			},
			// 获取指定乡镇的村庄列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/regions/villages",
				Handler: adminAuthWrapper(regionHandler.GetVillages),
			},
		},
	)
}
