package service

import (
	"context"
	"errors"
	"fmt"

	"yekaitai/internal/modules/user_points/model"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// UserPointsService 叶小币服务
type UserPointsService struct {
	db *gorm.DB
}

// NewUserPointsService 创建叶小币服务
func NewUserPointsService() *UserPointsService {
	return &UserPointsService{
		db: mysql.GetDB(),
	}
}

// GetGlobalConfig 获取全局配置
func (s *UserPointsService) GetGlobalConfig(ctx context.Context) (*model.CoinGlobalConfig, error) {
	var config model.CoinGlobalConfig
	err := s.db.WithContext(ctx).First(&config).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("全局配置不存在，请先创建配置")
		}
		return nil, fmt.Errorf("获取全局配置失败: %w", err)
	}
	return &config, nil
}

// CreateGlobalConfig 创建全局配置
func (s *UserPointsService) CreateGlobalConfig(ctx context.Context, req *model.CreateCoinGlobalConfigRequest) (*model.CoinGlobalConfig, error) {
	// 检查是否已存在配置
	var count int64
	err := s.db.WithContext(ctx).Model(&model.CoinGlobalConfig{}).Count(&count).Error
	if err != nil {
		return nil, fmt.Errorf("检查配置失败: %w", err)
	}
	if count > 0 {
		return nil, fmt.Errorf("全局配置已存在，不能重复创建")
	}

	config := &model.CoinGlobalConfig{
		Enabled:      req.Enabled,
		ExchangeRate: req.ExchangeRate,
	}

	err = s.db.WithContext(ctx).Create(config).Error
	if err != nil {
		return nil, fmt.Errorf("创建全局配置失败: %w", err)
	}

	return config, nil
}

// UpdateGlobalConfig 更新全局配置
func (s *UserPointsService) UpdateGlobalConfig(ctx context.Context, req *model.UpdateCoinGlobalConfigRequest) error {
	var config model.CoinGlobalConfig
	err := s.db.WithContext(ctx).First(&config, req.ID).Error
	if err != nil {
		return fmt.Errorf("配置不存在: %w", err)
	}

	// 更新配置
	config.Enabled = req.Enabled
	config.ExchangeRate = req.ExchangeRate

	err = s.db.WithContext(ctx).Save(&config).Error
	if err != nil {
		return fmt.Errorf("更新全局配置失败: %w", err)
	}

	return nil
}

// CreateCoinRule 创建叶小币规则
func (s *UserPointsService) CreateCoinRule(ctx context.Context, req *model.CreateCoinRuleRequest) (*model.CoinRules, error) {
	// 检查用户等级是否存在
	var count int64
	err := s.db.WithContext(ctx).Table("user_level_rules").Where("id = ? AND deleted_at IS NULL", req.UserLevelID).Count(&count).Error
	if err != nil {
		return nil, fmt.Errorf("检查用户等级失败: %w", err)
	}
	if count == 0 {
		return nil, errors.New("用户等级不存在")
	}

	// 检查同一等级下是否已存在相同类型的规则
	var existCount int64
	err = s.db.WithContext(ctx).Model(&model.CoinRules{}).Where("user_level_id = ? AND rule_type = ?", req.UserLevelID, req.RuleType).Count(&existCount).Error
	if err != nil {
		return nil, fmt.Errorf("检查规则重复失败: %w", err)
	}
	if existCount > 0 {
		return nil, errors.New("该等级下已存在相同类型的规则")
	}

	rule := &model.CoinRules{
		UserLevelID:      req.UserLevelID,
		RuleType:         req.RuleType,
		RuleName:         req.RuleName,
		Description:      req.Description,
		Enabled:          req.Enabled,
		CoinsAwarded:     req.CoinsAwarded,
		MinAmount:        req.MinAmount,
		AmountThreshold:  req.AmountThreshold,
		IsOneTime:        req.IsOneTime,
		RequireShare:     req.RequireShare,
		ActivityType:     req.ActivityType,
		ExpiryPolicyType: req.ExpiryPolicyType,
		CustomYears:      req.CustomYears,
	}

	err = s.db.WithContext(ctx).Create(rule).Error
	if err != nil {
		return nil, fmt.Errorf("创建规则失败: %w", err)
	}

	return rule, nil
}

// UpdateCoinRule 更新叶小币规则
func (s *UserPointsService) UpdateCoinRule(ctx context.Context, req *model.UpdateCoinRuleRequest) error {
	var rule model.CoinRules
	err := s.db.WithContext(ctx).First(&rule, req.ID).Error
	if err != nil {
		return fmt.Errorf("规则不存在: %w", err)
	}

	// 检查用户等级是否存在
	var count int64
	err = s.db.WithContext(ctx).Table("user_level_rules").Where("id = ? AND deleted_at IS NULL", req.UserLevelID).Count(&count).Error
	if err != nil {
		return fmt.Errorf("检查用户等级失败: %w", err)
	}
	if count == 0 {
		return errors.New("用户等级不存在")
	}

	// 检查同一等级下是否已存在相同类型的规则（排除当前规则）
	var existCount int64
	err = s.db.WithContext(ctx).Model(&model.CoinRules{}).Where("user_level_id = ? AND rule_type = ? AND id != ?", req.UserLevelID, req.RuleType, req.ID).Count(&existCount).Error
	if err != nil {
		return fmt.Errorf("检查规则重复失败: %w", err)
	}
	if existCount > 0 {
		return errors.New("该等级下已存在相同类型的规则")
	}

	// 更新规则
	rule.UserLevelID = req.UserLevelID
	rule.RuleType = req.RuleType
	rule.RuleName = req.RuleName
	rule.Description = req.Description
	rule.Enabled = req.Enabled
	rule.CoinsAwarded = req.CoinsAwarded
	rule.MinAmount = req.MinAmount
	rule.AmountThreshold = req.AmountThreshold
	rule.IsOneTime = req.IsOneTime
	rule.RequireShare = req.RequireShare
	rule.ActivityType = req.ActivityType
	rule.ExpiryPolicyType = req.ExpiryPolicyType
	rule.CustomYears = req.CustomYears

	err = s.db.WithContext(ctx).Save(&rule).Error
	if err != nil {
		return fmt.Errorf("更新规则失败: %w", err)
	}

	return nil
}

// DeleteCoinRule 删除叶小币规则
func (s *UserPointsService) DeleteCoinRule(ctx context.Context, id uint) error {
	var rule model.CoinRules
	err := s.db.WithContext(ctx).First(&rule, id).Error
	if err != nil {
		return fmt.Errorf("规则不存在: %w", err)
	}

	err = s.db.WithContext(ctx).Delete(&rule).Error
	if err != nil {
		return fmt.Errorf("删除规则失败: %w", err)
	}

	return nil
}

// GetCoinRule 获取叶小币规则详情
func (s *UserPointsService) GetCoinRule(ctx context.Context, id uint) (*model.CoinRules, error) {
	var rule model.CoinRules
	err := s.db.WithContext(ctx).First(&rule, id).Error
	if err != nil {
		return nil, fmt.Errorf("获取规则失败: %w", err)
	}
	return &rule, nil
}

// GetCoinRuleList 获取叶小币规则列表
func (s *UserPointsService) GetCoinRuleList(ctx context.Context, req *model.CoinRuleListRequest) ([]*model.CoinRules, int64, error) {
	query := s.db.WithContext(ctx).Model(&model.CoinRules{})

	// 添加筛选条件
	if req.UserLevelID > 0 {
		query = query.Where("user_level_id = ?", req.UserLevelID)
	}
	if req.RuleType != "" {
		query = query.Where("rule_type = ?", req.RuleType)
	}
	if req.Enabled != nil {
		query = query.Where("enabled = ?", *req.Enabled)
	}

	// 获取总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取规则总数失败: %w", err)
	}

	// 分页查询
	var rules []*model.CoinRules
	offset := (req.Page - 1) * req.Size
	err = query.Offset(offset).Limit(req.Size).Order("created_at DESC").Find(&rules).Error
	if err != nil {
		return nil, 0, fmt.Errorf("获取规则列表失败: %w", err)
	}

	return rules, total, nil
}

// GetCoinRulesByLevel 根据等级获取叶小币规则
func (s *UserPointsService) GetCoinRulesByLevel(ctx context.Context, userLevelID uint) ([]*model.CoinRules, error) {
	var rules []*model.CoinRules
	err := s.db.WithContext(ctx).Where("user_level_id = ? AND enabled = ?", userLevelID, true).Find(&rules).Error
	if err != nil {
		return nil, fmt.Errorf("获取等级规则失败: %w", err)
	}
	return rules, nil
}

// SyncRulesToAllLevels 同步规则到所有等级
func (s *UserPointsService) SyncRulesToAllLevels(ctx context.Context, req *model.SyncToAllLevelsRequest) error {
	// 获取源等级的所有规则
	var sourceRules []*model.CoinRules
	err := s.db.WithContext(ctx).Where("user_level_id = ?", req.SourceLevelID).Find(&sourceRules).Error
	if err != nil {
		return fmt.Errorf("获取源等级规则失败: %w", err)
	}

	if len(sourceRules) == 0 {
		return errors.New("源等级没有规则可同步")
	}

	// 获取所有等级
	var allLevels []struct {
		ID uint `json:"id"`
	}
	err = s.db.WithContext(ctx).Table("user_level_rules").Where("deleted_at IS NULL AND id != ?", req.SourceLevelID).Select("id").Find(&allLevels).Error
	if err != nil {
		return fmt.Errorf("获取所有等级失败: %w", err)
	}

	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 为每个等级同步规则
	for _, level := range allLevels {
		for _, sourceRule := range sourceRules {
			// 检查目标等级是否已存在相同类型的规则
			var existingRule model.CoinRules
			err = tx.Where("user_level_id = ? AND rule_type = ?", level.ID, sourceRule.RuleType).First(&existingRule).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				tx.Rollback()
				return fmt.Errorf("检查现有规则失败: %w", err)
			}

			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 不存在，创建新规则
				newRule := &model.CoinRules{
					UserLevelID:      level.ID,
					RuleType:         sourceRule.RuleType,
					RuleName:         sourceRule.RuleName,
					Description:      sourceRule.Description,
					Enabled:          sourceRule.Enabled,
					CoinsAwarded:     sourceRule.CoinsAwarded,
					MinAmount:        sourceRule.MinAmount,
					AmountThreshold:  sourceRule.AmountThreshold,
					IsOneTime:        sourceRule.IsOneTime,
					RequireShare:     sourceRule.RequireShare,
					ActivityType:     sourceRule.ActivityType,
					ExpiryPolicyType: sourceRule.ExpiryPolicyType,
					CustomYears:      sourceRule.CustomYears,
				}
				err = tx.Create(newRule).Error
				if err != nil {
					tx.Rollback()
					return fmt.Errorf("创建同步规则失败: %w", err)
				}
			} else {
				// 已存在，更新规则
				existingRule.RuleName = sourceRule.RuleName
				existingRule.Description = sourceRule.Description
				existingRule.Enabled = sourceRule.Enabled
				existingRule.CoinsAwarded = sourceRule.CoinsAwarded
				existingRule.MinAmount = sourceRule.MinAmount
				existingRule.AmountThreshold = sourceRule.AmountThreshold
				existingRule.IsOneTime = sourceRule.IsOneTime
				existingRule.RequireShare = sourceRule.RequireShare
				existingRule.ActivityType = sourceRule.ActivityType
				existingRule.ExpiryPolicyType = sourceRule.ExpiryPolicyType
				existingRule.CustomYears = sourceRule.CustomYears
				err = tx.Save(&existingRule).Error
				if err != nil {
					tx.Rollback()
					return fmt.Errorf("更新同步规则失败: %w", err)
				}
			}
		}
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		return fmt.Errorf("提交同步事务失败: %w", err)
	}

	return nil
}

// IsGlobalEnabled 检查全局开关是否启用
func (s *UserPointsService) IsGlobalEnabled(ctx context.Context) (bool, error) {
	config, err := s.GetGlobalConfig(ctx)
	if err != nil {
		return false, err
	}
	return config.Enabled, nil
}

// GetUserLevels 获取所有用户等级
func (s *UserPointsService) GetUserLevels(ctx context.Context) ([]map[string]interface{}, error) {
	var levels []map[string]interface{}
	err := s.db.WithContext(ctx).Table("user_level_rules").Where("deleted_at IS NULL").Select("id, level_name, level_order").Order("level_order ASC").Find(&levels).Error
	if err != nil {
		return nil, fmt.Errorf("获取用户等级失败: %w", err)
	}
	return levels, nil
}

// GetRuleTypes 获取所有规则类型
func (s *UserPointsService) GetRuleTypes() []map[string]string {
	return []map[string]string{
		{"value": model.RuleTypeRegister, "label": "注册奖励"},
		{"value": model.RuleTypeCompleteProfile, "label": "完善信息"},
		{"value": model.RuleTypeActivity, "label": "活动奖励"},
		{"value": model.RuleTypeCheckin, "label": "打卡奖励"},
		{"value": model.RuleTypeCheckinShare, "label": "打卡分享奖励"},
		{"value": model.RuleTypeReferral, "label": "推荐奖励"},
		{"value": model.RuleTypeConsume, "label": "消费奖励"},
		{"value": model.RuleTypeConsumeAccumulate, "label": "消费累计奖励"},
		{"value": model.RuleTypeReferralConsume, "label": "推荐人消费奖励"},
	}
}

// GetExpiryTypes 获取所有期限类型
func (s *UserPointsService) GetExpiryTypes() []map[string]string {
	return []map[string]string{
		{"value": model.ExpiryTypePermanent, "label": "永久"},
		{"value": model.ExpiryTypeYearly, "label": "逐年（当年）"},
		{"value": model.ExpiryTypeMonthly, "label": "逐月（当月）"},
	}
}

// GetActivityTypes 获取所有活动类型
func (s *UserPointsService) GetActivityTypes() []map[string]string {
	return []map[string]string{
		{"value": model.ActivityTypeGeneral, "label": "普通活动"},
		{"value": model.ActivityTypeSpecial, "label": "专项活动"},
		{"value": model.ActivityTypePromotion, "label": "促销活动"},
		{"value": model.ActivityTypeEducation, "label": "教育活动"},
	}
}