# YeKaiTai 定时任务和手动执行任务文档

## 概述

YeKaiTai 系统提供了丰富的定时任务和手动执行任务，用于数据同步、业务处理和系统维护。所有任务都已集成到主程序中，通过统一的命令行接口执行。

## 定时任务列表

定时任务在 `admin` 或 `all` 服务模式下自动启动，无需手动干预。

### 1. 基础数据同步任务

| 任务名称 | 执行频率 | Cron表达式 | 功能描述 |
|---------|---------|-----------|----------|
| 门诊收费信息同步 | 每小时30分 | `0 30 * * * ?` | 同步HIS系统的门诊收费数据 |
| 医生数据同步 | 每小时10分 | `0 10 * * * ?` | 同步HIS系统的医生信息 |
| 患者数据同步 | 每小时20分 | `0 20 * * * ?` | 同步HIS系统的患者信息 |

### 2. ABC云同步任务

| 任务名称 | 执行频率 | Cron表达式 | 功能描述 |
|---------|---------|-----------|----------|
| ABC云门店同步 | 每小时整点 | `0 0 * * * ?` | 同步ABC云系统的门店信息 |
| ABC云科室同步 | 每小时5分 | `0 5 * * * ?` | 同步ABC云系统的科室信息 |
| ABC云医生同步 | 每小时10分 | `0 10 * * * ?` | 同步ABC云系统的医生信息 |
| ABC云患者同步 | 每小时15分 | `0 15 * * * ?` | 同步ABC云系统的患者信息 |

### 3. 万里牛ERP同步任务

| 任务名称 | 执行频率 | Cron表达式 | 功能描述 |
|---------|---------|-----------|----------|
| 万里牛商品分类同步 | 每小时20分 | `0 20 * * * ?` | 同步商品分类数据 |
| 万里牛商品同步 | 每小时25分 | `0 25 * * * ?` | 同步商品基础信息 |
| 万里牛库存同步 | 每小时35分 | `0 35 * * * ?` | 同步商品库存数据 |
| 万里牛发货状态同步 | 每20分钟 | `0 */20 * * * ?` | 同步订单发货状态 |
| 万里牛失败订单推送 | 每小时40分 | `0 40 * * * ?` | 重新推送失败的订单 |

### 4. 业务处理任务

| 任务名称 | 执行频率 | Cron表达式 | 功能描述 |
|---------|---------|-----------|----------|
| 优惠券发放任务处理 | 每2分钟 | `0 */2 * * * ?` | 处理异步优惠券发放任务 |
| 订单自动关闭 | 每2分钟 | `0 */2 * * * ?` | 关闭超时未支付订单 |

## 手动执行任务列表

所有手动任务都通过以下命令格式执行：
```bash
./main -f <配置文件> -tool_task <任务名称> [参数]
```

### 1. 基础数据同步

#### 门店地理编码同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task store_geo
```
- **功能**：同步门店地址信息到经纬度坐标
- **依赖**：腾讯地图API

#### 医生数据同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task doctor_sync
```
- **功能**：从HIS系统同步医生信息

#### 患者数据同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task patient_sync
```
- **功能**：从HIS系统同步患者信息

#### 机构数据同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task org_sync
```
- **功能**：从HIS系统同步医疗机构信息

#### 科室数据同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task department_sync
```
- **功能**：从HIS系统同步科室信息

#### 门诊收费信息同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task charge_sync
```
- **功能**：同步门诊收费信息（从2024年10月6日开始到今天）

### 2. ABC云数据同步

#### ABC云门店同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task abcyun_store_sync
```

#### ABC云科室同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task abcyun_department_sync
```

#### ABC云医生同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task abcyun_doctor_sync
```

#### ABC云患者同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task abcyun_patient_sync
```
- **功能**：同步ABC云患者数据（从2025年4月9日开始到今天）

### 3. 万里牛ERP同步

#### 商品数据同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_goods_sync
```
- **功能**：手动同步万里牛商品数据（首次全量同步或增量同步）

#### 库存数据同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_sync
```
- **功能**：手动同步万里牛库存数据

#### 发货状态同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_shipping_sync
```
- **功能**：同步万里牛发货状态

#### 失败订单推送
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_failed_push
```
- **功能**：推送失败的订单到万里牛ERP

### 4. 万里牛库存查询

#### 单笔库存查询（旧接口）
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_query -item_id "商品编码" -spec_code "规格编码"
```
- **示例**：
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_query -item_id "4b60170" -spec_code "001"
```

#### 库存查询V2（新接口）
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_query_v2 -spec_code "规格编码"
```
- **示例**：
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_query_v2 -spec_code "SPEC_456"
```

#### 库存查询V2（时间范围）
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_query_v2_time
```
- **功能**：查询最近7天修改的库存数据

### 5. 万里牛商品分类

#### 分类API测试
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_categories_test
```
- **功能**：测试万里牛商品分类API

#### 分类数据同步
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_categories_sync
```
- **功能**：手动同步万里牛商品分类

#### 初始化默认分类
```bash
./main -f etc/yekaitai-dev.yaml -tool_task init_default_category
```
- **功能**：初始化默认商品分类

### 6. 测试任务

#### 库存同步测试
```bash
./main -f etc/yekaitai-dev.yaml -tool_task wanliniu_inventory_sync_test
```
- **功能**：测试万里牛库存同步功能

### 7. 优惠券管理

#### 手动执行优惠券发放任务
```bash
./main -f etc/yekaitai-dev.yaml -tool_task coupon_issue_execute -task_id 任务ID
```
- **示例**：
```bash
./main -f etc/yekaitai-dev.yaml -tool_task coupon_issue_execute -task_id 123
```

## 参数说明

| 参数 | 说明 | 示例 | 必需 |
|------|------|------|------|
| `-f` | 配置文件路径 | `etc/yekaitai-dev.yaml` | 是 |
| `-tool_task` | 要执行的任务名称 | `doctor_sync` | 是 |
| `-item_id` | 商品编码（库存查询用） | `"4b60170"` | 否 |
| `-spec_code` | 规格编码（库存查询用） | `"SPEC_456"` | 否 |
| `-task_id` | 任务ID（优惠券发放用） | `123` | 否 |

## 配置要求

### 必需配置

1. **数据库配置**：MySQL连接信息
2. **Redis配置**：缓存和会话存储
3. **日志配置**：日志输出路径和级别

### 可选配置

1. **杭州HIS配置**：HIS系统同步任务需要
2. **ABC云配置**：ABC云同步任务需要
3. **万里牛ERP配置**：万里牛相关任务需要
4. **腾讯地图配置**：门店地理编码同步需要

## 使用建议

### 1. 首次部署
1. 先执行手动同步任务验证配置
2. 确认无误后启动定时任务服务
3. 监控日志输出确保任务正常执行

### 2. 日常维护
1. 定期检查定时任务执行日志
2. 对于失败的任务，可手动重新执行
3. 根据业务需要调整任务执行频率

### 3. 故障排查
1. 检查配置文件是否正确
2. 确认网络连接到第三方系统
3. 查看详细错误日志进行问题定位

## 注意事项

1. **执行权限**：确保程序有足够的文件和网络访问权限
2. **配置安全**：保护好配置文件中的敏感信息
3. **资源消耗**：大量同步任务可能消耗较多系统资源
4. **数据一致性**：同步过程中避免手动修改相关数据
5. **备份策略**：重要数据同步前建议先备份

## 环境配置示例

### 开发环境配置 (etc/yekaitai-dev.yaml)
```yaml
# 定时任务配置
cron:
  enable: true  # 是否启用定时任务
  storeGeoSync:
    enable: true  # 是否启用门店地理编码同步
    cron: "0 0 3 * * ?"  # 每天凌晨3点执行
    batchSize: 100  # 每批处理数量

# 腾讯地图配置
tencentMap:
  key: "YOUR_TENCENT_MAP_KEY"

# 万里牛ERP配置
wanliniu:
  BaseURL: https://open-api.hupun.com/api
  AppKey: "YOUR_APP_KEY"
  AppSecret: "YOUR_APP_SECRET"
  shop_nick: "测试叶小医"
  shop_type: 100

# 杭州HIS配置
HangzhouHIS:
  BaseURL: "http://his.example.com"
  Port: 8080
  ClientSecret: "YOUR_CLIENT_SECRET"
  UserName: "YOUR_USERNAME"
  Password: "YOUR_PASSWORD"
  # ... 其他HIS配置

# ABC云配置
abcYun:
  BaseURL: "https://api.abcyun.com"
  AppID: "YOUR_APP_ID"
  AppSecret: "YOUR_APP_SECRET"
  ClinicID: "YOUR_CLINIC_ID"
```

## 定时任务执行时间表

| 时间 | 任务 | 说明 |
|------|------|------|
| 每小时00分 | ABC云门店同步 | 同步门店基础信息 |
| 每小时05分 | ABC云科室同步 | 同步科室信息 |
| 每小时10分 | 医生数据同步、ABC云医生同步 | 同步医生信息 |
| 每小时15分 | ABC云患者同步 | 同步患者信息 |
| 每小时20分 | 患者数据同步、万里牛商品分类同步 | 同步患者和分类信息 |
| 每小时25分 | 万里牛商品同步 | 同步商品基础信息 |
| 每小时30分 | 门诊收费信息同步 | 同步收费数据 |
| 每小时35分 | 万里牛库存同步 | 同步库存数据 |
| 每小时40分 | 万里牛失败订单推送 | 重推失败订单 |
| 每20分钟 | 万里牛发货状态同步 | 同步发货状态 |
| 每2分钟 | 优惠券发放任务处理、订单自动关闭 | 处理业务任务 |

## 常见问题解答

### Q1: 如何查看定时任务是否正常运行？
A: 查看日志文件 `logs/app.log`，定时任务执行时会输出详细日志。

### Q2: 手动任务执行失败怎么办？
A:
1. 检查配置文件是否正确
2. 确认网络连接正常
3. 查看错误日志进行具体问题排查
4. 确认第三方系统API是否可用

### Q3: 可以修改定时任务的执行频率吗？
A: 可以，修改 `internal/service/cron_service.go` 中的 cron 表达式，然后重新编译部署。

### Q4: 如何停止某个定时任务？
A: 目前需要修改代码注释掉相应的任务注册，然后重新部署。

### Q5: 数据同步会覆盖本地修改吗？
A: 不会，同步逻辑会保留本地的自定义设置，只更新来源系统的数据。

## 监控和告警

### 日志监控
- **位置**：`logs/` 目录
- **格式**：JSON格式，便于日志分析
- **级别**：INFO、WARN、ERROR

### 关键指标
- 任务执行成功率
- 数据同步数量
- 执行耗时
- 错误频率

### 建议告警规则
- 连续3次任务执行失败
- 单次任务执行时间超过30分钟
- 数据同步数量异常（过多或过少）

## 性能优化建议

1. **批量处理**：大量数据同步时使用批量操作
2. **并发控制**：避免同时执行过多同步任务
3. **增量同步**：优先使用增量同步减少数据传输
4. **缓存策略**：合理使用Redis缓存减少数据库压力
5. **错误重试**：实现指数退避的重试机制

## 查看帮助

执行不带参数的命令可查看所有可用任务：
```bash
./main -f etc/yekaitai-dev.yaml -tool_task
```

## 版本更新记录

- **v1.0.0**: 初始版本，支持基础同步任务
- **v1.1.0**: 新增ABC云同步任务
- **v1.2.0**: 新增万里牛ERP集成
- **v1.3.0**: 新增优惠券和订单处理任务
- **v1.4.0**: 任务集成到主程序，统一命令行接口
