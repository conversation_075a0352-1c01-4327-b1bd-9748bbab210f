package queue

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/pkg/adapters/medlinker"
	"yekaitai/pkg/infra/kafka"
)

// KafkaProducerAdapter Kafka生产者适配器，实现medlinker.KafkaProducer接口
type KafkaProducerAdapter struct {
	isEnabled bool // 是否启用Kafka
}

// NewKafkaProducerAdapter 创建Kafka生产者适配器
func NewKafkaProducerAdapter() medlinker.KafkaProducer {
	// 检查Kafka是否已初始化
	isEnabled := kafka.IsProducerInitialized()
	if !isEnabled {
		logx.Info("Kafka生产者未初始化，将以模拟模式运行")
	} else {
		logx.Info("Kafka生产者已初始化，消息将发送到Kafka")
	}

	return &KafkaProducerAdapter{
		isEnabled: isEnabled,
	}
}

// SendMessage 发送消息到Kafka
func (p *KafkaProducerAdapter) SendMessage(topic string, key, value []byte) (int32, int64, error) {
	// 如果Kafka未启用，仅记录日志并返回成功
	if !p.isEnabled {
		logx.Infof("模拟发送Kafka消息: topic=%s, key=%s, value长度=%d字节",
			topic, string(key), len(value))
		return 0, 0, nil
	}

	// 安全调用Kafka发送
	partition, offset, err := kafka.SendMessage(topic, key, value)
	if err != nil {
		logx.Errorf("发送Kafka消息失败: %v", err)
		return 0, 0, fmt.Errorf("发送Kafka消息失败: %w", err)
	}

	logx.Infof("发送Kafka消息成功: topic=%s, partition=%d, offset=%d",
		topic, partition, offset)
	return partition, offset, nil
}
