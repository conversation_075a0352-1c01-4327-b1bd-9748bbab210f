package jushuitan

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

// LogisticQueryRequest 物流查询请求
type LogisticQueryRequest struct {
	ShopID        int      `json:"shop_id,omitempty"`        // 店铺编号
	PageIndex     int      `json:"page_index,omitempty"`     // 第几页，从1开始
	PageSize      int      `json:"page_size,omitempty"`      // 默认30，最大不超过50
	ModifiedBegin string   `json:"modified_begin,omitempty"` // 修改起始时间，和结束时间必须同时存在，时间间隔不能超过七天
	ModifiedEnd   string   `json:"modified_end,omitempty"`   // 修改结束时间，和起始时间必须同时存在，时间间隔不能超过七天
	DateType      int      `json:"date_type,omitempty"`      // 日期类型，默认发货时间，0=修改时间，1=制单日期，2=订单日期，3=发货时间
	SoIDs         []string `json:"so_ids,omitempty"`         // 平台订单编号，最多20（如果通过线上单号查询除时间条件外不受其它查询条件限制）
}

// LogisticQueryResponse 物流查询响应
type LogisticQueryResponse struct {
	PageSize  int                 `json:"page_size"`  // 每页多少条
	PageIndex int                 `json:"page_index"` // 第几页
	DataCount int                 `json:"data_count"` // 总条数
	PageCount int                 `json:"page_count"` // 总页数
	HasNext   bool                `json:"has_next"`   // 是否有下一页
	Orders    []LogisticOrderInfo `json:"orders"`     // 数据集合
}

// LogisticOrderInfo 物流订单信息
type LogisticOrderInfo struct {
	OID              int                     `json:"o_id"`                // ERP订单号;唯一
	ShopID           int                     `json:"shop_id"`             // 店铺编号
	SoID             string                  `json:"so_id"`               // 订单号，最长不超过50;唯一
	AsID             int                     `json:"as_id,omitempty"`     // 补发换货订单对应的售后单号
	SendDate         string                  `json:"send_date"`           // 发货时间
	Freight          float64                 `json:"freight"`             // 运费
	Weight           float64                 `json:"weight,omitempty"`    // 重量
	WmsCoID          int                     `json:"wms_co_id,omitempty"` // 发货仓编码（0表示主仓发货）
	LcID             string                  `json:"lc_id"`               // 快递公司代码
	LID              string                  `json:"l_id"`                // 快递单号
	LogisticsCompany string                  `json:"logistics_company"`   // 快递公司
	Items            []LogisticOrderItemInfo `json:"items"`               // 订单明细；商品信息
}

// LogisticOrderItemInfo 物流订单商品明细
type LogisticOrderItemInfo struct {
	SkuID        string `json:"sku_id"`             // 商家SKU，对应库存管理的SKU
	Qty          int    `json:"qty"`                // 购买数量
	OuterOiID    string `json:"outer_oi_id"`        // 子订单号
	RawSoID      string `json:"raw_so_id"`          // 原始平台订单号，可以为空，最长不超过50
	OID          string `json:"o_id"`               // 内部单号
	RefundStatus string `json:"refund_status"`      // 商品退款状态
	SkuType      string `json:"sku_type,omitempty"` // 商品类型
}

// LogisticDB 物流信息数据库模型
type LogisticDB struct {
	ID               int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	OID              int       `gorm:"column:o_id;index;comment:ERP订单号" json:"o_id"`                   // ERP订单号
	ShopID           int       `gorm:"column:shop_id;index;comment:店铺编号" json:"shop_id"`               // 店铺编号
	SoID             string    `gorm:"column:so_id;index;comment:订单号" json:"so_id"`                    // 订单号
	AsID             int       `gorm:"column:as_id;index;comment:售后单号" json:"as_id"`                   // 售后单号
	SendDate         string    `gorm:"column:send_date;comment:发货时间" json:"send_date"`                 // 发货时间
	Freight          float64   `gorm:"column:freight;comment:运费" json:"freight"`                       // 运费
	Weight           float64   `gorm:"column:weight;comment:重量" json:"weight"`                         // 重量
	WmsCoID          int       `gorm:"column:wms_co_id;comment:发货仓编码" json:"wms_co_id"`                // 发货仓编码
	LcID             string    `gorm:"column:lc_id;comment:快递公司代码" json:"lc_id"`                       // 快递公司代码
	LID              string    `gorm:"column:l_id;comment:快递单号" json:"l_id"`                           // 快递单号
	LogisticsCompany string    `gorm:"column:logistics_company;comment:快递公司" json:"logistics_company"` // 快递公司
	CreatedAt        time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`             // 记录创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`             // 记录更新时间
}

// TableName 指定表名
func (LogisticDB) TableName() string {
	return "jst_logistic_db"
}

// LogisticItemDB 物流商品明细数据库模型
type LogisticItemDB struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	LogisticID   int64     `gorm:"column:logistic_id;index;comment:物流信息ID" json:"logistic_id"` // 物流信息ID
	SkuID        string    `gorm:"column:sku_id;index;comment:商品编码" json:"sku_id"`             // 商品编码
	Qty          int       `gorm:"column:qty;comment:购买数量" json:"qty"`                         // 购买数量
	OuterOiID    string    `gorm:"column:outer_oi_id;comment:子订单号" json:"outer_oi_id"`         // 子订单号
	RawSoID      string    `gorm:"column:raw_so_id;comment:原始平台订单号" json:"raw_so_id"`          // 原始平台订单号
	OID          string    `gorm:"column:o_id;comment:内部单号" json:"o_id"`                       // 内部单号
	RefundStatus string    `gorm:"column:refund_status;comment:商品退款状态" json:"refund_status"`   // 商品退款状态
	SkuType      string    `gorm:"column:sku_type;comment:商品类型" json:"sku_type"`               // 商品类型
	CreatedAt    time.Time `gorm:"column:created_at;comment:记录创建时间" json:"created_at"`         // 记录创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;comment:记录更新时间" json:"updated_at"`         // 记录更新时间
}

// TableName 指定表名
func (LogisticItemDB) TableName() string {
	return "jst_logistic_item_db"
}

// QueryLogistic 查询物流信息
func (c *Client) QueryLogistic(ctx context.Context, req LogisticQueryRequest) (*LogisticQueryResponse, error) {
	respBody, err := c.Request(ctx, PathTradeQuery, req)
	if err != nil {
		return nil, fmt.Errorf("物流查询请求执行失败: %w", err)
	}

	var baseResp BaseResponse
	if err := json.Unmarshal(respBody, &baseResp); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %w", err)
	}

	if baseResp.Code != 0 {
		return nil, fmt.Errorf("API返回错误: %s", baseResp.Msg)
	}

	// 重新将data部分解析为LogisticQueryResponse
	dataBytes, err := json.Marshal(baseResp.Data)
	if err != nil {
		return nil, fmt.Errorf("序列化data字段失败: %w", err)
	}

	var resp LogisticQueryResponse
	if err := json.Unmarshal(dataBytes, &resp); err != nil {
		return nil, fmt.Errorf("解析物流查询响应失败: %w", err)
	}

	return &resp, nil
}
