package handler

import (
	"net/http"
	"strings"

	"yekaitai/internal/types"
	"yekaitai/pkg/common/model/evaluation"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// ServiceEvaluationHandler 服务评价处理器
type ServiceEvaluationHandler struct {
	repo *evaluation.ServiceEvaluationRepository
}

// NewServiceEvaluationHandler 创建服务评价处理器
func NewServiceEvaluationHandler() *ServiceEvaluationHandler {
	return &ServiceEvaluationHandler{
		repo: evaluation.NewServiceEvaluationRepository(mysql.GetDB()),
	}
}

// CreateEvaluationRequest 创建评价请求
type CreateEvaluationRequest struct {
	Content     string `json:"content" validate:"required,min=1,max=300"`
	IsAnonymous bool   `json:"is_anonymous"`
}

// EvaluationStatusResponse 评价状态响应
type EvaluationStatusResponse struct {
	HasEvaluated bool   `json:"has_evaluated"`
	Content      string `json:"content,omitempty"`
	CreatedAt    string `json:"created_at,omitempty"`
}

// CreateEvaluation 创建服务评价
func (h *ServiceEvaluationHandler) CreateEvaluation(w http.ResponseWriter, r *http.Request) {
	var req CreateEvaluationRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析创建评价请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 获取用户信息
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 检查用户是否已经评价过
	hasEvaluated, err := h.repo.HasUserEvaluated(r.Context(), userID)
	if err != nil {
		logx.Errorf("检查用户评价状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "检查评价状态失败"))
		return
	}

	if hasEvaluated {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "您已经评价过了，每位用户只能评价一次"))
		return
	}

	// 截取评价内容（确保不超过300字）
	content := strings.TrimSpace(req.Content)
	contentRunes := []rune(content)
	if len(contentRunes) > 300 {
		content = string(contentRunes[:300])
	}

	// 创建评价记录
	serviceEvaluation := &evaluation.ServiceEvaluation{
		UserID:      userID,
		Content:     content,
		IsAnonymous: req.IsAnonymous,
		Status:      1, // 默认显示状态
	}

	err = h.repo.Create(r.Context(), serviceEvaluation)
	if err != nil {
		logx.Errorf("创建服务评价失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "提交评价失败"))
		return
	}

	logx.Infof("用户 %d 提交服务评价成功，评价ID: %d", userID, serviceEvaluation.ID)

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(map[string]interface{}{
		"id":         serviceEvaluation.ID,
		"message":    "评价提交成功",
		"created_at": serviceEvaluation.CreatedAt.Format("2006-01-02 15:04:05"),
	}))
}

// GetEvaluationStatus 获取用户评价状态
func (h *ServiceEvaluationHandler) GetEvaluationStatus(w http.ResponseWriter, r *http.Request) {
	// 获取用户信息
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 查找用户的评价记录
	userEvaluation, err := h.repo.FindByUserID(r.Context(), userID)
	if err != nil {
		logx.Errorf("查询用户评价状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询评价状态失败"))
		return
	}

	response := &EvaluationStatusResponse{
		HasEvaluated: userEvaluation != nil,
	}

	// 如果已评价，返回评价信息
	if userEvaluation != nil {
		response.Content = userEvaluation.Content
		response.CreatedAt = userEvaluation.CreatedAt.Format("2006-01-02 15:04:05")
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(response))
}
