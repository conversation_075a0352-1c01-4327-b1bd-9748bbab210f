package utils

import (
	"fmt"
	"math/rand"
	"time"
)

// GenerateAppointmentNo 生成预约编号
func GenerateAppointmentNo() string {
	return fmt.Sprintf("APT%s%06d", time.Now().Format("20060102"), time.Now().UnixNano()%1000000)
}

// GenerateOrderNo 生成订单编号
func GenerateOrderNo() string {
	return fmt.Sprintf("ORD%s%06d", time.Now().Format("20060102"), time.Now().UnixNano()%1000000)
}

// GeneratePaymentNo 生成支付单号
func GeneratePaymentNo() string {
	return fmt.Sprintf("PAY%s%06d", time.Now().Format("20060102"), time.Now().UnixNano()%1000000)
}

// GenerateRefundNo 生成退款单号
func GenerateRefundNo() string {
	return fmt.Sprintf("REF%s%06d", time.Now().Format("20060102"), time.Now().UnixNano()%1000000)
}

// GenerateTraceNo 生成追踪编号
func GenerateTraceNo() string {
	rand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("TR%s%06d", time.Now().Format("20060102150405"), rand.Intn(1000000))
}
