package logic

import (
	"context"
	"yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// DistributorLogic 分销商查询逻辑
type DistributorLogic struct {
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

// NewDistributorLogic 创建分销商查询逻辑
func NewDistributorLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *DistributorLogic {
	return &DistributorLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// QueryDistributors 查询分销商列表
func (l *DistributorLogic) QueryDistributors(pageNum, pageSize int, params map[string]interface{}) (*jushuitan.BaseResp, error) {
	// 构建请求参数
	req := &jushuitan.DistributorQueryRequest{
		PageNum:  pageNum,
		PageSize: pageSize,
	}

	// 设置可选参数
	if val, ok := params["status"]; ok {
		if status, ok := val.(int); ok {
			req.Status = status
		}
	}
	if val, ok := params["updatedStart"]; ok {
		if updatedStart, ok := val.(string); ok {
			req.UpdatedStart = updatedStart
		}
	}
	if val, ok := params["updatedEnd"]; ok {
		if updatedEnd, ok := val.(string); ok {
			req.UpdatedEnd = updatedEnd
		}
	}

	return l.svcCtx.JushuitanClient.QueryDistributors(l.ctx, req)
}
