package handler

import (
	"fmt"
	"net/http"

	"yekaitai/internal/svc"
	"yekaitai/pkg/upload"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"go.uber.org/zap"
)

// UploadFileHandler 文件上传处理器
func UploadFileHandler(ctx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单
		err := r.ParseMultipartForm(10 << 20) // 限制上传文件大小为10MB
		if err != nil {
			logx.Error("解析表单失败", zap.Error(err))
			httpx.Error(w, err)
			return
		}

		// 获取文件
		file, header, err := r.FormFile("file")
		if err != nil {
			logx.Error("获取文件失败", zap.Error(err))
			httpx.Error(w, err)
			return
		}
		defer file.Close()

		// 获取上传类型和自定义路径
		uploadTypeStr := r.FormValue("type")
		customPath := r.FormValue("path")

		// 确定上传类型
		var uploadType upload.UploadType
		switch uploadTypeStr {
		case "image":
			uploadType = upload.UploadTypeImage
		case "document":
			uploadType = upload.UploadTypeDocument
		default:
			uploadType = upload.UploadTypeGeneral
		}

		// 使用七牛云上传组件
		result, err := upload.DefaultService.UploadFile(r.Context(), header, uploadType, customPath)
		if err != nil {
			logx.Error("上传文件失败", zap.Error(err), zap.String("filename", header.Filename))
			httpx.Error(w, fmt.Errorf("上传文件失败: %w", err))
			return
		}

		// 返回文件信息
		httpx.OkJson(w, map[string]interface{}{
			"url":      result.URL,
			"filename": result.Filename,
			"size":     result.Size,
			"key":      result.Key,
			"mime":     result.MimeType,
		})
	}
}

// UploadImageHandler 图片上传处理器
func UploadImageHandler(ctx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单
		err := r.ParseMultipartForm(10 << 20) // 限制上传文件大小为10MB
		if err != nil {
			logx.Error("解析表单失败", zap.Error(err))
			httpx.Error(w, err)
			return
		}

		// 获取文件
		file, header, err := r.FormFile("file")
		if err != nil {
			logx.Error("获取文件失败", zap.Error(err))
			httpx.Error(w, err)
			return
		}
		defer file.Close()

		// 获取自定义路径
		customPath := r.FormValue("path")

		// 使用七牛云上传组件上传图片
		result, err := upload.DefaultService.UploadImage(r.Context(), header, customPath)
		if err != nil {
			logx.Error("上传图片失败", zap.Error(err), zap.String("filename", header.Filename))
			httpx.Error(w, fmt.Errorf("上传图片失败: %w", err))
			return
		}

		// 返回图片信息
		httpx.OkJson(w, map[string]interface{}{
			"url":      result.URL,
			"filename": result.Filename,
			"size":     result.Size,
			"key":      result.Key,
			"mime":     result.MimeType,
		})
	}
}

// UploadDocumentHandler 文档上传处理器
func UploadDocumentHandler(ctx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单
		err := r.ParseMultipartForm(10 << 20) // 限制上传文件大小为10MB
		if err != nil {
			logx.Error("解析表单失败", zap.Error(err))
			httpx.Error(w, err)
			return
		}

		// 获取文件
		file, header, err := r.FormFile("file")
		if err != nil {
			logx.Error("获取文件失败", zap.Error(err))
			httpx.Error(w, err)
			return
		}
		defer file.Close()

		// 获取自定义路径
		customPath := r.FormValue("path")

		// 使用七牛云上传组件上传文档
		result, err := upload.DefaultService.UploadDocument(r.Context(), header, customPath)
		if err != nil {
			logx.Error("上传文档失败", zap.Error(err), zap.String("filename", header.Filename))
			httpx.Error(w, fmt.Errorf("上传文档失败: %w", err))
			return
		}

		// 返回文档信息
		httpx.OkJson(w, map[string]interface{}{
			"url":      result.URL,
			"filename": result.Filename,
			"size":     result.Size,
			"key":      result.Key,
			"mime":     result.MimeType,
		})
	}
}
