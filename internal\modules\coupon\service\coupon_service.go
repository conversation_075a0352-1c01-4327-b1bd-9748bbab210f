package service

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"yekaitai/internal/modules/coupon/model"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// 初始化随机数种子
func init() {
	rand.Seed(time.Now().UnixNano())
}

// CouponService 优惠券服务
type CouponService struct {
	db       *gorm.DB
	repo     model.CouponRepository
	taskRepo model.CouponIssueTaskRepository
}

// NewCouponService 创建优惠券服务实例
func NewCouponService() *CouponService {
	db := mysql.GetDB()
	return &CouponService{
		db:       db,
		repo:     model.NewCouponRepository(db),
		taskRepo: model.NewCouponIssueTaskRepository(db),
	}
}

// CreateCouponRequest 创建优惠券请求
type CreateCouponRequest struct {
	Name        string  `json:"name" validate:"required,max=30" label:"优惠券名称"`
	Type        int     `json:"type" validate:"required,oneof=1 2 3" label:"优惠券类型"`
	Description string  `json:"description,optional" label:"使用说明"`
	Amount      float64 `json:"amount,optional" validate:"min=0" label:"优惠金额"`
	Discount    float64 `json:"discount,optional" validate:"min=0,max=10" label:"折扣比例"`
	MinAmount   float64 `json:"min_amount,optional" validate:"min=0" label:"最低使用金额"`

	TotalQuantity int `json:"total_quantity" validate:"required,min=1,max=10000" label:"发放数量"`

	Scope int `json:"scope" validate:"required,oneof=1 2 3 4 5" label:"可用范围"`

	ValidityType int        `json:"validity_type" validate:"required,oneof=1 2 3" label:"有效期类型"`
	ValidDays    int        `json:"valid_days,optional" validate:"min=0,max=9999" label:"有效天数"`
	ValidFrom    *time.Time `json:"valid_from,optional" label:"生效开始时间"`
	ValidUntil   *time.Time `json:"valid_until,optional" label:"生效结束时间"`

	UsageRestriction int `json:"usage_restriction" validate:"required,oneof=1 2" label:"使用限制"`

	UserLevel    int    `json:"user_level" validate:"required,oneof=1 2" label:"用户可见范围"`
	UserLevelIDs []uint `json:"user_level_ids,optional" label:"可见用户等级ID"`

	// 适用商品/服务
	ProductIDs []uint `json:"product_ids,optional" label:"适用商品"`
	ServiceIDs []uint `json:"service_ids,optional" label:"适用服务"`
}

// UpdateCouponRequest 更新优惠券请求
type UpdateCouponRequest struct {
	ID          uint    `json:"id" validate:"required" label:"优惠券ID"`
	Name        string  `json:"name" validate:"required,max=30" label:"优惠券名称"`
	Type        int     `json:"type" validate:"required,oneof=1 2 3" label:"优惠券类型"`
	Description string  `json:"description,optional" label:"使用说明"`
	Amount      float64 `json:"amount,optional" validate:"min=0" label:"优惠金额"`
	Discount    float64 `json:"discount,optional" validate:"min=0,max=10" label:"折扣比例"`
	MinAmount   float64 `json:"min_amount,optional" validate:"min=0" label:"最低使用金额"`

	TotalQuantity int `json:"total_quantity" validate:"required,min=1,max=10000" label:"发放数量"`

	Scope int `json:"scope" validate:"required,oneof=1 2 3 4 5" label:"可用范围"`

	ValidityType int        `json:"validity_type" validate:"required,oneof=1 2 3" label:"有效期类型"`
	ValidDays    int        `json:"valid_days,optional" validate:"min=0,max=9999" label:"有效天数"`
	ValidFrom    *time.Time `json:"valid_from,optional" label:"生效开始时间"`
	ValidUntil   *time.Time `json:"valid_until,optional" label:"生效结束时间"`

	UsageRestriction int `json:"usage_restriction" validate:"required,oneof=1 2" label:"使用限制"`

	UserLevel    int    `json:"user_level" validate:"required,oneof=1 2" label:"用户可见范围"`
	UserLevelIDs []uint `json:"user_level_ids,optional" label:"可见用户等级ID"`

	// 适用商品/服务
	ProductIDs []uint `json:"product_ids,optional" label:"适用商品"`
	ServiceIDs []uint `json:"service_ids,optional" label:"适用服务"`
}

// IssueCouponsRequest 批量发放优惠券请求
type IssueCouponsRequest struct {
	CouponID    uint   `json:"coupon_id"`    // 优惠券ID
	UserLevels  []int  `json:"user_levels"`  // 用户等级列表
	IssueCount  int    `json:"issue_count"`  // 发放数量
	IssueReason string `json:"issue_reason"` // 发放原因
}

// IssueCouponsResponse 批量发放优惠券响应
type IssueCouponsResponse struct {
	IssuedCount int    `json:"issued_count"` // 成功发放数量
	TargetCount int    `json:"target_count"` // 目标用户数量
	FailedCount int    `json:"failed_count"` // 发放失败数量
	CouponCode  string `json:"coupon_code"`  // 优惠券编码
	CouponName  string `json:"coupon_name"`  // 优惠券名称
}

// CreateIssueTaskRequest 创建发放任务请求
type CreateIssueTaskRequest struct {
	CouponID    uint   `json:"coupon_id" validate:"required"`         // 优惠券ID
	UserLevels  []int  `json:"user_levels" validate:"required"`       // 用户等级列表
	IssueCount  int    `json:"issue_count" validate:"required,min=1"` // 发放数量
	IssueReason string `json:"issue_reason"`                          // 发放原因
	TaskName    string `json:"task_name"`                             // 任务名称
}

// CreateIssueTaskResponse 创建发放任务响应
type CreateIssueTaskResponse struct {
	TaskID     uint   `json:"task_id"`     // 任务ID
	TaskName   string `json:"task_name"`   // 任务名称
	CouponCode string `json:"coupon_code"` // 优惠券编码
	CouponName string `json:"coupon_name"` // 优惠券名称
	Message    string `json:"message"`     // 提示信息
}

// IssueTaskListRequest 发放任务列表请求
type IssueTaskListRequest struct {
	types.PageRequest
	Status *int `form:"status,optional" validate:"omitempty,oneof=0 1 2 3"` // 任务状态
}

// IssueTaskResponse 发放任务响应
type IssueTaskResponse struct {
	*model.CouponIssueTask
	StatusText  string `json:"status_text"`  // 状态文本
	CouponName  string `json:"coupon_name"`  // 优惠券名称
	CouponCode  string `json:"coupon_code"`  // 优惠券编码
	CreatorName string `json:"creator_name"` // 创建人姓名
}

// CouponListRequest 优惠券列表请求
type CouponListRequest struct {
	types.PageRequest
	CouponCode string     `form:"coupon_code,optional" label:"优惠券编码"`
	Name       string     `form:"name,optional" label:"优惠券名称"`
	Type       *int       `form:"type,optional" validate:"omitempty,oneof=1 2 3" label:"优惠券类型"`
	Status     *int       `form:"status,optional" validate:"omitempty,oneof=0 1 2" label:"优惠券状态"`
	ValidFrom  *time.Time `form:"valid_from,optional" label:"有效期开始"`
	ValidUntil *time.Time `form:"valid_until,optional" label:"有效期结束"`
}

// CouponResponse 优惠券响应
type CouponResponse struct {
	*model.Coupon
	TypeText             string `json:"type_text"`              // 类型文本
	StatusText           string `json:"status_text"`            // 状态文本
	ScopeText            string `json:"scope_text"`             // 范围文本
	ValidityTypeText     string `json:"validity_type_text"`     // 有效期类型文本
	UsageRestrictionText string `json:"usage_restriction_text"` // 使用限制文本
	UserLevelText        string `json:"user_level_text"`        // 用户等级文本

	// 统计信息
	UsedQuantity    int `json:"used_quantity"`    // 已使用数量
	ExpiredQuantity int `json:"expired_quantity"` // 已过期数量

	// 关联信息
	ProductIDs   []uint `json:"product_ids,omitempty"`    // 适用商品ID
	ServiceIDs   []uint `json:"service_ids,omitempty"`    // 适用服务ID
	UserLevelIDs []uint `json:"user_level_ids,omitempty"` // 可见用户等级ID
}

// CreateCoupon 创建优惠券
func (s *CouponService) CreateCoupon(ctx context.Context, req *CreateCouponRequest) (*CouponResponse, error) {
	// 验证请求参数
	if err := s.validateCreateRequest(req); err != nil {
		return nil, err
	}

	// 生成优惠券编码
	couponCode, err := s.generateCouponCode()
	if err != nil {
		return nil, fmt.Errorf("生成优惠券编码失败: %w", err)
	}

	// 创建优惠券对象
	coupon := &model.Coupon{
		CouponCode:  couponCode,
		Name:        req.Name,
		Type:        model.CouponType(req.Type),
		Description: req.Description,

		Amount:    req.Amount,
		Discount:  req.Discount,
		MinAmount: req.MinAmount,

		TotalQuantity:    req.TotalQuantity,
		ReceivedQuantity: 0,

		Scope: model.CouponScope(req.Scope),

		ValidityType: model.ValidityType(req.ValidityType),
		ValidDays:    req.ValidDays,
		ValidFrom:    req.ValidFrom,
		ValidUntil:   req.ValidUntil,

		UsageRestriction: model.CouponUsageRestriction(req.UsageRestriction),

		UserLevel: model.UserLevel(req.UserLevel),

		Status: model.CouponStatusActive,
	}

	// 创建优惠券
	if err := s.repo.Create(coupon); err != nil {
		return nil, fmt.Errorf("创建优惠券失败: %w", err)
	}

	// 设置适用商品
	if req.Scope == int(model.CouponScopeGoods) && len(req.ProductIDs) > 0 {
		if err := s.repo.SetApplicableProducts(coupon.ID, req.ProductIDs); err != nil {
			return nil, fmt.Errorf("设置适用商品失败: %w", err)
		}
	}

	// 设置适用服务
	if req.Scope == int(model.CouponScopeServices) && len(req.ServiceIDs) > 0 {
		if err := s.repo.SetApplicableServices(coupon.ID, req.ServiceIDs); err != nil {
			return nil, fmt.Errorf("设置适用服务失败: %w", err)
		}
	}

	// 设置可见用户等级
	if req.UserLevel == int(model.UserLevelPart) && len(req.UserLevelIDs) > 0 {
		if err := s.repo.SetVisibleUserLevels(coupon.ID, req.UserLevelIDs); err != nil {
			return nil, fmt.Errorf("设置可见用户等级失败: %w", err)
		}
	}

	return s.buildCouponResponse(coupon)
}

// UpdateCoupon 更新优惠券
func (s *CouponService) UpdateCoupon(ctx context.Context, req *UpdateCouponRequest) (*CouponResponse, error) {
	// 验证请求参数
	if err := s.validateUpdateRequest(req); err != nil {
		return nil, err
	}

	// 查找现有优惠券
	existingCoupon, err := s.repo.FindByID(req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("优惠券不存在")
		}
		return nil, fmt.Errorf("查找优惠券失败: %w", err)
	}

	// 检查是否可以编辑
	if !s.canEditCoupon(existingCoupon) {
		return nil, fmt.Errorf("优惠券不可编辑")
	}

	// 更新优惠券信息（不更新CouponCode，编码不可修改）
	existingCoupon.Name = req.Name
	existingCoupon.Type = model.CouponType(req.Type)
	existingCoupon.Description = req.Description
	existingCoupon.Amount = req.Amount
	existingCoupon.Discount = req.Discount
	existingCoupon.MinAmount = req.MinAmount

	existingCoupon.TotalQuantity = req.TotalQuantity

	existingCoupon.Scope = model.CouponScope(req.Scope)

	existingCoupon.ValidityType = model.ValidityType(req.ValidityType)
	existingCoupon.ValidDays = req.ValidDays
	existingCoupon.ValidFrom = req.ValidFrom
	existingCoupon.ValidUntil = req.ValidUntil

	existingCoupon.UsageRestriction = model.CouponUsageRestriction(req.UsageRestriction)

	existingCoupon.UserLevel = model.UserLevel(req.UserLevel)

	if err := s.repo.Update(existingCoupon); err != nil {
		return nil, fmt.Errorf("更新优惠券失败: %w", err)
	}

	// 更新适用商品
	if req.Scope == int(model.CouponScopeGoods) {
		if err := s.repo.SetApplicableProducts(existingCoupon.ID, req.ProductIDs); err != nil {
			return nil, fmt.Errorf("更新适用商品失败: %w", err)
		}
	} else {
		// 清除适用商品
		s.repo.ClearApplicableProducts(existingCoupon.ID)
	}

	// 更新适用服务
	if req.Scope == int(model.CouponScopeServices) {
		if err := s.repo.SetApplicableServices(existingCoupon.ID, req.ServiceIDs); err != nil {
			return nil, fmt.Errorf("更新适用服务失败: %w", err)
		}
	} else {
		// 清除适用服务
		s.repo.ClearApplicableServices(existingCoupon.ID)
	}

	// 更新可见用户等级
	if req.UserLevel == int(model.UserLevelPart) {
		if err := s.repo.SetVisibleUserLevels(existingCoupon.ID, req.UserLevelIDs); err != nil {
			return nil, fmt.Errorf("更新可见用户等级失败: %w", err)
		}
	} else {
		// 清除可见用户等级
		s.repo.ClearVisibleUserLevels(existingCoupon.ID)
	}

	return s.buildCouponResponse(existingCoupon)
}

// GetCouponList 获取优惠券列表
func (s *CouponService) GetCouponList(ctx context.Context, req *CouponListRequest) ([]*CouponResponse, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	var couponType *model.CouponType
	if req.Type != nil {
		t := model.CouponType(*req.Type)
		couponType = &t
	}

	var status *model.CouponStatus
	if req.Status != nil {
		s := model.CouponStatus(*req.Status)
		status = &s
	}

	coupons, total, err := s.repo.List(req.Page, req.Size, req.CouponCode, req.Name, couponType, status, req.ValidFrom, req.ValidUntil)
	if err != nil {
		return nil, 0, fmt.Errorf("获取优惠券列表失败: %w", err)
	}

	var responses []*CouponResponse
	for _, coupon := range coupons {
		response, err := s.buildCouponResponse(coupon)
		if err != nil {
			continue // 跳过构建失败的项
		}
		responses = append(responses, response)
	}

	return responses, total, nil
}

// GetCouponDetail 获取优惠券详情
func (s *CouponService) GetCouponDetail(ctx context.Context, id uint) (*CouponResponse, error) {
	coupon, err := s.repo.FindByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("优惠券不存在")
		}
		return nil, fmt.Errorf("获取优惠券详情失败: %w", err)
	}

	return s.buildCouponResponse(coupon)
}

// DeleteCoupon 删除优惠券
func (s *CouponService) DeleteCoupon(ctx context.Context, id uint) error {
	// 检查优惠券是否存在
	coupon, err := s.repo.FindByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("优惠券不存在")
		}
		return fmt.Errorf("查找优惠券失败: %w", err)
	}

	// 检查是否可以删除
	if !s.canDeleteCoupon(coupon) {
		return fmt.Errorf("优惠券不可删除，已有用户领取")
	}

	if err := s.repo.Delete(id); err != nil {
		return fmt.Errorf("删除优惠券失败: %w", err)
	}

	return nil
}

// UpdateCouponStatus 更新优惠券状态
func (s *CouponService) UpdateCouponStatus(ctx context.Context, id uint, status int) error {
	// 检查优惠券是否存在
	_, err := s.repo.FindByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("优惠券不存在")
		}
		return fmt.Errorf("查找优惠券失败: %w", err)
	}

	couponStatus := model.CouponStatus(status)
	if err := s.repo.UpdateStatus(id, couponStatus); err != nil {
		return fmt.Errorf("更新优惠券状态失败: %w", err)
	}

	return nil
}

// GetProductList 获取商品列表（用于选择）
func (s *CouponService) GetProductList(ctx context.Context, name string, page, size int) (interface{}, error) {
	// TODO: 调用商品服务获取商品列表
	// 这里返回模拟数据，实际应该调用商品模块的服务
	return map[string]interface{}{
		"list": []map[string]interface{}{
			{"id": 1, "name": "丽水安瞿片", "category": "中药材", "price": 32.00},
			{"id": 2, "name": "头孢混悬液", "category": "西药", "price": 34.00},
		},
		"total": 2,
	}, nil
}

// GetServiceList 获取服务列表（用于选择）
func (s *CouponService) GetServiceList(ctx context.Context, name string, page, size int) (interface{}, error) {
	// TODO: 调用服务设置模块获取服务列表
	// 这里返回模拟数据，实际应该调用服务模块的服务
	return map[string]interface{}{
		"list": []map[string]interface{}{
			{"id": 1, "name": "中医针灸", "price": 30.00, "valid_from": "2023-12-12", "valid_until": "2024-12-12"},
		},
		"total": 1,
	}, nil
}

// IssueCoupons 批量发放优惠券
func (s *CouponService) IssueCoupons(ctx context.Context, req *IssueCouponsRequest) (*IssueCouponsResponse, error) {
	// 获取优惠券信息
	coupon, err := s.repo.FindByID(req.CouponID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("优惠券不存在")
		}
		return nil, fmt.Errorf("获取优惠券失败: %w", err)
	}

	// 检查优惠券状态
	if coupon.Status != model.CouponStatusActive {
		return nil, fmt.Errorf("优惠券状态不可用")
	}

	// 检查剩余数量
	remainingQuantity := coupon.TotalQuantity - coupon.ReceivedQuantity
	if remainingQuantity <= 0 {
		return nil, fmt.Errorf("优惠券已发放完毕")
	}

	// 计算实际发放数量限制
	maxIssueCount := remainingQuantity
	if req.IssueCount > 0 && req.IssueCount < remainingQuantity {
		maxIssueCount = req.IssueCount
	}

	// 限制单次最大发放数量，避免超时
	if maxIssueCount > 1000 {
		maxIssueCount = 1000
	}

	// 根据用户等级查询符合条件的用户 - 优化查询性能
	var targetUsers []uint

	// 使用更高效的查询方式
	query := s.db.Table("wx_user u").
		Select("u.user_id").
		Joins("JOIN user_level_rules ur ON u.user_level_id = ur.id").
		Where("u.deleted_at IS NULL AND ur.deleted_at IS NULL")

	if len(req.UserLevels) > 0 {
		query = query.Where("ur.level_order IN ?", req.UserLevels)
	}

	// 使用NOT EXISTS替代NOT IN，性能更好
	query = query.Where("NOT EXISTS (SELECT 1 FROM user_coupons uc WHERE uc.user_id = u.user_id AND uc.coupon_id = ? AND uc.deleted_at IS NULL)", req.CouponID)

	// 限制查询数量
	query = query.Limit(maxIssueCount)

	if err := query.Pluck("user_id", &targetUsers).Error; err != nil {
		return nil, fmt.Errorf("查询目标用户失败: %w", err)
	}

	targetCount := len(targetUsers)
	if targetCount == 0 {
		return &IssueCouponsResponse{
			IssuedCount: 0,
			TargetCount: 0,
			FailedCount: 0,
			CouponCode:  coupon.CouponCode,
			CouponName:  coupon.Name,
		}, nil
	}

	// 预计算有效期
	var validFrom, validUntil time.Time
	now := time.Now()

	switch coupon.ValidityType {
	case model.ValidityTypePermanent:
		// 永久有效
		validFrom = now
		validUntil = time.Date(2099, 12, 31, 23, 59, 59, 0, time.Local)
	case model.ValidityTypeDays:
		// 天数有效
		validFrom = now
		validUntil = now.AddDate(0, 0, coupon.ValidDays)
	case model.ValidityTypeDate:
		// 固定日期
		if coupon.ValidFrom != nil {
			validFrom = *coupon.ValidFrom
		} else {
			validFrom = now
		}
		if coupon.ValidUntil != nil {
			validUntil = *coupon.ValidUntil
		} else {
			validUntil = time.Date(2099, 12, 31, 23, 59, 59, 0, time.Local)
		}
	default:
		validFrom = now
		validUntil = time.Date(2099, 12, 31, 23, 59, 59, 0, time.Local)
	}

	// 批量创建用户优惠券记录
	var userCoupons []model.UserCoupon
	for _, userID := range targetUsers {
		userCoupons = append(userCoupons, model.UserCoupon{
			UserID:     userID,
			CouponID:   req.CouponID,
			Status:     model.UserCouponStatusUnused,
			ValidFrom:  validFrom,
			ValidUntil: validUntil,
		})
	}

	// 开启事务进行批量操作
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 批量插入用户优惠券记录（分批处理避免单次插入过多）
	batchSize := 100
	issuedCount := 0

	for i := 0; i < len(userCoupons); i += batchSize {
		end := i + batchSize
		if end > len(userCoupons) {
			end = len(userCoupons)
		}

		batch := userCoupons[i:end]
		if err := tx.CreateInBatches(batch, batchSize).Error; err != nil {
			logx.Errorf("批量创建用户优惠券失败: %v", err)
			tx.Rollback()
			return nil, fmt.Errorf("批量发放优惠券失败: %w", err)
		}

		issuedCount += len(batch)
	}

	// 更新优惠券已领取数量
	if err := tx.Model(&model.Coupon{}).Where("id = ?", req.CouponID).
		Update("received_quantity", gorm.Expr("received_quantity + ?", issuedCount)).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新优惠券领取数量失败: %w", err)
	}

	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	return &IssueCouponsResponse{
		IssuedCount: issuedCount,
		TargetCount: targetCount,
		FailedCount: 0, // 批量操作成功则失败数为0
		CouponCode:  coupon.CouponCode,
		CouponName:  coupon.Name,
	}, nil
}

// CreateIssueTask 创建发放任务（异步）
func (s *CouponService) CreateIssueTask(ctx context.Context, req *CreateIssueTaskRequest, creatorID uint) (*CreateIssueTaskResponse, error) {
	// 获取优惠券信息
	coupon, err := s.repo.FindByID(req.CouponID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("优惠券不存在")
		}
		return nil, fmt.Errorf("获取优惠券失败: %w", err)
	}

	// 检查优惠券状态
	if coupon.Status != model.CouponStatusActive {
		return nil, fmt.Errorf("优惠券状态不可用")
	}

	// 检查剩余数量
	remainingQuantity := coupon.TotalQuantity - coupon.ReceivedQuantity
	if remainingQuantity <= 0 {
		return nil, fmt.Errorf("优惠券已发放完毕")
	}

	// 生成任务名称
	taskName := req.TaskName
	if taskName == "" {
		taskName = fmt.Sprintf("批量发放优惠券 - %s", coupon.Name)
	}

	// 创建任务
	task := &model.CouponIssueTask{
		CouponID:    req.CouponID,
		TaskName:    taskName,
		IssueCount:  req.IssueCount,
		IssueReason: req.IssueReason,
		Status:      model.TaskStatusPending,
		CreatorID:   creatorID,
	}

	// 设置用户等级
	if err := task.SetUserLevelsSlice(req.UserLevels); err != nil {
		return nil, fmt.Errorf("设置用户等级失败: %w", err)
	}

	// 保存任务
	if err := s.taskRepo.Create(task); err != nil {
		return nil, fmt.Errorf("创建发放任务失败: %w", err)
	}

	return &CreateIssueTaskResponse{
		TaskID:     task.ID,
		TaskName:   task.TaskName,
		CouponCode: coupon.CouponCode,
		CouponName: coupon.Name,
		Message:    "发放任务已创建，正在异步处理中",
	}, nil
}

// GetIssueTaskList 获取发放任务列表
func (s *CouponService) GetIssueTaskList(ctx context.Context, req *IssueTaskListRequest) ([]*IssueTaskResponse, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	var status *model.CouponIssueTaskStatus
	if req.Status != nil {
		s := model.CouponIssueTaskStatus(*req.Status)
		status = &s
	}

	tasks, total, err := s.taskRepo.List(req.Page, req.Size, status)
	if err != nil {
		return nil, 0, fmt.Errorf("获取任务列表失败: %w", err)
	}

	var responses []*IssueTaskResponse
	for _, task := range tasks {
		response, err := s.buildIssueTaskResponse(task)
		if err != nil {
			continue // 跳过构建失败的项
		}
		responses = append(responses, response)
	}

	return responses, total, nil
}

// GetIssueTaskDetail 获取发放任务详情
func (s *CouponService) GetIssueTaskDetail(ctx context.Context, id uint) (*IssueTaskResponse, error) {
	task, err := s.taskRepo.FindByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("任务不存在")
		}
		return nil, fmt.Errorf("获取任务详情失败: %w", err)
	}

	return s.buildIssueTaskResponse(task)
}

// ProcessIssueTask 处理发放任务（由定时任务调用）
func (s *CouponService) ProcessIssueTask(ctx context.Context, task *model.CouponIssueTask) error {
	// 更新任务状态为处理中
	if err := s.taskRepo.UpdateStatus(task.ID, model.TaskStatusProcessing, ""); err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	// 获取用户等级列表
	userLevels, err := task.GetUserLevelsSlice()
	if err != nil {
		s.taskRepo.UpdateStatus(task.ID, model.TaskStatusFailed, fmt.Sprintf("解析用户等级失败: %v", err))
		return fmt.Errorf("解析用户等级失败: %w", err)
	}

	// 构建发放请求
	req := &IssueCouponsRequest{
		CouponID:    task.CouponID,
		UserLevels:  userLevels,
		IssueCount:  task.IssueCount,
		IssueReason: task.IssueReason,
	}

	// 执行发放
	result, err := s.IssueCoupons(ctx, req)
	if err != nil {
		s.taskRepo.UpdateStatus(task.ID, model.TaskStatusFailed, err.Error())
		return fmt.Errorf("执行发放失败: %w", err)
	}

	// 完成任务
	if err := s.taskRepo.CompleteTask(task.ID, result.IssuedCount, result.FailedCount); err != nil {
		return fmt.Errorf("完成任务失败: %w", err)
	}

	return nil
}

// ExecuteIssueTask 手动执行发放任务
func (s *CouponService) ExecuteIssueTask(ctx context.Context, taskID uint, executorID uint) error {
	// 查找任务
	task, err := s.taskRepo.FindByID(taskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("发放任务不存在")
		}
		return fmt.Errorf("获取发放任务失败: %w", err)
	}

	// 检查任务状态，只有待处理和失败的任务可以手动执行
	if task.Status != model.TaskStatusPending && task.Status != model.TaskStatusFailed {
		return fmt.Errorf("任务状态不允许执行，当前状态: %s", s.getTaskStatusText(task.Status))
	}

	// 重置任务状态为待处理（以防是失败状态的重新执行）
	if err := s.taskRepo.UpdateStatus(taskID, model.TaskStatusPending, ""); err != nil {
		return fmt.Errorf("重置任务状态失败: %w", err)
	}

	// 记录手动执行信息
	logx.Infof("手动执行发放任务: TaskID=%d, ExecutorID=%d, TaskName=%s", taskID, executorID, task.TaskName)

	// 立即执行任务
	if err := s.ProcessIssueTask(ctx, task); err != nil {
		logx.Errorf("手动执行发放任务失败: %v", err)
		return fmt.Errorf("执行发放任务失败: %w", err)
	}

	logx.Infof("手动执行发放任务成功: TaskID=%d", taskID)
	return nil
}

// buildIssueTaskResponse 构建任务响应
func (s *CouponService) buildIssueTaskResponse(task *model.CouponIssueTask) (*IssueTaskResponse, error) {
	response := &IssueTaskResponse{
		CouponIssueTask: task,
		StatusText:      s.getTaskStatusText(task.Status),
	}

	// 获取优惠券信息
	coupon, err := s.repo.FindByID(task.CouponID)
	if err == nil {
		response.CouponName = coupon.Name
		response.CouponCode = coupon.CouponCode
	}

	// TODO: 获取创建人姓名（需要调用用户服务）
	response.CreatorName = fmt.Sprintf("用户%d", task.CreatorID)

	return response, nil
}

// getTaskStatusText 获取任务状态文本
func (s *CouponService) getTaskStatusText(status model.CouponIssueTaskStatus) string {
	switch status {
	case model.TaskStatusPending:
		return "待处理"
	case model.TaskStatusProcessing:
		return "处理中"
	case model.TaskStatusCompleted:
		return "已完成"
	case model.TaskStatusFailed:
		return "已失败"
	default:
		return "未知状态"
	}
}

// validateCreateRequest 验证创建请求
func (s *CouponService) validateCreateRequest(req *CreateCouponRequest) error {
	// 编码现在是自动生成的，不需要检查唯一性

	// 类型相关验证
	switch req.Type {
	case int(model.CouponTypeDiscount):
		if req.Discount <= 0 || req.Discount >= 10 {
			return fmt.Errorf("折扣券折扣比例必须大于0小于10")
		}
	case int(model.CouponTypeCash), int(model.CouponTypeFixed):
		if req.Amount <= 0 {
			return fmt.Errorf("满减券/立减券优惠金额必须大于0")
		}
	}

	// 有效期验证
	switch req.ValidityType {
	case int(model.ValidityTypeDays):
		if req.ValidDays <= 0 || req.ValidDays > 9999 {
			return fmt.Errorf("有效天数必须在1-9999之间")
		}
	case int(model.ValidityTypeDate):
		if req.ValidFrom == nil || req.ValidUntil == nil {
			return fmt.Errorf("固定日期类型必须设置开始和结束时间")
		}
		if req.ValidUntil.Before(*req.ValidFrom) {
			return fmt.Errorf("结束时间必须晚于开始时间")
		}
		if req.ValidFrom.Before(time.Now()) {
			return fmt.Errorf("开始时间不能早于当前时间")
		}
	}

	// 适用范围验证
	switch req.Scope {
	case int(model.CouponScopeGoods):
		if len(req.ProductIDs) == 0 {
			return fmt.Errorf("指定商品范围必须选择商品")
		}
	case int(model.CouponScopeServices):
		if len(req.ServiceIDs) == 0 {
			return fmt.Errorf("指定服务范围必须选择服务")
		}
	}

	// 用户可见范围验证
	switch req.UserLevel {
	case int(model.UserLevelPart):
		if len(req.UserLevelIDs) == 0 {
			return fmt.Errorf("部分等级可见范围必须选择用户等级")
		}
	}

	return nil
}

// validateUpdateRequest 验证更新请求
func (s *CouponService) validateUpdateRequest(req *UpdateCouponRequest) error {
	// 构造一个CreateCouponRequest进行验证
	createReq := &CreateCouponRequest{
		Name:             req.Name,
		Type:             req.Type,
		Description:      req.Description,
		Amount:           req.Amount,
		Discount:         req.Discount,
		MinAmount:        req.MinAmount,
		TotalQuantity:    req.TotalQuantity,
		Scope:            req.Scope,
		ValidityType:     req.ValidityType,
		ValidDays:        req.ValidDays,
		ValidFrom:        req.ValidFrom,
		ValidUntil:       req.ValidUntil,
		UsageRestriction: req.UsageRestriction,
		UserLevel:        req.UserLevel,
		UserLevelIDs:     req.UserLevelIDs,
		ProductIDs:       req.ProductIDs,
		ServiceIDs:       req.ServiceIDs,
	}

	// 使用创建验证逻辑
	return s.validateCreateRequest(createReq)
}

// canEditCoupon 检查优惠券是否可以编辑
func (s *CouponService) canEditCoupon(coupon *model.Coupon) bool {
	// 禁用状态可以编辑
	if coupon.Status == model.CouponStatusDisabled {
		return true
	}

	// 已过期不可编辑
	if coupon.Status == model.CouponStatusExpired {
		return false
	}

	// 已领取完不可编辑数量等核心字段，但说明等可以编辑
	return true
}

// canDeleteCoupon 检查优惠券是否可以删除
func (s *CouponService) canDeleteCoupon(coupon *model.Coupon) bool {
	// 已有用户领取则不可删除
	return coupon.ReceivedQuantity == 0
}

// buildCouponResponse 构建优惠券响应
func (s *CouponService) buildCouponResponse(coupon *model.Coupon) (*CouponResponse, error) {
	response := &CouponResponse{
		Coupon:               coupon,
		TypeText:             s.getCouponTypeText(coupon.Type),
		StatusText:           s.getCouponStatusText(coupon.Status),
		ScopeText:            s.getCouponScopeText(coupon.Scope),
		ValidityTypeText:     s.getValidityTypeText(coupon.ValidityType),
		UsageRestrictionText: s.getUsageRestrictionText(coupon.UsageRestriction),
		UserLevelText:        s.getUserLevelText(coupon.UserLevel),
	}

	// 获取统计信息
	stats, err := s.repo.GetCouponStats(coupon.ID)
	if err == nil {
		response.UsedQuantity = stats.UsedQuantity
		response.ExpiredQuantity = stats.ExpiredQuantity
	}

	// 获取适用商品
	if coupon.Scope == model.CouponScopeGoods {
		productIDs, err := s.repo.GetApplicableProducts(coupon.ID)
		if err == nil {
			response.ProductIDs = productIDs
		}
	}

	// 获取适用服务
	if coupon.Scope == model.CouponScopeServices {
		serviceIDs, err := s.repo.GetApplicableServices(coupon.ID)
		if err == nil {
			response.ServiceIDs = serviceIDs
		}
	}

	// 获取可见用户等级
	if coupon.UserLevel == model.UserLevelPart {
		userLevelIDs, err := s.repo.GetVisibleUserLevels(coupon.ID)
		if err == nil {
			response.UserLevelIDs = userLevelIDs
		}
	}

	return response, nil
}

// getCouponTypeText 获取优惠券类型文本
func (s *CouponService) getCouponTypeText(couponType model.CouponType) string {
	switch couponType {
	case model.CouponTypeDiscount:
		return "折扣券"
	case model.CouponTypeCash:
		return "满减券"
	case model.CouponTypeFixed:
		return "立减券"
	default:
		return "未知"
	}
}

// getCouponStatusText 获取优惠券状态文本
func (s *CouponService) getCouponStatusText(status model.CouponStatus) string {
	switch status {
	case model.CouponStatusDisabled:
		return "禁用"
	case model.CouponStatusActive:
		return "生效中"
	case model.CouponStatusExpired:
		return "已过期"
	default:
		return "未知"
	}
}

// getCouponScopeText 获取优惠券适用范围文本
func (s *CouponService) getCouponScopeText(scope model.CouponScope) string {
	switch scope {
	case model.CouponScopeAll:
		return "全站"
	case model.CouponScopeServices:
		return "指定服务"
	case model.CouponScopeGoods:
		return "指定商品"
	case model.CouponScopeAllServices:
		return "全部服务"
	case model.CouponScopeAllGoods:
		return "全部商品"
	default:
		return "未知"
	}
}

// getValidityTypeText 获取有效期类型文本
func (s *CouponService) getValidityTypeText(validityType model.ValidityType) string {
	switch validityType {
	case model.ValidityTypePermanent:
		return "永久有效"
	case model.ValidityTypeDays:
		return "天数有效"
	case model.ValidityTypeDate:
		return "固定日期"
	default:
		return "未知"
	}
}

// getUsageRestrictionText 获取使用限制文本
func (s *CouponService) getUsageRestrictionText(usageRestriction model.CouponUsageRestriction) string {
	switch usageRestriction {
	case model.CouponUsageRestrictionMutualExclusive:
		return "互斥"
	case model.CouponUsageRestrictionStackable:
		return "同享"
	default:
		return "未知"
	}
}

// getUserLevelText 获取用户等级文本
func (s *CouponService) getUserLevelText(userLevel model.UserLevel) string {
	switch userLevel {
	case model.UserLevelAll:
		return "全部"
	case model.UserLevelPart:
		return "部分等级"
	default:
		return "未知"
	}
}

// generateCouponCode 生成唯一的全数字优惠券编码
func (s *CouponService) generateCouponCode() (string, error) {
	// 使用时间戳(10位) + 随机数(6位) 生成16位全数字编码
	timestamp := time.Now().Unix() // 10位时间戳
	random := rand.Intn(999999)    // 0-999999的随机数

	couponCode := fmt.Sprintf("%d%06d", timestamp, random)

	// 检查编码是否已存在，如果存在则重新生成
	_, err := s.repo.FindByCouponCode(couponCode)
	if err == nil {
		// 编码已存在，递归重新生成
		return s.generateCouponCode()
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", fmt.Errorf("检查优惠券编码失败: %w", err)
	}

	return couponCode, nil
}
