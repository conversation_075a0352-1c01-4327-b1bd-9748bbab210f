package routes

import (
	"net/http"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/user/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterAuthRoutes 注册认证相关路由
func RegisterAuthRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 无需认证的认证路由
	server.AddRoutes([]rest.Route{
		// 微信小程序登录
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/auth/login",
			Handler: handler.WxLoginHandler(serverCtx),
		},
		// 微信小程序刷新Token
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/auth/refresh-token",
			Handler: handler.WxRefreshTokenHandler(serverCtx),
		},
	})

	// 微信小程序登出路由 (需要认证)
	server.AddRoutes(
		[]rest.Route{
			// 微信小程序登出
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/auth/logout",
				Handler: wxAuthWrapper(handler.WxLogoutHandler(serverCtx)),
			},
		},
	)
}
