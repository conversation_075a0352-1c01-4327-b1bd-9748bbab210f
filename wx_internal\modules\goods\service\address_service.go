package service

import (
	"context"
	"fmt"

	userModel "yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type AddressService struct {
	db *gorm.DB
}

func NewAddressService() *AddressService {
	return &AddressService{
		db: mysql.GetDB(),
	}
}

// RegionInfo 地区信息结构
type RegionInfo struct {
	Code string `gorm:"column:code"`
	Name string `gorm:"column:name"`
}

// getRegionNames 根据地区编码获取地区名称
func (s *AddressService) getRegionNames(provinceCode, cityCode, districtCode string) (string, string, string, error) {
	var regions []RegionInfo
	codes := []string{provinceCode, cityCode, districtCode}

	// 过滤掉空的编码
	validCodes := make([]string, 0, 3)
	for _, code := range codes {
		if code != "" {
			validCodes = append(validCodes, code)
		}
	}

	if len(validCodes) == 0 {
		return "", "", "", fmt.Errorf("所有地区编码都为空")
	}

	err := s.db.Raw("SELECT code, name FROM t_regions WHERE code IN (?)", validCodes).Scan(&regions).Error
	if err != nil {
		return "", "", "", fmt.Errorf("查询地区名称失败: %w", err)
	}

	// 建立编码到名称的映射
	nameMap := make(map[string]string)
	for _, region := range regions {
		nameMap[region.Code] = region.Name
	}

	// 记录调试信息
	provinceName := nameMap[provinceCode]
	cityName := nameMap[cityCode]
	districtName := nameMap[districtCode]

	// 如果某些名称为空，记录警告日志
	if provinceCode != "" && provinceName == "" {
		fmt.Printf("[Address] 省份编码 %s 未找到对应名称\n", provinceCode)
	}
	if cityCode != "" && cityName == "" {
		fmt.Printf("[Address] 城市编码 %s 未找到对应名称\n", cityCode)
	}
	if districtCode != "" && districtName == "" {
		fmt.Printf("[Address] 区县编码 %s 未找到对应名称\n", districtCode)
	}

	fmt.Printf("[Address] 地区编码查询结果: 省%s(%s), 市%s(%s), 区%s(%s)\n",
		provinceCode, provinceName, cityCode, cityName, districtCode, districtName)

	return provinceName, cityName, districtName, nil
}

// CreateAddress 创建收货地址
func (s *AddressService) CreateAddress(ctx context.Context, userID uint, req *userModel.AddressCreateRequest) (*userModel.AddressWithRegionNames, error) {
	// 检查用户地址数量限制（最多30条）
	var count int64
	if err := s.db.WithContext(ctx).Model(&userModel.Address{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return nil, fmt.Errorf("查询地址数量失败: %w", err)
	}
	if count >= 30 {
		return nil, fmt.Errorf("每位用户最多只能添加30个收货地址")
	}

	// 如果设置为默认地址，先取消其他默认地址
	if req.IsDefault == 1 {
		s.db.WithContext(ctx).Model(&userModel.Address{}).
			Where("user_id = ?", userID).
			Update("is_default", 0)
	}

	address := &userModel.Address{
		UserID:       userID,
		Name:         req.Name,
		Phone:        req.Phone,
		Avatar:       req.Avatar,
		ProvinceCode: req.ProvinceCode,
		CityCode:     req.CityCode,
		DistrictCode: req.DistrictCode,
		Address:      req.Address,
		Postcode:     req.Postcode,
		IsDefault:    req.IsDefault,
	}

	if err := s.db.WithContext(ctx).Create(address).Error; err != nil {
		return nil, fmt.Errorf("创建收货地址失败: %w", err)
	}

	// 查询地区名称并构建响应
	provinceName, cityName, districtName, err := s.getRegionNames(req.ProvinceCode, req.CityCode, req.DistrictCode)
	if err != nil {
		// 即使查询地区名称失败，也返回地址，但名称为空
		provinceName, cityName, districtName = "", "", ""
	}

	result := &userModel.AddressWithRegionNames{
		Address:      *address,
		ProvinceName: provinceName,
		CityName:     cityName,
		DistrictName: districtName,
	}

	return result, nil
}

// UpdateAddress 更新收货地址
func (s *AddressService) UpdateAddress(ctx context.Context, id, userID uint, req *userModel.AddressUpdateRequest) (*userModel.AddressWithRegionNames, error) {
	address := &userModel.Address{}
	if err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", id, userID).First(address).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("收货地址不存在")
		}
		return nil, fmt.Errorf("查询收货地址失败: %w", err)
	}

	// 如果设置为默认地址，先取消其他默认地址
	if req.IsDefault == 1 && address.IsDefault != 1 {
		s.db.WithContext(ctx).Model(&userModel.Address{}).
			Where("user_id = ? AND id != ?", userID, id).
			Update("is_default", 0)
	}

	// 更新地址信息
	address.Name = req.Name
	address.Phone = req.Phone
	address.Avatar = req.Avatar
	address.ProvinceCode = req.ProvinceCode
	address.CityCode = req.CityCode
	address.DistrictCode = req.DistrictCode
	address.Address = req.Address
	address.Postcode = req.Postcode
	address.IsDefault = req.IsDefault

	if err := s.db.WithContext(ctx).Save(address).Error; err != nil {
		return nil, fmt.Errorf("更新收货地址失败: %w", err)
	}

	// 查询地区名称并构建响应
	provinceName, cityName, districtName, err := s.getRegionNames(req.ProvinceCode, req.CityCode, req.DistrictCode)
	if err != nil {
		// 即使查询地区名称失败，也返回地址，但名称为空
		provinceName, cityName, districtName = "", "", ""
	}

	result := &userModel.AddressWithRegionNames{
		Address:      *address,
		ProvinceName: provinceName,
		CityName:     cityName,
		DistrictName: districtName,
	}

	return result, nil
}

// DeleteAddress 删除收货地址
func (s *AddressService) DeleteAddress(ctx context.Context, id, userID uint) error {
	result := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", id, userID).
		Delete(&userModel.Address{})

	if result.Error != nil {
		return fmt.Errorf("删除收货地址失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("收货地址不存在")
	}

	return nil
}

// GetAddress 获取收货地址详情
func (s *AddressService) GetAddress(ctx context.Context, id, userID uint) (*userModel.AddressWithRegionNames, error) {
	address := &userModel.Address{}
	if err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", id, userID).First(address).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("收货地址不存在")
		}
		return nil, fmt.Errorf("查询收货地址失败: %w", err)
	}

	// 查询地区名称并构建响应
	provinceName, cityName, districtName, err := s.getRegionNames(address.ProvinceCode, address.CityCode, address.DistrictCode)
	if err != nil {
		// 即使查询地区名称失败，也返回地址，但名称为空
		provinceName, cityName, districtName = "", "", ""
	}

	result := &userModel.AddressWithRegionNames{
		Address:      *address,
		ProvinceName: provinceName,
		CityName:     cityName,
		DistrictName: districtName,
	}

	return result, nil
}

// ListAddresses 获取用户收货地址列表
func (s *AddressService) ListAddresses(ctx context.Context, userID uint) ([]*userModel.AddressWithRegionNames, error) {
	var addresses []*userModel.Address
	if err := s.db.WithContext(ctx).Where("user_id = ?", userID).
		Order("is_default DESC, created_at DESC").Find(&addresses).Error; err != nil {
		return nil, fmt.Errorf("查询收货地址列表失败: %w", err)
	}

	// 收集所有地区编码
	var allCodes []string
	codeMap := make(map[string]bool)
	for _, addr := range addresses {
		codes := []string{addr.ProvinceCode, addr.CityCode, addr.DistrictCode}
		for _, code := range codes {
			if code != "" && !codeMap[code] {
				allCodes = append(allCodes, code)
				codeMap[code] = true
			}
		}
	}

	// 批量查询地区名称
	var regions []RegionInfo
	nameMap := make(map[string]string)
	if len(allCodes) > 0 {
		err := s.db.Raw("SELECT code, name FROM t_regions WHERE code IN (?)", allCodes).Scan(&regions).Error
		if err == nil {
			for _, region := range regions {
				nameMap[region.Code] = region.Name
			}
		}
	}

	// 构建带地区名称的响应
	var result []*userModel.AddressWithRegionNames
	for _, addr := range addresses {
		result = append(result, &userModel.AddressWithRegionNames{
			Address:      *addr,
			ProvinceName: nameMap[addr.ProvinceCode],
			CityName:     nameMap[addr.CityCode],
			DistrictName: nameMap[addr.DistrictCode],
		})
	}

	return result, nil
}

// GetDefaultAddress 获取默认收货地址
func (s *AddressService) GetDefaultAddress(ctx context.Context, userID uint) (*userModel.AddressWithRegionNames, error) {
	address := &userModel.Address{}
	err := s.db.WithContext(ctx).Where("user_id = ? AND is_default = ?", userID, 1).First(address).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有默认地址，返回nil而不是错误
		}
		return nil, fmt.Errorf("查询默认收货地址失败: %w", err)
	}

	// 查询地区名称并构建响应
	provinceName, cityName, districtName, err := s.getRegionNames(address.ProvinceCode, address.CityCode, address.DistrictCode)
	if err != nil {
		// 即使查询地区名称失败，也返回地址，但名称为空
		provinceName, cityName, districtName = "", "", ""
	}

	result := &userModel.AddressWithRegionNames{
		Address:      *address,
		ProvinceName: provinceName,
		CityName:     cityName,
		DistrictName: districtName,
	}

	return result, nil
}

// SetDefaultAddress 设置默认收货地址
func (s *AddressService) SetDefaultAddress(ctx context.Context, id, userID uint) error {
	// 事务处理
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先取消其他默认地址
		if err := tx.Model(&userModel.Address{}).
			Where("user_id = ?", userID).
			Update("is_default", 0).Error; err != nil {
			return fmt.Errorf("取消默认地址失败: %w", err)
		}

		// 设置新的默认地址
		result := tx.Model(&userModel.Address{}).
			Where("id = ? AND user_id = ?", id, userID).
			Update("is_default", 1)

		if result.Error != nil {
			return fmt.Errorf("设置默认地址失败: %w", result.Error)
		}
		if result.RowsAffected == 0 {
			return fmt.Errorf("收货地址不存在")
		}

		return nil
	})
}
