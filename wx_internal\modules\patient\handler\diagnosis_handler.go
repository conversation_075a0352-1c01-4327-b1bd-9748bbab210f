package handler

import (
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"

	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/response"
)

// DiagnosisHandler 诊断记录处理器
type DiagnosisHandler struct{}

// DiagnosisListRequest 诊断记录列表请求
type DiagnosisListRequest struct {
	Page      int    `form:"page,default=1"`     // 页码
	Size      int    `form:"size,default=10"`    // 每页记录数
	PatientID int    `form:"patientId"`          // 患者ID
	StartDate string `form:"startDate,optional"` // 开始日期
	EndDate   string `form:"endDate,optional"`   // 结束日期
}

// DiagnosisDetailRequest 诊断记录详情请求
type DiagnosisDetailRequest struct {
	ID int `path:"id"` // 诊断记录ID
}

// NewDiagnosisHandler 创建诊断记录处理器
func NewDiagnosisHandler() *DiagnosisHandler {
	return &DiagnosisHandler{}
}

// List 获取诊断记录列表
func (h *DiagnosisHandler) List(w http.ResponseWriter, r *http.Request) {
	var req DiagnosisListRequest
	if err := httpx.Parse(r, &req); err != nil {
		response.Error(w, response.CodeInvalidParams, "参数解析失败")
		return
	}

	// 参数验证
	if req.Page < 1 {
		req.Page = 1
	}
	if req.Size < 1 || req.Size > 100 {
		req.Size = 10
	}
	if req.PatientID <= 0 {
		response.Error(w, response.CodeInvalidParams, "患者ID必须大于0")
		return
	}

	// 构建查询
	db := mysql.Master()
	query := db.Model(&patient.DiagnosisRecord{})

	// 应用筛选条件
	query = query.Where("grxxid = ?", req.PatientID) // 小程序端必须指定患者ID

	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate+" 23:59:59")
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logx.Errorf("查询诊断记录总数失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 查询数据
	var records []patient.DiagnosisRecord
	offset := (req.Page - 1) * req.Size
	if err := query.Order("created_at DESC").Offset(offset).Limit(req.Size).Find(&records).Error; err != nil {
		logx.Errorf("查询诊断记录失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 构建响应
	response.Success(w, map[string]interface{}{
		"total":   total,
		"current": req.Page,
		"size":    req.Size,
		"records": records,
	})
}

// Detail 获取诊断记录详情
func (h *DiagnosisHandler) Detail(w http.ResponseWriter, r *http.Request) {
	var req DiagnosisDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		response.Error(w, response.CodeInvalidParams, "参数解析失败")
		return
	}

	// 参数验证
	if req.ID <= 0 {
		response.Error(w, response.CodeInvalidParams, "无效的诊断记录ID")
		return
	}

	// 查询诊断记录
	var record patient.DiagnosisRecord
	if err := mysql.Master().First(&record, req.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(w, response.CodeNotFound, "诊断记录不存在")
		} else {
			logx.Errorf("查询诊断记录详情失败: %v", err)
			response.Error(w, response.CodeInternalError, "查询失败")
		}
		return
	}

	// 返回详情
	response.Success(w, record)
}
