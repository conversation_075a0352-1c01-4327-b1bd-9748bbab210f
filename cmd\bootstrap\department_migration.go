package bootstrap

import (
	"log"

	"yekaitai/pkg/infra/mysql"
	departmentModel "yekaitai/wx_internal/modules/department/model"
)

// MigrateDepartmentTables 执行科室表结构迁移
func MigrateDepartmentTables() error {
	log.Println("开始执行科室表结构迁移...")

	// 执行科室表结构迁移
	db := mysql.Master()
	db.Set("gorm:table_options", "COMMENT='机构科室信息表'").AutoMigrate(&departmentModel.Department{})

	log.Println("科室表结构迁移完成")
	return nil
}
