package handler

import (
	"net/http"
	"yekaitai/wx_internal/modules/user/logic"
	"yekaitai/wx_internal/modules/user/types"
	"yekaitai/wx_internal/svc"
	wxTypes "yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// UpdateProfileHandler 更新用户资料
func UpdateProfileHandler(ctx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateProfileReq
		if err := httpx.Parse(r, &req); err != nil {
			logx.Errorf("解析更新用户资料请求参数失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, wxTypes.NewErrorResponse(wxTypes.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 验证请求参数
		if req.Avatar == "" && req.NickName == "" {
			logx.Error("更新用户资料失败: 头像和昵称均为空")
			httpx.WriteJson(w, http.StatusOK, wxTypes.NewErrorResponse(wxTypes.CodeInvalidParams, "头像和昵称不能同时为空"))
			return
		}

		l := logic.NewUpdateProfileLogic(r.Context(), ctx)
		resp, err := l.UpdateProfile(&req)
		if err != nil {
			logx.Errorf("更新用户资料失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, wxTypes.NewErrorResponse(wxTypes.CodeInternalError, err.Error()))
			return
		}

		httpx.WriteJson(w, http.StatusOK, wxTypes.NewSuccessResponse(resp))
	}
}
