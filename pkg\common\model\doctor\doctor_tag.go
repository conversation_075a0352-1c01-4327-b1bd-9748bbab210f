package doctor

import "time"

// DoctorTag 医生与标签的多对多关系中间表
type DoctorTag struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:关联ID"`
	DoctorID  uint      `json:"doctor_id" gorm:"index;not null;comment:医生ID"`
	TagID     uint      `json:"tag_id" gorm:"index;not null;comment:标签ID"`
	CreatedAt time.Time `json:"created_at" gorm:"comment:创建时间"`
}

// TableName 设置DoctorTag表名
func (DoctorTag) TableName() string {
	return "doctor_tag"
}
