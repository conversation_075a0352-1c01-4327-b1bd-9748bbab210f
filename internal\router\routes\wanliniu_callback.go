package routes

import (
	"yekaitai/internal/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterWanLiNiuCallbackRoutes 注册万里牛回调路由
func RegisterWanLiNiuCallbackRoutes(server *rest.Server, serverCtx *svc.ServiceContext) {
	callbackHandler := handler.NewWanLiNiuCallbackHandler()

	// 万里牛回调接口路由 - 这些接口是万里牛调用我们的，不需要认证
	server.AddRoutes([]rest.Route{
		{
			Method:  "POST",
			Path:    "/erp/callback/tradeSendMsg",
			Handler: callbackHandler.TradeShipmentNotificationHandler,
		},
		{
			Method:  "POST",
			Path:    "/erp/callback/itemQuantityUploadMsg",
			Handler: callbackHandler.InventoryChangeNotificationHandler,
		},
	})
}
