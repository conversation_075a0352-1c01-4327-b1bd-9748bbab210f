package jushuitan

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"
)

// Client 聚水潭API客户端
type Client struct {
	config     Config
	httpClient *http.Client
}

// BaseResponse 基础API响应结构
type BaseResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// TokenResponse 令牌响应结构
type TokenResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		AccessToken  string `json:"access_token"`
		ExpiresIn    int    `json:"expires_in"`
		RefreshToken string `json:"refresh_token"`
		Scope        string `json:"scope"`
	} `json:"data"`
}

// NewClient 创建聚水潭API客户端
func NewClient(config Config) *Client {
	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Request 发送请求到聚水潭API
func (c *Client) Request(ctx context.Context, path string, bizParams interface{}) ([]byte, error) {
	// 检查是否需要刷新token
	if err := c.ensureValidToken(ctx); err != nil {
		return nil, fmt.Errorf("确保有效token失败: %w", err)
	}

	return c.doRequest(ctx, path, bizParams)
}

// ensureValidToken 确保有有效的access_token
func (c *Client) ensureValidToken(ctx context.Context) error {
	// 如果没有设置refresh_token，则尝试获取初始token
	if c.config.RefreshToken == "" {
		return c.getInitToken(ctx)
	}

	// 检查token是否即将过期（12小时内过期需要刷新）
	if !c.config.TokenExpiresAt.IsZero() && time.Until(c.config.TokenExpiresAt) < 12*time.Hour {
		logx.Infof("[JST] access_token即将过期，剩余时间: %v，准备刷新", time.Until(c.config.TokenExpiresAt))
		return c.refreshToken(ctx)
	}

	return nil
}

// getInitToken 获取初始token
func (c *Client) getInitToken(ctx context.Context) error {
	logx.Infof("[JST] 正在获取初始token")

	// 构建完整URL
	fullURL := "https://openapi.jushuitan.com/openWeb/auth/getInitToken"
	if c.config.IsTestEnv {
		fullURL = "https://dev-api.jushuitan.com/openWebIsv/auth/getInitToken"
	}

	// 生成随机码 (六位字符)
	code := generateRandomCode(6)

	// 准备参数
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	params := map[string]string{
		"app_key":    c.config.AppKey,
		"timestamp":  timestamp,
		"grant_type": "authorization_code",
		"charset":    "utf-8",
		"code":       code,
	}

	// 计算签名
	params["sign"] = c.generateSignForAuth(params)

	// 构建表单
	form := url.Values{}
	for key, value := range params {
		form.Add(key, value)
	}

	// 发送请求
	req, err := http.NewRequestWithContext(ctx, "POST", fullURL, strings.NewReader(form.Encode()))
	if err != nil {
		return fmt.Errorf("创建token请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")

	// 执行请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(req)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[JST] token请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return fmt.Errorf("发送token请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[JST] 读取token响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return fmt.Errorf("读取token响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[JST] 收到token响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)
	logx.Infof("[JST] 响应内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[JST] token请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return fmt.Errorf("token请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var tokenResp TokenResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &tokenResp); err != nil {
		logx.Errorf("[JST] 解析token响应失败: %s, 错误: %v", fullURL, err)
		return fmt.Errorf("解析token响应失败: %w", err)
	}

	// 检查业务状态码
	if tokenResp.Code != 0 {
		logx.Errorf("[JST] token业务处理失败: %s, 业务码: %d, 业务消息: %s", fullURL, tokenResp.Code, tokenResp.Msg)
		return fmt.Errorf("token业务处理失败: %s", tokenResp.Msg)
	}

	// 更新配置中的token信息
	c.config.AccessToken = tokenResp.Data.AccessToken
	c.config.RefreshToken = tokenResp.Data.RefreshToken
	c.config.TokenExpiresAt = time.Now().Add(time.Duration(tokenResp.Data.ExpiresIn) * time.Second)

	logx.Infof("[JST] token更新成功，新access_token: %s, 过期时间: %v",
		c.config.AccessToken, c.config.TokenExpiresAt)

	return nil
}

// refreshToken 刷新token
func (c *Client) refreshToken(ctx context.Context) error {
	logx.Infof("[JST] 正在刷新token")

	// 构建完整URL
	fullURL := "https://openapi.jushuitan.com/openWeb/auth/refreshToken"
	if c.config.IsTestEnv {
		fullURL = "https://dev-api.jushuitan.com/openWebIsv/auth/refreshToken"
	}

	// 准备参数
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	params := map[string]string{
		"app_key":       c.config.AppKey,
		"timestamp":     timestamp,
		"grant_type":    "refresh_token",
		"charset":       "utf-8",
		"refresh_token": c.config.RefreshToken,
		"scope":         "all",
	}

	// 计算签名
	params["sign"] = c.generateSignForAuth(params)

	// 构建表单
	form := url.Values{}
	for key, value := range params {
		form.Add(key, value)
	}

	// 发送请求
	req, err := http.NewRequestWithContext(ctx, "POST", fullURL, strings.NewReader(form.Encode()))
	if err != nil {
		return fmt.Errorf("创建token请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")

	// 执行请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(req)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[JST] token请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return fmt.Errorf("发送token请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[JST] 读取token响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return fmt.Errorf("读取token响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[JST] 收到token响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)
	logx.Infof("[JST] 响应内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[JST] token请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return fmt.Errorf("token请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var tokenResp TokenResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &tokenResp); err != nil {
		logx.Errorf("[JST] 解析token响应失败: %s, 错误: %v", fullURL, err)
		return fmt.Errorf("解析token响应失败: %w", err)
	}

	// 检查业务状态码
	if tokenResp.Code != 0 {
		logx.Errorf("[JST] token业务处理失败: %s, 业务码: %d, 业务消息: %s", fullURL, tokenResp.Code, tokenResp.Msg)
		return fmt.Errorf("token业务处理失败: %s", tokenResp.Msg)
	}

	// 更新配置中的token信息
	c.config.AccessToken = tokenResp.Data.AccessToken
	c.config.RefreshToken = tokenResp.Data.RefreshToken
	c.config.TokenExpiresAt = time.Now().Add(time.Duration(tokenResp.Data.ExpiresIn) * time.Second)

	logx.Infof("[JST] token更新成功，新access_token: %s, 过期时间: %v",
		c.config.AccessToken, c.config.TokenExpiresAt)

	return nil
}

// generateSignForAuth 根据授权接口要求生成签名
func (c *Client) generateSignForAuth(params map[string]string) string {
	// 排除 sign 参数并收集其他参数
	var keys []string
	for k := range params {
		if k != "sign" {
			keys = append(keys, k)
		}
	}

	// 按字典序排序
	sort.Strings(keys)

	// 拼接字符串
	var sb strings.Builder
	sb.WriteString(c.config.AppSecret) // 先拼接 app_secret

	for _, k := range keys {
		sb.WriteString(k)
		sb.WriteString(params[k])
	}

	// 计算 MD5
	signStr := sb.String()
	logx.Infof("[JST] 授权签名字符串: %s", signStr)

	hash := md5.New()
	hash.Write([]byte(signStr))
	sign := hex.EncodeToString(hash.Sum(nil))
	logx.Infof("[JST] 生成授权签名: %s", sign)

	return sign
}

// generateRandomCode 生成指定长度的随机字符串
func generateRandomCode(length int) string {
	const charset = "123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
		time.Sleep(1 * time.Nanosecond) // 确保每个字符生成的时间不同
	}
	return string(result)
}

// doRequest 执行HTTP请求并处理响应
func (c *Client) doRequest(ctx context.Context, path string, bizParams interface{}) ([]byte, error) {
	// 构建完整URL
	fullURL := fmt.Sprintf("%s%s", c.config.BaseURL, path)
	logx.Infof("[JST] 发送请求: %s", fullURL)

	// 准备通用参数
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 智能处理业务参数
	var bizContent string
	switch v := bizParams.(type) {
	case string:
		// 直接使用原始字符串（开发者需确保是合法JSON）
		bizContent = v
		if bizContent == "" {
			bizContent = "{}" // 空值处理
		}
	case nil:
		bizContent = "{}"
	default:
		// 非字符串类型使用不转义HTML的JSON序列化
		config := jsoniter.Config{EscapeHTML: false}.Froze()
		bizJSON, err := config.Marshal(v)
		if err != nil {
			return nil, fmt.Errorf("序列化业务参数失败: %w", err)
		}
		bizContent = string(bizJSON)
	}
	logx.Infof("[JST] 原始业务参数: %s", bizContent)

	// 构建签名参数
	signParams := map[string]string{
		"app_key":      c.config.AppKey,
		"access_token": c.config.AccessToken,
		"timestamp":    timestamp,
		"version":      "2",
		"charset":      "utf-8",
		"biz":          bizContent,
	}

	// 计算签名
	sign := c.generateSign(signParams)

	// 构建请求参数
	form := url.Values{}
	form.Add("app_key", c.config.AppKey)
	form.Add("access_token", c.config.AccessToken)
	form.Add("timestamp", timestamp)
	form.Add("version", "2")
	form.Add("charset", "utf-8")
	form.Add("sign", sign)
	form.Add("biz", bizContent)

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", fullURL, strings.NewReader(form.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")

	// 执行请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(req)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[JST] 请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[JST] 读取响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[JST] 收到响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)
	logx.Infof("[JST] 响应内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[JST] 请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return nil, fmt.Errorf("API请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析基础响应
	var baseResp BaseResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &baseResp); err != nil {
		logx.Errorf("[JST] 解析响应失败: %s, 错误: %v", fullURL, err)
		return nil, fmt.Errorf("解析API响应失败: %w", err)
	}

	// 检查业务状态码
	if baseResp.Code != 0 {
		logx.Errorf("[JST] 业务处理失败: %s, 业务码: %d, 业务消息: %s", fullURL, baseResp.Code, baseResp.Msg)
		return body, fmt.Errorf(string(body))
	}

	return body, nil
}

// generateSign 根据聚水潭规则生成签名
func (c *Client) generateSign(params map[string]string) string {
	// 排除 sign 参数并收集其他参数
	var keys []string
	for k := range params {
		if k != "sign" {
			keys = append(keys, k)
		}
	}

	// 按字典序排序
	sort.Strings(keys)

	// 拼接字符串
	var sb strings.Builder
	sb.WriteString(c.config.AppSecret) // 先拼接 app_secret

	for _, k := range keys {
		sb.WriteString(k)
		sb.WriteString(params[k])
	}

	// 计算 MD5
	signStr := sb.String()
	logx.Infof("[JST] 签名字符串: %s", signStr)

	hash := md5.New()
	hash.Write([]byte(signStr))
	sign := hex.EncodeToString(hash.Sum(nil))
	logx.Infof("[JST] 生成签名: %s", sign)

	return sign
}

// 全局默认客户端实例
var DefaultClient *Client

// Init 初始化默认客户端
func Init(config Config) {
	DefaultClient = NewClient(config)
}
