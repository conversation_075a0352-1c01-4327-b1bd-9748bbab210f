package service

import (
	"fmt"
	"math/rand"
	"time"

	"yekaitai/internal/modules/sms/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// SmsService 短信服务
type SmsService struct {
	smsRepo   model.SmsCodeRepository
	smsClient interface {
		SendSms(phoneNumber, code string) error
		ValidateConfig() error
	}
}

// NewSmsService 创建短信服务
func NewSmsService() *SmsService {
	return &SmsService{
		smsRepo:   model.NewSmsCodeRepository(mysql.Master()),
		smsClient: nil, // 暂时设为nil，在实际使用时会被设置
	}
}

// SetSmsClient 设置短信客户端
func (s *SmsService) SetSmsClient(client interface {
	SendSms(phoneNumber, code string) error
	ValidateConfig() error
}) {
	s.smsClient = client
}

// SendSmsCode 发送短信验证码
func (s *SmsService) SendSmsCode(mobile string, smsType int) (string, error) {
	logx.Infof("开始发送短信验证码: mobile=%s, type=%d", mobile, smsType)

	// 生成6位随机验证码
	code := generateRandomCode(6)
	logx.Infof("生成验证码: mobile=%s, code=%s", mobile, code)

	// 设置过期时间（1分钟）
	expireAt := time.Now().Add(1 * time.Minute)

	// 保存到数据库
	smsCode := &model.SmsCode{
		Mobile:   mobile,
		Code:     code,
		Type:     smsType,
		ExpireAt: expireAt,
	}

	logx.Infof("准备保存验证码到数据库: mobile=%s", mobile)
	if err := s.smsRepo.Create(smsCode); err != nil {
		logx.Errorf("保存短信验证码失败: %v", err)
		return "", fmt.Errorf("保存短信验证码失败")
	}
	logx.Infof("验证码保存成功: mobile=%s, id=%d", mobile, smsCode.ID)

	// 发送短信验证码
	if s.smsClient == nil {
		logx.Errorf("短信客户端未初始化")
		return "", fmt.Errorf("短信客户端未初始化")
	}

	if err := s.smsClient.SendSms(mobile, code); err != nil {
		logx.Errorf("发送短信失败: %v", err)
		return "", fmt.Errorf("发送短信失败")
	}
	logx.Infof("腾讯云短信发送成功: 手机号=%s, 验证码=%s, 类型=%d", mobile, code, smsType)

	return code, nil
}

// VerifySmsCode 验证短信验证码
func (s *SmsService) VerifySmsCode(mobile string, code string, smsType int) (bool, error) {
	// 查询最新的验证码
	smsCode, err := s.smsRepo.FindLatestByMobile(mobile, smsType)
	if err != nil {
		logx.Errorf("查询验证码失败: %v", err)
		return false, fmt.Errorf("验证码不存在或已过期")
	}

	// 验证码已过期
	if smsCode.ExpireAt.Before(time.Now()) {
		logx.Errorf("验证码已过期: mobile=%s, code=%s", mobile, code)
		return false, fmt.Errorf("验证码已过期")
	}

	// 验证码已使用
	if smsCode.Used {
		logx.Errorf("验证码已使用: mobile=%s, code=%s", mobile, code)
		return false, fmt.Errorf("验证码已使用")
	}

	// 验证码不匹配
	if smsCode.Code != code {
		logx.Errorf("验证码不匹配: mobile=%s, expected=%s, actual=%s", mobile, smsCode.Code, code)
		return false, fmt.Errorf("验证码错误")
	}

	// 标记验证码为已使用
	if err := s.smsRepo.MarkAsUsed(smsCode.ID); err != nil {
		logx.Errorf("标记验证码为已使用失败: %v", err)
		// 继续执行，不返回错误
	}

	return true, nil
}

// 生成随机验证码
func generateRandomCode(length int) string {
	rand.Seed(time.Now().UnixNano())
	digits := "0123456789"
	code := ""
	for i := 0; i < length; i++ {
		code += string(digits[rand.Intn(len(digits))])
	}
	return code
}
