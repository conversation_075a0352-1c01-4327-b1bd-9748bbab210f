package routes

import (
	"net/http"

	baseSetupHandler "yekaitai/internal/modules/base_setup/handler"
	"yekaitai/wx_internal/middleware"

	checkoutHandler "yekaitai/wx_internal/modules/checkout/handler"
	goodsHandler "yekaitai/wx_internal/modules/goods/handler"
	logisticsHandler "yekaitai/wx_internal/modules/logistics/handler"
	orderHandler "yekaitai/wx_internal/modules/order/handler"
	pointsHandler "yekaitai/wx_internal/modules/points/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterGoodsRoutes 注册小程序商城相关路由
func RegisterGoodsRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用微信认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 创建go-zero格式的handler实例
	goodsHandlerInst := goodsHandler.NewWxGoodsGoZeroHandler()
	storeHandler := baseSetupHandler.NewStoreConfigGoZeroHandler()
	cartHandler := goodsHandler.NewCartGoZeroHandler()
	checkoutHandlerInst := checkoutHandler.NewCheckoutGoZeroHandler()
	orderHandlerInst := orderHandler.NewOrderGoZeroHandler()
	if orderHandlerInst == nil {
		panic("订单处理器初始化失败")
	}

	logisticsHandler := logisticsHandler.NewLogisticsGoZeroHandler()
	addressHandler := goodsHandler.NewAddressGoZeroHandler()

	pointsHandlerInst := pointsHandler.NewPointsGoZeroHandler()

	// 商品相关路由（无需认证）
	server.AddRoutes(
		[]rest.Route{
			// 获取商品分类
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/categories",
				Handler: wxAuthWrapper(goodsHandlerInst.GetCategories),
			},
			// 获取分类详情（包含子分类）
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/categories/:id",
				Handler: wxAuthWrapper(goodsHandlerInst.GetCategoryDetail),
			},
			// 获取商品列表
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/goods",
				Handler: wxAuthWrapper(goodsHandlerInst.GetHomePage),
			},
			// 获取商品详情
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/goods/:id",
				Handler: wxAuthWrapper(goodsHandlerInst.GetGoodsDetail),
			},
			// 搜索商品
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/goods/search",
				Handler: wxAuthWrapper(goodsHandlerInst.SearchGoods),
			},
		},
	)

	// 店铺信息相关路由
	server.AddRoutes(
		[]rest.Route{
			// 获取店铺信息
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/store/info",
				Handler: wxAuthWrapper(storeHandler.GetStoreConfigForWx),
			},
			// 获取店铺证件
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/store/certificates",
				Handler: wxAuthWrapper(storeHandler.GetStoreLicenses),
			},
			// 检查店铺状态
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/store/status",
				Handler: wxAuthWrapper(storeHandler.CheckStoreStatus),
			},
		},
	)

	// 购物车相关路由（需要认证）
	server.AddRoutes(
		[]rest.Route{
			// 获取购物车
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/cart",
				Handler: wxAuthWrapper(cartHandler.GetCartList),
			},
			// 添加到购物车
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/cart",
				Handler: wxAuthWrapper(cartHandler.AddToCart),
			},
			// 更新购物车商品数量
			{
				Method:  http.MethodPut,
				Path:    "/api/wx/cart/:id",
				Handler: wxAuthWrapper(cartHandler.UpdateCartQuantity),
			},
			// 更新购物车商品规格
			{
				Method:  http.MethodPut,
				Path:    "/api/wx/cart/:id/spec",
				Handler: wxAuthWrapper(cartHandler.UpdateCartSpec),
			},
			// 删除购物车商品
			{
				Method:  http.MethodDelete,
				Path:    "/api/wx/cart/:id",
				Handler: wxAuthWrapper(cartHandler.RemoveFromCart),
			},
			// 获取购物车数量
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/cart/count",
				Handler: wxAuthWrapper(cartHandler.GetCartCount),
			},
			// 清空购物车
			{
				Method:  http.MethodDelete,
				Path:    "/api/wx/cart/clear",
				Handler: wxAuthWrapper(cartHandler.ClearCart),
			},
		},
	)

	// 收货地址相关路由（需要认证）
	server.AddRoutes(
		[]rest.Route{
			// 获取地址列表
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/address/list",
				Handler: wxAuthWrapper(addressHandler.ListAddresses),
			},
			// 创建收货地址
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/address",
				Handler: wxAuthWrapper(addressHandler.CreateAddress),
			},
			// 获取地址详情
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/address/:id",
				Handler: wxAuthWrapper(addressHandler.GetAddress),
			},
			// 更新收货地址
			{
				Method:  http.MethodPut,
				Path:    "/api/wx/address/:id",
				Handler: wxAuthWrapper(addressHandler.UpdateAddress),
			},
			// 删除收货地址
			{
				Method:  http.MethodDelete,
				Path:    "/api/wx/address/:id",
				Handler: wxAuthWrapper(addressHandler.DeleteAddress),
			},
			// 获取默认地址
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/address/default",
				Handler: wxAuthWrapper(addressHandler.GetDefaultAddress),
			},
			// 设置默认地址
			{
				Method:  http.MethodPut,
				Path:    "/api/wx/address/:id/default",
				Handler: wxAuthWrapper(addressHandler.SetDefaultAddress),
			},
		},
	)

	// 结算相关路由（需要认证）
	server.AddRoutes(
		[]rest.Route{
			// 预加载结算数据
			{
				Method:  http.MethodPost,
				Path:    "/api/checkout/preload",
				Handler: wxAuthWrapper(checkoutHandlerInst.Preload),
			},
			// 动态重新计算
			{
				Method:  http.MethodPost,
				Path:    "/api/checkout/recalculate",
				Handler: wxAuthWrapper(checkoutHandlerInst.Recalculate),
			},
		},
	)

	// 订单相关路由（核心支付流程）
	server.AddRoutes(
		[]rest.Route{
			// 创建订单并生成支付参数（核心接口）
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/orders",
				Handler: wxAuthWrapper(orderHandlerInst.CreateOrder),
			},
			// 获取订单列表（需要认证）
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/orders",
				Handler: wxAuthWrapper(orderHandlerInst.GetOrderList),
			},
			// 获取订单详情（需要认证）
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/orders/:id",
				Handler: wxAuthWrapper(orderHandlerInst.GetOrderDetail),
			},
			// 取消订单（需要认证）
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/orders/:id/cancel",
				Handler: wxAuthWrapper(orderHandlerInst.CancelOrder),
			},
			// 确认收货（需要认证）
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/orders/:id/receive",
				Handler: wxAuthWrapper(orderHandlerInst.ConfirmReceive),
			},

			// 重新支付订单（支付参数过期时使用）
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/orders/:id/repay",
				Handler: wxAuthWrapper(orderHandlerInst.RepayOrder),
			},

			// 查询订单退款状态
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/orders/:id/refund-status",
				Handler: wxAuthWrapper(orderHandlerInst.GetOrderRefundStatus),
			},

			// 创建退款（仅用于测试）
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/orders/refunds",
				Handler: wxAuthWrapper(orderHandlerInst.CreateRefund),
			},
		},
	)

	// 积分相关路由（需要认证）
	server.AddRoutes(
		[]rest.Route{
			// 获取积分使用规则
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/points/rule",
				Handler: wxAuthWrapper(pointsHandlerInst.GetPointsRule),
			},
			// 计算积分抵扣
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/points/calculate",
				Handler: wxAuthWrapper(pointsHandlerInst.CalculatePointsDeduction),
			},
			// 获取用户积分余额
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/points",
				Handler: wxAuthWrapper(pointsHandlerInst.GetUserPoints),
			},
		},
	)

	// 物流相关路由（需要认证）
	server.AddRoutes(
		[]rest.Route{
			// 查询物流轨迹
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/logistics/trace",
				Handler: wxAuthWrapper(logisticsHandler.QueryLogisticsTrace),
			},
			// 查询订单物流
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/orders/:id/logistics",
				Handler: wxAuthWrapper(logisticsHandler.QueryOrderLogistics),
			},
		},
	)
}
