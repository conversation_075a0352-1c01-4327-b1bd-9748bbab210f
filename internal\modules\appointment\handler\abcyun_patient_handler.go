package handler

import (
	"errors"
	"net/http"

	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// 患者相关请求结构体
// PatientListRequest 获取患者分页列表请求
type PatientListRequest struct {
	Date   string `form:"date"`                      // 日期，格式yyyy-MM-dd，必填
	Limit  int    `form:"limit,optional,default=20"` // 每页显示条数，默认20
	Offset int    `form:"offset,optional,default=0"` // 分页起始下标，默认0
}

// PatientQueryRequest 查询患者请求
type PatientQueryRequest struct {
	Mobile string `form:"mobile,optional"` // 手机号，可选
	Name   string `form:"name,optional"`   // 姓名，可选
	SN     string `form:"sn,optional"`     // 档案号，可选
}

// PatientDetailRequest 患者详情请求
type PatientDetailRequest struct {
	ID string `path:"id"` // 患者ID
}

// PatientAttachmentListRequest 患者附件列表请求
type PatientAttachmentListRequest struct {
	PatientID string `path:"patientId"`                 // 患者ID
	Limit     int    `form:"limit,optional,default=10"` // 每页显示条数，默认10
	Offset    int    `form:"offset,optional,default=0"` // 分页起始下标，默认0
}

// PatientAttachmentRequest 患者附件请求
type PatientAttachmentRequest struct {
	PatientID    string `path:"patientId"`    // 患者ID
	AttachmentID string `path:"attachmentId"` // 附件ID
}

// PatientFamilyMemberRequest 患者家庭成员请求
type PatientFamilyMemberRequest struct {
	PatientID string `path:"patientId"` // 患者ID
}

// MemberCardPayRequest 会员卡支付请求
type MemberCardPayRequest struct {
	MemberCardID         string  `path:"memberCardId"`         // 会员卡ID
	TransactionPatientID string  `json:"transactionPatientId"` // 交易患者ID
	Amount               float64 `json:"amount"`               // 金额
	BusinessID           string  `json:"businessId"`           // 业务ID
	Password             string  `json:"password"`             // 密码
	OperatorID           string  `json:"operatorId"`           // 操作员ID
}

// MemberCardRefundRequest 会员卡退款请求
type MemberCardRefundRequest struct {
	MemberCardID   string   `path:"memberCardId"`   // 会员卡ID
	Amount         float64  `json:"amount"`         // 金额
	TransactionIDs []string `json:"transactionIds"` // 交易ID列表
	BusinessID     string   `json:"businessId"`     // 业务ID
	OperatorID     string   `json:"operatorId"`     // 操作员ID
}

// CreatePatientRequest 创建患者请求
type CreatePatientRequest struct {
	Name         string                `json:"name"`         // 姓名
	Mobile       string                `json:"mobile"`       // 手机号
	Sex          string                `json:"sex"`          // 性别
	Birthday     string                `json:"birthday"`     // 生日
	SourceID     string                `json:"sourceId"`     // 来源ID
	SourceFromID string                `json:"sourceFromId"` // 来源FromID
	IDCard       string                `json:"idCard"`       // 身份证号
	PastHistory  string                `json:"pastHistory"`  // 既往病史
	Address      abcyun.PatientAddress `json:"address"`      // 地址
	SN           string                `json:"sn"`           // 档案号
	Remark       string                `json:"remark"`       // 备注
	Profession   string                `json:"profession"`   // 职业
	Company      string                `json:"company"`      // 公司
	Marital      string                `json:"marital"`      // 婚姻状况
	Weight       string                `json:"weight"`       // 体重
}

// UpdatePatientRequest 更新患者请求
type UpdatePatientRequest struct {
	ID           string                `path:"id"`           // 患者ID
	Name         string                `json:"name"`         // 姓名
	Mobile       string                `json:"mobile"`       // 手机号
	Sex          string                `json:"sex"`          // 性别
	Birthday     string                `json:"birthday"`     // 生日
	SourceID     string                `json:"sourceId"`     // 来源ID
	SourceFromID string                `json:"sourceFromId"` // 来源FromID
	IDCard       string                `json:"idCard"`       // 身份证号
	PastHistory  string                `json:"pastHistory"`  // 既往病史
	Address      abcyun.PatientAddress `json:"address"`      // 地址
	SN           string                `json:"sn"`           // 档案号
	Remark       string                `json:"remark"`       // 备注
	Profession   string                `json:"profession"`   // 职业
	Company      string                `json:"company"`      // 公司
	Marital      string                `json:"marital"`      // 婚姻状况
	Weight       string                `json:"weight"`       // 体重
}

// AddPatientAttachmentsRequest 添加患者附件请求
type AddPatientAttachmentsRequest struct {
	PatientID   string                               `path:"patientId"`   // 患者ID
	Attachments []abcyun.AddPatientAttachmentRequest `json:"attachments"` // 附件列表
}

// AbcYunPatientListHandler 获取患者分页列表
func (h *AbcYunHandler) AbcYunPatientListHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientListRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	result, err := client.GetPatientList(req.Date, req.Limit, req.Offset)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者列表成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunPatientQueryHandler 查询患者
func (h *AbcYunHandler) AbcYunPatientQueryHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientQueryRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	result, err := client.SearchPatients(req.Mobile, req.Name, req.SN)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "查询患者成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunPatientDetailHandler 获取患者详情
func (h *AbcYunHandler) AbcYunPatientDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientDetailRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.ID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	result, err := client.GetPatientDetail(req.ID)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者详情成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunPatientSourceTypesHandler 获取患者来源分类列表
func (h *AbcYunHandler) AbcYunPatientSourceTypesHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	result, err := client.GetPatientSourceTypes()
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者来源分类列表成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunPatientMemberTypesHandler 获取患者会员类型列表
func (h *AbcYunHandler) AbcYunPatientMemberTypesHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	result, err := client.GetPatientMemberTypes()
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者会员类型列表成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunPatientFamilyMembersHandler 获取患者家庭成员
func (h *AbcYunHandler) AbcYunPatientFamilyMembersHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientFamilyMemberRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	result, err := client.GetPatientFamilyMembers(req.PatientID)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者家庭成员成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunPatientAttachmentsHandler 获取患者附件
func (h *AbcYunHandler) AbcYunPatientAttachmentsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientAttachmentListRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	result, err := client.GetPatientAttachments(req.PatientID, req.Limit, req.Offset)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取患者附件成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunPatientDeleteAttachmentHandler 删除患者附件
func (h *AbcYunHandler) AbcYunPatientDeleteAttachmentHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req PatientAttachmentRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" || req.AttachmentID == "" {
		httpx.Error(w, errors.New("缺少参数"))
		return
	}

	result, err := client.DeletePatientAttachment(req.PatientID, req.AttachmentID)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "删除患者附件成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunMemberCardPayHandler 会员卡支付
func (h *AbcYunHandler) AbcYunMemberCardPayHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req MemberCardPayRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.MemberCardID == "" {
		httpx.Error(w, errors.New("缺少会员卡ID参数"))
		return
	}

	payRequest := &abcyun.MemberCardPayRequest{
		TransactionPatientID: req.TransactionPatientID,
		Amount:               req.Amount,
		BusinessID:           req.BusinessID,
		Password:             req.Password,
		OperatorID:           req.OperatorID,
	}

	result, err := client.MemberCardPay(req.MemberCardID, payRequest)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "会员卡支付成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunMemberCardRefundHandler 会员卡退款
func (h *AbcYunHandler) AbcYunMemberCardRefundHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req MemberCardRefundRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.MemberCardID == "" {
		httpx.Error(w, errors.New("缺少会员卡ID参数"))
		return
	}

	refundRequest := &abcyun.MemberCardRefundRequest{
		Amount:         req.Amount,
		TransactionIDs: req.TransactionIDs,
		BusinessID:     req.BusinessID,
		OperatorID:     req.OperatorID,
	}

	result, err := client.MemberCardRefund(req.MemberCardID, refundRequest)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "会员卡退款成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunCreatePatientHandler 创建患者
func (h *AbcYunHandler) AbcYunCreatePatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req CreatePatientRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	createReq := &abcyun.CreatePatientRequest{
		Name:         req.Name,
		Mobile:       req.Mobile,
		Sex:          req.Sex,
		Birthday:     req.Birthday,
		SourceID:     req.SourceID,
		SourceFromID: req.SourceFromID,
		IDCard:       req.IDCard,
		PastHistory:  req.PastHistory,
		Address:      req.Address,
		SN:           req.SN,
		Remark:       req.Remark,
		Profession:   req.Profession,
		Company:      req.Company,
		Marital:      req.Marital,
		Weight:       req.Weight,
	}

	result, err := client.CreatePatient(createReq)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "创建患者成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunUpdatePatientHandler 更新患者
func (h *AbcYunHandler) AbcYunUpdatePatientHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req UpdatePatientRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.ID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	updateReq := &abcyun.CreatePatientRequest{
		Name:         req.Name,
		Mobile:       req.Mobile,
		Sex:          req.Sex,
		Birthday:     req.Birthday,
		SourceID:     req.SourceID,
		SourceFromID: req.SourceFromID,
		IDCard:       req.IDCard,
		PastHistory:  req.PastHistory,
		Address:      req.Address,
		SN:           req.SN,
		Remark:       req.Remark,
		Profession:   req.Profession,
		Company:      req.Company,
		Marital:      req.Marital,
		Weight:       req.Weight,
	}

	result, err := client.UpdatePatient(req.ID, updateReq)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "更新患者成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}

// AbcYunAddPatientAttachmentsHandler 添加患者附件
func (h *AbcYunHandler) AbcYunAddPatientAttachmentsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req AddPatientAttachmentsRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.PatientID == "" {
		httpx.Error(w, errors.New("缺少患者ID参数"))
		return
	}

	addReq := &abcyun.AddPatientAttachmentsRequest{
		Attachments: req.Attachments,
	}

	result, err := client.AddPatientAttachments(req.PatientID, addReq)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "添加患者附件成功",
		"data":    result,
	}
	httpx.OkJson(w, response)
}
