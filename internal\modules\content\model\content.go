package model

import (
	"fmt"
	"time"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// ContentType 内容类型枚举
type ContentType string

const (
	ContentTypeNews     ContentType = "news"     // 资讯
	ContentTypeActivity ContentType = "activity" // 活动
)

// ContentFormat 内容格式枚举
type ContentFormat string

const (
	ContentFormatText  ContentFormat = "text"  // 图文
	ContentFormatVideo ContentFormat = "video" // 视频
)

// Content 内容模型
type Content struct {
	ID                uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:内容ID"`
	Title             string         `json:"title" gorm:"type:varchar(90);not null;comment:内容标题(最多30个中文字符)"`
	Type              string         `json:"type" gorm:"type:varchar(20);not null;comment:内容类型:news-资讯,activity-活动"`
	Format            string         `json:"format" gorm:"type:varchar(20);not null;comment:内容格式:text-图文,video-视频"`
	Description       string         `json:"description" gorm:"type:text;comment:内容描述"`
	Content           string         `json:"content" gorm:"type:text;comment:内容详情(最多300个中文字符)"`
	CoverImage        string         `json:"cover_image" gorm:"type:varchar(500);not null;comment:封面图片URL(必填)"`
	VideoUrl          string         `json:"video_url" gorm:"type:varchar(500);comment:视频URL(视频格式时必填)"`
	IsEnabled         bool           `json:"is_enabled" gorm:"default:true;comment:是否上架"`
	IsRecommended     bool           `json:"is_recommended" gorm:"default:false;comment:是否推荐"`
	RecommendSort     int            `json:"recommend_sort" gorm:"default:0;comment:推荐排序值(从1开始,越小越靠前,0为默认)"`
	ViewCount         int            `json:"view_count" gorm:"default:0;comment:浏览次数"`
	ReportCount       int            `json:"report_count" gorm:"default:0;comment:举报次数"`
	MaxSignUp         int            `json:"max_sign_up" gorm:"default:1;comment:最大报名人数(1-10000,活动类型时使用)"`
	CanSignUp         bool           `json:"can_sign_up" gorm:"default:false;comment:是否可报名(活动类型时必填)"`
	SignUpDeadline    *time.Time     `json:"sign_up_deadline" gorm:"comment:报名截止时间"`
	SignUpCount       int            `json:"sign_up_count" gorm:"default:0;comment:已报名人数"`
	SignUpMethod      string         `json:"sign_up_method" gorm:"type:varchar(20);comment:报名方式:phone-手机号,name-姓名,idcard-身份证号"`
	SignUpAmount      int            `json:"sign_up_amount" gorm:"default:0;comment:报名费用(分,0表示免费)"`
	IsNewActivity     bool           `json:"is_new_activity" gorm:"default:true;comment:是否新创建活动(用于首次登录弹框显示)"`
	IsSpecialActivity bool           `json:"is_special_activity" gorm:"default:false;comment:是否专项活动(活动类型时使用)"`
	CreatedBy         uint           `json:"created_by" gorm:"comment:创建人ID"`
	CreatedAt         time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt         time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (c *Content) TableName() string {
	return "t_contents"
}

// ContentStoreRelation 内容与门店关联表
type ContentStoreRelation struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:关联ID"`
	ContentID uint           `json:"content_id" gorm:"column:content_id;index;not null;comment:内容ID"`
	StoreID   uint           `json:"store_id" gorm:"column:store_id;index;not null;comment:门店ID"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (c *ContentStoreRelation) TableName() string {
	return "t_content_store_relations"
}

// ContentSignUp 内容报名记录
type ContentSignUp struct {
	ID         uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:报名ID"`
	ContentID  uint           `json:"content_id" gorm:"index;not null;comment:内容ID"`
	UserID     uint           `json:"user_id" gorm:"index;not null;comment:用户ID"`
	OrderID    uint           `json:"order_id" gorm:"index;comment:订单ID"`
	Name       string         `json:"name" gorm:"type:varchar(50);comment:姓名"`
	Phone      string         `json:"phone" gorm:"type:varchar(20);comment:手机号"`
	IDCard     string         `json:"id_card" gorm:"type:varchar(30);comment:身份证号"`
	SignUpTime time.Time      `json:"sign_up_time" gorm:"autoCreateTime;comment:报名时间"`
	Status     int            `json:"status" gorm:"default:1;comment:状态:1-正常,0-取消"`
	Remark     string         `json:"remark" gorm:"type:varchar(255);comment:备注"`
	CreatedAt  time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt  time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// ContentSignUpOrder 内容报名订单
type ContentSignUpOrder struct {
	ID               uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:订单ID"`
	OrderNo          string         `json:"order_no" gorm:"type:varchar(32);uniqueIndex;not null;comment:订单号"`
	ContentID        uint           `json:"content_id" gorm:"index;not null;comment:内容ID"`
	UserID           uint           `json:"user_id" gorm:"index;not null;comment:用户ID"`
	Name             string         `json:"name" gorm:"type:varchar(50);comment:报名人姓名"`
	Phone            string         `json:"phone" gorm:"type:varchar(20);comment:报名人手机号"`
	Amount           int            `json:"amount" gorm:"not null;comment:订单金额(分)"`
	Status           int            `json:"status" gorm:"default:1;comment:订单状态:1-已报名,2-已取消,3-已取消已退款,4-已核销"`
	PaymentTime      *time.Time     `json:"payment_time" gorm:"comment:支付时间"`
	PaymentType      string         `json:"payment_type" gorm:"type:varchar(20);comment:支付方式:wechat-微信支付"`
	RefundTime       *time.Time     `json:"refund_time" gorm:"comment:退款时间"`
	RefundAmount     int            `json:"refund_amount" gorm:"default:0;comment:退款金额(分)"`
	VerificationCode string         `json:"verification_code" gorm:"type:varchar(50);index;comment:核销码"`
	VerificationTime *time.Time     `json:"verification_time" gorm:"comment:核销时间"`
	VerifierID       uint           `json:"verifier_id" gorm:"comment:核销人ID"`
	VerifierName     string         `json:"verifier_name" gorm:"type:varchar(50);comment:核销人姓名"`
	QRCodeURL        string         `json:"qr_code_url" gorm:"type:varchar(255);comment:二维码URL"`
	CreatedAt        time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt        time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (c *ContentSignUp) TableName() string {
	return "t_content_sign_ups"
}

// TableName 返回表名
func (c *ContentSignUpOrder) TableName() string {
	return "t_content_sign_up_orders"
}

// ContentRepository 内容仓库接口
type ContentRepository interface {
	// 基础CRUD
	Create(content *Content) error
	Update(content *Content) error
	Delete(id uint) error
	FindByID(id uint) (*Content, error)
	List(page, size int, query, contentType, storeID string, startTime, endTime *time.Time) ([]*Content, int64, error)

	// 内容状态管理
	UpdateStatus(id uint, isEnabled bool) error

	// 绑定门店
	BindStores(contentID uint, storeIDs []uint) error

	// 获取内容绑定的门店
	GetContentStores(contentID uint) ([]uint, error)

	// 获取门店的内容列表
	GetStoreContents(storeID uint, contentType string) ([]*Content, error)

	// 增加浏览次数
	IncrementViewCount(id uint) error

	// 添加报名记录
	AddSignUp(signUp *ContentSignUp) error

	// 获取内容的报名列表
	GetContentSignUps(contentID uint, page, size int) ([]*ContentSignUp, int64, error)

	// 检查用户是否已报名
	CheckUserSignUp(contentID, userID uint) (bool, error)

	// 报名订单相关
	CreateSignUpOrder(order *ContentSignUpOrder) error
	GetSignUpOrderByID(id uint) (*ContentSignUpOrder, error)
	GetSignUpOrdersByContentID(contentID uint, page, size int) ([]*ContentSignUpOrder, int64, error)
	UpdateSignUpOrderStatus(id uint, status int) error

	// 标记新活动为已显示
	MarkActivityAsShown(id uint) error
}

// contentRepository 内容仓库实现
type contentRepository struct{}

// NewContentRepository 创建内容仓库
func NewContentRepository() ContentRepository {
	return &contentRepository{}
}

// Create 创建内容
func (r *contentRepository) Create(content *Content) error {
	return mysql.Master().Create(content).Error
}

// Update 更新内容
func (r *contentRepository) Update(content *Content) error {
	return mysql.Master().Save(content).Error
}

// Delete 删除内容
func (r *contentRepository) Delete(id uint) error {
	// 开启事务
	tx := mysql.Master().Begin()

	// 使用GORM软删除机制删除内容
	if err := tx.Delete(&Content{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除内容与门店的关联
	if err := tx.Where("content_id = ?", id).Delete(&ContentStoreRelation{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// FindByID 根据ID查找内容
func (r *contentRepository) FindByID(id uint) (*Content, error) {
	var content Content
	err := mysql.Slave().First(&content, id).Error
	if err != nil {
		return nil, err
	}
	return &content, nil
}

// List 获取内容列表
func (r *contentRepository) List(page, size int, query, contentType, storeID string, startTime, endTime *time.Time) ([]*Content, int64, error) {
	var contents []*Content
	var total int64

	db := mysql.Slave().Model(&Content{})

	// 如果有查询条件，添加到查询中
	if query != "" {
		db = db.Where("title LIKE ?", "%"+query+"%")
	}

	// 如果指定了内容类型，添加到查询中
	if contentType != "" {
		db = db.Where("type = ?", contentType)
	}

	// 如果指定了门店，需要关联查询
	if storeID != "" {
		db = db.Joins("JOIN t_content_store_relations r ON t_contents.id = r.content_id").
			Where("r.store_id = ?", storeID)
	}

	// 如果指定了创建时间范围
	if startTime != nil {
		db = db.Where("created_at >= ?", startTime)
	}
	if endTime != nil {
		db = db.Where("created_at <= ?", endTime)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询，按创建时间最新排序
	offset := (page - 1) * size
	if err := db.Order("created_at DESC").Offset(offset).Limit(size).Find(&contents).Error; err != nil {
		return nil, 0, err
	}

	return contents, total, nil
}

// UpdateStatus 更新内容状态
func (r *contentRepository) UpdateStatus(id uint, isEnabled bool) error {
	return mysql.Master().Model(&Content{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_enabled": isEnabled,
			"updated_at": time.Now(),
		}).Error
}

// BindStores 绑定门店
func (r *contentRepository) BindStores(contentID uint, storeIDs []uint) error {
	tx := mysql.Master().Begin()

	// 删除原有关联
	if err := tx.Where("content_id = ?", contentID).Delete(&ContentStoreRelation{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 创建新关联
	if len(storeIDs) > 0 {
		relations := make([]ContentStoreRelation, 0, len(storeIDs))
		for _, storeID := range storeIDs {
			relations = append(relations, ContentStoreRelation{
				ContentID: contentID,
				StoreID:   storeID,
			})
		}

		if err := tx.Create(&relations).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetContentStores 获取内容绑定的门店
func (r *contentRepository) GetContentStores(contentID uint) ([]uint, error) {
	var relations []ContentStoreRelation
	var storeIDs []uint

	err := mysql.Slave().Where("content_id = ?", contentID).Find(&relations).Error
	if err != nil {
		return nil, err
	}

	for _, relation := range relations {
		storeIDs = append(storeIDs, relation.StoreID)
	}

	return storeIDs, nil
}

// GetStoreContents 获取门店的内容列表
func (r *contentRepository) GetStoreContents(storeID uint, contentType string) ([]*Content, error) {
	var contents []*Content

	query := mysql.Slave().Table("t_contents c").
		Joins("JOIN t_content_store_relations r ON c.id = r.content_id").
		Where("r.store_id = ?", storeID)

	if contentType != "" {
		query = query.Where("c.type = ?", contentType)
	}

	err := query.Find(&contents).Error

	return contents, err
}

// IncrementViewCount 增加浏览次数
func (r *contentRepository) IncrementViewCount(id uint) error {
	return mysql.Master().Model(&Content{}).Where("id = ?", id).
		UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error
}

// AddSignUp 添加报名记录
func (r *contentRepository) AddSignUp(signUp *ContentSignUp) error {
	tx := mysql.Master().Begin()

	// 先检查是否已报名
	var existCount int64
	tx.Model(&ContentSignUp{}).Where("content_id = ? AND user_id = ? AND status = 1", signUp.ContentID, signUp.UserID).Count(&existCount)
	if existCount > 0 {
		tx.Rollback()
		return fmt.Errorf("用户已报名该内容")
	}

	// 检查报名人数是否达到上限
	var content Content
	if err := tx.First(&content, signUp.ContentID).Error; err != nil {
		tx.Rollback()
		return err
	}

	if content.MaxSignUp > 0 && content.SignUpCount >= content.MaxSignUp {
		tx.Rollback()
		return fmt.Errorf("报名人数已达上限")
	}

	// 检查报名截止时间
	if content.SignUpDeadline != nil && time.Now().After(*content.SignUpDeadline) {
		tx.Rollback()
		return fmt.Errorf("报名已截止")
	}

	// 添加报名记录
	if err := tx.Create(signUp).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新内容报名人数
	if err := tx.Model(&Content{}).Where("id = ?", signUp.ContentID).
		UpdateColumn("sign_up_count", gorm.Expr("sign_up_count + ?", 1)).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetContentSignUps 获取内容的报名列表
func (r *contentRepository) GetContentSignUps(contentID uint, page, size int) ([]*ContentSignUp, int64, error) {
	var signUps []*ContentSignUp
	var total int64

	db := mysql.Slave().Model(&ContentSignUp{}).Where("content_id = ? AND status = 1", contentID)

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&signUps).Error; err != nil {
		return nil, 0, err
	}

	return signUps, total, nil
}

// CheckUserSignUp 检查用户是否已报名
func (r *contentRepository) CheckUserSignUp(contentID, userID uint) (bool, error) {
	var count int64
	err := mysql.Slave().Model(&ContentSignUp{}).
		Where("content_id = ? AND user_id = ? AND status = 1", contentID, userID).
		Count(&count).Error
	return count > 0, err
}

// CreateSignUpOrder 创建报名订单
func (r *contentRepository) CreateSignUpOrder(order *ContentSignUpOrder) error {
	return mysql.Master().Create(order).Error
}

// GetSignUpOrderByID 根据ID获取报名订单
func (r *contentRepository) GetSignUpOrderByID(id uint) (*ContentSignUpOrder, error) {
	var order ContentSignUpOrder
	err := mysql.Slave().First(&order, id).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// GetSignUpOrdersByContentID 获取内容的报名订单列表
func (r *contentRepository) GetSignUpOrdersByContentID(contentID uint, page, size int) ([]*ContentSignUpOrder, int64, error) {
	var orders []*ContentSignUpOrder
	var total int64

	db := mysql.Slave().Model(&ContentSignUpOrder{}).Where("content_id = ?", contentID)

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("created_at DESC").Offset(offset).Limit(size).Find(&orders).Error; err != nil {
		return nil, 0, err
	}

	return orders, total, nil
}

// UpdateSignUpOrderStatus 更新报名订单状态
func (r *contentRepository) UpdateSignUpOrderStatus(id uint, status int) error {
	return mysql.Master().Model(&ContentSignUpOrder{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error
}

// MarkActivityAsShown 标记新活动为已显示
func (r *contentRepository) MarkActivityAsShown(id uint) error {
	return mysql.Master().Model(&Content{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_new_activity": false,
			"updated_at":      time.Now(),
		}).Error
}
