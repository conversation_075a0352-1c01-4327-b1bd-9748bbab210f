package service

import (
	"context"
	"fmt"
	"time"

	"yekaitai/internal/modules/content/model"
	refundService "yekaitai/internal/modules/refund/service"
	"yekaitai/pkg/common/wechatpay"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// ActivityRefundService 活动退款服务
type ActivityRefundService struct {
	refundService *refundService.RefundService
}

// NewActivityRefundService 创建活动退款服务
func NewActivityRefundService() *ActivityRefundService {
	return &ActivityRefundService{
		refundService: refundService.NewRefundService(),
	}
}

// RefundActivityOrder 退款活动订单
func (s *ActivityRefundService) RefundActivityOrder(ctx context.Context, orderID uint, reason string) error {
	// 查询订单详情
	var order model.ContentSignUpOrder
	if err := mysql.Slave().Where("id = ?", orderID).First(&order).Error; err != nil {
		logx.Errorf("查询活动报名订单失败: %v", err)
		return fmt.Errorf("查询活动报名订单失败: %v", err)
	}

	// 检查订单状态
	if order.Status != 1 {
		return fmt.Errorf("订单状态不允许退款")
	}

	// 检查是否已经核销
	if order.VerificationTime != nil {
		return fmt.Errorf("活动已核销，无法退款")
	}

	// 如果是免费活动，直接更新状态
	if order.Amount == 0 {
		return s.updateOrderStatusToCancelled(ctx, &order, reason)
	}

	// 付费活动需要调用微信退款
	return s.processWechatRefund(ctx, &order, reason)
}

// RefundExpiredOrders 退款过期订单
func (s *ActivityRefundService) RefundExpiredOrders(ctx context.Context) error {
	logx.Info("开始处理过期活动订单退款")

	// 查询所有已报名但活动已过期的订单
	var orders []model.ContentSignUpOrder
	err := mysql.Master().
		Table("t_content_sign_up_orders o").
		Joins("JOIN t_contents c ON o.content_id = c.id").
		Where("o.status = ? AND c.sign_up_deadline < ? AND o.verification_time IS NULL", 1, time.Now()).
		Find(&orders).Error

	if err != nil {
		logx.Errorf("查询过期活动订单失败: %v", err)
		return err
	}

	logx.Infof("找到 %d 个过期活动订单", len(orders))

	// 处理每个过期订单
	successCount := 0
	for _, order := range orders {
		if err := s.processExpiredOrder(ctx, &order); err != nil {
			logx.Errorf("处理过期订单失败: orderID=%d, error=%v", order.ID, err)
			continue
		}
		successCount++
	}

	logx.Infof("过期活动订单处理完成: 成功=%d, 总数=%d", successCount, len(orders))
	return nil
}

// processExpiredOrder 处理过期订单
func (s *ActivityRefundService) processExpiredOrder(ctx context.Context, order *model.ContentSignUpOrder) error {
	logx.Infof("处理过期订单: orderID=%d, orderNo=%s, amount=%d", order.ID, order.OrderNo, order.Amount)

	// 如果是免费活动，直接更新状态
	if order.Amount == 0 {
		return s.updateOrderStatusToCancelled(ctx, order, "活动已过期")
	}

	// 付费活动需要调用微信退款
	return s.processWechatRefund(ctx, order, "活动已过期，自动退款")
}

// processWechatRefund 处理微信退款
func (s *ActivityRefundService) processWechatRefund(ctx context.Context, order *model.ContentSignUpOrder, reason string) error {
	// 获取微信退款服务
	refundService := wechatpay.GetGlobalRefundService()
	if refundService == nil {
		logx.Error("微信退款服务未初始化")
		return fmt.Errorf("微信退款服务未初始化")
	}

	// 生成退款单号
	refundNo := fmt.Sprintf("AR%s%d", order.OrderNo, time.Now().Unix())

	// 构建退款请求
	req := &wechatpay.CreateRefundRequest{
		OutRefundNo:  refundNo,
		OutTradeNo:   order.OrderNo,
		Reason:       reason,
		RefundAmount: int64(order.Amount), // 退款金额（分）
		TotalAmount:  int64(order.Amount), // 原订单金额（分）
	}

	// 调用微信退款API
	resp, err := refundService.CreateRefund(ctx, req)
	if err != nil {
		logx.Errorf("创建微信退款失败: orderID=%d, error=%v", order.ID, err)
		return fmt.Errorf("创建微信退款失败: %w", err)
	}

	// 更新订单退款信息
	now := time.Now()
	updates := map[string]interface{}{
		"status":        3,            // 已取消已退款
		"refund_time":   &now,         // 退款时间
		"refund_amount": order.Amount, // 退款金额
		"updated_at":    now,          // 更新时间
	}

	if err := mysql.Master().Model(order).Updates(updates).Error; err != nil {
		logx.Errorf("更新订单退款状态失败: orderID=%d, error=%v", order.ID, err)
		return fmt.Errorf("更新订单退款状态失败: %w", err)
	}

	logx.Infof("活动订单退款成功: orderID=%d, refundNo=%s, refundID=%s",
		order.ID, refundNo, resp.RefundID)
	return nil
}

// updateOrderStatusToCancelled 更新订单状态为已取消
func (s *ActivityRefundService) updateOrderStatusToCancelled(ctx context.Context, order *model.ContentSignUpOrder, reason string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":     2,   // 已取消
		"updated_at": now, // 更新时间
	}

	if err := mysql.Master().Model(order).Updates(updates).Error; err != nil {
		logx.Errorf("更新订单状态失败: orderID=%d, error=%v", order.ID, err)
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	logx.Infof("活动订单取消成功: orderID=%d, reason=%s", order.ID, reason)
	return nil
}

// QueryRefundStatus 查询退款状态
func (s *ActivityRefundService) QueryRefundStatus(ctx context.Context, orderID uint) (string, error) {
	// 查询订单详情
	var order model.ContentSignUpOrder
	if err := mysql.Slave().Where("id = ?", orderID).First(&order).Error; err != nil {
		logx.Errorf("查询活动报名订单失败: %v", err)
		return "", fmt.Errorf("查询活动报名订单失败: %v", err)
	}

	// 根据订单状态返回退款状态
	switch order.Status {
	case 1:
		return "未退款", nil
	case 2:
		return "已取消", nil
	case 3:
		return "已退款", nil
	case 4:
		return "已核销", nil
	default:
		return "未知状态", nil
	}
}

// CanRefund 检查订单是否可以退款
func (s *ActivityRefundService) CanRefund(ctx context.Context, orderID uint) (bool, string, error) {
	// 查询订单详情
	var order model.ContentSignUpOrder
	if err := mysql.Slave().Where("id = ?", orderID).First(&order).Error; err != nil {
		return false, "订单不存在", err
	}

	// 检查订单状态
	if order.Status != 1 {
		return false, "订单状态不允许退款", nil
	}

	// 检查是否已经核销
	if order.VerificationTime != nil {
		return false, "活动已核销，无法退款", nil
	}

	// 查询活动详情，检查是否已过期
	var activity model.Content
	if err := mysql.Slave().Where("id = ?", order.ContentID).First(&activity).Error; err != nil {
		return false, "活动不存在", err
	}

	// 检查活动是否已过期
	if activity.SignUpDeadline != nil && activity.SignUpDeadline.Before(time.Now()) {
		return true, "活动已过期，可以退款", nil
	}

	return true, "可以退款", nil
}

// GetRefundableOrders 获取可退款的订单列表
func (s *ActivityRefundService) GetRefundableOrders(ctx context.Context, userID uint) ([]model.ContentSignUpOrder, error) {
	var orders []model.ContentSignUpOrder
	err := mysql.Slave().Where("user_id = ? AND status = ? AND verification_time IS NULL", userID, 1).Find(&orders).Error
	if err != nil {
		logx.Errorf("查询可退款订单失败: %v", err)
		return nil, fmt.Errorf("查询可退款订单失败: %v", err)
	}

	return orders, nil
}

// BatchRefundExpiredOrders 批量退款过期订单
func (s *ActivityRefundService) BatchRefundExpiredOrders(ctx context.Context, batchSize int) error {
	logx.Infof("开始批量处理过期活动订单退款，批次大小: %d", batchSize)

	offset := 0
	totalProcessed := 0

	for {
		// 分批查询过期订单
		var orders []model.ContentSignUpOrder
		err := mysql.Master().
			Table("t_content_sign_up_orders o").
			Joins("JOIN t_contents c ON o.content_id = c.id").
			Where("o.status = ? AND c.sign_up_deadline < ? AND o.verification_time IS NULL", 1, time.Now()).
			Offset(offset).
			Limit(batchSize).
			Find(&orders).Error

		if err != nil {
			logx.Errorf("查询过期活动订单失败: %v", err)
			return err
		}

		// 如果没有更多订单，退出循环
		if len(orders) == 0 {
			break
		}

		// 处理当前批次的订单
		successCount := 0
		for _, order := range orders {
			if err := s.processExpiredOrder(ctx, &order); err != nil {
				logx.Errorf("处理过期订单失败: orderID=%d, error=%v", order.ID, err)
				continue
			}
			successCount++
		}

		totalProcessed += successCount
		offset += batchSize

		logx.Infof("批次处理完成: 成功=%d, 批次大小=%d, 总处理数=%d", successCount, len(orders), totalProcessed)

		// 如果当前批次少于批次大小，说明已经处理完所有订单
		if len(orders) < batchSize {
			break
		}
	}

	logx.Infof("批量过期活动订单处理完成: 总成功数=%d", totalProcessed)
	return nil
}

// CreateActivityRefund 创建活动退款（使用通用退款服务）
func (s *ActivityRefundService) CreateActivityRefund(ctx context.Context, orderNo string, amount int, reason string, operatorID uint, operatorName string) error {
	logx.Infof("创建活动退款: orderNo=%s, amount=%d, reason=%s", orderNo, amount, reason)

	// 使用通用退款服务创建退款
	req := &refundService.CreateRefundRequest{
		OrderNo:      orderNo,
		Amount:       amount,
		Reason:       reason,
		OperatorID:   operatorID,
		OperatorName: operatorName,
	}

	resp, err := s.refundService.CreateRefund(ctx, req)
	if err != nil {
		logx.Errorf("创建活动退款失败: %v", err)
		return fmt.Errorf("创建活动退款失败: %w", err)
	}

	logx.Infof("创建活动退款成功: refundNo=%s, status=%s", resp.RefundNo, resp.Status)
	return nil
}

// HandleActivityRefundCallback 处理活动退款回调
func (s *ActivityRefundService) HandleActivityRefundCallback(ctx context.Context, orderNo string, refundStatus string) error {
	logx.Infof("处理活动退款回调: orderNo=%s, status=%s", orderNo, refundStatus)

	// 查询活动订单
	var order model.ContentSignUpOrder
	if err := mysql.Master().Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		logx.Errorf("查询活动订单失败: orderNo=%s, error=%v", orderNo, err)
		return fmt.Errorf("查询活动订单失败: %w", err)
	}

	// 根据退款状态处理业务逻辑
	switch refundStatus {
	case "success":
		return s.handleRefundSuccess(ctx, &order)
	case "failed":
		return s.handleRefundFailed(ctx, &order)
	default:
		logx.Infof("活动退款状态为pending，暂不处理: orderNo=%s", orderNo)
		return nil
	}
}

// handleRefundSuccess 处理退款成功
func (s *ActivityRefundService) handleRefundSuccess(ctx context.Context, order *model.ContentSignUpOrder) error {
	logx.Infof("处理活动退款成功: orderNo=%s", order.OrderNo)

	// 更新订单状态为已退款
	updates := map[string]interface{}{
		"status":      3, // 已取消并退款
		"refund_time": time.Now(),
	}

	if err := mysql.Master().Model(order).Updates(updates).Error; err != nil {
		logx.Errorf("更新活动订单退款状态失败: %v", err)
		return fmt.Errorf("更新活动订单退款状态失败: %w", err)
	}

	// 释放活动名额
	if err := s.releaseActivityQuota(ctx, order.ContentID); err != nil {
		logx.Errorf("释放活动名额失败: activityID=%d, error=%v", order.ContentID, err)
		// 不阻断退款流程，只记录错误
	}

	logx.Infof("活动退款成功处理完成: orderNo=%s", order.OrderNo)
	return nil
}

// handleRefundFailed 处理退款失败
func (s *ActivityRefundService) handleRefundFailed(ctx context.Context, order *model.ContentSignUpOrder) error {
	logx.Errorf("活动退款失败: orderNo=%s", order.OrderNo)

	// 可以在这里添加退款失败的处理逻辑
	// 例如：发送通知、记录日志等

	return nil
}

// releaseActivityQuota 释放活动名额
func (s *ActivityRefundService) releaseActivityQuota(ctx context.Context, activityID uint) error {
	// 减少活动报名人数
	result := mysql.Master().Model(&model.Content{}).
		Where("id = ? AND sign_up_count > 0", activityID).
		UpdateColumn("sign_up_count", gorm.Expr("sign_up_count - 1"))

	if result.Error != nil {
		return fmt.Errorf("减少活动报名人数失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		logx.Infof("活动报名人数已为0，无需减少: activityID=%d", activityID)
	} else {
		logx.Infof("释放活动名额成功: activityID=%d", activityID)
	}

	return nil
}
