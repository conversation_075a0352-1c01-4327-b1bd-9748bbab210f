package routes

import (
	"net/http"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/user/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterUserRoutes 注册用户相关路由
func RegisterUserRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 用户相关路由
	server.AddRoutes(
		[]rest.Route{
			// 获取用户信息
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/user/info",
				Handler: wxAuthWrapper(handler.GetUserInfoHandler(serverCtx)),
			},
			// 获取手机号
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/user/phone",
				Handler: wxAuthWrapper(handler.GetPhoneNumberHandler(serverCtx)),
			},
			// 更新用户资料
			{
				Method:  http.MethodPut,
				Path:    "/api/wx/user/profile",
				Handler: wxAuthWrapper(handler.UpdateProfileHandler(serverCtx)),
			},
		},
	)
}
