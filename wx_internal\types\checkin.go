package types

import "time"

// 签到相关的请求和响应类型定义

// CheckinCalendarRequest 签到日历请求
type CheckinCalendarRequest struct {
	Year  int `json:"year" form:"year"`   // 年份，可选，默认当前年
	Month int `json:"month" form:"month"` // 月份，可选，默认当前月
}

// CheckinCalendarResponse 签到日历响应
type CheckinCalendarResponse struct {
	Year            int                 `json:"year"`             // 年份
	Month           int                 `json:"month"`            // 月份
	TotalCoins      int                 `json:"total_coins"`      // 当前叶小币总数
	ContinuousDays  int                 `json:"continuous_days"`  // 连续签到天数
	MonthlyCheckins int                 `json:"monthly_checkins"` // 本月签到次数
	TodayChecked    bool                `json:"today_checked"`    // 今日是否已签到
	CanCheckin      bool                `json:"can_checkin"`      // 是否可以签到
	CheckinDays     []CheckinDayInfo    `json:"checkin_days"`     // 签到日期信息
	RewardRules     []CheckinRewardRule `json:"reward_rules"`     // 奖励规则
}

// CheckinDayInfo 签到日期信息
type CheckinDayInfo struct {
	Day         int  `json:"day"`          // 日期
	IsChecked   bool `json:"is_checked"`   // 是否已签到
	IsToday     bool `json:"is_today"`     // 是否是今天
	CanCheckin  bool `json:"can_checkin"`  // 是否可以签到
	CoinsReward int  `json:"coins_reward"` // 奖励叶小币数量
}

// CheckinRewardRule 签到奖励规则
type CheckinRewardRule struct {
	Day              int `json:"day"`                // 签到天数
	CoinsReward      int `json:"coins_reward"`       // 奖励叶小币数量
	ShareCoinsReward int `json:"share_coins_reward"` // 分享奖励叶小币数量
}

// CheckinRequest 签到请求
type CheckinRequest struct {
	Date string `json:"date" form:"date"` // 签到日期，可选，默认为今天
}

// CheckinResponse 签到响应
type CheckinResponse struct {
	Success        bool   `json:"success"`         // 签到是否成功
	Message        string `json:"message"`         // 提示信息
	ContinuousDays int    `json:"continuous_days"` // 连续签到天数
	CoinsAwarded   int    `json:"coins_awarded"`   // 奖励叶小币数量
	TotalCoins     int    `json:"total_coins"`     // 签到后叶小币总数
	CanShare       bool   `json:"can_share"`       // 是否可以分享
	ShareCoins     int    `json:"share_coins"`     // 分享可获得的叶小币
}

// ShareCheckinRequest 分享签到请求
type ShareCheckinRequest struct {
	Date string `json:"date" form:"date" binding:"required"` // 分享的签到日期
}

// ShareCheckinResponse 分享签到响应
type ShareCheckinResponse struct {
	Success      bool   `json:"success"`       // 分享是否成功
	Message      string `json:"message"`       // 提示信息
	CoinsAwarded int    `json:"coins_awarded"` // 分享奖励叶小币数量
	TotalCoins   int    `json:"total_coins"`   // 分享后叶小币总数
}

// CheckinPopupResponse 签到弹窗响应
type CheckinPopupResponse struct {
	ShowPopup      bool   `json:"show_popup"`      // 是否显示弹窗
	Message        string `json:"message"`         // 弹窗消息
	TodayChecked   bool   `json:"today_checked"`   // 今日是否已签到
	ContinuousDays int    `json:"continuous_days"` // 连续签到天数
	TodayReward    int    `json:"today_reward"`    // 今日签到奖励
	CanCheckin     bool   `json:"can_checkin"`     // 是否可以签到
}

// CheckinStatsRequest 签到统计请求
type CheckinStatsRequest struct {
	StartDate string `json:"start_date" form:"start_date"` // 开始日期
	EndDate   string `json:"end_date" form:"end_date"`     // 结束日期
}

// CheckinStatsResponse 签到统计响应
type CheckinStatsResponse struct {
	TotalCheckins   int `json:"total_checkins"`   // 总签到次数
	ContinuousDays  int `json:"continuous_days"`  // 连续签到天数
	TotalCoins      int `json:"total_coins"`      // 总获得叶小币
	MonthlyCheckins int `json:"monthly_checkins"` // 本月签到次数
}

// 邀请相关的请求和响应类型定义

// InvitationCenterRequest 邀请中心请求
type InvitationCenterRequest struct {
	Page     int `json:"page" form:"page"`           // 页码，默认1
	PageSize int `json:"page_size" form:"page_size"` // 每页数量，默认10
}

// InvitationCenterResponse 邀请中心响应
type InvitationCenterResponse struct {
	TotalCoins      int                    `json:"total_coins"`      // 已获得邀请叶小币总数
	InvitationCount int                    `json:"invitation_count"` // 邀请成功总数
	InvitationRules []InvitationRule       `json:"invitation_rules"` // 邀请奖励规则
	Records         []InvitationRecordInfo `json:"records"`          // 邀请记录列表
	HasMore         bool                   `json:"has_more"`         // 是否有更多数据
	CurrentPage     int                    `json:"current_page"`     // 当前页码
	TotalPages      int                    `json:"total_pages"`      // 总页数
}

// InvitationRecordInfo 邀请记录信息
type InvitationRecordInfo struct {
	ID           uint      `json:"id"`            // 记录ID
	InviteeName  string    `json:"invitee_name"`  // 被邀请人姓名
	InviteePhone string    `json:"invitee_phone"` // 被邀请人手机号（脱敏）
	RewardType   string    `json:"reward_type"`   // 奖励类型
	CoinsAwarded int       `json:"coins_awarded"` // 奖励叶小币数量
	CompletedAt  time.Time `json:"completed_at"`  // 完成时间
	Status       int       `json:"status"`        // 状态
}

// InvitationRule 邀请奖励规则
type InvitationRule struct {
	RuleType     string `json:"rule_type"`     // 规则类型
	RuleName     string `json:"rule_name"`     // 规则名称
	Description  string `json:"description"`   // 规则描述
	CoinsAwarded int    `json:"coins_awarded"` // 奖励叶小币数量
}

// CreateInvitationResponse 创建邀请响应
type CreateInvitationResponse struct {
	InvitationCode string `json:"invitation_code"` // 邀请码
	ShareUrl       string `json:"share_url"`       // 分享链接
	ShareTitle     string `json:"share_title"`     // 分享标题
	ShareDesc      string `json:"share_desc"`      // 分享描述
}

// AcceptInvitationRequest 接受邀请请求
type AcceptInvitationRequest struct {
	InvitationCode string `json:"invitation_code" form:"invitation_code" binding:"required"` // 邀请码
}

// AcceptInvitationResponse 接受邀请响应
type AcceptInvitationResponse struct {
	Success     bool   `json:"success"`      // 是否成功
	Message     string `json:"message"`      // 提示信息
	InviterName string `json:"inviter_name"` // 邀请人姓名
	Reward      int    `json:"reward"`       // 奖励叶小币数量
}
