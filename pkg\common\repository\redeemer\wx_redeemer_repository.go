package redeemer

import (
	"context"
	"errors"
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/pkg/common/model/redeemer"
	"yekaitai/pkg/infra/mysql"
)

// WxRedeemerRepository 核销员仓库接口
type WxRedeemerRepository interface {
	// FindByID 根据ID查找核销员
	FindByID(ctx context.Context, id uint) (*redeemer.WxRedeemer, error)

	// FindByUserID 根据用户ID查找核销员
	FindByUserID(ctx context.Context, userID uint) (*redeemer.WxRedeemer, error)

	// IsValidRedeemer 验证核销员是否有效
	IsValidRedeemer(ctx context.Context, redeemerID uint, storeID uint) (bool, error)
}

// wxRedeemerRepository 核销员仓库实现
type wxRedeemerRepository struct {
	db *gorm.DB
}

// NewWxRedeemerRepository 创建核销员仓库
func NewWxRedeemerRepository() WxRedeemerRepository {
	return &wxRedeemerRepository{
		db: mysql.Slave(),
	}
}

// FindByID 根据ID查找核销员
func (r *wxRedeemerRepository) FindByID(ctx context.Context, id uint) (*redeemer.WxRedeemer, error) {
	var redeemerInfo redeemer.WxRedeemer
	if err := r.db.WithContext(ctx).Where("redeemer_id = ? AND status = 1", id).First(&redeemerInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("核销员不存在或已禁用: ID=%d", id)
		}
		logx.WithContext(ctx).Errorf("查询核销员失败: %v", err)
		return nil, err
	}
	return &redeemerInfo, nil
}

// FindByUserID 根据用户ID查找核销员
func (r *wxRedeemerRepository) FindByUserID(ctx context.Context, userID uint) (*redeemer.WxRedeemer, error) {
	var redeemerInfo redeemer.WxRedeemer
	if err := r.db.WithContext(ctx).Where("user_id = ? AND status = 1", userID).First(&redeemerInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("核销员不存在或已禁用: UserID=%d", userID)
		}
		logx.WithContext(ctx).Errorf("查询核销员失败: %v", err)
		return nil, err
	}
	return &redeemerInfo, nil
}

// IsValidRedeemer 验证核销员是否有效
func (r *wxRedeemerRepository) IsValidRedeemer(ctx context.Context, redeemerID uint, storeID uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&redeemer.WxRedeemer{}).
		Where("redeemer_id = ? AND store_id = ? AND status = 1", redeemerID, storeID).
		Count(&count).Error; err != nil {
		logx.WithContext(ctx).Errorf("验证核销员权限失败: %v", err)
		return false, err
	}
	return count > 0, nil
}
