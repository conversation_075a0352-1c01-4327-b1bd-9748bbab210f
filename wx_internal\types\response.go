package types

// StandardResponse 统一API响应格式
type StandardResponse struct {
	Code int         `json:"code"` // 状态码，200表示成功，其他值表示不同类型的错误
	Msg  string      `json:"msg"`  // 提示信息
	Data interface{} `json:"data"` // 响应数据，错误时可能为null
}

// NewSuccessResponse 创建成功响应
// 支持可选的消息参数，如果提供则使用，否则使用默认消息
func NewSuccessResponse(data interface{}, msg ...string) *StandardResponse {
	message := "操作成功"
	if len(msg) > 0 && msg[0] != "" {
		message = msg[0]
	}

	return &StandardResponse{
		Code: 200,
		Msg:  message,
		Data: data,
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code int, msg string) *StandardResponse {
	return &StandardResponse{
		Code: code,
		Msg:  msg,
		Data: nil,
	}
}

// 预定义错误码常量
const (
	CodeSuccess            = 200 // 成功
	CodeInvalidParams      = 400 // 参数错误
	CodeUnauthorized       = 401 // 未授权
	CodeForbidden          = 403 // 权限不足
	CodeNotFound           = 404 // 资源不存在
	CodeMethodNotAllowed   = 405 // 方法不允许
	CodeInternalError      = 500 // 服务器内部错误
	CodeServiceUnavailable = 503 // 服务不可用

	// 业务错误码（1000+）
	CodeUserNotFound    = 1001 // 用户不存在
	CodePasswordError   = 1002 // 密码错误
	CodeTokenExpired    = 1003 // 令牌已过期
	CodeTokenInvalid    = 1004 // 无效的令牌
	CodeDatabaseError   = 1005 // 数据库操作失败
	CodeDuplicateRecord = 1006 // 记录已存在
)

// 微信登录错误码
const (
	CodeWeChatError   = 1007 // 微信登录失败
	CodeUserForbidden = 1008 // 用户被禁用
	CodeInvalidToken  = 1009 // 无效的Token
)

// 以下是兼容旧代码的结构和方法

// Response 旧版通用响应（为向后兼容保留）
// 推荐使用StandardResponse替代
type Response struct {
	Code    int         `json:"code"`    // 状态码，0表示成功
	Message string      `json:"message"` // 提示信息
	Data    interface{} `json:"data"`    // 响应数据
}

// NewResponse 创建旧版通用响应（为向后兼容保留）
// 推荐使用NewSuccessResponse替代
func NewResponse(data interface{}) *Response {
	return &Response{
		Code:    CodeSuccess,
		Message: "success",
		Data:    data,
	}
}

// CodeError 业务错误
type CodeError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Error 实现error接口
func (e *CodeError) Error() string {
	return e.Message
}

// NewCodeError 创建业务错误
func NewCodeError(code int, msg string) *CodeError {
	return &CodeError{
		Code:    code,
		Message: msg,
	}
}
