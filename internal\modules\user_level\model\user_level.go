package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// UserLevelRule 用户等级规则
type UserLevelRule struct {
	ID          uint   `json:"id" gorm:"primaryKey;autoIncrement;comment:等级规则ID"`
	LevelName   string `json:"level_name" gorm:"type:varchar(50);not null;comment:等级名称;uniqueIndex:idx_user_level_rules_level_name"`
	LevelOrder  int    `json:"level_order" gorm:"not null;comment:等级顺序,数字越大等级越高"`
	Description string `json:"description" gorm:"type:text;comment:等级描述"`

	// 升级条件
	RequireRegister       bool    `json:"require_register" gorm:"default:false;comment:需要注册成功"`
	RequirePatientProfile bool    `json:"require_patient_profile" gorm:"default:false;comment:需要完善就诊人档案"`
	RequireConsumption    bool    `json:"require_consumption" gorm:"default:false;comment:需要消费累计"`
	ConsumptionAmount     float64 `json:"consumption_amount" gorm:"type:decimal(10,2);default:0;comment:消费累计金额"`

	// 会员权益
	FreeActivitySignup       bool    `json:"free_activity_signup" gorm:"default:false;comment:活动报名费免费"`
	RegistrationFeeReduction float64 `json:"registration_fee_reduction" gorm:"type:decimal(10,2);default:0;comment:挂号费减免金额(元)"`
	ExtraCoins               int     `json:"extra_coins" gorm:"default:0;comment:额外赠送叶小币"`
	ProductDiscount          float64 `json:"product_discount" gorm:"type:decimal(5,2);default:0;comment:商品折扣比例(9.5表示9.5折)"`

	// 赠送内容配置
	GiftCoupons string `json:"gift_coupons" gorm:"type:text;comment:赠送优惠券配置(JSON格式)"`

	// 创建信息
	CreatorID uint `json:"creator_id" gorm:"default:0;comment:创建人ID"`

	// 时间戳
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"uniqueIndex:idx_user_level_rules_level_name;index;comment:删除时间"`
}

// TableName 返回表名
func (ulr *UserLevelRule) TableName() string {
	return "user_level_rules"
}

// UserLevelCouponGift 等级赠送优惠券配置
type UserLevelCouponGift struct {
	CouponID uint `json:"coupon_id"` // 优惠券ID
	Quantity int  `json:"quantity"`  // 赠送数量
}

// UserLevelServiceGift 等级赠送服务配置
type UserLevelServiceGift struct {
	ServiceID uint `json:"service_id"` // 服务ID
	Times     int  `json:"times"`      // 赠送次数
}

// UserLevelUpgradeLog 用户等级升级日志
type UserLevelUpgradeLog struct {
	ID            uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:升级日志ID"`
	UserID        uint      `json:"user_id" gorm:"index;not null;comment:用户ID"`
	FromLevelID   *uint     `json:"from_level_id" gorm:"comment:原等级ID"`
	ToLevelID     uint      `json:"to_level_id" gorm:"not null;comment:目标等级ID"`
	FromLevelName string    `json:"from_level_name" gorm:"type:varchar(50);comment:原等级名称"`
	ToLevelName   string    `json:"to_level_name" gorm:"type:varchar(50);not null;comment:目标等级名称"`
	UpgradeReason string    `json:"upgrade_reason" gorm:"type:varchar(200);comment:升级原因"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime;comment:升级时间"`
}

// TableName 返回表名
func (ull *UserLevelUpgradeLog) TableName() string {
	return "user_level_upgrade_logs"
}

// UserLevelRepository 用户等级仓库接口
type UserLevelRepository interface {
	// 基础CRUD
	Create(level *UserLevelRule) error
	Update(level *UserLevelRule) error
	Delete(id uint) error
	FindByID(id uint) (*UserLevelRule, error)
	FindByName(levelName string) (*UserLevelRule, error)
	List(page, size int, levelName, startDate, endDate string) ([]*UserLevelRule, int64, error)

	// 等级排序
	UpdateLevelOrder(id uint, order int) error
	GetMaxLevelOrder() (int, error)

	// 升级日志
	CreateUpgradeLog(log *UserLevelUpgradeLog) error
	GetUpgradeLogs(userID uint, page, size int) ([]*UserLevelUpgradeLog, int64, error)

	// 用户等级计算
	CalculateUserLevel(userID uint) (*UserLevelRule, error)
	BatchUpdateUserLevels() error
}

// userLevelRepository 用户等级仓库实现
type userLevelRepository struct {
	db *gorm.DB
}

// NewUserLevelRepository 创建用户等级仓库
func NewUserLevelRepository(db *gorm.DB) UserLevelRepository {
	return &userLevelRepository{
		db: db,
	}
}

// Create 创建等级规则
func (r *userLevelRepository) Create(level *UserLevelRule) error {
	return r.db.Create(level).Error
}

// Update 更新等级规则
func (r *userLevelRepository) Update(level *UserLevelRule) error {
	return r.db.Save(level).Error
}

// Delete 删除等级规则
func (r *userLevelRepository) Delete(id uint) error {
	return r.db.Delete(&UserLevelRule{}, id).Error
}

// FindByID 根据ID查找等级规则
func (r *userLevelRepository) FindByID(id uint) (*UserLevelRule, error) {
	var level UserLevelRule
	err := r.db.First(&level, id).Error
	if err != nil {
		return nil, err
	}
	return &level, nil
}

// FindByName 根据名称查找等级规则
func (r *userLevelRepository) FindByName(levelName string) (*UserLevelRule, error) {
	var level UserLevelRule
	err := r.db.Where("level_name = ?", levelName).First(&level).Error
	if err != nil {
		return nil, err
	}
	return &level, nil
}

// List 获取等级规则列表
func (r *userLevelRepository) List(page, size int, levelName, startDate, endDate string) ([]*UserLevelRule, int64, error) {
	var levels []*UserLevelRule
	var total int64

	query := r.db.Model(&UserLevelRule{})

	// 筛选条件
	if levelName != "" {
		query = query.Where("level_name LIKE ?", "%"+levelName+"%")
	}
	if startDate != "" {
		query = query.Where("DATE(created_at) >= ?", startDate)
	}
	if endDate != "" {
		query = query.Where("DATE(created_at) <= ?", endDate)
	}

	// 获取总数
	query.Count(&total)

	// 分页查询
	offset := (page - 1) * size
	err := query.Offset(offset).Limit(size).Order("level_order ASC, created_at DESC").Find(&levels).Error

	return levels, total, err
}

// UpdateLevelOrder 更新等级排序
func (r *userLevelRepository) UpdateLevelOrder(id uint, order int) error {
	return r.db.Model(&UserLevelRule{}).Where("id = ?", id).Update("level_order", order).Error
}

// GetMaxLevelOrder 获取最大等级排序值
func (r *userLevelRepository) GetMaxLevelOrder() (int, error) {
	var maxOrder int
	err := r.db.Model(&UserLevelRule{}).Select("COALESCE(MAX(level_order), 0)").Scan(&maxOrder).Error
	return maxOrder, err
}

// CreateUpgradeLog 创建升级日志
func (r *userLevelRepository) CreateUpgradeLog(log *UserLevelUpgradeLog) error {
	return r.db.Create(log).Error
}

// GetUpgradeLogs 获取升级日志
func (r *userLevelRepository) GetUpgradeLogs(userID uint, page, size int) ([]*UserLevelUpgradeLog, int64, error) {
	var logs []*UserLevelUpgradeLog
	var total int64

	query := r.db.Model(&UserLevelUpgradeLog{}).Where("user_id = ?", userID)

	// 获取总数
	query.Count(&total)

	// 分页查询
	offset := (page - 1) * size
	err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&logs).Error

	return logs, total, err
}

// CalculateUserLevel 计算用户等级
func (r *userLevelRepository) CalculateUserLevel(userID uint) (*UserLevelRule, error) {
	// 1. 获取用户基本信息
	var user struct {
		UserID            uint    `json:"user_id"`
		IsRegistered      bool    `json:"is_registered"`
		HasPatientProfile bool    `json:"has_patient_profile"`
		TotalConsumption  float64 `json:"total_consumption"`
	}

	// 查询用户注册状态和就诊人档案完善情况
	userQuery := `
		SELECT 
			u.user_id,
			CASE WHEN u.user_id IS NOT NULL THEN 1 ELSE 0 END as is_registered,
			CASE WHEN COUNT(p.patient_id) > 0 THEN 1 ELSE 0 END as has_patient_profile,
			COALESCE(
				(SELECT total_amount FROM wx_user_annual_consumption 
				WHERE user_id = u.user_id AND year = YEAR(CURDATE()) 
				AND deleted_at IS NULL LIMIT 1),
				0
			) as total_consumption
		FROM wx_user u
		LEFT JOIN wx_patient p ON u.user_id = p.user_id AND p.deleted_at IS NULL
		WHERE u.user_id = ? AND u.deleted_at IS NULL
		GROUP BY u.user_id
	`

	if err := r.db.Raw(userQuery, userID).Scan(&user).Error; err != nil {
		return nil, fmt.Errorf("查询用户信息失败: %w", err)
	}

	// 如果用户不存在，返回nil
	if user.UserID == 0 {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 获取所有活跃的等级规则，按等级从高到低排序
	var levels []*UserLevelRule
	if err := r.db.Order("level_order DESC").Find(&levels).Error; err != nil {
		return nil, fmt.Errorf("查询等级规则失败: %w", err)
	}

	// 3. 按等级顺序从高到低匹配条件，返回符合条件的最高等级
	for _, level := range levels {
		if r.checkUserMeetsLevelRequirements(user, level) {
			return level, nil
		}
	}

	// 如果没有符合条件的等级，返回最低等级（如果存在）
	if len(levels) > 0 {
		minLevel := levels[len(levels)-1]
		for _, level := range levels {
			if level.LevelOrder < minLevel.LevelOrder {
				minLevel = level
			}
		}
		return minLevel, nil
	}

	return nil, fmt.Errorf("没有可用的等级规则")
}

// checkUserMeetsLevelRequirements 检查用户是否满足等级要求
func (r *userLevelRepository) checkUserMeetsLevelRequirements(user struct {
	UserID            uint    `json:"user_id"`
	IsRegistered      bool    `json:"is_registered"`
	HasPatientProfile bool    `json:"has_patient_profile"`
	TotalConsumption  float64 `json:"total_consumption"`
}, level *UserLevelRule) bool {
	// 检查注册要求
	if level.RequireRegister && !user.IsRegistered {
		return false
	}

	// 检查就诊人档案要求
	if level.RequirePatientProfile && !user.HasPatientProfile {
		return false
	}

	// 检查累积消费要求
	if level.RequireConsumption && user.TotalConsumption < level.ConsumptionAmount {
		return false
	}

	return true
}

// BatchUpdateUserLevels 批量更新用户等级
func (r *userLevelRepository) BatchUpdateUserLevels() error {
	// 获取所有用户ID
	var userIDs []uint
	if err := r.db.Table("wx_user").Where("deleted_at IS NULL").Pluck("user_id", &userIDs).Error; err != nil {
		return fmt.Errorf("获取用户列表失败: %w", err)
	}

	// 批量处理用户等级更新
	for _, userID := range userIDs {
		// 计算用户应有的等级
		newLevel, err := r.CalculateUserLevel(userID)
		if err != nil {
			// 记录错误但继续处理其他用户
			fmt.Printf("计算用户%d等级失败: %v\n", userID, err)
			continue
		}

		// 获取用户当前等级
		var currentLevelID *uint
		if err := r.db.Table("wx_user").
			Where("user_id = ?", userID).
			Pluck("user_level_id", &currentLevelID).Error; err != nil {
			fmt.Printf("获取用户%d当前等级失败: %v\n", userID, err)
			continue
		}

		// 如果等级有变化，更新用户等级并记录日志
		if currentLevelID == nil || *currentLevelID != newLevel.ID {
			// 更新用户等级
			if err := r.db.Table("wx_user").
				Where("user_id = ?", userID).
				Updates(map[string]interface{}{
					"user_level_id": newLevel.ID,
					"user_level":    newLevel.LevelOrder,
				}).Error; err != nil {
				fmt.Printf("更新用户%d等级失败: %v\n", userID, err)
				continue
			}

			// 记录升级日志
			var currentLevelName string
			if currentLevelID != nil {
				var currentLevel UserLevelRule
				if err := r.db.First(&currentLevel, *currentLevelID).Error; err == nil {
					currentLevelName = currentLevel.LevelName
				}
			}

			upgradeLog := &UserLevelUpgradeLog{
				UserID:        userID,
				FromLevelID:   currentLevelID,
				ToLevelID:     newLevel.ID,
				FromLevelName: currentLevelName,
				ToLevelName:   newLevel.LevelName,
				UpgradeReason: "系统自动计算等级变更",
			}

			if err := r.CreateUpgradeLog(upgradeLog); err != nil {
				fmt.Printf("记录用户%d等级升级日志失败: %v\n", userID, err)
			}
		}
	}

	return nil
}
