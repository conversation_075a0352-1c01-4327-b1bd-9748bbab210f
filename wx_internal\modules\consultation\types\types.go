package types

// PreDiagnosisInitRequest 预问诊初始化请求参数
type PreDiagnosisInitRequest struct {
	ModelID   int    `json:"model_id"`  // 模型ID，400表示预问诊
	SessionID string `json:"sessionid"` // 会话ID，可选，不传则自动生成
}

// PreDiagnosisRequest 预问诊请求参数
type PreDiagnosisRequest struct {
	ModelID   int    `json:"model_id"`  // 模型ID，400表示预问诊
	Content   string `json:"content"`   // 用户输入内容
	SessionID string `json:"sessionid"` // 会话ID
}

// PreDiagnosisResponse 预问诊响应
type PreDiagnosisResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		WebSocketURL string `json:"websocket_url"` // WebSocket连接URL
		SessionID    string `json:"sessionid"`     // 会话ID
	} `json:"data"`
}

// InitConsultationRequest 初始化咨询请求参数
type InitConsultationRequest struct {
	ModelID int `json:"model_id"` // 模型ID: 399-健康咨询, 400-预问诊, 401-报告解读, 403-智能分导诊, 405-智能客服
}

// InitConsultationResponse 初始化咨询响应
type InitConsultationResponse struct {
	SessionID    string `json:"session_id"`    // 会话ID
	WebSocketURL string `json:"websocket_url"` // WebSocket连接URL
	ModelID      int    `json:"model_id"`      // 模型ID
	ModelType    string `json:"model_type"`    // 模型类型
}

// WebSocketMessage WebSocket消息格式
type WebSocketMessage struct {
	Type            string        `json:"type"`                       // 消息类型: chunk, full, error, welcome, medical_card, report_image
	Content         string        `json:"content"`                    // 消息内容（普通文本消息）
	SessionID       string        `json:"sessionid"`                  // 会话ID
	IsFinal         bool          `json:"is_final"`                   // 是否为最后一条消息
	ModelID         int           `json:"model_id"`                   // 模型ID
	MsgType         int           `json:"msg_type,omitempty"`         // 医联消息类型，366表示病历卡片，407表示图片消息
	CardData        *MedicalCard  `json:"card_data,omitempty"`        // 病历卡片数据（当msgType=366时使用）
	ImageData       *ReportImages `json:"image_data,omitempty"`       // 报告图片数据（当msgType=407时使用）
	RawContent      *RawContent   `json:"raw_content,omitempty"`      // 原始内容数据（用于复杂消息类型）
	LocationData    *LocationInfo `json:"location_data,omitempty"`    // 位置信息（智能导诊使用）
	RecommendDoctor bool          `json:"recommend_doctor,omitempty"` // 是否推荐医生（智能导诊使用）
}

// LocationInfo 位置信息
type LocationInfo struct {
	Longitude string `json:"longitude"` // 经度
	Latitude  string `json:"latitude"`  // 纬度
}

// MedicalCard 病历卡片数据结构
type MedicalCard struct {
	PromptTitle string            `json:"prompt_title"` // 病历标题，如"预问诊病历"
	CardMsg     []MedicalCardItem `json:"card_msg"`     // 病历信息项列表
}

// MedicalCardItem 病历信息项
type MedicalCardItem struct {
	Type           int    `json:"type"`                      // 字段类型：2-标题，3-总结语
	SubType        int    `json:"sub_type,omitempty"`        // 子类型（当type=2时使用）
	SubName        string `json:"sub_name,omitempty"`        // 名称（当type=2时使用）
	SubContent     string `json:"sub_content,omitempty"`     // 内容（当type=2时使用）
	SummaryType    int    `json:"summary_type,omitempty"`    // 总结类型（当type=3时使用）
	SummaryContent string `json:"summary_content,omitempty"` // 总结内容（当type=3时使用）
}

// ReportImages 报告图片数据结构（用于msgType=407）
type ReportImages struct {
	Content string   `json:"content"` // 用户输入的文本内容，如"请解读一下报告"
	Images  []string `json:"images"`  // 报告图片URL列表，支持1-3张图片
}

// RawContent 原始内容数据结构（用于复杂消息类型）
type RawContent struct {
	Content string   `json:"content"`          // 文本内容
	Images  []string `json:"images,omitempty"` // 图片URL列表（可选）
	MsgType int      `json:"msg_type"`         // 消息类型：1-普通文本，407-图文混合
}

// SessionHistoryItem 会话历史记录项
type SessionHistoryItem struct {
	SessionID     string `json:"session_id"`     // 会话ID
	ModelID       int    `json:"model_id"`       // 模型ID
	ModelType     string `json:"model_type"`     // 模型类型
	Title         string `json:"title"`          // 会话标题
	FirstQuestion string `json:"first_question"` // 首次提问内容
	LastMessage   string `json:"last_message"`   // 最后一条消息
	MessageCount  int    `json:"message_count"`  // 消息数量
	Status        int    `json:"status"`         // 状态(1-进行中,2-已结束)
	StartTime     string `json:"start_time"`     // 开始时间
	UpdatedAt     string `json:"updated_at"`     // 更新时间
}

// SessionHistoryResponse 会话历史记录响应
type SessionHistoryResponse struct {
	List     []SessionHistoryItem `json:"list"`      // 会话列表
	Page     int                  `json:"page"`      // 当前页码
	PageSize int                  `json:"page_size"` // 每页大小
	Total    int                  `json:"total"`     // 总数量
}

// SessionDetailRequest 会话详情请求
type SessionDetailRequest struct {
	SessionID string `json:"session_id"` // 会话ID
}

// SessionDetailResponse 会话详情响应
type SessionDetailResponse struct {
	SessionID   string `json:"session_id"`   // 会话ID
	ModelID     int    `json:"model_id"`     // 模型ID
	ModelType   string `json:"model_type"`   // 模型类型
	Title       string `json:"title"`        // 会话标题
	FullContent string `json:"full_content"` // 完整会话内容
	Status      int    `json:"status"`       // 状态
	StartTime   string `json:"start_time"`   // 开始时间
	EndTime     string `json:"end_time"`     // 结束时间
}

// SmartTriageRequest 智能导诊请求
type SmartTriageRequest struct {
	Symptoms                 string        `json:"symptoms"`                   // 症状描述
	NeedDoctorRecommendation bool          `json:"need_doctor_recommendation"` // 是否需要推荐医生
	Location                 *LocationInfo `json:"location,omitempty"`         // 位置信息（推荐医生时需要）
	Age                      int           `json:"age,omitempty"`              // 年龄（可选）
	Gender                   string        `json:"gender,omitempty"`           // 性别（可选）
	MedicalHistory           string        `json:"medical_history,omitempty"`  // 病史（可选）
}

// SmartTriageResponse 智能导诊响应
type SmartTriageResponse struct {
	SessionID             string                     `json:"session_id"`             // 会话ID
	Analysis              string                     `json:"analysis"`               // 症状分析
	Recommendations       []DepartmentRecommendation `json:"recommendations"`        // 科室推荐
	DoctorRecommendations []DoctorRecommendation     `json:"doctor_recommendations"` // 医生推荐
	Confidence            float64                    `json:"confidence"`             // 置信度
	Suggestions           []string                   `json:"suggestions"`            // 建议
}

// DepartmentRecommendation 科室推荐
type DepartmentRecommendation struct {
	DepartmentID   string  `json:"department_id"`   // 科室ID
	DepartmentName string  `json:"department_name"` // 科室名称
	Reason         string  `json:"reason"`          // 推荐理由
	Confidence     float64 `json:"confidence"`      // 置信度
	Priority       int     `json:"priority"`        // 优先级
}

// DoctorRecommendation 医生推荐
type DoctorRecommendation struct {
	DoctorID     string  `json:"doctor_id"`    // 医生ID
	DoctorName   string  `json:"doctor_name"`  // 医生姓名
	Department   string  `json:"department"`   // 所属科室
	Title        string  `json:"title"`        // 职称
	Specialty    string  `json:"specialty"`    // 专长
	Hospital     string  `json:"hospital"`     // 医院
	Distance     float64 `json:"distance"`     // 距离（米）
	Rating       float64 `json:"rating"`       // 评分
	Introduction string  `json:"introduction"` // 简介
	Avatar       string  `json:"avatar"`       // 头像
}
