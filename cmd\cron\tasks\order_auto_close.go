package tasks

import (
	"context"
	"fmt"
	"time"

	orderModel "yekaitai/pkg/common/model/order"
	"yekaitai/pkg/common/wechatpay"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// OrderAutoCloseService 订单自动关闭服务
type OrderAutoCloseService struct {
	db *gorm.DB
}

// NewOrderAutoCloseService 创建订单自动关闭服务
func NewOrderAutoCloseService() *OrderAutoCloseService {
	return &OrderAutoCloseService{
		db: mysql.GetDB(),
	}
}

// CloseExpiredOrders 关闭过期的待支付订单
func (s *OrderAutoCloseService) CloseExpiredOrders() error {
	logx.Info("开始检查并关闭过期的待支付订单...")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 查询超过15分钟未支付的订单（与微信支付参数过期时间一致）
	expireTime := time.Now().Add(-15 * time.Minute)

	var expiredOrders []orderModel.Order
	err := s.db.WithContext(ctx).
		Where("status = ? AND created_at < ?", orderModel.OrderStatusPending, expireTime).
		Find(&expiredOrders).Error

	if err != nil {
		logx.Errorf("查询过期订单失败: %v", err)
		return err
	}

	if len(expiredOrders) == 0 {
		logx.Info("没有找到过期的待支付订单")
		return nil
	}

	logx.Infof("找到 %d 个过期的待支付订单，开始处理...", len(expiredOrders))

	successCount := 0
	failCount := 0

	for _, order := range expiredOrders {
		err := s.closeExpiredOrder(ctx, &order)
		if err != nil {
			logx.Errorf("关闭过期订单失败: 订单号=%s, 错误=%v", order.OrderNo, err)
			failCount++
		} else {
			logx.Infof("关闭过期订单成功: 订单号=%s", order.OrderNo)
			successCount++
		}
	}

	logx.Infof("过期订单处理完成: 成功=%d, 失败=%d", successCount, failCount)
	return nil
}

// closeExpiredOrder 关闭单个过期订单
func (s *OrderAutoCloseService) closeExpiredOrder(ctx context.Context, order *orderModel.Order) error {
	// 开启事务
	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 更新订单状态为已取消
	err := tx.Model(order).Updates(map[string]interface{}{
		"status":        orderModel.OrderStatusCancelled,
		"cancel_time":   time.Now(),
		"cancel_reason": "支付超时自动取消",
		"updated_at":    time.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 2. 恢复商品库存（如果有库存管理）
	err = s.restoreGoodsStock(ctx, tx, order)
	if err != nil {
		logx.Errorf("恢复商品库存失败: 订单号=%s, 错误=%v", order.OrderNo, err)
		// 库存恢复失败不回滚整个事务，只记录日志
	}

	// 3. 恢复优惠券和叶小币（修正：自动关闭订单时需要恢复）
	err = s.restoreCouponAndPoints(ctx, tx, order)
	if err != nil {
		logx.Errorf("恢复优惠券和叶小币失败: 订单号=%s, 错误=%v", order.OrderNo, err)
		// 恢复失败不回滚整个事务，只记录日志
	}

	// 3. 关闭微信支付订单
	err = s.closeWechatPayOrder(ctx, order.OrderNo)
	if err != nil {
		logx.Errorf("关闭微信支付订单失败: 订单号=%s, 错误=%v", order.OrderNo, err)
		// 微信支付关闭失败不回滚整个事务，只记录日志
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// restoreGoodsStock 恢复商品库存
func (s *OrderAutoCloseService) restoreGoodsStock(ctx context.Context, tx *gorm.DB, order *orderModel.Order) error {
	// 查询订单商品
	var orderItems []orderModel.OrderItem
	err := tx.Where("order_id = ?", order.ID).Find(&orderItems).Error
	if err != nil {
		return err
	}

	// 恢复每个商品的库存
	for _, item := range orderItems {
		// 如果有规格，恢复规格库存
		if item.SpecID > 0 {
			err = tx.Table("goods_spec").
				Where("id = ?", item.SpecID).
				UpdateColumn("stock", gorm.Expr("stock + ?", item.Quantity)).Error
			if err != nil {
				logx.Errorf("恢复规格库存失败: 规格ID=%d, 数量=%d, 错误=%v", item.SpecID, item.Quantity, err)
			}
		} else {
			// 恢复商品库存
			err = tx.Table("goods").
				Where("id = ?", item.GoodsID).
				UpdateColumn("stock", gorm.Expr("stock + ?", item.Quantity)).Error
			if err != nil {
				logx.Errorf("恢复商品库存失败: 商品ID=%d, 数量=%d, 错误=%v", item.GoodsID, item.Quantity, err)
			}
		}
	}

	return nil
}

// restoreCouponAndPoints 恢复优惠券和叶小币（自动关闭订单时调用）
func (s *OrderAutoCloseService) restoreCouponAndPoints(ctx context.Context, tx *gorm.DB, order *orderModel.Order) error {
	// 1. 恢复优惠券
	if order.CouponID > 0 {
		err := tx.Table("user_coupons").
			Where("id = ? AND user_id = ? AND status = 1 AND order_id = ?", order.CouponID, order.UserID, order.ID).
			Updates(map[string]interface{}{
				"status":     0,   // 恢复为未使用
				"used_at":    nil, // 清空使用时间
				"order_id":   nil, // 清空订单关联
				"updated_at": time.Now(),
			}).Error
		if err != nil {
			logx.Errorf("恢复优惠券失败: 订单号=%s, 优惠券ID=%d, 错误=%v", order.OrderNo, order.CouponID, err)
			return fmt.Errorf("恢复优惠券失败: %w", err)
		}
		logx.Infof("恢复优惠券成功: 订单号=%s, 优惠券ID=%d", order.OrderNo, order.CouponID)
	}

	// 2. 恢复叶小币
	if order.UsePoints > 0 {
		err := tx.Table("user_coins").
			Where("user_id = ?", order.UserID).
			Updates(map[string]interface{}{
				"available_coins": gorm.Expr("available_coins + ?", order.UsePoints),
				"used_coins":      gorm.Expr("used_coins - ?", order.UsePoints),
				"updated_at":      time.Now(),
			}).Error
		if err != nil {
			logx.Errorf("恢复叶小币失败: 订单号=%s, 用户ID=%d, 使用积分=%d, 错误=%v", order.OrderNo, order.UserID, order.UsePoints, err)
			return fmt.Errorf("恢复叶小币失败: %w", err)
		}
		logx.Infof("恢复叶小币成功: 订单号=%s, 用户ID=%d, 使用积分=%d", order.OrderNo, order.UserID, order.UsePoints)
	}

	return nil
}

// closeWechatPayOrder 关闭微信支付订单
func (s *OrderAutoCloseService) closeWechatPayOrder(ctx context.Context, orderNo string) error {
	paymentService := wechatpay.GetGlobalPaymentService()
	if paymentService == nil {
		logx.Errorf("微信支付服务未初始化，跳过关闭微信支付订单: %s", orderNo)
		return nil
	}

	req := &wechatpay.CloseOrderRequest{
		OutTradeNo: orderNo,
	}

	err := paymentService.CloseOrder(ctx, req)
	if err != nil {
		// 如果订单已经支付或已关闭，微信会返回错误，这是正常的
		logx.Infof("关闭微信支付订单: 订单号=%s, 结果=%v", orderNo, err)
		return nil // 不返回错误，避免影响整个流程
	}

	logx.Infof("关闭微信支付订单成功: 订单号=%s", orderNo)
	return nil
}

// GetExpiredOrdersCount 获取过期订单数量（用于监控）
func (s *OrderAutoCloseService) GetExpiredOrdersCount() (int64, error) {
	expireTime := time.Now().Add(-15 * time.Minute)

	var count int64
	err := s.db.Model(&orderModel.Order{}).
		Where("status = ? AND created_at < ?", orderModel.OrderStatusPending, expireTime).
		Count(&count).Error

	return count, err
}
