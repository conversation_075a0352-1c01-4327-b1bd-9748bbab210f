package bootstrap

import (
	"log"

	"yekaitai/pkg/common/model/disease"
	"yekaitai/pkg/infra/mysql"
)

// MigrateDiseaseTables 执行疾病库相关表结构迁移
func MigrateDiseaseTables() error {
	log.Println("开始执行疾病库表结构迁移...")

	// 执行疾病库表结构迁移
	db := mysql.Master()

	// 迁移疾病库表
	err := db.Set("gorm:table_options", "COMMENT='疾病库表'").AutoMigrate(&disease.Disease{})
	if err != nil {
		log.Printf("疾病库表迁移失败: %v", err)
		return err
	}

	log.Println("疾病库表结构迁移完成")
	return nil
}
