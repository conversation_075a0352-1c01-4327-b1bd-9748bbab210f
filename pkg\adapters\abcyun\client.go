package abcyun

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"yekaitai/internal/config"
)

// AbcYunClient 是ABC云API客户端结构体
type AbcYunClient struct {
	BaseURL     string
	AppID       string
	AppSecret   string
	ClinicID    string // 诊所ID
	AccessToken string
	ExpiresAt   time.Time
	HTTPClient  *http.Client
	tokenMutex  sync.RWMutex
}

// AuthResponse 授权响应
type AuthResponse struct {
	Data struct {
		AccessToken    string `json:"accessToken"`
		TokenType      string `json:"tokenType"`
		ExpiresIn      int    `json:"expiresIn"`
		ExpirationTime int64  `json:"expirationTime"`
	} `json:"data"`
}

// NewClient 创建新的ABC云客户端
func NewClient(conf *config.AbcYunConfig) *AbcYunClient {
	// 确保BaseURL完整，没有结尾的斜杠
	baseURL := conf.BaseURL
	// 如果BaseURL为空，设置默认值
	if baseURL == "" {
		baseURL = "https://open-region2.abcyun.cn"
	}
	// 移除URL末尾的斜杠
	baseURL = strings.TrimSuffix(baseURL, "/")

	return &AbcYunClient{
		BaseURL:    baseURL,
		AppID:      conf.AppID,
		AppSecret:  conf.AppSecret,
		ClinicID:   conf.ClinicID,
		HTTPClient: &http.Client{Timeout: 10 * time.Second},
	}
}

// GetAccessToken 获取访问令牌
func (c *AbcYunClient) GetAccessToken() (string, error) {
	c.tokenMutex.RLock()
	token := c.AccessToken
	expireTime := c.ExpiresAt
	c.tokenMutex.RUnlock()

	// 如果令牌存在且未过期，直接返回
	if token != "" && expireTime.After(time.Now()) {
		return token, nil
	}

	// 否则重新获取令牌
	c.tokenMutex.Lock()
	defer c.tokenMutex.Unlock()

	// 双重检查，防止在等待锁期间另一个goroutine已经更新了令牌
	if c.AccessToken != "" && c.ExpiresAt.After(time.Now()) {
		return c.AccessToken, nil
	}

	// 构建请求参数
	params := map[string]interface{}{
		"appId":     c.AppID,
		"appSecret": c.AppSecret,
		"grantType": "client_credentials",
	}

	// 如果有诊所ID，添加到请求参数
	if c.ClinicID != "" {
		params["clinicId"] = c.ClinicID
	}

	// 发送请求获取令牌
	url := c.BaseURL + "/api/v2/auth/token"
	jsonData, err := json.Marshal(params)
	if err != nil {
		return "", fmt.Errorf("序列化请求参数失败: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("获取令牌失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	var authResp AuthResponse
	if err := json.Unmarshal(body, &authResp); err != nil {
		return "", fmt.Errorf("解析响应失败: %w", err)
	}

	// 更新令牌和过期时间
	c.AccessToken = authResp.Data.AccessToken
	// 设置过期时间提前5分钟，以防止边界问题
	c.ExpiresAt = time.Unix(authResp.Data.ExpirationTime, 0).Add(-5 * time.Minute)

	return c.AccessToken, nil
}

// GenerateSign 生成签名
func (c *AbcYunClient) GenerateSign(path string, params map[string]string, isGet bool) string {
	// 复制参数，添加公共参数
	signParams := make(map[string]string)
	for k, v := range params {
		if v != "" { // 空值不参与签名
			signParams[k] = v
		}
	}

	// 添加公共参数
	signParams["appId"] = c.AppID
	signParams["path"] = path
	signParams["ts"] = strconv.FormatInt(time.Now().Unix(), 10)

	// GET请求需包含所有query参数
	if isGet {
		// 按字典序排序参数
		var keys []string
		for k := range signParams {
			keys = append(keys, k)
		}
		sort.Strings(keys)

		// 构建签名字符串
		var signStr strings.Builder
		for _, k := range keys {
			signStr.WriteString(k)
			signStr.WriteString("=")
			signStr.WriteString(signParams[k])
			signStr.WriteString("&")
		}
		// 添加appSecret
		signStr.WriteString("appSecret=")
		signStr.WriteString(c.AppSecret)

		// 计算MD5签名
		hash := md5.Sum([]byte(signStr.String()))
		return strings.ToUpper(hex.EncodeToString(hash[:]))
	} else {
		// 非GET请求只使用公共参数
		var keys []string
		for k := range signParams {
			if k == "appId" || k == "path" || k == "ts" {
				keys = append(keys, k)
			}
		}
		sort.Strings(keys)

		// 构建签名字符串
		var signStr strings.Builder
		for _, k := range keys {
			signStr.WriteString(k)
			signStr.WriteString("=")
			signStr.WriteString(signParams[k])
			signStr.WriteString("&")
		}
		// 添加appSecret
		signStr.WriteString("appSecret=")
		signStr.WriteString(c.AppSecret)

		// 计算MD5签名
		hash := md5.Sum([]byte(signStr.String()))
		return strings.ToUpper(hex.EncodeToString(hash[:]))
	}
}

// Get 发送GET请求
func (c *AbcYunClient) Get(path string, queryParams map[string]string) ([]byte, error) {
	// 获取访问令牌
	token, err := c.GetAccessToken()
	if err != nil {
		return nil, err
	}

	// 构建URL
	apiURL := c.BaseURL + path
	if queryParams != nil && len(queryParams) > 0 {
		values := url.Values{}
		for k, v := range queryParams {
			values.Add(k, v)
		}
		apiURL += "?" + values.Encode()
	}

	// 生成签名
	sign := c.GenerateSign(path, queryParams, true)
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 创建请求
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sign", sign)
	req.Header.Set("ts", timestamp)
	req.Header.Set("appId", c.AppID)
	req.Header.Set("authorization", token)

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// Post 发送POST请求
func (c *AbcYunClient) Post(path string, data interface{}) ([]byte, error) {
	// 获取访问令牌
	token, err := c.GetAccessToken()
	if err != nil {
		return nil, err
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 生成签名
	sign := c.GenerateSign(path, nil, false)
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 创建请求
	req, err := http.NewRequest("POST", c.BaseURL+path, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sign", sign)
	req.Header.Set("ts", timestamp)
	req.Header.Set("appId", c.AppID)
	req.Header.Set("authorization", token)

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// Put 发送PUT请求
func (c *AbcYunClient) Put(path string, data []byte) ([]byte, error) {
	// 获取访问令牌
	token, err := c.GetAccessToken()
	if err != nil {
		return nil, err
	}

	// 构建URL
	apiURL := c.BaseURL + path

	// 生成签名
	sign := c.GenerateSign(path, nil, false)
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 创建请求
	var req *http.Request
	if data != nil {
		req, err = http.NewRequest("PUT", apiURL, bytes.NewBuffer(data))
	} else {
		req, err = http.NewRequest("PUT", apiURL, nil)
	}
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sign", sign)
	req.Header.Set("ts", timestamp)
	req.Header.Set("appId", c.AppID)
	req.Header.Set("authorization", token)

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	return body, nil
}

// Delete 发送DELETE请求
func (c *AbcYunClient) Delete(path string, queryParams map[string]string) ([]byte, error) {
	// 获取访问令牌
	token, err := c.GetAccessToken()
	if err != nil {
		return nil, err
	}

	// 构建URL
	apiURL := c.BaseURL + path
	if queryParams != nil && len(queryParams) > 0 {
		values := url.Values{}
		for k, v := range queryParams {
			values.Add(k, v)
		}
		apiURL += "?" + values.Encode()
	}

	// 生成签名
	sign := c.GenerateSign(path, queryParams, true)
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 创建请求
	req, err := http.NewRequest("DELETE", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sign", sign)
	req.Header.Set("ts", timestamp)
	req.Header.Set("appId", c.AppID)
	req.Header.Set("authorization", token)

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	return body, nil
}

// addAuthHeaders 添加认证头
func (c *AbcYunClient) addAuthHeaders(req *http.Request) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	sign := c.generateSign(timestamp)

	req.Header.Set("X-ABC-App-ID", c.AppID)
	req.Header.Set("X-ABC-Timestamp", timestamp)
	req.Header.Set("X-ABC-Sign", sign)
	req.Header.Set("Content-Type", "application/json")
}

// generateSign 生成签名
func (c *AbcYunClient) generateSign(timestamp string) string {
	// TODO: 实现签名生成逻辑
	// 这里需要根据ABC云的签名规则来实现
	return ""
}
