package routes

import (
	"net/http"

	"yekaitai/wx_internal/middleware"
	couponHandler "yekaitai/wx_internal/modules/coupon/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterCouponRoutes 注册小程序优惠券相关路由
func RegisterCouponRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用微信认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 创建优惠券处理器实例
	couponHandlerInst := couponHandler.NewCouponGoZeroHandler()

	// 优惠券相关路由（需要认证）
	server.AddRoutes(
		[]rest.Route{
			// 获取可领取的优惠券列表
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/coupons/available",
				Handler: wxAuthWrapper(couponHandlerInst.GetAvailableCoupons),
			},
			// 领取优惠券
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/coupons/:id/receive",
				Handler: wxAuthWrapper(couponHandlerInst.ReceiveCoupon),
			},
			// 获取用户优惠券列表
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/coupons",
				Handler: wxAuthWrapper(couponHandlerInst.GetUserCoupons),
			},
			// 获取用户优惠券详情
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/coupons/user/:id",
				Handler: wxAuthWrapper(couponHandlerInst.GetUserCouponDetail),
			},
			// 检查优惠券是否可用
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/coupons/check",
				Handler: wxAuthWrapper(couponHandlerInst.CheckCouponUsage),
			},
			// 使用优惠券
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/coupons/use",
				Handler: wxAuthWrapper(couponHandlerInst.UseCoupon),
			},
			// 获取订单可用的优惠券列表
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/coupons/usable",
				Handler: wxAuthWrapper(couponHandlerInst.GetUsableCouponsForOrder),
			},
			// 自动选择最优优惠券
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/coupons/auto-select",
				Handler: wxAuthWrapper(couponHandlerInst.AutoSelectBestCoupon),
			},
		},
	)
}
