# 叶开泰管理系统部署使用说明

## 📋 项目概述

叶开泰管理系统是一个基于 Go + go-zero 框架开发的医疗管理平台，包含小程序端和后台管理端两个服务。

## 📊 服务端口配置

| 环境 | 小程序端口 | 后台管理端口 | 配置文件 |
|------|------------|--------------|----------|
| **开发环境** | `8888` | `8889` | `etc/yekaitai-dev.yaml` |
| **测试环境** | `8888` | `8889` | `etc/yekaitai-test.yaml` |
| **生产环境** | `8888` | `8889` | `etc/yekaitai-prod.yaml` |

## 🔧 环境要求

- **Go**: 1.21+
- **MySQL**: 8.0+
- **Redis**: 7.0+
- **CKafka**: 2.8.1 (可选，用于医疗AI聊天功能)

## 🚀 部署步骤

### 1. 环境准备

```bash
# 检查Go环境
go version  # 需要 Go 1.21+

# 克隆项目
git clone <repository-url>
cd yekaitai_admin

# 安装依赖
go mod download
```

### 2. 数据库准备

确保 MySQL 和 Redis 服务正常运行，并创建相应的数据库：

```sql
CREATE DATABASE yekaitai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 配置文件设置

根据部署环境修改对应的配置文件：

#### 开发环境配置 (`etc/yekaitai-dev.yaml`)
```yaml
# 小程序服务配置
Mini:
  Name: yekaitai-mini
  Host: 0.0.0.0
  Port: 8888

# 后台管理服务配置  
Backend:
  Name: yekaitai-admin
  Host: 0.0.0.0
  Port: 8889

# 数据库配置
Mysql:
  Master:
    Addr: root:123456@tcp(localhost:3306)/yekaitai?charset=utf8mb4&parseTime=True&loc=Local

# Redis配置
Redis:
  Addr: localhost:6379
  Password: ""
  DB: 0
```

### 4. 构建和运行

#### 开发环境运行
```bash
# 方式1：直接运行
go run main.go -env dev

# 方式2：使用 Makefile
make run

# 方式3：指定配置文件
go run main.go -f etc/yekaitai-dev.yaml
```

#### 测试/生产环境部署
```bash
# 构建二进制文件
go build -o yekaitai_admin .

# 运行测试环境
./yekaitai_admin -env test

# 运行生产环境  
./yekaitai_admin -env prod
```

## 🌐 服务访问地址

### 开发环境
- **小程序API服务**: `http://localhost:8888`
- **后台管理API服务**: `http://localhost:8889`

### 测试环境
- **小程序API服务**: `http://your-test-server:8888`
- **后台管理API服务**: `http://your-test-server:8889`

### 生产环境
- **小程序API服务**: `http://your-prod-server:8888`
- **后台管理API服务**: `http://your-prod-server:8889`

## 📋 API 接口说明

### 小程序端 (端口 8888)

#### 用户认证
```
POST /api/wx/auth/login          # 微信登录
POST /api/wx/auth/phone          # 获取手机号
```

#### 医生相关
```
GET  /api/wx/doctors             # 获取医生列表
GET  /api/wx/doctors/:id         # 获取医生详情
GET  /api/wx/doctors/recommend   # 获取推荐医生
```

#### 预约挂号
```
POST /api/wx/appointments        # 创建预约
GET  /api/wx/appointments        # 获取预约列表
PUT  /api/wx/appointments/:id    # 更新预约状态
```

#### 商品购买
```
GET  /api/wx/goods               # 获取商品列表
GET  /api/wx/goods/:id           # 获取商品详情
POST /api/wx/orders              # 创建订单
GET  /api/wx/orders              # 获取订单列表
```

### 后台管理端 (端口 8889)

#### 管理员认证
```
POST /api/admin/auth/login       # 管理员登录
POST /api/admin/auth/logout      # 管理员登出
GET  /api/admin/auth/profile     # 获取管理员信息
```

#### 医生管理
```
GET  /api/admin/doctors          # 获取医生列表
POST /api/admin/doctors          # 创建医生
PUT  /api/admin/doctors/:id      # 更新医生信息
DELETE /api/admin/doctors/:id    # 删除医生
```

#### 推荐医生管理
```
GET  /api/admin/doctor-recommend    # 获取推荐医生列表
POST /api/admin/doctor-recommend    # 添加推荐医生
PUT  /api/admin/doctor-recommend/:id # 更新推荐医生
DELETE /api/admin/doctor-recommend/:id # 删除推荐医生
```

#### 用户管理
```
GET  /api/admin/users            # 获取用户列表
GET  /api/admin/users/:id        # 获取用户详情
PUT  /api/admin/users/:id        # 更新用户信息
```

## 🐳 Docker 部署

### Dockerfile
```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY . .
RUN go mod download
RUN CGO_ENABLED=0 GOOS=linux go build -o yekaitai_admin .

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/yekaitai_admin .
COPY --from=builder /app/etc ./etc

EXPOSE 8888 8889

CMD ["./yekaitai_admin", "-env", "prod"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  yekaitai-app:
    build: .
    ports:
      - "8888:8888"  # 小程序端口
      - "8889:8889"  # 后台管理端口
    environment:
      - ENV=prod
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/root/logs
    restart: unless-stopped
      
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: yekaitai
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
      
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 启动 Docker 服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f yekaitai-app
```

## 🔍 健康检查

```bash
# 检查小程序服务
curl http://localhost:8888/health

# 检查后台管理服务  
curl http://localhost:8889/health

# 检查服务状态
curl -I http://localhost:8888
curl -I http://localhost:8889
```

## 📝 日志管理

### 日志文件位置
- **应用日志**: `logs/app.log`
- **错误日志**: `logs/error.log`
- **访问日志**: 自动记录在应用日志中

### 日志查看命令
```bash
# 实时查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看最近100行日志
tail -n 100 logs/app.log

# 搜索特定关键词
grep "ERROR" logs/app.log
```

## ⚙️ 系统监控

### 进程监控
```bash
# 查看进程状态
ps aux | grep yekaitai_admin

# 查看端口占用
netstat -tulpn | grep :8888
netstat -tulpn | grep :8889

# 查看系统资源使用
top -p $(pgrep yekaitai_admin)
```

### 服务监控
```bash
# 检查数据库连接
mysql -h localhost -u root -p -e "SELECT 1"

# 检查Redis连接
redis-cli ping

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 🚨 注意事项

### 安全配置
1. **端口安全**: 确保 8888 和 8889 端口未被占用
2. **数据库安全**: 生产环境请修改默认的数据库密码
3. **JWT密钥**: 生产环境必须修改默认的 JWT 密钥
4. **防火墙设置**: 确保服务器防火墙允许对应端口访问
5. **SSL证书**: 生产环境强烈建议配置 HTTPS

### 性能优化
1. **数据库连接池**: 根据并发量调整 `MaxOpenConns` 和 `MaxIdleConns`
2. **Redis连接池**: 调整 `PoolSize` 参数
3. **日志级别**: 生产环境建议设置为 `warn` 或 `error`
4. **静态资源**: 使用 CDN 加速静态资源访问

### 备份策略
1. **数据库备份**: 定期备份 MySQL 数据
2. **配置文件备份**: 备份重要的配置文件
3. **日志归档**: 定期清理和归档日志文件

## 🔧 常见问题

### Q: 启动时提示端口被占用？
```bash
# 检查端口占用情况
netstat -tulpn | grep :8888
netstat -tulpn | grep :8889

# 杀死占用端口的进程
sudo kill -9 <PID>
```

### Q: 数据库连接失败？
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查数据库连接
mysql -h localhost -u root -p

# 检查配置文件中的连接字符串
grep -A 5 "Mysql:" etc/yekaitai-dev.yaml
```

### Q: Redis连接失败？
```bash
# 检查Redis服务状态
systemctl status redis

# 测试Redis连接
redis-cli ping

# 检查Redis配置
grep -A 5 "Redis:" etc/yekaitai-dev.yaml
```

### Q: 如何查看详细日志？
```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log

# 查看特定时间段的日志
grep "2024-01-01" logs/app.log
```

### Q: 如何重启服务？
```bash
# 开发环境
# Ctrl+C 停止，然后重新运行
go run main.go -env dev

# 生产环境
# 找到进程ID并杀死
ps aux | grep yekaitai_admin
kill -9 <PID>
# 重新启动
./yekaitai_admin -env prod

# Docker环境
docker-compose restart yekaitai-app
```

### Q: 如何更新代码？
```bash
# 1. 停止服务
kill -9 <PID>

# 2. 拉取最新代码
git pull origin main

# 3. 重新构建
go build -o yekaitai_admin .

# 4. 重新启动
./yekaitai_admin -env prod
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认所有依赖服务正常运行
4. 联系技术支持团队

## 📈 版本更新

### 更新流程
1. 备份当前版本和数据
2. 停止服务
3. 更新代码
4. 运行数据库迁移（如有）
5. 重新构建和启动服务
6. 验证功能正常

### 回滚方案
如果更新后出现问题，可以快速回滚到上一个版本：
1. 停止新版本服务
2. 恢复备份的二进制文件
3. 恢复数据库备份（如需要）
4. 重新启动服务

---

🎉 **恭喜！** 现在你已经成功部署了叶开泰管理系统！

如有任何问题，请参考上述常见问题解决方案或联系技术支持。
```
