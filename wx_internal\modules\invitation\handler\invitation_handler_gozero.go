package handler

import (
	"net/http"

	"yekaitai/wx_internal/modules/invitation/service"
	"yekaitai/wx_internal/types"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// WxInvitationGoZeroHandler 小程序端go-zero格式的邀请处理器
type WxInvitationGoZeroHandler struct {
	invitationService *service.InvitationService
}

func NewWxInvitationGoZeroHandler() *WxInvitationGoZeroHandler {
	return &WxInvitationGoZeroHandler{
		invitationService: service.NewInvitationService(),
	}
}

// 邀请中心请求
type InvitationCenterRequest struct {
	Page int `form:"page,optional"` // 页码，默认1
	Size int `form:"size,optional"` // 每页数量，默认10
}

// 创建邀请请求
type CreateInvitationRequest struct {
	// 无需参数，系统自动生成邀请码
}

// 接受邀请请求
type AcceptInvitationRequest struct {
	InvitationCode string `json:"invitation_code"` // 邀请码
}

// GetInvitationCenter 获取邀请中心数据
func (h *WxInvitationGoZeroHandler) GetInvitationCenter(w http.ResponseWriter, r *http.Request) {
	var req InvitationCenterRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 调用邀请服务
	result, _, err := h.invitationService.GetInvitationCenter(r.Context(), userID, req.Page, req.Size)
	if err != nil {
		logx.Errorf("获取邀请中心数据失败: userID=%d, error=%v", userID, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取邀请中心数据失败"))
		return
	}

	// 直接返回结果
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// CreateInvitation 创建邀请
func (h *WxInvitationGoZeroHandler) CreateInvitation(w http.ResponseWriter, r *http.Request) {
	var req CreateInvitationRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 调用邀请服务
	result, err := h.invitationService.CreateInvitation(r.Context(), userID)
	if err != nil {
		logx.Errorf("创建邀请失败: userID=%d, error=%v", userID, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// AcceptInvitation 接受邀请
func (h *WxInvitationGoZeroHandler) AcceptInvitation(w http.ResponseWriter, r *http.Request) {
	var req AcceptInvitationRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 参数验证
	if req.InvitationCode == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "邀请码不能为空"))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 调用邀请服务
	result, err := h.invitationService.AcceptInvitation(r.Context(), userID, req.InvitationCode)
	if err != nil {
		logx.Errorf("接受邀请失败: userID=%d, invitationCode=%s, error=%v", userID, req.InvitationCode, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetInvitationStats 获取邀请统计
func (h *WxInvitationGoZeroHandler) GetInvitationStats(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	// 调用邀请服务
	result, err := h.invitationService.GetInvitationStats(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取邀请统计失败: userID=%d, error=%v", userID, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取邀请统计失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetInvitationRules 获取邀请奖励规则
func (h *WxInvitationGoZeroHandler) GetInvitationRules(w http.ResponseWriter, r *http.Request) {
	// 调用邀请服务
	result, err := h.invitationService.GetInvitationRules(r.Context())
	if err != nil {
		logx.Errorf("获取邀请奖励规则失败: error=%v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取奖励规则失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// 以下是兼容旧版本的处理器函数

// InvitationCenterHandler 邀请中心处理器（兼容旧版本）
func InvitationCenterHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxInvitationGoZeroHandler()
	return handler.GetInvitationCenter
}

// CreateInvitationHandler 创建邀请处理器（兼容旧版本）
func CreateInvitationHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxInvitationGoZeroHandler()
	return handler.CreateInvitation
}

// AcceptInvitationHandler 接受邀请处理器（兼容旧版本）
func AcceptInvitationHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxInvitationGoZeroHandler()
	return handler.AcceptInvitation
}

// InvitationStatsHandler 邀请统计处理器（兼容旧版本）
func InvitationStatsHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxInvitationGoZeroHandler()
	return handler.GetInvitationStats
}

// InvitationRulesHandler 邀请奖励规则处理器（兼容旧版本）
func InvitationRulesHandler(svcCtx interface{}) http.HandlerFunc {
	handler := NewWxInvitationGoZeroHandler()
	return handler.GetInvitationRules
}
