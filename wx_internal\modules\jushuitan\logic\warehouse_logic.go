package logic

import (
	"context"
	"yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// WarehouseLogic 仓库查询逻辑
type WarehouseLogic struct {
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

// NewWarehouseLogic 创建仓库查询逻辑
func NewWarehouseLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *WarehouseLogic {
	return &WarehouseLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// QueryWarehouses 查询仓库列表
func (l *WarehouseLogic) QueryWarehouses(pageIndex, pageSize int) (*jushuitan.BaseResp, error) {
	// 调用聚水潭API
	req := &jushuitan.WarehouseQueryRequest{
		PageIndex: pageIndex,
		PageSize:  pageSize,
	}

	return l.svcCtx.JushuitanClient.QueryWarehouses(l.ctx, req)
}
