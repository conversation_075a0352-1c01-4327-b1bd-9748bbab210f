package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	adminHandlerPkg "yekaitai/internal/modules/admin/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterAdminRoutes 注册管理员相关路由
func RegisterAdminRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 初始化管理员处理器
	adminHandler := adminHandlerPkg.NewAdminUserHandler(serverCtx)

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 管理员路由
	server.AddRoutes(
		[]rest.Route{
			// 管理员列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/users",
				Handler: adminAuthWrapper(adminHandler.ListAdmins),
			},
			// 创建管理员
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/users",
				Handler: adminAuthWrapper(adminHandler.CreateAdmin),
			},
			// 获取管理员详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/users/:adminId",
				Handler: adminAuthWrapper(adminHandler.GetAdmin),
			},
			// 更新管理员
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/users/:adminId",
				Handler: adminAuthWrapper(adminHandler.UpdateAdmin),
			},
			// 删除管理员
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/users/:adminId",
				Handler: adminAuthWrapper(adminHandler.DeleteAdmin),
			},
			// 切换管理员状态（启用/禁用）- 重要！确保此路由正确注册
			{
				Method:  http.MethodPatch,
				Path:    "/api/admin/users/:adminId/status",
				Handler: adminAuthWrapper(adminHandler.ToggleAdminStatus),
			},
			// 管理员操作日志
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/logs",
				Handler: adminAuthWrapper(adminHandler.ListOperationLogs),
			},
		},
	)
}
