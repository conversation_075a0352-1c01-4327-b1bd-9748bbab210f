package model

import (
	"time"

	"gorm.io/gorm"
)

// SmsCode 短信验证码模型
type SmsCode struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Mobile    string         `gorm:"size:20;index" json:"mobile"` // 手机号
	Code      string         `gorm:"size:10" json:"-"`            // 验证码
	Type      int            `gorm:"default:1" json:"type"`       // 验证码类型：1-登录, 2-注册, 3-找回密码
	ExpireAt  time.Time      `json:"expire_at"`                   // 过期时间
	Used      bool           `gorm:"default:false" json:"used"`   // 是否已使用
	CreatedAt time.Time      `json:"created_at"`                  // 创建时间
	UpdatedAt time.Time      `json:"updated_at"`                  // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`              // 删除时间
}

// TableName 设置表名
func (SmsCode) TableName() string {
	return "sms_codes"
}

// 短信验证码类型常量
const (
	SmsTypeLogin        = 1 // 登录验证码
	SmsTypeRegister     = 2 // 注册验证码
	SmsTypeResetPwd     = 3 // 重置密码验证码
	SmsTypeBindMobile   = 4 // 绑定手机号验证码
	SmsTypeChangeMobile = 5 // 更换手机号验证码
)

// SmsCodeRepository 短信验证码仓库接口
type SmsCodeRepository interface {
	Create(smsCode *SmsCode) error
	FindLatestByMobile(mobile string, smsType int) (*SmsCode, error)
	MarkAsUsed(id uint) error
	DeleteExpired() error
}

// smsCodeRepository 短信验证码仓库实现
type smsCodeRepository struct {
	db *gorm.DB
}

// NewSmsCodeRepository 创建短信验证码仓库
func NewSmsCodeRepository(db *gorm.DB) SmsCodeRepository {
	return &smsCodeRepository{db: db}
}
