package routes

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/modules/store/handler"
	"yekaitai/wx_internal/svc"
)

// RegisterStoreHandlers 注册门店相关接口
func RegisterStoreHandlers(server RestServer, serverCtx *svc.WxServiceContext) {
	// 获取门店列表接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/store/list",
			Handler: handler.ListStores,
		},
	)

	// 获取门店详情接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/store/:id",
			Handler: handler.GetStore,
		},
	)
}
