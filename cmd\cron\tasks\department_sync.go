package tasks

import (
	"context"
	"strconv"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	"yekaitai/pkg/adapters/hangzhou"
	wxDepartmentModel "yekaitai/wx_internal/modules/department/model"
)

// DepartmentSyncService 机构科室同步服务
type DepartmentSyncService struct {
	client    *hangzhou.Client                       // 杭州HIS客户端
	deptRepo  wxDepartmentModel.DepartmentRepository // 科室仓库
	enabled   bool                                   // 是否启用同步
	interval  int                                    // 同步间隔（小时）
	ticker    *time.Ticker                           // 定时器
	stopChan  chan struct{}                          // 停止信号通道
	isRunning bool                                   // 是否正在运行
	lock      sync.Mutex                             // 互斥锁
}

// NewDepartmentSyncService 创建机构科室同步服务
func NewDepartmentSyncService(client *hangzhou.Client, enabled bool, interval int) *DepartmentSyncService {
	// 默认1小时同步一次
	if interval <= 0 {
		interval = 1
	}

	return &DepartmentSyncService{
		client:    client,
		deptRepo:  wxDepartmentModel.NewDepartmentRepository(),
		enabled:   enabled,
		interval:  interval,
		stopChan:  make(chan struct{}),
		isRunning: false,
	}
}

// Start 启动同步服务
func (s *DepartmentSyncService) Start() error {
	s.lock.Lock()
	defer s.lock.Unlock()

	if s.isRunning {
		logx.Info("机构科室同步服务已经在运行中")
		return nil
	}

	if !s.enabled {
		logx.Info("机构科室同步服务已禁用，不会同步数据")
		return nil
	}

	s.isRunning = true
	s.ticker = time.NewTicker(time.Duration(s.interval) * time.Hour)

	// 启动定时任务
	go func() {
		for {
			select {
			case <-s.ticker.C:
				go s.syncDepartments()
			case <-s.stopChan:
				s.ticker.Stop()
				return
			}
		}
	}()

	// 立即执行一次同步
	s.syncDepartments()

	logx.Info("机构科室同步服务已启动，将定期执行同步操作")
	return nil
}

// Stop 停止同步服务
func (s *DepartmentSyncService) Stop() {
	s.lock.Lock()
	defer s.lock.Unlock()

	if !s.isRunning {
		return
	}

	close(s.stopChan)
	s.isRunning = false
	logx.Info("机构科室同步服务已停止")
}

// syncDepartments 同步机构科室信息
func (s *DepartmentSyncService) syncDepartments() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 获取配置中的卫生机构ID
	wsjgID := s.client.GetConfig().CurrentWsjgid
	if wsjgID == "" {
		logx.Error("卫生机构ID为空，无法同步科室信息")
		return
	}
	logx.Info("开始同步机构科室信息", logx.Field("wsjgID", wsjgID))

	// 调用机构科室列表接口
	// 确保传递正确的机构ID参数
	logx.Info("调用GetDepartments API，参数", logx.Field("pbksbz", 1), logx.Field("wsjgid", wsjgID))
	resp, err := s.client.GetDepartments(ctx, 1, wsjgID)
	if err != nil {
		logx.Error("获取机构科室列表失败", logx.Field("error", err))
		return
	}

	// 解析响应数据
	departments, ok := resp.Data.([]interface{})
	if !ok {
		logx.Error("解析机构科室数据失败，响应数据类型不匹配")
		return
	}

	logx.Info("获取到机构科室数据", logx.Field("count", len(departments)))

	// 批量保存科室信息
	created := 0
	updated := 0
	filtered := 0

	for _, item := range departments {
		dept, ok := item.(map[string]interface{})
		if !ok {
			logx.Error("解析单个科室数据失败")
			continue
		}

		// 提取科室信息
		jgksID := 0
		if id, ok := dept["jgksid"].(float64); ok {
			jgksID = int(id)
		}

		if jgksID == 0 {
			logx.Error("科室ID无效，跳过")
			continue
		}

		// 验证是否属于指定的卫生机构
		itemWsjgID := ""
		if id, ok := dept["wsjgid"].(float64); ok {
			itemWsjgID = strconv.Itoa(int(id))
		} else if id, ok := dept["wsjgid"].(string); ok {
			itemWsjgID = id
		}

		// 如果不匹配当前机构ID，则跳过
		if itemWsjgID != "" && itemWsjgID != wsjgID {
			logx.Info("科室不属于指定卫生机构，跳过",
				logx.Field("jgksID", jgksID),
				logx.Field("itemWsjgID", itemWsjgID),
				logx.Field("expectedWsjgID", wsjgID))
			filtered++
			continue
		}

		// 查找已存在的科室
		existingDept, err := s.deptRepo.FindByJgksIDAndWsjgID(jgksID, wsjgID)
		isNew := err != nil

		// 提取其他字段
		var deptModel wxDepartmentModel.Department
		if !isNew {
			deptModel = *existingDept
		}

		deptModel.JgksID = strconv.Itoa(jgksID)
		deptModel.WsjgID = wsjgID

		// 提取其他字段
		if v, ok := dept["jgksmc"].(string); ok {
			deptModel.JgksMC = v
		}
		if v, ok := dept["pym"].(string); ok {
			deptModel.PYM = v
		}
		if v, ok := dept["zfbz"].(int); ok {
			deptModel.ZfBz = v
		}
		if v, ok := dept["fwfw"].(string); ok {
			deptModel.FwFw = v
		}
		if v, ok := dept["fwfwmc"].(string); ok {
			deptModel.FwFwMC = v
		}
		if v, ok := dept["gllb"].(string); ok {
			deptModel.GlLb = v
		}
		if v, ok := dept["gllbmc"].(string); ok {
			deptModel.GlLbMC = v
		}
		if v, ok := dept["ksfldm"].(string); ok {
			deptModel.KsFLDM = v
		}
		if v, ok := dept["ksflmc"].(string); ok {
			deptModel.KsFLMC = v
		}
		if v, ok := dept["kslx"].(string); ok {
			deptModel.KsLx = v
		}

		if isNew {
			deptModel.CreatedAt = time.Now()
			deptModel.UpdatedAt = time.Now()
			if err := s.deptRepo.Create(&deptModel); err != nil {
				logx.Error("创建科室失败", logx.Field("error", err))
				continue
			}
			logx.Info("成功创建科室", logx.Field("jgksID", jgksID), logx.Field("name", deptModel.JgksMC))
			created++
		} else {
			deptModel.UpdatedAt = time.Now()
			if err := s.deptRepo.Update(&deptModel); err != nil {
				logx.Error("更新科室失败", logx.Field("error", err))
				continue
			}
			logx.Info("成功更新科室", logx.Field("jgksID", jgksID), logx.Field("name", deptModel.JgksMC))
			updated++
		}
	}

	logx.Info("机构科室同步完成", logx.Field("created", created), logx.Field("updated", updated), logx.Field("filtered", filtered))
}

// SyncDepartments 手动执行科室同步（可从外部调用）
func (s *DepartmentSyncService) SyncDepartments() {
	s.syncDepartments()
}
