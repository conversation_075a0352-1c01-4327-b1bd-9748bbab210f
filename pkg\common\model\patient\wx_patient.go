package patient

import (
	"time"

	"gorm.io/gorm"
)

// WxPatient 患者信息表
type WxPatient struct {
	PatientID            uint           `json:"patient_id" gorm:"primaryKey;autoIncrement;comment:患者ID"`
	UserID               uint           `json:"user_id" gorm:"index;not null;comment:关联微信用户ID"`
	Name                 string         `json:"name" gorm:"type:varchar(50);comment:姓名"`
	Gender               int            `json:"gender" gorm:"type:tinyint(1);default:0;comment:性别(0-未知,1-男,2-女)"`
	BirthDate            string         `json:"birth_date" gorm:"type:varchar(20);comment:出生年月日"`
	IdCard               string         `json:"id_card" gorm:"type:varchar(30);comment:身份证号"`
	Ethnicity            string         `json:"ethnicity" gorm:"type:varchar(20);comment:民族"`
	RelationshipWithUser string         `json:"relationship_with_user" gorm:"type:varchar(20);comment:与本人关系"`
	Mobile               string         `json:"mobile" gorm:"type:varchar(20);comment:手机号码"`
	MaritalStatus        int            `json:"marital_status" gorm:"type:tinyint(1);default:0;comment:婚姻状态(0-未知,1-已婚,2-未婚)"`
	MedicalHistory       string         `json:"medical_history" gorm:"type:text;comment:病史"`
	Allergies            string         `json:"allergies" gorm:"type:text;comment:过敏史"`
	Status               int            `json:"status" gorm:"default:1;not null;comment:状态(1-正常，0-禁用)"`
	// 平台患者ID字段
	HangzhouHisID        string         `json:"hangzhou_his_id" gorm:"type:varchar(100);index;comment:杭州HIS患者ID"`
	AbcyunPatientID      string         `json:"abcyun_patient_id" gorm:"type:varchar(100);index;comment:ABC云患者ID"`
	CreatedAt            time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt            time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt            gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 设置WxPatient表名
func (WxPatient) TableName() string {
	return "wx_patient"
}
