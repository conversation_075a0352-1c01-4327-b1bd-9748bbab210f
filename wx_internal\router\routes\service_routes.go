package routes

import (
	"net/http"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/service/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterServiceRoutes 注册服务模块路由
func RegisterServiceRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 创建处理器
	serviceHandler := handler.NewServiceHandler(serverCtx)
	checkoutHandler := handler.NewServiceCheckoutHandler(serverCtx)
	orderHandler := handler.NewServiceOrderHandler(serverCtx)
	appointmentHandler := handler.NewServiceAppointmentHandler(serverCtx)

	// 使用微信认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 服务列表和详情
	server.AddRoute(rest.Route{
		Method:  http.MethodGet,
		Path:    "/api/wx/services",
		Handler: serviceHandler.GetServiceList,
	})

	server.AddRoute(rest.Route{
		Method:  http.MethodGet,
		Path:    "/api/wx/services/detail",
		Handler: serviceHandler.GetServiceDetail,
	})

	// 我的服务列表（已购买待使用、已预约）
	server.AddRoute(rest.Route{
		Method:  http.MethodGet,
		Path:    "/api/wx/services/my",
		Handler: wxAuthWrapper(serviceHandler.GetMyServiceList),
	})

	// 服务结算相关接口
	server.AddRoute(rest.Route{
		Method:  http.MethodPost,
		Path:    "/api/wx/services/checkout/preload",
		Handler: wxAuthWrapper(checkoutHandler.PreloadCheckout),
	})

	server.AddRoute(rest.Route{
		Method:  http.MethodPost,
		Path:    "/api/wx/services/checkout/recalculate",
		Handler: wxAuthWrapper(checkoutHandler.RecalculateCheckout),
	})

	server.AddRoute(rest.Route{
		Method:  http.MethodPost,
		Path:    "/api/wx/services/orders",
		Handler: wxAuthWrapper(checkoutHandler.CreateServiceOrder),
	})

	// 服务订单管理
	server.AddRoute(rest.Route{
		Method:  http.MethodGet,
		Path:    "/api/wx/services/orders/list",
		Handler: wxAuthWrapper(orderHandler.GetOrderList),
	})

	server.AddRoute(rest.Route{
		Method:  http.MethodGet,
		Path:    "/api/wx/services/orders/detail",
		Handler: wxAuthWrapper(orderHandler.GetOrderDetail),
	})

	server.AddRoute(rest.Route{
		Method:  http.MethodPost,
		Path:    "/api/wx/services/orders/cancel",
		Handler: wxAuthWrapper(orderHandler.CancelOrder),
	})

	// 生成服务订单二维码
	server.AddRoute(rest.Route{
		Method:  http.MethodPost,
		Path:    "/api/wx/services/orders/generate-qrcode",
		Handler: wxAuthWrapper(serviceHandler.GenerateServiceQRCode),
	})

	// 核销服务订单
	server.AddRoute(rest.Route{
		Method:  http.MethodPost,
		Path:    "/api/wx/services/orders/verify",
		Handler: wxAuthWrapper(serviceHandler.VerifyServiceOrder),
	})

	// 服务预约管理
	server.AddRoute(rest.Route{
		Method:  http.MethodGet,
		Path:    "/api/wx/services/appointments/availability",
		Handler: wxAuthWrapper(appointmentHandler.GetAppointmentAvailability),
	})

	server.AddRoute(rest.Route{
		Method:  http.MethodPost,
		Path:    "/api/wx/services/appointments",
		Handler: wxAuthWrapper(appointmentHandler.CreateAppointment),
	})

	server.AddRoute(rest.Route{
		Method:  http.MethodPut,
		Path:    "/api/wx/services/appointments",
		Handler: wxAuthWrapper(appointmentHandler.ModifyAppointment),
	})

	server.AddRoute(rest.Route{
		Method:  http.MethodDelete,
		Path:    "/api/wx/services/appointments",
		Handler: wxAuthWrapper(appointmentHandler.CancelAppointment),
	})
}
