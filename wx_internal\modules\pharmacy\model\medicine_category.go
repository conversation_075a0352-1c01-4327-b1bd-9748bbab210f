package model

import (
	"time"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// MedicineCategory 药品分类模型
type MedicineCategory struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
	Name         string         `gorm:"size:50;not null" json:"name"`      // 分类名称
	Icon         string         `gorm:"size:255" json:"icon"`              // 分类图标
	ParentID     uint           `gorm:"index" json:"parent_id"`            // 父级ID，用于构建分类树形结构
	Level        int            `gorm:"default:1" json:"level"`            // 分类级别 1:一级分类 2:二级分类
	Sort         int            `gorm:"default:0" json:"sort"`             // 排序
	Status       int            `gorm:"default:1" json:"status"`           // 状态 1:启用 0:禁用
	ExternalID   string         `gorm:"size:100;index" json:"external_id"` // 外部系统ID
	ExternalType string         `gorm:"size:50" json:"external_type"`      // 外部系统类型
}

// TableName 设置表名
func (MedicineCategory) TableName() string {
	return "medicine_categories"
}

// MedicineCategoryRepository 药品分类仓库接口
type MedicineCategoryRepository interface {
	Create(category *MedicineCategory) error
	Update(category *MedicineCategory) error
	Delete(id uint) error
	FindByID(id uint) (*MedicineCategory, error)
	List(page, size int) ([]*MedicineCategory, int64, error)
	ListAll() ([]*MedicineCategory, error)
	ListByParentID(parentID uint) ([]*MedicineCategory, error)
	UpdateStatus(id uint, status int) error
	FindByExternalID(externalID string) (*MedicineCategory, error)
}

// medicineCategoryRepository 药品分类仓库实现
type medicineCategoryRepository struct{}

// NewMedicineCategoryRepository 创建药品分类仓库
func NewMedicineCategoryRepository() MedicineCategoryRepository {
	return &medicineCategoryRepository{}
}

// FindByExternalID 根据外部ID查询分类
func (r *medicineCategoryRepository) FindByExternalID(externalID string) (*MedicineCategory, error) {
	var category MedicineCategory
	err := mysql.Slave().Where("external_id = ?", externalID).First(&category).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}
