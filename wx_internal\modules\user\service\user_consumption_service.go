package service

import (
	"context"
	"fmt"
	userLevelModel "yekaitai/internal/modules/user_level/model"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/user/model"

	"github.com/zeromicro/go-zero/core/logx"
)

// UserConsumptionService 用户消费服务
type UserConsumptionService struct {
	repo model.WxUserAnnualConsumptionRepository
}

// NewUserConsumptionService 创建用户消费服务
func NewUserConsumptionService() *UserConsumptionService {
	return &UserConsumptionService{
		repo: model.NewWxUserAnnualConsumptionRepository(mysql.GetDB()),
	}
}

// RecordProductConsumption 记录商品消费
func (s *UserConsumptionService) RecordProductConsumption(userID uint, amount float64, orderNo string) error {
	if userID <= 0 {
		return fmt.Errorf("无效的用户ID")
	}

	if amount <= 0 {
		return fmt.Errorf("消费金额必须大于0")
	}

	if orderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}

	// 记录商品消费
	if err := s.repo.AddProductConsumption(userID, amount, orderNo); err != nil {
		logx.Errorf("记录商品消费失败: userID=%d, amount=%.2f, orderNo=%s, error=%v",
			userID, amount, orderNo, err)
		return fmt.Errorf("记录商品消费失败: %w", err)
	}

	logx.Infof("商品消费记录成功: userID=%d, amount=%.2f, orderNo=%s",
		userID, amount, orderNo)

	// 触发用户等级更新
	if err := s.TriggerUserLevelUpdate(userID); err != nil {
		logx.Errorf("触发用户等级更新失败: userID=%d, error=%v", userID, err)
		// 不返回错误，不影响主流程
	}

	return nil
}

// RecordServiceConsumption 记录服务消费
func (s *UserConsumptionService) RecordServiceConsumption(userID uint, amount float64, orderNo string) error {
	if userID <= 0 {
		return fmt.Errorf("无效的用户ID")
	}

	if amount <= 0 {
		return fmt.Errorf("消费金额必须大于0")
	}

	if orderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}

	// 记录服务消费
	if err := s.repo.AddServiceConsumption(userID, amount, orderNo); err != nil {
		logx.Errorf("记录服务消费失败: userID=%d, amount=%.2f, orderNo=%s, error=%v",
			userID, amount, orderNo, err)
		return fmt.Errorf("记录服务消费失败: %w", err)
	}

	logx.Infof("服务消费记录成功: userID=%d, amount=%.2f, orderNo=%s",
		userID, amount, orderNo)

	// 触发用户等级更新
	if err := s.TriggerUserLevelUpdate(userID); err != nil {
		logx.Errorf("触发用户等级更新失败: userID=%d, error=%v", userID, err)
		// 不返回错误，不影响主流程
	}

	return nil
}

// RecordActivityConsumption 记录活动消费
func (s *UserConsumptionService) RecordActivityConsumption(userID uint, amount float64, activityNo string) error {
	if userID <= 0 {
		return fmt.Errorf("无效的用户ID")
	}

	if amount <= 0 {
		return fmt.Errorf("消费金额必须大于0")
	}

	if activityNo == "" {
		return fmt.Errorf("活动编号不能为空")
	}

	// 记录活动消费
	if err := s.repo.AddActivityConsumption(userID, amount, activityNo); err != nil {
		logx.Errorf("记录活动消费失败: userID=%d, amount=%.2f, activityNo=%s, error=%v",
			userID, amount, activityNo, err)
		return fmt.Errorf("记录活动消费失败: %w", err)
	}

	logx.Infof("活动消费记录成功: userID=%d, amount=%.2f, activityNo=%s",
		userID, amount, activityNo)

	// 触发用户等级更新
	if err := s.TriggerUserLevelUpdate(userID); err != nil {
		logx.Errorf("触发用户等级更新失败: userID=%d, error=%v", userID, err)
		// 不返回错误，不影响主流程
	}

	return nil
}

// GetUserCurrentYearConsumption 获取用户当年消费记录
func (s *UserConsumptionService) GetUserCurrentYearConsumption(userID uint) (*model.WxUserAnnualConsumption, error) {
	if userID <= 0 {
		return nil, fmt.Errorf("无效的用户ID")
	}

	return s.repo.GetUserCurrentYearConsumption(userID)
}

// GetUserTotalConsumption 获取用户当年总消费金额
func (s *UserConsumptionService) GetUserTotalConsumption(userID uint) (float64, error) {
	if userID <= 0 {
		return 0, fmt.Errorf("无效的用户ID")
	}

	return s.repo.GetUserTotalConsumption(userID)
}

// CheckConsumptionForLevelUpgrade 检查用户消费是否满足等级升级条件
func (s *UserConsumptionService) CheckConsumptionForLevelUpgrade(userID uint, requiredAmount float64) (bool, error) {
	if userID <= 0 {
		return false, fmt.Errorf("无效的用户ID")
	}

	// 获取用户当年总消费金额
	totalAmount, err := s.GetUserTotalConsumption(userID)
	if err != nil {
		return false, err
	}

	// 检查是否满足消费要求
	return totalAmount >= requiredAmount, nil
}

// HookOrderPaymentSuccess 订单支付成功钩子，用于在订单支付成功时添加用户消费记录
// 该方法可以被订单支付成功的回调处理函数调用
func HookOrderPaymentSuccess(ctx context.Context, userID uint, orderType string, amount float64, orderNo string) {
	// 创建用户消费服务
	service := NewUserConsumptionService()

	// 更新用户消费记录并触发等级更新
	err := service.UpdateUserConsumption(userID, amount, orderType, orderNo)

	if err != nil {
		// 记录错误但不影响主流程
		logx.Errorf("记录用户消费失败: type=%s, userID=%d, amount=%.2f, orderNo=%s, error=%v",
			orderType, userID, amount, orderNo, err)
	} else {
		logx.Infof("记录用户消费成功: type=%s, userID=%d, amount=%.2f, orderNo=%s",
			orderType, userID, amount, orderNo)
	}
}

// TriggerUserLevelUpdate 触发用户等级更新
// 当用户消费金额变动时，调用此函数自动更新用户等级
func (s *UserConsumptionService) TriggerUserLevelUpdate(userID uint) error {
	if userID <= 0 {
		return fmt.Errorf("无效的用户ID")
	}

	// 获取数据库连接
	db := mysql.GetDB()

	// 创建用户等级仓库
	userLevelRepo := userLevelModel.NewUserLevelRepository(db)

	// 计算用户应有的等级
	newLevel, err := userLevelRepo.CalculateUserLevel(userID)
	if err != nil {
		logx.Errorf("计算用户等级失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("计算用户等级失败: %w", err)
	}

	// 获取用户当前等级
	var currentLevelID *uint
	if err := db.Table("wx_user").
		Where("user_id = ?", userID).
		Pluck("user_level_id", &currentLevelID).Error; err != nil {
		logx.Errorf("获取用户当前等级失败: userID=%d, error=%v", userID, err)
		return fmt.Errorf("获取用户当前等级失败: %w", err)
	}

	// 如果等级有变化，更新用户等级并记录日志
	if currentLevelID == nil || *currentLevelID != newLevel.ID {
		// 更新用户等级
		if err := db.Table("wx_user").
			Where("user_id = ?", userID).
			Updates(map[string]interface{}{
				"user_level_id": newLevel.ID,
			}).Error; err != nil {
			logx.Errorf("更新用户等级失败: userID=%d, error=%v", userID, err)
			return fmt.Errorf("更新用户等级失败: %w", err)
		}

		logx.Infof("用户等级更新成功: userID=%d, 新等级=%s(ID=%d)",
			userID, newLevel.LevelName, newLevel.ID)

		// 记录升级日志
		var currentLevelName string
		if currentLevelID != nil {
			var currentLevel userLevelModel.UserLevelRule
			if err := db.First(&currentLevel, *currentLevelID).Error; err == nil {
				currentLevelName = currentLevel.LevelName
			}
		}

		upgradeLog := &userLevelModel.UserLevelUpgradeLog{
			UserID:        userID,
			FromLevelID:   currentLevelID,
			ToLevelID:     newLevel.ID,
			FromLevelName: currentLevelName,
			ToLevelName:   newLevel.LevelName,
			UpgradeReason: "消费达到等级要求，自动升级",
		}

		if err := userLevelRepo.CreateUpgradeLog(upgradeLog); err != nil {
			logx.Errorf("记录用户等级升级日志失败: userID=%d, error=%v", userID, err)
			// 不返回错误，继续执行
		}
	}

	return nil
}

// TriggerUserLevelUpdateOnRegister 用户注册时触发等级更新
func (s *UserConsumptionService) TriggerUserLevelUpdateOnRegister(userID uint) error {
	if userID <= 0 {
		return fmt.Errorf("无效的用户ID")
	}

	logx.Infof("用户注册，触发等级检查: userID=%d", userID)
	return s.TriggerUserLevelUpdate(userID)
}

// TriggerUserLevelUpdateOnProfileComplete 用户完善档案时触发等级更新
func (s *UserConsumptionService) TriggerUserLevelUpdateOnProfileComplete(userID uint) error {
	if userID <= 0 {
		return fmt.Errorf("无效的用户ID")
	}

	logx.Infof("用户完善档案，触发等级检查: userID=%d", userID)
	return s.TriggerUserLevelUpdate(userID)
}

// UpdateUserConsumption 更新用户年度消费记录
func (s *UserConsumptionService) UpdateUserConsumption(userID uint, amount float64, orderType string, orderNo string) error {
	if userID <= 0 {
		return fmt.Errorf("无效的用户ID")
	}

	if amount <= 0 {
		return fmt.Errorf("消费金额必须大于0")
	}

	if orderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}

	// 根据订单类型记录不同类型的消费
	var err error
	switch orderType {
	case "goods", "product": // 商品订单
		err = s.RecordProductConsumption(userID, amount, orderNo)
	case "service": // 服务订单
		err = s.RecordServiceConsumption(userID, amount, orderNo)
	case "activity": // 活动订单
		err = s.RecordActivityConsumption(userID, amount, orderNo)
	default:
		return fmt.Errorf("未知的订单类型: %s", orderType)
	}

	if err != nil {
		return err
	}

	// 记录日志
	logx.Infof("更新用户消费记录成功: orderType=%s, userID=%d, amount=%.2f, orderNo=%s",
		orderType, userID, amount, orderNo)

	// 触发用户等级更新
	if err := s.TriggerUserLevelUpdate(userID); err != nil {
		logx.Errorf("触发用户等级更新失败: userID=%d, error=%v", userID, err)
		// 不返回错误，不影响主流程
	}

	return nil
}
