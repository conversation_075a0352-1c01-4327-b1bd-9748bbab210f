package service

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"yekaitai/pkg/hmac"
	"yekaitai/pkg/upload"

	"github.com/google/uuid"
	"github.com/skip2/go-qrcode"
	"github.com/zeromicro/go-zero/core/logx"
)

// TempDir 获取临时目录
func TempDir() string {
	return filepath.Join(os.TempDir(), "yekaitai", "qrcode")
}

// GenerateQRCodeWithData 生成并上传二维码
func GenerateQRCodeWithData(ctx context.Context, data map[string]interface{}) (string, error) {
	// 生成临时文件路径
	fileDir := TempDir()
	if err := os.MkdirAll(fileDir, 0755); err != nil {
		logx.Errorf("创建目录失败: %v", err)
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	fileName := fmt.Sprintf("%s_%d.png", uuid.New().String()[:8], time.Now().UnixNano())
	filePath := filepath.Join(fileDir, fileName)

	// 检查是否已有签名，如果没有则生成（保持向后兼容）
	if _, hasSign := data["sign"]; !hasSign {
		// 生成签名（向后兼容旧的签名方式）
		orderNo := fmt.Sprintf("%v", data["order_no"])
		verificationCode := fmt.Sprintf("%v", data["verification_code"])
		sign := hmac.GenerateHMAC(orderNo + verificationCode)
		data["sign"] = sign
	}

	// 序列化数据为JSON字符串
	content, err := json.Marshal(data)
	if err != nil {
		logx.Errorf("序列化数据失败: %v", err)
		return "", fmt.Errorf("序列化数据失败: %v", err)
	}

	// 生成二维码
	err = qrcode.WriteFile(string(content), qrcode.Medium, 256, filePath)
	if err != nil {
		logx.Errorf("生成二维码失败: %v", err)
		return "", fmt.Errorf("生成二维码失败: %v", err)
	}

	// 使用七牛云上传二维码
	fileInfo, err := os.Open(filePath)
	if err != nil {
		logx.Errorf("打开文件失败: %v", err)
		return "", fmt.Errorf("打开文件失败: %v", err)
	}
	defer fileInfo.Close()

	// 使用七牛云上传服务
	uploader := upload.DefaultQiniuUploader
	if uploader == nil {
		upload.InitDefaultQiniuUploader() // 确保七牛云上传器已初始化
		uploader = upload.DefaultQiniuUploader
	}

	fileKey := fmt.Sprintf("qrcode/registration/%s", fileName)
	fileBytes, err := os.ReadFile(filePath)
	if err != nil {
		logx.Errorf("读取文件失败: %v", err)
		return "", fmt.Errorf("读取文件失败: %v", err)
	}

	// 上传二维码
	url, err := uploader.UploadBytes(ctx, fileBytes, fileKey)
	if err != nil {
		logx.Errorf("上传二维码失败: %v", err)
		return "", fmt.Errorf("上传二维码失败: %v", err)
	}

	// 删除临时文件
	os.Remove(filePath)

	return url, nil
}

// GenerateAppointmentQRCode 生成预约挂号二维码
func GenerateAppointmentQRCode(ctx context.Context, orderID uint, orderNo string, verificationCode string) (string, error) {
	// 构建二维码数据 - 仅包含必要信息
	data := map[string]interface{}{
		"order_no":          orderNo,
		"verification_code": verificationCode,
		"timestamp":         strconv.FormatInt(time.Now().UnixNano(), 10),
	}

	// 生成签名（基于order_no + verification_code）
	signatureData := fmt.Sprintf("%s%s", orderNo, verificationCode)
	sign := hmac.GenerateHMAC(signatureData)
	data["sign"] = sign

	// 生成并上传二维码
	return GenerateQRCodeWithData(ctx, data)
}
