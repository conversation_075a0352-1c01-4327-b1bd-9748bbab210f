package logic

import (
	"context"
	"yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// LogisticsLogic 物流公司查询逻辑
type LogisticsLogic struct {
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

// NewLogisticsLogic 创建物流公司查询逻辑
func NewLogisticsLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *LogisticsLogic {
	return &LogisticsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// QueryLogisticsCompanies 查询物流公司列表
func (l *LogisticsLogic) QueryLogisticsCompanies(pageIndex, pageSize int, modifiedBegin, modifiedEnd string, wmsCoID int) (*jushuitan.BaseResp, error) {
	// 调用聚水潭API
	req := &jushuitan.LogisticsCompanyQueryRequest{
		PageIndex:     pageIndex,
		PageSize:      pageSize,
		ModifiedBegin: modifiedBegin,
		ModifiedEnd:   modifiedEnd,
		WmsCoID:       wmsCoID,
	}

	return l.svcCtx.JushuitanClient.QueryLogisticsCompanies(l.ctx, req)
}
