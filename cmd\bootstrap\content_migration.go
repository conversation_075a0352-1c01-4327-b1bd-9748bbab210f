package bootstrap

import (
	"fmt"

	"yekaitai/internal/modules/content/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// MigrateContentTables 执行内容管理模块的表结构迁移
func MigrateContentTables() error {
	fmt.Println("开始迁移内容管理模块表结构...")

	db := mysql.Master()

	// 为每个表单独设置表名注释并执行迁移
	if err := db.Set("gorm:table_options", "COMMENT='内容信息表'").AutoMigrate(&model.Content{}); err != nil {
		return fmt.Errorf("迁移内容表失败: %v", err)
	}

	if err := db.Set("gorm:table_options", "COMMENT='内容门店关联表'").AutoMigrate(&model.ContentStoreRelation{}); err != nil {
		return fmt.Errorf("迁移内容门店关联表失败: %v", err)
	}

	if err := db.Set("gorm:table_options", "COMMENT='内容报名表'").AutoMigrate(&model.ContentSignUp{}); err != nil {
		return fmt.Errorf("迁移内容报名表失败: %v", err)
	}

	if err := db.Set("gorm:table_options", "COMMENT='内容报名订单表'").AutoMigrate(&model.ContentSignUpOrder{}); err != nil {
		return fmt.Errorf("迁移内容报名订单表失败: %v", err)
	}

	// 执行数据更新
	if err := updateContentTableData(db); err != nil {
		return fmt.Errorf("更新内容表数据失败: %v", err)
	}

	fmt.Println("内容管理模块表结构迁移完成")
	return nil
}

// updateContentTableData 更新内容表的数据
func updateContentTableData(db *gorm.DB) error {
	fmt.Println("开始更新内容表数据...")

	// 更新现有数据的默认值
	// 将 max_sign_up 为 0 的记录更新为 1
	if err := db.Model(&model.Content{}).Where("max_sign_up = 0").Update("max_sign_up", 1).Error; err != nil {
		return fmt.Errorf("更新最大报名人数失败: %v", err)
	}

	// 将资讯类型的 is_new_activity 设置为 false
	if err := db.Model(&model.Content{}).Where("type = ?", "news").Update("is_new_activity", false).Error; err != nil {
		return fmt.Errorf("更新资讯新活动标记失败: %v", err)
	}

	// 检查是否存在 is_special_activity 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.Content{}, "is_special_activity") {
		fmt.Println("添加专项活动字段...")
		if err := db.Migrator().AddColumn(&model.Content{}, "is_special_activity"); err != nil {
			return fmt.Errorf("添加专项活动字段失败: %v", err)
		}
	}

	// 为现有活动数据设置专项活动字段默认值为 false
	if err := db.Model(&model.Content{}).Where("type = ? AND is_special_activity IS NULL", "activity").Update("is_special_activity", false).Error; err != nil {
		return fmt.Errorf("更新专项活动标记失败: %v", err)
	}

	// 检查并添加 ContentSignUpOrder 表的新字段
	if err := updateContentSignUpOrderFields(db); err != nil {
		return fmt.Errorf("更新报名订单表字段失败: %v", err)
	}

	fmt.Println("内容表数据更新完成")
	return nil
}

// updateContentSignUpOrderFields 更新报名订单表字段
func updateContentSignUpOrderFields(db *gorm.DB) error {
	fmt.Println("检查并更新报名订单表字段...")

	// 检查是否存在 name 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.ContentSignUpOrder{}, "name") {
		fmt.Println("添加报名人姓名字段...")
		if err := db.Migrator().AddColumn(&model.ContentSignUpOrder{}, "name"); err != nil {
			return fmt.Errorf("添加报名人姓名字段失败: %v", err)
		}
	}

	// 检查是否存在 phone 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.ContentSignUpOrder{}, "phone") {
		fmt.Println("添加报名人手机号字段...")
		if err := db.Migrator().AddColumn(&model.ContentSignUpOrder{}, "phone"); err != nil {
			return fmt.Errorf("添加报名人手机号字段失败: %v", err)
		}
	}

	// 检查是否存在 verification_code 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.ContentSignUpOrder{}, "verification_code") {
		fmt.Println("添加核销码字段...")
		if err := db.Migrator().AddColumn(&model.ContentSignUpOrder{}, "verification_code"); err != nil {
			return fmt.Errorf("添加核销码字段失败: %v", err)
		}
	}

	// 检查是否存在 verification_time 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.ContentSignUpOrder{}, "verification_time") {
		fmt.Println("添加核销时间字段...")
		if err := db.Migrator().AddColumn(&model.ContentSignUpOrder{}, "verification_time"); err != nil {
			return fmt.Errorf("添加核销时间字段失败: %v", err)
		}
	}

	// 检查是否存在 verifier_id 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.ContentSignUpOrder{}, "verifier_id") {
		fmt.Println("添加核销人ID字段...")
		if err := db.Migrator().AddColumn(&model.ContentSignUpOrder{}, "verifier_id"); err != nil {
			return fmt.Errorf("添加核销人ID字段失败: %v", err)
		}
	}

	// 检查是否存在 verifier_name 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.ContentSignUpOrder{}, "verifier_name") {
		fmt.Println("添加核销人姓名字段...")
		if err := db.Migrator().AddColumn(&model.ContentSignUpOrder{}, "verifier_name"); err != nil {
			return fmt.Errorf("添加核销人姓名字段失败: %v", err)
		}
	}

	// 检查是否存在 qr_code_url 字段，如果不存在则添加
	if !db.Migrator().HasColumn(&model.ContentSignUpOrder{}, "qr_code_url") {
		fmt.Println("添加二维码URL字段...")
		if err := db.Migrator().AddColumn(&model.ContentSignUpOrder{}, "qr_code_url"); err != nil {
			return fmt.Errorf("添加二维码URL字段失败: %v", err)
		}
	}

	// 更新订单状态注释
	fmt.Println("更新订单状态注释...")
	if err := db.Exec(`
		ALTER TABLE t_content_sign_up_orders
		MODIFY COLUMN status bigint DEFAULT '1' COMMENT '订单状态:1-已报名,2-已取消,3-已取消已退款,4-已核销'
	`).Error; err != nil {
		// 如果修改失败，记录日志但不中断流程
		fmt.Printf("更新订单状态注释失败: %v\n", err)
	}

	fmt.Println("报名订单表字段更新完成")
	return nil
}
