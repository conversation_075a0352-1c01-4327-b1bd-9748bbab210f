package bootstrap

import (
	"log"

	"yekaitai/pkg/common/model/evaluation"
	"yekaitai/pkg/infra/mysql"
)

// MigrateServiceEvaluationTables 执行服务评价相关表结构迁移
func MigrateServiceEvaluationTables() error {
	log.Println("开始执行服务评价表结构迁移...")

	// 执行服务评价表结构迁移
	db := mysql.Master()

	// 迁移服务评价表
	err := db.Set("gorm:table_options", "COMMENT='服务评价表'").AutoMigrate(&evaluation.ServiceEvaluation{})
	if err != nil {
		log.Printf("服务评价表迁移失败: %v", err)
		return err
	}

	log.Println("服务评价表结构迁移完成")
	return nil
}
