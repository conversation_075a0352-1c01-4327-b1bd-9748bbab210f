package routes

import (
	"net/http"

	evaluationHandler "yekaitai/wx_internal/modules/evaluation/handler"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterEvaluationRoutes 注册服务评价相关路由
func RegisterEvaluationRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用微信认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 创建服务评价处理器实例
	evaluationHandlerInst := evaluationHandler.NewServiceEvaluationHandler()

	// 服务评价相关路由（需要认证）
	server.AddRoutes(
		[]rest.Route{
			// 获取用户评价状态
			{
				Method:  http.MethodGet,
				Path:    "/api/wx/evaluation/status",
				Handler: wxAuthWrapper(evaluationHandlerInst.GetEvaluationStatus),
			},
			// 提交服务评价
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/evaluation",
				Handler: wxAuthWrapper(evaluationHandlerInst.CreateEvaluation),
			},
		},
	)
}
