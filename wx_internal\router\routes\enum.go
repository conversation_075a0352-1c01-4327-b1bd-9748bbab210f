package routes

import (
	"net/http"
	patientHandler "yekaitai/internal/modules/patient/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterEnumRoutes 注册枚举相关路由（需要微信认证）
func RegisterEnumRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 创建枚举处理器
	enumHandler := patientHandler.NewEnumHandler(serverCtx.ServiceContext)

	// 添加需要微信认证的路由
	server.AddRoutes([]rest.Route{
		// 获取民族列表
		{
			Method:  http.MethodGet,
			Path:    "/api/enum/ethnicities",
			Handler: enumHandler.GetEthnicities,
		},
		// 获取与本人关系列表
		{
			Method:  http.MethodGet,
			Path:    "/api/enum/relationships",
			Handler: enumHandler.GetRelationships,
		},
	})
}
