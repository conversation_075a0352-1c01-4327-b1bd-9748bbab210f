package service

import (
	"context"
	"fmt"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type ShippingService struct {
	db *gorm.DB
}

func NewShippingService() *ShippingService {
	return &ShippingService{
		db: mysql.GetDB(),
	}
}

// SetShippingConfig 设置运费配置
func (s *ShippingService) SetShippingConfig(ctx context.Context, req *model.ShippingConfigRequest) (*model.ShippingConfig, error) {
	// 先删除所有现有配置（单一配置）
	s.db.WithContext(ctx).Where("1=1").Delete(&model.ShippingConfig{})

	config := &model.ShippingConfig{
		ConfigType:  req.ConfigType,
		FreeAmount:  req.FreeAmount,
		ShippingFee: req.ShippingFee,
		IsActive:    1,
		Description: req.Description,
	}

	if err := s.db.WithContext(ctx).Create(config).Error; err != nil {
		return nil, fmt.Errorf("设置运费配置失败: %w", err)
	}

	return config, nil
}

// GetShippingConfig 获取运费配置
func (s *ShippingService) GetShippingConfig(ctx context.Context) (*model.ShippingConfig, error) {
	config := &model.ShippingConfig{}
	if err := s.db.WithContext(ctx).Where("is_active = ?", 1).First(config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("运费配置未设置，请在管理后台配置运费规则")
		}
		return nil, fmt.Errorf("查询运费配置失败: %w", err)
	}
	return config, nil
}

// CalculateShippingFee 计算运费
func (s *ShippingService) CalculateShippingFee(ctx context.Context, req *model.ShippingCalculateRequest) (*model.ShippingCalculateResponse, error) {
	config, err := s.GetShippingConfig(ctx)
	if err != nil {
		return nil, err
	}

	response := &model.ShippingCalculateResponse{
		ConfigType: config.ConfigType,
	}

	switch config.ConfigType {
	case 1: // 全部包邮
		response.ShippingFee = 0
		response.IsFree = true
		response.Message = "全场包邮"

	case 2: // 满减运费
		if config.FreeAmount > 0 && req.TotalAmount >= config.FreeAmount {
			response.ShippingFee = 0
			response.IsFree = true
			response.Message = fmt.Sprintf("满%.2f元包邮", config.FreeAmount)
		} else {
			response.ShippingFee = config.ShippingFee
			response.IsFree = false
			if config.FreeAmount > 0 {
				remaining := config.FreeAmount - req.TotalAmount
				response.Message = fmt.Sprintf("运费%.2f元，再买%.2f元即可包邮", config.ShippingFee, remaining)
			} else {
				response.Message = fmt.Sprintf("运费%.2f元", config.ShippingFee)
			}
		}

	default:
		response.ShippingFee = config.ShippingFee
		response.IsFree = false
		response.Message = fmt.Sprintf("运费%.2f元", config.ShippingFee)
	}

	return response, nil
}
