package handler

import (
	"fmt"
	"net/http"
	"time"

	contentModel "yekaitai/internal/modules/content/model"
	storeModel "yekaitai/internal/modules/store/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 内容列表请求
type ContentListRequest struct {
	types.PageRequest
	ContentType string `form:"contentType,optional"` // 内容类型过滤：news-资讯,activity-活动
	StoreID     string `form:"storeId,optional"`     // 门店ID过滤
	StartTime   string `form:"startTime,optional"`   // 创建开始时间 格式:2025-06-01
	EndTime     string `form:"endTime,optional"`     // 创建结束时间 格式:2025-06-01
}

// 内容详情请求
type ContentDetailRequest struct {
	ContentID uint `path:"contentId"` // 内容ID
}

// 创建内容请求
type CreateContentRequest struct {
	Title             string     `json:"title"`               // 内容标题(最多30个中文字符)
	Type              string     `json:"type"`                // 内容类型:news-资讯,activity-活动(必填)
	Format            string     `json:"format"`              // 内容格式:text-图文,video-视频(必填)
	Description       string     `json:"description"`         // 内容描述
	Content           string     `json:"content"`             // 内容详情(最多300个中文字符)
	CoverImage        string     `json:"cover_image"`         // 封面图片URL(必填)
	VideoUrl          string     `json:"video_url"`           // 视频URL(视频格式时必填)
	IsEnabled         bool       `json:"is_enabled"`          // 是否上架
	IsRecommended     bool       `json:"is_recommended"`      // 是否推荐
	RecommendSort     int        `json:"recommend_sort"`      // 推荐排序值(从1开始,越小越靠前,0为默认)
	CanSignUp         bool       `json:"can_sign_up"`         // 是否可报名(活动类型时必填)
	MaxSignUp         int        `json:"max_sign_up"`         // 最大报名人数(1-10000,活动类型时使用)
	SignUpDeadline    *time.Time `json:"sign_up_deadline"`    // 报名截止时间
	SignUpMethod      string     `json:"sign_up_method"`      // 报名方式:phone-手机号,name-姓名,idcard-身份证号
	SignUpAmount      int        `json:"sign_up_amount"`      // 报名费用(分,0表示免费)
	IsSpecialActivity bool       `json:"is_special_activity"` // 是否专项活动(活动类型时使用)
	StoreIDs          []uint     `json:"store_ids"`           // 绑定的门店ID列表(活动类型必选)
}

// 更新内容请求
type UpdateContentRequest struct {
	ContentID         uint       `path:"contentId"`           // 内容ID
	Title             string     `json:"title"`               // 内容标题(最多30个中文字符)
	Type              string     `json:"type"`                // 内容类型:news-资讯,activity-活动
	Format            string     `json:"format"`              // 内容格式:text-图文,video-视频
	Description       string     `json:"description"`         // 内容描述
	Content           string     `json:"content"`             // 内容详情(最多300个中文字符)
	CoverImage        string     `json:"cover_image"`         // 封面图片URL
	VideoUrl          string     `json:"video_url"`           // 视频URL
	IsRecommended     bool       `json:"is_recommended"`      // 是否推荐
	RecommendSort     int        `json:"recommend_sort"`      // 推荐排序值
	CanSignUp         bool       `json:"can_sign_up"`         // 是否可报名
	MaxSignUp         int        `json:"max_sign_up"`         // 最大报名人数(1-10000)
	SignUpDeadline    *time.Time `json:"sign_up_deadline"`    // 报名截止时间
	SignUpMethod      string     `json:"sign_up_method"`      // 报名方式:phone-手机号,name-姓名,idcard-身份证号
	SignUpAmount      int        `json:"sign_up_amount"`      // 报名费用(分,0表示免费)
	IsSpecialActivity bool       `json:"is_special_activity"` // 是否专项活动(活动类型时使用)
	StoreIDs          []uint     `json:"store_ids"`           // 绑定的门店ID列表(活动类型需要)
}

// 更新内容状态请求
type UpdateContentStatusRequest struct {
	ContentID uint `path:"contentId"`  // 内容ID
	IsEnabled bool `json:"is_enabled"` // 是否启用
}

// 删除内容请求
type DeleteContentRequest struct {
	ContentID uint `path:"contentId"` // 内容ID
}

// 内容报名请求
type ContentSignUpRequest struct {
	ContentID uint   `path:"contentId"` // 内容ID
	UserID    uint   `json:"user_id"`   // 用户ID
	Name      string `json:"name"`      // 姓名
	Phone     string `json:"phone"`     // 手机号
	IDCard    string `json:"id_card"`   // 身份证号
	Remark    string `json:"remark"`    // 备注
}

// 内容报名列表请求
type ContentSignUpListRequest struct {
	types.PageRequest
	ContentID uint `path:"contentId"` // 内容ID
}

// 报名订单列表请求
type SignUpOrderListRequest struct {
	types.PageRequest
	ContentID uint `path:"contentId"` // 内容ID
}

// 报名订单详情请求
type SignUpOrderDetailRequest struct {
	OrderID uint `path:"orderId"` // 订单ID
}

// ContentHandler 内容处理器
type ContentHandler struct {
	svcCtx *svc.ServiceContext
}

// NewContentHandler 创建内容处理器
func NewContentHandler(svcCtx *svc.ServiceContext) *ContentHandler {
	return &ContentHandler{
		svcCtx: svcCtx,
	}
}

// ListContents 获取内容列表
func (h *ContentHandler) ListContents(w http.ResponseWriter, r *http.Request) {
	var req ContentListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取内容列表: page=%d, size=%d, query=%s, type=%s, storeId=%s, startTime=%s, endTime=%s",
		req.Page, req.Size, req.Query, req.ContentType, req.StoreID, req.StartTime, req.EndTime)

	// 解析时间参数
	var startTime, endTime *time.Time
	if req.StartTime != "" {
		if t, err := time.Parse("2006-01-02", req.StartTime); err == nil {
			startTime = &t
		}
	}
	if req.EndTime != "" {
		if t, err := time.Parse("2006-01-02", req.EndTime); err == nil {
			// 结束时间设为当天的23:59:59
			t = t.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			endTime = &t
		}
	}

	contentRepo := h.svcCtx.ContentRepo
	contents, total, err := contentRepo.List(req.Page, req.Size, req.Query, req.ContentType, req.StoreID, startTime, endTime)
	if err != nil {
		logx.Errorf("获取内容列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取内容列表失败"))
		return
	}

	// 处理数据
	result := make([]map[string]interface{}, len(contents))
	for i, content := range contents {
		// 获取内容绑定的门店
		storeIDs, _ := contentRepo.GetContentStores(content.ID)

		// 获取门店信息
		var stores []map[string]interface{}
		if len(storeIDs) > 0 {
			storeRepo := storeModel.NewStoreRepository(mysql.Slave())
			for _, storeID := range storeIDs {
				store, err := storeRepo.FindByID(storeID)
				if err == nil {
					stores = append(stores, map[string]interface{}{
						"id":   store.ID,
						"name": store.Name,
					})
				}
			}
		}

		// 获取创建人信息
		createdByName := "系统"
		if content.CreatedBy > 0 {
			adminUser, err := h.svcCtx.AdminUserRepo.FindByID(content.CreatedBy)
			if err == nil {
				createdByName = adminUser.Username
			}
		}

		result[i] = map[string]interface{}{
			"id":                  content.ID,
			"title":               content.Title,
			"type":                content.Type,
			"format":              content.Format,
			"description":         content.Description,
			"cover_image":         content.CoverImage,
			"video_url":           content.VideoUrl,
			"is_enabled":          content.IsEnabled,
			"is_recommended":      content.IsRecommended,
			"recommend_sort":      content.RecommendSort,
			"view_count":          content.ViewCount,
			"can_sign_up":         content.CanSignUp,
			"max_sign_up":         content.MaxSignUp,
			"sign_up_deadline":    content.SignUpDeadline,
			"sign_up_count":       content.SignUpCount,
			"sign_up_method":      content.SignUpMethod,
			"sign_up_amount":      content.SignUpAmount,
			"is_special_activity": content.IsSpecialActivity,
			"created_by":          content.CreatedBy,
			"created_by_name":     createdByName,
			"created_at":          content.CreatedAt,
			"updated_at":          content.UpdatedAt,
			"stores":              stores,
			"store_ids":           storeIDs,
		}
	}

	logx.Infof("获取内容列表成功: 共%d条记录", total)

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取内容列表成功"))
}

// GetContent 获取内容详情
func (h *ContentHandler) GetContent(w http.ResponseWriter, r *http.Request) {
	var req ContentDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取内容详情: contentId=%d", req.ContentID)

	contentRepo := h.svcCtx.ContentRepo
	content, err := contentRepo.FindByID(req.ContentID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "内容不存在"))
		} else {
			logx.Errorf("获取内容详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取内容详情失败"))
		}
		return
	}

	// 获取内容绑定的门店
	storeIDs, _ := contentRepo.GetContentStores(content.ID)

	// 获取门店信息
	var stores []map[string]interface{}
	if len(storeIDs) > 0 {
		storeRepo := storeModel.NewStoreRepository(mysql.Slave())
		for _, storeID := range storeIDs {
			store, err := storeRepo.FindByID(storeID)
			if err == nil {
				stores = append(stores, map[string]interface{}{
					"id":   store.ID,
					"name": store.Name,
				})
			}
		}
	}

	// 获取创建人信息
	createdByName := "系统"
	if content.CreatedBy > 0 {
		adminUser, err := h.svcCtx.AdminUserRepo.FindByID(content.CreatedBy)
		if err == nil {
			createdByName = adminUser.Username
		}
	}

	// 构建结果
	result := map[string]interface{}{
		"id":                  content.ID,
		"title":               content.Title,
		"type":                content.Type,
		"format":              content.Format,
		"description":         content.Description,
		"content":             content.Content,
		"cover_image":         content.CoverImage,
		"video_url":           content.VideoUrl,
		"is_enabled":          content.IsEnabled,
		"is_recommended":      content.IsRecommended,
		"recommend_sort":      content.RecommendSort,
		"view_count":          content.ViewCount,
		"can_sign_up":         content.CanSignUp,
		"max_sign_up":         content.MaxSignUp,
		"sign_up_deadline":    content.SignUpDeadline,
		"sign_up_count":       content.SignUpCount,
		"sign_up_method":      content.SignUpMethod,
		"sign_up_amount":      content.SignUpAmount,
		"is_special_activity": content.IsSpecialActivity,
		"created_by":          content.CreatedBy,
		"created_by_name":     createdByName,
		"created_at":          content.CreatedAt,
		"updated_at":          content.UpdatedAt,
		"stores":              stores,
		"store_ids":           storeIDs,
	}

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取内容详情成功"))
}

// CreateContent 创建内容
func (h *ContentHandler) CreateContent(w http.ResponseWriter, r *http.Request) {
	var req CreateContentRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 检查必填字段
	if req.Title == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "内容标题不能为空"))
		return
	}

	// 检查标题长度(最多30个中文字符，约90字节)
	if len([]rune(req.Title)) > 30 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "内容标题最多30个字符"))
		return
	}

	if req.Type == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "内容类型不能为空"))
		return
	}

	if req.Format == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "内容格式不能为空"))
		return
	}

	if req.CoverImage == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "封面图片不能为空"))
		return
	}

	// 检查内容长度(最多300个中文字符)
	if req.Content != "" && len([]rune(req.Content)) > 300 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "内容详情最多300个字符"))
		return
	}

	// 如果是视频格式，检查视频URL
	if req.Format == string(contentModel.ContentFormatVideo) && req.VideoUrl == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "视频格式内容必须提供视频URL"))
		return
	}

	// 如果是活动类型，检查是否提供了门店信息和报名设置
	if req.Type == string(contentModel.ContentTypeActivity) {
		if len(req.StoreIDs) == 0 {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "活动类型内容必须绑定门店"))
			return
		}
		// 活动类型时，是否可报名为必填
		// 这里不需要额外验证，因为布尔值有默认值

		// 如果可以报名，检查最大报名人数
		if req.CanSignUp && (req.MaxSignUp < 1 || req.MaxSignUp > 10000) {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "最大报名人数必须在1-10000之间"))
			return
		}
	}

	// 检查推荐排序值
	if req.RecommendSort < 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "推荐排序值不能为负数"))
		return
	}

	// 获取当前管理员ID
	adminIDStr, _ := r.Context().Value("admin_id").(string)
	adminID := utils.StringToUint(adminIDStr)
	if adminID == 0 {
		adminID = 1 // 默认为系统管理员
	}

	// 设置默认值
	maxSignUp := req.MaxSignUp
	if maxSignUp == 0 {
		maxSignUp = 1 // 默认最大报名人数为1
	}

	isNewActivity := false
	if req.Type == string(contentModel.ContentTypeActivity) {
		isNewActivity = true // 新创建的活动标记为新活动
	}

	// 创建内容
	content := &contentModel.Content{
		Title:             req.Title,
		Type:              req.Type,
		Format:            req.Format,
		Description:       req.Description,
		Content:           req.Content,
		CoverImage:        req.CoverImage,
		VideoUrl:          req.VideoUrl,
		IsEnabled:         req.IsEnabled,
		IsRecommended:     req.IsRecommended,
		RecommendSort:     req.RecommendSort,
		CanSignUp:         req.CanSignUp,
		MaxSignUp:         maxSignUp,
		SignUpDeadline:    req.SignUpDeadline,
		SignUpMethod:      req.SignUpMethod,
		SignUpAmount:      req.SignUpAmount,
		IsNewActivity:     isNewActivity,
		IsSpecialActivity: req.IsSpecialActivity,
		CreatedBy:         adminID,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	contentRepo := h.svcCtx.ContentRepo
	if err := contentRepo.Create(content); err != nil {
		logx.Errorf("创建内容失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建内容失败"))
		return
	}

	// 如果有门店ID，绑定门店
	if len(req.StoreIDs) > 0 {
		// 验证门店是否存在
		storeRepo := storeModel.NewStoreRepository(mysql.Slave())
		for _, storeID := range req.StoreIDs {
			_, err := storeRepo.FindByID(storeID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, fmt.Sprintf("门店ID %d 不存在", storeID)))
				} else {
					logx.Errorf("获取门店失败: %v", err)
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建内容失败"))
				}
				return
			}
		}

		if err := contentRepo.BindStores(content.ID, req.StoreIDs); err != nil {
			logx.Errorf("绑定门店失败: %v", err)
			// 不返回错误，继续处理
		}
	}

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// go h.logAdminOperation(r, "内容管理", "创建", content.ID, "Content", fmt.Sprintf("创建内容: %s", content.Title))

	httpx.OkJson(w, types.NewSuccessResponse(content, "创建内容成功"))
}

// UpdateContent 更新内容
func (h *ContentHandler) UpdateContent(w http.ResponseWriter, r *http.Request) {
	var req UpdateContentRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析更新内容请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	contentRepo := h.svcCtx.ContentRepo

	// 检查内容是否存在
	content, err := contentRepo.FindByID(req.ContentID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "内容不存在"))
		} else {
			logx.Errorf("获取内容失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新内容失败"))
		}
		return
	}

	// 检查是否为上架状态，上架状态不能编辑
	if content.IsEnabled {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "上架状态的内容不能编辑，请先下架"))
		return
	}

	// 验证更新字段
	if req.Title != "" {
		// 检查标题长度(最多30个中文字符)
		if len([]rune(req.Title)) > 30 {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "内容标题最多30个字符"))
			return
		}
		content.Title = req.Title
	}

	if req.Type != "" {
		content.Type = req.Type
	}

	if req.Format != "" {
		content.Format = req.Format
	}

	if req.Description != "" {
		content.Description = req.Description
	}

	if req.Content != "" {
		// 检查内容长度(最多300个中文字符)
		if len([]rune(req.Content)) > 300 {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "内容详情最多300个字符"))
			return
		}
		content.Content = req.Content
	}

	if req.CoverImage != "" {
		content.CoverImage = req.CoverImage
	}

	if req.VideoUrl != "" {
		content.VideoUrl = req.VideoUrl
	}

	// 如果是视频格式，检查视频URL
	if content.Format == string(contentModel.ContentFormatVideo) && content.VideoUrl == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "视频格式内容必须提供视频URL"))
		return
	}

	// 如果是活动类型且可以报名，检查最大报名人数
	if content.Type == string(contentModel.ContentTypeActivity) && req.CanSignUp && (req.MaxSignUp < 1 || req.MaxSignUp > 10000) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "最大报名人数必须在1-10000之间"))
		return
	}

	// 布尔型和数值型字段直接赋值，因为零值也有意义
	content.IsRecommended = req.IsRecommended
	content.RecommendSort = req.RecommendSort
	content.CanSignUp = req.CanSignUp
	content.MaxSignUp = req.MaxSignUp
	content.SignUpAmount = req.SignUpAmount
	content.IsSpecialActivity = req.IsSpecialActivity

	if req.SignUpDeadline != nil {
		content.SignUpDeadline = req.SignUpDeadline
	}

	if req.SignUpMethod != "" {
		content.SignUpMethod = req.SignUpMethod
	}

	content.UpdatedAt = time.Now()

	// 更新内容
	if err := contentRepo.Update(content); err != nil {
		logx.Errorf("更新内容失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新内容失败"))
		return
	}

	// 处理门店绑定
	if len(req.StoreIDs) > 0 {
		// 验证门店是否存在
		storeRepo := storeModel.NewStoreRepository(mysql.Slave())
		for _, storeID := range req.StoreIDs {
			_, err := storeRepo.FindByID(storeID)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, fmt.Sprintf("门店ID %d 不存在", storeID)))
				} else {
					logx.Errorf("获取门店失败: %v", err)
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新内容失败"))
				}
				return
			}
		}

		if err := contentRepo.BindStores(content.ID, req.StoreIDs); err != nil {
			logx.Errorf("绑定门店失败: %v", err)
			// 不返回错误，继续处理
		}
	}

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// go h.logAdminOperation(r, "内容管理", "更新", content.ID, "Content", fmt.Sprintf("更新内容: %s", content.Title))

	httpx.OkJson(w, types.NewSuccessResponse(content, "更新内容成功"))
}

// UpdateContentStatus 更新内容状态
func (h *ContentHandler) UpdateContentStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdateContentStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("更新内容状态: contentId=%d, isEnabled=%v", req.ContentID, req.IsEnabled)

	contentRepo := h.svcCtx.ContentRepo

	// 检查内容是否存在
	content, err := contentRepo.FindByID(req.ContentID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "内容不存在"))
		} else {
			logx.Errorf("获取内容失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新内容状态失败"))
		}
		return
	}

	// 检查状态是否已经是目标状态
	if content.IsEnabled == req.IsEnabled {
		statusText := "上架"
		if !req.IsEnabled {
			statusText = "下架"
		}
		httpx.OkJson(w, types.NewSuccessResponse(nil, fmt.Sprintf("内容已处于%s状态", statusText)))
		return
	}

	// 更新状态
	if err := contentRepo.UpdateStatus(req.ContentID, req.IsEnabled); err != nil {
		logx.Errorf("更新内容状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新内容状态失败"))
		return
	}

	statusText := "上架"
	if !req.IsEnabled {
		statusText = "下架"
	}

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// go h.logAdminOperation(r, "内容管理", "更新状态", content.ID, "Content", fmt.Sprintf("%s内容: %s", statusText, content.Title))

	httpx.OkJson(w, types.NewSuccessResponse(nil, fmt.Sprintf("内容%s成功", statusText)))
}

// DeleteContent 删除内容
func (h *ContentHandler) DeleteContent(w http.ResponseWriter, r *http.Request) {
	var req DeleteContentRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("删除内容: contentId=%d", req.ContentID)

	contentRepo := h.svcCtx.ContentRepo

	// 检查内容是否存在
	_, err := contentRepo.FindByID(req.ContentID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "内容不存在"))
		} else {
			logx.Errorf("获取内容失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除内容失败"))
		}
		return
	}

	// 删除内容
	if err := contentRepo.Delete(req.ContentID); err != nil {
		logx.Errorf("删除内容失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除内容失败"))
		return
	}

	// 记录操作日志 - 已由中间件统一处理，无需手动记录
	// go h.logAdminOperation(r, "内容管理", "删除", content.ID, "Content", fmt.Sprintf("删除内容: %s", content.Title))

	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除内容成功"))
}

// GetContentSignUps 获取内容报名列表
func (h *ContentHandler) GetContentSignUps(w http.ResponseWriter, r *http.Request) {
	var req ContentSignUpListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取内容报名列表: contentId=%d, page=%d, size=%d", req.ContentID, req.Page, req.Size)

	contentRepo := h.svcCtx.ContentRepo

	// 检查内容是否存在
	content, err := contentRepo.FindByID(req.ContentID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "内容不存在"))
		} else {
			logx.Errorf("获取内容失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取内容报名列表失败"))
		}
		return
	}

	// 获取报名列表
	signUps, total, err := contentRepo.GetContentSignUps(req.ContentID, req.Page, req.Size)
	if err != nil {
		logx.Errorf("获取内容报名列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取内容报名列表失败"))
		return
	}

	// 处理数据
	result := make([]map[string]interface{}, len(signUps))
	for i, signUp := range signUps {
		result[i] = map[string]interface{}{
			"id":           signUp.ID,
			"content_id":   signUp.ContentID,
			"user_id":      signUp.UserID,
			"name":         signUp.Name,
			"phone":        signUp.Phone,
			"id_card":      signUp.IDCard,
			"sign_up_time": signUp.SignUpTime,
			"status":       signUp.Status,
			"remark":       signUp.Remark,
			"created_at":   signUp.CreatedAt,
			"updated_at":   signUp.UpdatedAt,
		}
	}

	logx.Infof("获取内容报名列表成功: 内容=%s, 共%d条记录", content.Title, total)

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取内容报名列表成功"))
}

// ContentSignUp 内容报名
func (h *ContentHandler) ContentSignUp(w http.ResponseWriter, r *http.Request) {
	var req ContentSignUpRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	contentRepo := h.svcCtx.ContentRepo

	// 检查内容是否存在
	content, err := contentRepo.FindByID(req.ContentID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "内容不存在"))
		} else {
			logx.Errorf("获取内容失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "内容报名失败"))
		}
		return
	}

	// 检查内容是否可以报名
	if !content.CanSignUp {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该内容不支持报名"))
		return
	}

	// 检查是否已达到报名上限
	if content.MaxSignUp > 0 && content.SignUpCount >= content.MaxSignUp {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "报名人数已达上限"))
		return
	}

	// 检查报名截止时间
	if content.SignUpDeadline != nil && content.SignUpDeadline.Before(time.Now()) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "报名已截止"))
		return
	}

	// 检查用户是否已报名
	isSignedUp, err := contentRepo.CheckUserSignUp(req.ContentID, req.UserID)
	if err != nil {
		logx.Errorf("检查用户是否已报名失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "内容报名失败"))
		return
	}

	if isSignedUp {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "您已报名，请勿重复报名"))
		return
	}

	// 创建报名记录
	signUp := &contentModel.ContentSignUp{
		ContentID:  req.ContentID,
		UserID:     req.UserID,
		Name:       req.Name,
		Phone:      req.Phone,
		IDCard:     req.IDCard,
		SignUpTime: time.Now(),
		Status:     1,
		Remark:     req.Remark,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 添加报名记录
	if err := contentRepo.AddSignUp(signUp); err != nil {
		logx.Errorf("添加报名记录失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "内容报名失败"))
		return
	}

	httpx.OkJson(w, types.NewSuccessResponse(signUp, "报名成功"))
}

// GetSignUpOrders 获取报名订单列表
func (h *ContentHandler) GetSignUpOrders(w http.ResponseWriter, r *http.Request) {
	var req SignUpOrderListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取报名订单列表: contentId=%d, page=%d, size=%d", req.ContentID, req.Page, req.Size)

	contentRepo := h.svcCtx.ContentRepo

	// 检查内容是否存在
	content, err := contentRepo.FindByID(req.ContentID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "内容不存在"))
		} else {
			logx.Errorf("获取内容失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取报名订单列表失败"))
		}
		return
	}

	// 获取报名订单列表
	orders, total, err := contentRepo.GetSignUpOrdersByContentID(req.ContentID, req.Page, req.Size)
	if err != nil {
		logx.Errorf("获取报名订单列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取报名订单列表失败"))
		return
	}

	// 处理数据
	result := make([]map[string]interface{}, len(orders))
	for i, order := range orders {
		// 获取用户信息
		userName := ""
		userPhone := ""
		if order.UserID > 0 {
			// 这里需要根据实际的用户模型来获取用户信息
			// user, err := h.svcCtx.UserRepo.FindByID(order.UserID)
			// if err == nil {
			//     userName = user.Name
			//     userPhone = user.Phone
			// }
		}

		// 订单状态文本
		statusText := ""
		switch order.Status {
		case 1:
			statusText = "已核销"
		case 2:
			statusText = "已取消"
		case 3:
			statusText = "已取消已退款"
		case 4:
			statusText = "已过期已退款"
		}

		result[i] = map[string]interface{}{
			"id":            order.ID,
			"order_no":      order.OrderNo,
			"content_id":    order.ContentID,
			"user_id":       order.UserID,
			"user_name":     userName,
			"user_phone":    userPhone,
			"amount":        order.Amount,
			"status":        order.Status,
			"status_text":   statusText,
			"payment_time":  order.PaymentTime,
			"payment_type":  order.PaymentType,
			"refund_time":   order.RefundTime,
			"refund_amount": order.RefundAmount,
			"created_at":    order.CreatedAt,
			"updated_at":    order.UpdatedAt,
		}
	}

	logx.Infof("获取报名订单列表成功: 内容=%s, 共%d条记录", content.Title, total)

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取报名订单列表成功"))
}

// GetSignUpOrderDetail 获取报名订单详情
func (h *ContentHandler) GetSignUpOrderDetail(w http.ResponseWriter, r *http.Request) {
	var req SignUpOrderDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取报名订单详情: orderId=%d", req.OrderID)

	contentRepo := h.svcCtx.ContentRepo

	// 获取订单详情
	order, err := contentRepo.GetSignUpOrderByID(req.OrderID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在"))
		} else {
			logx.Errorf("获取订单详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取订单详情失败"))
		}
		return
	}

	// 获取内容信息
	content, err := contentRepo.FindByID(order.ContentID)
	if err != nil {
		logx.Errorf("获取内容信息失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取订单详情失败"))
		return
	}

	// 获取用户信息
	userName := ""
	userPhone := ""
	if order.UserID > 0 {
		// 这里需要根据实际的用户模型来获取用户信息
		// user, err := h.svcCtx.UserRepo.FindByID(order.UserID)
		// if err == nil {
		//     userName = user.Name
		//     userPhone = user.Phone
		// }
	}

	// 订单状态文本
	statusText := ""
	switch order.Status {
	case 1:
		statusText = "已核销"
	case 2:
		statusText = "已取消"
	case 3:
		statusText = "已取消已退款"
	case 4:
		statusText = "已过期已退款"
	}

	// 构建结果
	result := map[string]interface{}{
		"id":            order.ID,
		"order_no":      order.OrderNo,
		"content_id":    order.ContentID,
		"content_title": content.Title,
		"user_id":       order.UserID,
		"user_name":     userName,
		"user_phone":    userPhone,
		"amount":        order.Amount,
		"status":        order.Status,
		"status_text":   statusText,
		"payment_time":  order.PaymentTime,
		"payment_type":  order.PaymentType,
		"refund_time":   order.RefundTime,
		"refund_amount": order.RefundAmount,
		"created_at":    order.CreatedAt,
		"updated_at":    order.UpdatedAt,
	}

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取订单详情成功"))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *ContentHandler) logAdminOperation(r *http.Request, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 模块=%s, 操作=%s", module, action)
}
