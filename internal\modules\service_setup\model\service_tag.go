package model

import (
	"time"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// ServiceTag 服务套餐标签模型
type ServiceTag struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:标签ID"`
	Name      string         `json:"name" gorm:"type:varchar(15);not null;comment:标签名称"`         // 1-15字
	Status    string         `json:"status" gorm:"type:varchar(20);default:'active';comment:状态"` // active启用/disabled禁用
	CreatedBy uint           `json:"created_by" gorm:"comment:创建人ID"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (s *ServiceTag) TableName() string {
	return "service_tags"
}

// ServiceTagRepository 标签仓库接口
type ServiceTagRepository interface {
	// 基础CRUD
	Create(tag *ServiceTag) error
	Update(tag *ServiceTag) error
	Delete(id uint) error
	FindByID(id uint) (*ServiceTag, error)
	List(page, size int, query string) ([]*ServiceTag, int64, error)

	// 标签状态管理
	UpdateStatus(id uint, status string) error

	// 检查标签名称是否存在
	CheckNameExists(name string, excludeID uint) (bool, error)

	// 检查标签下是否有套餐
	HasPackages(id uint) (bool, error)
}

// serviceTagRepository 标签仓库实现
type serviceTagRepository struct {
	db *gorm.DB
}

// NewServiceTagRepository 创建标签仓库
func NewServiceTagRepository() ServiceTagRepository {
	return &serviceTagRepository{
		db: mysql.Master(),
	}
}

// Create 创建标签
func (r *serviceTagRepository) Create(tag *ServiceTag) error {
	return r.db.Create(tag).Error
}

// Update 更新标签
func (r *serviceTagRepository) Update(tag *ServiceTag) error {
	return r.db.Save(tag).Error
}

// Delete 删除标签
func (r *serviceTagRepository) Delete(id uint) error {
	return r.db.Delete(&ServiceTag{}, id).Error
}

// FindByID 根据ID查找标签
func (r *serviceTagRepository) FindByID(id uint) (*ServiceTag, error) {
	var tag ServiceTag
	err := r.db.First(&tag, id).Error
	if err != nil {
		return nil, err
	}
	return &tag, nil
}

// List 获取标签列表
func (r *serviceTagRepository) List(page, size int, query string) ([]*ServiceTag, int64, error) {
	var tags []*ServiceTag
	var total int64

	db := r.db.Model(&ServiceTag{})

	// 条件筛选
	if query != "" {
		db = db.Where("name LIKE ?", "%"+query+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && size > 0 {
		offset := (page - 1) * size
		db = db.Offset(offset).Limit(size)
	}

	if err := db.Order("id DESC").Find(&tags).Error; err != nil {
		return nil, 0, err
	}

	return tags, total, nil
}

// UpdateStatus 更新标签状态
func (r *serviceTagRepository) UpdateStatus(id uint, status string) error {
	return r.db.Model(&ServiceTag{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error
}

// CheckNameExists 检查标签名称是否存在
func (r *serviceTagRepository) CheckNameExists(name string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.Model(&ServiceTag{}).Where("name = ?", name)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// HasPackages 检查标签下是否有套餐
func (r *serviceTagRepository) HasPackages(id uint) (bool, error) {
	var count int64
	err := r.db.Model(&ServicePackage{}).Where("tag_id = ?", id).Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}
