package routes

import (
	"net/http"

	storeHandler "yekaitai/internal/modules/store/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterStoreRoutes 注册门店管理相关路由
func RegisterStoreRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建门店处理器
	handler := storeHandler.NewStoreHandler(serverCtx)

	// 注册路由 - 不需要手动包装认证中间件，由 registerWithAdminAuth 统一处理
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/stores",
				Handler: handler.ListStores,
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/stores/:storeId",
				Handler: handler.GetStore,
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/stores",
				Handler: handler.CreateStore,
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/stores/:storeId",
				Handler: handler.UpdateStore,
			},
			{
				Method:  http.MethodPatch,
				Path:    "/api/admin/stores/:storeId/status",
				Handler: handler.UpdateStoreStatus,
			},
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/stores/:storeId",
				Handler: handler.DeleteStore,
			},
		},
	)
}
