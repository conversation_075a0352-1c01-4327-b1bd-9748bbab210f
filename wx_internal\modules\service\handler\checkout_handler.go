package handler

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"yekaitai/internal/modules/service_setup/model"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/payment"
	wxSvc "yekaitai/wx_internal/svc"
	wxUtils "yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// ServiceCheckoutHandler 服务结算处理器
type ServiceCheckoutHandler struct {
	svcCtx *wxSvc.WxServiceContext
}

// NewServiceCheckoutHandler 创建服务结算处理器
func NewServiceCheckoutHandler(svcCtx *wxSvc.WxServiceContext) *ServiceCheckoutHandler {
	return &ServiceCheckoutHandler{
		svcCtx: svcCtx,
	}
}

// PreloadCheckout 服务结算预加载
func (h *ServiceCheckoutHandler) PreloadCheckout(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	var req struct {
		ServiceID uint `json:"service_id"` // 服务套餐ID
		Quantity  int  `json:"quantity"`   // 购买数量
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	if req.Quantity <= 0 {
		req.Quantity = 1
	}

	// 查询服务套餐
	var service model.ServicePackage
	err := mysql.Slave().Table("service_packages").
		Where("id = ? AND status = ? AND deleted_at IS NULL", req.ServiceID, "active").
		First(&service).Error

	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "服务不存在"))
		return
	}

	// 检查购买限制
	if req.Quantity > service.SingleMaxPurchases {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams,
			fmt.Sprintf("单次最多购买%d张", service.SingleMaxPurchases)))
		return
	}

	// 检查用户总购买限制
	var userPurchased int64
	mysql.Slave().Table("service_orders").
		Where("user_id = ? AND service_package_id = ? AND pay_status = 'paid' AND deleted_at IS NULL",
			userID, req.ServiceID).
		Select("COALESCE(SUM(quantity), 0)").
		Scan(&userPurchased)

	if userPurchased+int64(req.Quantity) > int64(service.MaxPurchases) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams,
			fmt.Sprintf("每人最多购买%d张", service.MaxPurchases)))
		return
	}

	// 计算金额
	originalAmount := service.Price * float64(req.Quantity)

	// 获取用户可用优惠券
	availableCoupons := h.getAvailableCoupons(userID, originalAmount)

	// 获取用户积分
	userPoints := h.getUserPoints(userID)

	// 计算最优方案（不包含会员等级折扣）
	bestCoupon := h.findBestCoupon(availableCoupons, originalAmount)

	// 计算是否可以使用积分
	remainingAmount := originalAmount
	var couponAmount float64
	if bestCoupon != nil {
		couponAmount = h.calculateCouponDiscount(bestCoupon, originalAmount)
		remainingAmount -= couponAmount
	}

	usePoints := remainingAmount > 0 && userPoints > 0 && service.PointPrice > 0

	result := map[string]interface{}{
		"service_id":        service.ID,
		"service_name":      service.Name,
		"service_images":    service.Images,
		"price":             service.Price,
		"original_price":    service.OriginalPrice,
		"quantity":          req.Quantity,
		"original_amount":   originalAmount,
		"coupon_amount":     couponAmount,
		"remaining_amount":  remainingAmount,
		"user_points":       userPoints,
		"use_points":        usePoints,
		"support_points":    service.PointPrice > 0,
		"point_rate":        1.0, // 1积分=1元
		"available_coupons": availableCoupons,
		"best_coupon":       bestCoupon,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取结算信息成功"))
}

// RecalculateCheckout 服务结算重新计算
func (h *ServiceCheckoutHandler) RecalculateCheckout(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	var req struct {
		ServiceID uint  `json:"service_id"` // 服务套餐ID
		Quantity  int   `json:"quantity"`   // 购买数量
		CouponID  *uint `json:"coupon_id"`  // 使用的优惠券ID
		UsePoints bool  `json:"use_points"` // 是否使用积分
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	if req.Quantity <= 0 {
		req.Quantity = 1
	}

	// 查询服务套餐
	var service model.ServicePackage
	err := mysql.Slave().Table("service_packages").
		Where("id = ? AND status = ? AND deleted_at IS NULL", req.ServiceID, "active").
		First(&service).Error

	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "服务不存在"))
		return
	}

	// 计算金额
	originalAmount := service.Price * float64(req.Quantity)
	remainingAmount := originalAmount
	var couponAmount float64
	var pointAmount float64
	var usedPoints int64

	// 应用优惠券
	if req.CouponID != nil {
		coupon := h.getCouponByID(*req.CouponID, userID)
		if coupon != nil && h.validateCoupon(coupon, originalAmount) {
			couponAmount = h.calculateCouponDiscount(coupon, originalAmount)
			remainingAmount -= couponAmount
		}
	}

	// 应用积分
	if req.UsePoints && remainingAmount > 0 && service.PointPrice > 0 {
		userPoints := h.getUserPoints(userID)
		if userPoints > 0 {
			maxPointsToUse := int64(remainingAmount) // 1积分=1元
			if userPoints < maxPointsToUse {
				maxPointsToUse = userPoints
			}
			usedPoints = maxPointsToUse
			pointAmount = float64(usedPoints)
			remainingAmount -= pointAmount
		}
	}

	// 最终支付金额
	finalAmount := remainingAmount
	if finalAmount < 0 {
		finalAmount = 0
	}

	result := map[string]interface{}{
		"service_id":      service.ID,
		"service_name":    service.Name,
		"quantity":        req.Quantity,
		"original_amount": originalAmount,
		"coupon_amount":   couponAmount,
		"point_amount":    pointAmount,
		"used_points":     usedPoints,
		"final_amount":    finalAmount,
		"use_points":      req.UsePoints && usedPoints > 0,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "重新计算成功"))
}

// CreateServiceOrder 创建服务订单并生成支付参数
func (h *ServiceCheckoutHandler) CreateServiceOrder(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 获取用户OpenID
	userOpenID, ok := h.getUserOpenID(r)
	if !ok {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "获取用户信息失败"))
		return
	}

	var req struct {
		ServiceID uint  `json:"service_id"` // 服务套餐ID
		Quantity  int   `json:"quantity"`   // 购买数量
		CouponID  *uint `json:"coupon_id"`  // 使用的优惠券ID
		UsePoints bool  `json:"use_points"` // 是否使用积分
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	// 开始事务
	tx := mysql.Master().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询服务套餐
	var service model.ServicePackage
	err := tx.Table("service_packages").
		Where("id = ? AND status = ? AND deleted_at IS NULL", req.ServiceID, "active").
		First(&service).Error

	if err != nil {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "服务不存在"))
		return
	}

	// 生成订单号
	orderNo := fmt.Sprintf("SRV%d%d", time.Now().Unix(), userID)

	// 重新计算金额（确保数据一致性）
	originalAmount := service.Price * float64(req.Quantity)
	remainingAmount := originalAmount
	var couponAmount float64
	var pointAmount float64
	var usedPoints int64

	// 应用优惠券
	if req.CouponID != nil {
		coupon := h.getCouponByID(*req.CouponID, userID)
		if coupon != nil && h.validateCoupon(coupon, originalAmount) {
			couponAmount = h.calculateCouponDiscount(coupon, originalAmount)
			remainingAmount -= couponAmount

			// 标记优惠券为已使用
			err = tx.Table("user_coupons").
				Where("id = ? AND user_id = ? AND status = 'unused'", *req.CouponID, userID).
				Updates(map[string]interface{}{
					"status":     "used",
					"used_time":  time.Now(),
					"order_no":   orderNo,
					"updated_at": time.Now(),
				}).Error

			if err != nil {
				tx.Rollback()
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "优惠券使用失败"))
				return
			}
		}
	}

	// 应用积分
	if req.UsePoints && remainingAmount > 0 && service.PointPrice > 0 {
		userPoints := h.getUserPoints(userID)
		if userPoints > 0 {
			maxPointsToUse := int64(remainingAmount) // 1积分=1元
			if userPoints < maxPointsToUse {
				maxPointsToUse = userPoints
			}
			usedPoints = maxPointsToUse
			pointAmount = float64(usedPoints)
			remainingAmount -= pointAmount

			// 扣除用户积分
			err = tx.Table("user_coins").
				Where("user_id = ? AND available_coins >= ?", userID, usedPoints).
				Updates(map[string]interface{}{
					"available_coins": gorm.Expr("available_coins - ?", usedPoints),
					"used_coins":      gorm.Expr("used_coins + ?", usedPoints),
				}).Error

			if err != nil {
				tx.Rollback()
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "积分扣除失败"))
				return
			}

			// 记录积分使用日志
			err = tx.Table("user_point_logs").Create(map[string]interface{}{
				"user_id":     userID,
				"type":        "consume",
				"points":      -usedPoints,
				"description": "服务订单消费积分",
				"order_no":    orderNo,
				"created_at":  time.Now(),
			}).Error

			if err != nil {
				logx.Errorf("记录积分日志失败: %v", err)
			}
		}
	}

	finalAmount := remainingAmount
	if finalAmount < 0 {
		finalAmount = 0
	}

	// 创建订单
	order := model.ServiceOrder{
		OrderNo:          orderNo,
		UserID:           userID,
		ServicePackageID: req.ServiceID,
		ServiceName:      service.Name,
		Quantity:         req.Quantity,
		OriginalAmount:   originalAmount,
		CouponAmount:     couponAmount,
		PointAmount:      pointAmount,
		PayAmount:        finalAmount,
		UsedPoints:       usedPoints,
		CouponID:         req.CouponID,
		Status:           "pending",
		PayStatus:        "unpaid",
		RemainingTimes:   int(service.Times) * req.Quantity,
		UsedTimes:        0,
		ValidityStart:    service.ValidityStart,
		ValidityEnd:      service.ValidityEnd,
		ExpireTime:       &[]time.Time{time.Now().Add(15 * time.Minute)}[0], // 15分钟后过期
	}

	if err := tx.Create(&order).Error; err != nil {
		tx.Rollback()
		logx.Errorf("创建服务订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建订单失败"))
		return
	}

	// 生成支付参数
	var payParams map[string]interface{}
	needPayment := false
	if finalAmount > 0 {
		// 使用通用支付服务生成支付参数
		paymentParams, err := h.generateServicePaymentParams(&order, userOpenID, &service)
		if err != nil {
			tx.Rollback()
			logx.Errorf("生成支付参数失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "生成支付参数失败"))
			return
		}
		payParams = paymentParams
		needPayment = true
	}

	tx.Commit()

	result := map[string]interface{}{
		"order_id":     order.ID,
		"order_no":     orderNo,
		"pay_amount":   finalAmount,
		"pay_params":   payParams,
		"need_payment": needPayment,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "创建订单成功"))
}

// getAvailableCoupons 获取用户可用优惠券
func (h *ServiceCheckoutHandler) getAvailableCoupons(userID uint, amount float64) []map[string]interface{} {
	var coupons []map[string]interface{}

	// 查询用户可用的优惠券
	rows, err := mysql.Slave().Raw(`
		SELECT uc.id, uc.coupon_id, c.name, c.type, c.discount_value, c.min_amount, c.max_discount,
			   uc.expire_time, c.description
		FROM user_coupons uc
		JOIN coupons c ON uc.coupon_id = c.id
		WHERE uc.user_id = ?
		  AND uc.status = 'unused'
		  AND uc.expire_time > NOW()
		  AND c.min_amount <= ?
		  AND c.status = 'active'
		ORDER BY c.discount_value DESC
	`, userID, amount).Rows()

	if err != nil {
		logx.Errorf("查询用户优惠券失败: %v", err)
		return coupons
	}
	defer rows.Close()

	for rows.Next() {
		var (
			id            uint
			couponID      uint
			name          string
			couponType    string
			discountValue float64
			minAmount     float64
			maxDiscount   float64
			expireTime    time.Time
			description   string
		)

		if err := rows.Scan(&id, &couponID, &name, &couponType, &discountValue,
			&minAmount, &maxDiscount, &expireTime, &description); err != nil {
			continue
		}

		// 计算折扣金额
		discountAmount := h.calculateCouponDiscountAmount(couponType, discountValue, maxDiscount, amount)

		coupon := map[string]interface{}{
			"id":              id,
			"coupon_id":       couponID,
			"name":            name,
			"type":            couponType,
			"discount_value":  discountValue,
			"min_amount":      minAmount,
			"max_discount":    maxDiscount,
			"discount_amount": discountAmount,
			"expire_time":     expireTime,
			"description":     description,
			"is_best":         false,
		}

		coupons = append(coupons, coupon)
	}

	return coupons
}

// getUserPoints 获取用户积分
func (h *ServiceCheckoutHandler) getUserPoints(userID uint) int64 {
	var points int64

	// 从用户积分表获取积分
	err := mysql.Slave().Table("user_coins").
		Where("user_id = ?", userID).
		Pluck("available_coins", &points).Error

	if err != nil {
		logx.Errorf("获取用户积分失败: %v", err)
		return 0
	}

	return points
}

// findBestCoupon 找到最优优惠券
func (h *ServiceCheckoutHandler) findBestCoupon(coupons []map[string]interface{}, amount float64) map[string]interface{} {
	if len(coupons) == 0 {
		return nil
	}

	var bestCoupon map[string]interface{}
	maxDiscount := 0.0

	for _, coupon := range coupons {
		if discountAmount, ok := coupon["discount_amount"].(float64); ok {
			if discountAmount > maxDiscount {
				maxDiscount = discountAmount
				bestCoupon = coupon
			}
		}
	}

	if bestCoupon != nil {
		bestCoupon["is_best"] = true
	}

	return bestCoupon
}

// calculateCouponDiscountAmount 计算优惠券折扣金额
func (h *ServiceCheckoutHandler) calculateCouponDiscountAmount(couponType string, discountValue, maxDiscount, amount float64) float64 {
	switch couponType {
	case "fixed":
		// 固定金额优惠券
		return discountValue
	case "percent":
		// 百分比优惠券
		discount := amount * discountValue / 100
		if maxDiscount > 0 && discount > maxDiscount {
			discount = maxDiscount
		}
		return discount
	default:
		return 0
	}
}

// calculateCouponDiscount 计算优惠券折扣金额
func (h *ServiceCheckoutHandler) calculateCouponDiscount(coupon map[string]interface{}, amount float64) float64 {
	if coupon == nil {
		return 0
	}

	couponType, _ := coupon["type"].(string)
	discountValue, _ := coupon["discount_value"].(float64)
	maxDiscount, _ := coupon["max_discount"].(float64)

	return h.calculateCouponDiscountAmount(couponType, discountValue, maxDiscount, amount)
}

// getCouponByID 根据ID获取优惠券
func (h *ServiceCheckoutHandler) getCouponByID(couponID uint, userID uint) map[string]interface{} {
	var coupon map[string]interface{}

	row := mysql.Slave().Raw(`
		SELECT uc.id, uc.coupon_id, c.name, c.type, c.discount_value, c.min_amount, c.max_discount,
			   uc.expire_time, c.description
		FROM user_coupons uc
		JOIN coupons c ON uc.coupon_id = c.id
		WHERE uc.id = ? AND uc.user_id = ? AND uc.status = 'unused' AND uc.expire_time > NOW()
	`, couponID, userID).Row()

	var (
		id            uint
		realCouponID  uint
		name          string
		couponType    string
		discountValue float64
		minAmount     float64
		maxDiscount   float64
		expireTime    time.Time
		description   string
	)

	if err := row.Scan(&id, &realCouponID, &name, &couponType, &discountValue,
		&minAmount, &maxDiscount, &expireTime, &description); err != nil {
		return nil
	}

	coupon = map[string]interface{}{
		"id":             id,
		"coupon_id":      realCouponID,
		"name":           name,
		"type":           couponType,
		"discount_value": discountValue,
		"min_amount":     minAmount,
		"max_discount":   maxDiscount,
		"expire_time":    expireTime,
		"description":    description,
	}

	return coupon
}

// validateCoupon 验证优惠券是否可用
func (h *ServiceCheckoutHandler) validateCoupon(coupon map[string]interface{}, amount float64) bool {
	if coupon == nil {
		return false
	}

	// 检查最低消费金额
	if minAmount, ok := coupon["min_amount"].(float64); ok {
		if amount < minAmount {
			return false
		}
	}

	// 检查过期时间
	if expireTime, ok := coupon["expire_time"].(time.Time); ok {
		if time.Now().After(expireTime) {
			return false
		}
	}

	return true
}

// getUserOpenID 从请求中获取用户OpenID
func (h *ServiceCheckoutHandler) getUserOpenID(r *http.Request) (string, bool) {
	openIDValue := r.Context().Value("openid")
	logx.Infof("获取OpenID: openIDValue=%v", openIDValue)

	if openIDValue == nil {
		logx.Errorf("OpenID为空: context中没有openid")
		return "", false
	}

	openID, ok := openIDValue.(string)
	if !ok {
		logx.Errorf("OpenID类型转换失败: openIDValue=%v, type=%T", openIDValue, openIDValue)
		return "", false
	}

	if openID == "" {
		logx.Errorf("OpenID为空字符串")
		return "", false
	}

	logx.Infof("获取到OpenID: %s", openID)
	return openID, true
}

// generateServicePaymentParams 生成服务支付参数
func (h *ServiceCheckoutHandler) generateServicePaymentParams(order *model.ServiceOrder, userOpenID string, service *model.ServicePackage) (map[string]interface{}, error) {
	// 使用通用支付服务
	paymentService := payment.GetGlobalPaymentService()
	if paymentService == nil {
		logx.Errorf("支付服务未初始化")
		return nil, fmt.Errorf("支付服务未初始化")
	}

	req := &payment.CreatePaymentRequest{
		OrderID:      order.ID,
		OrderNo:      order.OrderNo,
		Amount:       int(order.PayAmount * 100), // 转换为分
		Description:  fmt.Sprintf("服务套餐-%s", service.Name),
		OpenID:       userOpenID,
		Attach:       fmt.Sprintf("service_order_id:%d", order.ID),
		BusinessType: "service",
	}

	logx.Infof("创建服务支付参数: orderNo=%s, amount=%d, openID=%s", order.OrderNo, req.Amount, userOpenID)

	paymentParams, err := paymentService.CreatePayment(context.Background(), req)
	if err != nil {
		logx.Errorf("创建服务支付参数失败: orderNo=%s, error=%v", order.OrderNo, err)
		return nil, err
	}

	// 转换map[string]string为map[string]interface{}
	result := make(map[string]interface{})
	for k, v := range paymentParams.PaymentParams {
		result[k] = v
	}
	return result, nil
}
