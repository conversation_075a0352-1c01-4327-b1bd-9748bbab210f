package service

import (
	"context"
	"fmt"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type GoodsService struct {
	db *gorm.DB
}

func NewGoodsService() *GoodsService {
	return &GoodsService{
		db: mysql.GetDB(),
	}
}

// GetHotGoods 获取热卖商品（推荐商品）
func (s *GoodsService) GetHotGoods(ctx context.Context, limit int) ([]*model.Goods, error) {
	var goods []*model.Goods
	query := s.db.WithContext(ctx).Where("is_recommended = ?", 1).
		Where("status = ?", 1).
		Where("is_on_sale = ?", 1).
		Order("recommend_order ASC, sales_count DESC, created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Preload("LocalCategory").Preload("Specs").Find(&goods).Error; err != nil {
		return nil, fmt.Errorf("查询热卖商品失败: %w", err)
	}

	return goods, nil
}

// GetGoodsByCategory 根据分类获取商品
func (s *GoodsService) GetGoodsByCategory(ctx context.Context, categoryID uint, page, pageSize int) ([]*model.Goods, int64, error) {
	var goods []*model.Goods
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Goods{}).
		Where("local_category_id = ?", categoryID).
		Where("status = ?", 1).
		Where("is_on_sale = ?", 1)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计商品数量失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Preload("LocalCategory").Preload("Specs").
		Order("sort_order ASC, recommend_order ASC, created_at DESC").
		Offset(offset).Limit(pageSize).Find(&goods).Error; err != nil {
		return nil, 0, fmt.Errorf("查询商品列表失败: %w", err)
	}

	return goods, total, nil
}

// GetGoodsDetail 获取商品详情（增加浏览次数）
func (s *GoodsService) GetGoodsDetail(ctx context.Context, id uint) (*model.Goods, error) {
	goods := &model.Goods{}
	if err := s.db.WithContext(ctx).Preload("LocalCategory").Preload("Specs").First(goods, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("商品不存在")
		}
		return nil, fmt.Errorf("查询商品详情失败: %w", err)
	}

	// 增加浏览次数
	go func() {
		s.db.Model(&model.Goods{}).Where("id = ?", id).
			UpdateColumn("view_count", gorm.Expr("view_count + ?", 1))
	}()

	return goods, nil
}

// SearchGoods 搜索商品
func (s *GoodsService) SearchGoods(ctx context.Context, keyword string, page, pageSize int) ([]*model.Goods, int64, error) {
	var goods []*model.Goods
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Goods{}).
		Where("goods_name LIKE ?", "%"+keyword+"%").
		Where("status = ?", 1).
		Where("is_on_sale = ?", 1)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计商品数量失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Preload("LocalCategory").Preload("Specs").
		Order("sales_count DESC, created_at DESC").
		Offset(offset).Limit(pageSize).Find(&goods).Error; err != nil {
		return nil, 0, fmt.Errorf("搜索商品失败: %w", err)
	}

	return goods, total, nil
}

// GetRecommendGoods 获取推荐商品
func (s *GoodsService) GetRecommendGoods(ctx context.Context, limit int) ([]*model.Goods, error) {
	var goods []*model.Goods
	query := s.db.WithContext(ctx).Where("is_recommended = ?", 1).
		Where("status = ?", 1).
		Where("is_on_sale = ?", 1).
		Order("recommend_order ASC, created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Preload("LocalCategory").Preload("Specs").Find(&goods).Error; err != nil {
		return nil, fmt.Errorf("查询推荐商品失败: %w", err)
	}

	return goods, nil
}

// GetHealthProducts 获取保健品
func (s *GoodsService) GetHealthProducts(ctx context.Context, page, pageSize int) ([]*model.Goods, int64, error) {
	// 假设保健品是特定分类下的商品，需要根据实际分类设置
	return s.getGoodsByTypeKeyword(ctx, "保健", page, pageSize)
}

// GetMedicines 获取药品
func (s *GoodsService) GetMedicines(ctx context.Context, page, pageSize int) ([]*model.Goods, int64, error) {
	// 假设药品是特定分类下的商品，需要根据实际分类设置
	return s.getGoodsByTypeKeyword(ctx, "药品", page, pageSize)
}

// getGoodsByTypeKeyword 根据类型关键词获取商品
func (s *GoodsService) getGoodsByTypeKeyword(ctx context.Context, keyword string, page, pageSize int) ([]*model.Goods, int64, error) {
	var goods []*model.Goods
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Goods{}).
		Joins("LEFT JOIN categories ON goods.local_category_id = categories.id").
		Where("(goods.goods_name LIKE ? OR categories.name LIKE ?)", "%"+keyword+"%", "%"+keyword+"%").
		Where("goods.status = ?", 1).
		Where("goods.is_on_sale = ?", 1)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计商品数量失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Preload("LocalCategory").Preload("Specs").
		Order("goods.sort_order ASC, goods.recommend_order ASC, goods.created_at DESC").
		Offset(offset).Limit(pageSize).Find(&goods).Error; err != nil {
		return nil, 0, fmt.Errorf("查询商品失败: %w", err)
	}

	return goods, total, nil
}

// GetGoodsSpecs 获取商品规格
func (s *GoodsService) GetGoodsSpecs(ctx context.Context, goodsID uint) ([]*model.GoodsSpec, error) {
	var specs []*model.GoodsSpec
	if err := s.db.WithContext(ctx).Where("goods_id = ?", goodsID).
		Where("status = ?", 1).Find(&specs).Error; err != nil {
		return nil, fmt.Errorf("查询商品规格失败: %w", err)
	}
	return specs, nil
}
