#!/bin/bash

# 检查配置文件是否存在
CONFIG_FILE="etc/config.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
  echo "错误：配置文件 $CONFIG_FILE 不存在"
  exit 1
fi

# 检查JWT密钥配置
check_secret() {
  local key=$1
  local value=$(grep "$key:" $CONFIG_FILE | awk '{print $2}')
  
  if [ -z "$value" ]; then
    echo "错误：$key 未配置"
    exit 1
  fi
  
  if [ "$value" == "default_secret" ]; then
    echo "警告：$key 使用默认值，生产环境不安全！"
  fi
}

# 检查管理员JWT密钥
check_secret "AdminAccessSecret"
check_secret "AdminRefreshSecret"

echo "JWT配置检查通过"
echo "提示：请确保管理员JWT密钥的安全" 