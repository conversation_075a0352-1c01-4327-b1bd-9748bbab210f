package service

import (
	"context"
	"fmt"

	"yekaitai/pkg/adapters/jushuitan"
)

// LogisticService 物流服务
type LogisticService struct {
	client *jushuitan.Client
}

// NewLogisticService 创建物流服务
func NewLogisticService() *LogisticService {
	return &LogisticService{
		client: jushuitan.DefaultClient,
	}
}

// QueryLogistic 查询物流信息
func (s *LogisticService) QueryLogistic(ctx context.Context, req jushuitan.LogisticQueryRequest) (*jushuitan.LogisticQueryResponse, error) {
	if s.client == nil {
		return nil, fmt.Errorf("聚水潭客户端未初始化")
	}

	return s.client.QueryLogistic(ctx, req)
}
