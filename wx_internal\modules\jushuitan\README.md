# 聚水潭模块

本模块实现了与聚水潭ERP系统的接口对接，提供了基础数据和商品数据的查询和操作功能。

## 目录结构

```
wx_internal/modules/jushuitan/
  ├── handler/             # HTTP处理器
  │   ├── handler.go       # 基础处理器
  │   ├── item_handler.go  # 商品相关处理器-1
  │   ├── item_handler2.go # 商品相关处理器-2
  │   └── item_handler3.go # 商品相关处理器-3
  ├── logic/               # 业务逻辑层
  │   ├── distributor_logic.go # 分销商逻辑
  │   ├── item_logic.go        # 商品逻辑
  │   ├── logistics_logic.go   # 物流逻辑
  │   ├── shop_logic.go        # 店铺逻辑
  │   ├── supplier_logic.go    # 供应商逻辑
  │   ├── user_logic.go        # 用户逻辑
  │   └── warehouse_logic.go   # 仓库逻辑
  └── model/               # 数据模型
      ├── model.go         # 基础模型
      └── result.go        # 结果模型
```

## API接口

### 基础数据接口

- `/api/v1/jst/shops` - 查询店铺列表
- `/api/v1/jst/logistics/companies` - 查询物流公司列表
- `/api/v1/jst/warehouses` - 查询仓库列表
- `/api/v1/jst/users` - 查询用户列表
- `/api/v1/jst/suppliers` - 查询供应商列表
- `/api/v1/jst/distributors` - 查询分销商列表

### 商品接口

- `/api/v1/jst/item/upload` - 普通商品资料上传
- `/api/v1/jst/item/query` - 普通商品资料查询（按sku）
- `/api/v1/jst/mall/item/query` - 普通商品资料查询（按款）
- `/api/v1/jst/item/batch/upload` - 批量上传/更新普通商品资料
- `/api/v1/jst/item/upload/style` - 商品款式新增/更新

- `/api/v1/jst/item/shop/upload` - 店铺商品资料上传
- `/api/v1/jst/item/shop/query` - 店铺商品资料查询
- `/api/v1/jst/item/link/bind` - 绑定/解绑商品对应关系

- `/api/v1/jst/item/combine/upload` - 组合装商品资料上传
- `/api/v1/jst/item/combine/query` - 组合装商品资料查询

- `/api/v1/jst/item/category/upload` - 商品类目上传/更新
- `/api/v1/jst/item/category/query` - 商品类目查询

- `/api/v1/jst/item/bom/upload` - BOM信息上传
- `/api/v1/jst/item/bom/query` - BOM信息查询

- `/api/v1/jst/item/costprice/upload` - 商品历史成本价上传
- `/api/v1/jst/item/costprice/query` - 商品历史成本价查询

- `/api/v1/jst/item/supplier/upload` - 商品多供应商上传/更新
- `/api/v1/jst/item/supplier/query` - 商品多供应商查询

- `/api/v1/jst/item/bins/update` - 更新商品库容信息

## 使用示例

### 普通商品资料上传

```json
POST /api/v1/jst/item/upload
{
  "items": [
    {
      "sku_id": "001C0812",
      "i_id": "001C0812",
      "brand": "NIKE12",
      "name": "短袖112",
      "properties_value": "蓝色;XXL"
    }
  ]
}
```

### 普通商品资料查询

```
GET /api/v1/jst/item/query?sku_ids=001C0812,001C0813&page_no=1&page_size=10
```

### 店铺商品资料上传

```json
POST /api/v1/jst/item/shop/upload
{
  "shop_id": 123,
  "maps": [
    {
      "sku_id": "001C0812",
      "shop_sku_id": "TB001C0812",
      "shop_name": "淘宝商品名称"
    }
  ]
}
```

## 依赖

本模块依赖于 `pkg/adapters/jushuitan` 包，该包封装了与聚水潭API交互的底层实现。 