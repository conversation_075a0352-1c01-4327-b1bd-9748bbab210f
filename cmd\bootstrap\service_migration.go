package bootstrap

import (
	"log"

	"yekaitai/internal/modules/service_setup/model"
	"yekaitai/pkg/infra/mysql"
)

// MigrateServiceTables 迁移服务模块相关表
func MigrateServiceTables() {
	db := mysql.Master()

	// 自动迁移服务套餐表
	if err := db.AutoMigrate(&model.ServicePackage{}); err != nil {
		log.Fatalf("迁移服务套餐表失败: %v", err)
	}
	log.Println("服务套餐表迁移成功")

	// 自动迁移服务订单表
	if err := db.AutoMigrate(&model.ServiceOrder{}); err != nil {
		log.Fatalf("迁移服务订单表失败: %v", err)
	}
	log.Println("服务订单表迁移成功")

	// 自动迁移服务预约表
	if err := db.AutoMigrate(&model.ServiceAppointment{}); err != nil {
		log.Fatalf("迁移服务预约表失败: %v", err)
	}
	log.Println("服务预约表迁移成功")
}

// 在 main.go 中调用此函数进行迁移
// func main() {
//     // 初始化数据库连接
//     mysql.InitDB()
//
//     // 迁移服务模块表
//     MigrateServiceTables()
// }
