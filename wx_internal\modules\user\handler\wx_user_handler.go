package handler

import (
	"net/http"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/user/model"
	"yekaitai/wx_internal/svc"
	"yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/core/logx"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// GetUserInfoHandler 获取用户信息
func GetUserInfoHandler(svcCtx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 从上下文中获取OpenID
		openIDVal := r.Context().Value(middleware.OpenIDKey)
		if openIDVal == nil {
			logx.Error("获取用户信息失败: openid不存在")
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeUnauthorized,
				Message: "未授权的访问",
				Data:    nil,
			})
			return
		}

		openID, ok := openIDVal.(string)
		if !ok || openID == "" {
			logx.Error("获取用户信息失败: 无效的openid")
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeUnauthorized,
				Message: "未授权的访问",
				Data:    nil,
			})
			return
		}

		// 查询用户基础信息
		db := mysql.Slave()
		var wxUser user.WxUser
		if err := db.Where("open_id = ?", openID).First(&wxUser).Error; err != nil {
			logx.Errorf("查询用户基础信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeInternalError,
				Message: "获取用户信息失败",
				Data:    nil,
			})
			return
		}

		// 查询用户的所有身份
		var isPatient, isDoctor, isRedeemer bool
		var patientInfo *model.WxPatient
		var doctorInfo *model.WxDoctor
		var redeemerInfo *model.WxRedeemer
		var roles []string

		// 检查患者身份
		patientInfo = &model.WxPatient{}
		if err := db.Where("mobile = ?", wxUser.Mobile).First(patientInfo).Error; err == nil {
			isPatient = true
			roles = append(roles, "patient")
		} else {
			patientInfo = nil
		}

		// 检查医生身份
		doctorInfo = &model.WxDoctor{}
		if err := db.Where("mobile = ?", wxUser.Mobile).First(doctorInfo).Error; err == nil {
			isDoctor = true
			roles = append(roles, "doctor")
		} else {
			doctorInfo = nil
		}

		// 检查核销用户身份
		redeemerInfo = &model.WxRedeemer{}
		if err := db.Where("mobile = ?", wxUser.Mobile).First(redeemerInfo).Error; err == nil {
			isRedeemer = true
			roles = append(roles, "redeemer")
		} else {
			redeemerInfo = nil
		}

		// 组装用户身份信息
		userRoles := map[string]interface{}{}
		if isPatient {
			userRoles["patient"] = map[string]interface{}{
				"patientId":      patientInfo.PatientID,
				"medicalHistory": patientInfo.MedicalHistory,
				"allergies":      patientInfo.Allergies,
				"status":         patientInfo.Status,
				"createdAt":      patientInfo.CreatedAt,
				"updatedAt":      patientInfo.UpdatedAt,
			}
		}

		if isDoctor {
			userRoles["doctor"] = map[string]interface{}{
				"doctorId":       doctorInfo.DoctorID,
				"medicalLicense": doctorInfo.MedicalLicense,
				"specialty":      doctorInfo.Specialty,
				"hospital":       doctorInfo.Hospital,
				"department":     doctorInfo.Department,
				"title":          doctorInfo.Title,
				"introduction":   doctorInfo.Introduction,
				"status":         doctorInfo.Status,
				"createdAt":      doctorInfo.CreatedAt,
				"updatedAt":      doctorInfo.UpdatedAt,
			}
		}

		if isRedeemer {
			userRoles["redeemer"] = map[string]interface{}{
				"redeemerId": redeemerInfo.RedeemerID,
				"storeId":    redeemerInfo.StoreID,
				"storeName":  redeemerInfo.StoreName,
				// "position":   redeemerInfo.Position,
				"status":    redeemerInfo.Status,
				"createdAt": redeemerInfo.CreatedAt,
				"updatedAt": redeemerInfo.UpdatedAt,
			}
		}

		// 返回用户信息
		httpx.WriteJson(w, http.StatusOK, types.Response{
			Code:    types.CodeSuccess,
			Message: "success",
			Data: map[string]interface{}{
				"userInfo": map[string]interface{}{
					"userId":    wxUser.UserID,
					"openId":    wxUser.OpenID,
					"nickName":  wxUser.Nickname,
					"avatar":    wxUser.Avatar,
					"mobile":    wxUser.Mobile,
					"gender":    wxUser.Gender,
					"status":    wxUser.Status,
					"createdAt": wxUser.CreatedAt,
					"updatedAt": wxUser.UpdatedAt,
					"roles":     roles,
					"identity":  userRoles,
				},
			},
		})
	}
}
