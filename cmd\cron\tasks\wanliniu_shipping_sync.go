package tasks

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/pkg/adapters/wanliniu"
	orderModel "yekaitai/pkg/common/model/order"
	"yekaitai/pkg/infra/mysql"
)

// WanLiNiuShippingSyncService 万里牛发货状态同步服务
type WanLiNiuShippingSyncService struct {
	db *gorm.DB
}

// NewWanLiNiuShippingSyncService 创建万里牛发货状态同步服务
func NewWanLiNiuShippingSyncService() *WanLiNiuShippingSyncService {
	return &WanLiNiuShippingSyncService{
		db: mysql.Master(),
	}
}

// SyncShippingStatus 定时同步发货状态
func (s *WanLiNiuShippingSyncService) SyncShippingStatus() error {
	logx.Info("[WanLiNiu] 开始定时同步发货状态...")

	// 检查并初始化万里牛客户端
	if err := s.ensureWanLiNiuClient(); err != nil {
		logx.Errorf("[WanLiNiu] 万里牛客户端初始化失败: %v", err)
		return fmt.Errorf("万里牛客户端初始化失败: %w", err)
	}

	// 一次性查询最多500个待处理订单
	orders, err := s.getPaidUnshippedOrdersLimited(500)
	if err != nil {
		logx.Errorf("[WanLiNiu] 获取待查询订单失败: %v", err)
		return fmt.Errorf("获取待查询订单失败: %w", err)
	}

	if len(orders) == 0 {
		logx.Info("[WanLiNiu] 没有需要查询发货状态的订单")
		return nil
	}

	logx.Infof("[WanLiNiu] 找到 %d 个待查询发货状态的订单，开始分批处理（每批50个）", len(orders))

	// 分批处理：每批最多50个订单调用API
	totalProcessed := 0
	batchSize := 50
	batchCount := (len(orders) + batchSize - 1) / batchSize // 向上取整

	for i := 0; i < len(orders); i += batchSize {
		end := i + batchSize
		if end > len(orders) {
			end = len(orders)
		}

		batch := orders[i:end]
		batchIndex := i/batchSize + 1

		logx.Infof("[WanLiNiu] 处理第 %d/%d 批，包含 %d 个订单", batchIndex, batchCount, len(batch))

		// 批量查询发货状态
		err = s.batchQueryShippingStatus(batch)
		if err != nil {
			logx.Errorf("[WanLiNiu] 第 %d 批查询发货状态失败: %v", batchIndex, err)
			// 记录错误但继续处理下一批
		}

		totalProcessed += len(batch)

		// 避免请求过于频繁，批次间稍作延迟
		if end < len(orders) {
			time.Sleep(1 * time.Second)
		}
	}

	logx.Infof("[WanLiNiu] 发货状态同步完成，共处理 %d 个订单", totalProcessed)
	return nil
}

// SyncShippingStatusManual 手动同步发货状态
func (s *WanLiNiuShippingSyncService) SyncShippingStatusManual() error {
	logx.Info("[WanLiNiu] 开始手动同步发货状态...")

	// 检查并初始化万里牛客户端
	if err := s.ensureWanLiNiuClient(); err != nil {
		logx.Errorf("[WanLiNiu] 万里牛客户端初始化失败: %v", err)
		return fmt.Errorf("万里牛客户端初始化失败: %w", err)
	}

	// 分批次查询所有符合条件的订单
	totalProcessed := 0
	batchSize := 50 // 每批最多50个订单
	offset := 0

	for {
		// 获取一批需要查询的订单
		orders, err := s.getPaidUnshippedOrdersPaged(batchSize, offset)
		if err != nil {
			logx.Errorf("[WanLiNiu] 获取待查询订单失败: %v", err)
			return fmt.Errorf("获取待查询订单失败: %w", err)
		}

		if len(orders) == 0 {
			// 没有更多订单了
			break
		}

		logx.Infof("[WanLiNiu] 手动同步第 %d 批: 查询 %d 个订单的发货状态", (offset/batchSize)+1, len(orders))

		// 批量查询发货状态
		err = s.batchQueryShippingStatus(orders)
		if err != nil {
			logx.Errorf("[WanLiNiu] 手动同步第 %d 批查询发货状态失败: %v", (offset/batchSize)+1, err)
			// 记录错误但继续处理下一批
		}

		totalProcessed += len(orders)
		offset += batchSize

		// 如果返回的订单数少于批次大小，说明已经是最后一批
		if len(orders) < batchSize {
			break
		}

		// 避免请求过于频繁，稍作延迟
		time.Sleep(1 * time.Second)
	}

	if totalProcessed == 0 {
		logx.Info("[WanLiNiu] 没有需要查询发货状态的订单")
	} else {
		logx.Infof("[WanLiNiu] 发货状态手动同步完成，共处理 %d 个订单", totalProcessed)
	}

	return nil
}

// ensureWanLiNiuClient 确保万里牛客户端已初始化
func (s *WanLiNiuShippingSyncService) ensureWanLiNiuClient() error {
	client := wanliniu.GetClient()
	if client == nil {
		logx.Error("[WanLiNiu] 万里牛客户端未初始化，尝试重新初始化...")

		// 尝试重新初始化
		config := wanliniu.Config{
			AppKey:    "36771105875",
			AppSecret: "dc80a8e1dc9e4fecb7c1c82c9b8063aa",
			BaseURL:   "https://open-api.hupun.com/api",
			ShopNick:  "叶开泰商城",
			ShopType:  100,
		}

		wanliniu.Init(config)

		// 检查是否初始化成功
		if wanliniu.GetClient() == nil {
			return fmt.Errorf("重新初始化万里牛客户端失败")
		}

		logx.Info("[WanLiNiu] 万里牛客户端重新初始化成功")
	}

	return nil
}

// getPaidUnshippedOrders 获取已支付但未发货的订单（兼容性方法）
func (s *WanLiNiuShippingSyncService) getPaidUnshippedOrders() ([]orderModel.Order, error) {
	return s.getPaidUnshippedOrdersPaged(200, 0)
}

// getPaidUnshippedOrdersLimited 一次性获取指定数量的已支付但未发货的订单
func (s *WanLiNiuShippingSyncService) getPaidUnshippedOrdersLimited(limit int) ([]orderModel.Order, error) {
	var orders []orderModel.Order

	// 查询已支付但未发货的订单（状态为已支付，且ship_time为空）
	err := s.db.Where("status = ? AND ship_time IS NULL", orderModel.OrderStatusPaid).
		Order("created_at ASC").
		Limit(limit).
		Find(&orders).Error

	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	return orders, nil
}

// getPaidUnshippedOrdersPaged 分页获取已支付但未发货的订单
func (s *WanLiNiuShippingSyncService) getPaidUnshippedOrdersPaged(limit, offset int) ([]orderModel.Order, error) {
	var orders []orderModel.Order

	// 查询已支付但未发货的订单（状态为已支付，且ship_time为空）
	err := s.db.Where("status = ? AND ship_time IS NULL", orderModel.OrderStatusPaid).
		Order("created_at ASC").
		Limit(limit).
		Offset(offset).
		Find(&orders).Error

	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	return orders, nil
}

// batchQueryShippingStatus 批量查询发货状态
func (s *WanLiNiuShippingSyncService) batchQueryShippingStatus(orders []orderModel.Order) error {
	if len(orders) == 0 {
		return nil
	}

	// 万里牛API最多支持200个订单号，我们设定为50个以提高稳定性和响应速度
	maxBatchSize := 50

	// 如果订单数量超过最大批次大小，需要分批处理
	if len(orders) > maxBatchSize {
		logx.Infof("[WanLiNiu] 订单数量 %d 超过单批最大限制 %d，需要分批查询", len(orders), maxBatchSize)

		for i := 0; i < len(orders); i += maxBatchSize {
			end := i + maxBatchSize
			if end > len(orders) {
				end = len(orders)
			}

			batch := orders[i:end]
			logx.Infof("[WanLiNiu] 处理第 %d 个子批次，包含 %d 个订单", (i/maxBatchSize)+1, len(batch))

			err := s.querySingleBatch(batch)
			if err != nil {
				logx.Errorf("[WanLiNiu] 第 %d 个子批次查询失败: %v", (i/maxBatchSize)+1, err)
				// 记录错误但继续处理下一个子批次
			}

			// 避免请求过于频繁
			if end < len(orders) {
				time.Sleep(500 * time.Millisecond)
			}
		}

		return nil
	}

	// 单批次处理
	return s.querySingleBatch(orders)
}

// querySingleBatch 查询单个批次的发货状态
func (s *WanLiNiuShippingSyncService) querySingleBatch(orders []orderModel.Order) error {
	// 构建订单号列表
	var tradeIDs []string
	orderMap := make(map[string]*orderModel.Order)

	for i := range orders {
		tradeIDs = append(tradeIDs, orders[i].OrderNo)
		orderMap[orders[i].OrderNo] = &orders[i]
	}

	// 获取万里牛客户端配置
	client := wanliniu.GetClient()
	if client == nil {
		return fmt.Errorf("万里牛客户端未初始化")
	}

	config := client.GetConfig()

	// 构建查询请求
	request := wanliniu.TradeStatusRequest{
		ShopType: config.ShopType,
		ShopNick: config.ShopNick,
		TradeIDs: strings.Join(tradeIDs, ","),
	}

	logx.Infof("[WanLiNiu] 开始查询发货状态: 店铺=%s, 订单数量=%d", request.ShopNick, len(tradeIDs))
	logx.Infof("[WanLiNiu] 查询的订单号: %s", request.TradeIDs)

	// 调用万里牛API查询发货状态
	ctx := context.Background()
	response, err := client.QueryTradeStatus(ctx, request)
	if err != nil {
		logx.Errorf("[WanLiNiu] 查询发货状态API调用失败: %v", err)
		return fmt.Errorf("查询发货状态API调用失败: %w", err)
	}

	// 解析响应
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 发货状态查询失败: code=%d, data=%+v", response.Code, response.Data)
		return fmt.Errorf("发货状态查询失败: code=%d", response.Code)
	}

	// 检查业务层响应
	if data, ok := response.Data.(map[string]interface{}); ok {
		success, _ := data["success"].(bool)
		if !success {
			errorCode, _ := data["error_code"].(string)
			errorMsg, _ := data["error_msg"].(string)
			logx.Errorf("[WanLiNiu] 发货状态查询业务失败: error_code=%s, error_msg=%s", errorCode, errorMsg)
			return fmt.Errorf("发货状态查询业务失败: %s", errorMsg)
		}

		// 解析发货状态信息
		responseStr, _ := data["response"].(string)
		logx.Infof("[WanLiNiu] 发货状态查询成功，响应内容: %s", responseStr)

		// 解析和更新发货状态
		err = s.parseAndUpdateShippingStatus(responseStr, orderMap)
		if err != nil {
			logx.Errorf("[WanLiNiu] 解析和更新发货状态失败: %v", err)
			return fmt.Errorf("解析和更新发货状态失败: %w", err)
		}
	} else {
		logx.Errorf("[WanLiNiu] 发货状态查询响应格式异常: %+v", response.Data)
		return fmt.Errorf("发货状态查询响应格式异常")
	}

	return nil
}

// parseAndUpdateShippingStatus 解析并更新发货状态
func (s *WanLiNiuShippingSyncService) parseAndUpdateShippingStatus(responseStr string, orderMap map[string]*orderModel.Order) error {
	logx.Infof("[WanLiNiu] 开始解析发货状态响应: %s", responseStr)

	// 解析万里牛发货状态响应
	var statusResponse wanliniu.TradeStatusResponse
	if err := json.Unmarshal([]byte(responseStr), &statusResponse); err != nil {
		logx.Errorf("[WanLiNiu] 解析发货状态响应失败: %v", err)
		return fmt.Errorf("解析发货状态响应失败: %w", err)
	}

	logx.Infof("[WanLiNiu] 解析到 %d 个订单的发货状态", len(statusResponse.Statuses))

	updatedCount := 0
	for _, status := range statusResponse.Statuses {
		// 通过TID（第三方交易号）找到对应的本地订单
		order, exists := orderMap[status.TID]
		if !exists {
			logx.Errorf("[WanLiNiu] 本地找不到对应订单: %s", status.TID)
			continue
		}

		logx.Infof("[WanLiNiu] 处理订单 %s: 状态=%s, 快递公司=%s, 快递单号=%s",
			status.TID, status.Status, status.Express, status.Waybill)

		// 判断订单是否已发货
		// 根据万里牛文档和实际返回数据：
		// - status: 订单状态，-1表示未发货，其他值可能表示不同的发货状态
		// - waybill: 快递单号，有值表示已生成快递单
		isShipped := s.isOrderShipped(status)

		if isShipped {
			// 订单已发货，更新本地订单状态
			var shipTime *time.Time
			now := time.Now()
			shipTime = &now // 使用当前时间作为发货时间，实际可能需要从万里牛获取准确时间

			err := s.updateOrderShippingStatus(order, true, status.Express, status.Waybill, shipTime)
			if err != nil {
				logx.Errorf("[WanLiNiu] 更新订单发货状态失败: 订单号=%s, 错误=%v", order.OrderNo, err)
				continue
			}
			updatedCount++
		} else {
			logx.Infof("[WanLiNiu] 订单 %s 尚未发货，保持当前状态", status.TID)
		}
	}

	logx.Infof("[WanLiNiu] 发货状态解析完成，共更新 %d 个订单", updatedCount)
	return nil
}

// isOrderShipped 判断订单是否已发货
func (s *WanLiNiuShippingSyncService) isOrderShipped(status wanliniu.TradeStatus) bool {
	// 根据万里牛API返回的数据判断订单是否已发货
	// 1. 快递单号不为空表示已生成快递单
	// 2. 订单状态不为-1（-1可能表示未发货）

	hasWaybill := status.Waybill != ""
	statusNotUnshipped := status.Status != "-1"

	// 如果有快递单号，认为已发货
	if hasWaybill {
		logx.Infof("[WanLiNiu] 订单 %s 已发货：有快递单号 %s", status.TID, status.Waybill)
		return true
	}

	// 如果状态不是-1且有快递公司信息，可能也表示已发货（但没有单号）
	if statusNotUnshipped && status.Express != "" {
		logx.Infof("[WanLiNiu] 订单 %s 可能已发货：状态=%s, 快递公司=%s",
			status.TID, status.Status, status.Express)
		// 暂时保守处理，没有快递单号不认为已发货
		return false
	}

	logx.Infof("[WanLiNiu] 订单 %s 未发货：状态=%s, 快递单号为空", status.TID, status.Status)
	return false
}

// updateOrderShippingStatus 更新订单发货状态
func (s *WanLiNiuShippingSyncService) updateOrderShippingStatus(order *orderModel.Order, isShipped bool, expressCompany, expressNo string, shipTime *time.Time) error {
	updates := make(map[string]interface{})

	if isShipped {
		// 订单已发货
		updates["status"] = orderModel.OrderStatusShipped
		if expressCompany != "" {
			updates["express_company"] = expressCompany
		}
		if expressNo != "" {
			updates["express_no"] = expressNo
		}
		if shipTime != nil {
			updates["ship_time"] = shipTime
		}

		logx.Infof("[WanLiNiu] 订单 %s 已发货: 快递公司=%s, 快递单号=%s",
			order.OrderNo, expressCompany, expressNo)
	} else {
		logx.Infof("[WanLiNiu] 订单 %s 仍未发货", order.OrderNo)
		return nil // 未发货则不更新
	}

	// 更新数据库
	err := s.db.Model(order).Updates(updates).Error
	if err != nil {
		logx.Errorf("[WanLiNiu] 更新订单发货状态失败: 订单号=%s, 错误=%v", order.OrderNo, err)
		return fmt.Errorf("更新订单发货状态失败: %w", err)
	}

	logx.Infof("[WanLiNiu] 订单发货状态更新成功: 订单号=%s", order.OrderNo)
	return nil
}
