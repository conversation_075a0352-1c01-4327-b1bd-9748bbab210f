package wechatpay

import (
	"context"
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"github.com/zeromicro/go-zero/core/logx"
)

// PaymentCallbackHandler 支付回调处理函数类型
type PaymentCallbackHandler func(ctx context.Context, transaction *payments.Transaction) error

// RefundCallbackHandler 退款回调处理函数类型
type RefundCallbackHandler func(ctx context.Context, refund *refunddomestic.Refund) error

// NotifyService 回调通知服务
type NotifyService struct {
	handler        *notify.Handler
	paymentHandler PaymentCallbackHandler
	refundHandler  RefundCallbackHandler
	config         *WechatPayConfig
	privateKey     *rsa.PrivateKey
}

// NewNotifyService 创建新的回调通知服务
func NewNotifyService(config *WechatPayConfig) (*NotifyService, error) {
	// 从配置字符串解析私钥（而不是从文件读取）
	privateKey, err := utils.LoadPrivateKey(config.PrivateKeyContent)
	if err != nil {
		return nil, fmt.Errorf("解析商户私钥失败: %w", err)
	}

	// 注册证书下载器
	ctx := context.Background()
	err = downloader.MgrInstance().RegisterDownloaderWithPrivateKey(
		ctx,
		privateKey,
		config.CertSerialNo,
		config.MchID,
		config.APIv3Key,
	)
	if err != nil {
		return nil, fmt.Errorf("注册证书下载器失败: %w", err)
	}

	// 获取商户号对应的微信支付平台证书访问器
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(config.MchID)

	// 使用证书访问器初始化 notify.Handler
	handler := notify.NewNotifyHandler(
		config.APIv3Key,
		verifiers.NewSHA256WithRSAVerifier(certificateVisitor),
	)

	return &NotifyService{
		handler:    handler,
		config:     config,
		privateKey: privateKey,
	}, nil
}

// SetPaymentHandler 设置支付回调处理器
func (s *NotifyService) SetPaymentHandler(handler PaymentCallbackHandler) {
	s.paymentHandler = handler
}

// SetRefundHandler 设置退款回调处理器
func (s *NotifyService) SetRefundHandler(handler RefundCallbackHandler) {
	s.refundHandler = handler
}

// HandlePaymentNotify 处理支付成功回调通知
func (s *NotifyService) HandlePaymentNotify(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// 解析并验证支付回调通知
	transaction := new(payments.Transaction)
	notifyReq, err := s.handler.ParseNotifyRequest(ctx, r, transaction)
	if err != nil {
		logx.Errorf("支付回调验签失败: %v", err)
		s.writeErrorResponse(w, "VERIFY_FAIL", "验签失败")
		return
	}

	logx.Infof("收到支付成功回调: ID=%s, 订单号=%s, 微信订单号=%s, 状态=%s",
		notifyReq.ID,
		*transaction.OutTradeNo,
		*transaction.TransactionId,
		*transaction.TradeState)

	// 调用业务处理器
	if s.paymentHandler != nil {
		if err := s.paymentHandler(ctx, transaction); err != nil {
			logx.Errorf("支付回调业务处理失败: %v", err)
			s.writeErrorResponse(w, "BUSINESS_FAIL", "业务处理失败")
			return
		}
	}

	// 返回成功响应
	w.WriteHeader(http.StatusOK)
	logx.Infof("支付回调处理成功: 订单号=%s", *transaction.OutTradeNo)
}

// HandleRefundNotify 处理退款回调通知
func (s *NotifyService) HandleRefundNotify(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// 详细记录请求信息用于诊断
	logx.Infof("===== 微信退款回调详细信息 =====")
	logx.Infof("请求方法: %s", r.Method)
	logx.Infof("请求URL: %s", r.URL.String())
	logx.Infof("User-Agent: %s", r.Header.Get("User-Agent"))
	logx.Infof("Content-Type: %s", r.Header.Get("Content-Type"))
	logx.Infof("Content-Length: %s", r.Header.Get("Content-Length"))

	// 检查微信支付必需的请求头
	wechatSerial := r.Header.Get("Wechatpay-Serial")
	wechatSignature := r.Header.Get("Wechatpay-Signature")
	wechatTimestamp := r.Header.Get("Wechatpay-Timestamp")
	wechatNonce := r.Header.Get("Wechatpay-Nonce")

	logx.Infof("Wechatpay-Serial: [%s]", wechatSerial)
	logx.Infof("Wechatpay-Signature: [%s]", wechatSignature)
	logx.Infof("Wechatpay-Timestamp: [%s]", wechatTimestamp)
	logx.Infof("Wechatpay-Nonce: [%s]", wechatNonce)

	// 如果是真实的微信回调，这些头部不应该为空
	if wechatSerial == "" || wechatSignature == "" || wechatTimestamp == "" || wechatNonce == "" {
		logx.Errorf("微信支付回调缺少必需的请求头，这可能不是真实的微信回调")
		logx.Errorf("请检查：1. 微信支付配置是否正确 2. 回调URL是否正确 3. 是否经过了代理服务器")
		s.writeErrorResponse(w, "INVALID_REQUEST", "缺少必需的微信支付请求头")
		return
	}

	// 先读取原始请求体用于调试
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logx.Errorf("读取请求体失败: %v", err)
		s.writeErrorResponse(w, "INVALID_REQUEST", "读取请求失败")
		return
	}

	// 重新设置请求体，因为已经被读取了
	r.Body = io.NopCloser(strings.NewReader(string(body)))

	logx.Infof("===== 微信退款回调原始数据 =====")
	logx.Infof("原始请求体: %s", string(body))
	logx.Infof("===== 原始数据结束 =====")

	// 解析并验证退款回调通知
	refund := new(refunddomestic.Refund)
	notifyReq, err := s.handler.ParseNotifyRequest(ctx, r, refund)
	if err != nil {
		logx.Errorf("退款回调验签或解密失败: %v", err)
		logx.Errorf("请检查: 1. APIv3密钥是否正确 2. 证书是否有效 3. 加密算法是否匹配")
		s.writeErrorResponse(w, "VERIFY_FAIL", "验签失败")
		return
	}

	// 调试：打印解析后的退款数据结构
	logx.Infof("===== 解析后的退款数据详情 =====")
	logx.Infof("RefundId: %v", refund.RefundId)
	logx.Infof("OutRefundNo: %v", refund.OutRefundNo)
	logx.Infof("OutTradeNo: %v", refund.OutTradeNo)
	logx.Infof("TransactionId: %v", refund.TransactionId)
	logx.Infof("Status: %v", refund.Status)
	if refund.Status != nil {
		logx.Infof("Status值: %s", string(*refund.Status))
	}
	logx.Infof("Amount: %v", refund.Amount)
	if refund.Amount != nil {
		logx.Infof("Amount.Refund: %v", refund.Amount.Refund)
		logx.Infof("Amount.Total: %v", refund.Amount.Total)
	}
	logx.Infof("SuccessTime: %v", refund.SuccessTime)
	logx.Infof("===== 退款数据详情结束 =====")

	// 安全地获取退款信息，避免空指针异常
	var outTradeNo, outRefundNo, status string
	if refund.OutTradeNo != nil {
		outTradeNo = *refund.OutTradeNo
	}
	if refund.OutRefundNo != nil {
		outRefundNo = *refund.OutRefundNo
	}
	if refund.Status != nil {
		status = string(*refund.Status)
	}

	logx.Infof("收到退款回调: ID=%s, 订单号=%s, 退款单号=%s, 状态=%s",
		notifyReq.ID, outTradeNo, outRefundNo, status)

	// 调用业务处理器
	if s.refundHandler != nil {
		if err := s.refundHandler(ctx, refund); err != nil {
			logx.Errorf("退款回调业务处理失败: %v", err)
			s.writeErrorResponse(w, "BUSINESS_FAIL", "业务处理失败")
			return
		}
	}

	// 返回成功响应
	w.WriteHeader(http.StatusOK)
	logx.Infof("退款回调处理成功: 退款单号=%s", outRefundNo)
}

// writeErrorResponse 写入错误响应
func (s *NotifyService) writeErrorResponse(w http.ResponseWriter, code, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusBadRequest)

	response := map[string]string{
		"code":    code,
		"message": message,
	}

	responseBody, _ := json.Marshal(response)
	w.Write(responseBody)
}

// PaymentNotifyResponse 支付回调响应格式（用于文档说明）
type PaymentNotifyResponse struct {
	Code    string `json:"code,omitempty"`    // 返回状态码
	Message string `json:"message,omitempty"` // 返回信息
}

// 全局回调服务实例（单例模式）
var globalNotifyService *NotifyService

// InitGlobalNotifyService 初始化全局回调服务
func InitGlobalNotifyService(config *WechatPayConfig) error {
	service, err := NewNotifyService(config)
	if err != nil {
		return err
	}
	globalNotifyService = service
	return nil
}

// GetGlobalNotifyService 获取全局回调服务实例
func GetGlobalNotifyService() *NotifyService {
	return globalNotifyService
}
