package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	patientHandler "yekaitai/internal/modules/patient/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterPatientRoutes 注册患者管理相关路由
func RegisterPatientRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建患者处理器
	handler := patientHandler.NewPatientHandler(serverCtx)
	diagnosisHandler := patientHandler.NewDiagnosisHandler()

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 注册路由
	server.AddRoutes(
		[]rest.Route{
			// 患者基本信息管理
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients",
				Handler: adminAuthWrapper(handler.ListPatients),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/:patientId",
				Handler: adminAuth<PERSON>rapper(handler.GetPatient),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/patients",
				Handler: adminAuthWrapper(handler.CreatePatient),
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/patients/:patientId",
				Handler: adminAuthWrapper(handler.UpdatePatient),
			},
			{
				Method:  http.MethodPatch,
				Path:    "/api/admin/patients/:patientId/status",
				Handler: adminAuthWrapper(handler.UpdatePatientStatus),
			},
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/patients/:patientId",
				Handler: adminAuthWrapper(handler.DeletePatient),
			},
			// 患者就诊记录管理
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/:patientId/visits",
				Handler: adminAuthWrapper(handler.GetPatientMedicalRecords),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/:patientId/records/:recordId",
				Handler: adminAuthWrapper(handler.GetPatientMedicalRecordDetail),
			},

			// 患者处方记录管理
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/:patientId/prescriptions",
				Handler: adminAuthWrapper(handler.GetPatientPrescriptions),
			},

			// 患者预问诊记录管理
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/:patientId/pre-consultations",
				Handler: adminAuthWrapper(handler.GetPreConsultationList),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/:patientId/pre-consultations/:sessionId",
				Handler: adminAuthWrapper(handler.GetPreConsultationDetail),
			},

			// 患者报告管理
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/:patientId/reports",
				Handler: adminAuthWrapper(handler.GetPatientReports),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/:patientId/reports/:reportId",
				Handler: adminAuthWrapper(handler.GetReportDetail),
			},

			// 患者健康档案管理
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/patients/:patientId/health-records",
				Handler: adminAuthWrapper(handler.AddPatientHealthRecord),
			},

			// 诊断记录管理
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/diagnosis/list",
				Handler: adminAuthWrapper(diagnosisHandler.List),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patients/diagnosis/:id",
				Handler: adminAuthWrapper(diagnosisHandler.Detail),
			},

			// 原路径诊断记录管理路由（保留兼容性）
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patient/diagnosis/list",
				Handler: adminAuthWrapper(diagnosisHandler.List),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/patient/diagnosis/:id",
				Handler: adminAuthWrapper(diagnosisHandler.Detail),
			},
		},
	)
}
