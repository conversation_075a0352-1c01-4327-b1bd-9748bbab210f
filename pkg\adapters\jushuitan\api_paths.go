package jushuitan

// 聚水潭API路径常量
const (
	// 一.基础信息
	ShopQueryAPIPath             = "/open/shops/query"                                           // 店铺查询
	LogisticsCompanyQueryAPIPath = "/open/logisticscompany/query"                                // 物流公司查询
	WarehouseQueryAPIPath        = "/open/wms/partner/query"                                     // 仓库查询
	DistributorQueryAPIPath      = "/open/api/drp/inneropen/partner/supplier/querymychannel"     // 分销商列表查询
	SupplierQueryAPIPath         = "/open/api/company/inneropen/partner/channel/querymysupplier" // 供销商列表查询
	UserInfoAPIPath              = "/open/webapi/userapi/company/getcompanyusers"                // 商家用户信息

	// 二.商品
	ItemSkuUploadAPIPath         = "/open/jushuitan/itemsku/upload"                      // 普通商品资料上传
	ItemSkuQueryAPIPath          = "/open/sku/query"                                     // 普通商品资料查询（按sku）
	MallItemQueryAPIPath         = "/open/mall/item/query"                               // 普通商品资料查询（按款）
	SkuMapUploadAPIPath          = "/open/jushuitan/skumap/upload"                       // 店铺商品资料上传
	SkuMapQueryAPIPath           = "/open/skumap/query"                                  // 店铺商品资料查询
	CombineSkuUploadAPIPath      = "/open/item/combinesku/upload"                        // 组合装商品资料上传
	CombineSkuQueryAPIPath       = "/open/combine/sku/query"                             // 组合装商品资料查询
	CategoryAddOrUpdateAPIPath   = "/open/webapi/itemapi/category/addorupdate"           // 商品类目上传/更新
	CategoryQueryAPIPath         = "/open/category/query"                                // 商品类目查询
	BomSaveAPIPath               = "/open/webapi/itemapi/bom/save"                       // BOM信息上传
	BomQueryAPIPath              = "/open/webapi/itemapi/bom/getskubompagelist"          // BOM信息查询
	CostPriceUploadAPIPath       = "/open/jushuitan/itemsku/costprice/upload"            // 商品历史成本价上传
	HistoryCostPriceQueryAPIPath = "/open/webapi/itemapi/itemsku/gethistorycostpricev2"  // 商品历史成本价查询
	SupplierSkuSaveAPIPath       = "/open/webapi/itemapi/suppliersku/save"               // 商品多供应商上传/更新
	SupplierSkuQueryAPIPath      = "/open/webapi/itemapi/suppliersku/getsupplierskulist" // 商品多供应商查询

	// 三.订单
	OrderQueryAPIPath        = "/open/orders/single/query"
	OrderUploadAPIPath       = "/open/jushuitan/orders/upload"                 // 订单上传（自有商城）
	OrderCancelAPIPath       = "/open/jushuitan/orderbyoid/cancel"             // 订单取消
	OrderSplitAPIPath        = "/open/jushuitan/drporder/split"                // 订单拆分
	OrderModifyWMSAPIPath    = "/open/orders/modifywms/upload"                 // 订单指定发货仓
	OrderQuestionAPIPath     = "/open/webapi/orderapi/questionorder/questions" // 订单转异常
	OrderRemarkUploadAPIPath = "/open/jushuitan/order/remark/upload"           // 修改卖家备注
	OrderNodeSetAPIPath      = "/open/order/node/soid/set"                     // 修改线下备注
	OrderLabelUploadAPIPath  = "/open/jushuitan/order/label/upload"            // 修改订单标签
	OrderSentUploadAPIPath   = "/open/order/sent/upload"                       // 订单发货
	OrderActionQueryAPIPath  = "/open/order/action/query"                      // 订单操作日志查询

	// 四.库存
	InventoryQueryAPIPath         = "/open/inventory/query"                                        // 商品库存查询
	PackQueryAPIPath              = "/open/pack/query"                                             // 箱及仓位库存查询
	InventoryUploadAPIPath        = "/open/jushuitan/inventoryv2/upload"                           // 新建盘点单
	InventoryCountQueryAPIPath    = "/open/inventory/count/query"                                  // 库存盘点查询
	UpdateVirtualInventoryAPIPath = "/open/webapi/itemapi/iteminventory/batchupdatewmsvirtualqtys" // 导入/更新虚拟库存
	PackBatchQueryAPIPath         = "/open/webapi/wmsapi/pack/pagequerypackanditems"               // 箱及仓位库存批量查询

	// 五.售后
	AfterSaleUploadAPIPath        = "/open/aftersale/upload"                           // 售后上传
	AfterSaleNoInfoUploadAPIPath  = "/open/aftersale/noinfo/upload"                    // 售后上传（无信息件）
	RefundSingleQueryAPIPath      = "/open/refund/single/query"                        // 售后退货退款查询
	AfterSaleConfirmGoodsAPIPath  = "/open/webapi/aftersaleapi/confirmgoods"           // 售后确认收到货物
	AfterSaleReceivedQueryAPIPath = "/open/aftersale/received/query"                   // 实际收货查询
	AfterSaleConfirmAPIPath       = "/open/webapi/aftersaleapi/open/confirm"           // 售后单确认
	AfterSaleUnconfirmAPIPath     = "/open/webapi/aftersaleapi/open/unconfirm"         // 售后单反确认
	AfterSaleConfirmBySnsAPIPath  = "/open/webapi/aftersaleapi/confirmgoodsbyskusns"   // 唯一码批量确认收货
	RefundQueryAPIPath            = "/open/webapi/aftersaleapi/pay/payqueryasmodified" // 退款单查询

	// 六.物流
	PathTradeQuery = "/open/logistic/query" // 物流查询
)
