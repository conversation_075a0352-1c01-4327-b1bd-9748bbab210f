package service

import (
	"fmt"
	"time"

	"yekaitai/pkg/adapters/medlinker"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// SyncService 医联数据同步服务
type SyncService struct {
	medlinkerClient *medlinker.MedlinkerClient
}

// NewSyncService 创建同步服务
func NewSyncService(medlinkerClient *medlinker.MedlinkerClient) *SyncService {
	return &SyncService{
		medlinkerClient: medlinkerClient,
	}
}

// StoreData 门店数据
type StoreData struct {
	ID        uint    `json:"id"`
	Name      string  `json:"name"`
	Address   string  `json:"address"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// DepartmentData 科室数据
type DepartmentData struct {
	ID     uint   `json:"id"`
	JgksID string `json:"jgks_id"`
	Name   string `json:"name"`
	WsjgID string `json:"wsjg_id"`
}

// DoctorData 医生数据
type DoctorData struct {
	DoctorID     uint   `json:"doctor_id"`
	Name         string `json:"name"`
	Title        string `json:"title"`
	Specialty    string `json:"specialty"`
	Introduction string `json:"introduction"`
	Mobile       string `json:"mobile"`
	Department   string `json:"department"`
	WsjgID       string `json:"wsjg_id"`
	JgksID       string `json:"jgks_id"`
}

// SyncAllData 全量同步数据到医联
func (s *SyncService) SyncAllData() error {
	logx.Info("开始全量同步数据到医联")

	// 先登录医联
	err := s.medlinkerClient.Login("18888888888") // 使用配置的默认电话
	if err != nil {
		return fmt.Errorf("医联登录失败: %v", err)
	}

	// 获取所有门店数据
	stores, err := s.getAllStores()
	if err != nil {
		return fmt.Errorf("获取门店数据失败: %v", err)
	}

	// 获取所有科室数据
	departments, err := s.getAllDepartments()
	if err != nil {
		return fmt.Errorf("获取科室数据失败: %v", err)
	}

	// 获取所有医生数据
	doctors, err := s.getAllDoctors()
	if err != nil {
		return fmt.Errorf("获取医生数据失败: %v", err)
	}

	// 按门店分组同步数据
	for _, store := range stores {
		err := s.syncStoreData(store, departments, doctors)
		if err != nil {
			logx.Errorf("同步门店数据失败: storeID=%d, error=%v", store.ID, err)
			continue
		}
		logx.Infof("门店数据同步成功: storeID=%d, name=%s", store.ID, store.Name)
	}

	logx.Info("全量同步数据到医联完成")
	return nil
}

// syncStoreData 同步单个门店的数据
func (s *SyncService) syncStoreData(store StoreData, allDepartments []DepartmentData, allDoctors []DoctorData) error {
	// 构建医院数据
	hospitalData := medlinker.HospitalData{
		HospitalID: fmt.Sprintf("ykty_store_%d", store.ID),
		Name:       store.Name,
		Address:    store.Address,
		Longitude:  fmt.Sprintf("%.6f", store.Longitude),
		Latitude:   fmt.Sprintf("%.6f", store.Latitude),
	}

	// 构建科室数据
	var sectionData []medlinker.SectionData
	storeDepartments := s.filterDepartmentsByStore(allDepartments, store.ID)
	for _, dept := range storeDepartments {
		section := medlinker.SectionData{
			SectionID:   fmt.Sprintf("ykty_section_%d_%s", store.ID, dept.JgksID),
			Name:        dept.Name,
			Description: fmt.Sprintf("%s科室", dept.Name),
		}
		sectionData = append(sectionData, section)
	}

	// 构建医生数据
	var doctorData []medlinker.DoctorData
	storeDoctors := s.filterDoctorsByStore(allDoctors, store.ID)
	for _, doctor := range storeDoctors {
		// 解析医生性别（假设从姓名或其他字段推断，这里默认为0）
		gender := 0

		// 解析生日和执业开始日期（这里使用默认值，实际应从数据库获取）
		birthday := "1980-01-01"
		practiceStartDate := "2000-01-01"

		// 构建医生所属科室ID数组
		var sections []string
		if doctor.JgksID != "" {
			sections = append(sections, fmt.Sprintf("ykty_section_%d_%s", store.ID, doctor.JgksID))
		}

		doctorItem := medlinker.DoctorData{
			DoctorID:          fmt.Sprintf("ykty_doctor_%d", doctor.DoctorID),
			Name:              doctor.Name,
			Title:             doctor.Title,
			Gender:            gender,
			Birthday:          birthday,
			PracticeStartDate: practiceStartDate,
			Specialty:         doctor.Specialty,
			Bio:               doctor.Introduction,
			Sections:          sections,
		}
		doctorData = append(doctorData, doctorItem)
	}

	// 构建全量数据请求
	req := &medlinker.ReceiveHospitalDataRequest{
		Data: struct {
			DataID       string                  `json:"data_id"`
			HospitalData medlinker.HospitalData  `json:"hospital_data"`
			SectionData  []medlinker.SectionData `json:"section_data"`
			DoctorData   []medlinker.DoctorData  `json:"doctor_data"`
		}{
			DataID:       fmt.Sprintf("ykty_%s_%d", time.Now().Format("20060102"), store.ID),
			HospitalData: hospitalData,
			SectionData:  sectionData,
			DoctorData:   doctorData,
		},
	}

	// 调用医联接口
	resp, err := s.medlinkerClient.ReceiveHospitalData(req)
	if err != nil {
		return fmt.Errorf("调用医联接口失败: %v", err)
	}

	if resp.Code != 200 {
		return fmt.Errorf("医联接口返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
	}

	return nil
}

// getAllStores 获取所有门店数据
func (s *SyncService) getAllStores() ([]StoreData, error) {
	var stores []StoreData

	err := mysql.Slave().Table("t_stores").
		Select("id, name, address, latitude, longitude").
		Where("deleted_at IS NULL AND status = 1").
		Find(&stores).Error

	if err != nil {
		return nil, err
	}

	logx.Infof("获取到门店数据: %d个", len(stores))
	return stores, nil
}

// getAllDepartments 获取所有科室数据
func (s *SyncService) getAllDepartments() ([]DepartmentData, error) {
	var departments []DepartmentData

	err := mysql.Slave().Table("t_departments").
		Select("id, jgks_id, jgks_mc as name, wsjg_id").
		Where("deleted_at IS NULL AND zf_bz = 0").
		Find(&departments).Error

	if err != nil {
		return nil, err
	}

	logx.Infof("获取到科室数据: %d个", len(departments))
	return departments, nil
}

// getAllDoctors 获取所有医生数据
func (s *SyncService) getAllDoctors() ([]DoctorData, error) {
	var doctors []DoctorData

	err := mysql.Slave().Table("wx_doctor").
		Select("doctor_id, name, title, specialty, introduction, mobile, department, wsjg_id, jgks_id").
		Where("deleted_at IS NULL AND status = 1").
		Find(&doctors).Error

	if err != nil {
		return nil, err
	}

	logx.Infof("获取到医生数据: %d个", len(doctors))
	return doctors, nil
}

// filterDepartmentsByStore 根据门店过滤科室
func (s *SyncService) filterDepartmentsByStore(departments []DepartmentData, storeID uint) []DepartmentData {
	// 这里可以根据实际业务逻辑过滤科室
	// 目前返回所有科室，实际应该根据门店ID关联
	return departments
}

// filterDoctorsByStore 根据门店过滤医生
func (s *SyncService) filterDoctorsByStore(doctors []DoctorData, storeID uint) []DoctorData {
	// 这里可以根据实际业务逻辑过滤医生
	// 目前返回所有医生，实际应该根据门店ID关联
	return doctors
}

// SyncIncrementalData 增量同步数据到医联
func (s *SyncService) SyncIncrementalData() error {
	logx.Info("开始增量同步数据到医联")

	// 先登录医联
	err := s.medlinkerClient.Login("18888888888") // 使用配置的默认电话
	if err != nil {
		return fmt.Errorf("医联登录失败: %v", err)
	}

	// 检查最近10分钟内是否有新增或更新的数据
	since := time.Now().Add(-10 * time.Minute)

	// 检查新增或更新的门店
	updatedStores, err := s.getUpdatedStores(since)
	if err != nil {
		return fmt.Errorf("获取更新的门店数据失败: %v", err)
	}

	// 检查新增或更新的科室
	updatedDepartments, err := s.getUpdatedDepartments(since)
	if err != nil {
		return fmt.Errorf("获取更新的科室数据失败: %v", err)
	}

	// 检查新增或更新的医生
	updatedDoctors, err := s.getUpdatedDoctors(since)
	if err != nil {
		return fmt.Errorf("获取更新的医生数据失败: %v", err)
	}

	// 如果没有更新数据，直接返回
	if len(updatedStores) == 0 && len(updatedDepartments) == 0 && len(updatedDoctors) == 0 {
		logx.Info("没有需要增量同步的数据")
		return nil
	}

	logx.Infof("发现需要增量同步的数据: 门店=%d个, 科室=%d个, 医生=%d个",
		len(updatedStores), len(updatedDepartments), len(updatedDoctors))

	// 同步更新的门店
	for _, store := range updatedStores {
		err := s.syncStoreIncremental(store)
		if err != nil {
			logx.Errorf("增量同步门店失败: storeID=%d, error=%v", store.ID, err)
		}
	}

	// 同步更新的科室
	for _, dept := range updatedDepartments {
		err := s.syncDepartmentIncremental(dept)
		if err != nil {
			logx.Errorf("增量同步科室失败: deptID=%d, error=%v", dept.ID, err)
		}
	}

	// 同步更新的医生
	for _, doctor := range updatedDoctors {
		err := s.syncDoctorIncremental(doctor)
		if err != nil {
			logx.Errorf("增量同步医生失败: doctorID=%d, error=%v", doctor.DoctorID, err)
		}
	}

	logx.Info("增量同步数据到医联完成")
	return nil
}

// getUpdatedStores 获取最近更新的门店
func (s *SyncService) getUpdatedStores(since time.Time) ([]StoreData, error) {
	var stores []StoreData

	err := mysql.Slave().Table("t_stores").
		Select("id, name, address, latitude, longitude").
		Where("deleted_at IS NULL AND status = 1 AND (created_at > ? OR updated_at > ?)", since, since).
		Find(&stores).Error

	return stores, err
}

// getUpdatedDepartments 获取最近更新的科室
func (s *SyncService) getUpdatedDepartments(since time.Time) ([]DepartmentData, error) {
	var departments []DepartmentData

	err := mysql.Slave().Table("t_departments").
		Select("id, jgks_id, jgks_mc as name, wsjg_id").
		Where("deleted_at IS NULL AND zf_bz = 0 AND (created_at > ? OR updated_at > ?)", since, since).
		Find(&departments).Error

	return departments, err
}

// getUpdatedDoctors 获取最近更新的医生
func (s *SyncService) getUpdatedDoctors(since time.Time) ([]DoctorData, error) {
	var doctors []DoctorData

	err := mysql.Slave().Table("wx_doctor").
		Select("doctor_id, name, title, specialty, introduction, mobile, department, wsjg_id, jgks_id").
		Where("deleted_at IS NULL AND status = 1 AND (created_at > ? OR updated_at > ?)", since, since).
		Find(&doctors).Error

	return doctors, err
}

// syncStoreIncremental 增量同步门店
func (s *SyncService) syncStoreIncremental(store StoreData) error {
	req := &medlinker.CreateHospitalRequest{
		HospitalID: fmt.Sprintf("ykty_store_%d", store.ID),
		Name:       store.Name,
		Address:    store.Address,
		Longitude:  fmt.Sprintf("%.6f", store.Longitude),
		Latitude:   fmt.Sprintf("%.6f", store.Latitude),
	}

	resp, err := s.medlinkerClient.CreateHospital(req)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		// 如果创建失败，尝试更新
		updateReq := &medlinker.UpdateHospitalRequest{
			HospitalID: req.HospitalID,
			Name:       req.Name,
			Address:    req.Address,
			Longitude:  req.Longitude,
			Latitude:   req.Latitude,
		}

		updateResp, updateErr := s.medlinkerClient.UpdateHospital(updateReq)
		if updateErr != nil {
			return updateErr
		}

		if updateResp.Code != 200 {
			return fmt.Errorf("更新医院失败: code=%d, msg=%s", updateResp.Code, updateResp.Msg)
		}
	}

	return nil
}

// syncDepartmentIncremental 增量同步科室
func (s *SyncService) syncDepartmentIncremental(dept DepartmentData) error {
	// 获取科室所属的门店ID（这里需要根据实际业务逻辑确定）
	// 暂时使用默认门店ID 1
	storeID := uint(1)

	req := &medlinker.CreateSectionRequest{
		HospitalID:  fmt.Sprintf("ykty_store_%d", storeID),
		SectionID:   fmt.Sprintf("ykty_section_%d_%s", storeID, dept.JgksID),
		Name:        dept.Name,
		Description: fmt.Sprintf("%s科室", dept.Name),
	}

	resp, err := s.medlinkerClient.CreateSection(req)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		// 如果创建失败，尝试更新
		updateReq := &medlinker.UpdateSectionRequest{
			HospitalID:  req.HospitalID,
			SectionID:   req.SectionID,
			Name:        req.Name,
			Description: req.Description,
		}

		updateResp, updateErr := s.medlinkerClient.UpdateSection(updateReq)
		if updateErr != nil {
			return updateErr
		}

		if updateResp.Code != 200 {
			return fmt.Errorf("更新科室失败: code=%d, msg=%s", updateResp.Code, updateResp.Msg)
		}
	}

	return nil
}

// syncDoctorIncremental 增量同步医生
func (s *SyncService) syncDoctorIncremental(doctor DoctorData) error {
	// 获取医生所属的门店ID（这里需要根据实际业务逻辑确定）
	// 暂时使用默认门店ID 1
	storeID := uint(1)

	// 解析医生性别（假设从姓名或其他字段推断，这里默认为0）
	gender := 0

	// 解析生日和执业开始日期（这里使用默认值，实际应从数据库获取）
	birthday := "1980-01-01"
	practiceStartDate := "2000-01-01"

	// 构建医生所属科室ID数组
	var sections []string
	if doctor.JgksID != "" {
		sections = append(sections, fmt.Sprintf("ykty_section_%d_%s", storeID, doctor.JgksID))
	}

	req := &medlinker.CreateDoctorRequest{
		HospitalID:        fmt.Sprintf("ykty_store_%d", storeID),
		DoctorID:          fmt.Sprintf("ykty_doctor_%d", doctor.DoctorID),
		Name:              doctor.Name,
		Title:             doctor.Title,
		Gender:            gender,
		Birthday:          birthday,
		PracticeStartDate: practiceStartDate,
		Specialty:         doctor.Specialty,
		Bio:               doctor.Introduction,
		Sections:          sections,
		DataID:            fmt.Sprintf("ykty_doctor_%d_%s", doctor.DoctorID, time.Now().Format("20060102")),
	}

	resp, err := s.medlinkerClient.CreateDoctor(req)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		// 如果创建失败，尝试更新
		updateReq := &medlinker.UpdateDoctorRequest{
			HospitalID:        req.HospitalID,
			DoctorID:          req.DoctorID,
			Name:              req.Name,
			Title:             req.Title,
			Gender:            req.Gender,
			Birthday:          req.Birthday,
			PracticeStartDate: req.PracticeStartDate,
			Specialty:         req.Specialty,
			Bio:               req.Bio,
			Sections:          req.Sections,
		}

		updateResp, updateErr := s.medlinkerClient.UpdateDoctor(updateReq)
		if updateErr != nil {
			return updateErr
		}

		if updateResp.Code != 200 {
			return fmt.Errorf("更新医生失败: code=%d, msg=%s", updateResp.Code, updateResp.Msg)
		}
	}

	return nil
}

// GetMedlinkerConfig 获取医联客户端配置
func (s *SyncService) GetMedlinkerConfig() medlinker.Config {
	return s.medlinkerClient.Config
}

// GetMedlinkerClient 获取医联客户端
func (s *SyncService) GetMedlinkerClient() *medlinker.MedlinkerClient {
	return s.medlinkerClient
}

// SyncStatus 同步状态
type SyncStatus struct {
	LastFullSync        string `json:"last_full_sync"`        // 最后一次全量同步时间
	LastIncrementalSync string `json:"last_incremental_sync"` // 最后一次增量同步时间
	TotalStores         int    `json:"total_stores"`          // 总门店数
	TotalDepartments    int    `json:"total_departments"`     // 总科室数
	TotalDoctors        int    `json:"total_doctors"`         // 总医生数
	SyncEnabled         bool   `json:"sync_enabled"`          // 是否启用同步
}

// GetSyncStatus 获取同步状态
func (s *SyncService) GetSyncStatus() (*SyncStatus, error) {
	// 获取数据统计
	var storeCount, deptCount, doctorCount int64

	mysql.Slave().Table("t_stores").Where("deleted_at IS NULL AND status = 1").Count(&storeCount)
	mysql.Slave().Table("t_departments").Where("deleted_at IS NULL AND zf_bz = 0").Count(&deptCount)
	mysql.Slave().Table("wx_doctor").Where("deleted_at IS NULL AND status = 1").Count(&doctorCount)

	status := &SyncStatus{
		LastFullSync:        "2024-01-01 00:00:00", // 这里应该从配置或日志表获取
		LastIncrementalSync: "2024-01-01 00:00:00", // 这里应该从配置或日志表获取
		TotalStores:         int(storeCount),
		TotalDepartments:    int(deptCount),
		TotalDoctors:        int(doctorCount),
		SyncEnabled:         true, // 这里应该从配置获取
	}

	return status, nil
}

// SetSyncConfig 设置同步配置
func (s *SyncService) SetSyncConfig(enabled bool, interval int, phone, baseURL string, modelID, dailyCallLimit int) error {
	// 这里应该将配置保存到数据库或配置文件
	// 暂时只记录日志
	logx.Infof("设置同步配置: enabled=%v, interval=%d, phone=%s, baseURL=%s, modelID=%d, dailyCallLimit=%d",
		enabled, interval, phone, baseURL, modelID, dailyCallLimit)

	// 更新客户端配置
	s.medlinkerClient.Config.BaseURL = baseURL
	s.medlinkerClient.Config.ModelID = modelID
	s.medlinkerClient.Config.DailyCallLimit = dailyCallLimit

	return nil
}

// SyncLogItem 同步日志项
type SyncLogItem struct {
	ID        uint   `json:"id"`         // 日志ID
	Type      string `json:"type"`       // 同步类型
	Status    string `json:"status"`     // 状态
	Message   string `json:"message"`    // 消息
	StartTime string `json:"start_time"` // 开始时间
	EndTime   string `json:"end_time"`   // 结束时间
	Duration  int    `json:"duration"`   // 耗时（秒）
}

// GetSyncLogs 获取同步日志
func (s *SyncService) GetSyncLogs(page, pageSize int, syncType, status string) ([]SyncLogItem, int64, error) {
	// 这里应该从数据库获取同步日志
	// 暂时返回模拟数据
	logs := []SyncLogItem{
		{
			ID:        1,
			Type:      "full",
			Status:    "success",
			Message:   "全量同步成功",
			StartTime: "2024-01-01 10:00:00",
			EndTime:   "2024-01-01 10:05:00",
			Duration:  300,
		},
		{
			ID:        2,
			Type:      "incremental",
			Status:    "success",
			Message:   "增量同步成功",
			StartTime: "2024-01-01 10:10:00",
			EndTime:   "2024-01-01 10:11:00",
			Duration:  60,
		},
	}

	// 简单的分页处理
	start := (page - 1) * pageSize
	end := start + pageSize
	if start >= len(logs) {
		return []SyncLogItem{}, int64(len(logs)), nil
	}
	if end > len(logs) {
		end = len(logs)
	}

	return logs[start:end], int64(len(logs)), nil
}
