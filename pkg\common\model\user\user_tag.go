package user

import "time"

// UserTag 用户与标签的多对多关系中间表
type UserTag struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:关联ID"`
	UserID    uint      `json:"user_id" gorm:"index;not null;comment:用户ID"`
	TagID     uint      `json:"tag_id" gorm:"index;not null;comment:标签ID"`
	CreatedAt time.Time `json:"created_at" gorm:"comment:创建时间"`
}

// TableName 设置UserTag表名
func (UserTag) TableName() string {
	return "user_tag"
}
