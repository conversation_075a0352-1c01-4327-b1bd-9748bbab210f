package service

import (
	"time"

	"gorm.io/gorm"
)

// ServicePackageRecommend 推荐服务套餐表
type ServicePackageRecommend struct {
	ID               uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	ServicePackageID uint           `gorm:"column:service_package_id;not null;index;comment:服务套餐ID" json:"service_package_id"`
	IsTop            bool           `gorm:"column:is_top;default:false;comment:是否置顶" json:"is_top"`
	SortOrder        int            `gorm:"column:sort_order;default:0;comment:排序值，数字越小越靠前" json:"sort_order"`
	Status           int            `gorm:"column:status;default:1;comment:状态(0-禁用，1-启用)" json:"status"`
	CreatorID        uint           `gorm:"column:creator_id;comment:创建人ID" json:"creator_id"`
	CreatedAt        time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`

	// 关联字段（用于查询时填充）
	ServicePackageName string  `gorm:"-" json:"service_package_name"` // 套餐名称
	TagName            string  `gorm:"-" json:"tag_name"`             // 标签名称
	StoreNames         string  `gorm:"-" json:"store_names"`          // 适用门店名称
	Price              float64 `gorm:"-" json:"price"`                // 价格
	Images             string  `gorm:"-" json:"images"`               // 套餐图片
}

// TableName 设置表名
func (ServicePackageRecommend) TableName() string {
	return "service_package_recommends"
}

// ServicePackageRecommendInfo 推荐服务套餐信息（用于列表展示）
type ServicePackageRecommendInfo struct {
	ID                 uint    `json:"id"`
	ServicePackageID   uint    `json:"service_package_id"`
	ServicePackageName string  `json:"service_package_name"`
	TagName            string  `json:"tag_name"`
	StoreNames         string  `json:"store_names"`
	Price              float64 `json:"price"`
	Images             string  `json:"images"`
	IsTop              bool    `json:"is_top"`
	SortOrder          int     `json:"sort_order"`
	Status             int     `json:"status"`
	CreatorID          uint    `json:"creator_id"`
	CreatedAt          string  `json:"created_at"`
	UpdatedAt          string  `json:"updated_at"`
}

// ServicePackageRecommendQueryParams 推荐服务套餐查询参数
type ServicePackageRecommendQueryParams struct {
	Page               int    `json:"page" validate:"min=1"`
	PageSize           int    `json:"page_size" validate:"min=1,max=100"`
	ServicePackageName string `json:"service_package_name,optional"`
	StoreID            uint   `json:"store_id,optional"`
	IsTop              *bool  `json:"is_top,optional"`
	Status             *int   `json:"status,optional"`
}

// ServicePackageSelectInfo 服务套餐选择信息（用于选择列表）
type ServicePackageSelectInfo struct {
	ServicePackageID   uint    `json:"service_package_id"`
	Name               string  `json:"name"`
	TagName            string  `json:"tag_name"`
	StoreNames         string  `json:"store_names"`
	Price              float64 `json:"price"`
	Images             string  `json:"images"`
	IsRecommended      bool    `json:"is_recommended"`
}

// ServicePackageSelectQueryParams 服务套餐选择查询参数
type ServicePackageSelectQueryParams struct {
	Page               int    `json:"page" validate:"min=1"`
	PageSize           int    `json:"page_size" validate:"min=1,max=100"`
	ServicePackageName string `json:"service_package_name,optional"`
	StoreID            uint   `json:"store_id,optional"`
}
