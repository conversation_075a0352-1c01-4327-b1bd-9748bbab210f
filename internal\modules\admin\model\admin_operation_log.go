package model

import (
	"time"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// AdminOperationLog 管理员操作日志表
type AdminOperationLog struct {
	LogID      uint      `json:"log_id" gorm:"primaryKey;autoIncrement;comment:日志ID"`
	AdminID    uint      `json:"admin_id" gorm:"index;not null;default:0;comment:操作人ID"`
	Module     string    `json:"module" gorm:"type:varchar(50);not null;comment:操作模块"`
	Action     string    `json:"action" gorm:"type:varchar(50);not null;comment:操作类型"`
	TargetID   uint      `json:"target_id" gorm:"index;not null;default:0;comment:操作对象ID"`
	TargetType string    `json:"target_type" gorm:"type:varchar(50);not null;comment:操作对象类型"`
	Content    string    `json:"content" gorm:"type:text;not null;comment:操作内容"`
	IP         string    `json:"ip" gorm:"type:varchar(50);not null;comment:操作IP"`
	Duration   int       `json:"duration" gorm:"default:0;comment:执行时间(毫秒)"`
	StatusCode int       `json:"status_code" gorm:"default:200;comment:响应状态码"`
	CreatedAt  time.Time `json:"created_at" gorm:"not null;comment:操作时间"`
}

// TableName 设置表名
func (AdminOperationLog) TableName() string {
	return "admin_operation_log"
}

// AdminOperationLogRepository 管理员操作日志仓库接口
type AdminOperationLogRepository interface {
	Create(log *AdminOperationLog) error
	List(page, size int, query string) ([]*AdminOperationLog, int64, error)
	ListWithFilter(page, size int, adminID *uint, startTime, endTime *time.Time, module string) ([]*AdminOperationLog, int64, error)
}

// adminOperationLogRepository 管理员操作日志仓库实现
type adminOperationLogRepository struct {
	db *gorm.DB
}

// NewAdminOperationLogRepository 创建管理员操作日志仓库
func NewAdminOperationLogRepository(db *gorm.DB) AdminOperationLogRepository {
	if db == nil {
		db = mysql.Slave()
	}
	return &adminOperationLogRepository{db: db}
}

// Create 创建操作日志
func (r *adminOperationLogRepository) Create(log *AdminOperationLog) error {
	return r.db.Create(log).Error
}

// List 获取操作日志列表
func (r *adminOperationLogRepository) List(page, size int, query string) ([]*AdminOperationLog, int64, error) {
	var logs []*AdminOperationLog
	var total int64

	db := r.db

	// 如果有查询条件，添加到查询中
	if query != "" {
		db = db.Where("module LIKE ? OR action LIKE ? OR content LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// 获取总数
	if err := db.Model(&AdminOperationLog{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("log_id DESC").Offset(offset).Limit(size).Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// ListWithFilter 根据条件获取操作日志列表
func (r *adminOperationLogRepository) ListWithFilter(page, size int, adminID *uint, startTime, endTime *time.Time, module string) ([]*AdminOperationLog, int64, error) {
	var logs []*AdminOperationLog
	var total int64

	db := r.db.Model(&AdminOperationLog{})

	// 添加筛选条件
	if adminID != nil && *adminID > 0 {
		db = db.Where("admin_id = ?", *adminID)
	}

	if startTime != nil {
		db = db.Where("created_at >= ?", *startTime)
	}

	if endTime != nil {
		db = db.Where("created_at <= ?", *endTime)
	}

	if module != "" {
		db = db.Where("module = ?", module)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("log_id DESC").Offset(offset).Limit(size).Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}
