package redeemer

import (
	"yekaitai/pkg/infra/mysql"
)

// WxRedeemerRepository 核销员仓库接口
type WxRedeemerRepository interface {
	Create(redeemer *WxRedeemer) error
	Update(redeemer *WxRedeemer) error
	Delete(id uint) error
	FindByID(id uint) (*WxRedeemer, error)
	FindByUserID(userID uint) (*WxRedeemer, error)
	List(page, size int, keyword string) ([]*WxRedeemer, int64, error)
	UpdateStatus(id uint, status int) error
}

// wxRedeemerRepository 核销员仓库实现
type wxRedeemerRepository struct{}

// NewWxRedeemerRepository 创建核销员仓库实例
func NewWxRedeemerRepository() WxRedeemerRepository {
	return &wxRedeemerRepository{}
}

// Create 创建核销员记录
func (r *wxRedeemerRepository) Create(redeemer *WxRedeemer) error {
	return mysql.Master().Create(redeemer).Error
}

// Update 更新核销员记录
func (r *wxRedeemerRepository) Update(redeemer *WxRedeemer) error {
	return mysql.Master().Save(redeemer).Error
}

// Delete 删除核销员记录
func (r *wxRedeemerRepository) Delete(id uint) error {
	return mysql.Master().Delete(&WxRedeemer{}, id).Error
}

// FindByID 根据ID查找核销员
func (r *wxRedeemerRepository) FindByID(id uint) (*WxRedeemer, error) {
	var redeemer WxRedeemer
	err := mysql.Slave().Where("redeemer_id = ?", id).First(&redeemer).Error
	if err != nil {
		return nil, err
	}
	return &redeemer, nil
}

// FindByUserID 根据用户ID查找核销员
func (r *wxRedeemerRepository) FindByUserID(userID uint) (*WxRedeemer, error) {
	var redeemer WxRedeemer
	err := mysql.Slave().Where("user_id = ?", userID).First(&redeemer).Error
	if err != nil {
		return nil, err
	}
	return &redeemer, nil
}

// List 获取核销员列表
func (r *wxRedeemerRepository) List(page, size int, keyword string) ([]*WxRedeemer, int64, error) {
	var redeemers []*WxRedeemer
	var total int64

	db := mysql.Slave().Model(&WxRedeemer{})

	// 如果有关键字，添加搜索条件
	if keyword != "" {
		db = db.Where("redeemer_id LIKE ? OR user_id LIKE ? OR store_id LIKE ? OR store_name LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("redeemer_id DESC").Offset(offset).Limit(size).Find(&redeemers).Error; err != nil {
		return nil, 0, err
	}

	return redeemers, total, nil
}

// UpdateStatus 更新核销员状态
func (r *wxRedeemerRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&WxRedeemer{}).Where("redeemer_id = ?", id).Update("status", status).Error
}
