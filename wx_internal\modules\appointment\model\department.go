package model

import (
	"time"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// 科室状态
const (
	DepartmentStatusActive   = 1 // 正常
	DepartmentStatusInactive = 0 // 停用
)

// Department 科室信息
type Department struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
	Name        string         `gorm:"size:50;not null" json:"name"`            // 科室名称
	ClinicID    string         `gorm:"type:varchar(64);index" json:"clinic_id"` // 诊所ID
	Description string         `gorm:"size:200" json:"description"`             // 科室描述
	Icon        string         `gorm:"size:200" json:"icon"`                    // 科室图标
	Status      int            `gorm:"not null;default:1" json:"status"`        // 状态：1-正常，0-停用
	Sort        int            `gorm:"not null;default:0" json:"sort"`          // 排序
	ExternalID  string         `gorm:"size:50;index" json:"external_id"`        // 外部系统科室ID
	ExternalKey string         `gorm:"size:20;index" json:"external_key"`       // 外部系统标识，如：yekaitai, abcyun
}

// TableName 表名
func (Department) TableName() string {
	return "departments"
}

// DepartmentRepository 科室仓库接口
type DepartmentRepository interface {
	Create(department *Department) error
	Update(department *Department) error
	Delete(id uint) error
	FindByID(id uint) (*Department, error)
	FindAll() ([]*Department, error)
	FindByStatus(status int) ([]*Department, error)
	FindByExternalID(externalID string) (*Department, error)
}

// departmentRepository 科室仓库实现
type departmentRepository struct{}

// NewDepartmentRepository 创建科室仓库
func NewDepartmentRepository() DepartmentRepository {
	return &departmentRepository{}
}

// Create 创建科室
func (r *departmentRepository) Create(department *Department) error {
	return mysql.Master().Create(department).Error
}

// Update 更新科室
func (r *departmentRepository) Update(department *Department) error {
	return mysql.Master().Save(department).Error
}

// Delete 删除科室
func (r *departmentRepository) Delete(id uint) error {
	return mysql.Master().Delete(&Department{}, id).Error
}

// FindByID 根据ID查询科室
func (r *departmentRepository) FindByID(id uint) (*Department, error) {
	var department Department
	err := mysql.Slave().Where("id = ?", id).First(&department).Error
	if err != nil {
		return nil, err
	}
	return &department, nil
}

// FindAll 查询所有科室
func (r *departmentRepository) FindAll() ([]*Department, error) {
	var departments []*Department
	err := mysql.Slave().Order("sort desc, id asc").Find(&departments).Error
	return departments, err
}

// FindByStatus 根据状态查询科室
func (r *departmentRepository) FindByStatus(status int) ([]*Department, error) {
	var departments []*Department
	err := mysql.Slave().Where("status = ?", status).Order("sort desc, id asc").Find(&departments).Error
	return departments, err
}

// FindByExternalID 根据外部ID查询科室
func (r *departmentRepository) FindByExternalID(externalID string) (*Department, error) {
	var department Department
	err := mysql.Slave().Where("external_id = ?", externalID).First(&department).Error
	if err != nil {
		return nil, err
	}
	return &department, nil
}
