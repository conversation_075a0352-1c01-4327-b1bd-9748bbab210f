package abcyun

import (
	"encoding/json"
	"fmt"
	"strconv"
)

// EventPushDetail 事件推送详情
type EventPushDetail struct {
	ID             string       `json:"id"`
	AppId          string       `json:"appId"`
	AppCallBackUrl string       `json:"appCallBackUrl"`
	CallBackBody   CallBackBody `json:"callBackBody"`
	FailReason     string       `json:"failReason"`
	Status         int          `json:"status"`
	Created        string       `json:"created"`
}

// CallBackBody 回调内容
type CallBackBody struct {
	ClinicId    string      `json:"clinicId"`
	EventModule int         `json:"eventModule"`
	EventType   int         `json:"eventType"`
	EventName   string      `json:"eventName"`
	EventData   interface{} `json:"eventData"`
}

// PaginatedEventPushResult 分页事件推送结果
type PaginatedEventPushResult struct {
	Rows   []EventPushDetail `json:"rows"`
	Total  int               `json:"total"`
	Offset int               `json:"offset"`
	Limit  int               `json:"limit"`
}

// AddressInfo 地区信息
type AddressInfo struct {
	ID       string        `json:"id"`
	Name     string        `json:"name"`
	Children []AddressInfo `json:"children"`
}

// OssToken OSS开放Token
type OssToken struct {
	Endpoint        string `json:"endpoint"`
	Bucket          string `json:"bucket"`
	FileDir         string `json:"fileDir"`
	SecurityToken   string `json:"securityToken"`
	AccessKeySecret string `json:"accessKeySecret"`
	AccessKeyId     string `json:"accessKeyId"`
	Expiration      string `json:"expiration"`
}

// AlertResult 告警结果
type AlertResult struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// GetAppCallbackFailList 获取推送失败事件列表
func (c *AbcYunClient) GetAppCallbackFailList(appId string, date string, limit int, offset int) (*Response, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"date":   date,
		"limit":  strconv.Itoa(limit),
		"offset": strconv.Itoa(offset),
	}

	// 构建API路径
	path := fmt.Sprintf("/api/v2/open-agency/app-callback/%s/fail", appId)

	// 发送请求
	response, err := c.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("获取推送失败事件列表失败: %w", err)
	}

	// 解析响应
	var result Response
	var paginatedResult PaginatedEventPushResult

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &paginatedResult)
	if err != nil {
		return nil, fmt.Errorf("解析分页数据失败: %w", err)
	}

	// 将解析后的分页数据赋值回result.Data
	result.Data = paginatedResult

	return &result, nil
}

// GetChinaAddressInfo 获取中国地区信息
func (c *AbcYunClient) GetChinaAddressInfo() (*Response, error) {
	// 构建API路径
	path := "/api/v2/open-agency/property/address/china"

	// 发送请求
	response, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取中国地区信息失败: %w", err)
	}

	// 解析响应
	var result Response
	var addressInfo AddressInfo

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &addressInfo)
	if err != nil {
		return nil, fmt.Errorf("解析地区信息失败: %w", err)
	}

	// 将解析后的地区信息赋值回result.Data
	result.Data = addressInfo

	return &result, nil
}

// GetOssToken 获取OSS Token
func (c *AbcYunClient) GetOssToken() (*Response, error) {
	// 构建API路径
	path := "/api/v2/open-agency/property/oss/token"

	// 发送请求
	response, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取OSS Token失败: %w", err)
	}

	// 解析响应
	var result Response
	var ossToken OssToken

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &ossToken)
	if err != nil {
		return nil, fmt.Errorf("解析OSS Token失败: %w", err)
	}

	// 将解析后的OSS Token赋值回result.Data
	result.Data = ossToken

	return &result, nil
}

// ReportAlert 发送报警
func (c *AbcYunClient) ReportAlert(title string, content string) (*Response, error) {
	// 构建请求体
	requestBody := map[string]string{
		"title":   title,
		"content": content,
	}

	// 构建API路径
	path := "/api/v2/open-agency/property/report/alert"

	// 发送请求
	response, err := c.Post(path, requestBody)
	if err != nil {
		return nil, fmt.Errorf("发送报警失败: %w", err)
	}

	// 解析响应
	var result Response
	var alertResult AlertResult

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &alertResult)
	if err != nil {
		return nil, fmt.Errorf("解析报警结果失败: %w", err)
	}

	// 将解析后的报警结果赋值回result.Data
	result.Data = alertResult

	return &result, nil
}
