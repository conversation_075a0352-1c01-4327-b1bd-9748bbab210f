package service

import (
	"fmt"
	"yekaitai/internal/config"
	"yekaitai/pkg/sms"

	"github.com/zeromicro/go-zero/core/logx"
)

// CreateSmsServiceWithConfig 创建带配置的短信服务
func CreateSmsServiceWithConfig(smsConfig config.TencentSmsConfig) *SmsService {
	logx.Infof("=== 短信服务初始化开始 ===")
	logx.Infof("接收到的配置: %+v", smsConfig)

	smsService := NewSmsService()

	// 详细打印配置信息用于调试
	logx.Infof("配置字段检查:")
	logx.Infof("  SecretId: '%s' (长度: %d)", smsConfig.SecretId, len(smsConfig.SecretId))
	logx.Infof("  SecretKey: '%s' (长度: %d)", smsConfig.Secret<PERSON>ey, len(smsConfig.SecretKey))
	logx.Infof("  SdkAppId: '%s' (长度: %d)", smsConfig.SdkAppId, len(smsConfig.SdkAppId))
	logx.Infof("  SignName: '%s' (长度: %d)", smsConfig.SignName, len(smsConfig.SignName))
	logx.Infof("  TemplateId: '%s' (长度: %d)", smsConfig.TemplateId, len(smsConfig.TemplateId))
	logx.Infof("  Region: '%s' (长度: %d)", smsConfig.Region, len(smsConfig.Region))

	// 检查必要字段是否为空
	if smsConfig.SecretId == "" {
		logx.Error("SecretId为空")
	}
	if smsConfig.SecretKey == "" {
		logx.Error("SecretKey为空")
	}
	if smsConfig.SdkAppId == "" {
		logx.Error("SdkAppId为空")
	}

	// 如果配置了腾讯云短信，则创建腾讯云短信客户端
	if smsConfig.SecretId != "" && smsConfig.SecretKey != "" && smsConfig.SdkAppId != "" {
		logx.Info("配置验证通过，开始创建腾讯云短信客户端...")
		tencentSmsClient, err := sms.NewTencentSmsClient(smsConfig)
		if err != nil {
			logx.Errorf("创建腾讯云短信客户端失败: %v", err)
			panic(fmt.Sprintf("腾讯云短信客户端创建失败: %v", err))
		}

		logx.Info("腾讯云短信客户端创建成功，开始验证配置...")
		// 验证配置
		if err := tencentSmsClient.ValidateConfig(); err != nil {
			logx.Errorf("腾讯云短信配置验证失败: %v", err)
			panic(fmt.Sprintf("腾讯云短信配置验证失败: %v", err))
		}

		smsService.SetSmsClient(tencentSmsClient)
		logx.Info("腾讯云短信客户端初始化成功，将使用真实短信发送")
	} else {
		logx.Errorf("腾讯云短信配置不完整:")
		logx.Errorf("  SecretId: '%s' (是否为空: %t)", smsConfig.SecretId, smsConfig.SecretId == "")
		logx.Errorf("  SecretKey: '%s' (是否为空: %t)", smsConfig.SecretKey, smsConfig.SecretKey == "")
		logx.Errorf("  SdkAppId: '%s' (是否为空: %t)", smsConfig.SdkAppId, smsConfig.SdkAppId == "")
		panic("腾讯云短信配置不完整，无法初始化短信服务")
	}

	logx.Infof("=== 短信服务初始化完成 ===")
	return smsService
}
