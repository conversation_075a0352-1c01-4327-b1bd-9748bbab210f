package hangzhou

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// BaseResp 通用响应结构
type BaseResp struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// 1. 登录认证接口
// URL: /api/v1/api-gateway/oauth/token
// GetAccessToken 获取访问令牌，这个方法已在client.go中实现
// 使用方式: token, err := client.GetAccessToken(ctx)

// 2. 查询卫生机构信息接口
// URL: /api/v1/consumer-jcfw/jcfw/wsjg/{wsjgid}
// 医院信息
func (c *Client) GetHealthOrganization(ctx context.Context, wsjgID int) (*BaseResp, error) {
	// 构建API路径
	path := fmt.Sprintf("/api/v1/consumer-jcfw/jcfw/wsjg/%d", wsjgID)

	// 发送请求
	resp, err := c.Request(ctx, path, nil)
	if err != nil {
		return nil, fmt.Errorf("卫生机构信息查询请求失败: %w", err)
	}

	// 解析响应
	var organization HealthOrganization
	if err := jsoniter_.Unmarshal(resp, &organization); err != nil {
		return nil, fmt.Errorf("解析卫生机构信息响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到卫生机构信息: ID=%d, 名称=%s", organization.WsjgID, organization.WsjgMC)

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取卫生机构信息成功",
		Data: organization,
	}

	return result, nil
}

// GetHealthOrganization 医院信息(全局方法)
func GetHealthOrganization(ctx context.Context, wsjgID int) (*BaseResp, error) {
	return DefaultClient.GetHealthOrganization(ctx, wsjgID)
}

// 3. 查询机构科室信息接口
// URL: /api/v1/consumer-jcfw/jcfw/jgks/list
// 查询当前可预约机构科室
func (c *Client) GetDepartments(ctx context.Context, pbksbz int, wsjgid string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := make(map[string]string)

	// 如果提供了相关参数，则添加
	if pbksbz != 0 {
		queryParams["pbksbz"] = strconv.Itoa(pbksbz)
	}
	if wsjgid != "" {
		queryParams["wsjgid"] = wsjgid
	}

	// 构建API路径
	path := "/api/v1/consumer-jcfw/jcfw/jgks/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("机构科室查询请求失败: %w", err)
	}

	// 解析响应
	var departments []interface{}
	if err := json.Unmarshal(resp, &departments); err != nil {
		return nil, fmt.Errorf("解析机构科室响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到机构科室列表: 机构ID=%s, 科室数量=%d", wsjgid, len(departments))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取机构科室列表成功",
		Data: departments,
	}

	return result, nil
}

// GetDepartments 查询科室信息(全局方法)
func GetDepartments(ctx context.Context, wsjgID int, pbksbz int) (*BaseResp, error) {
	return DefaultClient.GetDepartments(ctx, pbksbz, strconv.Itoa(wsjgID))
}

// 4. 查询用户信息接口
// URL: /api/v1/consumer-jcfw/jcfw/yh/list
// 查询用户信息
func (c *Client) GetUsers(ctx context.Context, qybz, zfbz string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := make(map[string]string)

	// 如果提供了相关参数，则添加
	if qybz != "" {
		queryParams["qybz"] = qybz
	}
	if zfbz != "" {
		queryParams["zfbz"] = zfbz
	}

	// 设置特殊请求头 - 无需特殊调整，使用默认值

	// 构建API路径
	path := "/api/v1/consumer-jcfw/jcfw/yh/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("用户查询请求失败: %w", err)
	}

	// 解析响应
	var users []User
	if err := jsoniter_.Unmarshal(resp, &users); err != nil {
		return nil, fmt.Errorf("解析用户响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到用户列表: 用户数量=%d", len(users))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取用户列表成功",
		Data: users,
	}

	return result, nil
}

// GetUsers 查询用户信息(全局方法)
func GetUsers(ctx context.Context, qybz, zfbz string) (*BaseResp, error) {
	return DefaultClient.GetUsers(ctx, qybz, zfbz)
}

// 5. 查询用户科室信息接口
// URL: /api/v1/consumer-jcfw/jcfw/yhks/list
// 医生所属科室
func (c *Client) GetUserDepartments(ctx context.Context, wsjgID, yhID int, zfbz string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"wsjgid": strconv.Itoa(wsjgID),
		"yhid":   strconv.Itoa(yhID),
	}

	// 如果提供了作废标志参数，则添加
	if zfbz != "" {
		queryParams["zfbz"] = zfbz
	}

	// 设置特殊请求头 - 无需特殊调整，使用默认值

	// 构建API路径
	path := "/api/v1/consumer-jcfw/jcfw/yhks/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("用户科室查询请求失败: %w", err)
	}

	// 解析响应
	var userDepartments []UserDepartment
	if err := jsoniter_.Unmarshal(resp, &userDepartments); err != nil {
		return nil, fmt.Errorf("解析用户科室响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到用户科室列表: 用户ID=%d, 科室数量=%d", yhID, len(userDepartments))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取用户科室列表成功",
		Data: userDepartments,
	}

	return result, nil
}

// GetUserDepartments 医生所属科室(全局方法)
func GetUserDepartments(ctx context.Context, wsjgID, yhID int, zfbz string) (*BaseResp, error) {
	return DefaultClient.GetUserDepartments(ctx, wsjgID, yhID, zfbz)
}

// 6. 查询卫生人员信息接口
// URL: /api/v1/consumer-jcfw/jcfw/wsry/list
// 医生信息
func (c *Client) GetHealthPersons(ctx context.Context, wsjgID, yhID int, zfbz string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"wsjgid": strconv.Itoa(wsjgID),
	}

	// 如果提供了相关参数，则添加
	if yhID > 0 {
		queryParams["yhid"] = strconv.Itoa(yhID)
	}
	if zfbz != "" {
		queryParams["zfbz"] = zfbz
	}

	// 设置特殊请求头 - 无需特殊调整，使用默认值

	// 构建API路径
	path := "/api/v1/consumer-jcfw/jcfw/wsry/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("卫生人员查询请求失败: %w", err)
	}

	// 解析响应
	var healthPersons []HealthPerson
	if err := jsoniter_.Unmarshal(resp, &healthPersons); err != nil {
		return nil, fmt.Errorf("解析卫生人员响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到卫生人员列表: 卫生机构ID=%d, 人员数量=%d", wsjgID, len(healthPersons))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取卫生人员列表成功",
		Data: healthPersons,
	}

	return result, nil
}

// GetHealthPersons 医生信息(全局方法)
func GetHealthPersons(ctx context.Context, wsjgID, yhID int, zfbz string) (*BaseResp, error) {
	return DefaultClient.GetHealthPersons(ctx, wsjgID, yhID, zfbz)
}

// CurrentUser 当前登录用户信息
type CurrentUser struct {
	YhID   int    `json:"yhid"`   // 用户ID
	YhMC   string `json:"yhmc"`   // 用户名称
	JsDj   string `json:"jsdj"`   // 角色等级
	YhjsID int    `json:"yhjsid"` // 用户角色ID
	WsjgID int    `json:"wsjgid"` // 卫生机构ID
}

// 7. 获取当前登录用户信息
func (c *Client) GetCurrentUserInfo(ctx context.Context) (*BaseResp, error) {
	// 首先确保已经登录并获取到了token
	if _, err := c.GetAccessToken(ctx); err != nil {
		return nil, fmt.Errorf("获取认证信息失败: %w", err)
	}

	// 读取登录时获取的信息
	yhjsid, _ := strconv.Atoi(c.config.CurrentYhjsid)
	wsjgid, _ := strconv.Atoi(c.config.CurrentWsjgid)

	// 构建当前用户信息
	currentUser := &CurrentUser{
		YhID:   c.config.UserID,
		YhMC:   c.config.LoginUserName,
		JsDj:   c.config.CurrentJsdj,
		YhjsID: yhjsid,
		WsjgID: wsjgid,
	}

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取当前用户信息成功",
		Data: currentUser,
	}

	return result, nil
}

// GetCurrentUserInfo 获取当前登录用户信息(全局方法)
func GetCurrentUserInfo(ctx context.Context) (*BaseResp, error) {
	return DefaultClient.GetCurrentUserInfo(ctx)
}

// ==================== 医疗服务接口(ylfw) ====================

// 8. 医生排班查询接口
// URL: /api/v1/consumer-ylfw/ylfw/yspb/ys/list
// 17.当前机构科室下显示医生的接口
func (c *Client) GetDoctorSchedules(ctx context.Context, pbrq string, wsjgID, jgksID int) (*BaseResp, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"pbrq":   pbrq,                 // 排班日期
		"wsjgid": strconv.Itoa(wsjgID), // 卫生机构ID
	}

	// 如果提供了科室ID，则添加
	if jgksID > 0 {
		queryParams["jgksid"] = strconv.Itoa(jgksID)
	}

	// 构建API路径
	path := "/api/v1/consumer-ylfw/ylfw/yspb/ys/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("医生排班查询请求失败: %w", err)
	}

	// 解析响应
	var schedules []DoctorSchedule
	if err := jsoniter_.Unmarshal(resp, &schedules); err != nil {
		return nil, fmt.Errorf("解析医生排班响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到医生排班列表: 日期=%s, 机构ID=%d, 科室ID=%d, 排班数量=%d",
		pbrq, wsjgID, jgksID, len(schedules))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取医生排班列表成功",
		Data: schedules,
	}

	return result, nil
}

// GetDoctorSchedules 当前机构科室下显示医生的接口(全局方法)
func GetDoctorSchedules(ctx context.Context, pbrq string, wsjgID, jgksID int) (*BaseResp, error) {
	return DefaultClient.GetDoctorSchedules(ctx, pbrq, wsjgID, jgksID)
}

// 9. 号源记录查询接口
// URL: /api/v1/consumer-ylfw/ylfw/yspb/hyjl/list
// 查询当前机构科室下医生的号源
func (c *Client) GetScheduleSources(ctx context.Context, pbrq, wsjgid, jgksid, ysid string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := make(map[string]string)

	// 如果提供了相关参数，则添加
	if pbrq != "" {
		queryParams["pbrq"] = pbrq
	}
	if wsjgid != "" {
		queryParams["wsjgid"] = wsjgid
	}
	if jgksid != "" {
		queryParams["jgksid"] = jgksid
	}
	if ysid != "" {
		queryParams["ysid"] = ysid
	}

	// 构建API路径
	path := "/api/v1/consumer-ylfw/ylfw/yspb/hyjl/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("号源记录查询请求失败: %w", err)
	}

	// 解析响应
	var sources []interface{}
	if err := json.Unmarshal(resp, &sources); err != nil {
		return nil, fmt.Errorf("解析号源记录响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到号源记录列表: 日期=%s, 机构ID=%s, 科室ID=%s, 医生ID=%s, 号源数量=%d",
		pbrq, wsjgid, jgksid, ysid, len(sources), sources)

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取号源记录列表成功",
		Data: resp,
	}

	return result, nil
}

// GetScheduleSources 获取号源记录(全局方法)
func GetScheduleSources(ctx context.Context, pbrq string, wsjgID, jgksID int, ysID string) (*BaseResp, error) {
	return DefaultClient.GetScheduleSources(ctx, pbrq, strconv.Itoa(wsjgID), strconv.Itoa(jgksID), ysID)
}

// 10. 门诊挂号查询接口
// URL: /api/v1/consumer-ylfw/ylfw/mzgh/list
// 10.查询挂号信息
func (c *Client) GetOutpatientRegistrations(ctx context.Context, wsjgID int, dateFrom, dateTo, xm, zjhm, zfbz string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"wsjgid": strconv.Itoa(wsjgID), // 卫生机构ID
	}

	// 添加可选参数
	if dateFrom != "" {
		queryParams["datefrom"] = dateFrom
	}
	if dateTo != "" {
		queryParams["dateto"] = dateTo
	}
	if xm != "" {
		queryParams["xm"] = xm
	}
	if zjhm != "" {
		queryParams["zjhm"] = zjhm
	}
	if zfbz != "" {
		queryParams["zfbz"] = zfbz
	}

	// 构建API路径
	path := "/api/v1/consumer-ylfw/ylfw/mzgh/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("门诊挂号查询请求失败: %w", err)
	}

	// 解析响应
	var registrations []OutpatientRegistration
	if err := jsoniter_.Unmarshal(resp, &registrations); err != nil {
		return nil, fmt.Errorf("解析门诊挂号响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到门诊挂号列表: 机构ID=%d, 开始时间=%s, 结束时间=%s, 挂号数量=%d",
		wsjgID, dateFrom, dateTo, len(registrations))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取门诊挂号列表成功",
		Data: registrations,
	}

	return result, nil
}

// GetOutpatientRegistrations 查询挂号信息(全局方法)
func GetOutpatientRegistrations(ctx context.Context, wsjgID int, dateFrom, dateTo, xm, zjhm, zfbz string) (*BaseResp, error) {
	return DefaultClient.GetOutpatientRegistrations(ctx, wsjgID, dateFrom, dateTo, xm, zjhm, zfbz)
}

// 11. 预约挂号查询接口
// URL: /api/v1/consumer-ylfw/ylfw/yygh/list
// 19.查询已预约号源列表
func (c *Client) GetAppointmentRegistrations(ctx context.Context, wsjgID int, dateFrom, dateTo, zjhm string, hyzt int) (*BaseResp, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"wsjgid": strconv.Itoa(wsjgID), // 卫生机构ID
	}

	// 添加可选参数
	if dateFrom != "" {
		queryParams["datefrom"] = dateFrom
	}
	if dateTo != "" {
		queryParams["dateto"] = dateTo
	}
	if zjhm != "" {
		queryParams["zjhm"] = zjhm
	}
	if hyzt >= 0 {
		queryParams["hyzt"] = strconv.Itoa(hyzt)
	}

	// 构建API路径
	path := "/api/v1/consumer-ylfw/ylfw/yygh/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("预约挂号查询请求失败: %w", err)
	}

	// 解析响应
	var registrations []AppointmentRegistration
	if err := jsoniter_.Unmarshal(resp, &registrations); err != nil {
		return nil, fmt.Errorf("解析预约挂号响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到预约挂号列表: 机构ID=%d, 开始时间=%s, 结束时间=%s, 挂号数量=%d",
		wsjgID, dateFrom, dateTo, len(registrations))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取预约挂号列表成功",
		Data: registrations,
	}

	return result, nil
}

// GetAppointmentRegistrations 查询已预约号源列表(全局方法)
func GetAppointmentRegistrations(ctx context.Context, wsjgID int, dateFrom, dateTo, zjhm string, hyzt int) (*BaseResp, error) {
	return DefaultClient.GetAppointmentRegistrations(ctx, wsjgID, dateFrom, dateTo, zjhm, hyzt)
}

// 12. 创建预约挂号接口
// URL: /api/v1/consumer-ylfw/ylfw/yygh
// 20.预约挂号
// AppointmentRequest 预约挂号请求参数
type AppointmentRequest struct {
	HyjlID int    `json:"hyjlid"` // 号源记录id
	GrxxID int    `json:"grxxid"` // 个人信息id
	YyLx   int    `json:"yylx"`   // 预约类型 1:现场预约 2:自助机预约 3:电话预约 4:网上预约
	BzXx   string `json:"bzxx"`   // 备注信息
	CzrID  int    `json:"czrid"`  // 操作人ID
	Czr    string `json:"czr"`    // 操作人名称
	YsID   string `json:"ysid"`   // 医生ID
	YsMC   string `json:"ysmc"`   // 医生名称
	JgksID int    `json:"jgksid"` // 机构科室ID
	JgksMC string `json:"jgksmc"` // 机构科室名称
	WsjgID int    `json:"wsjgid"` // 卫生机构ID
	WsjgMC string `json:"wsjgmc"` // 卫生机构名称
}

// CreateAppointment 预约挂号
func (c *Client) CreateAppointment(ctx context.Context, req *AppointmentRequest) (*BaseResp, error) {
	// 将请求参数转换为JSON
	reqBody, err := jsoniter_.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化预约挂号请求失败: %w", err)
	}

	// 构建API路径
	path := "/api/v1/consumer-ylfw/ylfw/yygh"

	// 创建请求对象
	fullURL := fmt.Sprintf("%s%s", c.config.BaseURL, path)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", fullURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建预约挂号请求失败: %w", err)
	}

	// 获取token
	token, err := c.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json;charset=UTF-8")
	httpReq.Header.Set("Authorization", "Bearer "+token)

	// 添加HIS系统特定的认证头
	if c.config.CurrentJsdj != "" {
		httpReq.Header.Set("currentJsdj", c.config.CurrentJsdj)
	}
	if c.config.CurrentJtid != "" {
		httpReq.Header.Set("currentJtid", c.config.CurrentJtid)
	}
	if c.config.CurrentWsjgid != "" {
		httpReq.Header.Set("currentWsjgid", c.config.CurrentWsjgid)
	}
	if c.config.CurrentYhjsid != "" {
		httpReq.Header.Set("currentYhjsid", c.config.CurrentYhjsid)
	}

	// 发送请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(httpReq)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 发送预约挂号请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("发送预约挂号请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 读取预约挂号响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("读取预约挂号响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[HangzhouHIS] 收到预约挂号响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)
	logx.Infof("[HangzhouHIS] 响应内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[HangzhouHIS] 预约挂号请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return nil, fmt.Errorf("预约挂号请求失败: %s", string(body))
	}

	// 解析响应
	var result struct {
		Yyghid int    `json:"yyghid"` // 预约挂号ID
		Hyzt   string `json:"hyzt"`   // 号源状态
	}
	if err := jsoniter_.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析预约挂号响应失败: %w", err)
	}

	// 构建返回结果
	baseResp := &BaseResp{
		Code: 200,
		Msg:  "预约挂号成功",
		Data: result,
	}

	return baseResp, nil
}

// CreateAppointment 预约挂号(全局方法)
func CreateAppointment(ctx context.Context, req *AppointmentRequest) (*BaseResp, error) {
	return DefaultClient.CreateAppointment(ctx, req)
}

// 13. 取消预约挂号接口
// URL: /api/v1/consumer-ylfw/ylfw/yygh/{yyghid}
// 21.取消预约
// CancelAppointmentRequest 取消预约挂号请求参数
type CancelAppointmentRequest struct {
	YyghID int    `json:"yyghid"` // 预约挂号ID
	GrxxID int    `json:"grxxid"` // 个人信息ID
	HyjlID int    `json:"hyjlid"` // 号源记录ID
	YyLx   int    `json:"yylx"`   // 预约类型
	ZfrID  int    `json:"zfrid"`  // 作废人ID
	ZfrMC  string `json:"zfrmc"`  // 作废人名称
	ZfYy   string `json:"zfyy"`   // 作废原因
}

// CancelAppointment 取消预约
func (c *Client) CancelAppointment(ctx context.Context, req *CancelAppointmentRequest) (*BaseResp, error) {
	// 将请求参数转换为JSON
	reqBody, err := jsoniter_.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化取消预约请求失败: %w", err)
	}

	// 构建API路径
	path := fmt.Sprintf("/api/v1/consumer-ylfw/ylfw/yygh/%d", req.YyghID)

	// 创建请求对象
	fullURL := fmt.Sprintf("%s%s", c.config.BaseURL, path)
	httpReq, err := http.NewRequestWithContext(ctx, "DELETE", fullURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建取消预约请求失败: %w", err)
	}

	// 获取token
	token, err := c.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json;charset=UTF-8")
	httpReq.Header.Set("Authorization", "Bearer "+token)

	// 添加HIS系统特定的认证头
	c.SetHISAuthHeaders(httpReq)

	// 发送请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(httpReq)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 发送取消预约请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("发送取消预约请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 读取取消预约响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("读取取消预约响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[HangzhouHIS] 收到取消预约响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)
	logx.Infof("[HangzhouHIS] 响应内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[HangzhouHIS] 取消预约请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return nil, fmt.Errorf("取消预约请求失败: %s", string(body))
	}

	// 构建返回结果
	baseResp := &BaseResp{
		Code: 200,
		Msg:  "取消预约成功",
		Data: nil,
	}

	return baseResp, nil
}

// CancelAppointment 取消预约(全局方法)
func CancelAppointment(ctx context.Context, req *CancelAppointmentRequest) (*BaseResp, error) {
	return DefaultClient.CancelAppointment(ctx, req)
}

// 14. 查询个人信息接口
// URL: /api/v1/consumer-jcfw/jcfw/grxx/list
// 查询患者信息
func (c *Client) GetPersonalInfos(ctx context.Context, xm, zfbz string, wsjgid int) (*BaseResp, error) {
	// 构建查询参数
	queryParams := make(map[string]string)

	// 添加可选参数
	if xm != "" {
		queryParams["xm"] = xm
	}
	if zfbz != "" {
		queryParams["zfbz"] = zfbz
	}

	if wsjgid != 0 {
		queryParams["wsjgid"] = strconv.Itoa(wsjgid)
	}

	// 构建API路径
	path := "/api/v1/consumer-jcfw/jcfw/grxx/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("个人信息查询请求失败: %w", err)
	}

	// 解析响应
	var infos []PersonalInfo
	if err := jsoniter_.Unmarshal(resp, &infos); err != nil {
		return nil, fmt.Errorf("解析个人信息响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到个人信息列表: 姓名=%s, 数量=%d", xm, len(infos))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取个人信息列表成功",
		Data: infos,
	}

	return result, nil
}

// GetPersonalInfos 查询患者信息(全局方法)
func GetPersonalInfos(ctx context.Context, xm, zfbz string, wsjgid int) (*BaseResp, error) {
	return DefaultClient.GetPersonalInfos(ctx, xm, zfbz, wsjgid)
}

// 15. 创建个人信息接口
// URL: /api/v1/consumer-jcfw/jcfw/grxx
// 患者信息新增
func (c *Client) CreatePersonalInfo(ctx context.Context, info *PersonalInfo) (*BaseResp, error) {
	// 将请求参数转换为JSON
	reqBody, err := jsoniter_.Marshal(info)
	if err != nil {
		return nil, fmt.Errorf("序列化个人信息请求失败: %w", err)
	}

	// 构建API路径
	path := "/api/v1/consumer-jcfw/jcfw/grxx"

	// 创建请求对象
	fullURL := fmt.Sprintf("%s%s", c.config.BaseURL, path)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", fullURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建个人信息请求失败: %w", err)
	}

	// 获取token
	token, err := c.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json;charset=UTF-8")
	httpReq.Header.Set("Authorization", "Bearer "+token)

	// 添加HIS系统特定的认证头
	c.SetHISAuthHeaders(httpReq)

	// 发送请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(httpReq)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 发送创建个人信息请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("发送创建个人信息请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 读取创建个人信息响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("读取创建个人信息响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[HangzhouHIS] 收到创建个人信息响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)
	logx.Infof("[HangzhouHIS] 响应内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[HangzhouHIS] 创建个人信息请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return nil, fmt.Errorf("创建个人信息请求失败: %s", string(body))
	}

	// 解析响应
	var newInfo PersonalInfo
	if err := jsoniter_.Unmarshal(body, &newInfo); err != nil {
		return nil, fmt.Errorf("解析创建个人信息响应失败: %w", err)
	}

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "创建个人信息成功",
		Data: newInfo,
	}

	return result, nil
}

// CreatePersonalInfo 患者信息新增(全局方法)
func CreatePersonalInfo(ctx context.Context, info *PersonalInfo) (*BaseResp, error) {
	return DefaultClient.CreatePersonalInfo(ctx, info)
}

// 16. 诊断记录查询接口
// URL: /api/v1/consumer-ylfw/ylfw/zdjl/list
// 查询诊断信息
func (c *Client) GetDiagnosisRecords(ctx context.Context, wsjgID int, datefrom, dateto, xm, zjhm string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"wsjgid": strconv.Itoa(wsjgID), // 卫生机构ID
	}

	// 添加可选参数
	if datefrom != "" {
		queryParams["datefrom"] = datefrom
	}
	if dateto != "" {
		queryParams["dateto"] = dateto
	}
	if xm != "" {
		queryParams["xm"] = xm
	}
	if zjhm != "" {
		queryParams["zjhm"] = zjhm
	}

	// 构建API路径
	path := "/api/v1/consumer-ylfw/ylfw/zdjl/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("诊断记录查询请求失败: %w", err)
	}

	// 解析响应
	var records []DiagnosisRecord
	if err := jsoniter_.Unmarshal(resp, &records); err != nil {
		return nil, fmt.Errorf("解析诊断记录响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到诊断记录列表: 机构ID=%d, 开始时间=%s, 结束时间=%s, 数量=%d",
		wsjgID, datefrom, dateto, len(records))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取诊断记录列表成功",
		Data: records,
	}

	return result, nil
}

// GetDiagnosisRecords 查询诊断信息(全局方法)
func GetDiagnosisRecords(ctx context.Context, wsjgID int, datefrom, dateto, xm, zjhm string) (*BaseResp, error) {
	return DefaultClient.GetDiagnosisRecords(ctx, wsjgID, datefrom, dateto, xm, zjhm)
}

// 17. 科室查询接口（支持排班科室筛选）
// URL: /api/v1/consumer-jcfw/jcfw/jgks/list?pbksbz=1
// 16.当前可预约机构科室接口
func (c *Client) GetDepartmentsWithSchedule(ctx context.Context, wsjgID int, pbksbz string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"wsjgid": strconv.Itoa(wsjgID), // 卫生机构ID
		"pbksbz": pbksbz,               // 排班科室标志 1:是排班科室
	}

	// 构建API路径
	path := "/api/v1/consumer-jcfw/jcfw/jgks/list"

	// 发送请求
	resp, err := c.Request(ctx, path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("科室查询请求失败: %w", err)
	}

	// 解析响应
	var departments []Department
	if err := jsoniter_.Unmarshal(resp, &departments); err != nil {
		return nil, fmt.Errorf("解析科室响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到可排班科室列表: 机构ID=%d, 排班科室标志=%s, 科室数量=%d",
		wsjgID, pbksbz, len(departments))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取可排班科室列表成功",
		Data: departments,
	}

	return result, nil
}

// GetDepartmentsWithSchedule 当前可预约机构科室接口(全局方法)
func GetDepartmentsWithSchedule(ctx context.Context, wsjgID int, pbksbz string) (*BaseResp, error) {
	return DefaultClient.GetDepartmentsWithSchedule(ctx, wsjgID, pbksbz)
}

// 查询门诊收费信息接口
// URL: /api/v1/consumer-ylfw/ylfw/mzsf/list
func (c *Client) GetOutpatientChargeInfo(ctx context.Context, datefrom, dateto string) (*BaseResp, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"wsjgid": "5424", // 卫生机构ID - 杭州余杭叶开泰综合门诊部
	}

	// 添加查询参数
	if datefrom != "" {
		queryParams["datefrom"] = datefrom
	}
	if dateto != "" {
		queryParams["dateto"] = dateto
	}

	// 构建API路径
	path := "/api/v1/consumer-ylfw/ylfw/mzsf/list"

	// 构建完整URL，包含查询参数
	fullURL := fmt.Sprintf("%s%s", c.config.BaseURL, path)
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建门诊收费信息查询请求失败: %w", err)
	}

	// 获取token
	token, err := c.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取访问令牌失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	req.Header.Set("Authorization", "Bearer "+token)

	// 添加HIS系统特定的认证头
	c.SetHISAuthHeaders(req)

	// 添加查询参数
	q := req.URL.Query()
	for key, value := range queryParams {
		q.Add(key, value)
	}
	req.URL.RawQuery = q.Encode()

	// 发送请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(req)
	requestDuration := time.Since(startTime)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 发送门诊收费信息查询请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("发送门诊收费信息查询请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[HangzhouHIS] 读取门诊收费信息响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("读取门诊收费信息响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[HangzhouHIS] 收到门诊收费信息响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[HangzhouHIS] 门诊收费信息查询请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return nil, fmt.Errorf("门诊收费信息查询请求失败: %s", string(body))
	}

	// API返回的数据可能是字符串形式的数值，例如"3"而非3，需要进行预处理
	var rawData interface{}
	if err := json.Unmarshal(body, &rawData); err != nil {
		return nil, fmt.Errorf("解析门诊收费信息响应失败: %w", err)
	}

	// 重新序列化为JSON，然后解析为正确的结构体
	processedJSON, err := json.Marshal(rawData)
	if err != nil {
		return nil, fmt.Errorf("处理门诊收费信息数据失败: %w", err)
	}

	// 解析处理后的响应
	var outpatientCharges []OutpatientCharge
	if err := json.Unmarshal(processedJSON, &outpatientCharges); err != nil {
		return nil, fmt.Errorf("解析门诊收费信息响应失败: %w", err)
	}

	// 记录日志
	logx.Infof("获取到门诊收费信息记录数: %d", len(outpatientCharges))

	// 构建返回结果
	result := &BaseResp{
		Code: 200,
		Msg:  "获取门诊收费信息成功",
		Data: outpatientCharges,
	}

	return result, nil
}

// GetOutpatientChargeInfo 获取门诊收费信息(全局方法)
func GetOutpatientChargeInfo(ctx context.Context, datefrom, dateto string) (*BaseResp, error) {
	return DefaultClient.GetOutpatientChargeInfo(ctx, datefrom, dateto)
}

// OutpatientCharge 门诊收费信息结构
type OutpatientCharge struct {
	// 基础信息
	PYM   string `json:"pym"`   // 拼音码
	WBM   string `json:"wbm"`   // 五笔码
	CzrID int    `json:"czrid"` // 操作人ID
	CZSJ  string `json:"czsj"`  // 操作时间
	XgrID int    `json:"xgrid"` // 修改人ID
	XgSJ  string `json:"xgsj"`  // 修改时间
	ZfrID int    `json:"zfrid"` // 作废人ID
	ZfSJ  string `json:"zfsj"`  // 作废时间
	ZfBZ  int    `json:"zfbz"`  // 作废标志
	ZfYY  string `json:"zfyy"`  // 作废原因
	BZ    string `json:"bz"`    // 备注
	CzrMC string `json:"czrmc"` // 操作人名称
	XgrMC string `json:"xgrmc"` // 修改人名称
	ZfrMC string `json:"zfrmc"` // 作废人名称

	// 患者信息
	BAH    string `json:"bah"`    // 病案号
	XM     string `json:"xm"`     // 患者姓名
	XbDM   string `json:"xbdm"`   // 性别代码
	XbMC   string `json:"xbmc"`   // 性别名称
	CSSJ   string `json:"cssj"`   // 出生时间
	NL     string `json:"nl"`     // 年龄
	XZZ    string `json:"xzz"`    // 现住址
	SJHM   string `json:"sjhm"`   // 手机号码
	ZJLx   string `json:"zjlx"`   // 证件类型
	ZJLxMC string `json:"zjlxmc"` // 证件类型名称
	ZJHM   string `json:"zjhm"`   // 证件号码

	// 门诊收费信息
	MzsfID  int    `json:"mzsfid"`  // 门诊收费ID
	WsjgID  int    `json:"wsjgid"`  // 卫生机构ID
	WsjgMC  string `json:"wsjgmc"`  // 卫生机构名称
	MzsfdjH string `json:"mzsfdjh"` // 门诊收费登记号
	MzghID  int    `json:"mzghid"`  // 门诊挂号ID
	MzghdjH string `json:"mzghdjh"` // 门诊挂号登记号
	MzblID  int    `json:"mzblid"`  // 门诊病历ID
	MzYZIDs string `json:"mzyzids"` // 门诊医嘱IDs
	MzhjID  int    `json:"mzhjid"`  // 门诊会诊ID
	GrxxID  int    `json:"grxxid"`  // 个人信息ID

	// 会员信息
	HyglID   int    `json:"hyglid"`   // 会员管理ID
	HyglDJID int    `json:"hygldjid"` // 会员管理单据ID
	HydjMC   string `json:"hydjmc"`   // 会员等级名称
	HyxfQDH  string `json:"hyxfqdh"`  // 会员消费渠道号
	KH       string `json:"kh"`       // 卡号
	KLX      string `json:"klx"`      // 卡类型

	// 费用信息
	FylbDM string `json:"fylbdm"` // 费用类别代码
	FylbMC string `json:"fylbmc"` // 费用类别名称

	// 科室医生信息
	JgksID int    `json:"jgksid"` // 机构科室ID
	JgksMC string `json:"jgksmc"` // 机构科室名称
	YsID   int    `json:"ysid"`   // 医生ID
	YsMC   string `json:"ysmc"`   // 医生名称

	// 金额信息
	YzJE  float64 `json:"yzje"`  // 应收金额
	ZJE   float64 `json:"zje"`   // 总金额
	YzfwF float64 `json:"yzfwf"` // 应收服务费
	ZfwF  float64 `json:"zfwf"`  // 总服务费
	TfzJE float64 `json:"tfzje"` // 退费总金额

	// 票据信息
	PJH   string `json:"pjh"`   // 票据号
	PJDM  string `json:"pjdm"`  // 票据代码
	PJLSH string `json:"pjlsh"` // 票据流水号
	PJLx  string `json:"pjlx"`  // 票据类型
	PJURL string `json:"pjurl"` // 票据URL
	KPRQ  string `json:"kprq"`  // 开票日期

	// 疾病信息
	JbDM string `json:"jbdm"` // 疾病代码
	JbMC string `json:"jbmc"` // 疾病名称

	// 医保信息
	YbmzlxDM string `json:"ybmzlxdm"` // 医保门诊类型代码
	YbysDM   string `json:"ybysdm"`   // 医保医生代码
	YbksDM   string `json:"ybksdm"`   // 医保科室代码
	YbtmbDM  string `json:"ybtmbdm"`  // 医保特慢病代码
	YbtmbMC  string `json:"ybtmbmc"`  // 医保特慢病名称

	// 体检信息
	TJBH string `json:"tjbh"` // 体检编号
	TJDH string `json:"tjdh"` // 体检单号

	// 门诊类型
	MzlxDM string `json:"mzlxdm"` // 门诊类型代码
	MzlxMC string `json:"mzlxmc"` // 门诊类型名称

	// 其他信息
	Count   int    `json:"count"`   // 计数
	CrmDH   string `json:"crmdh"`   // CRM电话
	QLX     int    `json:"qlx"`     // 渠道类型
	XmqsYXM string `json:"xmqsyxm"` // 项目清单医学名

	// 新增字段（根据API响应）
	MzJS   interface{}   `json:"mzjs"`   // 门诊结算信息
	MzsfMX []interface{} `json:"mzsfmx"` // 门诊收费明细
}

// 强制清除缓存
func (c *Client) ClearCache() {
	c.mu.Lock()
	c.config.AccessToken = ""
	c.tokenData = nil
	c.mu.Unlock()
}
