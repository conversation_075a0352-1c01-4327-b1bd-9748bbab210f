package hangzhou

import (
	"yekaitai/internal/config"

	"github.com/zeromicro/go-zero/core/logx"
)

// LoadConfig 从配置文件中加载杭州HIS系统配置
func LoadConfig(configMap map[string]interface{}) Config {
	config := Config{}

	if baseURL, ok := configMap["BaseURL"].(string); ok {
		config.BaseURL = baseURL
	}

	if port, ok := configMap["Port"].(int); ok {
		config.Port = port
	}

	if clientSecret, ok := configMap["ClientSecret"].(string); ok {
		config.ClientSecret = clientSecret
	}

	if userName, ok := configMap["UserName"].(string); ok {
		config.UserName = userName
	}

	if password, ok := configMap["Password"].(string); ok {
		config.Password = password
	}

	if currentJsdj, ok := configMap["CurrentJsdj"].(string); ok {
		config.CurrentJsdj = currentJsdj
	}

	if currentJtid, ok := configMap["CurrentJtid"].(string); ok {
		config.CurrentJtid = currentJtid
	}

	if currentWsjgid, ok := configMap["CurrentWsjgid"].(string); ok {
		config.CurrentWsjgid = currentWsjgid
	}

	if currentYhjsid, ok := configMap["CurrentYhjsid"].(string); ok {
		config.CurrentYhjsid = currentYhjsid
	}

	// 访问令牌需要通过接口获取，不从配置加载

	return config
}

// InitFromConfig 使用配置初始化HIS系统客户端
func InitFromConfig(configMap map[string]interface{}) *Client {
	// 加载配置
	config := LoadConfig(configMap)

	// 设置默认客户端
	DefaultClient = NewClient(config)

	logx.Infof("杭州HIS系统客户端已初始化: BaseURL=%s", config.BaseURL)

	return DefaultClient
}

// InitDefaultClient 使用配置初始化默认客户端
func InitDefaultClient(cfg *config.HangzhouConfig) error {
	if cfg == nil {
		return nil
	}

	// 创建客户端配置
	config := Config{
		BaseURL:       cfg.BaseURL,
		Port:          cfg.Port,
		ClientSecret:  cfg.ClientSecret,
		UserName:      cfg.UserName,
		Password:      cfg.Password,
		CurrentJsdj:   cfg.CurrentJsdj,
		CurrentJtid:   cfg.CurrentJtid,
		CurrentWsjgid: cfg.CurrentWsjgid,
		CurrentYhjsid: cfg.CurrentYhjsid,
	}

	// 设置默认客户端
	DefaultClient = NewClient(config)

	logx.Infof("杭州HIS系统客户端已初始化: BaseURL=%s", config.BaseURL)

	return nil
}
