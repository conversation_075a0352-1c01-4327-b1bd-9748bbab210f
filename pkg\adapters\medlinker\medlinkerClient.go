package medlinker

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"
)

// Config 医联配置
type Config struct {
	BaseURL        string `json:"baseURL"`
	AppID          string `json:"appID"`
	AppSecret      string `json:"appSecret"`
	ModelID        int    `json:"modelID"`
	DailyCallLimit int    `json:"dailyCallLimit"` // 每日调用次数限制，0表示无限制
}

// MedlinkerClient 是医联客户端（公开类型）
type MedlinkerClient = medlinkerClient

// AIService 医联AI服务
type AIService struct {
	Client *medlinkerClient
}

// medlinkerClient 医联客户端
type medlinkerClient struct {
	Config    Config
	token     string
	callCount *RedisDailyCallCounter
}

// DailyCallCounter 每日调用次数计数器（内存版本，保留用于测试）
type DailyCallCounter struct {
	mu    sync.RWMutex
	date  string // 格式: 2024-07-24
	count int
	limit int
}

// RedisDailyCallCounter 基于Redis的每日调用次数计数器
type RedisDailyCallCounter struct {
	redis     *redis.Client
	limit     int
	keyPrefix string // Redis key前缀，如 "medlinker:call_count:"
}

// NewDailyCallCounter 创建每日调用计数器
func NewDailyCallCounter(limit int) *DailyCallCounter {
	return &DailyCallCounter{
		date:  time.Now().Format("2006-01-02"),
		count: 0,
		limit: limit,
	}
}

// CanCall 检查是否可以调用
func (c *DailyCallCounter) CanCall() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 检查日期是否需要重置
	today := time.Now().Format("2006-01-02")
	if c.date != today {
		return true // 新的一天，可以调用
	}

	// 0表示无限制
	if c.limit == 0 {
		return true
	}

	return c.count < c.limit
}

// IncrementCall 增加调用次数
func (c *DailyCallCounter) IncrementCall() {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 检查日期是否需要重置
	today := time.Now().Format("2006-01-02")
	if c.date != today {
		c.date = today
		c.count = 0
	}

	c.count++
}

// GetCallInfo 获取调用信息
func (c *DailyCallCounter) GetCallInfo() (int, int, string) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 检查日期是否需要重置
	today := time.Now().Format("2006-01-02")
	if c.date != today {
		return 0, c.limit, today
	}

	return c.count, c.limit, c.date
}

// NewRedisDailyCallCounter 创建基于Redis的每日调用计数器
func NewRedisDailyCallCounter(redisClient *redis.Client, limit int) *RedisDailyCallCounter {
	return &RedisDailyCallCounter{
		redis:     redisClient,
		limit:     limit,
		keyPrefix: "medlinker:call_count:",
	}
}

// getRedisKey 获取Redis key
func (c *RedisDailyCallCounter) getRedisKey() string {
	today := time.Now().Format("2006-01-02")
	return c.keyPrefix + today
}

// CanCall 检查是否可以调用
func (c *RedisDailyCallCounter) CanCall() bool {
	// 0表示无限制
	if c.limit == 0 {
		return true
	}

	ctx := context.Background()
	key := c.getRedisKey()

	count, err := c.redis.Get(ctx, key).Int()
	if err != nil && err != redis.Nil {
		logx.Errorf("Redis获取调用次数失败: %v", err)
		return true // 出错时允许调用
	}

	return count < c.limit
}

// IncrementCall 增加调用次数
func (c *RedisDailyCallCounter) IncrementCall() {
	ctx := context.Background()
	key := c.getRedisKey()

	// 使用INCR原子操作增加计数
	count, err := c.redis.Incr(ctx, key).Result()
	if err != nil {
		logx.Errorf("Redis增加调用次数失败: %v", err)
		return
	}

	// 如果是第一次设置，设置过期时间为当天结束
	if count == 1 {
		now := time.Now()
		endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
		ttl := endOfDay.Sub(now)
		c.redis.Expire(ctx, key, ttl)
	}
}

// GetCallInfo 获取调用信息
func (c *RedisDailyCallCounter) GetCallInfo() (int, int, string) {
	ctx := context.Background()
	key := c.getRedisKey()
	today := time.Now().Format("2006-01-02")

	count, err := c.redis.Get(ctx, key).Int()
	if err != nil && err != redis.Nil {
		logx.Errorf("Redis获取调用信息失败: %v", err)
		return 0, c.limit, today
	}
	if err == redis.Nil {
		count = 0
	}

	return count, c.limit, today
}

// GetDailyCallInfo 获取客户端的每日调用信息
func (c *medlinkerClient) GetDailyCallInfo() (int, int, string) {
	if c.callCount == nil {
		return 0, 0, ""
	}
	return c.callCount.GetCallInfo()
}

// ResetDailyCallCount 重置每日调用次数（仅用于测试）
func (c *medlinkerClient) ResetDailyCallCount() {
	if c.callCount != nil {
		ctx := context.Background()
		key := c.callCount.getRedisKey()
		c.callCount.redis.Del(ctx, key)
	}
}

// GetToken 获取当前token
func (c *medlinkerClient) GetToken() string {
	return c.token
}

// LoginV2Response 医联登录返回数据
type LoginV2Response struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	MsgID     string `json:"msgId"`
	SessionID string `json:"sessionid"`
	Data      struct {
		UserID int    `json:"user_id"`
		Token  string `json:"token"`
	} `json:"data"`
}

// Message 表示聊天消息
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
	MsgType int    `json:"msgType,omitempty"` // 消息类型：1-普通文本，407-图片消息
}

// ChatV2Request 医联聊天请求
type ChatV2Request struct {
	ModelID        int       `json:"model_id"`
	Messages       []Message `json:"messages"`
	SessionID      string    `json:"sessionid"`
	ThirdSessionID string    `json:"third_sessionid,omitempty"`
	ChatVersion    string    `json:"chat_version"`
}

// NewMedlinkerClient 创建医联客户端
func NewMedlinkerClient(config Config) *medlinkerClient {
	return &medlinkerClient{
		Config:    config,
		callCount: nil, // 需要通过SetRedisClient设置
	}
}

// NewMedlinkerClientWithRedis 创建带Redis的医联客户端
func NewMedlinkerClientWithRedis(config Config, redisClient *redis.Client) *medlinkerClient {
	return &medlinkerClient{
		Config:    config,
		callCount: NewRedisDailyCallCounter(redisClient, config.DailyCallLimit),
	}
}

// SetRedisClient 设置Redis客户端（用于调用次数统计）
func (c *medlinkerClient) SetRedisClient(redisClient *redis.Client) {
	c.callCount = NewRedisDailyCallCounter(redisClient, c.Config.DailyCallLimit)
}

// NewAIService 创建医联AI服务
func NewAIService(client *medlinkerClient) *AIService {
	return &AIService{
		Client: client,
	}
}

// Login_V2 医联用户登录
func (c *medlinkerClient) Login_V2(phone string) error {
	logx.Infof("开始医联Login_V2请求: BaseURL=%s, AppID=%s, Phone=%s",
		c.Config.BaseURL, c.Config.AppID, phone)

	url := fmt.Sprintf("%s/api/med/login_v2", c.Config.BaseURL)
	logx.Infof("医联Login_V2完整URL: %s", url)

	// 准备请求数据
	reqData := map[string]interface{}{
		"phone":  phone,
		"app_id": c.Config.AppID,
	}

	// 将请求数据转换为JSON
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		logx.Errorf("医联Login_V2请求数据序列化失败: %v", err)
		return fmt.Errorf("请求数据序列化失败: %w", err)
	}

	logx.Infof("医联Login_V2请求体: %s", string(reqBody))

	// 使用HTTP客户端请求医联API
	httpClient := &http.Client{
		Timeout: 10 * time.Second,
	}

	logx.Info("发送医联Login_V2 HTTP请求...")
	resp, err := httpClient.Post(url, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		logx.Errorf("医联Login_V2请求失败: %v", err)
		return fmt.Errorf("请求医联服务器失败: %w", err)
	}
	defer resp.Body.Close()

	logx.Infof("收到医联Login_V2响应: 状态码=%d", resp.StatusCode)

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("读取医联Login_V2响应体失败: %v", err)
		return fmt.Errorf("读取响应失败: %w", err)
	}

	logx.Infof("医联Login_V2原始响应: %s", string(respBody))

	// 重置响应体用于解析
	resp.Body = io.NopCloser(bytes.NewBuffer(respBody))

	// 解析响应数据
	var result LoginV2Response
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.NewDecoder(resp.Body).Decode(&result); err != nil {
		logx.Errorf("解析医联Login_V2响应失败: %v", err)
		return fmt.Errorf("解析医联响应失败: %w", err)
	}

	// 检查返回错误码
	if result.Code != 200 {
		logx.Errorf("医联Login_V2返回错误: code=%d, msg=%s, 详细响应=%s",
			result.Code, result.Msg, string(respBody))
		return fmt.Errorf("医联服务错误: %s (错误码: %d)", result.Msg, result.Code)
	}

	// 检查token是否为空
	if result.Data.Token == "" {
		logx.Error("医联Login_V2返回了空token")
		return fmt.Errorf("医联返回了空token")
	}

	// 保存token
	c.token = result.Data.Token
	// 打印截断的token，避免完整泄露
	if len(c.token) > 10 {
		logx.Infof("医联Login_V2成功, 用户ID: %d, Token前10位: %s...",
			result.Data.UserID, c.token[:10])
	} else {
		logx.Infof("医联Login_V2成功, 用户ID: %d, Token: %s",
			result.Data.UserID, c.token)
	}

	return nil
}

// Login 保留旧方法名称用于兼容性
func (c *medlinkerClient) Login(phone string) error {
	return c.Login_V2(phone)
}

// Chat 发起医联会话请求(流式返回)
func (c *medlinkerClient) Chat(modelID int, content, sessionID string) (io.ReadCloser, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	// 检查每日调用次数限制
	if c.callCount != nil && !c.callCount.CanCall() {
		count, limit, date := c.callCount.GetCallInfo()
		return nil, fmt.Errorf("已达到每日调用次数限制: %d/%d (日期: %s)", count, limit, date)
	}

	url := fmt.Sprintf("%s/api/med/chat", c.Config.BaseURL)

	// 准备请求数据
	reqData := ChatV2Request{
		ModelID:     modelID,
		Messages:    []Message{{Role: "user", Content: content}},
		SessionID:   sessionID,
		ChatVersion: "v2.0.1",
	}

	// 将请求数据转换为JSON
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		logx.Errorf("医联Chat请求数据序列化失败: %v", err)
		return nil, fmt.Errorf("请求数据序列化失败: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		logx.Errorf("创建医联Chat请求失败: %v", err)
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头 - 更新为在header中使用token
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", c.token)

	// 使用HTTP客户端请求医联API，流式请求使用更长的超时时间
	httpClient := &http.Client{
		Timeout: 120 * time.Second, // 增加超时时间到2分钟
		Transport: &http.Transport{
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 20 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			IdleConnTimeout:       90 * time.Second,
			// 启用HTTP/2
			ForceAttemptHTTP2: true,
		},
	}

	// 添加重试逻辑
	maxRetries := 3
	var resp *http.Response
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			logx.Infof("尝试第%d次重试医联Chat请求: SessionID=%s", i+1, sessionID)
			time.Sleep(time.Duration(i) * time.Second) // 增加重试间隔
		}

		resp, err = httpClient.Do(req)
		if err == nil && resp.StatusCode == http.StatusOK {
			break // 请求成功，退出重试循环
		}

		lastErr = err
		if err != nil {
			logx.Errorf("医联Chat请求失败(尝试%d/%d): %v", i+1, maxRetries, err)
		} else if resp != nil {
			body, _ := io.ReadAll(resp.Body)
			resp.Body.Close()
			logx.Errorf("医联Chat请求返回非200状态码(尝试%d/%d): code=%d, body=%s",
				i+1, maxRetries, resp.StatusCode, string(body))
			lastErr = fmt.Errorf("医联服务返回错误状态码: %d", resp.StatusCode)
		}

		// 最后一次重试仍然失败，则返回错误
		if i == maxRetries-1 {
			if err != nil {
				return nil, fmt.Errorf("请求医联服务器失败(已重试%d次): %w", maxRetries, err)
			}
			return nil, fmt.Errorf("医联服务返回错误状态码(已重试%d次): %d", maxRetries, resp.StatusCode)
		}

		// 创建新的请求体用于重试
		req.Body = io.NopCloser(bytes.NewBuffer(reqBody))
	}

	if resp == nil {
		return nil, fmt.Errorf("无法连接到医联服务器: %v", lastErr)
	}

	// 调用成功，增加计数
	if c.callCount != nil {
		c.callCount.IncrementCall()
		count, limit, _ := c.callCount.GetCallInfo()
		logx.Infof("医联API调用成功，当前调用次数: %d/%d", count, limit)
	}

	// 返回响应体，由调用者负责关闭
	return resp.Body, nil
}

// ChatWithImages 发起医联会话请求(支持图片，流式返回)
func (c *medlinkerClient) ChatWithImages(modelID int, content string, images []string, sessionID string) (io.ReadCloser, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	// 检查每日调用次数限制
	if c.callCount != nil && !c.callCount.CanCall() {
		count, limit, date := c.callCount.GetCallInfo()
		return nil, fmt.Errorf("已达到每日调用次数限制: %d/%d (日期: %s)", count, limit, date)
	}

	url := fmt.Sprintf("%s/api/med/chat", c.Config.BaseURL)

	// 准备消息内容
	var messageContent string
	var msgType int

	if len(images) > 0 {
		// 有图片时，构建JSON格式的content
		imageData := map[string]interface{}{
			"content": content,
			"images":  images,
		}
		imageJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(imageData)
		if err != nil {
			return nil, fmt.Errorf("序列化图片数据失败: %w", err)
		}
		messageContent = string(imageJSON)
		msgType = 407 // 图片消息类型
	} else {
		// 无图片时，直接使用文本内容
		messageContent = content
		msgType = 1 // 普通文本消息
	}

	// 准备请求数据
	message := Message{
		Role:    "user",
		Content: messageContent,
		MsgType: msgType,
	}

	reqData := ChatV2Request{
		ModelID:     modelID,
		Messages:    []Message{message},
		SessionID:   sessionID,
		ChatVersion: "v2.0.1",
	}

	// 将请求数据转换为JSON
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		logx.Errorf("医联ChatWithImages请求数据序列化失败: %v", err)
		return nil, fmt.Errorf("请求数据序列化失败: %w", err)
	}

	logx.Infof("医联ChatWithImages请求: modelID=%d, msgType=%d, images=%d张, sessionID=%s",
		modelID, msgType, len(images), sessionID)

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		logx.Errorf("创建医联ChatWithImages请求失败: %v", err)
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", c.token)

	// 使用HTTP客户端请求医联API
	httpClient := &http.Client{
		Timeout: 120 * time.Second,
		Transport: &http.Transport{
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 20 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			IdleConnTimeout:       90 * time.Second,
			ForceAttemptHTTP2:     true,
		},
	}

	// 添加重试逻辑
	maxRetries := 3
	var resp *http.Response
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			logx.Infof("尝试第%d次重试医联ChatWithImages请求: SessionID=%s", i+1, sessionID)
			time.Sleep(time.Duration(i) * time.Second)
		}

		resp, err = httpClient.Do(req)
		if err == nil && resp.StatusCode == http.StatusOK {
			break
		}

		lastErr = err
		if err != nil {
			logx.Errorf("医联ChatWithImages请求失败(尝试%d/%d): %v", i+1, maxRetries, err)
		} else if resp != nil {
			body, _ := io.ReadAll(resp.Body)
			resp.Body.Close()
			logx.Errorf("医联ChatWithImages请求返回非200状态码(尝试%d/%d): code=%d, body=%s",
				i+1, maxRetries, resp.StatusCode, string(body))
			lastErr = fmt.Errorf("医联服务返回错误状态码: %d", resp.StatusCode)
		}
	}

	if lastErr != nil {
		return nil, fmt.Errorf("医联ChatWithImages请求失败: %w", lastErr)
	}

	// 调用成功，增加计数
	if c.callCount != nil {
		c.callCount.IncrementCall()
		count, limit, _ := c.callCount.GetCallInfo()
		logx.Infof("医联ChatWithImages调用成功，当前调用次数: %d/%d", count, limit)
	}

	logx.Infof("医联ChatWithImages请求成功: SessionID=%s", sessionID)
	return resp.Body, nil
}

// 全局默认客户端实例
var DefaultClient *medlinkerClient

// Init 初始化默认客户端
func Init(config Config) {
	DefaultClient = NewMedlinkerClient(config)
	logx.Infof("初始化医联默认客户端, BaseURL: %s, ModelID: %d", config.BaseURL, config.ModelID)
}

// 医联数据同步相关结构体

// HospitalData 医院数据
type HospitalData struct {
	HospitalID string `json:"hospital_id"` // 医院唯一ID
	Name       string `json:"name"`        // 医院名称
	Address    string `json:"address"`     // 医院地址
	Longitude  string `json:"longitude"`   // 医院经度
	Latitude   string `json:"latitude"`    // 医院纬度
}

// SectionData 科室数据
type SectionData struct {
	SectionID   string `json:"section_id"`  // 科室唯一ID
	Name        string `json:"name"`        // 科室名称
	Description string `json:"description"` // 科室介绍
}

// DoctorData 医生数据
type DoctorData struct {
	DoctorID          string   `json:"doctor_id"`           // 医生唯一ID
	Name              string   `json:"name"`                // 医生姓名
	Title             string   `json:"title"`               // 医生职称
	Gender            int      `json:"gender"`              // 性别，1为男，2为女，未知为0
	Birthday          string   `json:"birthday"`            // 出生日期
	PracticeStartDate string   `json:"practice_start_date"` // 执业开始日期
	Specialty         string   `json:"specialty"`           // 专长领域
	Bio               string   `json:"bio"`                 // 医生介绍
	Sections          []string `json:"sections"`            // 医生所属的科室ID数组
}

// ReceiveHospitalDataRequest 接收医院数据请求
type ReceiveHospitalDataRequest struct {
	Data struct {
		DataID       string        `json:"data_id"`       // 数据唯一ID
		HospitalData HospitalData  `json:"hospital_data"` // 医院信息
		SectionData  []SectionData `json:"section_data"`  // 科室信息
		DoctorData   []DoctorData  `json:"doctor_data"`   // 医生信息
	} `json:"data"`
}

// CreateHospitalRequest 创建医院请求
type CreateHospitalRequest struct {
	HospitalID string `json:"hospital_id"` // 医院唯一ID
	Name       string `json:"name"`        // 医院名称
	Address    string `json:"address"`     // 医院地址
	Longitude  string `json:"longitude"`   // 医院经度
	Latitude   string `json:"latitude"`    // 医院纬度
}

// UpdateHospitalRequest 更新医院请求
type UpdateHospitalRequest struct {
	HospitalID string `json:"hospital_id"`         // 医院唯一ID
	Name       string `json:"name,omitempty"`      // 医院名称
	Address    string `json:"address,omitempty"`   // 医院地址
	Longitude  string `json:"longitude,omitempty"` // 医院经度
	Latitude   string `json:"latitude,omitempty"`  // 医院纬度
}

// DeleteHospitalRequest 删除医院请求
type DeleteHospitalRequest struct {
	HospitalID string `json:"hospital_id"` // 医院ID
}

// CreateSectionRequest 创建科室请求
type CreateSectionRequest struct {
	HospitalID  string `json:"hospital_id"` // 医院唯一ID
	SectionID   string `json:"section_id"`  // 科室唯一ID
	Name        string `json:"name"`        // 科室名称
	Description string `json:"description"` // 科室介绍
}

// UpdateSectionRequest 更新科室请求
type UpdateSectionRequest struct {
	HospitalID  string `json:"hospital_id"`           // 医院唯一ID
	SectionID   string `json:"section_id"`            // 科室唯一ID
	Name        string `json:"name,omitempty"`        // 科室名称
	Description string `json:"description,omitempty"` // 科室介绍
}

// DeleteSectionRequest 删除科室请求
type DeleteSectionRequest struct {
	HospitalID string `json:"hospital_id"` // 医院唯一ID
	SectionID  string `json:"section_id"`  // 科室唯一ID
}

// CreateDoctorRequest 创建医生请求
type CreateDoctorRequest struct {
	HospitalID        string   `json:"hospital_id"`         // 医院唯一ID
	DoctorID          string   `json:"doctor_id"`           // 医生唯一ID
	Name              string   `json:"name"`                // 医生姓名
	Title             string   `json:"title"`               // 医生职称
	Gender            int      `json:"gender"`              // 性别，1为男，2为女，未知为0
	Birthday          string   `json:"birthday"`            // 出生日期
	PracticeStartDate string   `json:"practice_start_date"` // 执业开始日期
	Specialty         string   `json:"specialty"`           // 专长领域
	Bio               string   `json:"bio"`                 // 医生介绍
	Sections          []string `json:"sections"`            // 医生所属的科室ID数组
	DataID            string   `json:"data_id"`             // 数据ID
}

// UpdateDoctorRequest 更新医生请求
type UpdateDoctorRequest struct {
	HospitalID        string   `json:"hospital_id"`                   // 医院唯一ID
	DoctorID          string   `json:"doctor_id"`                     // 医生唯一ID
	Name              string   `json:"name,omitempty"`                // 医生姓名
	Title             string   `json:"title,omitempty"`               // 医生职称
	Gender            int      `json:"gender,omitempty"`              // 性别，1为男，2为女，未知为0
	Birthday          string   `json:"birthday,omitempty"`            // 出生日期
	PracticeStartDate string   `json:"practice_start_date,omitempty"` // 执业开始日期
	Specialty         string   `json:"specialty,omitempty"`           // 专长领域
	Bio               string   `json:"bio,omitempty"`                 // 医生介绍
	Sections          []string `json:"sections,omitempty"`            // 医生所属的科室ID数组
}

// DeleteDoctorRequest 删除医生请求
type DeleteDoctorRequest struct {
	HospitalID string `json:"hospital_id"` // 医院唯一ID
	DoctorID   string `json:"doctor_id"`   // 医生唯一ID
}

// MedlinkerResponse 医联通用响应
type MedlinkerResponse struct {
	Code      int         `json:"code"`
	Msg       string      `json:"msg"`
	Data      interface{} `json:"data"`
	MsgID     string      `json:"msgId"`
	SessionID string      `json:"sessionid"`
	Tag       string      `json:"tag"`
	Type      string      `json:"type"`
}

// 医联数据同步方法

// ReceiveHospitalData 接收医院全量数据
func (c *medlinkerClient) ReceiveHospitalData(req *ReceiveHospitalDataRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/receive_hospital_data", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联接收医院数据请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联接收医院数据响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// CreateHospital 创建医院
func (c *medlinkerClient) CreateHospital(req *CreateHospitalRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/create_hospital", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联创建医院请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联创建医院响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// UpdateHospital 更新医院
func (c *medlinkerClient) UpdateHospital(req *UpdateHospitalRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/update_hospital", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联更新医院请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联更新医院响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// DeleteHospital 删除医院
func (c *medlinkerClient) DeleteHospital(req *DeleteHospitalRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/delete_hospital", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联删除医院请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联删除医院响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// CreateSection 创建科室
func (c *medlinkerClient) CreateSection(req *CreateSectionRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/create_section", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联创建科室请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联创建科室响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// UpdateSection 更新科室
func (c *medlinkerClient) UpdateSection(req *UpdateSectionRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/update_section", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联更新科室请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联更新科室响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// DeleteSection 删除科室
func (c *medlinkerClient) DeleteSection(req *DeleteSectionRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/delete_section", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联删除科室请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联删除科室响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// CreateDoctor 创建医生
func (c *medlinkerClient) CreateDoctor(req *CreateDoctorRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/create_doctor", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联创建医生请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联创建医生响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// UpdateDoctor 更新医生
func (c *medlinkerClient) UpdateDoctor(req *UpdateDoctorRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/update_doctor", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联更新医生请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联更新医生响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}

// DeleteDoctor 删除医生
func (c *medlinkerClient) DeleteDoctor(req *DeleteDoctorRequest) (*MedlinkerResponse, error) {
	if c.token == "" {
		return nil, fmt.Errorf("未认证，请先调用Login_V2方法")
	}

	url := fmt.Sprintf("%s/api/yekaitai/delete_doctor", c.Config.BaseURL)

	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	logx.Infof("医联删除医生请求: %s", string(reqBody))

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", c.token)

	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result MedlinkerResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	logx.Infof("医联删除医生响应: code=%d, msg=%s", result.Code, result.Msg)
	return &result, nil
}
