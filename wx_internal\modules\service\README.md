# 服务模块

## 概述

服务模块为小程序端提供门诊服务套餐的购买、预约、核销等功能。支持多种服务类型，灵活的预约规则，完整的订单管理和自动化任务处理。

## 功能特性

### 核心功能
- 🛍️ **服务列表展示** - 支持标签筛选、距离排序、推荐排序
- 📋 **服务详情查看** - 完整的服务信息展示，包含门店、价格、预约规则等
- 💰 **服务结算购买** - 支持优惠券、积分抵扣，不支持会员等级折扣
- 📅 **预约管理** - 灵活的预约规则，支持时间段预约和全天预约
- 📦 **订单管理** - 完整的订单生命周期管理
- 🔄 **自动化任务** - 过期订单处理、自动退款等

### 业务规则
- **结算顺序**: 商品金额 → 优惠券抵扣 → 积分抵扣 → 最终金额
- **积分兑换**: 1积分 = 1元，支持部分抵扣
- **订单过期**: 15分钟未支付自动关闭
- **预约限制**: 只能修改1次，时间前5小时不可取消
- **自动退款**: 支持过期未使用自动退款

## 目录结构

```
wx_internal/modules/service/
├── handler/                    # 处理器
│   ├── service_handler.go     # 服务列表、详情处理器
│   ├── checkout_handler.go    # 结算处理器
│   ├── order_handler.go       # 订单管理处理器
│   └── appointment_handler.go # 预约管理处理器
├── model/                     # 数据模型
│   └── service_package.go     # 服务套餐、订单、预约模型
├── router/                    # 路由配置
│   └── service_router.go      # 服务模块路由
├── task/                      # 定时任务
│   └── service_task.go        # 服务相关定时任务
└── README.md                  # 模块说明文档
```

## 数据模型

### 服务套餐 (service_packages)
- 基本信息：名称、图片、标签、价格等
- 预约规则：是否需要预约、提前时间、时间类型等
- 购买限制：单次最大购买数、每人最大购买数等
- 退款规则：是否支持随时退、过期自动退等

### 服务订单 (service_orders)
- 订单信息：订单号、用户、服务、数量、金额等
- 支付信息：支付状态、支付方式、支付时间等
- 使用信息：剩余次数、已使用次数、有效期等
- 退款信息：退款金额、退款时间、退款原因等

### 服务预约 (service_appointments)
- 预约信息：订单、用户、服务、门店、日期时间等
- 状态管理：已预约、已取消、已完成、已过期
- 修改记录：修改次数、取消时间、完成时间等

## API 接口

### 服务相关
- `GET /api/wx/services` - 获取服务列表
- `GET /api/wx/services/detail` - 获取服务详情
- `GET /api/wx/services/my` - 获取我的服务列表

### 结算相关
- `POST /api/wx/services/checkout/preload` - 结算预加载
- `POST /api/wx/services/checkout/recalculate` - 重新计算
- `POST /api/wx/services/orders` - 创建订单

### 订单相关
- `GET /api/wx/services/orders/list` - 订单列表
- `GET /api/wx/services/orders/detail` - 订单详情
- `POST /api/wx/services/orders/cancel` - 取消订单

### 预约相关
- `GET /api/wx/services/appointments/availability` - 获取预约可用性
- `POST /api/wx/services/appointments` - 创建预约
- `PUT /api/wx/services/appointments` - 修改预约
- `DELETE /api/wx/services/appointments` - 取消预约

## 使用方法

### 1. 注册路由

在主路由文件中注册服务模块路由：

```go
import "yekaitai/wx_internal/modules/service/router"

// 注册服务模块路由
router.RegisterServiceRoutes(server, svcCtx)
```

### 2. 数据库迁移

运行数据库迁移创建相关表：

```go
import "yekaitai/cmd/bootstrap"

// 迁移服务模块表
bootstrap.MigrateServiceTables()
```

### 3. 定时任务配置

在 main.go 中配置定时任务：

```go
import "yekaitai/wx_internal/modules/service/task"

// 配置定时任务
c := cron.New()
c.AddFunc("0 */5 * * * *", task.RunExpiredOrdersTask)      // 每5分钟处理过期订单
c.AddFunc("0 0 */1 * * *", task.RunExpiredAppointmentsTask) // 每小时处理过期预约
c.AddFunc("0 0 2 * * *", task.RunAutoRefundTask)           // 每天凌晨2点处理自动退款
c.Start()
```

## 配置说明

### 支付配置
- 订单号前缀：`SRV`（用于支付回调路由识别）
- 支付超时：15分钟
- 支持微信支付

### 积分配置
- 兑换比例：1积分 = 1元
- 从 `coin_global_config` 表获取配置

### 预约配置
- 修改次数限制：1次
- 取消时间限制：预约时间前5小时
- 容量管理：按门店和时间段控制

## 扩展说明

### 添加新的服务类型
1. 在 `service_packages` 表中添加相应字段
2. 更新 `ServicePackage` 模型
3. 修改相关处理器逻辑

### 自定义预约规则
1. 扩展 `appointment_rule` 字段的枚举值
2. 在 `validateAppointmentRules` 方法中添加验证逻辑
3. 更新前端预约界面

### 集成其他支付方式
1. 扩展 `payment` 包支持新的支付方式
2. 更新订单模型的支付方式字段
3. 修改支付回调处理逻辑

## 注意事项

1. **数据一致性**: 所有涉及金额计算的操作都使用事务处理
2. **并发控制**: 预约容量检查使用数据库锁防止超售
3. **错误处理**: 所有外部调用都有完善的错误处理和日志记录
4. **性能优化**: 列表查询使用分页，详情查询使用缓存
5. **安全考虑**: 所有用户操作都验证权限，防止越权访问

## 依赖项

- `gorm` - ORM框架
- `go-zero` - Web框架
- `mysql` - 数据库
- `payment` - 支付模块
- `cron` - 定时任务

## 版本历史

- v1.0.0 - 初始版本，包含基础的服务购买和预约功能
