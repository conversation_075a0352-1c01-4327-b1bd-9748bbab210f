package handler

import (
	"errors"
	"net/http"

	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// AlertRequest 告警信息请求
type AlertRequest struct {
	Title   string `json:"title"`   // 告警标题
	Content string `json:"content"` // 告警内容
}

// AbcYunPropertyChinaAddressHandler 获取中国地区信息
func AbcYunPropertyChinaAddressHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 调用ABC云API获取中国地区信息
	addressInfo, err := client.GetChinaAddressInfo()
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, addressInfo)
}

// AbcYunPropertyOssTokenHandler 获取OSS Token
func AbcYunPropertyOssTokenHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 调用ABC云API获取OSS Token
	ossToken, err := client.GetOssToken()
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, ossToken)
}

// AbcYunPropertyReportAlertHandler 发送报警
func AbcYunPropertyReportAlertHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 解析请求参数
	var req AlertRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 参数验证
	if req.Title == "" {
		httpx.Error(w, errors.New("缺少告警标题"))
		return
	}

	// 调用ABC云API发送报警
	result, err := client.ReportAlert(req.Title, req.Content)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	// 响应结果
	httpx.OkJson(w, result)
}
