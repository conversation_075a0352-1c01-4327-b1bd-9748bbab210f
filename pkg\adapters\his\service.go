package his

import (
	"context"
	"errors"
	"strconv"
	"sync"
	"time"

	"yekaitai/internal/modules/appointment/model"
	appointmentModel "yekaitai/internal/modules/appointment/model"

	"yekaitai/pkg/utils/constants"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"
)

var (
	ErrProviderNotFound      = errors.New("HIS提供商不存在")
	ErrClinicNotFound        = errors.New("诊所不存在")
	ErrDepartmentNotFound    = errors.New("科室不存在")
	ErrDoctorNotFound        = errors.New("医生不存在")
	ErrScheduleNotFound      = errors.New("排班不存在")
	ErrPatientNotFound       = errors.New("患者不存在")
	ErrAppointmentNotFound   = errors.New("预约不存在")
	ErrOperationNotSupported = errors.New("操作不支持")
	ErrNotFound              = errors.New("未找到")
)

// HISService HIS服务
type HISService struct {
	providers      map[ProviderType]HISProvider
	doctorRepo     interface{}
	departmentRepo appointmentModel.DepartmentRepository
	scheduleRepo   model.ScheduleTimeRepository

	appointRepo appointmentModel.AppointmentRepository
	lock        sync.RWMutex
}

// NewHISService 创建HIS服务
func NewHISService(
	doctorRepo interface{},
	departmentRepo model.DepartmentRepository,
	scheduleRepo model.ScheduleTimeRepository,

	appointRepo appointmentModel.AppointmentRepository,
) *HISService {
	return &HISService{
		providers:      make(map[ProviderType]HISProvider),
		doctorRepo:     doctorRepo,
		departmentRepo: departmentRepo,
		scheduleRepo:   scheduleRepo,

		appointRepo: appointRepo,
	}
}

// RegisterProvider 注册HIS提供商
func (s *HISService) RegisterProvider(provider HISProvider) {
	s.lock.Lock()
	defer s.lock.Unlock()

	providerType := provider.GetProviderType()
	s.providers[providerType] = provider
	logx.Info("注册HIS提供商", zap.String("provider", string(providerType)))
}

// GetProvider 获取HIS提供商
func (s *HISService) GetProvider(providerType ProviderType) (HISProvider, error) {
	s.lock.RLock()
	defer s.lock.RUnlock()

	provider, ok := s.providers[providerType]
	if !ok {
		return nil, ErrProviderNotFound
	}

	return provider, nil
}

// SyncDepartments 同步所有诊所的科室信息
func (s *HISService) SyncDepartments(ctx context.Context) error {
	s.lock.RLock()
	defer s.lock.RUnlock()

	for providerType, provider := range s.providers {
		// 获取诊所列表
		clinics, err := provider.GetClinicList(ctx)
		if err != nil {
			logx.Error("获取诊所列表失败",
				zap.String("provider", string(providerType)),
				zap.Error(err))
			continue
		}

		// 遍历诊所，同步科室
		for _, clinicID := range clinics {
			// 获取科室列表
			departments, err := provider.GetDepartments(ctx, clinicID)
			if err != nil {
				logx.Error("获取科室列表失败",
					zap.String("provider", string(providerType)),
					zap.String("clinicID", clinicID),
					zap.Error(err))
				continue
			}

			// 保存科室信息到数据库
			for _, dept := range departments {
				// 查询科室是否存在
				existDept, err := s.departmentRepo.FindByExternalID(dept.ExternalID)
				if err != nil && !errors.Is(err, ErrNotFound) {
					logx.Error("查询科室失败",
						zap.String("externalID", dept.ExternalID),
						zap.String("provider", string(providerType)),
						zap.Error(err))
					continue
				}

				// 转换为内部模型
				deptModel := s.convertToDepartmentModel(dept)
				deptModel.ClinicID = clinicID

				// 更新或创建科室
				if existDept != nil {
					deptModel.ID = existDept.ID
					err = s.departmentRepo.Update(deptModel)
				} else {
					err = s.departmentRepo.Create(deptModel)
				}

				if err != nil {
					logx.Error("保存科室失败",
						zap.String("name", dept.Name),
						zap.Error(err))
				}
			}
		}
	}

	return nil
}

// SyncDoctors 同步所有诊所的医生信息
func (s *HISService) SyncDoctors(ctx context.Context) error {
	s.lock.RLock()
	defer s.lock.RUnlock()

	for providerType, provider := range s.providers {
		// 获取诊所列表
		clinics, err := provider.GetClinicList(ctx)
		if err != nil {
			logx.Error("获取诊所列表失败",
				zap.String("provider", string(providerType)),
				zap.Error(err))
			continue
		}

		// 遍历诊所
		for _, clinicID := range clinics {
			// 获取科室列表
			departments, err := provider.GetDepartments(ctx, clinicID)
			if err != nil {
				logx.Error("获取科室列表失败",
					zap.String("provider", string(providerType)),
					zap.String("clinicID", clinicID),
					zap.Error(err))
				continue
			}

			// 遍历科室，同步医生
			for _, dept := range departments {
				// 获取医生列表
				doctors, err := provider.GetDoctors(ctx, clinicID, dept.ID, time.Now())
				if err != nil {
					logx.Error("获取医生列表失败",
						zap.String("provider", string(providerType)),
						zap.String("clinicID", clinicID),
						zap.String("departmentID", dept.ID),
						zap.Error(err))
					continue
				}

				// 保存医生信息到数据库
				for _, doc := range doctors {
					// 临时记录日志，表明已处理但未保存医生信息
					logx.Info("已处理但未保存医生信息",
						zap.String("name", doc.Name),
						zap.String("clinicID", clinicID))
				}
			}
		}
	}

	return nil
}

// SyncSchedules 同步所有诊所的排班信息
func (s *HISService) SyncSchedules(ctx context.Context, days int) error {
	s.lock.RLock()
	defer s.lock.RUnlock()

	// 计算日期范围
	startDate := time.Now()
	endDate := startDate.AddDate(0, 0, days)

	for providerType, provider := range s.providers {
		// 获取诊所列表
		clinics, err := provider.GetClinicList(ctx)
		if err != nil {
			logx.Error("获取诊所列表失败",
				zap.String("provider", string(providerType)),
				zap.Error(err))
			continue
		}

		// 遍历诊所
		for _, clinicID := range clinics {
			// 同步排班信息
			err = provider.SyncSchedules(ctx, clinicID, startDate, endDate)
			if err != nil {
				logx.Error("同步排班信息失败",
					zap.String("provider", string(providerType)),
					zap.String("clinicID", clinicID),
					zap.Error(err))
			}
		}
	}

	return nil
}

// SyncAppointments 同步所有诊所的预约信息
func (s *HISService) SyncAppointments(ctx context.Context, days int) error {
	s.lock.RLock()
	defer s.lock.RUnlock()

	// 计算日期范围
	startDate := time.Now().AddDate(0, 0, -days) // 同步过去几天的数据
	endDate := time.Now().AddDate(0, 0, days)    // 同步未来几天的数据

	for providerType, provider := range s.providers {
		// 获取诊所列表
		clinics, err := provider.GetClinicList(ctx)
		if err != nil {
			logx.Error("获取诊所列表失败",
				zap.String("provider", string(providerType)),
				zap.Error(err))
			continue
		}

		// 遍历诊所
		for _, clinicID := range clinics {
			// 同步预约信息
			err = provider.SyncAppointments(ctx, clinicID, startDate, endDate)
			if err != nil {
				logx.Error("同步预约信息失败",
					zap.String("provider", string(providerType)),
					zap.String("clinicID", clinicID),
					zap.Error(err))
			}
		}
	}

	return nil
}

// GetDepartments 获取科室列表
func (s *HISService) GetDepartments(ctx context.Context, providerType ProviderType, clinicID string) ([]DepartmentInfo, error) {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetDepartments(ctx, clinicID)
}

// GetDoctors 获取医生列表
func (s *HISService) GetDoctors(ctx context.Context, providerType ProviderType, clinicID, departmentID string, date time.Time) ([]DoctorInfo, error) {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetDoctors(ctx, clinicID, departmentID, date)
}

// GetSchedules 获取排班信息
func (s *HISService) GetSchedules(ctx context.Context, providerType ProviderType, clinicID, departmentID, doctorID string, date time.Time) ([]ScheduleInfo, error) {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetSchedules(ctx, clinicID, departmentID, doctorID, date)
}

// // CreatePatient 创建患者信息
// func (s *HISService) CreatePatient(ctx context.Context, providerType ProviderType, clinicID string, patient *PatientInfo) (*PatientInfo, error) {
// 	provider, err := s.GetProvider(providerType)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 创建患者
// 	createdPatient, err := provider.CreatePatient(ctx, clinicID, patient)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 保存到内部数据库
// 	patientModel := s.convertToPatientModel(*createdPatient)
// 	patientModel.ClinicID = clinicID

// 	err = s.patientRepo.Create(patientModel)
// 	if err != nil {
// 		logx.Error("保存患者信息失败",
// 			zap.String("name", patient.Name),
// 			zap.Error(err))
// 	}

// 	return createdPatient, nil
// }

// GetPatients 获取患者列表
func (s *HISService) GetPatients(ctx context.Context, providerType ProviderType, clinicID, nameQuery string) ([]PatientInfo, error) {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetPatients(ctx, clinicID, nameQuery)
}

// GetPatient 获取患者信息
func (s *HISService) GetPatient(ctx context.Context, providerType ProviderType, clinicID, patientID string) (*PatientInfo, error) {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetPatient(ctx, clinicID, patientID)
}

// CreateAppointment 创建预约
func (s *HISService) CreateAppointment(ctx context.Context, providerType ProviderType, clinicID string, appointment *AppointmentInfo) (*AppointmentInfo, error) {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return nil, err
	}

	// 创建预约
	createdAppointment, err := provider.CreateAppointment(ctx, clinicID, appointment)
	if err != nil {
		return nil, err
	}

	// 保存到内部数据库
	appointModel := s.convertToAppointmentModel(*createdAppointment)
	appointModel.ClinicID = clinicID

	err = s.appointRepo.CreateAppointment(appointModel)
	if err != nil {
		logx.Error("保存预约信息失败",
			zap.String("patientName", appointment.PatientName),
			zap.Error(err))
	}

	return createdAppointment, nil
}

// CancelAppointment 取消预约
func (s *HISService) CancelAppointment(ctx context.Context, providerType ProviderType, clinicID, appointmentID, reason string) error {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return err
	}

	// 取消预约
	err = provider.CancelAppointment(ctx, clinicID, appointmentID, reason)
	if err != nil {
		return err
	}

	// 更新内部数据库状态
	appoint, err := s.appointRepo.FindByExternalID(appointmentID)
	if err != nil {
		if errors.Is(err, ErrNotFound) {
			return nil // 内部不存在该预约，忽略
		}
		return err
	}

	// 更新状态为已取消
	appoint.Status = constants.AppointmentStatusCancelled // 已取消
	err = s.appointRepo.UpdateAppointmentStatus(appointmentID, constants.AppointmentStatusCancelled)
	if err != nil {
		logx.Error("更新预约状态失败",
			zap.String("appointmentID", appointmentID),
			zap.Error(err))
	}

	return nil
}

// GetAppointments 获取预约列表
func (s *HISService) GetAppointments(ctx context.Context, providerType ProviderType, clinicID, patientID string, startDate, endDate time.Time, status int) ([]AppointmentInfo, error) {
	provider, err := s.GetProvider(providerType)
	if err != nil {
		return nil, err
	}

	return provider.GetAppointments(ctx, clinicID, patientID, startDate, endDate, status)
}

// 转换科室信息为内部模型
func (s *HISService) convertToDepartmentModel(dept DepartmentInfo) *appointmentModel.Department {
	return &appointmentModel.Department{
		Name:        dept.Name,
		Description: dept.Description,
		Status:      dept.Status,
		ExternalID:  dept.ExternalID,
		ExternalKey: dept.ExternalKey,
	}
}

// 转换医生信息为内部模型
func (s *HISService) convertToDoctorModel(doc DoctorInfo) interface{} {
	return nil // 暂时返回nil，后续需要根据实际情况修改
}

// 转换患者信息为内部模型
// func (s *HISService) convertToPatientModel(patient PatientInfo) *userModel.Patient {
// 	birthday := patient.Birthday
// 	return &userModel.Patient{
// 		Name:        patient.Name,
// 		Gender:      1, // 默认值，根据实际情况可能需要转换
// 		Birthday:    &birthday,
// 		IDCardType:  1, // 默认身份证，根据实际情况可能需要转换
// 		IDCardNo:    patient.IDCardNo,
// 		Phone:       patient.Phone,
// 		Address:     patient.Address,
// 		ClinicID:    patient.ExternalKey, // 使用 ExternalKey 作为 ClinicID
// 		ExternalID:  patient.ExternalID,
// 		ExternalKey: patient.ExternalKey,
// 	}
// }

// 转换预约信息为内部模型
func (s *HISService) convertToAppointmentModel(appoint AppointmentInfo) *appointmentModel.Appointment {
	patientID, _ := strconv.ParseUint(appoint.PatientID, 10, 64)
	doctorID, _ := strconv.ParseUint(appoint.DoctorID, 10, 64)
	departmentID, _ := strconv.ParseUint(appoint.DepartmentID, 10, 64)

	return &appointmentModel.Appointment{
		PatientID:    uint(patientID),
		DoctorID:     uint(doctorID),
		DepartmentID: uint(departmentID),
		ClinicID:     appoint.ExternalKey, // 使用 ExternalKey 作为 ClinicID
		AppointTime:  appoint.AppointTime,
		RegisterFee:  appoint.RegisterFee,
		Status:       appoint.Status,
		ExternalID:   appoint.ExternalID,
		ExternalKey:  appoint.ExternalKey,
	}
}

// ScheduleSyncTask 定时同步任务
func (s *HISService) ScheduleSyncTask() {
	// 每天凌晨3点同步科室和医生信息
	go func() {
		for {
			now := time.Now()
			next := time.Date(now.Year(), now.Month(), now.Day()+1, 3, 0, 0, 0, now.Location())
			duration := next.Sub(now)
			time.Sleep(duration)

			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
			s.SyncDepartments(ctx)
			s.SyncDoctors(ctx)
			cancel()
		}
	}()

	// 每4小时同步一次排班信息，同步未来7天的排班
	go func() {
		for {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
			s.SyncSchedules(ctx, 7)
			cancel()
			time.Sleep(4 * time.Hour)
		}
	}()

	// 每小时同步一次预约信息，同步前后3天的预约
	go func() {
		for {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
			s.SyncAppointments(ctx, 3)
			cancel()
			time.Sleep(1 * time.Hour)
		}
	}()
}
