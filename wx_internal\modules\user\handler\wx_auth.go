package handler

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/infra/redis"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/user/model"
	"yekaitai/wx_internal/svc"
	"yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// WxLoginRequest 微信小程序登录请求
type WxLoginRequest struct {
	Code          string `json:"code"`                   // 微信登录凭证code
	EncryptedData string `json:"encryptedData,optional"` // 加密数据
	Iv            string `json:"iv,optional"`            // 加密算法的初始向量
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken"`
}

// LogoutRequest 登出请求
type LogoutRequest struct {
	// 可以为空，仅用于保持API一致性
}

// WxLoginHandler 处理微信小程序登录
func WxLoginHandler(svcCtx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req WxLoginRequest
		if err := httpx.ParseJsonBody(r, &req); err != nil {
			logx.Errorf("解析微信登录请求参数失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeInvalidParams,
				Message: "无效的请求参数",
				Data:    nil,
			})
			return
		}

		logx.Infof("微信登录请求: code=%s", req.Code)

		// 校验参数
		if req.Code == "" {
			logx.Error("微信登录失败: code为空")
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeInvalidParams,
				Message: "登录凭证code不能为空",
				Data:    nil,
			})
			return
		}

		// 调用微信接口获取openid和session_key
		var openid string
		var sessionKey string
		var unionid string

		// 如果是测试环境且使用特定的测试码，则使用默认openid
		if req.Code == "wx-login-code-test" {
			openid = "test_openid_123456"
			sessionKey = "test_session_key"
			logx.Info("使用测试OpenID登录")
		} else {
			// 调用微信API获取OpenID
			wxClient := svcCtx.WechatClient
			if wxClient == nil {
				logx.Error("微信客户端未初始化")
				httpx.WriteJson(w, http.StatusOK, types.Response{
					Code:    types.CodeWeChatError,
					Message: "微信服务未配置",
					Data:    nil,
				})
				return
			}

			wxResp, err := wxClient.Code2Session(req.Code)
			if err != nil {
				logx.Errorf("获取微信OpenID失败: %v", err)
				httpx.WriteJson(w, http.StatusOK, types.Response{
					Code:    types.CodeWeChatError,
					Message: "微信登录失败",
					Data:    nil,
				})
				return
			}

			// 检查响应
			if wxResp.ErrCode != 0 {
				logx.Errorf("微信登录API返回错误: %d %s", wxResp.ErrCode, wxResp.ErrMsg)

				var errMsg string
				switch wxResp.ErrCode {
				case 40029:
					errMsg = "登录凭证无效"
				case 45011:
					errMsg = "API调用频率超限"
				case 40226:
					errMsg = "高风险用户，登录被拦截"
				default:
					errMsg = "微信登录失败"
				}

				httpx.WriteJson(w, http.StatusOK, types.Response{
					Code:    types.CodeWeChatError,
					Message: errMsg,
					Data:    nil,
				})
				return
			}
			logx.Infof("微信登录返回值: %+v", wxResp)
			openid = wxResp.OpenID
			sessionKey = wxResp.SessionKey
			unionid = wxResp.UnionID
			logx.Infof("获取到的unionid: %s", unionid)

			// 记录获取到的session_key
			logx.Infof("获取sessionKey成功，长度: %d", len(sessionKey))
		}

		if openid == "" {
			logx.Error("微信登录失败: 获取openid为空")
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeWeChatError,
				Message: "微信登录失败",
				Data:    nil,
			})
			return
		}

		// 查找或创建微信用户
		db := mysql.Master()

		// 创建wx_user表（如果不存在）
		if err := db.AutoMigrate(&model.WxUser{}); err != nil {
			logx.Errorf("自动迁移wx_user表失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeInternalError,
				Message: "内部错误",
				Data:    nil,
			})
			return
		}

		var wxUser model.WxUser
		result := db.Where("open_id = ?", openid).First(&wxUser)

		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				// 新用户，创建用户记录
				wxUser = model.WxUser{
					OpenID:       openid,
					UnionID:      unionid,
					Nickname:     "用户" + openid[len(openid)-6:],
					Status:       1,          // 正常状态
					RegisterDate: time.Now(), // 设置注册日期为当前时间
				}
				if err := db.Create(&wxUser).Error; err != nil {
					logx.Errorf("创建微信用户失败: %v", err)
					httpx.WriteJson(w, http.StatusOK, types.Response{
						Code:    types.CodeInternalError,
						Message: "内部错误",
						Data:    nil,
					})
					return
				}
				logx.Infof("创建微信用户成功: UserID=%d, OpenID=%s", wxUser.UserID, wxUser.OpenID)
			} else {
				logx.Errorf("查询微信用户失败: %v", result.Error)
				httpx.WriteJson(w, http.StatusOK, types.Response{
					Code:    types.CodeInternalError,
					Message: "内部错误",
					Data:    nil,
				})
				return
			}
		} else {
			// 检查用户状态
			if wxUser.Status != 1 {
				logx.Errorf("微信用户状态异常: %d", wxUser.Status)
				httpx.WriteJson(w, http.StatusOK, types.Response{
					Code:    types.CodeUserForbidden,
					Message: "用户已被禁用",
					Data:    nil,
				})
				return
			}

			// 更新 UnionID（如果有新值且当前值为空）
			if unionid != "" && wxUser.UnionID == "" {
				err := db.Model(&wxUser).Update("union_id", unionid).Error
				if err != nil {
					logx.Errorf("更新UnionID失败: %v", err)
				} else {
					logx.Infof("更新UnionID成功: UserID=%d, UnionID=%s", wxUser.UserID, unionid)
				}
			} else if unionid == "" {
				logx.Infof("微信接口未返回UnionID，可能小程序未绑定开放平台账号: UserID=%d", wxUser.UserID)
			}
		}

		// 查询用户的所有身份
		var isPatient, isDoctor, isRedeemer bool
		var patientID, doctorID, redeemerID uint
		var roles []string

		// 检查患者身份（优先按user_id查询，如果没有再按手机号查询）
		var patient model.WxPatient
		if err := db.Where("user_id = ?", wxUser.UserID).First(&patient).Error; err == nil {
			isPatient = true
			patientID = patient.PatientID
			roles = append(roles, "patient")
		} else if wxUser.Mobile != "" {
			// 如果按user_id没找到，且有手机号，则按手机号查询
			if err := db.Where("mobile = ?", wxUser.Mobile).First(&patient).Error; err == nil {
				isPatient = true
				patientID = patient.PatientID
				roles = append(roles, "patient")
			}
		}

		// 检查医生身份（只有有手机号时才检查）
		var doctor model.WxDoctor
		if wxUser.Mobile != "" {
			if err := db.Where("mobile = ?", wxUser.Mobile).First(&doctor).Error; err == nil {
				isDoctor = true
				doctorID = doctor.DoctorID
				roles = append(roles, "doctor")
			}
		}

		// 检查核销用户身份（只有有手机号时才检查）
		var redeemer model.WxRedeemer
		if wxUser.Mobile != "" {
			if err := db.Where("mobile = ?", wxUser.Mobile).First(&redeemer).Error; err == nil {
				isRedeemer = true
				redeemerID = redeemer.RedeemerID
				roles = append(roles, "redeemer")
			}
		}

		// 如果用户没有任何角色，默认创建患者角色
		if !isPatient && !isDoctor && !isRedeemer {
			// 创建默认患者记录
			newPatient := model.WxPatient{
				UserID:    wxUser.UserID,
				Status:    1,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			if err := db.Create(&newPatient).Error; err != nil {
				logx.Errorf("创建默认患者记录失败: %v", err)
				httpx.WriteJson(w, http.StatusOK, types.Response{
					Code:    types.CodeInternalError,
					Message: "内部错误",
					Data:    nil,
				})
				return
			}
			isPatient = true
			patientID = newPatient.PatientID
			roles = append(roles, "patient")
		}

		// 生成小程序Token
		tokenInfo, err := middleware.GenerateWxToken(svcCtx, openid, roles)
		if err != nil {
			logx.Errorf("生成微信Token失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeInternalError,
				Message: "生成令牌失败",
				Data:    nil,
			})
			return
		}

		logx.Infof("微信登录成功: UserID=%d, OpenID=%s, Roles=%v", wxUser.UserID, wxUser.OpenID, roles)

		// 组装用户身份信息
		userRoles := map[string]interface{}{}
		if isPatient {
			userRoles["patient"] = map[string]interface{}{
				"patientId": patientID,
			}
		}
		if isDoctor {
			userRoles["doctor"] = map[string]interface{}{
				"doctorId": doctorID,
			}
		}
		if isRedeemer {
			userRoles["redeemer"] = map[string]interface{}{
				"redeemerId": redeemerID,
			}
		}

		// 返回登录结果，使用旧的 Response 类型
		httpx.WriteJson(w, http.StatusOK, types.Response{
			Code:    types.CodeSuccess,
			Message: "success",
			Data: map[string]interface{}{
				"token": map[string]interface{}{
					"accessToken":  tokenInfo.AccessToken,
					"refreshToken": tokenInfo.RefreshToken,
					"expiresAt":    tokenInfo.ExpiresAt,
				},
				"userInfo": map[string]interface{}{
					"userId":   wxUser.UserID,
					"nickname": wxUser.Nickname,
					"avatar":   wxUser.Avatar,
					"mobile":   wxUser.Mobile,
					"roles":    roles,
					"identity": userRoles,
				},
			},
		})
	}
}

// WxRefreshTokenHandler 处理微信令牌刷新
func WxRefreshTokenHandler(svcCtx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req RefreshTokenRequest
		if err := httpx.ParseJsonBody(r, &req); err != nil {
			logx.Errorf("解析刷新令牌请求参数失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeInvalidParams,
				Message: "无效的请求参数",
				Data:    nil,
			})
			return
		}

		// 刷新令牌
		tokenInfo, err := middleware.RefreshWxToken(svcCtx, req.RefreshToken)
		if err != nil {
			logx.Errorf("刷新微信Token失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.Response{
				Code:    types.CodeInvalidToken,
				Message: "无效的刷新令牌",
				Data:    nil,
			})
			return
		}

		// 返回刷新结果，保持与登录接口一致的格式
		httpx.WriteJson(w, http.StatusOK, types.Response{
			Code:    types.CodeSuccess,
			Message: "success",
			Data: map[string]interface{}{
				"token": map[string]interface{}{
					"accessToken":  tokenInfo.AccessToken,
					"refreshToken": tokenInfo.RefreshToken,
					"expiresAt":    tokenInfo.ExpiresAt,
				},
			},
		})
	}
}

// WxLogoutHandler 处理微信小程序用户登出请求
func WxLogoutHandler(svcCtx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var logoutReq LogoutRequest
		if err := httpx.Parse(r, &logoutReq); err != nil {
			httpx.Error(w, err)
			return
		}

		// 从请求头获取token
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			logx.Error("退出失败：未提供认证令牌")
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未提供认证令牌"))
			return
		}

		// 解析token
		var openid string

		// 解析token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 {
			logx.Error("退出失败：认证格式错误")
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
			return
		}

		tokenType := strings.ToLower(parts[0])
		tokenString := parts[1]

		// 解析token
		if tokenType == "minitoken" || tokenType == "bearer" {
			// 尝试解析为微信token
			claims, err := middleware.ParseWxToken(svcCtx, tokenString)
			if err != nil {
				logx.Errorf("退出失败：解析微信令牌失败 %s", err.Error())
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, fmt.Sprintf("解析令牌失败: %v", err)))
				return
			}

			// 成功解析
			openid = claims.OpenID
		} else {
			logx.Error("退出失败：不支持的认证类型")
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "不支持的认证类型"))
			return
		}

		// 删除刷新令牌
		redisCli := redis.GetClient()
		_ = redisCli.Del(context.Background(), "wx_refresh_token:"+openid).Err()

		// 返回成功
		logx.Infof("微信用户登出成功: %s", openid)
		httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse("登出成功"))
	}
}
