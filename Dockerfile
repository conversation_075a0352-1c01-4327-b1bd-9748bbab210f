FROM {{.DOCKER_REGISTRY}}/base/ubuntu:20.04

LABEL maintainer="<EMAIL>"

###############################################################################
#                                INSTALLATION
###############################################################################

# 环境变量设置
ENV APP_NAME {{.APP_NAME}}
ENV APP_ROOT /var/www
ENV APP_PATH $APP_ROOT/$APP_NAME
ENV CONFIG_PATH $APP_PATH/etc
ENV LOG_ROOT /var/log/medlinker
ENV LOG_PATH /var/log/medlinker/$APP_NAME
ENV CONFIG_CENTER_BASE_URL http://consul.infra.svc.cluster.local:8500

# 更新CA证书（确保TLS连接正常）
RUN apt-get update && apt-get install -y ca-certificates && apt-get clean && rm -rf /var/lib/apt/lists/*

# 创建配置目录
RUN mkdir -p $CONFIG_PATH
# 创建日志目录
RUN mkdir -p $LOG_PATH && chmod 777 -R $LOG_PATH

# 执行入口文件添加
ADD ./main $APP_PATH/
ADD scripts/start_admin.sh /bin/
ADD scripts/start_mini.sh /bin/
ADD ./etc/*.yaml ${APP_PATH}/etc/
RUN chmod +x /bin/start_admin.sh /bin/start_mini.sh

###############################################################################
#                                   START
###############################################################################

# 启动脚本说明：
# start_admin.sh  - 只启动后台管理服务（端口 8889）+ 定时任务
# start_mini.sh   - 只启动小程序服务（端口 8888）

# 默认启动后台管理服务
# 可以通过 docker run 时指定不同的启动脚本：
# docker run ... yekaitai-admin-api:latest start_admin.sh
# docker run ... yekaitai-admin-api:latest start_mini.sh

CMD ["start_admin.sh"]