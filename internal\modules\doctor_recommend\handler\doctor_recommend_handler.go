package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	doctorModel "yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 医生推荐列表请求
type DoctorRecommendListRequest struct {
	types.PageRequest
	DoctorName string `form:"doctorName,optional"` // 医生姓名
	Department string `form:"department,optional"` // 科室
	IsTop      *bool  `form:"isTop,optional"`      // 是否置顶
	Status     *int   `form:"status,optional"`     // 状态
}

// 创建医生推荐请求
type CreateDoctorRecommendRequest struct {
	DoctorId  uint `json:"doctorId"`           // 医生ID
	IsTop     bool `json:"isTop,optional"`     // 是否置顶
	SortOrder int  `json:"sortOrder,optional"` // 排序
	Status    int  `json:"status,optional"`    // 状态，默认1
}

// 更新医生推荐请求
type UpdateDoctorRecommendRequest struct {
	Id        uint `path:"id"`                 // 推荐ID
	DoctorId  uint `json:"doctorId"`           // 医生ID
	IsTop     bool `json:"isTop,optional"`     // 是否置顶
	SortOrder int  `json:"sortOrder,optional"` // 排序
	Status    int  `json:"status,optional"`    // 状态
}

// 设置置顶请求
type SetTopRequest struct {
	Id uint `path:"id"` // 推荐ID
}

// 批量删除请求
type BatchDeleteRequest struct {
	Ids []uint `json:"ids"` // 推荐ID列表
}

// 批量添加请求
type BatchCreateRequest struct {
	DoctorIds []uint `json:"doctorIds"` // 医生ID列表
	Status    int    `json:"status"`    // 状态，默认1
}

// 医生选择列表请求
type DoctorSelectListRequest struct {
	types.PageRequest
	StoreId    uint   `form:"storeId,optional"`    // 门店ID
	DoctorName string `form:"doctorName,optional"` // 医生姓名
	Department string `form:"department,optional"` // 科室
}

// DoctorRecommendHandler 医生推荐处理器
type DoctorRecommendHandler struct {
	svcCtx *svc.ServiceContext
}

// NewDoctorRecommendHandler 创建医生推荐处理器
func NewDoctorRecommendHandler(svcCtx *svc.ServiceContext) *DoctorRecommendHandler {
	return &DoctorRecommendHandler{
		svcCtx: svcCtx,
	}
}

// ListDoctorRecommends 获取医生推荐列表
func (h *DoctorRecommendHandler) ListDoctorRecommends(w http.ResponseWriter, r *http.Request) {
	var req DoctorRecommendListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 默认页码和每页数量
	page := req.PageRequest.Page
	if page <= 0 {
		page = 1
	}
	size := req.PageRequest.Size
	if size <= 0 {
		size = 10
	}

	logx.Infof("获取医生推荐列表: page=%d, size=%d, doctorName=%s, department=%s, isTop=%v, status=%v",
		page, size, req.DoctorName, req.Department, req.IsTop, req.Status)

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Slave())

	// 构建查询参数
	params := &doctorModel.DoctorRecommendQueryParams{
		Page:       page,
		PageSize:   size,
		DoctorName: req.DoctorName,
		Department: req.Department,
		IsTop:      req.IsTop,
		Status:     req.Status,
	}

	// 获取医生推荐列表
	list, total, err := doctorRecommendRepo.List(params)
	if err != nil {
		logx.Errorf("获取医生推荐列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取医生推荐列表失败"))
		return
	}

	// 转换为响应格式
	result := make([]map[string]interface{}, 0, len(list))
	for _, item := range list {
		result = append(result, map[string]interface{}{
			"id":         item.ID,
			"doctorId":   item.DoctorID,
			"doctorName": item.DoctorName,
			"department": item.Department,
			"hospital":   item.Hospital,
			"title":      item.Title,
			"specialty":  item.Specialty,
			"headImgUrl": item.HeadImgUrl,
			"isTop":      item.IsTop,
			"sortOrder":  item.SortOrder,
			"status":     item.Status,
			"creatorId":  item.CreatorID,
			"createdAt":  item.CreatedAt.Format("2006-01-02 15:04:05"),
			"updatedAt":  item.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	logx.Infof("获取医生推荐列表成功: 共%d条记录", len(result))

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取医生推荐列表成功"))
}

// CreateDoctorRecommend 创建医生推荐
func (h *DoctorRecommendHandler) CreateDoctorRecommend(w http.ResponseWriter, r *http.Request) {
	var req CreateDoctorRecommendRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		logx.Errorf("创建医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("创建医生推荐请求: doctorId=%d, isTop=%v", req.DoctorId, req.IsTop)

	// 参数验证
	if req.DoctorId == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "医生ID不能为空"))
		return
	}

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Master())

	// 检查推荐医生数量限制（最多10个）
	count, err := doctorRecommendRepo.CountRecommends()
	if err != nil {
		logx.Errorf("检查推荐医生数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "系统错误"))
		return
	}

	if count >= 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "首页最多推荐10位医生"))
		return
	}

	// 检查医生是否已被推荐
	if doctorRecommendRepo.IsRecommended(req.DoctorId) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该医生已在推荐列表中"))
		return
	}

	// 如果设置为置顶，需要先取消其他置顶
	if req.IsTop {
		topRecommend, _ := doctorRecommendRepo.GetTopRecommend()
		if topRecommend != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "只能设置一个置顶医生，请先取消当前置顶医生"))
			return
		}
	}

	// 获取当前管理员ID作为创建人
	var creatorID uint = 0
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if adminID, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			creatorID = uint(adminID)
		}
	}

	// 创建推荐记录
	recommend := &doctorModel.DoctorRecommend{
		DoctorID:  req.DoctorId,
		IsTop:     req.IsTop,
		SortOrder: req.SortOrder,
		Status:    req.Status,
		CreatorID: creatorID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if recommend.Status == 0 {
		recommend.Status = 1 // 默认启用
	}

	err = doctorRecommendRepo.Create(recommend)
	if err != nil {
		logx.Errorf("创建医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建失败"))
		return
	}

	logx.Infof("医生推荐创建成功: doctorId=%d", req.DoctorId)

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"id": recommend.ID,
	}, "创建成功"))
}

// UpdateDoctorRecommend 更新医生推荐
func (h *DoctorRecommendHandler) UpdateDoctorRecommend(w http.ResponseWriter, r *http.Request) {
	var req UpdateDoctorRecommendRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("更新医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("更新医生推荐请求: id=%d, doctorId=%d", req.Id, req.DoctorId)

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Master())

	// 检查推荐记录是否存在
	recommend, err := doctorRecommendRepo.FindByID(req.Id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "推荐记录不存在"))
		} else {
			logx.Errorf("查找医生推荐记录失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新失败"))
		}
		return
	}

	// 如果要设置为置顶，需要先检查是否已有置顶
	if req.IsTop && !recommend.IsTop {
		topRecommend, _ := doctorRecommendRepo.GetTopRecommend()
		if topRecommend != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "只能设置一个置顶医生，请先取消当前置顶医生"))
			return
		}
	}

	// 更新推荐记录
	recommend.DoctorID = req.DoctorId
	recommend.IsTop = req.IsTop
	recommend.SortOrder = req.SortOrder
	recommend.Status = req.Status
	recommend.UpdatedAt = time.Now()

	err = doctorRecommendRepo.Update(recommend)
	if err != nil {
		logx.Errorf("更新医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新失败"))
		return
	}

	logx.Infof("医生推荐更新成功: id=%d", req.Id)

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(nil, "更新成功"))
}

// DeleteDoctorRecommend 删除医生推荐
func (h *DoctorRecommendHandler) DeleteDoctorRecommend(w http.ResponseWriter, r *http.Request) {
	idStr := r.URL.Query().Get("id")
	if idStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "ID不能为空"))
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的ID"))
		return
	}

	logx.Infof("删除医生推荐请求: id=%d", id)

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Master())

	// 检查推荐记录是否存在
	_, err = doctorRecommendRepo.FindByID(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "推荐记录不存在"))
		} else {
			logx.Errorf("查找医生推荐记录失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除失败"))
		}
		return
	}

	// 删除推荐记录
	err = doctorRecommendRepo.Delete(uint(id))
	if err != nil {
		logx.Errorf("删除医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除失败"))
		return
	}

	logx.Infof("医生推荐删除成功: id=%d", id)

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除成功"))
}

// BatchDeleteDoctorRecommends 批量删除医生推荐
func (h *DoctorRecommendHandler) BatchDeleteDoctorRecommends(w http.ResponseWriter, r *http.Request) {
	var req BatchDeleteRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		logx.Errorf("批量删除医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	if len(req.Ids) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请选择要删除的记录"))
		return
	}

	logx.Infof("批量删除医生推荐请求: ids=%v", req.Ids)

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Master())

	// 批量删除推荐记录
	err := doctorRecommendRepo.BatchDelete(req.Ids)
	if err != nil {
		logx.Errorf("批量删除医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "批量删除失败"))
		return
	}

	logx.Infof("医生推荐批量删除成功: 删除%d条记录", len(req.Ids))

	// 记录操作日志
	logAdminOperation(r, "批量删除推荐医生", map[string]interface{}{
		"recommend_ids": req.Ids,
		"count":         len(req.Ids),
	})

	httpx.OkJson(w, types.NewSuccessResponse(nil, "批量删除成功"))
}

// BatchCreateDoctorRecommends 批量添加医生推荐
func (h *DoctorRecommendHandler) BatchCreateDoctorRecommends(w http.ResponseWriter, r *http.Request) {
	var req BatchCreateRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		logx.Errorf("批量添加医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	if len(req.DoctorIds) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请选择要添加的医生"))
		return
	}

	if len(req.DoctorIds) > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "一次最多添加10位医生"))
		return
	}

	logx.Infof("批量添加医生推荐请求: doctorIds=%v", req.DoctorIds)

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Master())

	// 检查推荐医生数量限制（最多10个）
	count, err := doctorRecommendRepo.CountRecommends()
	if err != nil {
		logx.Errorf("检查推荐医生数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "系统错误"))
		return
	}

	if count+int64(len(req.DoctorIds)) > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams,
			fmt.Sprintf("首页最多推荐10位医生，当前已有%d位，最多还能添加%d位", count, 10-count)))
		return
	}

	// 检查医生是否已被推荐
	var alreadyRecommended []uint
	for _, doctorID := range req.DoctorIds {
		if doctorRecommendRepo.IsRecommended(doctorID) {
			alreadyRecommended = append(alreadyRecommended, doctorID)
		}
	}

	if len(alreadyRecommended) > 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams,
			fmt.Sprintf("医生ID %v 已在推荐列表中", alreadyRecommended)))
		return
	}

	// 获取当前管理员ID作为创建人
	var creatorID uint = 0
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if adminID, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			creatorID = uint(adminID)
		}
	}

	// 构建推荐记录
	var recommends []*doctorModel.DoctorRecommend
	for _, doctorID := range req.DoctorIds {
		recommend := &doctorModel.DoctorRecommend{
			DoctorID:  doctorID,
			IsTop:     false, // 批量添加默认不置顶
			SortOrder: 0,
			Status:    req.Status,
			CreatorID: creatorID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		if recommend.Status == 0 {
			recommend.Status = 1 // 默认启用
		}

		recommends = append(recommends, recommend)
	}

	// 批量创建推荐记录
	err = doctorRecommendRepo.BatchCreate(recommends)
	if err != nil {
		logx.Errorf("批量创建医生推荐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "批量添加失败"))
		return
	}

	logx.Infof("医生推荐批量添加成功: 添加%d位医生", len(req.DoctorIds))

	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"count": len(req.DoctorIds),
	}, "批量添加成功"))
}

// SetTopDoctorRecommend 设置置顶
func (h *DoctorRecommendHandler) SetTopDoctorRecommend(w http.ResponseWriter, r *http.Request) {
	var req SetTopRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("设置置顶请求: id=%d", req.Id)

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Master())

	// 检查推荐记录是否存在
	recommend, err := doctorRecommendRepo.FindByID(req.Id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "推荐记录不存在"))
		} else {
			logx.Errorf("查找医生推荐记录失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "设置置顶失败"))
		}
		return
	}

	if recommend.IsTop {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该医生已经是置顶状态"))
		return
	}

	// 设置置顶（会自动取消其他置顶）
	err = doctorRecommendRepo.SetTop(req.Id)
	if err != nil {
		logx.Errorf("设置置顶失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "设置置顶失败"))
		return
	}

	logx.Infof("设置置顶成功: id=%d", req.Id)

	httpx.OkJson(w, types.NewSuccessResponse(nil, "置顶成功"))
}

// CancelTopDoctorRecommend 取消置顶
func (h *DoctorRecommendHandler) CancelTopDoctorRecommend(w http.ResponseWriter, r *http.Request) {
	var req SetTopRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("取消置顶请求: id=%d", req.Id)

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Master())

	// 检查推荐记录是否存在
	recommend, err := doctorRecommendRepo.FindByID(req.Id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "推荐记录不存在"))
		} else {
			logx.Errorf("查找医生推荐记录失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "取消置顶失败"))
		}
		return
	}

	if !recommend.IsTop {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "该医生不是置顶状态"))
		return
	}

	// 取消置顶
	err = doctorRecommendRepo.CancelTop(req.Id)
	if err != nil {
		logx.Errorf("取消置顶失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "取消置顶失败"))
		return
	}

	logx.Infof("取消置顶成功: id=%d", req.Id)

	httpx.OkJson(w, types.NewSuccessResponse(nil, "取消置顶成功"))
}

// ListDoctorSelect 获取医生选择列表
func (h *DoctorRecommendHandler) ListDoctorSelect(w http.ResponseWriter, r *http.Request) {
	var req DoctorSelectListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 默认页码和每页数量
	page := req.PageRequest.Page
	if page <= 0 {
		page = 1
	}
	size := req.PageRequest.Size
	if size <= 0 {
		size = 10
	}

	logx.Infof("获取医生选择列表: page=%d, size=%d, storeId=%d, doctorName=%s, department=%s",
		page, size, req.StoreId, req.DoctorName, req.Department)

	// 创建医生推荐仓库
	doctorRecommendRepo := doctorModel.NewDoctorRecommendRepository(mysql.Slave())

	// 构建查询参数
	params := &doctorModel.DoctorSelectQueryParams{
		Page:       page,
		PageSize:   size,
		StoreID:    req.StoreId,
		DoctorName: req.DoctorName,
		Department: req.Department,
	}

	// 获取医生选择列表
	list, total, err := doctorRecommendRepo.GetDoctorSelectList(params)
	if err != nil {
		logx.Errorf("获取医生选择列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取医生列表失败"))
		return
	}

	// 转换为响应格式
	result := make([]map[string]interface{}, 0, len(list))
	for _, item := range list {
		result = append(result, map[string]interface{}{
			"doctorId":      item.DoctorID,
			"name":          item.Name,
			"department":    item.Department,
			"hospital":      item.Hospital,
			"title":         item.Title,
			"specialty":     item.Specialty,
			"headImgUrl":    item.HeadImgUrl,
			"isRecommended": item.IsRecommended,
		})
	}

	logx.Infof("获取医生选择列表成功: 共%d条记录", len(result))

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取医生列表成功"))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func logAdminOperation(r *http.Request, operation string, details map[string]interface{}) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理")
}
