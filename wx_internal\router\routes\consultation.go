package routes

import (
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/consultation/handler"
	"yekaitai/wx_internal/queue"
	"yekaitai/wx_internal/svc"
)

// RegisterConsultationRoutes 注册咨询模块路由
func RegisterConsultationRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 创建Kafka生产者适配器
	kafkaProducer := queue.NewKafkaProducerAdapter()

	// 创建统一的健康咨询处理器（支持所有模型：健康咨询、预问诊、报告解读等）
	healthConsultHandler := handler.NewHealthConsultHandler(serverCtx, serverCtx.MedlinkerClient, kafkaProducer)

	// 注册统一的咨询初始化路由（支持所有模型）
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/consultation/init",
				Handler: wxAuthWrapper(healthConsultHandler.InitHealthConsult),
			},
			// 保留预问诊的兼容性路由
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/consultation/prediagnosis",
				Handler: wxAuthWrapper(healthConsultHandler.InitHealthConsult),
			},
		},
	)

	logx.Info("已注册咨询初始化路由（支持健康咨询、预问诊、报告解读等）")

	// WebSocket路由
	logx.Info("开始注册WebSocket路由...")

	// 创建WebSocket专用路由组
	wsGroup := []rest.Route{
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/consultation/ws",
			Handler: wxAuthWrapper(healthConsultHandler.HandleWebSocket),
		},
		// 保留预问诊的兼容性路由
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/consultation/ws/prediagnosis",
			Handler: wxAuthWrapper(healthConsultHandler.HandleWebSocket),
		},
	}

	// 注册WebSocket路由
	server.AddRoutes(wsGroup)

	logx.Info("已注册WebSocket路由: /api/wx/consultation/ws（统一接口）和 /api/wx/consultation/ws/prediagnosis（兼容性接口）")

	// 注册会话管理路由
	sessionRoutes := []rest.Route{
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/consultation/sessions",
			Handler: wxAuthWrapper(healthConsultHandler.GetSessionHistory),
		},
		{
			Method:  http.MethodGet,
			Path:    "/api/wx/consultation/sessions/:sessionid",
			Handler: wxAuthWrapper(healthConsultHandler.GetSessionDetail),
		},
	}

	server.AddRoutes(sessionRoutes)
	logx.Info("已注册会话管理路由")
}
