package bootstrap

import (
	"fmt"

	aiModel "yekaitai/internal/modules/ai_data_upload/model"
	"yekaitai/pkg/infra/mysql"
)

// MigrateAIKnowledgeBaseTables 执行AI知识库模块的表结构迁移
func MigrateAIKnowledgeBaseTables() error {
	fmt.Println("开始迁移AI知识库模块表结构...")

	db := mysql.Master()

	// 删除旧表（重新创建以移除不需要的字段）
	if err := db.Migrator().DropTable(&aiModel.AIKnowledgeBase{}); err != nil {
		fmt.Printf("删除旧AI知识库表失败: %v\n", err)
	} else {
		fmt.Println("删除旧AI知识库表成功")
	}

	// 设置表注释
	tableComment := `AI知识库文件表 - 智能客服知识库，仅支持单个知识库自动覆盖上传`

	// 迁移AI知识库表
	if err := db.Set("gorm:table_options", fmt.Sprintf("COMMENT='%s'", tableComment)).AutoMigrate(&aiModel.AIKnowledgeBase{}); err != nil {
		return fmt.Errorf("迁移AI知识库表失败: %v", err)
	}

	fmt.Println("AI知识库模块表结构迁移完成")
	return nil
}
