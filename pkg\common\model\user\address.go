package user

import (
	"database/sql"
	"time"
)

// Address 收货地址表
type Address struct {
	ID           uint         `json:"id" gorm:"primaryKey;autoIncrement;comment:地址ID"`
	UserID       uint         `json:"user_id" gorm:"not null;index;comment:用户ID"`
	Name         string       `json:"name" gorm:"type:varchar(50);not null;comment:收货人姓名"`
	Phone        string       `json:"phone" gorm:"type:varchar(20);not null;comment:手机号"`
	Avatar       string       `json:"avatar" gorm:"type:varchar(255);comment:收货人头像"`
	ProvinceCode string       `json:"province_code" gorm:"type:varchar(10);not null;comment:省份编码"`
	CityCode     string       `json:"city_code" gorm:"type:varchar(10);not null;comment:城市编码"`
	DistrictCode string       `json:"district_code" gorm:"type:varchar(10);not null;comment:区县编码"`
	Address      string       `json:"address" gorm:"type:varchar(255);not null;comment:详细地址"`
	Postcode     string       `json:"postcode" gorm:"type:varchar(10);comment:邮政编码"`
	IsDefault    int          `json:"is_default" gorm:"type:tinyint(1);default:0;comment:是否默认地址(0-否，1-是)"`
	CreatedAt    time.Time    `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt    time.Time    `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt    sql.NullTime `json:"-" gorm:"index;comment:删除时间"`

	// 关联字段
	User *WxUser `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// AddressWithRegionNames 带地区名称的地址响应结构
type AddressWithRegionNames struct {
	Address
	ProvinceName string `json:"province_name"` // 省份名称
	CityName     string `json:"city_name"`     // 城市名称
	DistrictName string `json:"district_name"` // 区县名称
}

// AddressQueryParams 地址查询参数
type AddressQueryParams struct {
	UserID    uint `form:"user_id"`
	IsDefault *int `form:"is_default"`
	Page      int  `form:"page"`
	PageSize  int  `form:"page_size"`
}

// AddressCreateRequest 创建地址请求
type AddressCreateRequest struct {
	Name         string `json:"name" validate:"required,max=50"`
	Phone        string `json:"phone" validate:"required,max=20"`
	Avatar       string `json:"avatar,optional" validate:"max=255"`
	ProvinceCode string `json:"province_code" validate:"required,max=10"`
	CityCode     string `json:"city_code" validate:"required,max=10"`
	DistrictCode string `json:"district_code" validate:"required,max=10"`
	Address      string `json:"address" validate:"required,max=255"`
	Postcode     string `json:"postcode,optional" validate:"max=10"`
	IsDefault    int    `json:"is_default,optional" validate:"oneof=0 1"`
}

// AddressUpdateRequest 更新地址请求
type AddressUpdateRequest struct {
	Name         string `json:"name" validate:"required,max=50"`
	Phone        string `json:"phone" validate:"required,max=20"`
	Avatar       string `json:"avatar,optional" validate:"max=255"`
	ProvinceCode string `json:"province_code" validate:"required,max=10"`
	CityCode     string `json:"city_code" validate:"required,max=10"`
	DistrictCode string `json:"district_code" validate:"required,max=10"`
	Address      string `json:"address" validate:"required,max=255"`
	Postcode     string `json:"postcode,optional" validate:"max=10"`
	IsDefault    int    `json:"is_default,optional" validate:"oneof=0 1"`
}

// TableName 表名
func (Address) TableName() string {
	return "addresses"
}

// GetFullAddress 获取完整地址（仅详细地址）
func (a *Address) GetFullAddress() string {
	return a.Address
}
