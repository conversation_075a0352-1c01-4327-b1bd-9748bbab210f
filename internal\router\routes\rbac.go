package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	adminHandlerPkg "yekaitai/internal/modules/admin/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterRBACRoutes 注册RBAC相关路由
func RegisterRBACRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.ServiceContext) {
	registerAdminRBACRoutes(server, serverCtx)
	registerAdminMenuRoutes(server, serverCtx)
}

// 注册后台管理RBAC相关路由
func registerAdminRBACRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.ServiceContext) {
	// 后台管理系统一般角色和权限路由，使用AdminAuthMiddleware
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	server.AddRoutes(
		[]rest.Route{
			// 获取当前管理员角色
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/rbac/roles/current",
				Handler: adminAuthWrapper(adminHandlerPkg.GetCurrentAdminRolesHandler(serverCtx)),
			},
			// 获取所有角色
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/rbac/roles",
				Handler: adminAuthWrapper(adminHandlerPkg.GetAllRolesHandler(serverCtx)),
			},
			// 创建角色
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/rbac/roles",
				Handler: adminAuthWrapper(adminHandlerPkg.CreateRoleHandler(serverCtx)),
			},
			// 更新角色
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/rbac/roles/:roleId",
				Handler: adminAuthWrapper(adminHandlerPkg.UpdateRoleHandler(serverCtx)),
			},
			// 删除角色
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/rbac/roles/:roleId",
				Handler: adminAuthWrapper(adminHandlerPkg.DeleteRoleHandler(serverCtx)),
			},
			// 获取角色权限
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/rbac/roles/:roleId/permissions",
				Handler: adminAuthWrapper(adminHandlerPkg.GetRolePermissionsHandler(serverCtx)),
			},
			// 分配角色权限
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/rbac/roles/:roleId/permissions",
				Handler: adminAuthWrapper(adminHandlerPkg.AssignPermissionsToRoleHandler(serverCtx)),
			},
			// 获取权限树
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/rbac/permissions/tree",
				Handler: adminAuthWrapper(adminHandlerPkg.GetPermissionsTreeHandler(serverCtx)),
			},
			// 获取所有权限
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/rbac/permissions",
				Handler: adminAuthWrapper(adminHandlerPkg.GetAllPermissionsHandler(serverCtx)),
			},
		},
	)

	// 用户管理特殊权限路由
	userPermHandler := func(next http.HandlerFunc) http.HandlerFunc {
		// 先应用管理员认证，然后应用权限检查
		handler := middleware.AdminAuthMiddleware(serverCtx)(
			middleware.AdminPermissionMiddleware("user", "manage")(next),
		)
		return http.HandlerFunc(handler.ServeHTTP)
	}

	server.AddRoutes(
		[]rest.Route{
			// 给用户分配角色
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/rbac/users/:userId/roles",
				Handler: userPermHandler(adminHandlerPkg.AssignRolesToUserHandler(serverCtx)),
			},
			// 移除用户角色
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/rbac/users/:userId/roles/:roleId",
				Handler: userPermHandler(adminHandlerPkg.RemoveRoleFromUserHandler(serverCtx)),
			},
			// 获取用户角色
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/rbac/users/:userId/roles",
				Handler: userPermHandler(adminHandlerPkg.GetUserRolesHandler(serverCtx)),
			},
		},
	)
}

// 注册后台管理菜单相关路由
func registerAdminMenuRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.ServiceContext) {
	// 后台管理系统菜单路由，使用AdminAuthMiddleware
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	server.AddRoutes(
		[]rest.Route{
			// 获取所有菜单
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/menus",
				Handler: adminAuthWrapper(adminHandlerPkg.GetAllMenusHandler(serverCtx)),
			},
			// 获取菜单详情及权限信息
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/menus/:menuId",
				Handler: adminAuthWrapper(adminHandlerPkg.GetMenuByIDHandler(serverCtx)),
			},
			// 获取角色可访问菜单
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/roles/:roleId/menus",
				Handler: adminAuthWrapper(adminHandlerPkg.GetRoleMenusHandler(serverCtx)),
			},
			// 为角色分配菜单
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/roles/:roleId/menus",
				Handler: adminAuthWrapper(adminHandlerPkg.AssignMenusToRoleHandler(serverCtx)),
			},
			// 获取当前管理员可访问菜单
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/menus/admin",
				Handler: adminAuthWrapper(adminHandlerPkg.GetCurrentAdminMenusHandler(serverCtx)),
			},
			// 获取指定管理员可访问菜单
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/admins/:adminId/menus",
				Handler: adminAuthWrapper(adminHandlerPkg.GetAdminMenusHandler(serverCtx)),
			},
		},
	)
}

// GetRBACRoutes 获取RBAC相关路由
