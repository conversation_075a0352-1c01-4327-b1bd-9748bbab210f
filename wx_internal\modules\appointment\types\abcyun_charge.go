package types

// PatientInfo 患者信息
type PatientInfo struct {
	ID       string     `json:"id"`       // 患者ID
	Name     string     `json:"name"`     // 姓名
	Mobile   string     `json:"mobile"`   // 手机号
	Sex      string     `json:"sex"`      // 性别
	Birthday string     `json:"birthday"` // 生日
	Age      PatientAge `json:"age"`      // 年龄
	IDCard   string     `json:"idCard"`   // 身份证号
}

// QueryByDateRequest 按日期查询收费单请求
type QueryByDateRequest struct {
	Date     string `form:"date"`
	ClinicID string `form:"clinicId,optional"`
}

// ChargeSheetSummary 收费单摘要信息
type ChargeSheetSummary struct {
	ID              string      `json:"id"`              // 收费单ID
	PatientOrderID  string      `json:"patientOrderId"`  // 就诊单ID
	Created         string      `json:"created"`         // 创建时间
	DoctorName      string      `json:"doctorName"`      // 医生姓名
	IsDraft         bool        `json:"isDraft"`         // 是否草稿
	Status          string      `json:"status"`          // 状态: unpaid, partial_paid, paid, partial_refund, refunded
	OwedStatus      string      `json:"owedStatus"`      // 欠款状态：owed, none
	Patient         PatientInfo `json:"patient"`         // 患者信息
	ActualTotalFee  float64     `json:"actualTotalFee"`  // 实际总金额
	TotalFee        float64     `json:"totalFee"`        // 总金额
	PaidFee         float64     `json:"paidFee"`         // 已付金额
	ClinicID        string      `json:"clinicId"`        // 诊所ID
	ClinicName      string      `json:"clinicName"`      // 诊所名称
	DeliveryStatus  string      `json:"deliveryStatus"`  // 配送状态: none, wait_delivery, delivering, delivered
	HasRefundRecord bool        `json:"hasRefundRecord"` // 是否有退款记录
}

// ChargeSheetByDateResponse 按日期查询收费单响应
type ChargeSheetByDateResponse struct {
	Sheets []ChargeSheetSummary `json:"sheets"` // 收费单列表
}

// ChargeSheetDetailRequest 获取收费单详情请求
type ChargeSheetDetailRequest struct {
	ID string `path:"id"`
}

// ChargeItem 收费项目
type ChargeItem struct {
	ID        string  `json:"id"`        // 项目ID
	Name      string  `json:"name"`      // 名称
	Type      string  `json:"type"`      // 类型
	TypeName  string  `json:"typeName"`  // 类型名称
	TotalFee  float64 `json:"totalFee"`  // 总费用
	Fee       float64 `json:"fee"`       // 费用
	Count     int     `json:"count"`     // 数量
	IsPaid    bool    `json:"isPaid"`    // 是否已支付
	IsRefund  bool    `json:"isRefund"`  // 是否已退款
	CanRefund bool    `json:"canRefund"` // 是否可退款
}

// ChargeFormItem 收费表单项目
type ChargeFormItem struct {
	ID               string  `json:"id"`                         // 项目ID
	Type             string  `json:"type"`                       // 类型
	TypeName         string  `json:"typeName"`                   // 类型名称
	UnitCount        float64 `json:"unitCount"`                  // 单位数量
	DoseCount        float64 `json:"doseCount,omitempty"`        // 剂数
	ItemFee          float64 `json:"itemFee,omitempty"`          // 项目费用
	TotalFee         float64 `json:"totalFee"`                   // 总费用
	Name             string  `json:"name"`                       // 名称
	Unit             string  `json:"unit,omitempty"`             // 单位
	Price            float64 `json:"price,omitempty"`            // 价格
	Specification    string  `json:"specification,omitempty"`    // 规格
	ManufacturerName string  `json:"manufacturerName,omitempty"` // 生产厂家
	Status           int     `json:"status,omitempty"`           // 状态
}

// Transaction 交易记录
type Transaction struct {
	ID            string  `json:"id"`            // 交易记录ID
	Type          string  `json:"type"`          // 类型：pay, refund
	Mode          string  `json:"mode"`          // 支付方式：cash, WeChat, Alipay, medical_insurance, card, transfer, other
	Amount        float64 `json:"amount"`        // 金额
	Created       string  `json:"created"`       // 创建时间
	Operator      string  `json:"operator"`      // 操作人
	OperatorID    string  `json:"operatorId"`    // 操作人ID
	Comment       string  `json:"comment"`       // 备注
	ChargeSheetID string  `json:"chargeSheetId"` // 收费单ID
	TransactionID string  `json:"transactionId"` // 第三方交易ID
}

// DeliveryInfo 配送信息
type DeliveryInfo struct {
	ID           string  `json:"id"`           // 配送信息ID
	Name         string  `json:"name"`         // 姓名
	Mobile       string  `json:"mobile"`       // 手机号
	Address      string  `json:"address"`      // 地址
	Fee          float64 `json:"fee"`          // 配送费
	Comment      string  `json:"comment"`      // 备注
	CreatedAt    string  `json:"createdAt"`    // 创建时间
	ModifiedAt   string  `json:"modifiedAt"`   // 修改时间
	PayStatus    string  `json:"payStatus"`    // 支付状态: unpaid, paid
	DeliveryMode string  `json:"deliveryMode"` // 配送方式: express, self
}

// MedicalRecord 医疗记录
type MedicalRecord struct {
	ID string `json:"id"` // 医疗记录ID
}

// ChargeSheetDetailResponse 收费单详情响应
type ChargeSheetDetailResponse struct {
	ID              string           `json:"id"`              // 收费单ID
	PatientOrderID  string           `json:"patientOrderId"`  // 就诊单ID
	Status          string           `json:"status"`          // 状态
	Type            string           `json:"type"`            // 类型
	Created         string           `json:"created"`         // 创建时间
	TotalFee        float64          `json:"totalFee"`        // 总金额
	ActualTotalFee  float64          `json:"actualTotalFee"`  // 实际总金额
	PaidFee         float64          `json:"paidFee"`         // 已付金额
	DiscountedFee   float64          `json:"discountedFee"`   // 折扣金额
	DoctorName      string           `json:"doctorName"`      // 医生姓名
	DoctorID        string           `json:"doctorId"`        // 医生ID
	Patient         PatientInfo      `json:"patient"`         // 患者信息
	Items           []ChargeItem     `json:"items"`           // 收费项目
	FormItems       []ChargeFormItem `json:"formItems"`       // 表单项目
	Transactions    []Transaction    `json:"transactions"`    // 交易记录
	DeliveryInfo    DeliveryInfo     `json:"deliveryInfo"`    // 配送信息
	ClinicID        string           `json:"clinicId"`        // 诊所ID
	ClinicName      string           `json:"clinicName"`      // 诊所名称
	DeliveryStatus  string           `json:"deliveryStatus"`  // 配送状态
	HasRefundRecord bool             `json:"hasRefundRecord"` // 是否有退款记录
	OwedStatus      string           `json:"owedStatus"`      // 欠款状态
	MedicalRecord   MedicalRecord    `json:"medicalRecord"`   // 病历
}

// ChargePayRequest 收费单付款请求
type ChargePayRequest struct {
	PayMode        int     `json:"payMode"`                  // 支付方式
	Amount         float64 `json:"amount"`                   // 付款金额
	NeedPayFee     float64 `json:"needPayFee"`               // 应付金额
	ExpectedOddFee float64 `json:"expectedOddFee,omitempty"` // 预期找零金额
	OperatorID     string  `json:"operatorId"`               // 操作人ID
	Comment        string  `json:"comment,omitempty"`        // 备注
}

// ChargePayResponse 收费单付款响应
type ChargePayResponse struct {
	ID             string  `json:"id"`             // 收费单ID
	PatientOrderID string  `json:"patientOrderId"` // 就诊单ID
	Status         int     `json:"status"`         // 状态码
	StatusName     string  `json:"statusName"`     // 状态名称
	NeedPay        float64 `json:"needPay"`        // 需要支付金额
	ReceivedFee    float64 `json:"receivedFee"`    // 已收费金额
}

// ChargeRefundRequest 收费单退款请求
type ChargeRefundRequest struct {
	OperatorID    string       `json:"operatorId"`            // 操作人ID
	NeedRefundFee float64      `json:"needRefundFee"`         // 需要退款金额
	RefundFee     float64      `json:"refundFee"`             // 退款金额
	PayMode       int          `json:"payMode"`               // 支付方式
	ChargeForms   []ChargeForm `json:"chargeForms,omitempty"` // 退费商品列表
}

// ChargeRefundResponse 收费单退款响应
type ChargeRefundResponse struct {
	ID          string  `json:"id"`          // 收费单ID
	Status      int     `json:"status"`      // 状态码
	StatusName  string  `json:"statusName"`  // 状态名称
	RefundFee   float64 `json:"refundFee"`   // 退款金额
	RefundedFee float64 `json:"refundedFee"` // 已退款金额
}

// ChargeDeliveryRequest 设置收费单快递信息请求
type ChargeDeliveryRequest struct {
	AddressProvinceID   string  `json:"addressProvinceId"`   // 省份ID
	AddressProvinceName string  `json:"addressProvinceName"` // 省份名称
	AddressCityID       string  `json:"addressCityId"`       // 城市ID
	AddressCityName     string  `json:"addressCityName"`     // 城市名称
	AddressDistrictID   string  `json:"addressDistrictId"`   // 区县ID
	AddressDistrictName string  `json:"addressDistrictName"` // 区县名称
	AddressDetail       string  `json:"addressDetail"`       // 详细地址
	DeliveryName        string  `json:"deliveryName"`        // 收件人姓名
	DeliveryMobile      string  `json:"deliveryMobile"`      // 收件人手机号
	DeliveryCompanyName string  `json:"deliveryCompanyName"` // 快递公司名称
	DeliveryFee         float64 `json:"deliveryFee"`         // 快递费
	DeliveryOrderNo     string  `json:"deliveryOrderNo"`     // 快递单号
	DeliveryPayType     int     `json:"deliveryPayType"`     // 支付方式：0-到付，1-寄付
	OperatorID          string  `json:"operatorId"`          // 操作人ID
}

// ChargeByPatientOrderIDRequest 通过就诊单ID查询收费单请求
type ChargeByPatientOrderIDRequest struct {
	PatientOrderID string `path:"patientOrderId"`
}

// ChargeByPatientOrderIDResponse 通过就诊单ID查询收费单响应
type ChargeByPatientOrderIDResponse struct {
	ChargeSheets []ChargeSheetSummary `json:"chargeSheets"` // 收费单列表
}

// ChargeByPatientRequest 查询患者的收费单请求
type ChargeByPatientRequest struct {
	PatientID string `path:"patientId"`
	BeginDate string `form:"beginDate,optional"` // 开始日期
	EndDate   string `form:"endDate,optional"`   // 结束日期
	Limit     int    `form:"limit,optional"`     // 限制数量
	Offset    int    `form:"offset,optional"`    // 偏移量
	Status    string `form:"status,optional"`    // 状态
}

// ChargeByPatientResponse 查询患者的收费单响应
type ChargeByPatientResponse struct {
	Rows   []ChargeSheetSummary `json:"rows"`   // 收费单列表
	Total  int                  `json:"total"`  // 总数
	Offset int                  `json:"offset"` // 偏移量
	Limit  int                  `json:"limit"`  // 限制数量
}

// ChargeCancelDeliveryRequest 取消收费单快递费请求
type ChargeCancelDeliveryRequest struct {
	OperatorID string `json:"operatorId"` // 操作人ID
}

// ChargeForm 收费表单
type ChargeForm struct {
	ID              string                 `json:"id"`                        // 收费表单ID
	ChargeFormItems []ChargeFormRefundItem `json:"chargeFormItems,omitempty"` // 收费表单项目
}

// ChargeFormRefundItem 收费表单退费项
type ChargeFormRefundItem struct {
	ID        string  `json:"id"`        // 项目ID
	UnitCount float64 `json:"unitCount"` // 单位数量
	DoseCount float64 `json:"doseCount"` // 剂数
}
