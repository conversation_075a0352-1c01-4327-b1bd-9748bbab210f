package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"yekaitai/internal/modules/admin/model"
	"yekaitai/internal/modules/admin/service"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

// OperationLogMiddleware 操作日志中间件
type OperationLogMiddleware struct {
	logService *service.AdminOperationLogService
}

// NewOperationLogMiddleware 创建操作日志中间件
func NewOperationLogMiddleware() *OperationLogMiddleware {
	return &OperationLogMiddleware{
		logService: service.NewAdminOperationLogService(),
	}
}

// responseWriter 包装 ResponseWriter 以捕获响应状态码和内容
type responseWriter struct {
	http.ResponseWriter
	statusCode int
	body       *bytes.Buffer
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

func (rw *responseWriter) Write(data []byte) (int, error) {
	rw.body.Write(data)
	return rw.ResponseWriter.Write(data)
}

// Handle 处理操作日志记录
func (m *OperationLogMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 添加调试日志
		logx.Infof("操作日志中间件: 处理请求 %s %s", r.Method, r.URL.Path)

		// 只记录后台管理接口的操作日志
		if !m.shouldLog(r) {
			logx.Infof("操作日志中间件: 跳过记录 %s %s (不符合记录条件)", r.Method, r.URL.Path)
			next(w, r)
			return
		}

		// 获取管理员ID
		adminID := m.getAdminID(r)
		logx.Infof("操作日志中间件: 获取到管理员ID=%d", adminID)
		if adminID == 0 {
			// 如果无法获取管理员ID，跳过日志记录
			logx.Infof("操作日志中间件: 跳过记录 %s %s (无法获取管理员ID)", r.Method, r.URL.Path)
			next(w, r)
			return
		}

		// 读取请求体
		requestBody := m.readRequestBody(r)

		// 包装 ResponseWriter
		rw := &responseWriter{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
			body:           &bytes.Buffer{},
		}

		// 记录开始时间
		startTime := time.Now()

		// 执行下一个处理器
		next(rw, r)

		// 记录操作日志
		logx.Infof("操作日志中间件: 开始记录操作日志 %s %s, 管理员ID=%d", r.Method, r.URL.Path, adminID)
		go m.recordOperationLog(r, adminID, requestBody, rw, startTime)
	}
}

// shouldLog 判断是否需要记录日志
func (m *OperationLogMiddleware) shouldLog(r *http.Request) bool {
	path := r.URL.Path
	logx.Infof("操作日志中间件: shouldLog检查路径 %s", path)

	// 去掉路径前缀 /yekaitai-admin-api
	if strings.HasPrefix(path, "/yekaitai-admin-api") {
		path = strings.TrimPrefix(path, "/yekaitai-admin-api")
		logx.Infof("操作日志中间件: 去掉前缀后的路径 %s", path)
	}

	// 判断是否为后台管理接口
	// 1. 以 /api/admin/ 开头的接口
	// 2. 以 /api/exchange/ 开头的接口
	// 3. 以 /api/doctor/ 开头的接口
	// 4. 以 /api/store/ 开头的接口
	// 5. 以 /api/patient/ 开头的接口
	// 6. 以 /api/content/ 开头的接口
	// 7. 以 /api/service/ 开头的接口
	// 8. 以 /api/coupon/ 开头的接口
	// 9. 以 /api/level/ 开头的接口
	// 10. 以 /api/points/ 开头的接口
	adminPrefixes := []string{
		"/api/admin/",
		"/api/exchange/",
		"/api/doctor/",
		"/api/store/",
		"/api/patient/",
		"/api/content/",
		"/api/service/",
		"/api/coupon/",
		"/api/level/",
		"/api/points/",
	}

	isAdminAPI := false
	for _, prefix := range adminPrefixes {
		if strings.HasPrefix(path, prefix) {
			isAdminAPI = true
			logx.Infof("操作日志中间件: 路径 %s 匹配前缀 %s", path, prefix)
			break
		}
	}

	if !isAdminAPI {
		logx.Infof("操作日志中间件: 路径 %s 不匹配任何管理接口前缀", path)
		return false
	}

	// 排除不需要记录的接口
	excludePaths := []string{
		"/api/admin/auth/login",   // 登录接口
		"/api/admin/auth/logout",  // 登出接口
		"/api/admin/auth/refresh", // 刷新token接口
		"/api/admin/health",       // 健康检查
		"/api/admin/ping",         // ping接口
	}

	for _, excludePath := range excludePaths {
		if path == excludePath {
			return false
		}
	}

	// 排除GET请求的列表查询接口（可选）- 暂时注释掉以便测试
	// if r.Method == "GET" && (strings.Contains(path, "/list") || strings.Contains(path, "/page")) {
	// 	return false
	// }

	return true
}

// getAdminID 从请求中获取管理员ID
func (m *OperationLogMiddleware) getAdminID(r *http.Request) uint {
	// 从JWT token中获取管理员ID
	adminIDStr := r.Header.Get("X-Admin-ID")
	logx.Infof("操作日志中间件: 从请求头获取X-Admin-ID = '%s'", adminIDStr)

	if adminIDStr == "" {
		// 尝试从context中获取
		logx.Infof("操作日志中间件: X-Admin-ID为空，尝试从context获取admin_id")
		if adminID := r.Context().Value("admin_id"); adminID != nil {
			logx.Infof("操作日志中间件: 从context获取到admin_id = %v (类型: %T)", adminID, adminID)
			if id, ok := adminID.(uint); ok {
				logx.Infof("操作日志中间件: admin_id转换为uint成功: %d", id)
				return id
			}
			if idStr, ok := adminID.(string); ok {
				logx.Infof("操作日志中间件: admin_id为string类型: '%s'", idStr)
				if id, err := strconv.ParseUint(idStr, 10, 32); err == nil {
					logx.Infof("操作日志中间件: admin_id解析成功: %d", uint(id))
					return uint(id)
				} else {
					logx.Errorf("操作日志中间件: admin_id解析失败: %v", err)
				}
			}
		} else {
			logx.Infof("操作日志中间件: context中没有admin_id")
		}
		return 0
	}

	adminID, err := strconv.ParseUint(adminIDStr, 10, 32)
	if err != nil {
		logx.Errorf("解析管理员ID失败: %v", err)
		return 0
	}

	logx.Infof("操作日志中间件: 从请求头解析管理员ID成功: %d", uint(adminID))
	return uint(adminID)
}

// readRequestBody 读取请求体
func (m *OperationLogMiddleware) readRequestBody(r *http.Request) string {
	if r.Body == nil {
		return ""
	}

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logx.Errorf("读取请求体失败: %v", err)
		return ""
	}

	// 重新设置请求体，以便后续处理器可以读取
	r.Body = io.NopCloser(bytes.NewBuffer(body))

	// 限制请求体长度，避免日志过大
	if len(body) > 1024 {
		return string(body[:1024]) + "..."
	}

	return string(body)
}

// recordOperationLog 记录操作日志
func (m *OperationLogMiddleware) recordOperationLog(r *http.Request, adminID uint, requestBody string, rw *responseWriter, startTime time.Time) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("记录操作日志时发生panic: %v", err)
		}
	}()

	logx.Infof("操作日志中间件: 开始记录操作日志详情 %s %s", r.Method, r.URL.Path)

	// 解析模块和操作
	module, action := m.parseModuleAndAction(r)

	// 解析目标ID和类型
	targetID, targetType := m.parseTargetInfo(r, requestBody)

	// 构建操作内容
	content := m.buildOperationContent(r, requestBody, rw)

	// 获取客户端IP
	clientIP := utils.GetClientIP(r)
	logx.Infof("操作日志中间件: 获取到客户端IP = '%s'", clientIP)
	logx.Infof("操作日志中间件: 请求头信息 - X-Forwarded-For: '%s', X-Real-IP: '%s', RemoteAddr: '%s'",
		r.Header.Get("X-Forwarded-For"), r.Header.Get("X-Real-IP"), r.RemoteAddr)

	// 计算执行时间
	duration := time.Since(startTime)

	// 创建操作日志
	log := &model.AdminOperationLog{
		AdminID:    adminID,
		Module:     module,
		Action:     action,
		TargetID:   targetID,
		TargetType: targetType,
		Content:    content,
		IP:         clientIP,
		Duration:   int(duration.Milliseconds()), // 执行时间（毫秒）
		StatusCode: rw.statusCode,
		CreatedAt:  time.Now(),
	}

	// 记录日志
	logx.Infof("操作日志中间件: 准备保存日志 - 管理员ID=%d, 模块=%s, 操作=%s", log.AdminID, log.Module, log.Action)
	if err := m.logService.CreateLog(context.Background(), log); err != nil {
		logx.Errorf("保存操作日志失败: %v", err)
	} else {
		logx.Infof("操作日志中间件: 日志保存成功 - ID=%d", log.LogID)
	}
}

// parseModuleAndAction 解析模块和操作
func (m *OperationLogMiddleware) parseModuleAndAction(r *http.Request) (string, string) {
	path := r.URL.Path
	method := r.Method

	// 去掉路径前缀 /yekaitai-admin-api
	originalPath := path
	if strings.HasPrefix(path, "/yekaitai-admin-api") {
		path = strings.TrimPrefix(path, "/yekaitai-admin-api")
	}
	logx.Infof("操作日志中间件: 路径解析 - 原始路径=%s, 去前缀后=%s", originalPath, path)

	// 解析模块名
	module := "unknown"

	// 移除API前缀
	if strings.HasPrefix(path, "/api/admin/") {
		path = strings.TrimPrefix(path, "/api/admin/")
		parts := strings.Split(path, "/")
		logx.Infof("操作日志中间件: 解析admin路径 - 去前缀后=%s, 分割结果=%v", path, parts)
		if len(parts) > 0 {
			module = parts[0]
		}
	} else if strings.HasPrefix(path, "/api/") {
		path = strings.TrimPrefix(path, "/api/")
		parts := strings.Split(path, "/")
		logx.Infof("操作日志中间件: 解析api路径 - 去前缀后=%s, 分割结果=%v", path, parts)
		if len(parts) > 0 {
			module = parts[0]
		}
	}
	logx.Infof("操作日志中间件: 解析得到模块=%s", module)

	// 根据HTTP方法和路径解析操作
	action := m.parseAction(method, path)

	// 特殊处理一些模块名
	switch module {
	case "exchange":
		module = "积分兑换"
	case "doctor":
		module = "医生管理"
	case "store":
		module = "门店管理"
	case "patient":
		module = "患者管理"
	case "content":
		module = "内容管理"
	case "service":
		module = "服务管理"
	case "coupon":
		module = "优惠券管理"
	case "level":
		module = "会员等级管理"
	case "points":
		module = "积分管理"
	case "users":
		module = "用户管理"
	case "rbac":
		module = "权限管理"
	case "menus":
		module = "菜单管理"
	case "roles":
		module = "角色管理"
	case "areas":
		module = "地区管理"
	}

	return module, action
}

// parseAction 解析操作类型
func (m *OperationLogMiddleware) parseAction(method, path string) string {
	switch method {
	case "POST":
		if strings.Contains(path, "/create") || strings.Contains(path, "/add") {
			return "创建"
		}
		if strings.Contains(path, "/update") || strings.Contains(path, "/edit") {
			return "更新"
		}
		if strings.Contains(path, "/delete") || strings.Contains(path, "/remove") {
			return "删除"
		}
		if strings.Contains(path, "/enable") {
			return "启用"
		}
		if strings.Contains(path, "/disable") {
			return "禁用"
		}
		if strings.Contains(path, "/assign") {
			return "分配"
		}
		if strings.Contains(path, "/upload") {
			return "上传"
		}
		if strings.Contains(path, "/import") {
			return "导入"
		}
		if strings.Contains(path, "/export") {
			return "导出"
		}
		return "创建"
	case "PUT":
		return "更新"
	case "DELETE":
		return "删除"
	case "PATCH":
		if strings.Contains(path, "/enable") {
			return "启用"
		}
		if strings.Contains(path, "/disable") {
			return "禁用"
		}
		return "更新"
	case "GET":
		if strings.Contains(path, "/export") {
			return "导出"
		}
		return "查看"
	default:
		return "未知操作"
	}
}

// parseTargetInfo 解析目标ID和类型
func (m *OperationLogMiddleware) parseTargetInfo(r *http.Request, requestBody string) (uint, string) {
	path := r.URL.Path
	targetType := m.getTargetTypeFromPath(path)

	// 从URL路径中提取ID（适用于更新、删除等操作）
	parts := strings.Split(path, "/")
	for i, part := range parts {
		if id, err := strconv.ParseUint(part, 10, 32); err == nil {
			// 找到数字ID，尝试确定类型
			if i > 0 {
				targetType = parts[i-1]
			}
			logx.Infof("操作日志中间件: 从URL路径解析到目标ID=%d, 类型=%s", uint(id), targetType)
			return uint(id), targetType
		}
	}

	// 从请求体中提取ID（适用于更新操作）
	if requestBody != "" {
		var bodyMap map[string]interface{}
		if err := json.Unmarshal([]byte(requestBody), &bodyMap); err == nil {
			// 尝试多种可能的ID字段
			idFields := []string{"id", "storeId", "store_id", "adminId", "admin_id", "userId", "user_id"}
			for _, field := range idFields {
				if id, ok := bodyMap[field]; ok {
					if idFloat, ok := id.(float64); ok {
						logx.Infof("操作日志中间件: 从请求体字段'%s'解析到目标ID=%d", field, uint(idFloat))
						return uint(idFloat), targetType
					}
				}
			}
		}
	}

	// 对于列表查询，尝试从查询参数中提取有用信息
	if r.Method == "GET" {
		query := r.URL.Query()

		// 如果有特定的ID参数
		if id := query.Get("id"); id != "" {
			if idNum, err := strconv.ParseUint(id, 10, 32); err == nil {
				logx.Infof("操作日志中间件: 从查询参数'id'解析到目标ID=%d", uint(idNum))
				return uint(idNum), targetType
			}
		}

		// 如果有用户ID参数
		if userID := query.Get("user_id"); userID != "" {
			if idNum, err := strconv.ParseUint(userID, 10, 32); err == nil {
				logx.Infof("操作日志中间件: 从查询参数'user_id'解析到目标ID=%d", uint(idNum))
				return uint(idNum), "user"
			}
		}

		// 尝试获取页码作为目标ID（用于标识查询的页面）
		if page := query.Get("page"); page != "" {
			if pageNum, err := strconv.ParseUint(page, 10, 32); err == nil {
				logx.Infof("操作日志中间件: 从查询参数'page'解析到目标ID=%d", uint(pageNum))
				return uint(pageNum), targetType
			}
		}
	}

	logx.Infof("操作日志中间件: 未能解析到目标ID，返回0")
	return 0, targetType
}

// getTargetTypeFromPath 从路径中获取目标类型
func (m *OperationLogMiddleware) getTargetTypeFromPath(path string) string {
	path = strings.TrimPrefix(path, "/api/admin/")
	parts := strings.Split(path, "/")
	if len(parts) > 0 {
		return parts[0]
	}
	return "unknown"
}

// buildOperationContent 构建操作内容
func (m *OperationLogMiddleware) buildOperationContent(r *http.Request, requestBody string, rw *responseWriter) string {
	// 基础信息
	module, action := m.parseModuleAndAction(r)
	targetID, _ := m.parseTargetInfo(r, requestBody)

	// 构建友好的操作描述
	var content string

	// 根据不同的操作类型构建不同的内容格式
	switch action {
	case "创建":
		content = fmt.Sprintf("创建%s", module)
		// 添加名称信息
		if name := m.extractNameFromRequest(r, requestBody); name != "" {
			content += fmt.Sprintf(": %s", name)
		}
		// 尝试从响应中获取新创建的ID
		if createdID := m.extractCreatedIDFromResponse(rw); createdID > 0 {
			content += fmt.Sprintf(" (ID: %d)", createdID)
		}
		// 添加创建的详细信息
		if createInfo := m.extractCreateInfo(r, requestBody); createInfo != "" {
			content += fmt.Sprintf(" - %s", createInfo)
		}
	case "更新":
		content = fmt.Sprintf("更新%s", module)
		if targetID > 0 {
			content += fmt.Sprintf(" (ID: %d)", targetID)
		}
		// 添加更新的字段信息
		if updateInfo := m.extractUpdateInfo(r, requestBody); updateInfo != "" {
			content += fmt.Sprintf(" - %s", updateInfo)
		}
	case "删除":
		content = fmt.Sprintf("删除%s", module)
		if targetID > 0 {
			content += fmt.Sprintf(" (ID: %d)", targetID)
		}
		// 添加删除的详细信息（如果请求体中有额外信息）
		if deleteInfo := m.extractDeleteInfo(r, requestBody); deleteInfo != "" {
			content += fmt.Sprintf(" - %s", deleteInfo)
		}
	case "查看":
		content = fmt.Sprintf("查看%s", module)
		// 添加查询条件
		if queryInfo := m.extractQueryInfo(r); queryInfo != "" {
			content += fmt.Sprintf(": %s", queryInfo)
		}
	case "导出":
		content = fmt.Sprintf("导出%s", module)
		if queryInfo := m.extractQueryInfo(r); queryInfo != "" {
			content += fmt.Sprintf(": %s", queryInfo)
		}
	case "导入":
		content = fmt.Sprintf("导入%s", module)
		if importInfo := m.extractImportInfo(r, requestBody); importInfo != "" {
			content += fmt.Sprintf(": %s", importInfo)
		}
	case "批量操作":
		content = fmt.Sprintf("批量操作%s", module)
		if batchInfo := m.extractBatchInfo(r, requestBody); batchInfo != "" {
			content += fmt.Sprintf(": %s", batchInfo)
		}
	default:
		content = fmt.Sprintf("%s%s", action, module)
		if targetID > 0 {
			content += fmt.Sprintf(" (ID: %d)", targetID)
		}
		// 添加通用的操作信息
		if generalInfo := m.extractGeneralInfo(r, requestBody); generalInfo != "" {
			content += fmt.Sprintf(" - %s", generalInfo)
		}
	}

	// 如果响应失败，添加错误信息
	if rw.statusCode >= 400 && rw.body.Len() > 0 {
		responseBody := rw.body.String()
		if len(responseBody) > 100 {
			responseBody = responseBody[:100] + "..."
		}
		content += fmt.Sprintf(" [失败: %s]", responseBody)
	}

	return content
}

// extractNameFromRequest 从请求中提取名称信息
func (m *OperationLogMiddleware) extractNameFromRequest(r *http.Request, requestBody string) string {
	if requestBody == "" {
		return ""
	}

	var bodyMap map[string]interface{}
	if err := json.Unmarshal([]byte(requestBody), &bodyMap); err != nil {
		return ""
	}

	// 尝试提取各种可能的名称字段
	nameFields := []string{"name", "title", "username", "realName", "real_name", "storeName", "store_name", "packageName", "package_name"}
	for _, field := range nameFields {
		if name, ok := bodyMap[field].(string); ok && name != "" {
			return name
		}
	}

	return ""
}

// extractUpdateInfo 提取更新信息
func (m *OperationLogMiddleware) extractUpdateInfo(r *http.Request, requestBody string) string {
	if requestBody == "" {
		return ""
	}

	var bodyMap map[string]interface{}
	if err := json.Unmarshal([]byte(requestBody), &bodyMap); err != nil {
		return ""
	}

	var updates []string

	// 定义字段映射，key是字段名，value是显示名称
	fieldMappings := map[string]string{
		"name":           "名称",
		"title":          "标题",
		"username":       "用户名",
		"realName":       "真实姓名",
		"real_name":      "真实姓名",
		"phone":          "电话",
		"email":          "邮箱",
		"address":        "地址",
		"description":    "描述",
		"content":        "内容",
		"price":          "价格",
		"originalPrice":  "原价",
		"original_price": "原价",
		"status":         "状态",
		"enabled":        "启用状态",
		"sort":           "排序",
		"weight":         "权重",
		"provinceId":     "省份",
		"province_id":    "省份",
		"cityId":         "城市",
		"city_id":        "城市",
		"areaId":         "区域",
		"area_id":        "区域",
		"managerId":      "管理员",
		"manager_id":     "管理员",
		"storeId":        "门店",
		"store_id":       "门店",
		"categoryId":     "分类",
		"category_id":    "分类",
		"tagIds":         "标签",
		"tag_ids":        "标签",
		"images":         "图片",
		"avatar":         "头像",
		"logo":           "Logo",
		"remark":         "备注",
		"note":           "备注",
	}

	// 提取更新字段
	for field, displayName := range fieldMappings {
		if value, ok := bodyMap[field]; ok {
			switch v := value.(type) {
			case string:
				if v != "" {
					updates = append(updates, fmt.Sprintf("%s:%s", displayName, v))
				}
			case float64:
				updates = append(updates, fmt.Sprintf("%s:%.2f", displayName, v))
			case int, int64:
				updates = append(updates, fmt.Sprintf("%s:%v", displayName, v))
			case bool:
				statusText := "否"
				if v {
					statusText = "是"
				}
				updates = append(updates, fmt.Sprintf("%s:%s", displayName, statusText))
			case []interface{}:
				if len(v) > 0 {
					updates = append(updates, fmt.Sprintf("%s:%d项", displayName, len(v)))
				}
			default:
				if value != nil {
					updates = append(updates, fmt.Sprintf("%s:%v", displayName, value))
				}
			}
		}
	}

	// 限制更新信息长度，避免过长
	result := strings.Join(updates, ", ")
	if len(result) > 200 {
		result = result[:200] + "..."
	}

	return result
}

// extractQueryInfo 提取查询信息
func (m *OperationLogMiddleware) extractQueryInfo(r *http.Request) string {
	query := r.URL.Query()
	var params []string

	// 定义查询参数映射
	queryMappings := map[string]string{
		"page":        "第%s页",
		"size":        "每页%s条",
		"keyword":     "关键词:%s",
		"name":        "名称:%s",
		"title":       "标题:%s",
		"phone":       "手机:%s",
		"email":       "邮箱:%s",
		"status":      "状态:%s",
		"enabled":     "启用:%s",
		"store_id":    "门店:%s",
		"storeId":     "门店:%s",
		"category_id": "分类:%s",
		"categoryId":  "分类:%s",
		"user_id":     "用户:%s",
		"userId":      "用户:%s",
		"admin_id":    "管理员:%s",
		"adminId":     "管理员:%s",
		"start_time":  "开始时间:%s",
		"startTime":   "开始时间:%s",
		"end_time":    "结束时间:%s",
		"endTime":     "结束时间:%s",
		"type":        "类型:%s",
		"module":      "模块:%s",
		"action":      "操作:%s",
		"sort":        "排序:%s",
		"order":       "排序:%s",
		"search":      "搜索:%s",
		"filter":      "筛选:%s",
	}

	// 提取查询参数
	for param, format := range queryMappings {
		if value := query.Get(param); value != "" {
			if param == "page" || param == "size" {
				params = append(params, fmt.Sprintf(format, value))
			} else {
				params = append(params, fmt.Sprintf(format, value))
			}
		}
	}

	// 处理特殊的数组参数
	if ids := query["ids"]; len(ids) > 0 {
		params = append(params, fmt.Sprintf("IDs:%s", strings.Join(ids, ",")))
	}
	if tags := query["tags"]; len(tags) > 0 {
		params = append(params, fmt.Sprintf("标签:%s", strings.Join(tags, ",")))
	}

	result := strings.Join(params, ", ")

	// 如果没有特殊参数，但是有其他查询参数，记录参数数量
	if result == "" && len(query) > 0 {
		result = fmt.Sprintf("查询参数:%d个", len(query))
	}

	return result
}

// extractCreatedIDFromResponse 从响应中提取新创建的ID
func (m *OperationLogMiddleware) extractCreatedIDFromResponse(rw *responseWriter) uint {
	if rw.statusCode != 200 || rw.body.Len() == 0 {
		return 0
	}

	responseBody := rw.body.String()
	var responseMap map[string]interface{}
	if err := json.Unmarshal([]byte(responseBody), &responseMap); err != nil {
		logx.Infof("操作日志中间件: 解析响应JSON失败: %v", err)
		return 0
	}

	// 尝试从data字段中获取ID（多种可能的字段名）
	if data, ok := responseMap["data"].(map[string]interface{}); ok {
		idFields := []string{"id", "ID", "storeId", "store_id", "adminId", "admin_id", "userId", "user_id", "packageId", "package_id"}
		for _, field := range idFields {
			if id, ok := data[field].(float64); ok && id > 0 {
				logx.Infof("操作日志中间件: 从响应data.%s获取到创建的ID=%d", field, uint(id))
				return uint(id)
			}
		}

		// 如果data是数组，尝试获取第一个元素的ID
		if dataArray, ok := responseMap["data"].([]interface{}); ok && len(dataArray) > 0 {
			if firstItem, ok := dataArray[0].(map[string]interface{}); ok {
				for _, field := range idFields {
					if id, ok := firstItem[field].(float64); ok && id > 0 {
						logx.Infof("操作日志中间件: 从响应data[0].%s获取到创建的ID=%d", field, uint(id))
						return uint(id)
					}
				}
			}
		}
	}

	// 直接从响应根级别获取ID
	idFields := []string{"id", "ID", "storeId", "store_id", "adminId", "admin_id", "userId", "user_id"}
	for _, field := range idFields {
		if id, ok := responseMap[field].(float64); ok && id > 0 {
			logx.Infof("操作日志中间件: 从响应根级别%s获取到创建的ID=%d", field, uint(id))
			return uint(id)
		}
	}

	logx.Infof("操作日志中间件: 未能从响应中提取到创建的ID")
	return 0
}

// extractCreateInfo 提取创建操作的详细信息
func (m *OperationLogMiddleware) extractCreateInfo(r *http.Request, requestBody string) string {
	// 创建操作的详细信息与更新信息类似，但可能包含更多字段
	return m.extractUpdateInfo(r, requestBody)
}

// extractDeleteInfo 提取删除操作的详细信息
func (m *OperationLogMiddleware) extractDeleteInfo(r *http.Request, requestBody string) string {
	if requestBody == "" {
		return ""
	}

	var bodyMap map[string]interface{}
	if err := json.Unmarshal([]byte(requestBody), &bodyMap); err != nil {
		return ""
	}

	var info []string

	// 提取删除相关的信息
	if reason, ok := bodyMap["reason"].(string); ok && reason != "" {
		info = append(info, fmt.Sprintf("原因:%s", reason))
	}
	if force, ok := bodyMap["force"].(bool); ok && force {
		info = append(info, "强制删除")
	}
	if cascade, ok := bodyMap["cascade"].(bool); ok && cascade {
		info = append(info, "级联删除")
	}
	if ids, ok := bodyMap["ids"].([]interface{}); ok && len(ids) > 0 {
		info = append(info, fmt.Sprintf("批量删除:%d项", len(ids)))
	}

	return strings.Join(info, ", ")
}

// extractImportInfo 提取导入操作的详细信息
func (m *OperationLogMiddleware) extractImportInfo(r *http.Request, requestBody string) string {
	if requestBody == "" {
		return ""
	}

	var bodyMap map[string]interface{}
	if err := json.Unmarshal([]byte(requestBody), &bodyMap); err != nil {
		return ""
	}

	var info []string

	// 提取导入相关的信息
	if filename, ok := bodyMap["filename"].(string); ok && filename != "" {
		info = append(info, fmt.Sprintf("文件:%s", filename))
	}
	if count, ok := bodyMap["count"].(float64); ok && count > 0 {
		info = append(info, fmt.Sprintf("数量:%.0f", count))
	}
	if mode, ok := bodyMap["mode"].(string); ok && mode != "" {
		info = append(info, fmt.Sprintf("模式:%s", mode))
	}

	return strings.Join(info, ", ")
}

// extractBatchInfo 提取批量操作的详细信息
func (m *OperationLogMiddleware) extractBatchInfo(r *http.Request, requestBody string) string {
	if requestBody == "" {
		return ""
	}

	var bodyMap map[string]interface{}
	if err := json.Unmarshal([]byte(requestBody), &bodyMap); err != nil {
		return ""
	}

	var info []string

	// 提取批量操作相关的信息
	if ids, ok := bodyMap["ids"].([]interface{}); ok && len(ids) > 0 {
		info = append(info, fmt.Sprintf("数量:%d", len(ids)))
	}
	if operation, ok := bodyMap["operation"].(string); ok && operation != "" {
		info = append(info, fmt.Sprintf("操作:%s", operation))
	}
	if status, ok := bodyMap["status"]; ok {
		info = append(info, fmt.Sprintf("状态:%v", status))
	}

	return strings.Join(info, ", ")
}

// extractGeneralInfo 提取通用操作信息
func (m *OperationLogMiddleware) extractGeneralInfo(r *http.Request, requestBody string) string {
	if requestBody == "" {
		return ""
	}

	var bodyMap map[string]interface{}
	if err := json.Unmarshal([]byte(requestBody), &bodyMap); err != nil {
		return ""
	}

	var info []string

	// 提取一些通用的重要字段
	importantFields := []string{"name", "title", "status", "type", "action"}
	for _, field := range importantFields {
		if value, ok := bodyMap[field]; ok && value != nil {
			switch v := value.(type) {
			case string:
				if v != "" {
					info = append(info, fmt.Sprintf("%s:%s", field, v))
				}
			case float64, int, int64:
				info = append(info, fmt.Sprintf("%s:%v", field, v))
			case bool:
				info = append(info, fmt.Sprintf("%s:%v", field, v))
			}
		}
	}

	// 限制长度
	result := strings.Join(info, ", ")
	if len(result) > 100 {
		result = result[:100] + "..."
	}

	return result
}
