package abcyun

// GenericResponse API响应通用结构(泛型版本)
type GenericResponse[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    T      `json:"data"`
}

// PagedResult 分页结果
type PagedResult[T any] struct {
	Total    int64 `json:"total"`
	PageSize int   `json:"pageSize"`
	PageNum  int   `json:"pageNum"`
	Rows     []T   `json:"rows"`
}

// 商品分类定义

// Product 商品信息
type Product struct {
	ID            string  `json:"id"`
	Name          string  `json:"name"`
	Status        int     `json:"status"`
	PackagePrice  float64 `json:"packagePrice"`
	PackageUnit   string  `json:"packageUnit"`
	PieceNum      float64 `json:"pieceNum"`
	CustomTypeID  string  `json:"customTypeId"`
	Manufacturer  string  `json:"manufacturer"`
	Specification string  `json:"specification"`
	BarCode       string  `json:"barCode"`
	MedicineNmpn  string  `json:"medicineNmpn"`
}

// ProductType 商品分类
type ProductType struct {
	ID       string        `json:"id"`
	Name     string        `json:"name"`
	Children []ProductType `json:"children"`
}

// ProductTypeResponse 商品分类响应
type ProductTypeResponse struct {
	TypeTree []ProductType `json:"typeTree"`
}

// CustomProductType 自定义商品分类
type CustomProductType struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// CustomProductTypeResponse 自定义商品分类响应
type CustomProductTypeResponse struct {
	List []CustomProductType `json:"list"`
}

// ProductStock 商品库存
type ProductStock struct {
	ProductID         string  `json:"productId"`
	StockPackageCount float64 `json:"stockPackageCount"`
}

// ProductStockResponse 商品库存响应
type ProductStockResponse struct {
	GoodsStocks []ProductStock `json:"goodsStocks"`
}

// 商品库存
type PharmacyStock struct {
	PharmacyNo        int     `json:"pharmacyNo"`
	PharmacyName      string  `json:"pharmacyName"`
	StockPieceCount   float64 `json:"stockPieceCount"`
	StockPackageCount float64 `json:"stockPackageCount"`
}

// 商品库存信息
type ProductStockInfo struct {
	ProductID         string          `json:"productId"`
	ProductShortID    string          `json:"productShortId"`
	StockPieceCount   float64         `json:"stockPieceCount"`
	StockPackageCount float64         `json:"stockPackageCount"`
	PharmacyStocks    []PharmacyStock `json:"pharmacyStocks"`
}

// 商品库存响应
type ProductStockResponseInfo struct {
	GoodsStocks []ProductStockInfo `json:"goodsStocks"`
}

// 商品批次信息
type ProductBatch struct {
	BatchID            int     `json:"batchId"`
	StockInOutDetailID string  `json:"stockInOutDetailId"`
	BatchNo            string  `json:"batchNo"`
	ProductionDate     string  `json:"productionDate"`
	ExpiryDate         string  `json:"expiryDate"`
	StockPieceCount    float64 `json:"stockPieceCount"`
	StockPackageCount  float64 `json:"stockPackageCount"`
	PackageCostPrice   float64 `json:"packageCostPrice"`
	InDate             string  `json:"inDate"`
}

// 商品批次响应商品
type ProductWithBatch struct {
	ProductID string         `json:"productId"`
	PieceNum  float64        `json:"pieceNum"`
	Batches   []ProductBatch `json:"batches"`
}

// 商品批次响应
type ProductBatchResponse struct {
	Products []ProductWithBatch `json:"products"`
}

// 供应商信息
type Supplier struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Status    int    `json:"status"`
	LicenseID string `json:"licenseId"`
	Contact   string `json:"contact"`
	Mobile    string `json:"mobile"`
	Mark      string `json:"mark"`
}

// 供应商列表响应
type SupplierListResponse struct {
	SupplierInfoList []Supplier `json:"supplierInfoList"`
}

// 出入库记录详情
type StockLogDetail struct {
	StockLogID             string  `json:"stockLogId"`
	Date                   string  `json:"date"`
	ProductID              string  `json:"productId"`
	ProductShortID         string  `json:"productShortId"`
	ProductName            string  `json:"productName"`
	PieceCount             float64 `json:"pieceCount"`
	PackageCount           float64 `json:"packageCount"`
	Action                 string  `json:"action"`
	OrderID                string  `json:"orderId"`
	OrderItemID            string  `json:"orderItemId"`
	PatientOrderID         string  `json:"patientOrderId"`
	SourceOrderID          string  `json:"sourceOrderId"`
	SourceOrderItemID      string  `json:"sourceOrderItemId"`
	PackageCostPrice       float64 `json:"packageCostPrice"`
	BatchID                int     `json:"batchId"`
	BatchNo                string  `json:"batchNo"`
	ProductionDate         string  `json:"productionDate"`
	ExpireDate             string  `json:"expireDate"`
	InOrderID              int     `json:"inOrderId"`
	InOrderItemID          int     `json:"inOrderItemId"`
	InOrderItemOutDetailID string  `json:"inOrderItemOutDetailId"`
	CostPrice              float64 `json:"costPrice"`
	SalePrice              float64 `json:"salePrice"`
	PieceNum               int     `json:"pieceNum"`
}

// StockLogResponse 出入库记录响应
type StockLogResponse struct {
	DetailsList []StockLogDetail `json:"detailsList"`
	Total       int              `json:"total"`
}
