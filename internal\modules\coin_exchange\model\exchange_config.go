package model

import (
	"time"

	"gorm.io/gorm"
)

// ExchangeType 兑换类型
const (
	ExchangeTypeProduct = iota + 1  // 商品
	ExchangeTypeService             // 服务
	ExchangeTypeAppointment         // 挂号
)

// ExchangeConfig 兑换配置表
type ExchangeConfig struct {
	ID        uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	Type      int            `gorm:"column:type;not null;comment:兑换类型(1=商品,2=服务,3=挂号)" json:"type"`
	ItemID    uint           `gorm:"column:item_id;default:null;comment:商品/服务ID" json:"item_id"`
	TimeSlot  string         `gorm:"column:time_slot;type:varchar(20);uniqueIndex;default:null;comment:挂号时段" json:"time_slot"`
	Operator  string         `gorm:"column:operator;type:varchar(50);not null;comment:操作人" json:"operator"`
	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`

	// 非数据库字段（联表查询）
	ItemName     string `gorm:"-" json:"item_name"`
	ItemCategory string `gorm:"-" json:"item_category"`
}

// TableName 设置表名
func (ExchangeConfig) TableName() string {
	return "exchange_config"
}

// ExchangeConfigQueryParams 兑换配置查询参数
type ExchangeConfigQueryParams struct {
	ItemName  string    `form:"name"`
	StartDate time.Time `form:"start_date" time_format:"2006-01-02"`
	EndDate   time.Time `form:"end_date" time_format:"2006-01-02"`
	Page      int       `form:"page"`
	PageSize  int       `form:"page_size"`
}

// ExchangeConfigCreateRequest 创建兑换配置请求
type ExchangeConfigCreateRequest struct {
	Type     int    `json:"type" validate:"required,oneof=1 2 3"`
	ItemID   uint   `json:"item_id,optional"`
	TimeSlot string `json:"time_slot,optional"`
} 