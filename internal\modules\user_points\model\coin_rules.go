package model

import (
	"errors"
	"fmt"
	"time"
)

// CoinRules 叶小币规则
type CoinRules struct {
	ID               uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:规则ID"`
	UserLevelID      uint      `json:"user_level_id" gorm:"not null;comment:用户等级ID"`
	RuleType         string    `json:"rule_type" gorm:"type:varchar(50);not null;comment:规则类型"`
	RuleName         string    `json:"rule_name" gorm:"type:varchar(100);not null;comment:规则名称"`
	Description      string    `json:"description" gorm:"type:text;comment:规则描述"`
	Enabled          bool      `json:"enabled" gorm:"default:true;comment:是否启用"`
	CoinsAwarded     int       `json:"coins_awarded" gorm:"default:0;comment:奖励叶小币数量"`
	MinAmount        int       `json:"min_amount" gorm:"default:0;comment:最小金额(分)"`
	AmountThreshold  int       `json:"amount_threshold" gorm:"default:0;comment:金额阈值(分)"`
	IsOneTime        bool      `json:"is_one_time" gorm:"default:false;comment:是否一次性奖励"`
	RequireShare     bool      `json:"require_share" gorm:"default:false;comment:是否需要分享"`
	ActivityType     string    `json:"activity_type" gorm:"type:varchar(50);comment:活动类型"`
	ExpiryPolicyType string    `json:"expiry_policy_type" gorm:"type:varchar(20);default:'PERMANENT';comment:期限类型:PERMANENT永久,YEARLY逐年,MONTHLY逐月,CUSTOM自定义"`
	CustomYears      int       `json:"custom_years" gorm:"default:0;comment:自定义有效期(年)"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt        *time.Time `json:"deleted_at" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (cr *CoinRules) TableName() string {
	return "coin_rules"
}

// SaveRulesRequest 批量保存积分规则请求
type SaveRulesRequest struct {
	LevelID      int           `json:"level_id"`      // 当前编辑的等级ID
	SyncAllLevel bool          `json:"sync_all_level"` // 是否同步到所有等级
	Rules        []RuleConfig  `json:"rules"`         // 规则配置列表
	ExpiryPolicy *ExpiryPolicy `json:"expiry_policy"` // 有效期策略
}

// Validate 验证批量保存规则请求
func (r *SaveRulesRequest) Validate() error {
	// 验证等级ID
	if r.LevelID <= 0 {
		return errors.New("等级ID不能为空")
	}

	// 验证规则列表
	if len(r.Rules) == 0 {
		return errors.New("规则配置不能为空")
	}

	// 验证有效期策略
	if r.ExpiryPolicy == nil {
		return errors.New("过期策略不能为空")
	}

	// 验证策略类型
	validPolicies := []string{
		ExpiryTypePermanent,
		ExpiryTypeYearly,
		ExpiryTypeMonthly,
		ExpiryTypeCustom,
	}
	
	isValidPolicy := false
	for _, policy := range validPolicies {
		if r.ExpiryPolicy.Policy == policy {
			isValidPolicy = true
			break
		}
	}
	
	if !isValidPolicy {
		return fmt.Errorf("无效的过期策略类型: %s", r.ExpiryPolicy.Policy)
	}

	// 如果是自定义策略，需要验证年份
	if r.ExpiryPolicy.Policy == ExpiryTypeCustom && r.ExpiryPolicy.CustomYears <= 0 {
		return errors.New("自定义策略的有效期年数必须大于0")
	}

	// 验证每个规则配置
	for i, rule := range r.Rules {
		// 验证规则类型
		if rule.RuleType == "" {
			return fmt.Errorf("第 %d 个规则的类型不能为空", i+1)
		}

		// 验证积分范围
		if rule.BasePoints < 0 || rule.BasePoints > 9999 {
			return fmt.Errorf("第 %d 个规则的基础积分超出范围 (0-9999)", i+1)
		}

		if rule.ExtraPoints < 0 || rule.ExtraPoints > 9999 {
			return fmt.Errorf("第 %d 个规则的额外积分超出范围 (0-9999)", i+1)
		}

		// 验证金额不能为负数
		if rule.MinAmount < 0 {
			return fmt.Errorf("第 %d 个规则的最小金额不能为负数", i+1)
		}

		if rule.AmountStep < 0 {
			return fmt.Errorf("第 %d 个规则的步长不能为负数", i+1)
		}
	}

	return nil
}

// RuleConfig 单个规则配置
type RuleConfig struct {
	RuleType    string `json:"rule_type"`     // 规则类型
	Enabled     bool   `json:"enabled"`       // 是否启用
	BasePoints  int    `json:"base_points"`   // 基础积分
	ExtraPoints int    `json:"extra_points"`  // 额外积分（部分规则使用）
	MinAmount   int    `json:"min_amount"`    // 最小金额/天数阈值
	AmountStep  int    `json:"amount_step"`   // 步长（每满多少）
}

// ExpiryPolicy 有效期策略
type ExpiryPolicy struct {
	Policy      string `json:"policy"`       // 策略类型：permanent/yearly/monthly/custom
	CustomYears int    `json:"custom_years"` // 自定义年数
}

// SaveRulesResponse 批量保存积分规则响应
type SaveRulesResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// GetRulesRequest 获取规则请求
type GetRulesRequest struct {
	LevelID int `json:"level_id" form:"level_id" query:"level_id"` // 等级ID
}

// GetRulesResponse 获取规则响应
type GetRulesResponse struct {
	Rules        []RuleConfig `json:"rules"`         // 规则配置列表
	ExpiryPolicy ExpiryPolicy `json:"expiry_policy"` // 有效期策略
} 