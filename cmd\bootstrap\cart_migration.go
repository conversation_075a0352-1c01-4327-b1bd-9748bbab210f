package bootstrap

import (
	"yekaitai/pkg/common/model/cart"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateCartTables 执行购物车模块表结构迁移
func MigrateCartTables() error {
	db := mysql.Master()

	logx.Info("开始执行购物车模块表结构迁移...")

	// 自动迁移购物车表
	if err := db.AutoMigrate(&cart.Cart{}); err != nil {
		logx.Errorf("购物车表迁移失败: %v", err)
		return err
	}
	logx.Info("购物车表迁移完成")

	logx.Info("购物车模块表结构迁移完成")
	return nil
}
