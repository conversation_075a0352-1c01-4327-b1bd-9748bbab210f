package service

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/internal/modules/user_points/model"
	"yekaitai/pkg/infra/mysql"
)

// CoinRewardService 叶小币奖励服务
type CoinRewardService struct {
	db *gorm.DB
}

// NewCoinRewardService 创建叶小币奖励服务
func NewCoinRewardService() *CoinRewardService {
	return &CoinRewardService{
		db: mysql.GetDB(),
	}
}

// ProcessConsumptionReward 处理消费奖励
func (s *CoinRewardService) ProcessConsumptionReward(ctx context.Context, userID uint, amount float64, orderNo string) error {
	// 1. 检查全局配置是否启用
	if !s.isGlobalEnabled(ctx) {
		logx.Infof("叶小币功能未启用，跳过消费奖励: userID=%d", userID)
		return nil
	}

	// 2. 获取用户等级
	userLevelID, err := s.getUserLevelID(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户等级失败: %w", err)
	}

	// 3. 查询消费奖励规则
	rules, err := s.getConsumptionRules(ctx, userLevelID, amount)
	if err != nil {
		return fmt.Errorf("查询消费奖励规则失败: %w", err)
	}

	if len(rules) == 0 {
		logx.Infof("无符合条件的消费奖励规则: userID=%d, amount=%.2f", userID, amount)
		return nil
	}

	// 4. 处理每个符合条件的规则
	totalCoins := 0
	for _, rule := range rules {
		// 检查是否已经奖励过（防重复）
		if rule.IsOneTime {
			awarded, err := s.checkOneTimeReward(ctx, userID, rule.ID)
			if err != nil {
				logx.Errorf("检查一次性奖励失败: userID=%d, ruleID=%d, error=%v", userID, rule.ID, err)
				continue
			}
			if awarded {
				logx.Infof("一次性奖励已发放，跳过: userID=%d, ruleID=%d", userID, rule.ID)
				continue
			}
		}

		// 计算奖励积分
		coins := s.calculateConsumptionCoins(rule, amount)
		if coins > 0 {
			totalCoins += coins

			// 记录一次性奖励
			if rule.IsOneTime {
				if err := s.recordOneTimeReward(ctx, userID, rule.ID, rule.RuleType); err != nil {
					logx.Errorf("记录一次性奖励失败: userID=%d, ruleID=%d, error=%v", userID, rule.ID, err)
				}
			}
		}
	}

	if totalCoins <= 0 {
		logx.Infof("无积分奖励: userID=%d, amount=%.2f", userID, amount)
		return nil
	}

	// 5. 发放积分
	return s.awardCoins(ctx, userID, totalCoins, "CONSUMPTION", orderNo, fmt.Sprintf("消费奖励(订单:%s,消费:%.2f元)", orderNo, amount))
}

// ProcessActivityReward 处理活动奖励
func (s *CoinRewardService) ProcessActivityReward(ctx context.Context, userID uint, activityType string, activityID string) error {
	// 1. 检查全局配置是否启用
	if !s.isGlobalEnabled(ctx) {
		logx.Infof("叶小币功能未启用，跳过活动奖励: userID=%d", userID)
		return nil
	}

	// 2. 获取用户等级
	userLevelID, err := s.getUserLevelID(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户等级失败: %w", err)
	}

	// 3. 查询活动奖励规则
	rules, err := s.getActivityRules(ctx, userLevelID, activityType)
	if err != nil {
		return fmt.Errorf("查询活动奖励规则失败: %w", err)
	}

	if len(rules) == 0 {
		logx.Infof("无符合条件的活动奖励规则: userID=%d, activityType=%s", userID, activityType)
		return nil
	}

	// 4. 处理每个符合条件的规则
	totalCoins := 0
	for _, rule := range rules {
		// 检查是否已经奖励过（防重复）
		if rule.IsOneTime {
			awarded, err := s.checkOneTimeReward(ctx, userID, rule.ID)
			if err != nil {
				logx.Errorf("检查一次性奖励失败: userID=%d, ruleID=%d, error=%v", userID, rule.ID, err)
				continue
			}
			if awarded {
				logx.Infof("一次性奖励已发放，跳过: userID=%d, ruleID=%d", userID, rule.ID)
				continue
			}
		}

		// 计算奖励积分
		coins := rule.CoinsAwarded
		if coins > 0 {
			totalCoins += coins

			// 记录一次性奖励
			if rule.IsOneTime {
				if err := s.recordOneTimeReward(ctx, userID, rule.ID, rule.RuleType); err != nil {
					logx.Errorf("记录一次性奖励失败: userID=%d, ruleID=%d, error=%v", userID, rule.ID, err)
				}
			}
		}
	}

	if totalCoins <= 0 {
		logx.Infof("无积分奖励: userID=%d, activityType=%s", userID, activityType)
		return nil
	}

	// 5. 发放积分
	return s.awardCoins(ctx, userID, totalCoins, "ACTIVITY", 0, fmt.Sprintf("活动奖励(%s:%s)", activityType, activityID))
}

// ProcessCheckinReward 处理打卡奖励
func (s *CoinRewardService) ProcessCheckinReward(ctx context.Context, userID uint, checkinDate string, shared bool) error {
	// 1. 检查全局配置是否启用
	if !s.isGlobalEnabled(ctx) {
		logx.Infof("叶小币功能未启用，跳过打卡奖励: userID=%d", userID)
		return nil
	}

	// 2. 获取用户等级
	userLevelID, err := s.getUserLevelID(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取用户等级失败: %w", err)
	}

	// 3. 查询打卡奖励规则
	var rule model.CoinRules
	err = s.db.WithContext(ctx).Where("user_level_id = ? AND rule_type = ? AND enabled = ?",
		userLevelID, "CHECKIN", true).First(&rule).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logx.Infof("无打卡奖励规则: userID=%d", userID)
			return nil
		}
		return fmt.Errorf("查询打卡奖励规则失败: %w", err)
	}

	// 4. 计算奖励积分
	coins := rule.CoinsAwarded
	if shared && rule.RequireShare {
		// 如果需要分享且用户已分享，可能有额外奖励
		// 这里可以根据业务需求调整
	}

	// 5. 发放积分（不需要在这里记录打卡记录，由签到服务处理）
	if coins > 0 {
		if err := s.awardCoins(ctx, userID, coins, "CHECKIN", 0, fmt.Sprintf("打卡奖励(%s)", checkinDate)); err != nil {
			return fmt.Errorf("发放打卡积分失败: %w", err)
		}
	}

	logx.Infof("打卡奖励成功: userID=%d, date=%s, coins=%d", userID, checkinDate, coins)
	return nil
}

// RefundCoinsForOrder 退款时归还叶小币
func (s *CoinRewardService) RefundCoinsForOrder(ctx context.Context, userID uint, orderNo string) error {
	// 查询该订单产生的所有积分交易
	var transactions []model.CoinTransactions
	err := s.db.WithContext(ctx).Where("user_id = ? AND order_id = ? AND transaction_type = ?",
		userID, orderNo, "CONSUMPTION").Find(&transactions).Error
	if err != nil {
		return fmt.Errorf("查询订单积分交易失败: %w", err)
	}

	if len(transactions) == 0 {
		logx.Infof("订单无积分交易记录: userID=%d, orderNo=%s", userID, orderNo)
		return nil
	}

	// 计算需要扣回的积分总数
	totalRefundCoins := 0
	for _, tx := range transactions {
		if tx.Amount > 0 { // 只扣回收入的积分
			totalRefundCoins += tx.Amount
		}
	}

	if totalRefundCoins <= 0 {
		logx.Infof("订单无需扣回积分: userID=%d, orderNo=%s", userID, orderNo)
		return nil
	}

	// 扣回积分
	return s.deductCoins(ctx, userID, totalRefundCoins, "REFUND", orderNo, fmt.Sprintf("订单退款扣回积分(订单:%s)", orderNo))
}

// isGlobalEnabled 检查全局配置是否启用
func (s *CoinRewardService) isGlobalEnabled(ctx context.Context) bool {
	var config model.CoinGlobalConfig
	err := s.db.WithContext(ctx).Where("enabled = ?", true).First(&config).Error
	return err == nil
}

// getUserLevelID 获取用户等级ID
func (s *CoinRewardService) getUserLevelID(ctx context.Context, userID uint) (uint, error) {
	var user struct {
		UserLevelID uint `json:"user_level_id"`
	}
	err := s.db.WithContext(ctx).Table("wx_user").
		Select("COALESCE(user_level_id, 1) as user_level_id").
		Where("user_id = ?", userID).First(&user).Error
	if err != nil {
		return 0, err
	}
	return user.UserLevelID, nil
}

// getConsumptionRules 获取消费奖励规则
func (s *CoinRewardService) getConsumptionRules(ctx context.Context, userLevelID uint, amount float64) ([]model.CoinRules, error) {
	var rules []model.CoinRules
	amountCents := int(amount * 100) // 转换为分

	err := s.db.WithContext(ctx).Where(
		"user_level_id = ? AND rule_type = ? AND enabled = ? AND amount_threshold <= ?",
		userLevelID, "CONSUMPTION", true, amountCents,
	).Find(&rules).Error

	return rules, err
}

// getActivityRules 获取活动奖励规则
func (s *CoinRewardService) getActivityRules(ctx context.Context, userLevelID uint, activityType string) ([]model.CoinRules, error) {
	var rules []model.CoinRules
	err := s.db.WithContext(ctx).Where(
		"user_level_id = ? AND rule_type = ? AND enabled = ? AND (activity_type = ? OR activity_type IS NULL)",
		userLevelID, "ACTIVITY", true, activityType,
	).Find(&rules).Error

	return rules, err
}

// calculateConsumptionCoins 计算消费积分
func (s *CoinRewardService) calculateConsumptionCoins(rule model.CoinRules, amount float64) int {
	amountCents := int(amount * 100)

	// 如果有最小金额要求，检查是否满足
	if rule.MinAmount > 0 && amountCents < rule.MinAmount {
		return 0
	}

	// 如果有金额阈值，检查是否满足
	if rule.AmountThreshold > 0 && amountCents < rule.AmountThreshold {
		return 0
	}

	return rule.CoinsAwarded
}

// checkOneTimeReward 检查一次性奖励是否已发放
func (s *CoinRewardService) checkOneTimeReward(ctx context.Context, userID uint, ruleID uint) (bool, error) {
	var count int64
	err := s.db.WithContext(ctx).Model(&model.CoinOneTimeRewards{}).
		Where("user_id = ? AND rule_id = ? AND awarded = ?", userID, ruleID, true).
		Count(&count).Error
	return count > 0, err
}

// recordOneTimeReward 记录一次性奖励
func (s *CoinRewardService) recordOneTimeReward(ctx context.Context, userID uint, ruleID uint, ruleType string) error {
	record := &model.CoinOneTimeRewards{
		UserID:   userID,
		RuleID:   ruleID,
		RuleType: ruleType,
		Awarded:  true,
	}
	return s.db.WithContext(ctx).Create(record).Error
}

// awardCoins 发放积分
func (s *CoinRewardService) awardCoins(ctx context.Context, userID uint, coins int, transactionType string, orderID interface{}, description string) error {
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := s.awardCoinsWithTx(ctx, tx, userID, coins, transactionType, orderID, description)
	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// awardCoinsWithTx 使用事务发放积分
func (s *CoinRewardService) awardCoinsWithTx(ctx context.Context, tx *gorm.DB, userID uint, coins int, transactionType string, orderID interface{}, description string) error {
	// 1. 更新用户积分账户
	err := tx.Exec(`
		INSERT INTO user_coins (user_id, total_coins, available_coins, frozen_coins, used_coins, created_at, updated_at)
		VALUES (?, ?, ?, 0, 0, NOW(), NOW())
		ON DUPLICATE KEY UPDATE
		total_coins = total_coins + VALUES(total_coins),
		available_coins = available_coins + VALUES(available_coins),
		updated_at = NOW()
	`, userID, coins, coins).Error

	if err != nil {
		return fmt.Errorf("更新用户积分账户失败: %w", err)
	}

	// 2. 获取更新后的余额
	var balance int
	err = tx.Table("user_coins").Select("available_coins").Where("user_id = ?", userID).Scan(&balance).Error
	if err != nil {
		return fmt.Errorf("获取用户积分余额失败: %w", err)
	}

	// 3. 记录交易流水
	transaction := &model.CoinTransactions{
		UserID:          userID,
		TransactionType: transactionType,
		Amount:          coins,
		Balance:         balance,
		Description:     description,
		CreatedAt:       time.Now(),
	}

	// 设置订单ID（如果有）
	if orderID != nil {
		switch v := orderID.(type) {
		case string:
			// 如果是订单号字符串，需要转换或存储在description中
			transaction.Description = fmt.Sprintf("%s(订单:%s)", description, v)
		case uint:
			transaction.OrderID = v
		case int:
			transaction.OrderID = uint(v)
		}
	}

	err = tx.Create(transaction).Error
	if err != nil {
		return fmt.Errorf("记录积分交易失败: %w", err)
	}

	logx.Infof("积分发放成功: userID=%d, coins=%d, type=%s, balance=%d", userID, coins, transactionType, balance)
	return nil
}

// deductCoins 扣减积分
func (s *CoinRewardService) deductCoins(ctx context.Context, userID uint, coins int, transactionType string, orderID interface{}, description string) error {
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 检查用户积分余额
	var userCoins model.UserCoins
	err := tx.Where("user_id = ?", userID).First(&userCoins).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("查询用户积分账户失败: %w", err)
	}

	if userCoins.AvailableCoins < coins {
		tx.Rollback()
		return fmt.Errorf("用户积分余额不足: 需要=%d, 可用=%d", coins, userCoins.AvailableCoins)
	}

	// 2. 扣减积分
	err = tx.Model(&userCoins).Updates(map[string]interface{}{
		"available_coins": gorm.Expr("available_coins - ?", coins),
		"updated_at":      time.Now(),
	}).Error

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("扣减用户积分失败: %w", err)
	}

	// 3. 记录交易流水
	transaction := &model.CoinTransactions{
		UserID:          userID,
		TransactionType: transactionType,
		Amount:          -coins, // 负数表示支出
		Balance:         userCoins.AvailableCoins - coins,
		Description:     description,
		CreatedAt:       time.Now(),
	}

	// 设置订单ID（如果有）
	if orderID != nil {
		switch v := orderID.(type) {
		case string:
			transaction.Description = fmt.Sprintf("%s(订单:%s)", description, v)
		case uint:
			transaction.OrderID = v
		case int:
			transaction.OrderID = uint(v)
		}
	}

	err = tx.Create(transaction).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("记录积分交易失败: %w", err)
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交扣减积分事务失败: %w", err)
	}

	logx.Infof("积分扣减成功: userID=%d, coins=%d, type=%s, balance=%d", userID, coins, transactionType, userCoins.AvailableCoins-coins)
	return nil
}
