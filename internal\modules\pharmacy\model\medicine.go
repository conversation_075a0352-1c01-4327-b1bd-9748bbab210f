package model

import (
	"time"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// Medicine 药品模型
type Medicine struct {
	ID               uint           `gorm:"primaryKey" json:"id"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"-"`
	Name             string         `gorm:"size:100;not null" json:"name"`              // 药品名称
	Code             string         `gorm:"size:50;uniqueIndex" json:"code"`            // 药品编码
	Barcode          string         `gorm:"size:50" json:"barcode"`                     // 条形码
	ApprovalNumber   string         `gorm:"size:100" json:"approval_number"`            // 批准文号
	CategoryID       uint           `gorm:"index" json:"category_id"`                   // 分类ID
	Spec             string         `gorm:"size:100" json:"spec"`                       // 规格
	Specification    string         `gorm:"size:100" json:"specification"`              // 规格详情
	Unit             string         `gorm:"size:20" json:"unit"`                        // 单位
	Manufacturer     string         `gorm:"size:200" json:"manufacturer"`               // 生产厂家
	Description      string         `gorm:"type:text" json:"description"`               // 药品描述
	Usage            string         `gorm:"size:500" json:"usage"`                      // 用法用量
	Indication       string         `gorm:"size:500" json:"indication"`                 // 适应症
	Contraindication string         `gorm:"size:500" json:"contraindication"`           // 禁忌症
	SideEffect       string         `gorm:"size:500" json:"side_effect"`                // 副作用
	Price            float64        `gorm:"type:decimal(10,2);not null" json:"price"`   // 价格
	MemberPrice      float64        `gorm:"type:decimal(10,2)" json:"member_price"`     // 会员价格
	Stock            int            `gorm:"not null;default:0" json:"stock"`            // 库存
	Images           string         `gorm:"size:1000" json:"images"`                    // 图片，多个图片用逗号分隔
	Status           int            `gorm:"default:1" json:"status"`                    // 状态 1:上架 0:下架
	IsRx             int            `gorm:"default:0" json:"is_rx"`                     // 是否处方药 1:是 0:否
	SalesCount       int            `gorm:"default:0" json:"sales_count"`               // 销量
	Score            float64        `gorm:"type:decimal(3,1);default:5.0" json:"score"` // 评分
	ExternalID       string         `gorm:"size:100;index" json:"external_id"`          // 外部系统ID
	ExternalType     string         `gorm:"size:50" json:"external_type"`               // 外部系统类型
}

// TableName 设置表名
func (Medicine) TableName() string {
	return "medicines"
}

// MedicineRepository 药品仓库接口
type MedicineRepository interface {
	Create(medicine *Medicine) error
	Update(medicine *Medicine) error
	Delete(id uint) error
	FindByID(id uint) (*Medicine, error)
	FindByCode(code string) (*Medicine, error)
	FindByExternalID(externalID string) (*Medicine, error)
	List(page, size int) ([]*Medicine, int64, error)
	ListByCategory(categoryID uint, page, size int) ([]*Medicine, int64, error)
	Search(keyword string, page, size int) ([]*Medicine, int64, error)
	UpdateStock(id uint, stock int) error
	UpdateStatus(id uint, status int) error
	UpdateSalesCount(id uint, count int) error
}

// medicineRepository 药品仓库实现
type medicineRepository struct{}

// NewMedicineRepository 创建药品仓库
func NewMedicineRepository() MedicineRepository {
	return &medicineRepository{}
}

// FindByExternalID 根据外部ID查询药品
func (r *medicineRepository) FindByExternalID(externalID string) (*Medicine, error) {
	var medicine Medicine
	err := mysql.Slave().Where("external_id = ?", externalID).First(&medicine).Error
	if err != nil {
		return nil, err
	}
	return &medicine, nil
}
