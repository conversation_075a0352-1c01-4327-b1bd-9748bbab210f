package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	doctorHandler "yekaitai/internal/modules/doctor/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterDoctorRoutes 注册医生管理相关路由
func RegisterDoctorRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建医生处理器
	handler := doctorHandler.NewDoctorHandler(serverCtx)

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 注册路由
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/doctors",
				Handler: adminAuthWrapper(handler.ListDoctors),
			},
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/doctors/:doctorId",
				Handler: adminAuthWrapper(handler.GetDoctor),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/doctors",
				Handler: adminAuthWrapper(handler.CreateDoctor),
			},
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/doctors/:doctorId",
				Handler: adminAuthWrapper(handler.UpdateDoctor),
			},
			{
				Method:  http.MethodPatch,
				Path:    "/api/admin/doctors/:doctorId/status",
				Handler: adminAuthWrapper(handler.UpdateDoctorStatus),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/doctors/import",
				Handler: adminAuthWrapper(handler.ImportDoctors),
			},
		},
	)

}
