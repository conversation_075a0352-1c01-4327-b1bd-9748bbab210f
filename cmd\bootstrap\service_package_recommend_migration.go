package bootstrap

import (
	"yekaitai/pkg/common/model/service"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateServicePackageRecommendTables 执行推荐服务套餐表结构迁移
func MigrateServicePackageRecommendTables() error {
	db := mysql.Master()

	// 自动迁移推荐服务套餐表
	if err := db.AutoMigrate(&service.ServicePackageRecommend{}); err != nil {
		logx.Errorf("迁移推荐服务套餐表失败: %v", err)
		return err
	}
	logx.Info("推荐服务套餐表迁移成功")

	return nil
}
