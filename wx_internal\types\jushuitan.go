package types

// Response 通用响应结构
// type Response struct {
// 	Code int         `json:"code"`
// 	Msg  string      `json:"msg"`
// 	Data interface{} `json:"data"`
// }

// Shop 店铺信息
type Shop struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// LogisticsCompany 物流公司信息
type LogisticsCompany struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
}

// Warehouse 仓库信息
type Warehouse struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Address string `json:"address"`
}

// User 用户信息
type User struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	RealName string `json:"real_name"`
}

// Supplier 供应商信息
type Supplier struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Contact string `json:"contact"`
	Phone   string `json:"phone"`
}

// Distributor 分销商信息
type Distributor struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Contact string `json:"contact"`
	Phone   string `json:"phone"`
}
