package user

import (
	"database/sql"
	"time"
)

// Tag 标签模型
type Tag struct {
	ID        uint         `json:"id" gorm:"primaryKey;autoIncrement;comment:标签ID"`
	Name      string       `json:"name" gorm:"type:varchar(50);not null;comment:标签名称"`
	CreatedAt time.Time    `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time    `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt sql.NullTime `json:"-" gorm:"index;comment:删除时间"`
	Users     []WxUser     `json:"-" gorm:"-"` // 手动管理，不使用many2many
}

// TableName 设置Tag表名
func (Tag) TableName() string {
	return "tag"
}
