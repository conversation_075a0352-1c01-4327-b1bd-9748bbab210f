package logic

import (
	"context"
	"yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// SupplierLogic 供应商查询逻辑
type SupplierLogic struct {
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

// NewSupplierLogic 创建供应商查询逻辑
func NewSupplierLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *SupplierLogic {
	return &SupplierLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// QuerySuppliers 查询供应商列表
func (l *SupplierLogic) QuerySuppliers(pageNum, pageSize int, params map[string]interface{}) (*jushuitan.BaseResp, error) {
	// 构建请求参数
	req := &jushuitan.SupplierQueryRequest{
		PageNum:  pageNum,
		PageSize: pageSize,
	}

	// 设置可选参数
	if val, ok := params["status"]; ok {
		if status, ok := val.(int); ok {
			req.Status = status
		}
	}
	if val, ok := params["coName"]; ok {
		if coName, ok := val.(string); ok {
			req.CoName = coName
		}
	}
	if val, ok := params["supplierCoID"]; ok {
		if supplierCoID, ok := val.(string); ok {
			req.SupplierCoID = supplierCoID
		}
	}

	return l.svcCtx.JushuitanClient.QuerySuppliers(l.ctx, req)
}
