package handler

import (
	"net/http"

	"yekaitai/internal/types"
	"yekaitai/pkg/common/model/evaluation"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// ServiceEvaluationHandler 后台服务评价处理器
type ServiceEvaluationHandler struct {
	repo *evaluation.ServiceEvaluationRepository
}

// NewServiceEvaluationHandler 创建后台服务评价处理器
func NewServiceEvaluationHandler() *ServiceEvaluationHandler {
	return &ServiceEvaluationHandler{
		repo: evaluation.NewServiceEvaluationRepository(mysql.Master()),
	}
}

// ListEvaluationsRequest 评价列表请求
type ListEvaluationsRequest struct {
	types.PageRequest
	StartDate string `form:"start_date,optional"` // 开始日期
	EndDate   string `form:"end_date,optional"`   // 结束日期
	Status    *int   `form:"status,optional"`     // 状态筛选
}

// EvaluationDetailRequest 评价详情请求
type EvaluationDetailRequest struct {
	ID uint `path:"id" validate:"required"`
}

// DeleteEvaluationRequest 删除评价请求
type DeleteEvaluationRequest struct {
	ID uint `path:"id" validate:"required"`
}

// ListEvaluations 获取服务评价列表
func (h *ServiceEvaluationHandler) ListEvaluations(w http.ResponseWriter, r *http.Request) {
	var req ListEvaluationsRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析评价列表请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	// 构建查询参数
	params := &evaluation.ServiceEvaluationQueryParams{
		Page:      req.Page,
		PageSize:  req.Size,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		Status:    req.Status,
	}

	// 查询评价列表
	evaluations, total, err := h.repo.List(r.Context(), params)
	if err != nil {
		logx.Errorf("查询服务评价列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询评价列表失败"))
		return
	}

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	// 构建响应
	resp := types.NewPageResponse(evaluations, total, &req.PageRequest)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(resp))
}

// GetEvaluationDetail 获取服务评价详情
func (h *ServiceEvaluationHandler) GetEvaluationDetail(w http.ResponseWriter, r *http.Request) {
	var req EvaluationDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析评价详情请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 查询评价详情（包含用户信息）
	evaluationInfo, err := h.repo.FindByIDWithUser(r.Context(), req.ID)
	if err != nil {
		logx.Errorf("查询服务评价详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询评价详情失败"))
		return
	}

	if evaluationInfo == nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "评价记录不存在"))
		return
	}

	// 记录操作日志
	// 记录操作日志 - 已由中间件统一处理
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(evaluationInfo))
}

// DeleteEvaluation 删除服务评价（软删除，仅在后台隐藏）
func (h *ServiceEvaluationHandler) DeleteEvaluation(w http.ResponseWriter, r *http.Request) {
	var req DeleteEvaluationRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析删除评价请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 先查询评价是否存在
	serviceEvaluation, err := h.repo.FindByID(r.Context(), req.ID)
	if err != nil {
		logx.Errorf("查询服务评价失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询评价失败"))
		return
	}

	if serviceEvaluation == nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "评价记录不存在"))
		return
	}

	// 软删除评价（设置状态为隐藏）
	err = h.repo.SoftDelete(r.Context(), req.ID)
	if err != nil {
		logx.Errorf("删除服务评价失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除评价失败"))
		return
	}

	// 记录操作日志
	logAdminOperation(r, "删除服务评价", map[string]interface{}{
		"evaluation_id": req.ID,
		"user_id":       serviceEvaluation.UserID,
		"content":       serviceEvaluation.TruncateContent(50),
	})

	logx.Infof("管理员删除服务评价成功，评价ID: %d", req.ID)

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(map[string]interface{}{
		"message": "删除评价成功",
	}))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func logAdminOperation(r *http.Request, operation string, details map[string]interface{}) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理")
}
