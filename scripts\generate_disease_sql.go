package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// go run scripts/generate_disease_sql.go
func main() {
	// 数据目录
	dataDir := "data/疾病库"

	// 输出文件
	outputFile := "migrations/disease_data.sql"

	if _, err := os.Stat(dataDir); os.IsNotExist(err) {
		fmt.Printf("数据目录不存在: %s\n", dataDir)
		return
	}

	// 确保输出目录存在
	os.MkdirAll(filepath.Dir(outputFile), 0755)

	// 读取疾病数据
	diseases, err := readDiseaseFiles(dataDir)
	if err != nil {
		fmt.Printf("读取疾病数据失败: %v\n", err)
		return
	}

	if len(diseases) == 0 {
		fmt.Println("没有找到疾病数据")
		return
	}

	// 生成SQL文件
	err = generateSQL(diseases, outputFile)
	if err != nil {
		fmt.Printf("生成SQL文件失败: %v\n", err)
		return
	}

	fmt.Printf("SQL文件已生成: %s\n", outputFile)
	fmt.Printf("共生成 %d 条疾病数据\n", len(diseases))
}

type DiseaseData struct {
	DiseaseName   string
	Department    string
	SubDepartment string
	Keywords      string
	Description   string
}

func readDiseaseFiles(dataDir string) ([]DiseaseData, error) {
	var diseases []DiseaseData

	// 遍历疾病库目录
	err := filepath.Walk(dataDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 只处理.txt文件
		if !strings.HasSuffix(info.Name(), ".txt") {
			return nil
		}

		// 解析科室名称
		department := strings.TrimSuffix(info.Name(), ".txt")

		// 如果是子目录中的文件，获取父目录作为主科室
		relPath, _ := filepath.Rel(dataDir, path)
		pathParts := strings.Split(relPath, string(filepath.Separator))

		var mainDepartment, subDepartment string
		if len(pathParts) > 1 {
			mainDepartment = pathParts[0]
			subDepartment = department
		} else {
			mainDepartment = department
		}

		fmt.Printf("正在处理: %s -> %s/%s\n", path, mainDepartment, subDepartment)

		// 读取文件内容
		fileDiseases, err := readDiseaseFile(path, mainDepartment, subDepartment)
		if err != nil {
			fmt.Printf("读取文件失败 %s: %v\n", path, err)
			return nil // 继续处理其他文件
		}

		diseases = append(diseases, fileDiseases...)
		return nil
	})

	return diseases, err
}

func readDiseaseFile(filePath, mainDepartment, subDepartment string) ([]DiseaseData, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	var diseases []DiseaseData
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		// 解析疾病名称，可能包含别名
		parts := strings.Split(line, "，")
		if len(parts) == 0 {
			continue
		}

		diseaseName := strings.TrimSpace(parts[0])
		if diseaseName == "" {
			continue
		}

		// 构建关键词（包含疾病名称和可能的别名）
		keywords := strings.Join(parts, ",")

		diseases = append(diseases, DiseaseData{
			DiseaseName:   diseaseName,
			Department:    mainDepartment,
			SubDepartment: subDepartment,
			Keywords:      keywords,
			Description:   fmt.Sprintf("%s相关疾病", mainDepartment),
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	return diseases, nil
}

func generateSQL(diseases []DiseaseData, outputFile string) error {
	file, err := os.Create(outputFile)
	if err != nil {
		return fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer file.Close()

	currentTime := time.Now().Format("2006-01-02 15:04:05")

	// 写入文件头
	fmt.Fprintf(file, "-- 疾病库数据导入SQL\n")
	fmt.Fprintf(file, "-- 生成时间: %s\n", currentTime)
	fmt.Fprintf(file, "-- 数据条数: %d\n\n", len(diseases))

	fmt.Fprintf(file, "-- 清空现有数据（可选）\n")
	fmt.Fprintf(file, "-- TRUNCATE TABLE diseases;\n\n")

	fmt.Fprintf(file, "-- 插入疾病库数据\n")
	fmt.Fprintf(file, "INSERT INTO `diseases` (`disease_name`, `department`, `sub_department`, `keywords`, `description`, `status`, `created_at`, `updated_at`) VALUES\n")

	// 写入数据
	for i, disease := range diseases {
		// 转义单引号
		diseaseName := strings.ReplaceAll(disease.DiseaseName, "'", "\\'")
		department := strings.ReplaceAll(disease.Department, "'", "\\'")
		subDepartment := strings.ReplaceAll(disease.SubDepartment, "'", "\\'")
		keywords := strings.ReplaceAll(disease.Keywords, "'", "\\'")
		description := strings.ReplaceAll(disease.Description, "'", "\\'")

		fmt.Fprintf(file, "('%s', '%s', '%s', '%s', '%s', 1, '%s', '%s')",
			diseaseName, department, subDepartment, keywords, description, currentTime, currentTime)

		if i < len(diseases)-1 {
			fmt.Fprintf(file, ",\n")
		} else {
			fmt.Fprintf(file, ";\n")
		}
	}

	return nil
}
