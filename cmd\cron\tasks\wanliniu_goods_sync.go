package tasks

import (
	"context"
	"fmt"
	"strings"
	"time"

	"yekaitai/internal/modules/goods/model"
	"yekaitai/internal/modules/goods/service"
	"yekaitai/pkg/adapters/wanliniu"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// WanLiNiuGoodsSyncService 万里牛商品同步服务
type WanLiNiuGoodsSyncService struct {
	client          *wanliniu.Client
	db              *gorm.DB
	categoryService *service.CategoryService
	categoryNameMap map[string]*model.Category // 分类名称映射缓存
}

// NewWanLiNiuGoodsSyncService 创建万里牛商品同步服务
func NewWanLiNiuGoodsSyncService() *WanLiNiuGoodsSyncService {
	return &WanLiNiuGoodsSyncService{
		client:          wanliniu.GetClient(),
		db:              mysql.Master(),
		categoryService: service.NewCategoryService(),
		categoryNameMap: make(map[string]*model.Category),
	}
}

// SyncGoods 定时同步商品
func (s *WanLiNiuGoodsSyncService) SyncGoods() error {
	logx.Info("[WanLiNiu] 开始定时同步商品...")

	// 检查并初始化万里牛客户端
	if err := s.ensureWanLiNiuClient(); err != nil {
		logx.Errorf("[WanLiNiu] 万里牛客户端初始化失败: %v", err)
		return fmt.Errorf("万里牛客户端初始化失败: %w", err)
	}

	// 确保默认分类存在并初始化分类缓存
	if err := s.initializeCategoryCache(); err != nil {
		logx.Errorf("[WanLiNiu] 初始化分类缓存失败: %v", err)
		return fmt.Errorf("初始化分类缓存失败: %w", err)
	}

	// 获取最后成功同步的时间
	lastSyncTime := s.getLastSyncTime()

	// 如果是首次同步或者距离上次同步超过1小时，进行增量同步
	if lastSyncTime == nil || time.Since(*lastSyncTime) > time.Hour {
		return s.syncGoodsIncremental(lastSyncTime)
	}

	logx.Info("[WanLiNiu] 距离上次同步时间不足1小时，跳过本次同步")
	return nil
}

// SyncGoodsManual 手动同步商品
func (s *WanLiNiuGoodsSyncService) SyncGoodsManual() error {
	logx.Info("[WanLiNiu] 开始手动同步商品...")

	// 检查并初始化万里牛客户端
	if err := s.ensureWanLiNiuClient(); err != nil {
		logx.Errorf("[WanLiNiu] 万里牛客户端初始化失败: %v", err)
		return fmt.Errorf("万里牛客户端初始化失败: %w", err)
	}

	// 确保默认分类存在并初始化分类缓存
	if err := s.initializeCategoryCache(); err != nil {
		logx.Errorf("[WanLiNiu] 初始化分类缓存失败: %v", err)
		return fmt.Errorf("初始化分类缓存失败: %w", err)
	}

	// 获取最后成功同步的时间
	lastSyncTime := s.getLastSyncTime()

	if lastSyncTime == nil {
		// 首次同步，拉取全部商品
		logx.Info("[WanLiNiu] 首次同步，开始拉取全部商品...")
		return s.syncAllGoods()
	} else {
		// 增量同步
		logx.Infof("[WanLiNiu] 增量同步，从 %s 开始...", lastSyncTime.Format("2006-01-02 15:04:05"))
		return s.syncGoodsIncremental(lastSyncTime)
	}
}

// getLastSyncTime 获取最后成功同步的时间
func (s *WanLiNiuGoodsSyncService) getLastSyncTime() *time.Time {
	var progress patient.SyncProgress
	err := s.db.Where("sync_type = ? AND status = ?", "wanliniu_goods_sync", 1).
		Order("sync_date DESC").
		First(&progress).Error

	if err != nil {
		return nil // 没有找到记录，返回nil
	}

	// 解析同步时间
	if syncTime, parseErr := time.Parse("2006-01-02 15:04:05", progress.SyncDate); parseErr == nil {
		return &syncTime
	}

	// 如果解析失败，返回nil
	return nil
}

// saveSyncProgress 保存同步进度
func (s *WanLiNiuGoodsSyncService) saveSyncProgress(syncTime time.Time, status int, records int, message string) {
	syncTimeStr := syncTime.Format("2006-01-02 15:04:05")

	progress := patient.SyncProgress{
		SyncType: "wanliniu_goods_sync",
		SyncDate: syncTimeStr,
		Status:   status,
		Records:  records,
		Message:  message,
	}

	// 先查找是否已存在该时间的记录
	var existing patient.SyncProgress
	err := s.db.Where("sync_type = ? AND sync_date = ?", "wanliniu_goods_sync", syncTimeStr).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 不存在，创建新记录
		s.db.Create(&progress)
		logx.Infof("[WanLiNiu] 保存同步进度: 时间=%s, 状态=%d, 记录数=%d", syncTimeStr, status, records)
	} else {
		// 存在，更新记录
		s.db.Model(&existing).Updates(map[string]interface{}{
			"status":  status,
			"records": records,
			"message": message,
		})
		logx.Infof("[WanLiNiu] 更新同步进度: 时间=%s, 状态=%d, 记录数=%d", syncTimeStr, status, records)
	}
}

// syncAllGoods 同步全部商品
func (s *WanLiNiuGoodsSyncService) syncAllGoods() error {
	ctx := context.Background()
	currentTime := time.Now()
	totalRecords := 0
	page := 1
	limit := 50 // 每页50条数据

	// 设置一个较早的修改时间以获取所有商品
	// 根据API文档，spec_code,item_code,modify_time,bar_code至少一个不能为空
	modifyTime := time.Now().AddDate(0, 0, -30) // 30天前

	for {
		// 查询商品
		// 根据API文档，spec_code,item_code,modify_time,bar_code至少一个不能为空
		// 我们使用modify_time参数进行查询
		params := wanliniu.GoodsQueryParams{
			Page:           page,
			Limit:          limit,
			ModifyTime:     &modifyTime, // 使用modify_time参数
			AllStatus:      true,        // 查询所有状态的商品
			NeedProperties: true,        // 需要查询商品属性
		}

		response, err := s.client.QueryGoods(ctx, params)
		if err != nil {
			logx.Errorf("[WanLiNiu] 查询商品失败: %v", err)
			s.saveSyncProgress(currentTime, 0, totalRecords, fmt.Sprintf("查询商品失败: %v", err))
			return err
		}

		if len(response.Data) == 0 {
			// 没有更多数据了
			break
		}

		// 处理商品数据
		for _, goodsData := range response.Data {
			if err := s.processGoods(goodsData); err != nil {
				logx.Errorf("[WanLiNiu] 处理商品失败: %s, 错误: %v", goodsData.GoodsCode, err)
				continue
			}
			totalRecords++
		}

		logx.Infof("[WanLiNiu] 已处理第 %d 页，本页 %d 条数据，累计 %d 条", page, len(response.Data), totalRecords)

		// 如果返回的数据少于limit，说明已经是最后一页
		if len(response.Data) < limit {
			break
		}

		page++

		// 避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}

	s.saveSyncProgress(currentTime, 1, totalRecords, fmt.Sprintf("同步成功，共处理 %d 条商品", totalRecords))
	logx.Infof("[WanLiNiu] 全量同步完成，共处理 %d 条商品", totalRecords)
	return nil
}

// syncGoodsIncremental 增量同步商品
func (s *WanLiNiuGoodsSyncService) syncGoodsIncremental(lastSyncTime *time.Time) error {
	ctx := context.Background()
	currentTime := time.Now()
	totalRecords := 0
	page := 1
	limit := 50

	// 如果lastSyncTime为空，使用一个默认的时间（1天前）
	// 根据API文档，spec_code,item_code,modify_time,bar_code至少一个不能为空
	modifyTime := lastSyncTime
	if modifyTime == nil {
		defaultTime := time.Now().AddDate(0, 0, -1) // 1天前
		modifyTime = &defaultTime
	}

	for {
		// 查询商品
		// 增量同步时，使用modify_time参数进行查询
		params := wanliniu.GoodsQueryParams{
			Page:           page,
			Limit:          limit,
			ModifyTime:     modifyTime, // 根据修改时间查询
			AllStatus:      true,       // 查询所有状态的商品
			NeedProperties: true,       // 需要查询商品属性
		}

		response, err := s.client.QueryGoods(ctx, params)
		if err != nil {
			logx.Errorf("[WanLiNiu] 增量查询商品失败: %v", err)
			s.saveSyncProgress(currentTime, 0, totalRecords, fmt.Sprintf("增量查询商品失败: %v", err))
			return err
		}

		if len(response.Data) == 0 {
			break
		}

		// 处理商品数据
		for _, goodsData := range response.Data {
			if err := s.processGoods(goodsData); err != nil {
				logx.Errorf("[WanLiNiu] 处理商品失败: %s, 错误: %v", goodsData.GoodsCode, err)
				continue
			}
			totalRecords++
		}

		logx.Infof("[WanLiNiu] 增量同步已处理第 %d 页，本页 %d 条数据，累计 %d 条", page, len(response.Data), totalRecords)

		if len(response.Data) < limit {
			break
		}

		page++
		time.Sleep(100 * time.Millisecond)
	}

	s.saveSyncProgress(currentTime, 1, totalRecords, fmt.Sprintf("增量同步成功，共处理 %d 条商品", totalRecords))
	logx.Infof("[WanLiNiu] 增量同步完成，共处理 %d 条商品", totalRecords)
	return nil
}

// processGoods 处理单个商品
func (s *WanLiNiuGoodsSyncService) processGoods(goodsData wanliniu.GoodsDetail) error {
	// 处理分类（如果有分类信息，自动添加到分类表）
	if goodsData.CatagoryID != "" && goodsData.CatagoryName != "" {
		if err := s.ensureCategory(goodsData.CatagoryID, goodsData.CatagoryName); err != nil {
			logx.Errorf("[WanLiNiu] 处理分类失败: %s, 错误: %v", goodsData.CatagoryID, err)
		}
	}

	// 根据分类名称匹配本地分类
	var localCategoryID string
	if goodsData.CatagoryName != "" {
		// 有分类名称，进行匹配
		matchedCategory := s.matchCategoryByName(goodsData.CatagoryName)
		if matchedCategory != nil {
			localCategoryID = matchedCategory.CategoryID
		} else {
			// 如果匹配失败，使用默认分类
			defaultCategory := s.getDefaultCategory()
			if defaultCategory != nil {
				localCategoryID = defaultCategory.CategoryID
			}
		}
	} else {
		// 没有分类名称，使用默认分类
		defaultCategory := s.getDefaultCategory()
		if defaultCategory != nil {
			localCategoryID = defaultCategory.CategoryID
		}
	}

	// 查找是否已存在该商品
	var existingGoods model.Goods
	err := s.db.Where("goods_code = ?", goodsData.GoodsCode).First(&existingGoods).Error

	// 收集图片
	pics := []string{}
	if goodsData.Pic != "" {
		pics = append(pics, goodsData.Pic)
	}
	if goodsData.Pic2 != "" {
		pics = append(pics, goodsData.Pic2)
	}
	if goodsData.Pic3 != "" {
		pics = append(pics, goodsData.Pic3)
	}
	if goodsData.Pic4 != "" {
		pics = append(pics, goodsData.Pic4)
	}

	// 构建商品数据
	goods := model.Goods{
		GoodsCode:          goodsData.GoodsCode,
		GoodsName:          goodsData.GoodsName,
		BrandName:          goodsData.BrandName,
		CategoryID:         localCategoryID, // 使用匹配的本地分类ID
		CategoryName:       goodsData.CatagoryName,
		Status:             goodsData.Status,
		TagPrice:           goodsData.TagPrice,
		Pic:                goodsData.Pic,
		Pics:               strings.Join(pics, ","),
		UnitName:           goodsData.UnitName,
		Remark:             goodsData.Remark,
		WanLiNiuModifyTime: goodsData.ModifyTime.ToTime(), // 使用ToTime()方法转换
	}

	if err == gorm.ErrRecordNotFound {
		// 商品不存在，创建新商品
		if err := s.db.Create(&goods).Error; err != nil {
			return fmt.Errorf("创建商品失败: %w", err)
		}
		logx.Infof("[WanLiNiu] 创建新商品: %s - %s, 分类: %s", goods.GoodsCode, goods.GoodsName, localCategoryID)
	} else if err != nil {
		return fmt.Errorf("查询商品失败: %w", err)
	} else {
		// 商品已存在，更新商品信息
		goods.ID = existingGoods.ID
		goods.IsOnSale = existingGoods.IsOnSale             // 保持上架状态
		goods.Description = existingGoods.Description       // 保持描述
		goods.SortOrder = existingGoods.SortOrder           // 保持排序
		goods.IsRecommended = existingGoods.IsRecommended   // 保持推荐状态
		goods.RecommendOrder = existingGoods.RecommendOrder // 保持推荐排序
		goods.SalesCount = existingGoods.SalesCount         // 保持销售单数统计
		goods.CreatedAt = existingGoods.CreatedAt           // 保持原创建时间

		// 保持药品相关信息（除非万里牛有新数据）
		if goodsData.UnitName == "" {
			goods.UnitName = existingGoods.UnitName
		}
		if goodsData.Remark == "" {
			goods.Remark = existingGoods.Remark
		}
		goods.FunctionIndication = existingGoods.FunctionIndication // 保持功能主治
		goods.Usage = existingGoods.Usage                           // 保持用法用量
		goods.ApplicablePeople = existingGoods.ApplicablePeople     // 保持适用人群

		// 如果原商品没有分类或使用默认分类，则更新为新匹配的分类
		if existingGoods.CategoryID == "" || existingGoods.CategoryID == model.DefaultCategoryID {
			goods.CategoryID = localCategoryID
			logx.Infof("[WanLiNiu] 更新商品分类: %s - %s, 新分类: %s", goods.GoodsCode, goods.GoodsName, localCategoryID)
		} else {
			// 保持原有分类设置
			goods.CategoryID = existingGoods.CategoryID
		}

		// 使用Select排除created_at字段的更新，避免无效日期错误
		if err := s.db.Model(&goods).Select("*").Omit("created_at").Updates(&goods).Error; err != nil {
			return fmt.Errorf("更新商品失败: %w", err)
		}
		logx.Infof("[WanLiNiu] 更新商品: %s - %s", goods.GoodsCode, goods.GoodsName)
	}

	// 处理商品规格
	return s.processGoodsSpecs(goods.ID, goodsData.Specs)
}

// ensureCategory 确保分类存在，如果不存在则创建
func (s *WanLiNiuGoodsSyncService) ensureCategory(categoryID, categoryName string) error {
	var existingCategory model.Category
	err := s.db.Where("category_id = ?", categoryID).First(&existingCategory).Error

	if err == gorm.ErrRecordNotFound {
		// 分类不存在，创建新分类
		category := model.Category{
			CategoryID:   categoryID,
			CategoryName: categoryName,
			ParentID:     0,  // 默认为顶级分类
			Status:       1,  // 默认启用
			Level:        1,  // 默认一级分类
			Description:  "", // 不自动添加描述内容
		}

		if err := s.db.Create(&category).Error; err != nil {
			return fmt.Errorf("创建分类失败: %w", err)
		}
		logx.Infof("[WanLiNiu] 创建新分类: %s - %s", categoryID, categoryName)
	} else if err != nil {
		return fmt.Errorf("查询分类失败: %w", err)
	} else {
		// 分类存在，检查是否需要更新名称
		if existingCategory.CategoryName != categoryName {
			existingCategory.CategoryName = categoryName
			if err := s.db.Save(&existingCategory).Error; err != nil {
				return fmt.Errorf("更新分类名称失败: %w", err)
			}
			logx.Infof("[WanLiNiu] 更新分类名称: %s - %s", categoryID, categoryName)
		}
	}

	return nil
}

// processGoodsSpecs 处理商品规格
func (s *WanLiNiuGoodsSyncService) processGoodsSpecs(goodsID uint, specs []wanliniu.GoodsSpec) error {
	for _, specData := range specs {
		// 查找是否已存在该规格
		var existingSpec model.GoodsSpec
		err := s.db.Where("goods_id = ? AND spec_code = ?", goodsID, specData.SpecCode).First(&existingSpec).Error

		// 构建规格数据
		spec := model.GoodsSpec{
			GoodsID:            goodsID,
			SpecCode:           specData.SpecCode,
			SpecName:           specData.Spec1 + " " + specData.Spec2, // 组合规格名称
			BarCode:            specData.BarCode,
			SalePrice:          specData.SalePrice, // 使用万里牛的标准售价
			Status:             specData.Status,
			Pic:                specData.Pic,
			WanLiNiuModifyTime: time.Now(), // 规格没有修改时间字段，使用当前时间
		}

		// 清理空白的规格名称
		spec.SpecName = strings.TrimSpace(spec.SpecName)
		if spec.SpecName == "" {
			spec.SpecName = specData.SpecCode // 如果没有规格名称，使用规格编码
		}

		if err == gorm.ErrRecordNotFound {
			// 规格不存在，创建新规格
			if err := s.db.Create(&spec).Error; err != nil {
				logx.Errorf("[WanLiNiu] 创建规格失败: %s, 错误: %v", spec.SpecCode, err)
				continue
			}
			logx.Infof("[WanLiNiu] 创建新规格: %s - %s", spec.SpecCode, spec.SpecName)
		} else if err != nil {
			logx.Errorf("[WanLiNiu] 查询规格失败: %s, 错误: %v", spec.SpecCode, err)
			continue
		} else {
			// 规格已存在，更新规格信息
			spec.ID = existingSpec.ID
			spec.Stock = existingSpec.Stock         // 保持库存数量
			spec.CreatedAt = existingSpec.CreatedAt // 保持原创建时间

			// 使用Select排除created_at字段的更新，避免无效日期错误
			if err := s.db.Model(&spec).Select("*").Omit("created_at").Updates(&spec).Error; err != nil {
				logx.Errorf("[WanLiNiu] 更新规格失败: %s, 错误: %v", spec.SpecCode, err)
				continue
			}
			logx.Infof("[WanLiNiu] 更新规格: %s - %s", spec.SpecCode, spec.SpecName)
		}
	}

	return nil
}

// initializeCategoryCache 初始化分类缓存
func (s *WanLiNiuGoodsSyncService) initializeCategoryCache() error {
	ctx := context.Background()

	// 确保默认分类存在
	if err := s.categoryService.InitDefaultCategory(ctx); err != nil {
		return fmt.Errorf("初始化默认分类失败: %w", err)
	}

	// 加载所有分类到缓存
	var categories []model.Category
	if err := s.db.Find(&categories).Error; err != nil {
		return fmt.Errorf("查询分类列表失败: %w", err)
	}

	// 清空并重建缓存
	s.categoryNameMap = make(map[string]*model.Category)
	for i := range categories {
		category := &categories[i]
		s.categoryNameMap[category.CategoryName] = category
	}

	logx.Infof("[WanLiNiu] 分类缓存初始化完成，共加载 %d 个分类", len(s.categoryNameMap))
	return nil
}

// matchCategoryByName 根据分类名称匹配本地分类
func (s *WanLiNiuGoodsSyncService) matchCategoryByName(categoryName string) *model.Category {
	// 如果分类名称为空，返回默认分类
	if categoryName == "" {
		return s.getDefaultCategory()
	}

	// 先进行精确匹配
	if category, exists := s.categoryNameMap[categoryName]; exists {
		return category
	}

	// 如果精确匹配失败，尝试模糊匹配（包含关系）
	for name, category := range s.categoryNameMap {
		if strings.Contains(name, categoryName) || strings.Contains(categoryName, name) {
			return category
		}
	}

	// 如果都没匹配到，返回默认分类
	return s.getDefaultCategory()
}

// getDefaultCategory 获取默认分类
func (s *WanLiNiuGoodsSyncService) getDefaultCategory() *model.Category {
	if category, exists := s.categoryNameMap[model.DefaultCategoryName]; exists {
		return category
	}

	// 如果缓存中没有默认分类，从数据库查询
	ctx := context.Background()
	defaultCategory, err := s.categoryService.GetDefaultCategory(ctx)
	if err != nil {
		logx.Errorf("[WanLiNiu] 获取默认分类失败: %v", err)
		return nil
	}

	// 更新缓存
	s.categoryNameMap[model.DefaultCategoryName] = defaultCategory
	return defaultCategory
}

// ensureWanLiNiuClient 确保万里牛客户端已初始化
func (s *WanLiNiuGoodsSyncService) ensureWanLiNiuClient() error {
	// 如果客户端已存在，先检查是否可用
	if s.client != nil {
		// 测试连接
		ctx := context.Background()
		if err := s.client.TestConnection(ctx); err == nil {
			logx.Info("[WanLiNiu] 客户端连接正常")
			return nil
		} else {
			logx.Infof("[WanLiNiu] 客户端连接测试失败: %v，尝试重新初始化", err)
		}
	}

	// 尝试获取全局客户端
	s.client = wanliniu.GetClient()
	if s.client != nil {
		// 测试连接
		ctx := context.Background()
		if err := s.client.TestConnection(ctx); err == nil {
			logx.Info("[WanLiNiu] 获取到全局客户端，连接正常")
			return nil
		} else {
			logx.Infof("[WanLiNiu] 全局客户端连接测试失败: %v", err)
		}
	}

	// 如果全局客户端不可用，说明可能配置有问题
	logx.Error("[WanLiNiu] 万里牛客户端未初始化或不可用，请检查配置")
	return fmt.Errorf("万里牛客户端不可用，请检查配置")
}
