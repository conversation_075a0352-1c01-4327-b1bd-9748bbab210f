package service

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"yekaitai/internal/modules/coin_exchange/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// ExchangeService 兑换服务
type ExchangeService struct {
	db *gorm.DB
}

// NewExchangeService 创建兑换服务
func NewExchangeService() *ExchangeService {
	return &ExchangeService{
		db: mysql.GetDB(),
	}
}

// CreateConfig 创建兑换配置
func (s *ExchangeService) CreateConfig(ctx context.Context, config *model.ExchangeConfig) error {
	switch config.Type {
	case model.ExchangeTypeAppointment:
		// 检查时间段格式
		if !isValidTimeSlot(config.TimeSlot) {
			return fmt.Errorf("时间段格式无效，正确格式为 HH:MM-HH:MM")
		}

		// 检查重复时间段
		var count int64
		if err := s.db.WithContext(ctx).Model(&model.ExchangeConfig{}).
			Where("time_slot = ? AND deleted_at IS NULL", config.TimeSlot).
			Count(&count).Error; err != nil {
			return fmt.Errorf("检查时间段是否存在失败: %w", err)
		}
		if count > 0 {
			return fmt.Errorf("该时间段已存在")
		}
	case model.ExchangeTypeProduct:
		// 检查商品是否存在
		var count int64
		if err := s.db.WithContext(ctx).Table("goods").
			Where("id = ? AND deleted_at IS NULL", config.ItemID).
			Count(&count).Error; err != nil {
			return fmt.Errorf("检查商品是否存在失败: %w", err)
		}
		if count == 0 {
			return fmt.Errorf("商品不存在")
		}

		// 检查是否已经添加过该商品
		var existCount int64
		if err := s.db.WithContext(ctx).Model(&model.ExchangeConfig{}).
			Where("type = ? AND item_id = ? AND deleted_at IS NULL", config.Type, config.ItemID).
			Count(&existCount).Error; err != nil {
			return fmt.Errorf("检查是否已添加该商品失败: %w", err)
		}
		if existCount > 0 {
			return fmt.Errorf("该商品已添加到兑换列表")
		}
	case model.ExchangeTypeService:
		// 检查服务套餐是否存在
		var count int64
		if err := s.db.WithContext(ctx).Table("service_packages").
			Where("id = ? AND deleted_at IS NULL AND status = 'active'", config.ItemID).
			Count(&count).Error; err != nil {
			return fmt.Errorf("检查服务套餐是否存在失败: %w", err)
		}
		if count == 0 {
			return fmt.Errorf("服务套餐不存在或未上架")
		}

		// 检查是否已经添加过该服务套餐
		var existCount int64
		if err := s.db.WithContext(ctx).Model(&model.ExchangeConfig{}).
			Where("type = ? AND item_id = ? AND deleted_at IS NULL", config.Type, config.ItemID).
			Count(&existCount).Error; err != nil {
			return fmt.Errorf("检查是否已添加该服务套餐失败: %w", err)
		}
		if existCount > 0 {
			return fmt.Errorf("该服务套餐已添加到兑换列表")
		}
	default:
		return fmt.Errorf("无效的兑换类型")
	}

	// 创建兑换配置
	if err := s.db.WithContext(ctx).Create(config).Error; err != nil {
		return fmt.Errorf("创建兑换配置失败: %w", err)
	}

	return nil
}

// ListConfigs 获取兑换配置列表
func (s *ExchangeService) ListConfigs(ctx context.Context, params *model.ExchangeConfigQueryParams) ([]*model.ExchangeConfig, int64, error) {
	var configs []*model.ExchangeConfig
	var total int64

	query := s.db.WithContext(ctx).Model(&model.ExchangeConfig{})

	// 构建查询条件
	if params.ItemName != "" {
		// 商品和服务类型需要联表查询
		query = query.Where(`(
			(type = ? AND item_id IN (SELECT id FROM goods WHERE goods_name LIKE ? AND deleted_at IS NULL)) OR
			(type = ? AND item_id IN (SELECT id FROM service_packages WHERE name LIKE ? AND deleted_at IS NULL))
		)`, model.ExchangeTypeProduct, "%"+params.ItemName+"%", model.ExchangeTypeService, "%"+params.ItemName+"%")
	}

	// 日期范围筛选
	if !params.StartDate.IsZero() {
		query = query.Where("created_at >= ?", params.StartDate)
	}
	if !params.EndDate.IsZero() {
		endDate := params.EndDate.Add(24 * time.Hour)
		query = query.Where("created_at < ?", endDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取兑换配置总数失败: %w", err)
	}

	// 分页查询
	offset := (params.Page - 1) * params.PageSize
	if err := query.Order("created_at DESC").
		Offset(offset).Limit(params.PageSize).Find(&configs).Error; err != nil {
		return nil, 0, fmt.Errorf("查询兑换配置列表失败: %w", err)
	}

	// 填充关联数据
	for _, config := range configs {
		switch config.Type {
		case model.ExchangeTypeProduct:
			// 从商品表查询商品信息
			var goods struct {
				GoodsName    string `gorm:"column:goods_name"`
				CategoryName string `gorm:"column:category_name"`
			}
			if err := s.db.WithContext(ctx).Table("goods").
				Select("goods_name, category_name").
				Where("id = ?", config.ItemID).
				Where("deleted_at IS NULL").
				First(&goods).Error; err == nil {
				config.ItemName = goods.GoodsName
				if goods.CategoryName != "" {
					config.ItemCategory = goods.CategoryName
				} else {
					config.ItemCategory = "其他"
				}
			} else {
				config.ItemName = "未知商品"
				config.ItemCategory = "其他"
			}
		case model.ExchangeTypeService:
			// 从service_packages表查询服务套餐信息
			var servicePackage struct {
				Name     string `gorm:"column:name"`
				BodyPart string `gorm:"column:body_part"`
			}
			if err := s.db.WithContext(ctx).Table("service_packages").
				Select("name, body_part").
				Where("id = ?", config.ItemID).
				Where("deleted_at IS NULL").
				First(&servicePackage).Error; err == nil {
				config.ItemName = servicePackage.Name
				if servicePackage.BodyPart != "" {
					config.ItemCategory = servicePackage.BodyPart
				} else {
					config.ItemCategory = "服务套餐"
				}
			} else {
				config.ItemName = "未知服务套餐"
				config.ItemCategory = "服务套餐"
			}
		case model.ExchangeTypeAppointment:
			config.ItemName = config.TimeSlot
			config.ItemCategory = "挂号服务"
		}
	}

	return configs, total, nil
}

// DeleteConfig 删除兑换配置
func (s *ExchangeService) DeleteConfig(ctx context.Context, id uint) error {
	result := s.db.WithContext(ctx).Delete(&model.ExchangeConfig{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除兑换配置失败: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("兑换配置不存在")
	}
	return nil
}

// GetAvailableServicePackages 获取可用的服务套餐列表
func (s *ExchangeService) GetAvailableServicePackages(ctx context.Context) ([]map[string]interface{}, error) {
	var servicePackages []struct {
		ID       uint    `gorm:"column:id"`
		Name     string  `gorm:"column:name"`
		Price    float64 `gorm:"column:price"`
		BodyPart string  `gorm:"column:body_part"`
	}

	// 查询所有可用的服务套餐
	if err := s.db.WithContext(ctx).Table("service_packages").
		Select("id, name, price, body_part").
		Where("deleted_at IS NULL AND status = 'active'").
		Find(&servicePackages).Error; err != nil {
		return nil, fmt.Errorf("查询服务套餐失败: %w", err)
	}

	// 转换为前端需要的格式
	result := make([]map[string]interface{}, len(servicePackages))
	for i, pkg := range servicePackages {
		result[i] = map[string]interface{}{
			"id":        pkg.ID,
			"name":      pkg.Name,
			"price":     pkg.Price,
			"body_part": pkg.BodyPart,
		}
	}

	return result, nil
}

// isValidTimeSlot 校验时间段格式 (HH:MM-HH:MM)
func isValidTimeSlot(timeSlot string) bool {
	pattern := `^([01][0-9]|2[0-3]):([0-5][0-9])-([01][0-9]|2[0-3]):([0-5][0-9])$`
	match, _ := regexp.MatchString(pattern, timeSlot)
	return match
}
