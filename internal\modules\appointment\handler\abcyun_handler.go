package handler

import (
	"errors"
	"net/http"

	"yekaitai/internal/svc"
	"yekaitai/pkg/adapters/abcyun"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// AbcYunHandler 处理ABC云API相关请求的处理器
type AbcYunHandler struct {
	svcCtx *svc.ServiceContext
}

// NewAbcYunHandler 创建一个新的ABC云API处理器
func NewAbcYunHandler(svcCtx *svc.ServiceContext) *AbcYunHandler {
	return &AbcYunHandler{
		svcCtx: svcCtx,
	}
}

// 请求结构体定义
// EmployeeDetailRequest 员工详情请求
type EmployeeDetailRequest struct {
	ID string `path:"id"` // 员工ID，从路径参数获取
}

// DepartmentDetailRequest 科室详情请求
type DepartmentDetailRequest struct {
	ID string `path:"id"` // 科室ID，从路径参数获取
}

// DoctorByMobileRequest 根据手机号获取医生信息请求
type DoctorByMobileRequest struct {
	Mobile string `path:"mobile"` // 手机号，从路径参数获取
}

// DoctorListRequest 获取医生列表请求
type DoctorListRequest struct {
	Limit  int `form:"limit,optional,default=20"` // 每页数量，可选，默认20
	Offset int `form:"offset,optional,default=0"` // 起始位置，可选，默认0
}

// AbcYunTestHandler 测试ABC云API连接
func (h *AbcYunHandler) AbcYunTestHandler(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"code":    200,
		"message": "ABC云API测试连接成功",
	}
	httpx.OkJson(w, response)
}

// AbcYunDepartmentsHandler 获取科室列表
func (h *AbcYunHandler) AbcYunDepartmentsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	departments, err := client.GetDepartmentList()
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取科室列表成功",
		"data":    departments,
	}
	httpx.OkJson(w, response)
}

// AbcYunDepartmentDetailHandler 获取科室详情
func (h *AbcYunHandler) AbcYunDepartmentDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req DepartmentDetailRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.ID == "" {
		httpx.Error(w, errors.New("缺少科室ID参数"))
		return
	}

	departmentDetail, err := client.GetDepartmentDetail(req.ID)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取科室详情成功",
		"data":    departmentDetail,
	}
	httpx.OkJson(w, response)
}

// AbcYunClinicInfoHandler 获取门店信息
func (h *AbcYunHandler) AbcYunClinicInfoHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	clinicInfo, err := client.GetClinicInfo()
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取门店信息成功",
		"data":    clinicInfo,
	}
	httpx.OkJson(w, response)
}

// AbcYunEmployeesHandler 获取员工列表
func (h *AbcYunHandler) AbcYunEmployeesHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	employees, err := client.GetEmployeeList()
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取员工列表成功",
		"data":    employees,
	}
	httpx.OkJson(w, response)
}

// AbcYunEmployeeDetailHandler 获取员工详情
func (h *AbcYunHandler) AbcYunEmployeeDetailHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req EmployeeDetailRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.ID == "" {
		httpx.Error(w, errors.New("缺少员工ID参数"))
		return
	}

	employeeDetail, err := client.GetEmployeeDetail(req.ID)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取员工详情成功",
		"data":    employeeDetail,
	}
	httpx.OkJson(w, response)
}

// AbcYunDoctorByMobileHandler 根据手机号获取医生信息
func (h *AbcYunHandler) AbcYunDoctorByMobileHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req DoctorByMobileRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	if req.Mobile == "" {
		httpx.Error(w, errors.New("缺少手机号参数"))
		return
	}

	doctorInfo, err := client.GetDoctorByMobile(req.Mobile)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取医生信息成功",
		"data":    doctorInfo,
	}
	httpx.OkJson(w, response)
}

// AbcYunDoctorListHandler 获取医生列表
func (h *AbcYunHandler) AbcYunDoctorListHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	var req DoctorListRequest
	err := httpx.Parse(r, &req)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	doctorList, err := client.GetDoctorList(req.Limit, req.Offset)
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "获取医生列表成功",
		"data":    doctorList,
	}
	httpx.OkJson(w, response)
}

// AbcYunSyncDepartmentsHandler 同步科室数据处理器
func (h *AbcYunHandler) AbcYunSyncDepartmentsHandler(w http.ResponseWriter, r *http.Request) {
	client := abcyun.GetGlobalClient()
	if client == nil {
		httpx.Error(w, errors.New("ABC云客户端未初始化"))
		return
	}

	// 这里实际应用中应该调用同步服务来执行异步同步操作
	// 这里简化为直接获取科室数据并返回
	departments, err := client.GetDepartmentList()
	if err != nil {
		httpx.Error(w, err)
		return
	}

	response := map[string]interface{}{
		"code":    200,
		"message": "科室数据同步成功",
		"data": map[string]interface{}{
			"count": len(departments),
		},
	}
	httpx.OkJson(w, response)
}
