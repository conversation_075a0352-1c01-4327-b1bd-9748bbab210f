package bootstrap

import (
	"fmt"

	"yekaitai/internal/modules/sms/model"
	"yekaitai/pkg/infra/mysql"
)

// MigrateSmsCodeTables 执行短信验证码模块的表结构迁移
func MigrateSmsCodeTables() error {
	fmt.Println("开始迁移短信验证码模块表结构...")

	db := mysql.Master()

	// 设置表注释，包含验证码类型说明
	tableComment := `短信验证码表
验证码类型说明:
- 1: 登录验证码
- 2: 注册验证码
- 3: 重置密码验证码
- 4: 绑定手机号验证码
- 5: 更换手机号验证码`

	// 迁移短信验证码表
	if err := db.Set("gorm:table_options", fmt.Sprintf("COMMENT='%s'", tableComment)).AutoMigrate(&model.SmsCode{}); err != nil {
		return fmt.Errorf("迁移短信验证码表失败: %v", err)
	}

	fmt.Println("短信验证码模块表结构迁移完成")
	return nil
}
