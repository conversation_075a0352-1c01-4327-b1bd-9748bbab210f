package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	serviceModel "yekaitai/internal/modules/service_setup/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 标签列表请求
type TagListRequest struct {
	types.PageRequest
}

// 创建标签请求
type CreateTagRequest struct {
	Name string `json:"name"` // 标签名称
}

// 更新标签请求
type UpdateTagRequest struct {
	TagID uint   `path:"tagId"` // 标签ID
	Name  string `json:"name"`  // 标签名称
}

// 标签详情请求
type TagDetailRequest struct {
	TagID uint `path:"tagId"` // 标签ID
}

// 更新标签状态请求
type UpdateTagStatusRequest struct {
	TagID  uint   `path:"tagId"`  // 标签ID
	Status string `json:"status"` // 状态
}

// ServiceTagHandler 服务标签处理器
type ServiceTagHandler struct {
	svcCtx *svc.ServiceContext
}

// NewServiceTagHandler 创建服务标签处理器
func NewServiceTagHandler(svcCtx *svc.ServiceContext) *ServiceTagHandler {
	return &ServiceTagHandler{
		svcCtx: svcCtx,
	}
}

// ListTags 获取标签列表
func (h *ServiceTagHandler) ListTags(w http.ResponseWriter, r *http.Request) {
	var req TagListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取标签列表: page=%d, size=%d, query=%s", req.Page, req.Size, req.Query)

	tagRepo := h.svcCtx.ServiceTagRepo
	tags, total, err := tagRepo.List(req.Page, req.Size, req.Query)
	if err != nil {
		logx.Errorf("获取标签列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取标签列表失败"))
		return
	}

	// 处理数据
	result := make([]map[string]interface{}, len(tags))
	for i, tag := range tags {
		// 获取创建人信息
		createdByName := "系统"
		if tag.CreatedBy > 0 {
			adminRepo := h.svcCtx.AdminUserRepo
			admin, err := adminRepo.FindByID(tag.CreatedBy)
			if err == nil && admin != nil {
				createdByName = admin.Username
			}
		}

		result[i] = map[string]interface{}{
			"id":         tag.ID,
			"name":       tag.Name,
			"status":     tag.Status,
			"created_at": tag.CreatedAt,
			"created_by": createdByName,
		}
	}

	logx.Infof("获取标签列表成功: 共%d条记录", total)

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取标签列表成功"))
}

// GetTag 获取标签详情
func (h *ServiceTagHandler) GetTag(w http.ResponseWriter, r *http.Request) {
	var req TagDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取标签详情: tagId=%d", req.TagID)

	tagRepo := h.svcCtx.ServiceTagRepo
	tag, err := tagRepo.FindByID(req.TagID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "标签不存在"))
		} else {
			logx.Errorf("获取标签详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取标签详情失败"))
		}
		return
	}

	// 获取创建人信息
	createdByName := "系统"
	if tag.CreatedBy > 0 {
		adminRepo := h.svcCtx.AdminUserRepo
		admin, err := adminRepo.FindByID(tag.CreatedBy)
		if err == nil && admin != nil {
			createdByName = admin.Username
		}
	}

	// 构建结果
	result := map[string]interface{}{
		"id":         tag.ID,
		"name":       tag.Name,
		"status":     tag.Status,
		"created_at": tag.CreatedAt,
		"created_by": createdByName,
	}

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取标签详情成功"))
}

// CreateTag 创建标签
func (h *ServiceTagHandler) CreateTag(w http.ResponseWriter, r *http.Request) {
	var req CreateTagRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 检查必填字段
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签名称不能为空"))
		return
	}

	// 检查名称长度
	if len(req.Name) > 15 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签名称不能超过15个字符"))
		return
	}

	tagRepo := h.svcCtx.ServiceTagRepo

	// 检查标签名称是否已存在
	exists, err := tagRepo.CheckNameExists(req.Name, 0)
	if err != nil {
		logx.Errorf("检查标签名称失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建标签失败"))
		return
	}

	if exists {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签名称已存在"))
		return
	}

	// 从上下文中获取管理员ID
	adminIDStr, _ := r.Context().Value("admin_id").(string)
	adminID := utils.StringToUint(adminIDStr)

	// 创建标签
	tag := &serviceModel.ServiceTag{
		Name:      req.Name,
		Status:    "active",
		CreatedBy: adminID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := tagRepo.Create(tag); err != nil {
		logx.Errorf("创建标签失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建标签失败"))
		return
	}

	// 记录操作日志
	go h.logAdminOperation(r, "标签管理", "创建", tag.ID, "ServiceTag", fmt.Sprintf("创建标签: %s", tag.Name))

	httpx.OkJson(w, types.NewSuccessResponse(tag, "创建标签成功"))
}

// UpdateTag 更新标签
func (h *ServiceTagHandler) UpdateTag(w http.ResponseWriter, r *http.Request) {
	var req UpdateTagRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 检查必填字段
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签名称不能为空"))
		return
	}

	// 检查名称长度
	if len(req.Name) > 15 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签名称不能超过15个字符"))
		return
	}

	tagRepo := h.svcCtx.ServiceTagRepo

	// 检查标签是否存在
	tag, err := tagRepo.FindByID(req.TagID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "标签不存在"))
		} else {
			logx.Errorf("获取标签失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新标签失败"))
		}
		return
	}

	// 检查名称是否已被使用
	if req.Name != tag.Name {
		exists, err := tagRepo.CheckNameExists(req.Name, req.TagID)
		if err != nil {
			logx.Errorf("检查标签名称失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新标签失败"))
			return
		}

		if exists {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签名称已存在"))
			return
		}
	}

	// 更新标签
	tag.Name = req.Name
	tag.UpdatedAt = time.Now()

	if err := tagRepo.Update(tag); err != nil {
		logx.Errorf("更新标签失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新标签失败"))
		return
	}

	// 记录操作日志
	go h.logAdminOperation(r, "标签管理", "更新", tag.ID, "ServiceTag", fmt.Sprintf("更新标签: %s", tag.Name))

	httpx.OkJson(w, types.NewSuccessResponse(tag, "更新标签成功"))
}

// UpdateTagStatus 更新标签状态
func (h *ServiceTagHandler) UpdateTagStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdateTagStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 验证状态值
	if req.Status != "active" && req.Status != "disabled" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的状态值"))
		return
	}

	logx.Infof("更新标签状态: tagId=%d, status=%s", req.TagID, req.Status)

	tagRepo := h.svcCtx.ServiceTagRepo

	// 检查标签是否存在
	tag, err := tagRepo.FindByID(req.TagID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "标签不存在"))
		} else {
			logx.Errorf("获取标签失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新标签状态失败"))
		}
		return
	}

	// 检查状态是否已经是目标状态
	if tag.Status == req.Status {
		statusText := "启用"
		if req.Status == "disabled" {
			statusText = "禁用"
		}
		httpx.OkJson(w, types.NewSuccessResponse(nil, fmt.Sprintf("标签已处于%s状态", statusText)))
		return
	}

	// 更新标签状态
	if err := tagRepo.UpdateStatus(req.TagID, req.Status); err != nil {
		logx.Errorf("更新标签状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新标签状态失败"))
		return
	}

	// 如果禁用标签，需要同步禁用所有关联的套餐
	if req.Status == "disabled" {
		// 获取关联的套餐并禁用
		packageRepo := h.svcCtx.ServicePackageRepo
		// 获取所有使用该标签的套餐并更新状态
		// 注意：实际实现可能需要分批处理
		err := packageRepo.UpdateStatus(req.TagID, "disabled")
		if err != nil {
			logx.Errorf("禁用关联套餐失败: %v", err)
			// 不影响主流程，继续执行
		}
	}

	statusText := "启用"
	if req.Status == "disabled" {
		statusText = "禁用"
	}

	// 记录操作日志
	go h.logAdminOperation(r, "标签管理", "更新状态", tag.ID, "ServiceTag", fmt.Sprintf("%s标签: %s", statusText, tag.Name))

	httpx.OkJson(w, types.NewSuccessResponse(nil, fmt.Sprintf("标签%s成功", statusText)))
}

// DeleteTag 删除标签
func (h *ServiceTagHandler) DeleteTag(w http.ResponseWriter, r *http.Request) {
	tagIDStr := r.URL.Query().Get("tagId")
	if tagIDStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "标签ID不能为空"))
		return
	}

	tagID, err := strconv.ParseUint(tagIDStr, 10, 32)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的标签ID"))
		return
	}

	logx.Infof("删除标签: tagId=%d", tagID)

	tagRepo := h.svcCtx.ServiceTagRepo

	// 检查标签是否存在
	tag, err := tagRepo.FindByID(uint(tagID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "标签不存在"))
		} else {
			logx.Errorf("获取标签失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除标签失败"))
		}
		return
	}

	// 检查是否有关联的套餐
	hasPackages, err := tagRepo.HasPackages(uint(tagID))
	if err != nil {
		logx.Errorf("检查关联套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除标签失败"))
		return
	}

	if hasPackages {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "该标签已被套餐使用，不能删除"))
		return
	}

	// 删除标签
	if err := tagRepo.Delete(uint(tagID)); err != nil {
		logx.Errorf("删除标签失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除标签失败"))
		return
	}

	// 记录操作日志
	go h.logAdminOperation(r, "标签管理", "删除", tag.ID, "ServiceTag", fmt.Sprintf("删除标签: %s", tag.Name))

	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除标签成功"))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *ServiceTagHandler) logAdminOperation(r *http.Request, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 模块=%s, 操作=%s", module, action)
}
