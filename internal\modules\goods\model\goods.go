package model

import (
	"time"

	"gorm.io/gorm"
)

// Goods 商品表
type Goods struct {
	ID                 uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	GoodsCode          string         `gorm:"column:goods_code;type:varchar(50);index;comment:商品编码" json:"goods_code"`
	GoodsName          string         `gorm:"column:goods_name;type:varchar(255);comment:商品名称" json:"goods_name"`
	BrandName          string         `gorm:"column:brand_name;type:varchar(100);comment:品牌名称" json:"brand_name"`
	CategoryID         string         `gorm:"column:category_id;type:varchar(50);index;comment:分类ID" json:"category_id"`
	CategoryName       string         `gorm:"column:category_name;type:varchar(100);comment:分类名称" json:"category_name"`
	Status             int            `gorm:"column:status;default:1;comment:状态(0停用,1启用)" json:"status"`
	IsOnSale           int            `gorm:"column:is_on_sale;default:0;comment:是否上架(0下架,1上架)" json:"is_on_sale"`
	IsRecommended      int            `gorm:"column:is_recommended;type:tinyint(1);default:0;comment:是否推荐(0-否，1-是)" json:"is_recommended"`
	RecommendOrder     int            `gorm:"column:recommend_order;default:0;comment:推荐排序，从1开始，数字越小越靠前" json:"recommend_order"`
	TagPrice           float64        `gorm:"column:tag_price;comment:吊牌价" json:"tag_price"`
	Pic                string         `gorm:"column:pic;type:varchar(500);comment:主图" json:"pic"`
	Pics               string         `gorm:"column:pics;type:text;comment:图片列表,逗号分隔" json:"pics"`
	Description        string         `gorm:"column:description;type:text;comment:商品描述" json:"description"`
	Origin             string         `gorm:"column:origin;type:varchar(100);comment:产地" json:"origin"`
	Manufacturer       string         `gorm:"column:manufacturer;type:varchar(200);comment:生产厂家" json:"manufacturer"`
	UnitName           string         `gorm:"column:unit_name;type:varchar(50);comment:单位名称" json:"unit_name"`
	Remark             string         `gorm:"column:remark;type:text;comment:备注" json:"remark"`
	FunctionIndication string         `gorm:"column:function_indication;type:text;comment:功能主治" json:"function_indication"`
	Usage              string         `gorm:"column:usage;type:text;comment:用法用量" json:"usage"`
	ApplicablePeople   string         `gorm:"column:applicable_people;type:text;comment:适用人群" json:"applicable_people"`
	SalesCount         int            `gorm:"column:sales_count;default:0;comment:销售单数（支付成功统计）" json:"sales_count"`
	ViewCount          int            `gorm:"column:view_count;default:0;comment:浏览次数" json:"view_count"`
	IsIntegralExchange int            `gorm:"column:is_integral_exchange;type:tinyint(1);default:0;comment:是否支持积分兑换(0-否，1-是)" json:"is_integral_exchange"`
	IntegralPrice      int            `gorm:"column:integral_price;default:0;comment:积分兑换所需积分" json:"integral_price"`
	SortOrder          int            `gorm:"column:sort_order;default:0;comment:排序" json:"sort_order"`
	WanLiNiuModifyTime time.Time      `gorm:"column:wanliniu_modify_time;comment:万里牛修改时间" json:"wanliniu_modify_time"`
	CreatedAt          time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`

	// 关联关系
	Specs    []GoodsSpec `gorm:"foreignKey:GoodsID" json:"specs,omitempty"`
	Category *Category   `gorm:"foreignKey:CategoryID;references:CategoryID" json:"category,omitempty"`
}

// TableName 设置表名
func (Goods) TableName() string {
	return "goods"
}

// GoodsSpec 商品规格表
type GoodsSpec struct {
	ID                 uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	GoodsID            uint           `gorm:"column:goods_id;index;comment:商品ID" json:"goods_id"`
	SpecCode           string         `gorm:"column:spec_code;type:varchar(50);index;comment:规格编码" json:"spec_code"`
	SpecName           string         `gorm:"column:spec_name;type:varchar(100);comment:规格名称" json:"spec_name"`
	BarCode            string         `gorm:"column:bar_code;type:varchar(50);comment:条码" json:"bar_code"`
	SalePrice          float64        `gorm:"column:sale_price;comment:标准售价" json:"sale_price"`
	Status             int            `gorm:"column:status;default:1;comment:状态(0停用,1启用)" json:"status"`
	Stock              int            `gorm:"column:stock;default:0;comment:库存" json:"stock"`
	Pic                string         `gorm:"column:pic;type:varchar(500);comment:规格图片" json:"pic"`
	WanLiNiuModifyTime time.Time      `gorm:"column:wanliniu_modify_time;comment:万里牛修改时间" json:"wanliniu_modify_time"`
	CreatedAt          time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (GoodsSpec) TableName() string {
	return "goods_spec"
}

// 商品查询参数
type GoodsQueryParams struct {
	GoodsName          string `form:"goods_name"`
	CategoryID         string `form:"category_id"`
	Status             *int   `form:"status"`
	IsOnSale           *int   `form:"is_on_sale"`
	IsRecommended      *int   `form:"is_recommended"`
	IsIntegralExchange *int   `form:"is_integral_exchange"`
	Page               int    `form:"page"`
	PageSize           int    `form:"page_size"`
}

// 商品创建请求
type GoodsCreateRequest struct {
	GoodsCode          string  `json:"goods_code" validate:"required,max=50"`
	GoodsName          string  `json:"goods_name" validate:"required,max=255"`
	BrandName          string  `json:"brand_name,optional" validate:"max=100"`
	CategoryID         string  `json:"category_id,optional" validate:"max=50"`
	CategoryName       string  `json:"category_name,optional" validate:"max=100"`
	Status             int     `json:"status,optional" validate:"oneof=0 1"`
	IsOnSale           int     `json:"is_on_sale,optional" validate:"oneof=0 1"`
	IsRecommended      int     `json:"is_recommended,optional" validate:"oneof=0 1"`
	RecommendOrder     int     `json:"recommend_order,optional"`
	TagPrice           float64 `json:"tag_price,optional" validate:"min=0"`
	Pic                string  `json:"pic,optional" validate:"max=500"`
	Pics               string  `json:"pics,optional"`
	Description        string  `json:"description,optional"`
	Origin             string  `json:"origin,optional" validate:"max=100"`
	Manufacturer       string  `json:"manufacturer,optional" validate:"max=200"`
	IsIntegralExchange int     `json:"is_integral_exchange,optional" validate:"oneof=0 1"`
	IntegralPrice      int     `json:"integral_price,optional" validate:"min=0"`
	SortOrder          int     `json:"sort_order,optional"`
}

// 商品更新请求
type GoodsUpdateRequest struct {
	CategoryID         string `json:"category_id,optional" validate:"max=50"`             // 分类ID
	IsRecommended      int    `json:"is_recommended,optional" validate:"oneof=0 1"`       // 是否推荐
	RecommendOrder     int    `json:"recommend_order,optional"`                           // 推荐排序
	Status             *int   `json:"status,optional" validate:"oneof=0 1"`               // 状态(0停用,1启用)
	IsOnSale           *int   `json:"is_on_sale,optional" validate:"oneof=0 1"`           // 是否上架(0下架,1上架)
	IsIntegralExchange *int   `json:"is_integral_exchange,optional" validate:"oneof=0 1"` // 是否支持积分兑换(0-否，1-是)
	IntegralPrice      *int   `json:"integral_price,optional" validate:"min=0"`           // 积分兑换所需积分
	SortOrder          *int   `json:"sort_order,optional" validate:"min=0"`               // 排序
}
