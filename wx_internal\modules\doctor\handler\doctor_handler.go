package handler

import (
	"fmt"
	"net/http"
	"sort"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"

	"yekaitai/internal/modules/store/model"
	"yekaitai/internal/modules/store/service"
	"yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/response"
)

// DoctorHandler 医生处理器
type DoctorHandler struct {
	tencentMapKey string // 添加腾讯地图API密钥字段
}

// DoctorListRequest 医生列表请求
type DoctorListRequest struct {
	Page          int     `form:"page,default=1"`                    // 页码
	Size          int     `form:"size,default=10"`                   // 每页记录数
	ProvinceID    string  `form:"provinceId,optional"`               // 省份编码
	CityID        string  `form:"cityId,optional"`                   // 城市编码
	AreaID        string  `form:"areaId,optional"`                   // 区县编码
	StoreID       uint    `form:"storeId,optional"`                  // 门店ID
	IsRecommended *bool   `form:"isRecommended,optional"`            // 是否推荐
	Latitude      float64 `form:"latitude,optional"`                 // 用户纬度
	Longitude     float64 `form:"longitude,optional"`                // 用户经度
	MaxDistance   float64 `form:"maxDistance,optional,default=5000"` // 最大距离（米），默认5公里
	SortBy        string  `form:"sortBy,optional,default=distance"`  // 排序方式：distance-距离优先, recommend-推荐优先
}

// DoctorDetailRequest 医生详情请求
type DoctorDetailRequest struct {
	ID uint `path:"id"` // 医生ID
}

// NewDoctorHandler 创建医生处理器
func NewDoctorHandler(tencentMapKey string) *DoctorHandler {
	logx.Infof("创建医生处理器，使用腾讯地图密钥: [%s]", tencentMapKey)
	return &DoctorHandler{
		tencentMapKey: tencentMapKey,
	}
}

// List 获取医生列表
func (h *DoctorHandler) List(w http.ResponseWriter, r *http.Request) {
	var req DoctorListRequest
	if err := httpx.Parse(r, &req); err != nil {
		response.Error(w, response.CodeInvalidParams, "参数解析失败")
		return
	}

	// 参数验证
	if req.Page < 1 {
		req.Page = 1
	}
	if req.Size < 1 || req.Size > 100 {
		req.Size = 10
	}

	// 处理逻辑根据排序方式分开
	if req.SortBy == "recommend" || (req.Latitude == 0 || req.Longitude == 0 || req.MaxDistance <= 0) {
		// 按推荐排序或没有位置信息，使用传统查询方式
		h.getRecommendedDoctors(w, req)
	} else {
		// 按距离排序，采用先排序门店再获取医生的方式
		h.getNearbyDoctors(w, req)
	}
}

// 按推荐优先排序获取医生列表
func (h *DoctorHandler) getRecommendedDoctors(w http.ResponseWriter, req DoctorListRequest) {
	// 构建查询
	db := mysql.Master()
	query := db.Model(&doctor.WxDoctor{}).
		Where("wx_doctor.status = ?", 1) // 只查询正常状态的医生

	// 标记是否已经JOIN了门店关联表
	hasJoinedDSR := false
	hasJoinedStore := false

	// 应用筛选条件
	if req.StoreID > 0 {
		query = query.Joins("JOIN doctor_store_relation ON wx_doctor.doctor_id = doctor_store_relation.doctor_id").
			Where("doctor_store_relation.store_id = ?", req.StoreID)
		hasJoinedDSR = true
	}

	// 如果提供了地区筛选条件，需要通过门店关联查询
	if req.ProvinceID != "" || req.CityID != "" || req.AreaID != "" {
		if !hasJoinedDSR {
			query = query.Joins("JOIN doctor_store_relation ON wx_doctor.doctor_id = doctor_store_relation.doctor_id")
			hasJoinedDSR = true
		}

		if !hasJoinedStore {
			query = query.Joins("JOIN t_stores ON doctor_store_relation.store_id = t_stores.id")
			hasJoinedStore = true
		}

		if req.ProvinceID != "" {
			query = query.Where("t_stores.province_id = ?", req.ProvinceID)
		}
		if req.CityID != "" {
			query = query.Where("t_stores.city_id = ?", req.CityID)
		}
		if req.AreaID != "" {
			query = query.Where("t_stores.area_id = ?", req.AreaID)
		}
	}

	// 推荐医生筛选
	if req.IsRecommended != nil {
		query = query.Where("wx_doctor.is_recommended = ?", *req.IsRecommended)
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logx.Errorf("查询医生总数失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 应用推荐排序
	query = query.Order("wx_doctor.is_recommended DESC, wx_doctor.recommend_order ASC, wx_doctor.doctor_id DESC")

	// 查询医生数据
	var doctors []doctor.WxDoctor
	offset := (req.Page - 1) * req.Size
	if err := query.Offset(offset).Limit(req.Size).Find(&doctors).Error; err != nil {
		logx.Errorf("查询医生列表失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 加载医生关联的门店数据
	for i := range doctors {
		var stores []model.Store
		err := mysql.Master().
			Table("t_stores").
			Joins("JOIN doctor_store_relation ON t_stores.id = doctor_store_relation.store_id").
			Where("doctor_store_relation.doctor_id = ?", doctors[i].DoctorID).
			Find(&stores).Error

		if err != nil {
			logx.Errorf("查询医生[%d]关联门店失败: %v", doctors[i].DoctorID, err)
		} else {
			doctors[i].Stores = stores
		}
	}

	// 构建响应
	response.Success(w, map[string]interface{}{
		"total":   total,
		"current": req.Page,
		"size":    req.Size,
		"records": doctors,
	})
}

// 按门店距离排序获取医生列表
func (h *DoctorHandler) getNearbyDoctors(w http.ResponseWriter, req DoctorListRequest) {
	// 步骤1: 获取符合条件的门店并计算距离
	logx.Info("开始按门店距离获取医生")

	// 构建门店查询条件
	db := mysql.Master()
	storeQuery := db.Model(&model.Store{}).Where("status = ?", 1)

	// 应用地区筛选
	if req.ProvinceID != "" {
		storeQuery = storeQuery.Where("province_id = ?", req.ProvinceID)
	}
	if req.CityID != "" {
		storeQuery = storeQuery.Where("city_id = ?", req.CityID)
	}
	if req.AreaID != "" {
		storeQuery = storeQuery.Where("area_id = ?", req.AreaID)
	}
	if req.StoreID > 0 {
		storeQuery = storeQuery.Where("id = ?", req.StoreID)
	}

	// 获取所有符合条件的门店
	var stores []model.Store
	if err := storeQuery.Find(&stores).Error; err != nil {
		logx.Errorf("查询门店失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 如果没有找到符合条件的门店，直接返回空结果
	if len(stores) == 0 {
		response.Success(w, map[string]interface{}{
			"total":   0,
			"current": req.Page,
			"size":    req.Size,
			"records": []interface{}{},
		})
		return
	}

	// 准备计算门店距离
	var storeLocations []struct {
		ID        uint
		Latitude  float64
		Longitude float64
	}

	// 收集所有门店的位置信息
	for _, store := range stores {
		if store.Latitude != 0 && store.Longitude != 0 {
			storeLocations = append(storeLocations, struct {
				ID        uint
				Latitude  float64
				Longitude float64
			}{
				ID:        store.ID,
				Latitude:  store.Latitude,
				Longitude: store.Longitude,
			})
		}
	}

	// 如果没有有效位置的门店，返回空结果
	if len(storeLocations) == 0 {
		response.Success(w, map[string]interface{}{
			"total":   0,
			"current": req.Page,
			"size":    req.Size,
			"records": []interface{}{},
		})
		return
	}

	// 计算门店距离
	logx.Infof("准备计算%d个门店的距离", len(storeLocations))
	locationService := service.NewLocationService(h.tencentMapKey)
	distanceResults, err := locationService.BatchCalculateDistance(req.Latitude, req.Longitude, storeLocations)

	if err != nil {
		logx.Errorf("计算门店距离失败: %v", err)
		response.Error(w, response.CodeInternalError, "距离计算失败")
		return
	}

	// 构建门店ID与距离的映射，并按距离排序门店
	type StoreWithDistance struct {
		Store    model.Store
		Distance int
	}

	var storesWithDistance []StoreWithDistance
	storeDistances := make(map[uint]int)

	// 为每个门店添加距离信息
	for _, result := range distanceResults {
		storeDistances[result.StoreID] = result.Distance
	}

	// 构建带距离的门店列表
	for _, store := range stores {
		distance, ok := storeDistances[store.ID]
		if !ok {
			// 如果没有距离数据，使用最大距离
			distance = int(req.MaxDistance * 1000)
		}
		storesWithDistance = append(storesWithDistance, StoreWithDistance{
			Store:    store,
			Distance: distance,
		})
	}

	// 按距离排序门店
	sort.Slice(storesWithDistance, func(i, j int) bool {
		return storesWithDistance[i].Distance < storesWithDistance[j].Distance
	})

	// 打印排序后的门店顺序
	logx.Info("门店按距离排序结果:")
	for i, sd := range storesWithDistance {
		logx.Infof("  [%d] 门店ID: %d, 名称: %s, 距离: %d米",
			i+1, sd.Store.ID, sd.Store.Name, sd.Distance)
	}

	// 步骤2: 按照门店距离顺序获取医生
	// 构建按门店ID排序的查询
	var sortedStoreIDs []uint
	for _, sd := range storesWithDistance {
		// 过滤超出最大距离的门店
		if float64(sd.Distance) <= req.MaxDistance*1000 {
			sortedStoreIDs = append(sortedStoreIDs, sd.Store.ID)
		}
	}

	// 如果所有门店都超出距离范围，返回空结果
	if len(sortedStoreIDs) == 0 {
		response.Success(w, map[string]interface{}{
			"total":   0,
			"current": req.Page,
			"size":    req.Size,
			"records": []interface{}{},
		})
		return
	}

	// 统计符合条件的医生总数
	var total int64
	countQuery := db.Table("wx_doctor").
		Joins("JOIN doctor_store_relation ON wx_doctor.doctor_id = doctor_store_relation.doctor_id").
		Where("wx_doctor.status = ? AND doctor_store_relation.store_id IN ?", 1, sortedStoreIDs)

	// 应用推荐筛选
	if req.IsRecommended != nil {
		countQuery = countQuery.Where("wx_doctor.is_recommended = ?", *req.IsRecommended)
	}

	// 统计总数
	if err := countQuery.Distinct("wx_doctor.doctor_id").Count(&total).Error; err != nil {
		logx.Errorf("统计医生总数失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 存储医生ID与其对应门店的关系
	type DoctorStoreRelation struct {
		DoctorID uint
		StoreID  uint
	}

	// 获取每个医生与最近门店的关联关系
	var allDoctorStoreRelations []DoctorStoreRelation
	if err := db.Table("doctor_store_relation").
		Where("store_id IN ?", sortedStoreIDs).
		Find(&allDoctorStoreRelations).Error; err != nil {
		logx.Errorf("查询医生门店关系失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 建立每个医生与他最近门店的映射
	doctorNearestStore := make(map[uint]uint)
	doctorStoreDist := make(map[uint]int)

	// 记录每个医生id到所有关联门店id的映射
	doctorToStores := make(map[uint][]uint)
	for _, rel := range allDoctorStoreRelations {
		doctorToStores[rel.DoctorID] = append(doctorToStores[rel.DoctorID], rel.StoreID)
	}

	// 对每个医生，找到距离最近的门店
	for doctorID, storeIDs := range doctorToStores {
		minDistance := int(req.MaxDistance * 1000)
		var nearestStoreID uint

		for _, storeID := range storeIDs {
			// 找出这个门店在排序列表中的位置
			for _, sd := range storesWithDistance {
				if sd.Store.ID == storeID && sd.Distance < minDistance {
					minDistance = sd.Distance
					nearestStoreID = storeID
				}
			}
		}

		if nearestStoreID > 0 {
			doctorNearestStore[doctorID] = nearestStoreID
			doctorStoreDist[doctorID] = minDistance
		}
	}

	// 获取所有符合条件的医生ID
	var doctorIDs []uint
	doctorQuery := db.Table("wx_doctor").
		Select("wx_doctor.doctor_id").
		Joins("JOIN doctor_store_relation ON wx_doctor.doctor_id = doctor_store_relation.doctor_id").
		Where("wx_doctor.status = ? AND doctor_store_relation.store_id IN ?", 1, sortedStoreIDs)

	if req.IsRecommended != nil {
		doctorQuery = doctorQuery.Where("wx_doctor.is_recommended = ?", *req.IsRecommended)
	}

	if err := doctorQuery.Distinct().Pluck("wx_doctor.doctor_id", &doctorIDs).Error; err != nil {
		logx.Errorf("查询医生ID列表失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 为医生排序：按照其最近门店的距离排序
	sort.Slice(doctorIDs, func(i, j int) bool {
		// 获取两个医生的最近门店距离
		distI, okI := doctorStoreDist[doctorIDs[i]]
		distJ, okJ := doctorStoreDist[doctorIDs[j]]

		if !okI && !okJ {
			return doctorIDs[i] < doctorIDs[j] // 都没有距离时按ID排序
		}
		if !okI {
			return false // i没有距离，j有距离，j更靠前
		}
		if !okJ {
			return true // j没有距离，i有距离，i更靠前
		}

		// 两者都有距离，按距离排序
		return distI < distJ
	})

	// 应用分页
	startIdx := (req.Page - 1) * req.Size
	endIdx := startIdx + req.Size

	if startIdx >= len(doctorIDs) {
		// 页码超出范围，返回空结果
		response.Success(w, map[string]interface{}{
			"total":   total,
			"current": req.Page,
			"size":    req.Size,
			"records": []interface{}{},
		})
		return
	}

	if endIdx > len(doctorIDs) {
		endIdx = len(doctorIDs)
	}

	// 获取当前页的医生ID
	pageDocIDs := doctorIDs[startIdx:endIdx]

	// 查询医生详细信息
	var doctors []doctor.WxDoctor
	if err := db.Where("doctor_id IN ?", pageDocIDs).
		Order(fmt.Sprintf("FIELD(doctor_id, %s)", joinUints(pageDocIDs))).
		Find(&doctors).Error; err != nil {
		logx.Errorf("查询医生详情失败: %v", err)
		response.Error(w, response.CodeInternalError, "查询失败")
		return
	}

	// 加载医生关联的所有门店
	for i := range doctors {
		var stores []model.Store
		err := mysql.Master().
			Table("t_stores").
			Joins("JOIN doctor_store_relation ON t_stores.id = doctor_store_relation.store_id").
			Where("doctor_store_relation.doctor_id = ?", doctors[i].DoctorID).
			Find(&stores).Error

		if err != nil {
			logx.Errorf("查询医生[%d]关联门店失败: %v", doctors[i].DoctorID, err)
		} else {
			doctors[i].Stores = stores

			// 设置医生的距离为其最近门店的距离
			if dist, ok := doctorStoreDist[doctors[i].DoctorID]; ok {
				doctors[i].Distance = float64(dist)
			} else {
				doctors[i].Distance = float64(req.MaxDistance * 1000)
			}
		}
	}

	// 打印最终结果
	logx.Info("按门店距离排序的医生列表:")
	for i, doc := range doctors {
		logx.Infof("  [%d] 医生ID: %d, 名称: %s, 距离: %.2f米",
			i+1, doc.DoctorID, doc.Name, doc.Distance)
	}

	// 构建响应
	response.Success(w, map[string]interface{}{
		"total":   total,
		"current": req.Page,
		"size":    req.Size,
		"records": doctors,
	})
}

// 辅助函数：将uint数组转换为逗号分隔字符串
func joinUints(ids []uint) string {
	strs := make([]string, len(ids))
	for i, id := range ids {
		strs[i] = fmt.Sprintf("%d", id)
	}
	return strings.Join(strs, ",")
}

// Detail 获取医生详情
func (h *DoctorHandler) Detail(w http.ResponseWriter, r *http.Request) {
	var req DoctorDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		response.Error(w, response.CodeInvalidParams, "参数解析失败")
		return
	}

	// 参数验证
	if req.ID <= 0 {
		response.Error(w, response.CodeInvalidParams, "无效的医生ID")
		return
	}

	// 查询医生信息
	var doctorInfo doctor.WxDoctor
	err := mysql.Master().
		Where("doctor_id = ? AND status = ?", req.ID, 1).
		First(&doctorInfo).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(w, response.CodeNotFound, "医生不存在")
		} else {
			logx.Errorf("查询医生详情失败: %v", err)
			response.Error(w, response.CodeInternalError, "查询失败")
		}
		return
	}

	// 单独查询关联的门店
	var stores []model.Store
	err = mysql.Master().
		Table("t_stores").
		Joins("JOIN doctor_store_relation ON t_stores.id = doctor_store_relation.store_id").
		Where("doctor_store_relation.doctor_id = ?", doctorInfo.DoctorID).
		Find(&stores).Error

	if err != nil {
		logx.Errorf("查询医生关联门店失败: %v", err)
	} else {
		doctorInfo.Stores = stores
	}

	// 单独查询关联的标签
	var tags []user.Tag
	err = mysql.Master().
		Table("tag").
		Joins("JOIN doctor_tag ON tag.id = doctor_tag.tag_id").
		Where("doctor_tag.doctor_id = ?", doctorInfo.DoctorID).
		Find(&tags).Error

	if err != nil {
		logx.Errorf("查询医生关联标签失败: %v", err)
	} else {
		doctorInfo.Tags = tags
	}

	// 返回详情
	response.Success(w, doctorInfo)
}
