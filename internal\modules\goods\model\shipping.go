package model

import (
	"time"
)

// ShippingConfig 运费配置(简化版)
type ShippingConfig struct {
	ID          uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:配置ID"`
	ConfigType  int       `json:"config_type" gorm:"not null;comment:配置类型:1-全部包邮,2-满减运费"`
	FreeAmount  float64   `json:"free_amount" gorm:"type:decimal(10,2);default:0;comment:满减金额(配置类型2时使用)"`
	ShippingFee float64   `json:"shipping_fee" gorm:"type:decimal(10,2);default:0;comment:运费金额(配置类型2时使用)"`
	IsActive    int       `json:"is_active" gorm:"type:tinyint(1);default:1;comment:是否启用"`
	Description string    `json:"description" gorm:"type:text;comment:规则描述"`
	CreatedAt   time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"comment:更新时间"`
}

// TableName 表名
func (ShippingConfig) TableName() string {
	return "shipping_config"
}

// ShippingConfigRequest 运费配置请求
type ShippingConfigRequest struct {
	ConfigType  int     `json:"config_type" validate:"required,oneof=1 2"`
	FreeAmount  float64 `json:"free_amount,optional" validate:"min=0"`
	ShippingFee float64 `json:"shipping_fee,optional" validate:"min=0"`
	Description string  `json:"description,optional"`
}

// ShippingCalculateRequest 运费计算请求
type ShippingCalculateRequest struct {
	TotalAmount float64 `json:"total_amount" validate:"required,min=0"`
}

// ShippingCalculateResponse 运费计算响应
type ShippingCalculateResponse struct {
	ShippingFee float64 `json:"shipping_fee"`
	IsFree      bool    `json:"is_free"`
	Message     string  `json:"message"`
	ConfigType  int     `json:"config_type"`
}
