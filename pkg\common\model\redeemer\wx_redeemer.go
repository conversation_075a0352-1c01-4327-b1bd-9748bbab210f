package redeemer

import (
	"time"

	"gorm.io/gorm"
)

// WxRedeemer 核销用户表
type WxRedeemer struct {
	RedeemerID uint           `json:"redeemer_id" gorm:"primaryKey;autoIncrement;comment:核销员ID"`
	UserID     uint           `json:"user_id" gorm:"uniqueIndex;not null;comment:关联微信用户ID"`
	StoreID    uint           `json:"store_id" gorm:"index;comment:门店ID"`
	StoreName  string         `json:"store_name" gorm:"type:varchar(100);comment:门店名称"`
	Status     int            `json:"status" gorm:"default:1;not null;comment:状态(1-正常，0-禁用)"`
	CreatedAt  time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt  time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 设置WxRedeemer表名
func (WxRedeemer) TableName() string {
	return "wx_redeemer"
}
