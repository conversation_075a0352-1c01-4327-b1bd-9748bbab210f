package enum

// Relationship 与本人关系
type Relationship string

const (
	RelationshipSelf   Relationship = "本人"
	RelationshipSpouse Relationship = "配偶"
	RelationshipChild  Relationship = "子女"
	RelationshipParent Relationship = "父母"
	RelationshipOther  Relationship = "其他"
)

// AllRelationships 返回所有关系列表
func AllRelationships() []Relationship {
	return []Relationship{
		RelationshipSelf,
		RelationshipSpouse,
		RelationshipChild,
		RelationshipParent,
		RelationshipOther,
	}
}

// RelationshipMap 返回所有关系的映射，方便前端使用
func RelationshipMap() map[string]string {
	relationships := AllRelationships()
	result := make(map[string]string, len(relationships))

	for _, r := range relationships {
		result[string(r)] = string(r)
	}

	return result
}

// IsValidRelationship 检查指定的关系是否有效
func IsValidRelationship(relationship string) bool {
	for _, r := range AllRelationships() {
		if string(r) == relationship {
			return true
		}
	}
	return false
}

// GetDefaultRelationship 获取默认关系（本人）
func GetDefaultRelationship() string {
	return string(RelationshipSelf)
}
