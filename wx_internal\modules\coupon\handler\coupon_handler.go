package handler

import (
	"net/http"

	"yekaitai/wx_internal/modules/coupon/service"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// CouponGoZeroHandler 小程序端Go-Zero格式的优惠券处理器
type CouponGoZeroHandler struct {
	couponService *service.WxCouponService
}

// NewCouponGoZeroHandler 创建优惠券处理器
func NewCouponGoZeroHandler() *CouponGoZeroHandler {
	return &CouponGoZeroHandler{
		couponService: service.NewWxCouponService(),
	}
}

// BaseResponse 基础响应
type BaseResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// GetAvailableCoupons 获取可领取的优惠券列表
func (h *CouponGoZeroHandler) GetAvailableCoupons(w http.ResponseWriter, r *http.Request) {
	var req service.GetAvailableCouponsRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 400,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 从上下文获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // GetUserIDFromRequest already handles the response
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	result, err := h.couponService.GetAvailableCoupons(r.Context(), &req, userID)
	if err != nil {
		logx.Errorf("获取可领取优惠券列表失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  "获取优惠券列表失败",
			Data: nil,
		})
		return
	}

	httpx.OkJson(w, &BaseResponse{
		Code: 200,
		Msg:  "success",
		Data: result,
	})
}

// ReceiveCoupon 领取优惠券
func (h *CouponGoZeroHandler) ReceiveCoupon(w http.ResponseWriter, r *http.Request) {
	var req struct {
		CouponID uint `path:"id"` // 优惠券ID
	}
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 400,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 从上下文获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // GetUserIDFromRequest already handles the response
	}

	err := h.couponService.ReceiveCoupon(r.Context(), req.CouponID, userID)
	if err != nil {
		logx.Errorf("领取优惠券失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  err.Error(),
			Data: nil,
		})
		return
	}

	httpx.OkJson(w, &BaseResponse{
		Code: 200,
		Msg:  "领取成功",
		Data: nil,
	})
}

// GetUserCoupons 获取用户优惠券列表
func (h *CouponGoZeroHandler) GetUserCoupons(w http.ResponseWriter, r *http.Request) {
	var req service.GetUserCouponsRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 400,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 从上下文获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // GetUserIDFromRequest already handles the response
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	result, err := h.couponService.GetUserCoupons(r.Context(), &req, userID)
	if err != nil {
		logx.Errorf("获取用户优惠券列表失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  "获取优惠券列表失败",
			Data: nil,
		})
		return
	}

	httpx.OkJson(w, &BaseResponse{
		Code: 200,
		Msg:  "success",
		Data: result,
	})
}

// GetUserCouponDetail 获取用户优惠券详情
func (h *CouponGoZeroHandler) GetUserCouponDetail(w http.ResponseWriter, r *http.Request) {
	var req struct {
		UserCouponID uint `path:"id"` // 用户优惠券ID
	}
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 400,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 从上下文获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // GetUserIDFromRequest already handles the response
	}

	result, err := h.couponService.GetUserCouponDetail(r.Context(), req.UserCouponID, userID)
	if err != nil {
		logx.Errorf("获取用户优惠券详情失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  err.Error(),
			Data: nil,
		})
		return
	}

	httpx.OkJson(w, &BaseResponse{
		Code: 200,
		Msg:  "success",
		Data: result,
	})
}

// CheckCouponUsage 检查优惠券是否可用
func (h *CouponGoZeroHandler) CheckCouponUsage(w http.ResponseWriter, r *http.Request) {
	var req service.CheckCouponUsageRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 400,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 从上下文获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // GetUserIDFromRequest already handles the response
	}

	result, err := h.couponService.CheckCouponUsage(r.Context(), &req, userID)
	if err != nil {
		logx.Errorf("检查优惠券可用性失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  "检查优惠券可用性失败",
			Data: nil,
		})
		return
	}

	httpx.OkJson(w, &BaseResponse{
		Code: 200,
		Msg:  "success",
		Data: result,
	})
}

// UseCoupon 使用优惠券
func (h *CouponGoZeroHandler) UseCoupon(w http.ResponseWriter, r *http.Request) {
	var req struct {
		UserCouponID uint `json:"user_coupon_id"` // 用户优惠券ID
		OrderID      uint `json:"order_id"`       // 订单ID
	}
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 400,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 从上下文获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // GetUserIDFromRequest already handles the response
	}

	err := h.couponService.UseCoupon(r.Context(), req.UserCouponID, req.OrderID)
	if err != nil {
		logx.Errorf("使用优惠券失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  err.Error(),
			Data: nil,
		})
		return
	}

	// 验证用户是否有权限使用该优惠券
	userCoupon, err := h.couponService.GetUserCouponDetail(r.Context(), req.UserCouponID, userID)
	if err != nil {
		logx.Errorf("验证用户优惠券权限失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  "验证优惠券权限失败",
			Data: nil,
		})
		return
	}

	if userCoupon == nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 403,
			Msg:  "无权限使用该优惠券",
			Data: nil,
		})
		return
	}

	httpx.OkJson(w, &BaseResponse{
		Code: 200,
		Msg:  "使用成功",
		Data: nil,
	})
}

// GetUsableCouponsForOrder 获取订单可用的优惠券列表
func (h *CouponGoZeroHandler) GetUsableCouponsForOrder(w http.ResponseWriter, r *http.Request) {
	var req struct {
		OrderAmount float64 `form:"order_amount"`         // 订单金额
		ProductIDs  []uint  `form:"product_ids,optional"` // 商品ID列表
		ServiceIDs  []uint  `form:"service_ids,optional"` // 服务ID列表
	}
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 400,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 从上下文获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // GetUserIDFromRequest already handles the response
	}

	result, err := h.couponService.GetUsableCouponsForOrder(r.Context(), userID, req.OrderAmount, req.ProductIDs, req.ServiceIDs)
	if err != nil {
		logx.Errorf("获取订单可用优惠券失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  "获取可用优惠券失败",
			Data: nil,
		})
		return
	}

	httpx.OkJson(w, &BaseResponse{
		Code: 200,
		Msg:  "success",
		Data: result,
	})
}

// AutoSelectBestCoupon 自动选择最优优惠券
func (h *CouponGoZeroHandler) AutoSelectBestCoupon(w http.ResponseWriter, r *http.Request) {
	var req service.AutoSelectBestCouponRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 400,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 从上下文获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // GetUserIDFromRequest already handles the response
	}

	result, err := h.couponService.AutoSelectBestCoupon(r.Context(), &req, userID)
	if err != nil {
		logx.Errorf("自动选择最优优惠券失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 500,
			Msg:  "获取最优优惠券失败",
			Data: nil,
		})
		return
	}

	httpx.OkJson(w, &BaseResponse{
		Code: 200,
		Msg:  "success",
		Data: result,
	})
}
