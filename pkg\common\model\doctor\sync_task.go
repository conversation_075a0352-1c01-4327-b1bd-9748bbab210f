package doctor

import (
	"time"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// 同步任务状态常量
const (
	SyncStatusPending    = 0 // 等待同步
	SyncStatusProcessing = 1 // 同步中
	SyncStatusSuccess    = 2 // 同步成功
	SyncStatusFailed     = 3 // 同步失败
)

// 同步平台类型常量
const (
	SyncPlatformAbcYun = 1 // abcyun平台
	SyncPlatformHzHis  = 2 // 杭州his平台
)

// SyncMessage 同步消息结构
type SyncMessage struct {
	TaskID    uint  `json:"task_id"`   // 同步任务ID
	DoctorID  uint  `json:"doctor_id"` // 医生ID
	Platform  int   `json:"platform"`  // 平台类型
	Type      int   `json:"type"`      // 同步类型
	Timestamp int64 `json:"timestamp"` // 时间戳
}

// DoctorSyncTask 医生同步任务
type DoctorSyncTask struct {
	ID         uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:同步任务ID"`
	DoctorID   uint           `json:"doctor_id" gorm:"index;comment:医生ID"`
	Nickname   string         `json:"nickname" gorm:"type:varchar(50);comment:医生姓名"`
	Type       int            `json:"type" gorm:"comment:同步类型(1-医生信息,2-排班信息)"`
	Platform   int            `json:"platform" gorm:"comment:同步平台(1-abcyun,2-杭州his)"`
	Status     int            `json:"status" gorm:"default:0;comment:状态(0-等待同步,1-同步中,2-同步成功,3-同步失败)"`
	ErrorMsg   string         `json:"error_msg" gorm:"type:text;comment:错误信息"`
	SyncAt     *time.Time     `json:"sync_at" gorm:"comment:同步时间"`
	OperatorID uint           `json:"operator_id" gorm:"comment:操作人ID"`
	CreatedAt  time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt  time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 设置DoctorSyncTask表名
func (DoctorSyncTask) TableName() string {
	return "doctor_sync_task"
}

// DoctorSyncTaskRepository 医生同步任务仓库接口
type DoctorSyncTaskRepository interface {
	// 创建同步任务
	Create(task *DoctorSyncTask) error
	// 更新同步任务
	Update(task *DoctorSyncTask) error
	// 根据ID查找同步任务
	FindByID(id uint) (*DoctorSyncTask, error)
	// 根据医生ID查找最近的同步任务
	FindLatestByDoctorID(doctorID uint, platform int) (*DoctorSyncTask, error)
	// 获取同步任务列表
	List(page, size int, doctorID uint, platform int, status int) ([]*DoctorSyncTask, int64, error)
	// 更新同步任务状态
	UpdateStatus(id uint, status int, errorMsg string) error
	// 批量创建同步任务
	BatchCreate(tasks []*DoctorSyncTask) error
}

// doctorSyncTaskRepository 医生同步任务仓库实现
type doctorSyncTaskRepository struct {
	db *gorm.DB // 保留db字段以兼容接口，但不在方法中使用
}

// NewDoctorSyncTaskRepository 创建医生同步任务仓库
func NewDoctorSyncTaskRepository(db *gorm.DB) DoctorSyncTaskRepository {
	return &doctorSyncTaskRepository{
		db: db,
	}
}

// Create 创建同步任务
func (r *doctorSyncTaskRepository) Create(task *DoctorSyncTask) error {
	return mysql.Master().Create(task).Error
}

// BatchCreate 批量创建同步任务
func (r *doctorSyncTaskRepository) BatchCreate(tasks []*DoctorSyncTask) error {
	if len(tasks) == 0 {
		return nil
	}
	return mysql.Master().Create(&tasks).Error
}

// Update 更新同步任务
func (r *doctorSyncTaskRepository) Update(task *DoctorSyncTask) error {
	return mysql.Master().Save(task).Error
}

// FindByID 根据ID查找同步任务
func (r *doctorSyncTaskRepository) FindByID(id uint) (*DoctorSyncTask, error) {
	var task DoctorSyncTask
	err := mysql.Slave().Where("id = ?", id).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// FindLatestByDoctorID 根据医生ID查找最近的同步任务
func (r *doctorSyncTaskRepository) FindLatestByDoctorID(doctorID uint, platform int) (*DoctorSyncTask, error) {
	var task DoctorSyncTask
	query := mysql.Slave().Where("doctor_id = ?", doctorID)

	if platform > 0 {
		query = query.Where("platform = ?", platform)
	}

	err := query.Order("id DESC").First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// List 获取同步任务列表
func (r *doctorSyncTaskRepository) List(page, size int, doctorID uint, platform int, status int) ([]*DoctorSyncTask, int64, error) {
	// 计算偏移量
	offset := (page - 1) * size
	if offset < 0 {
		offset = 0
	}

	// 基础查询
	db := mysql.Slave().Model(&DoctorSyncTask{})

	// 添加查询条件
	if doctorID > 0 {
		db = db.Where("doctor_id = ?", doctorID)
	}

	if platform > 0 {
		db = db.Where("platform = ?", platform)
	}

	if status >= 0 {
		db = db.Where("status = ?", status)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	var tasks []*DoctorSyncTask
	if err := db.Order("id DESC").Offset(offset).Limit(size).Find(&tasks).Error; err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// UpdateStatus 更新同步任务状态
func (r *doctorSyncTaskRepository) UpdateStatus(id uint, status int, errorMsg string) error {
	updates := map[string]interface{}{
		"status":     status,
		"error_msg":  errorMsg,
		"updated_at": time.Now(),
	}

	// 如果是完成状态，添加同步时间
	if status == SyncStatusSuccess || status == SyncStatusFailed {
		now := time.Now()
		updates["sync_at"] = &now
	}

	return mysql.Master().Model(&DoctorSyncTask{}).
		Where("id = ?", id).
		Updates(updates).Error
}
