package abcyun

import (
	"encoding/json"
	"fmt"
	"strconv"
)

// ExecuteSheetSummary 执行单摘要信息
type ExecuteSheetSummary struct {
	ID             string         `json:"id"`
	PatientOrderId string         `json:"patientOrderId"`
	Status         int            `json:"status"`
	Patient        PatientSummary `json:"patient"`
	AbstractInfo   string         `json:"abstractInfo"`
	Created        string         `json:"created"`
}

// ExecuteQueryByDateResponse 按天查询执行单列表响应
type ExecuteQueryByDateResponse struct {
	ExecuteSheets []ExecuteSheetSummary `json:"executeSheets"`
}

// ExecuteItemProductInfo 执行项目产品信息
type ExecuteItemProductInfo struct {
	Children       []ExecuteItemProductInfo `json:"children,omitempty"`
	ID             string                   `json:"id"`
	Name           string                   `json:"name"`
	<PERSON><PERSON>         Shebao                   `json:"shebao"`
	DisableSell    int                      `json:"disableSell"`
	TypeId         int                      `json:"typeId"`
	TypeName       string                   `json:"typeName"`
	CustomTypeId   string                   `json:"customTypeId"`
	CustomTypeName string                   `json:"customTypeName"`
	Manufacturer   string                   `json:"manufacturer"`
	MedicineNmpn   string                   `json:"medicineNmpn"`
	BarCode        string                   `json:"barCode"`
	PieceUnit      string                   `json:"pieceUnit"`
	PieceNum       float64                  `json:"pieceNum"`
	PiecePrice     float64                  `json:"piecePrice"`
	PackageUnit    string                   `json:"packageUnit"`
	PackagePrice   float64                  `json:"packagePrice"`
	ShortId        string                   `json:"shortId"`
	Specification  string                   `json:"specification"`
	OtcType        int                      `json:"otcType"`
	Status         int                      `json:"status"`
	LastModified   string                   `json:"lastModified"`
	GoodsSpu       GoodsSpu                 `json:"goodsSpu"`
	GoodsSpec      GoodsSpec                `json:"goodsSpec"`
}

// GoodsSpu 商品SPU信息
type GoodsSpu struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	BrandName string `json:"brandName"`
	Material  string `json:"material"`
}

// GoodsSpec 商品规格信息
type GoodsSpec struct {
	Color string `json:"color"`
	Spec  string `json:"spec"`
}

// Shebao 社保信息
type Shebao struct {
	NationalCode   string          `json:"nationalCode"`
	InsuranceTypes []InsuranceType `json:"insuranceTypes"`
}

// InsuranceType 保险类型
type InsuranceType struct {
	InsuranceType      string  `json:"insuranceType"`
	ReimbursementRatio float64 `json:"reimbursementRatio"`
}

// ExecuteDoItem 执行项目
type ExecuteDoItem struct {
	ID           string  `json:"id"`           // 执行项目ID
	ExecuteCount float64 `json:"executeCount"` // 执行数量
}

// ExecuteItem 执行项目信息
type ExecuteItem struct {
	ID                string                 `json:"id"`
	NeedExecutive     int                    `json:"needExecutive"`
	ChargeFormId      string                 `json:"chargeFormId"`
	ExecutedUnitCount float64                `json:"executedUnitCount"`
	ExecuteStatus     int                    `json:"executeStatus"`
	ProductId         string                 `json:"productId"`
	ProductName       string                 `json:"productName"`
	Status            int                    `json:"status"`
	Unit              string                 `json:"unit"`
	ReceivedFee       float64                `json:"receivedFee"`
	UnitPrice         float64                `json:"unitPrice"`
	DisplayUnitPrice  float64                `json:"displayUnitPrice"`
	DisplayTotalPrice float64                `json:"displayTotalPrice"`
	UnitCount         float64                `json:"unitCount"`
	DoseCount         float64                `json:"doseCount"`
	TotalCount        float64                `json:"totalCount"`
	ProductType       int                    `json:"productType"`
	ProductSubType    int                    `json:"productSubType"`
	SourceItemType    int                    `json:"sourceItemType"`
	ProductInfo       ExecuteItemProductInfo `json:"productInfo"`
	ComposeChildren   []ExecuteItem          `json:"composeChildren"`
}

// ExecuteDetail 执行单详情
type ExecuteDetail struct {
	ID             string        `json:"id"`
	PatientOrderId string        `json:"patientOrderId"`
	Items          []ExecuteItem `json:"items"`
	Status         int           `json:"status"`
	Created        string        `json:"created"`
	SellerId       string        `json:"sellerId"`
	SellerName     string        `json:"sellerName"`
	DoctorId       string        `json:"doctorId"`
	DoctorName     string        `json:"doctorName"`
	Diagnosis      string        `json:"diagnosis"`
}

// OperationResult 操作响应结果
type OperationResult struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Executor 执行人
type Executor struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// ExecuteRecordItem 执行记录项目
type ExecuteRecordItem struct {
	ID                     int    `json:"id"`
	ExecuteItemId          string `json:"executeItemId"`
	ExecuteItemProductId   string `json:"executeItemProductId"`
	ExecuteItemProductName string `json:"executeItemProductName"`
	ExecuteCount           int    `json:"executeCount"`
}

// ExecuteRecord 执行记录
type ExecuteRecord struct {
	ID          string              `json:"id"`
	Status      int                 `json:"status"`
	Executors   []Executor          `json:"executors"`
	Items       []ExecuteRecordItem `json:"items"`
	ExecuteTime string              `json:"executeTime"`
	Operator    Executor            `json:"operator"`
}

// ExecuteRecordResponse 查询执行单执行记录响应
type ExecuteRecordResponse struct {
	ExecuteRecords []ExecuteRecord `json:"executeRecords"`
}

// PatientExecuteItem 患者执行单项目信息
type PatientExecuteItem struct {
	ID                string  `json:"id"`
	ProductId         string  `json:"productId"`
	ProductName       string  `json:"productName"`
	ChargeStatus      int     `json:"chargeStatus"`
	TotalCount        float64 `json:"totalCount"`
	ExecutedUnitCount float64 `json:"executedUnitCount"`
}

// PatientExecuteInfo 患者执行单信息
type PatientExecuteInfo struct {
	ID         string               `json:"id"`
	Status     int                  `json:"status"`
	Created    string               `json:"created"`
	Items      []PatientExecuteItem `json:"items"`
	SellerId   string               `json:"sellerId"`
	SellerName string               `json:"sellerName"`
}

// PaginatedPatientExecuteResult 分页患者执行单结果
type PaginatedPatientExecuteResult struct {
	Rows   []PatientExecuteInfo `json:"rows"`
	Total  int                  `json:"total"`
	Offset int                  `json:"offset"`
	Limit  int                  `json:"limit"`
}

// GetExecuteListByDate 按天查询执行单列表
func (c *AbcYunClient) GetExecuteListByDate(date string) (*Response, error) {
	// 构建查询参数
	queryParams := map[string]string{
		"date": date,
	}

	// 构建API路径
	path := "/api/v2/open-agency/execute/query-by-date"

	// 发送请求
	response, err := c.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("按天查询执行单列表失败: %w", err)
	}

	// 解析响应
	var result Response
	var executeQueryResult ExecuteQueryByDateResponse

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &executeQueryResult)
	if err != nil {
		return nil, fmt.Errorf("解析执行单列表失败: %w", err)
	}

	// 将解析后的执行单列表赋值回result.Data
	result.Data = executeQueryResult

	return &result, nil
}

// GetExecuteDetail 获取执行单详情
func (c *AbcYunClient) GetExecuteDetail(id string) (*Response, error) {
	// 构建API路径
	path := fmt.Sprintf("/api/v2/open-agency/execute/%s", id)

	// 发送请求
	response, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("获取执行单详情失败: %w", err)
	}

	// 解析响应
	var result Response
	var executeDetail ExecuteDetail

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &executeDetail)
	if err != nil {
		return nil, fmt.Errorf("解析执行单详情失败: %w", err)
	}

	// 将解析后的执行单详情赋值回result.Data
	result.Data = executeDetail

	return &result, nil
}

// DoExecute 执行
func (c *AbcYunClient) DoExecute(id string, items []ExecuteDoItem, executorIds []string, operatorId string, comment string) (*Response, error) {
	// 构建请求体
	requestBody := map[string]interface{}{
		"items":       items,
		"executorIds": executorIds,
		"operatorId":  operatorId,
		"comment":     comment,
	}

	// 构建API路径
	path := fmt.Sprintf("/api/v2/open-agency/execute/%s/do", id)

	// 发送请求
	response, err := c.Post(path, requestBody)
	if err != nil {
		return nil, fmt.Errorf("执行失败: %w", err)
	}

	// 解析响应
	var result Response
	var operationResult OperationResult

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &operationResult)
	if err != nil {
		return nil, fmt.Errorf("解析操作结果失败: %w", err)
	}

	// 将解析后的操作结果赋值回result.Data
	result.Data = operationResult

	return &result, nil
}

// GetExecuteRecord 查询执行单执行历史
func (c *AbcYunClient) GetExecuteRecord(id string) (*Response, error) {
	// 构建API路径
	path := fmt.Sprintf("/api/v2/open-agency/execute/%s/record", id)

	// 发送请求
	response, err := c.Get(path, nil)
	if err != nil {
		return nil, fmt.Errorf("查询执行单执行历史失败: %w", err)
	}

	// 解析响应
	var result Response
	var executeRecordResponse ExecuteRecordResponse

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &executeRecordResponse)
	if err != nil {
		return nil, fmt.Errorf("解析执行单执行历史失败: %w", err)
	}

	// 将解析后的执行单执行历史赋值回result.Data
	result.Data = executeRecordResponse

	return &result, nil
}

// UndoExecute 撤销执行
func (c *AbcYunClient) UndoExecute(id string, executeRecordId string, operatorId string) (*Response, error) {
	// 构建请求体
	requestBody := map[string]interface{}{
		"operatorId": operatorId,
	}

	// 构建API路径
	path := fmt.Sprintf("/api/v2/open-agency/execute/%s/record/%s/undo", id, executeRecordId)

	// 发送请求
	response, err := c.Post(path, requestBody)
	if err != nil {
		return nil, fmt.Errorf("撤销执行失败: %w", err)
	}

	// 解析响应
	var result Response
	var operationResult OperationResult

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &operationResult)
	if err != nil {
		return nil, fmt.Errorf("解析操作结果失败: %w", err)
	}

	// 将解析后的操作结果赋值回result.Data
	result.Data = operationResult

	return &result, nil
}

// GetPatientExecuteList 查询患者执行单列表
func (c *AbcYunClient) GetPatientExecuteList(patientId string, beginDate string, endDate string, executeStatus int, limit int, offset int) (*Response, error) {
	// 构建查询参数
	queryParams := map[string]string{}

	if beginDate != "" {
		queryParams["beginDate"] = beginDate
	}

	if endDate != "" {
		queryParams["endDate"] = endDate
	}

	if executeStatus > 0 {
		queryParams["executeStatus"] = strconv.Itoa(executeStatus)
	}

	queryParams["limit"] = strconv.Itoa(limit)
	queryParams["offset"] = strconv.Itoa(offset)

	// 构建API路径
	path := fmt.Sprintf("/api/v2/open-agency/execute/patient/%s", patientId)

	// 发送请求
	response, err := c.Get(path, queryParams)
	if err != nil {
		return nil, fmt.Errorf("查询患者执行单列表失败: %w", err)
	}

	// 解析响应
	var result Response
	var paginatedResult PaginatedPatientExecuteResult

	// 先解析整体结构
	err = json.Unmarshal(response, &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 解析data部分
	dataBytes, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("转换响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &paginatedResult)
	if err != nil {
		return nil, fmt.Errorf("解析患者执行单列表失败: %w", err)
	}

	// 将解析后的患者执行单列表赋值回result.Data
	result.Data = paginatedResult

	return &result, nil
}
