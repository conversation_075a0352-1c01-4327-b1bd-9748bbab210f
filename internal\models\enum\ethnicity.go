package enum

// Ethnicity 中国56个民族
type Ethnicity string

const (
	EthnicityHan       Ethnicity = "汉族"
	EthnicityMongol    Ethnicity = "蒙古族"
	EthnicityHui       Ethnicity = "回族"
	EthnicityTibetan   Ethnicity = "藏族"
	EthnicityUyghur    Ethnicity = "维吾尔族"
	EthnicityMiao      Ethnicity = "苗族"
	EthnicityYi        Ethnicity = "彝族"
	EthnicityZhuang    Ethnicity = "壮族"
	EthnicityBuyi      Ethnicity = "布依族"
	EthnicityKorean    Ethnicity = "朝鲜族"
	EthnicityManchu    Ethnicity = "满族"
	EthnicityDong      Ethnicity = "侗族"
	EthnicityYao       Ethnicity = "瑶族"
	EthnicityBai       Ethnicity = "白族"
	EthnicityTujia     Ethnicity = "土家族"
	EthnicityHani      Ethnicity = "哈尼族"
	EthnicityCasack    Ethnicity = "哈萨克族"
	EthnicityDai       Ethnicity = "傣族"
	EthnicityLi        Ethnicity = "黎族"
	EthnicityLisu      Ethnicity = "傈僳族"
	EthnicityWa        Ethnicity = "佤族"
	EthnicityShe       Ethnicity = "畲族"
	EthnicityGaoshan   Ethnicity = "高山族"
	EthnicityLahu      Ethnicity = "拉祜族"
	EthnicitySui       Ethnicity = "水族"
	EthnicityDongxiang Ethnicity = "东乡族"
	EthnicityNakhi     Ethnicity = "纳西族"
	EthnicityJingpo    Ethnicity = "景颇族"
	EthnicityKyrgyz    Ethnicity = "柯尔克孜族"
	EthnicityTu        Ethnicity = "土族"
	EthnicityDaur      Ethnicity = "达斡尔族"
	EthnicityMulao     Ethnicity = "仫佬族"
	EthnicityQiang     Ethnicity = "羌族"
	EthnicityBlang     Ethnicity = "布朗族"
	EthnicitySalar     Ethnicity = "撒拉族"
	EthnicityMaonan    Ethnicity = "毛南族"
	EthnicityGelao     Ethnicity = "仡佬族"
	EthnicityXibe      Ethnicity = "锡伯族"
	EthnicityAchang    Ethnicity = "阿昌族"
	EthnicityPumi      Ethnicity = "普米族"
	EthnicityTajik     Ethnicity = "塔吉克族"
	EthnicityNu        Ethnicity = "怒族"
	EthnicityUzbek     Ethnicity = "乌孜别克族"
	EthnicityRussian   Ethnicity = "俄罗斯族"
	EthnicityEvenki    Ethnicity = "鄂温克族"
	EthnicityDeang     Ethnicity = "德昂族"
	EthnicityBaoan     Ethnicity = "保安族"
	EthnicityYugur     Ethnicity = "裕固族"
	EthnicityJing      Ethnicity = "京族"
	EthnicityTatar     Ethnicity = "塔塔尔族"
	EthnicityDulongzu  Ethnicity = "独龙族"
	EthnicityOroqen    Ethnicity = "鄂伦春族"
	EthnicityHezhen    Ethnicity = "赫哲族"
	EthnicityMonba     Ethnicity = "门巴族"
	EthnicityLhoba     Ethnicity = "珞巴族"
	EthnicityJino      Ethnicity = "基诺族"
	EthnicityOther     Ethnicity = "其它民族或外国人"
)

// EthnicityCode 民族编码
type EthnicityCode string

const (
	EthnicityCodeHan       EthnicityCode = "01"
	EthnicityCodeMongol    EthnicityCode = "02"
	EthnicityCodeHui       EthnicityCode = "03"
	EthnicityCodeTibetan   EthnicityCode = "04"
	EthnicityCodeUyghur    EthnicityCode = "05"
	EthnicityCodeMiao      EthnicityCode = "06"
	EthnicityCodeYi        EthnicityCode = "07"
	EthnicityCodeZhuang    EthnicityCode = "08"
	EthnicityCodeBuyi      EthnicityCode = "09"
	EthnicityCodeKorean    EthnicityCode = "10"
	EthnicityCodeManchu    EthnicityCode = "11"
	EthnicityCodeDong      EthnicityCode = "12"
	EthnicityCodeYao       EthnicityCode = "13"
	EthnicityCodeBai       EthnicityCode = "14"
	EthnicityCodeTujia     EthnicityCode = "15"
	EthnicityCodeHani      EthnicityCode = "16"
	EthnicityCodeCasack    EthnicityCode = "17"
	EthnicityCodeDai       EthnicityCode = "18"
	EthnicityCodeLi        EthnicityCode = "19"
	EthnicityCodeLisu      EthnicityCode = "20"
	EthnicityCodeWa        EthnicityCode = "21"
	EthnicityCodeShe       EthnicityCode = "22"
	EthnicityCodeGaoshan   EthnicityCode = "23"
	EthnicityCodeLahu      EthnicityCode = "24"
	EthnicityCodeSui       EthnicityCode = "25"
	EthnicityCodeDongxiang EthnicityCode = "26"
	EthnicityCodeNakhi     EthnicityCode = "27"
	EthnicityCodeJingpo    EthnicityCode = "28"
	EthnicityCodeKyrgyz    EthnicityCode = "29"
	EthnicityCodeTu        EthnicityCode = "30"
	EthnicityCodeDaur      EthnicityCode = "31"
	EthnicityCodeMulao     EthnicityCode = "32"
	EthnicityCodeQiang     EthnicityCode = "33"
	EthnicityCodeBlang     EthnicityCode = "34"
	EthnicityCodeSalar     EthnicityCode = "35"
	EthnicityCodeMaonan    EthnicityCode = "36"
	EthnicityCodeGelao     EthnicityCode = "37"
	EthnicityCodeXibe      EthnicityCode = "38"
	EthnicityCodeAchang    EthnicityCode = "39"
	EthnicityCodePumi      EthnicityCode = "40"
	EthnicityCodeTajik     EthnicityCode = "41"
	EthnicityCodeNu        EthnicityCode = "42"
	EthnicityCodeUzbek     EthnicityCode = "43"
	EthnicityCodeRussian   EthnicityCode = "44"
	EthnicityCodeEvenki    EthnicityCode = "45"
	EthnicityCodeDeang     EthnicityCode = "46"
	EthnicityCodeBaoan     EthnicityCode = "47"
	EthnicityCodeYugur     EthnicityCode = "48"
	EthnicityCodeJing      EthnicityCode = "49"
	EthnicityCodeTatar     EthnicityCode = "50"
	EthnicityCodeDulongzu  EthnicityCode = "51"
	EthnicityCodeOroqen    EthnicityCode = "52"
	EthnicityCodeHezhen    EthnicityCode = "53"
	EthnicityCodeMonba     EthnicityCode = "54"
	EthnicityCodeLhoba     EthnicityCode = "55"
	EthnicityCodeJino      EthnicityCode = "56"
	EthnicityCodeOther     EthnicityCode = "99"
)

// EthnicityInfo 民族信息
type EthnicityInfo struct {
	Code EthnicityCode `json:"code"` // 编码
	Name Ethnicity     `json:"name"` // 名称
}

// ethnicityCodeMap 民族名称到编码的映射
var ethnicityCodeMap = map[Ethnicity]EthnicityCode{
	EthnicityHan:       EthnicityCodeHan,
	EthnicityMongol:    EthnicityCodeMongol,
	EthnicityHui:       EthnicityCodeHui,
	EthnicityTibetan:   EthnicityCodeTibetan,
	EthnicityUyghur:    EthnicityCodeUyghur,
	EthnicityMiao:      EthnicityCodeMiao,
	EthnicityYi:        EthnicityCodeYi,
	EthnicityZhuang:    EthnicityCodeZhuang,
	EthnicityBuyi:      EthnicityCodeBuyi,
	EthnicityKorean:    EthnicityCodeKorean,
	EthnicityManchu:    EthnicityCodeManchu,
	EthnicityDong:      EthnicityCodeDong,
	EthnicityYao:       EthnicityCodeYao,
	EthnicityBai:       EthnicityCodeBai,
	EthnicityTujia:     EthnicityCodeTujia,
	EthnicityHani:      EthnicityCodeHani,
	EthnicityCasack:    EthnicityCodeCasack,
	EthnicityDai:       EthnicityCodeDai,
	EthnicityLi:        EthnicityCodeLi,
	EthnicityLisu:      EthnicityCodeLisu,
	EthnicityWa:        EthnicityCodeWa,
	EthnicityShe:       EthnicityCodeShe,
	EthnicityGaoshan:   EthnicityCodeGaoshan,
	EthnicityLahu:      EthnicityCodeLahu,
	EthnicitySui:       EthnicityCodeSui,
	EthnicityDongxiang: EthnicityCodeDongxiang,
	EthnicityNakhi:     EthnicityCodeNakhi,
	EthnicityJingpo:    EthnicityCodeJingpo,
	EthnicityKyrgyz:    EthnicityCodeKyrgyz,
	EthnicityTu:        EthnicityCodeTu,
	EthnicityDaur:      EthnicityCodeDaur,
	EthnicityMulao:     EthnicityCodeMulao,
	EthnicityQiang:     EthnicityCodeQiang,
	EthnicityBlang:     EthnicityCodeBlang,
	EthnicitySalar:     EthnicityCodeSalar,
	EthnicityMaonan:    EthnicityCodeMaonan,
	EthnicityGelao:     EthnicityCodeGelao,
	EthnicityXibe:      EthnicityCodeXibe,
	EthnicityAchang:    EthnicityCodeAchang,
	EthnicityPumi:      EthnicityCodePumi,
	EthnicityTajik:     EthnicityCodeTajik,
	EthnicityNu:        EthnicityCodeNu,
	EthnicityUzbek:     EthnicityCodeUzbek,
	EthnicityRussian:   EthnicityCodeRussian,
	EthnicityEvenki:    EthnicityCodeEvenki,
	EthnicityDeang:     EthnicityCodeDeang,
	EthnicityBaoan:     EthnicityCodeBaoan,
	EthnicityYugur:     EthnicityCodeYugur,
	EthnicityJing:      EthnicityCodeJing,
	EthnicityTatar:     EthnicityCodeTatar,
	EthnicityDulongzu:  EthnicityCodeDulongzu,
	EthnicityOroqen:    EthnicityCodeOroqen,
	EthnicityHezhen:    EthnicityCodeHezhen,
	EthnicityMonba:     EthnicityCodeMonba,
	EthnicityLhoba:     EthnicityCodeLhoba,
	EthnicityJino:      EthnicityCodeJino,
	EthnicityOther:     EthnicityCodeOther,
}

// GetEthnicityCode 根据民族名称获取编码
func GetEthnicityCode(ethnicity Ethnicity) EthnicityCode {
	if code, exists := ethnicityCodeMap[ethnicity]; exists {
		return code
	}
	return EthnicityCodeOther
}

// GetEthnicityInfo 根据民族名称获取民族信息
func GetEthnicityInfo(ethnicity Ethnicity) EthnicityInfo {
	return EthnicityInfo{
		Code: GetEthnicityCode(ethnicity),
		Name: ethnicity,
	}
}

// GetAllEthnicityInfos 获取所有民族信息列表（按编码顺序）
func GetAllEthnicityInfos() []EthnicityInfo {
	// 按编码顺序排列的民族列表
	orderedEthnicities := []Ethnicity{
		EthnicityHan, EthnicityMongol, EthnicityHui, EthnicityTibetan, EthnicityUyghur,
		EthnicityMiao, EthnicityYi, EthnicityZhuang, EthnicityBuyi, EthnicityKorean,
		EthnicityManchu, EthnicityDong, EthnicityYao, EthnicityBai, EthnicityTujia,
		EthnicityHani, EthnicityCasack, EthnicityDai, EthnicityLi, EthnicityLisu,
		EthnicityWa, EthnicityShe, EthnicityGaoshan, EthnicityLahu, EthnicitySui,
		EthnicityDongxiang, EthnicityNakhi, EthnicityJingpo, EthnicityKyrgyz, EthnicityTu,
		EthnicityDaur, EthnicityMulao, EthnicityQiang, EthnicityBlang, EthnicitySalar,
		EthnicityMaonan, EthnicityGelao, EthnicityXibe, EthnicityAchang, EthnicityPumi,
		EthnicityTajik, EthnicityNu, EthnicityUzbek, EthnicityRussian, EthnicityEvenki,
		EthnicityDeang, EthnicityBaoan, EthnicityYugur, EthnicityJing, EthnicityTatar,
		EthnicityDulongzu, EthnicityOroqen, EthnicityHezhen, EthnicityMonba, EthnicityLhoba,
		EthnicityJino, EthnicityOther,
	}

	var infos []EthnicityInfo
	for _, ethnicity := range orderedEthnicities {
		infos = append(infos, GetEthnicityInfo(ethnicity))
	}
	return infos
}

// AllEthnicities 返回所有民族列表（按编码顺序）
func AllEthnicities() []Ethnicity {
	return []Ethnicity{
		EthnicityHan, EthnicityMongol, EthnicityHui, EthnicityTibetan, EthnicityUyghur,
		EthnicityMiao, EthnicityYi, EthnicityZhuang, EthnicityBuyi, EthnicityKorean,
		EthnicityManchu, EthnicityDong, EthnicityYao, EthnicityBai, EthnicityTujia,
		EthnicityHani, EthnicityCasack, EthnicityDai, EthnicityLi, EthnicityLisu,
		EthnicityWa, EthnicityShe, EthnicityGaoshan, EthnicityLahu, EthnicitySui,
		EthnicityDongxiang, EthnicityNakhi, EthnicityJingpo, EthnicityKyrgyz, EthnicityTu,
		EthnicityDaur, EthnicityMulao, EthnicityQiang, EthnicityBlang, EthnicitySalar,
		EthnicityMaonan, EthnicityGelao, EthnicityXibe, EthnicityAchang, EthnicityPumi,
		EthnicityTajik, EthnicityNu, EthnicityUzbek, EthnicityRussian, EthnicityEvenki,
		EthnicityDeang, EthnicityBaoan, EthnicityYugur, EthnicityJing, EthnicityTatar,
		EthnicityDulongzu, EthnicityOroqen, EthnicityHezhen, EthnicityMonba, EthnicityLhoba,
		EthnicityJino, EthnicityOther,
	}
}

// EthnicityMap 返回所有民族的映射，方便前端使用
func EthnicityMap() map[string]string {
	ethnicities := AllEthnicities()
	result := make(map[string]string, len(ethnicities))

	for _, e := range ethnicities {
		result[string(e)] = string(e)
	}

	return result
}

// IsValidEthnicity 检查指定的民族是否有效
func IsValidEthnicity(ethnicity string) bool {
	for _, e := range AllEthnicities() {
		if string(e) == ethnicity {
			return true
		}
	}
	return false
}

// GetDefaultEthnicity 获取默认民族（汉族）
func GetDefaultEthnicity() string {
	return string(EthnicityHan)
}
