package doctor

import (
	"time"
	"yekaitai/internal/modules/store/model"
	"yekaitai/pkg/common/model/user"

	"gorm.io/gorm"
)

// WxDoctor 医生信息表
type WxDoctor struct {
	DoctorID       uint           `json:"doctor_id" gorm:"primaryKey;autoIncrement;comment:医生ID"`
	UserID         uint           `json:"user_id" gorm:"comment:关联微信用户ID"`
	Name           string         `json:"name" gorm:"type:varchar(50);comment:医生姓名"`
	Mobile         string         `json:"mobile" gorm:"type:varchar(20);comment:医生手机号"`
	YsID           string         `json:"ysid" gorm:"type:varchar(50);comment:医生在HIS系统中的卫生人员ID"`
	MedicalLicense string         `json:"medical_license" gorm:"type:varchar(50);comment:卫生人员类别代码"`
	Specialty      string         `json:"specialty" gorm:"type:text;comment:擅长领域"`
	Hospital       string         `json:"hospital" gorm:"type:varchar(100);comment:所属医院"`
	Department     string         `json:"department" gorm:"type:varchar(50);comment:科室"`
	Title          string         `json:"title" gorm:"type:varchar(50);comment:职称"`
	WsrylbMC       string         `json:"wsrylbmc" gorm:"type:varchar(50);comment:卫生人员类别名称"`
	Introduction   string         `json:"introduction" gorm:"type:text;comment:简介"`
	IsRecommended  bool           `json:"is_recommended" gorm:"default:false;comment:是否推荐"`
	RecommendOrder int            `json:"recommend_order" gorm:"default:0;comment:推荐排序，从1开始，数字越小越靠前"`
	HeadImgUrl     string         `json:"head_img_url" gorm:"type:varchar(500);comment:头像URL"`
	PracticeImgUrl string         `json:"practice_img_url" gorm:"type:varchar(500);comment:执业照片URL"`
	DoctorTags     string         `json:"doctor_tags" gorm:"type:text;comment:医生标签"`
	PracticeInfos  string         `json:"practice_infos" gorm:"type:text;comment:职称信息JSON"`
	CreatorID      uint           `json:"creator_id" gorm:"comment:创建人ID"`
	Status         int            `json:"status" gorm:"default:1;not null;comment:状态(1-正常，0-禁用)"`
	JgksID         string         `json:"jgks_id" gorm:"type:varchar(50);comment:机构科室ID"`
	WsjgID         string         `json:"wsjg_id" gorm:"type:varchar(50);comment:卫生机构ID"`
	CreatedAt      time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt      time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
	Tags           []user.Tag     `json:"tags" gorm:"-"`     // 手动管理，不使用many2many
	Stores         []model.Store  `json:"stores" gorm:"-"`   // 手动管理，不使用many2many
	Distance       float64        `json:"distance" gorm:"-"` // 与用户的距离（米），不存储到数据库
}

// TableName 设置WxDoctor表名
func (WxDoctor) TableName() string {
	return "wx_doctor"
}
