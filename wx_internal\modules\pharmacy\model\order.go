package model

import (
	"context"
	"time"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// 订单状态
const (
	OrderStatusPending   = 1 // 待支付
	OrderStatusPaid      = 2 // 已支付
	OrderStatusShipping  = 3 // 配送中
	OrderStatusCompleted = 4 // 已完成
	OrderStatusCancelled = 5 // 已取消
	OrderStatusRefunded  = 6 // 已退款
)

// 订单类型
const (
	OrderTypeMedicine    = 1 // 药品订单
	OrderTypeAppointment = 2 // 挂号订单
	OrderTypeConsult     = 3 // AI问诊
)

// 支付类型
const (
	PaymentTypeWechat  = 1 // 微信支付
	PaymentTypeAlipay  = 2 // 支付宝
	PaymentTypeBalance = 3 // 余额支付
)

// Order 订单
type Order struct {
	ID             uint           `gorm:"primaryKey" json:"id"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"-"`
	OrderNo        string         `gorm:"size:30;index" json:"order_no"`  // 订单编号
	UserID         uint           `gorm:"index" json:"user_id"`           // 用户ID
	MemberID       uint           `gorm:"index" json:"member_id"`         // 会员ID
	Type           int            `json:"type"`                           // 订单类型
	OrderType      int            `json:"order_type"`                     // 订单类型（兼容字段）
	Status         int            `json:"status"`                         // 订单状态
	TotalAmount    float64        `json:"total_amount"`                   // 订单总金额
	ActualAmount   float64        `json:"actual_amount"`                  // 实际金额（兼容字段）
	PayableAmount  float64        `json:"payable_amount"`                 // 应付金额
	DiscountAmount float64        `json:"discount_amount"`                // 优惠金额
	PaymentMethod  string         `gorm:"size:20" json:"payment_method"`  // 支付方式
	PaymentTime    *time.Time     `json:"payment_time"`                   // 支付时间
	PaymentNo      string         `gorm:"size:50" json:"payment_no"`      // 支付流水号
	RecipientName  string         `gorm:"size:50" json:"recipient_name"`  // 收货人姓名
	RecipientPhone string         `gorm:"size:20" json:"recipient_phone"` // 收货人电话
	RecipientAddr  string         `gorm:"size:200" json:"recipient_addr"` // 收货地址
	Remark         string         `gorm:"size:200" json:"remark"`         // 订单备注
	Points         int            `json:"points"`                         // 获得积分
	RefType        string         `gorm:"size:20;index" json:"ref_type"`  // 关联类型
	RefID          uint           `gorm:"index" json:"ref_id"`            // 关联ID
	RelatedID      uint           `json:"related_id"`                     // 关联ID（兼容字段）
}

// TableName 设置表名
func (Order) TableName() string {
	return "orders"
}

// OrderItem 订单项
type OrderItem struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
	OrderID      uint           `gorm:"index" json:"order_id"`         // 订单ID
	MedicineID   uint           `json:"medicine_id"`                   // 药品ID
	MedicineName string         `gorm:"size:100" json:"medicine_name"` // 药品名称
	Price        float64        `json:"price"`                         // 单价
	MemberPrice  float64        `json:"member_price"`                  // 会员价
	Quantity     int            `json:"quantity"`                      // 数量
	Unit         string         `gorm:"size:20" json:"unit"`           // 单位
	TotalAmount  float64        `json:"total_amount"`                  // 总金额
	RefType      string         `gorm:"size:20" json:"ref_type"`       // 关联类型
	RefID        uint           `json:"ref_id"`                        // 关联ID
}

// TableName 设置表名
func (OrderItem) TableName() string {
	return "order_items"
}

// OrderRepository 订单仓库接口
type OrderRepository interface {
	Create(order *Order) error
	Update(order *Order) error
	FindByID(id uint) (*Order, error)
	FindByOrderNo(orderNo string) (*Order, error)
	FindByUserID(userID uint, page, size int) ([]*Order, int64, error)
	FindByStatus(status int, page, size int) ([]*Order, int64, error)
	UpdateStatus(id uint, status int) error
	CreateOrderItem(item *OrderItem) error
	FindOrderItems(orderID uint) ([]*OrderItem, error)
	CreateOrder(ctx context.Context, order *Order) (int64, error)
	CreateOrderItems(ctx context.Context, items []*OrderItem) error
	GetOrderByOrderNo(ctx context.Context, orderNo string) (*Order, error)
	UpdateOrderStatus(ctx context.Context, orderNo string, status int) error
	GetUserOrders(ctx context.Context, userID int64, orderType, status int, page, pageSize int) ([]*Order, int64, error)
	GetOrderItems(ctx context.Context, orderNo string) ([]*OrderItem, error)
	CancelOrder(ctx context.Context, orderNo string) error
	CompleteOrder(ctx context.Context, orderNo string) error
}

// orderRepository 订单仓库实现
type orderRepository struct{}

// NewOrderRepository 创建订单仓库
func NewOrderRepository() OrderRepository {
	return &orderRepository{}
}

// Create 创建订单
func (r *orderRepository) Create(order *Order) error {
	return mysql.Master().Create(order).Error
}

// Update 更新订单
func (r *orderRepository) Update(order *Order) error {
	return mysql.Master().Save(order).Error
}

// FindByID 根据ID查询订单
func (r *orderRepository) FindByID(id uint) (*Order, error) {
	var order Order
	err := mysql.Slave().Where("id = ?", id).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// FindByOrderNo 根据订单号查询订单
func (r *orderRepository) FindByOrderNo(orderNo string) (*Order, error) {
	var order Order
	err := mysql.Slave().Where("order_no = ?", orderNo).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// FindByUserID 根据用户ID查询订单
func (r *orderRepository) FindByUserID(userID uint, page, size int) ([]*Order, int64, error) {
	var orders []*Order
	var count int64

	offset := (page - 1) * size

	err := mysql.Slave().Model(&Order{}).
		Where("user_id = ?", userID).
		Count(&count).
		Order("id DESC").
		Offset(offset).
		Limit(size).
		Find(&orders).Error

	return orders, count, err
}

// FindByStatus 根据状态查询订单
func (r *orderRepository) FindByStatus(status int, page, size int) ([]*Order, int64, error) {
	var orders []*Order
	var count int64

	offset := (page - 1) * size

	err := mysql.Slave().Model(&Order{}).
		Where("status = ?", status).
		Count(&count).
		Order("id DESC").
		Offset(offset).
		Limit(size).
		Find(&orders).Error

	return orders, count, err
}

// UpdateStatus 更新订单状态
func (r *orderRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&Order{}).
		Where("id = ?", id).
		Update("status", status).Error
}

// CreateOrderItem 创建订单项
func (r *orderRepository) CreateOrderItem(item *OrderItem) error {
	return mysql.Master().Create(item).Error
}

// FindOrderItems 查询订单项
func (r *orderRepository) FindOrderItems(orderID uint) ([]*OrderItem, error) {
	var items []*OrderItem
	err := mysql.Slave().Where("order_id = ?", orderID).Find(&items).Error
	return items, err
}

// CreateOrder 创建订单
func (r *orderRepository) CreateOrder(ctx context.Context, order *Order) (int64, error) {
	err := mysql.Master().WithContext(ctx).Create(order).Error
	if err != nil {
		return 0, err
	}
	return int64(order.ID), nil
}

// CreateOrderItems 创建订单项
func (r *orderRepository) CreateOrderItems(ctx context.Context, items []*OrderItem) error {
	return mysql.Master().WithContext(ctx).Create(&items).Error
}

// GetOrderByOrderNo 根据订单号查询订单
func (r *orderRepository) GetOrderByOrderNo(ctx context.Context, orderNo string) (*Order, error) {
	var order Order
	err := mysql.Slave().WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// UpdateOrderStatus 更新订单状态
func (r *orderRepository) UpdateOrderStatus(ctx context.Context, orderNo string, status int) error {
	return mysql.Master().WithContext(ctx).Model(&Order{}).
		Where("order_no = ?", orderNo).
		Update("status", status).Error
}

// GetUserOrders 查询用户订单列表
func (r *orderRepository) GetUserOrders(ctx context.Context, userID int64, orderType, status int, page, pageSize int) ([]*Order, int64, error) {
	var orders []*Order
	var total int64

	db := mysql.Slave().WithContext(ctx).Model(&Order{}).Where("user_id = ?", userID)

	if orderType > 0 {
		db = db.Where("type = ?", orderType)
	}

	if status > 0 {
		db = db.Where("status = ?", status)
	}

	// 查询总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = db.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&orders).Error
	if err != nil {
		return nil, 0, err
	}

	return orders, total, nil
}

// GetOrderItems 获取订单项
func (r *orderRepository) GetOrderItems(ctx context.Context, orderNo string) ([]*OrderItem, error) {
	var items []*OrderItem
	err := mysql.Slave().WithContext(ctx).Where("order_id = (SELECT id FROM orders WHERE order_no = ?)", orderNo).Find(&items).Error
	return items, err
}

// CancelOrder 取消订单
func (r *orderRepository) CancelOrder(ctx context.Context, orderNo string) error {
	return mysql.Master().WithContext(ctx).Model(&Order{}).
		Where("order_no = ? AND status IN (?, ?)", orderNo, OrderStatusPending, OrderStatusPaid).
		Update("status", OrderStatusCancelled).Error
}

// CompleteOrder 完成订单
func (r *orderRepository) CompleteOrder(ctx context.Context, orderNo string) error {
	return mysql.Master().WithContext(ctx).Model(&Order{}).
		Where("order_no = ? AND status = ?", orderNo, OrderStatusShipping).
		Update("status", OrderStatusCompleted).Error
}
