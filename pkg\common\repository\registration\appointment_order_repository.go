package registration

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"

	"yekaitai/pkg/common/model/registration"
	"yekaitai/pkg/infra/mysql"
)

// AppointmentOrderRepository 预约挂号订单仓库接口
type AppointmentOrderRepository interface {
	// SaveAppointmentOrder 保存预约挂号订单
	SaveAppointmentOrder(ctx context.Context, order *registration.AppointmentOrder) error

	// GetAppointmentOrderByID 根据ID查询预约挂号订单
	GetAppointmentOrderByID(ctx context.Context, id uint) (*registration.AppointmentOrder, error)

	// GetAppointmentOrderByOrderNo 根据订单编号查询预约挂号订单
	GetAppointmentOrderByOrderNo(ctx context.Context, orderNo string) (*registration.AppointmentOrder, error)

	// GetAppointmentOrdersByPatientID 根据患者ID查询预约挂号订单列表
	GetAppointmentOrdersByPatientID(ctx context.Context, patientID int, status string) ([]*registration.AppointmentOrder, error)

	// GetAppointmentOrderByYyghID 根据预约挂号ID查询订单
	GetAppointmentOrderByYyghID(ctx context.Context, yyghID int) (*registration.AppointmentOrder, error)

	// GetAppointmentOrderByRegistrationSheetID 根据ABC云挂号单ID查询订单
	GetAppointmentOrderByRegistrationSheetID(ctx context.Context, registrationSheetID string) (*registration.AppointmentOrder, error)

	// UpdateAppointmentOrder 更新预约挂号订单
	UpdateAppointmentOrder(ctx context.Context, order *registration.AppointmentOrder) error

	// UpdateOrderStatus 更新订单状态
	UpdateOrderStatus(ctx context.Context, id uint, status registration.OrderStatus) error

	// CancelAppointmentOrder 取消预约订单
	CancelAppointmentOrder(ctx context.Context, id uint, zfrID int, zfrMC, zfYY string) error

	// GetAppointmentOrderByVerificationCode 根据核销码查询预约挂号订单
	GetAppointmentOrderByVerificationCode(ctx context.Context, verificationCode string) (*registration.AppointmentOrder, error)

	// GetAppointmentOrdersByVerification 查询核销记录列表
	GetAppointmentOrdersByVerification(ctx context.Context, storeID string, startDate string, endDate string, page int, pageSize int) ([]*registration.AppointmentOrder, int, error)
}

// appointmentOrderRepository 预约挂号订单仓库实现
type appointmentOrderRepository struct {
	db *gorm.DB
}

// NewAppointmentOrderRepository 创建预约挂号订单仓库
func NewAppointmentOrderRepository() AppointmentOrderRepository {
	return &appointmentOrderRepository{
		db: mysql.Master(),
	}
}

// SaveAppointmentOrder 保存预约挂号订单
func (r *appointmentOrderRepository) SaveAppointmentOrder(ctx context.Context, order *registration.AppointmentOrder) error {
	// 设置创建时间和更新时间
	if order.CreatedAt.IsZero() {
		order.CreatedAt = time.Now()
	}
	order.UpdatedAt = time.Now()

	// 生成订单编号
	if order.OrderNo == "" {
		order.OrderNo = fmt.Sprintf("YY%s%d", time.Now().Format("20060102150405"), order.WxPatientID)
	}

	// 保存订单
	if err := r.db.WithContext(ctx).Create(order).Error; err != nil {
		logx.WithContext(ctx).Errorf("保存预约挂号订单失败: %v", err)
		return err
	}

	logx.WithContext(ctx).Infof("保存预约挂号订单成功: ID=%d, 订单编号=%s", order.ID, order.OrderNo)
	return nil
}

// GetAppointmentOrderByID 根据ID查询预约挂号订单
func (r *appointmentOrderRepository) GetAppointmentOrderByID(ctx context.Context, id uint) (*registration.AppointmentOrder, error) {
	var order registration.AppointmentOrder
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("预约挂号订单不存在: ID=%d", id)
		}
		logx.WithContext(ctx).Errorf("查询预约挂号订单失败: %v", err)
		return nil, err
	}
	return &order, nil
}

// GetAppointmentOrderByOrderNo 根据订单编号查询预约挂号订单
func (r *appointmentOrderRepository) GetAppointmentOrderByOrderNo(ctx context.Context, orderNo string) (*registration.AppointmentOrder, error) {
	var order registration.AppointmentOrder
	if err := r.db.WithContext(ctx).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("预约挂号订单不存在: orderNo=%s", orderNo)
		}
		logx.WithContext(ctx).Errorf("查询预约挂号订单失败: %v", err)
		return nil, err
	}
	return &order, nil
}

// GetAppointmentOrdersByPatientID 根据患者ID查询预约挂号订单列表
func (r *appointmentOrderRepository) GetAppointmentOrdersByPatientID(ctx context.Context, patientID int, status string) ([]*registration.AppointmentOrder, error) {
	db := r.db.WithContext(ctx)

	// 添加患者ID筛选条件（如果提供了患者ID）
	if patientID > 0 {
		db = db.Where("wx_patient_id = ?", patientID)
	}

	// 根据状态筛选
	switch status {
	case "pending": // 待就诊
		db = db.Where("order_status = ?", registration.OrderStatusWaitDiagnosis)
	case "wait_pay": // 待支付
		db = db.Where("order_status = ?", registration.OrderStatusWaitPay)
	case "completed": // 已完成
		db = db.Where("order_status = ?", registration.OrderStatusCompleted)
	case "canceled": // 已取消
		db = db.Where("order_status = ?", registration.OrderStatusCanceled)
	case "refunded": // 已退款
		db = db.Where("order_status = ?", registration.OrderStatusRefunded)
	case "all", "": // 全部或未提供状态
		// 不添加状态筛选
	default:
		logx.WithContext(ctx).Infof("未知订单状态: %s，将返回全部订单", status)
	}

	// 查询订单列表，按创建时间倒序排序（最新的在前面）
	var orders []*registration.AppointmentOrder
	if err := db.Order("created_at DESC").Find(&orders).Error; err != nil {
		logx.WithContext(ctx).Errorf("查询预约挂号订单列表失败: %v", err)
		return nil, err
	}

	return orders, nil
}

// GetAppointmentOrderByYyghID 根据预约挂号ID查询订单
func (r *appointmentOrderRepository) GetAppointmentOrderByYyghID(ctx context.Context, yyghID int) (*registration.AppointmentOrder, error) {
	var order registration.AppointmentOrder
	if err := r.db.WithContext(ctx).Where("yyghid = ?", yyghID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("预约挂号订单不存在: yyghID=%d", yyghID)
		}
		logx.WithContext(ctx).Errorf("查询预约挂号订单失败: %v", err)
		return nil, err
	}
	return &order, nil
}

// GetAppointmentOrderByRegistrationSheetID 根据ABC云挂号单ID查询订单
func (r *appointmentOrderRepository) GetAppointmentOrderByRegistrationSheetID(ctx context.Context, registrationSheetID string) (*registration.AppointmentOrder, error) {
	var order registration.AppointmentOrder
	if err := r.db.WithContext(ctx).Where("registration_sheet_id = ?", registrationSheetID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("预约挂号订单不存在: registrationSheetID=%s", registrationSheetID)
		}
		logx.WithContext(ctx).Errorf("查询预约挂号订单失败: %v, registrationSheetID=%s", err, registrationSheetID)
		return nil, err
	}
	return &order, nil
}

// UpdateAppointmentOrder 更新预约挂号订单
func (r *appointmentOrderRepository) UpdateAppointmentOrder(ctx context.Context, order *registration.AppointmentOrder) error {
	order.UpdatedAt = time.Now()
	if err := r.db.WithContext(ctx).Save(order).Error; err != nil {
		logx.WithContext(ctx).Errorf("更新预约挂号订单失败: %v", err)
		return err
	}
	logx.WithContext(ctx).Infof("更新预约挂号订单成功: ID=%d", order.ID)
	return nil
}

// UpdateOrderStatus 更新订单状态
func (r *appointmentOrderRepository) UpdateOrderStatus(ctx context.Context, id uint, status registration.OrderStatus) error {
	if err := r.db.WithContext(ctx).Model(&registration.AppointmentOrder{}).Where("id = ?", id).Update("order_status", status).Error; err != nil {
		logx.WithContext(ctx).Errorf("更新订单状态失败: %v", err)
		return err
	}
	logx.WithContext(ctx).Infof("更新订单状态成功: ID=%d, 状态=%d", id, status)
	return nil
}

// CancelAppointmentOrder 取消预约订单
func (r *appointmentOrderRepository) CancelAppointmentOrder(ctx context.Context, id uint, zfrID int, zfrMC, zfYY string) error {
	// 查询订单
	order, err := r.GetAppointmentOrderByID(ctx, id)
	if err != nil {
		return err
	}

	// 设置取消信息
	order.OrderStatus = registration.OrderStatusCanceled
	order.ZfrID = zfrID
	order.ZfrMC = zfrMC
	order.ZfYY = zfYY
	order.ZfSJ = time.Now().Format("2006-01-02 15:04:05")

	// 更新订单
	return r.UpdateAppointmentOrder(ctx, order)
}

// GetAppointmentOrderByVerificationCode 根据核销码查询预约挂号订单
func (r *appointmentOrderRepository) GetAppointmentOrderByVerificationCode(ctx context.Context, verificationCode string) (*registration.AppointmentOrder, error) {
	var order registration.AppointmentOrder
	if err := r.db.WithContext(ctx).Where("verification_code = ?", verificationCode).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("预约挂号订单不存在: verificationCode=%s", verificationCode)
		}
		logx.WithContext(ctx).Errorf("查询预约挂号订单失败: %v", err)
		return nil, err
	}
	return &order, nil
}

// GetAppointmentOrdersByVerification 查询核销记录列表
func (r *appointmentOrderRepository) GetAppointmentOrdersByVerification(ctx context.Context, storeID string, startDate string, endDate string, page int, pageSize int) ([]*registration.AppointmentOrder, int, error) {
	var orders []*registration.AppointmentOrder
	var total int64

	db := r.db.WithContext(ctx)

	// 添加患者ID筛选条件（如果提供了患者ID）
	if storeID != "" {
		db = db.Where("store_id = ?", storeID)
	}

	// 添加时间筛选条件
	if startDate != "" && endDate != "" {
		db = db.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	}

	// 查询订单列表，按创建时间倒序排序（最新的在前面）
	if err := db.Order("created_at DESC").Find(&orders).Count(&total).Error; err != nil {
		logx.WithContext(ctx).Errorf("查询预约挂号订单列表失败: %v", err)
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	// 查询订单列表，按创建时间倒序排序（最新的在前面）
	if err := db.Find(&orders).Error; err != nil {
		logx.WithContext(ctx).Errorf("查询预约挂号订单列表失败: %v", err)
		return nil, 0, err
	}

	return orders, int(total), nil
}

// 将结构体转换为JSON字符串
func structToJSON(v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		return ""
	}
	return string(data)
}

// 将JSON字符串转换为结构体
func jsonToStruct(jsonStr string, v interface{}) error {
	return json.Unmarshal([]byte(jsonStr), v)
}
