package jushuitan

import (
	"context"
	"fmt"

	jsoniter "github.com/json-iterator/go"

	"github.com/zeromicro/go-zero/core/logx"
)

// 创建json-iterator实例
var jsoniter_ = jsoniter.ConfigCompatibleWithStandardLibrary

// GetJSON 获取JSON处理对象
func GetJSON() jsoniter.API {
	return jsoniter_
}

// BaseResp 通用响应结构
type BaseResp struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// QueryShops 查询店铺列表
func (c *Client) QueryShops(ctx context.Context, req *ShopQueryRequest) (*BaseResp, error) {
	// 发送请求
	resp, err := c.Request(ctx, ShopQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("店铺查询请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析店铺查询响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryLogisticsCompanies 查询物流公司列表
func (c *Client) QueryLogisticsCompanies(ctx context.Context, req *LogisticsCompanyQueryRequest) (*BaseResp, error) {
	// 发送请求
	resp, err := c.Request(ctx, LogisticsCompanyQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("物流公司查询请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析物流公司查询响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryWarehouses 查询仓库列表
func (c *Client) QueryWarehouses(ctx context.Context, req *WarehouseQueryRequest) (*BaseResp, error) {
	// 发送请求
	resp, err := c.Request(ctx, WarehouseQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("仓库查询请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析仓库查询响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryUsers 查询用户信息列表
func (c *Client) QueryUsers(ctx context.Context, req *UserQueryRequest) (*BaseResp, error) {
	// 发送请求
	resp, err := c.Request(ctx, UserInfoAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("用户查询请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析用户查询响应失败: %w", err)
	}

	// 记录完整响应以便调试
	logx.Infof("QueryUsers API响应: %s", string(resp))

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QuerySuppliers 查询供应商列表
func (c *Client) QuerySuppliers(ctx context.Context, req *SupplierQueryRequest) (*BaseResp, error) {
	// 发送请求
	resp, err := c.Request(ctx, SupplierQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("供应商查询请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析供应商查询响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryDistributors 查询分销商列表
func (c *Client) QueryDistributors(ctx context.Context, req *DistributorQueryRequest) (*BaseResp, error) {
	// 发送请求
	resp, err := c.Request(ctx, DistributorQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("分销商查询请求失败: %w", err)
	}

	// 解析响应
	var result BaseResp
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析分销商查询响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// GetTradeDiff 获取订单变更
// 文档: https://open.jushuitan.com/doc/api/get.trade.diff.html
func GetTradeDiff(ctx context.Context, params *GetTradeDiffParams) (*GetTradeDiffResponse, error) {
	return DefaultClient.GetTradeDiff(ctx, params)
}

// GetTradeDiff 获取订单变更
func (c *Client) GetTradeDiff(ctx context.Context, params *GetTradeDiffParams) (*GetTradeDiffResponse, error) {
	resp, err := c.Request(ctx, "/open/jushuitan/get.trade.diff", params)
	if err != nil {
		logx.Errorf("获取订单变更失败: %s", err)
		return nil, fmt.Errorf("获取订单变更失败: %w", err)
	}

	var response GetTradeDiffResponse
	if err := jsoniter_.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析订单变更响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if response.Code == 0 {
		response.Code = 200
	}

	return &response, nil
}

// GetTradeDiffParams 获取订单变更请求参数
type GetTradeDiffParams struct {
	Start        string `json:"start,omitempty"`          // 起始时间 示例：2016-06-11 09:00:00
	End          string `json:"end,omitempty"`            // 结束时间 示例：2016-06-12 09:00:00
	Page         int    `json:"page"`                     // 分页参数，从1开始
	PageSize     int    `json:"page_size"`                // 每页条数，最大100
	Status       string `json:"status,omitempty"`         // 订单状态
	Type         string `json:"type,omitempty"`           // 订单类型
	TradePlatDef string `json:"trade_plat_def,omitempty"` // 平台编码
}

// GetTradeDiffResponse 获取订单变更响应
type GetTradeDiffResponse struct {
	BaseResponse
	Data GetTradeDiffData `json:"data"`
}

// GetTradeDiffData 获取订单变更数据
type GetTradeDiffData struct {
	Total    int `json:"total"`
	PageSize int `json:"page_size"`
	PageNo   int `json:"page_no"`
}
