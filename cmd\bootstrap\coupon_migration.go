package bootstrap

import (
	"fmt"
	"yekaitai/internal/modules/coupon/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateCouponTables 执行优惠券模块表结构迁移
func MigrateCouponTables() error {
	db := mysql.Master()

	logx.Info("开始执行优惠券模块表结构迁移...")

	// 自动迁移优惠券表
	db.Set("gorm:table_options", "COMMENT='优惠券表'").AutoMigrate(&model.Coupon{})
	db.Set("gorm:table_options", "COMMENT='用户优惠券表'").AutoMigrate(&model.UserCoupon{})
	db.Set("gorm:table_options", "COMMENT='优惠券适用商品表'").AutoMigrate(&model.CouponApplicableProduct{})
	db.Set("gorm:table_options", "COMMENT='优惠券适用服务表'").AutoMigrate(&model.CouponApplicableService{})
	fmt.Println("优惠券模块表结构迁移完成")
	return nil
}
