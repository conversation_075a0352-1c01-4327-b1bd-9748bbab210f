package handler

import (
	"testing"
	"time"

	"yekaitai/internal/modules/content/model"
)

// TestCalculateDistance 测试距离计算函数
func TestCalculateDistance(t *testing.T) {
	// 测试北京天安门到故宫的距离（约1公里）
	lat1, lng1 := 39.9042, 116.4074 // 天安门
	lat2, lng2 := 39.9163, 116.3972 // 故宫

	distance := calculateDistance(lat1, lng1, lat2, lng2)

	// 距离应该在0.5-2公里之间
	if distance < 0.5 || distance > 2.0 {
		t.<PERSON><PERSON><PERSON>("距离计算错误: 期望0.5-2.0公里，实际%.2f公里", distance)
	}

	t.Logf("天安门到故宫距离: %.2f公里", distance)
}

// TestGetOrderStatusText 测试订单状态文本
func TestGetOrderStatusText(t *testing.T) {
	handler := &ActivityHandler{}

	tests := []struct {
		status   int
		expected string
	}{
		{1, "已报名"},
		{2, "已取消"},
		{3, "已取消"},
		{4, "已核销"},
		{999, "未知状态"},
	}

	for _, test := range tests {
		result := handler.getOrderStatusText(test.status)
		if result != test.expected {
			t.Errorf("状态%d: 期望%s，实际%s", test.status, test.expected, result)
		}
	}
}

// TestBuildActivityResponse 测试活动响应构建
func TestBuildActivityResponse(t *testing.T) {
	handler := &ActivityHandler{}

	// 创建测试活动
	activity := &model.Content{
		ID:                1,
		Title:             "测试活动",
		Type:              "activity",
		Format:            "text",
		Description:       "这是一个测试活动",
		Content:           "活动详细内容",
		CoverImage:        "https://example.com/cover.jpg",
		IsEnabled:         true,
		IsRecommended:     true,
		RecommendSort:     1,
		ViewCount:         100,
		ReportCount:       0,
		MaxSignUp:         50,
		CanSignUp:         true,
		SignUpCount:       10,
		SignUpMethod:      "phone",
		IsNewActivity:     true,
		IsSpecialActivity: false,
		CreatedBy:         1,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	result := handler.buildActivityResponse(activity)

	// 验证返回的字段
	if result["id"] != uint(1) {
		t.Errorf("ID字段错误: 期望1，实际%v", result["id"])
	}

	if result["title"] != "测试活动" {
		t.Errorf("标题字段错误: 期望'测试活动'，实际%v", result["title"])
	}

	if result["type"] != "activity" {
		t.Errorf("类型字段错误: 期望'activity'，实际%v", result["type"])
	}

	if result["can_sign_up"] != true {
		t.Errorf("可报名字段错误: 期望true，实际%v", result["can_sign_up"])
	}

	if result["max_sign_up"] != 50 {
		t.Errorf("最大报名人数字段错误: 期望50，实际%v", result["max_sign_up"])
	}
}

// TestContentSignUpOrderModel 测试报名订单模型
func TestContentSignUpOrderModel(t *testing.T) {
	order := &model.ContentSignUpOrder{
		ID:               1,
		OrderNo:          "ACT202506250001",
		ContentID:        1,
		UserID:           1,
		Name:             "张三",
		Phone:            "13800138000",
		Amount:           0, // 免费活动
		Status:           1, // 已报名
		VerificationCode: "ACT1234567890",
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// 验证表名
	if order.TableName() != "t_content_sign_up_orders" {
		t.Errorf("表名错误: 期望't_content_sign_up_orders'，实际'%s'", order.TableName())
	}

	// 验证字段
	if order.OrderNo != "ACT202506250001" {
		t.Errorf("订单号错误: 期望'ACT202506250001'，实际'%s'", order.OrderNo)
	}

	if order.Name != "张三" {
		t.Errorf("姓名错误: 期望'张三'，实际'%s'", order.Name)
	}

	if order.Phone != "13800138000" {
		t.Errorf("手机号错误: 期望'13800138000'，实际'%s'", order.Phone)
	}

	if order.Status != 1 {
		t.Errorf("状态错误: 期望1，实际%d", order.Status)
	}
}

// TestActivityValidation 测试活动验证逻辑
func TestActivityValidation(t *testing.T) {
	// 测试活动是否过期
	now := time.Now()

	// 未过期的活动
	futureDeadline := now.Add(24 * time.Hour)
	activity1 := &model.Content{
		SignUpDeadline: &futureDeadline,
		CanSignUp:      true,
		MaxSignUp:      10,
		SignUpCount:    5,
	}

	// 检查活动是否可以报名
	if !activity1.CanSignUp {
		t.Error("活动应该可以报名")
	}

	if activity1.SignUpDeadline.Before(now) {
		t.Error("活动不应该过期")
	}

	if activity1.SignUpCount >= activity1.MaxSignUp {
		t.Error("活动报名人数不应该已满")
	}

	// 已过期的活动
	pastDeadline := now.Add(-24 * time.Hour)
	activity2 := &model.Content{
		SignUpDeadline: &pastDeadline,
		CanSignUp:      true,
		MaxSignUp:      10,
		SignUpCount:    5,
	}

	if !activity2.SignUpDeadline.Before(now) {
		t.Error("活动应该已过期")
	}

	// 报名已满的活动
	activity3 := &model.Content{
		SignUpDeadline: &futureDeadline,
		CanSignUp:      true,
		MaxSignUp:      10,
		SignUpCount:    10,
	}

	if activity3.SignUpCount < activity3.MaxSignUp {
		t.Error("活动报名人数应该已满")
	}
}

// BenchmarkCalculateDistance 距离计算性能测试
func BenchmarkCalculateDistance(b *testing.B) {
	lat1, lng1 := 39.9042, 116.4074
	lat2, lng2 := 39.9163, 116.3972

	for i := 0; i < b.N; i++ {
		calculateDistance(lat1, lng1, lat2, lng2)
	}
}
