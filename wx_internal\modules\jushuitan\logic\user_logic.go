package logic

import (
	"context"
	"yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// UserLogic 用户查询逻辑
type UserLogic struct {
	ctx    context.Context
	svcCtx *svc.WxServiceContext
}

// NewUserLogic 创建用户查询逻辑
func NewUserLogic(ctx context.Context, svcCtx *svc.WxServiceContext) *UserLogic {
	return &UserLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// QueryUsers 查询用户信息列表
func (l *UserLogic) QueryUsers(currentPage, pageSize int, params map[string]interface{}) (*jushuitan.BaseResp, error) {
	// 构建请求参数
	req := &jushuitan.UserQueryRequest{
		CurrentPage: currentPage,
		PageSize:    pageSize,
	}

	// 设置可选参数
	if val, ok := params["enabled"]; ok {
		if enabled, ok := val.(bool); ok {
			req.Enabled = &enabled
		}
	}
	if val, ok := params["pageAction"]; ok {
		if pageAction, ok := val.(int); ok {
			req.PageAction = pageAction
		}
	}
	if val, ok := params["version"]; ok {
		if version, ok := val.(int); ok {
			req.Version = version
		}
	}
	if val, ok := params["loginId"]; ok {
		if loginID, ok := val.(string); ok {
			req.LoginID = loginID
		}
	}
	if val, ok := params["createdBegin"]; ok {
		if createdBegin, ok := val.(string); ok {
			req.CreatedBegin = createdBegin
		}
	}
	if val, ok := params["createdEnd"]; ok {
		if createdEnd, ok := val.(string); ok {
			req.CreatedEnd = createdEnd
		}
	}

	return l.svcCtx.JushuitanClient.QueryUsers(l.ctx, req)
}
