package service

import (
	"context"
	"fmt"

	"yekaitai/internal/modules/sms_setup/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// SmsTemplateService 短信模板服务接口
type SmsTemplateService interface {
	CreateTemplate(ctx context.Context, req *model.SmsTemplateCreateRequest) (*model.SmsTemplateResponse, error)       // 创建模板
	UpdateTemplate(ctx context.Context, req *model.SmsTemplateUpdateRequest) (*model.SmsTemplateResponse, error)       // 更新模板
	DeleteTemplate(ctx context.Context, id uint) error                                                                 // 删除模板
	BatchDeleteTemplates(ctx context.Context, ids []uint) error                                                        // 批量删除模板
	GetTemplate(ctx context.Context, id uint) (*model.SmsTemplateResponse, error)                                      // 获取模板详情
	ListTemplates(ctx context.Context, req *model.SmsTemplateListRequest) ([]*model.SmsTemplateResponse, int64, error) // 获取模板列表
}

// smsTemplateService 短信模板服务实现
type smsTemplateService struct {
	repo model.SmsTemplateRepository
}

// NewSmsTemplateService 创建短信模板服务
func NewSmsTemplateService() SmsTemplateService {
	return &smsTemplateService{
		repo: model.NewSmsTemplateRepository(mysql.GetDB()),
	}
}

// CreateTemplate 创建模板
func (s *smsTemplateService) CreateTemplate(ctx context.Context, req *model.SmsTemplateCreateRequest) (*model.SmsTemplateResponse, error) {
	// 检查模板名称是否已存在
	exists, err := s.repo.CheckNameExists(req.TemplateName, 0)
	if err != nil {
		return nil, fmt.Errorf("检查模板名称失败: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("模板名称已存在")
	}

	// 创建模板
	template := &model.SmsTemplate{
		TemplateName: req.TemplateName,
		Content:      req.Content,
		Creator:      req.Creator,
	}

	if err := s.repo.Create(template); err != nil {
		return nil, fmt.Errorf("创建模板失败: %w", err)
	}

	return s.convertToResponse(template), nil
}

// UpdateTemplate 更新模板
func (s *smsTemplateService) UpdateTemplate(ctx context.Context, req *model.SmsTemplateUpdateRequest) (*model.SmsTemplateResponse, error) {
	// 检查模板是否存在
	template, err := s.repo.FindByID(req.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("模板不存在")
		}
		return nil, fmt.Errorf("查询模板失败: %w", err)
	}

	// 检查模板名称是否已被其他模板使用
	exists, err := s.repo.CheckNameExists(req.TemplateName, req.ID)
	if err != nil {
		return nil, fmt.Errorf("检查模板名称失败: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("模板名称已存在")
	}

	// 更新模板信息
	template.TemplateName = req.TemplateName
	template.Content = req.Content
	template.Creator = req.Creator

	if err := s.repo.Update(template); err != nil {
		return nil, fmt.Errorf("更新模板失败: %w", err)
	}

	return s.convertToResponse(template), nil
}

// DeleteTemplate 删除模板
func (s *smsTemplateService) DeleteTemplate(ctx context.Context, id uint) error {
	// 检查模板是否存在
	_, err := s.repo.FindByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("模板不存在")
		}
		return fmt.Errorf("查询模板失败: %w", err)
	}

	if err := s.repo.Delete(id); err != nil {
		return fmt.Errorf("删除模板失败: %w", err)
	}

	return nil
}

// BatchDeleteTemplates 批量删除模板
func (s *smsTemplateService) BatchDeleteTemplates(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return fmt.Errorf("删除ID列表不能为空")
	}

	if err := s.repo.BatchDelete(ids); err != nil {
		return fmt.Errorf("批量删除模板失败: %w", err)
	}

	return nil
}

// GetTemplate 获取模板详情
func (s *smsTemplateService) GetTemplate(ctx context.Context, id uint) (*model.SmsTemplateResponse, error) {
	template, err := s.repo.FindByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("模板不存在")
		}
		return nil, fmt.Errorf("查询模板失败: %w", err)
	}

	return s.convertToResponse(template), nil
}

// ListTemplates 获取模板列表
func (s *smsTemplateService) ListTemplates(ctx context.Context, req *model.SmsTemplateListRequest) ([]*model.SmsTemplateResponse, int64, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	templates, total, err := s.repo.List(req.Page, req.Size, req.TemplateName)
	if err != nil {
		return nil, 0, fmt.Errorf("查询模板列表失败: %w", err)
	}

	// 转换为响应格式
	list := make([]*model.SmsTemplateResponse, len(templates))
	for i, template := range templates {
		list[i] = s.convertToResponse(template)
	}

	return list, total, nil
}

// convertToResponse 转换为响应格式
func (s *smsTemplateService) convertToResponse(template *model.SmsTemplate) *model.SmsTemplateResponse {
	return &model.SmsTemplateResponse{
		ID:           template.ID,
		TemplateName: template.TemplateName,
		Content:      template.Content,
		Creator:      template.Creator,
		CreatedAt:    template.CreatedAt,
		UpdatedAt:    template.UpdatedAt,
	}
}
