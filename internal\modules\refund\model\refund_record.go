package model

import (
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// RefundRecord 退款记录表
type RefundRecord struct {
	ID           uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	RefundNo     string    `gorm:"type:varchar(32);uniqueIndex;not null;comment:业务系统生成的退款单号" json:"refund_no"`
	OrderNo      string    `gorm:"type:varchar(32);index;not null;comment:原订单号" json:"order_no"`
	BizType      string    `gorm:"type:varchar(10);not null;index;comment:业务类型(activity/service/mall)" json:"biz_type"`
	Amount       int       `gorm:"not null;comment:退款金额(分)" json:"amount"`
	Reason       string    `gorm:"type:varchar(255);comment:退款原因" json:"reason"`
	WxRefundID   string    `gorm:"type:varchar(32);index;comment:微信退款单号" json:"wx_refund_id"`
	Status       string    `gorm:"type:enum('pending','success','failed');default:'pending';not null;index;comment:退款状态" json:"status"`
	OperatorID   uint      `gorm:"comment:操作人ID" json:"operator_id"`
	OperatorName string    `gorm:"type:varchar(50);comment:操作人姓名" json:"operator_name"`
	CreatedAt    time.Time `gorm:"autoCreateTime;index" json:"created_at"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (RefundRecord) TableName() string {
	return "refund_records"
}

// RefundStatus 退款状态常量
const (
	RefundStatusPending = "pending" // 退款中
	RefundStatusSuccess = "success" // 退款成功
	RefundStatusFailed  = "failed"  // 退款失败
)

// BizType 业务类型常量
const (
	BizTypeActivity = "activity" // 活动
	BizTypeService  = "service"  // 服务
	BizTypeMall     = "mall"     // 商城
)

// RefundRecordRepository 退款记录仓储接口
type RefundRecordRepository interface {
	Create(record *RefundRecord) error
	GetByRefundNo(refundNo string) (*RefundRecord, error)
	GetByWxRefundID(wxRefundID string) (*RefundRecord, error)
	GetByOrderNo(orderNo string) ([]*RefundRecord, error)
	UpdateStatus(refundNo string, status string, wxRefundID string) error
	List(page, pageSize int, filters map[string]interface{}) ([]*RefundRecord, int64, error)
}

// refundRecordRepository 退款记录仓储实现
type refundRecordRepository struct {
	db *gorm.DB
}

// NewRefundRecordRepository 创建退款记录仓储
func NewRefundRecordRepository(db *gorm.DB) RefundRecordRepository {
	return &refundRecordRepository{db: db}
}

// Create 创建退款记录
func (r *refundRecordRepository) Create(record *RefundRecord) error {
	return r.db.Create(record).Error
}

// GetByRefundNo 根据退款单号查询
func (r *refundRecordRepository) GetByRefundNo(refundNo string) (*RefundRecord, error) {
	var record RefundRecord
	err := r.db.Where("refund_no = ?", refundNo).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetByWxRefundID 根据微信退款单号查询
func (r *refundRecordRepository) GetByWxRefundID(wxRefundID string) (*RefundRecord, error) {
	var record RefundRecord
	err := r.db.Where("wx_refund_id = ?", wxRefundID).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetByOrderNo 根据订单号查询所有退款记录
func (r *refundRecordRepository) GetByOrderNo(orderNo string) ([]*RefundRecord, error) {
	var records []*RefundRecord
	err := r.db.Where("order_no = ?", orderNo).Order("created_at DESC").Find(&records).Error
	return records, err
}

// UpdateStatus 更新退款状态
func (r *refundRecordRepository) UpdateStatus(refundNo string, status string, wxRefundID string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}
	if wxRefundID != "" {
		updates["wx_refund_id"] = wxRefundID
	}
	return r.db.Model(&RefundRecord{}).Where("refund_no = ?", refundNo).Updates(updates).Error
}

// List 分页查询退款记录
func (r *refundRecordRepository) List(page, pageSize int, filters map[string]interface{}) ([]*RefundRecord, int64, error) {
	var records []*RefundRecord
	var total int64

	query := r.db.Model(&RefundRecord{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "biz_type":
			query = query.Where("biz_type = ?", value)
		case "status":
			query = query.Where("status = ?", value)
		case "order_no":
			query = query.Where("order_no LIKE ?", "%"+value.(string)+"%")
		case "refund_no":
			query = query.Where("refund_no LIKE ?", "%"+value.(string)+"%")
		case "start_date":
			query = query.Where("created_at >= ?", value)
		case "end_date":
			query = query.Where("created_at <= ?", value)
		}
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&records).Error
	return records, total, err
}

// GetBizTypeFromOrderNo 根据订单号获取业务类型
func GetBizTypeFromOrderNo(orderNo string) string {
	if len(orderNo) < 3 {
		return ""
	}

	prefix := orderNo[:3]
	switch prefix {
	case "ACT":
		return BizTypeActivity
	case "SRV":
		return BizTypeService
	case "ORD", "MAL":
		return BizTypeMall
	default:
		return ""
	}
}

// GenerateRefundNo 生成退款单号
func GenerateRefundNo(orderNo string) string {
	bizType := GetBizTypeFromOrderNo(orderNo)
	timestamp := time.Now().Unix()
	return fmt.Sprintf("REF_%s_%d", strings.ToUpper(bizType), timestamp)
}
