package types

// PaginationRequest 分页请求参数
type PaginationRequest struct {
	Page     int `form:"page,default=1" json:"page"`                    // 页码，默认1
	PageSize int `form:"page_size,default=10,max=100" json:"page_size"` // 每页大小，默认10，最大100
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	Total    int64       `json:"total"`     // 总记录数
	Page     int         `json:"page"`      // 当前页码
	PageSize int         `json:"page_size"` // 每页大小
	List     interface{} `json:"list"`      // 数据列表
}

// NewPaginationResponse 创建分页响应
func NewPaginationResponse(total int64, page int, pageSize int, list interface{}) *PaginationResponse {
	return &PaginationResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		List:     list,
	}
}
