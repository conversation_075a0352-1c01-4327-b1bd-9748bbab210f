package doctor

import (
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// DoctorRecommendRepository 医生推荐仓库接口
type DoctorRecommendRepository interface {
	// 基础操作
	Create(recommend *DoctorRecommend) error
	Update(recommend *DoctorRecommend) error
	Delete(id uint) error
	FindByID(id uint) (*DoctorRecommend, error)
	List(params *DoctorRecommendQueryParams) ([]*DoctorRecommendInfo, int64, error)

	// 业务操作
	SetTop(id uint) error
	CancelTop(id uint) error
	GetTopRecommend() (*DoctorRecommend, error)
	CountRecommends() (int64, error)
	BatchCreate(recommends []*DoctorRecommend) error
	BatchDelete(ids []uint) error

	// 医生选择相关
	GetDoctorSelectList(params *DoctorSelectQueryParams) ([]*DoctorSelectInfo, int64, error)
	IsRecommended(doctorID uint) bool

	// 推荐逻辑相关
	GetRecommendedDoctors(limit int) ([]*WxDoctor, error)
	GetDoctorsBySpecialty(specialty string, limit int) ([]*WxDoctor, error)
	GetRecentVisitedDoctors(userID uint, limit int) ([]*WxDoctor, error)
}

// doctorRecommendRepository 医生推荐仓库实现
type doctorRecommendRepository struct {
	db *gorm.DB
}

// NewDoctorRecommendRepository 创建医生推荐仓库
func NewDoctorRecommendRepository(db *gorm.DB) DoctorRecommendRepository {
	return &doctorRecommendRepository{
		db: db,
	}
}

// Create 创建医生推荐
func (r *doctorRecommendRepository) Create(recommend *DoctorRecommend) error {
	return mysql.Master().Create(recommend).Error
}

// Update 更新医生推荐
func (r *doctorRecommendRepository) Update(recommend *DoctorRecommend) error {
	return mysql.Master().Save(recommend).Error
}

// Delete 删除医生推荐
func (r *doctorRecommendRepository) Delete(id uint) error {
	return mysql.Master().Delete(&DoctorRecommend{}, id).Error
}

// FindByID 根据ID查找医生推荐
func (r *doctorRecommendRepository) FindByID(id uint) (*DoctorRecommend, error) {
	var recommend DoctorRecommend
	err := mysql.Slave().Preload("Doctor").Where("id = ?", id).First(&recommend).Error
	if err != nil {
		return nil, err
	}
	return &recommend, nil
}

// List 获取医生推荐列表
func (r *doctorRecommendRepository) List(params *DoctorRecommendQueryParams) ([]*DoctorRecommendInfo, int64, error) {
	var total int64
	var results []*DoctorRecommendInfo

	// 计算偏移量
	offset := (params.Page - 1) * params.PageSize
	if offset < 0 {
		offset = 0
	}

	// 基础查询
	db := mysql.Slave().Table("doctor_recommends dr").
		Select(`dr.id, dr.doctor_id, dr.is_top, dr.sort_order, dr.status, dr.creator_id, dr.created_at, dr.updated_at,
				wd.name as doctor_name, wd.department, wd.hospital, wd.title, wd.specialty, wd.head_img_url`).
		Joins("LEFT JOIN wx_doctor wd ON dr.doctor_id = wd.doctor_id").
		Where("dr.deleted_at IS NULL")

	// 按医生姓名筛选
	if params.DoctorName != "" {
		db = db.Where("wd.name LIKE ?", "%"+params.DoctorName+"%")
	}

	// 按科室筛选
	if params.Department != "" {
		db = db.Where("wd.department = ?", params.Department)
	}

	// 按置顶状态筛选
	if params.IsTop != nil {
		db = db.Where("dr.is_top = ?", *params.IsTop)
	}

	// 按状态筛选
	if params.Status != nil {
		db = db.Where("dr.status = ?", *params.Status)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表数据，按置顶状态和创建时间排序
	err := db.Order("dr.is_top DESC, dr.created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Scan(&results).Error

	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

// SetTop 设置置顶
func (r *doctorRecommendRepository) SetTop(id uint) error {
	return mysql.Master().Transaction(func(tx *gorm.DB) error {
		// 先取消其他置顶
		if err := tx.Model(&DoctorRecommend{}).Where("is_top = true").Update("is_top", false).Error; err != nil {
			return err
		}

		// 设置当前为置顶
		return tx.Model(&DoctorRecommend{}).Where("id = ?", id).Update("is_top", true).Error
	})
}

// CancelTop 取消置顶
func (r *doctorRecommendRepository) CancelTop(id uint) error {
	return mysql.Master().Model(&DoctorRecommend{}).Where("id = ?", id).Update("is_top", false).Error
}

// GetTopRecommend 获取置顶推荐
func (r *doctorRecommendRepository) GetTopRecommend() (*DoctorRecommend, error) {
	var recommend DoctorRecommend
	err := mysql.Slave().Where("is_top = true AND status = 1").First(&recommend).Error
	if err != nil {
		return nil, err
	}
	return &recommend, nil
}

// CountRecommends 统计推荐医生数量
func (r *doctorRecommendRepository) CountRecommends() (int64, error) {
	var count int64
	err := mysql.Slave().Model(&DoctorRecommend{}).Where("status = 1").Count(&count).Error
	return count, err
}

// BatchCreate 批量创建医生推荐
func (r *doctorRecommendRepository) BatchCreate(recommends []*DoctorRecommend) error {
	if len(recommends) == 0 {
		return nil
	}

	// 分批插入，每次100条
	batchSize := 100
	for i := 0; i < len(recommends); i += batchSize {
		end := i + batchSize
		if end > len(recommends) {
			end = len(recommends)
		}

		batch := recommends[i:end]
		if err := mysql.Master().Create(&batch).Error; err != nil {
			return err
		}
	}

	return nil
}

// BatchDelete 批量删除
func (r *doctorRecommendRepository) BatchDelete(ids []uint) error {
	return mysql.Master().Delete(&DoctorRecommend{}, ids).Error
}

// GetDoctorSelectList 获取医生选择列表
func (r *doctorRecommendRepository) GetDoctorSelectList(params *DoctorSelectQueryParams) ([]*DoctorSelectInfo, int64, error) {
	var total int64
	var results []*DoctorSelectInfo

	// 计算偏移量
	offset := (params.Page - 1) * params.PageSize
	if offset < 0 {
		offset = 0
	}

	// 基础查询
	db := mysql.Slave().Table("wx_doctor wd").
		Select(`wd.doctor_id, wd.name, wd.department, wd.hospital, wd.title, wd.specialty, wd.head_img_url,
				CASE WHEN dr.id IS NOT NULL THEN true ELSE false END as is_recommended`).
		Joins("LEFT JOIN doctor_recommends dr ON wd.doctor_id = dr.doctor_id AND dr.deleted_at IS NULL AND dr.status = 1").
		Where("wd.deleted_at IS NULL AND wd.status = 1").
		Where("dr.id IS NULL") // 排除已被推荐的医生

	// 按门店筛选
	if params.StoreID > 0 {
		db = db.Where("wd.store_id = ?", params.StoreID)
	}

	// 按医生姓名筛选
	if params.DoctorName != "" {
		db = db.Where("wd.name LIKE ?", "%"+params.DoctorName+"%")
	}

	// 按科室筛选
	if params.Department != "" {
		db = db.Where("wd.department = ?", params.Department)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取列表数据
	err := db.Order("wd.created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Scan(&results).Error

	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

// IsRecommended 检查医生是否已被推荐
func (r *doctorRecommendRepository) IsRecommended(doctorID uint) bool {
	var count int64
	mysql.Slave().Model(&DoctorRecommend{}).Where("doctor_id = ? AND status = 1", doctorID).Count(&count)
	return count > 0
}

// GetRecommendedDoctors 获取推荐医生列表
func (r *doctorRecommendRepository) GetRecommendedDoctors(limit int) ([]*WxDoctor, error) {
	var doctors []*WxDoctor

	err := mysql.Slave().Table("wx_doctor wd").
		Select("wd.*").
		Joins("INNER JOIN doctor_recommends dr ON wd.doctor_id = dr.doctor_id").
		Where("dr.status = 1 AND wd.status = 1").
		Order("dr.is_top DESC, dr.created_at DESC").
		Limit(limit).
		Find(&doctors).Error

	return doctors, err
}

// GetDoctorsBySpecialty 根据专长获取医生
func (r *doctorRecommendRepository) GetDoctorsBySpecialty(specialty string, limit int) ([]*WxDoctor, error) {
	var doctors []*WxDoctor

	err := mysql.Slave().Where("status = 1").
		Where("specialty LIKE ?", "%"+specialty+"%").
		Order("created_at DESC").
		Limit(limit).
		Find(&doctors).Error

	return doctors, err
}

// GetRecentVisitedDoctors 获取最近就诊医生
func (r *doctorRecommendRepository) GetRecentVisitedDoctors(userID uint, limit int) ([]*WxDoctor, error) {
	var doctors []*WxDoctor

	// 这里需要根据实际的就诊记录表来查询
	// 假设有一个 consultations 表记录就诊信息
	err := mysql.Slave().Table("wx_doctor wd").
		Select("DISTINCT wd.*").
		Joins("INNER JOIN consultations c ON wd.doctor_id = c.doctor_id").
		Where("c.user_id = ? AND wd.status = 1", userID).
		Order("c.created_at DESC").
		Limit(limit).
		Find(&doctors).Error

	return doctors, err
}

// GetDoctorsByDepartment 根据科室获取医生
func (r *doctorRecommendRepository) GetDoctorsByDepartment(department string, limit int) ([]*WxDoctor, error) {
	var doctors []*WxDoctor

	err := mysql.Slave().Where("status = 1").
		Where("department = ?", department).
		Order("created_at DESC").
		Limit(limit).
		Find(&doctors).Error

	return doctors, err
}
