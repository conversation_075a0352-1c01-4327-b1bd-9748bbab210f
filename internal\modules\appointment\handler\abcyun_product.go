package handler

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"yekaitai/internal/modules/appointment/logic/abcyun"
	"yekaitai/internal/modules/appointment/types"
	"yekaitai/internal/svc"
)

// AbcYunProductTypesHandler 获取商品分类
func AbcYunProductTypesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductTypesLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductTypes(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductCustomTypesHandler 获取商品自定义分类
func AbcYunProductCustomTypesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductCustomTypesLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductCustomTypes(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductListHandler 获取商品列表
func AbcYunProductListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunProductListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductListLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductDetailHandler 获取商品详情
func AbcYunProductDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunProductDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductDetailLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductOutOrderListHandler 获取出库单列表
func AbcYunProductOutOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunProductOutOrderListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductOutOrderListLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductOutOrderList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductOutOrderDetailHandler 获取出库单详情
func AbcYunProductOutOrderDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunProductOutOrderDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductOutOrderDetailLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductOutOrderDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductInOrderListHandler 获取入库单列表
func AbcYunProductInOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunProductInOrderListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductInOrderListLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductInOrderList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductInOrderDetailHandler 获取入库单详情
func AbcYunProductInOrderDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunProductInOrderDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductInOrderDetailLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductInOrderDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductCheckOrderListHandler 获取盘点单列表
func AbcYunProductCheckOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunProductCheckOrderListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductCheckOrderListLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductCheckOrderList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunProductCheckOrderDetailHandler 获取盘点单详情
func AbcYunProductCheckOrderDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunProductCheckOrderDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunProductCheckOrderDetailLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunProductCheckOrderDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// --- New Handlers (5.3.11 to 5.3.30) --- //

// AbcYunSettlementOrderListHandler 获取结算单列表 (5.3.11)
func AbcYunSettlementOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunSettlementOrderListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunSettlementOrderListLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunSettlementOrderList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunSettlementOrderDetailHandler 获取结算单详情 (5.3.12)
func AbcYunSettlementOrderDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunSettlementOrderDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunSettlementOrderDetailLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunSettlementOrderDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunSupplierListHandler 获取供应商列表 (5.3.13)
func AbcYunSupplierListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunSupplierListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunSupplierListLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunSupplierList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunInOutStockDetailsHandler 获取进销存明细 (5.3.14)
func AbcYunInOutStockDetailsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunInOutStockDetailsRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunInOutStockDetailsLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunInOutStockDetails(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunQueryProductStockHandler 查询商品库存 (5.3.15)
func AbcYunQueryProductStockHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunQueryProductStockRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunQueryProductStockLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunQueryProductStock(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunQueryProductBatchesHandler 查询商品批次信息 (5.3.16)
func AbcYunQueryProductBatchesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunQueryProductBatchesRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunQueryProductBatchesLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunQueryProductBatches(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunCreateSupplierHandler 新增供应商 (5.3.17)
func AbcYunCreateSupplierHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunCreateSupplierRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunCreateSupplierLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunCreateSupplier(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunUpdateSupplierHandler 修改供应商 (5.3.18)
func AbcYunUpdateSupplierHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunUpdateSupplierRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunUpdateSupplierLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunUpdateSupplier(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunCreateProductHandler 新增商品 (5.3.19)
func AbcYunCreateProductHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunCreateProductRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunCreateProductLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunCreateProduct(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunUpdateProductHandler 修改商品 (5.3.20)
func AbcYunUpdateProductHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunUpdateProductRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunUpdateProductLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunUpdateProduct(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunPurchaseOrderListHandler 获取采购单列表 (5.3.21)
func AbcYunPurchaseOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunPurchaseOrderListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunPurchaseOrderListLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunPurchaseOrderList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunPurchaseOrderDetailHandler 获取采购单详情 (5.3.22)
func AbcYunPurchaseOrderDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunPurchaseOrderDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunPurchaseOrderDetailLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunPurchaseOrderDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunReviewPurchaseOrderHandler 审核采购单 (5.3.23)
func AbcYunReviewPurchaseOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunReviewPurchaseOrderRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunReviewPurchaseOrderLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunReviewPurchaseOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunReviewFixedInOrderHandler 审核修正入库单 (5.3.24)
func AbcYunReviewFixedInOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunReviewFixedInOrderRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunReviewFixedInOrderLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunReviewFixedInOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunCreateInOrderHandler 创建入库单 (5.3.25)
func AbcYunCreateInOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunCreateInOrderRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunCreateInOrderLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunCreateInOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunCreateOutOrderHandler 创建退货出库单 (5.3.26)
func AbcYunCreateOutOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunCreateOutOrderRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunCreateOutOrderLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunCreateOutOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunReviewOutApplyOrderHandler 审核退货出库申请单 (5.3.27)
func AbcYunReviewOutApplyOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunReviewOutApplyOrderRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunReviewOutApplyOrderLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunReviewOutApplyOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunQueryStoreBatchesHandler 查询门店库存(批次) (5.3.28)
func AbcYunQueryStoreBatchesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunQueryStoreBatchesRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunQueryStoreBatchesLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunQueryStoreBatches(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunTransOrderListHandler 获取调拨单列表 (5.3.29)
func AbcYunTransOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunTransOrderListRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunTransOrderListLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunTransOrderList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// AbcYunTransOrderDetailHandler 获取调拨单详情 (5.3.30)
func AbcYunTransOrderDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AbcYunTransOrderDetailRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := abcyun.NewAbcYunTransOrderDetailLogic(r.Context(), svcCtx)
		resp, err := l.AbcYunTransOrderDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
