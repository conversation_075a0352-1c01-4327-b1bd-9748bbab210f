package tasks

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"yekaitai/internal/modules/admin/model"
	storeModel "yekaitai/internal/modules/store/model"
	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"
)

// OrgSyncService 卫生机构同步服务
type OrgSyncService struct {
	client     *hangzhou.Client           // 杭州HIS客户端
	storeRepo  storeModel.StoreRepository // 门店仓库
	regionRepo model.RegionRepository     // 地区仓库
	enabled    bool                       // 是否启用同步
	interval   int                        // 同步间隔（小时）
	ticker     *time.Ticker               // 定时器
	stopChan   chan struct{}              // 停止信号通道
	isRunning  bool                       // 是否正在运行
	lock       sync.Mutex                 // 互斥锁
}

// NewOrgSyncService 创建卫生机构同步服务
func NewOrgSyncService(client *hangzhou.Client, storeRepo storeModel.StoreRepository, enabled bool, interval int) *OrgSyncService {
	// 默认1小时同步一次
	if interval <= 0 {
		interval = 1
	}

	return &OrgSyncService{
		client:     client,
		storeRepo:  storeRepo,
		regionRepo: model.NewRegionRepository(mysql.Master()),
		enabled:    enabled,
		interval:   interval,
		stopChan:   make(chan struct{}),
		isRunning:  false,
	}
}

// Start 启动同步服务
func (s *OrgSyncService) Start() error {
	s.lock.Lock()
	defer s.lock.Unlock()

	if s.isRunning {
		logx.Info("卫生机构同步服务已经在运行中")
		return nil
	}

	if !s.enabled {
		logx.Info("卫生机构同步服务已禁用，不会同步数据")
		return nil
	}

	s.isRunning = true

	// 仅执行一次同步，不启动定时任务
	// s.ticker = time.NewTicker(time.Duration(s.interval) * time.Hour)
	// 注释掉定时任务部分
	/*
		go func() {
			for {
				select {
				case <-s.ticker.C:
					go s.syncOrganizations()
				case <-s.stopChan:
					s.ticker.Stop()
					return
				}
			}
		}()
	*/

	// 立即执行一次同步
	s.syncOrganizations()

	logx.Info("卫生机构同步服务已启动，将仅执行一次同步操作")
	return nil
}

// Stop 停止同步服务
func (s *OrgSyncService) Stop() {
	s.lock.Lock()
	defer s.lock.Unlock()

	if !s.isRunning {
		return
	}

	close(s.stopChan)
	s.isRunning = false
	logx.Info("卫生机构同步服务已停止")
}

// syncOrganizations 同步卫生机构信息到门店表
func (s *OrgSyncService) syncOrganizations() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 获取配置中的卫生机构ID
	wsjgIDStr := s.client.GetConfig().CurrentWsjgid
	if wsjgIDStr == "" {
		logx.Error("卫生机构ID为空，无法同步", zap.String("CurrentWsjgid", wsjgIDStr))
		return
	}
	logx.Info("卫生机构ID信息", zap.String("CurrentWsjgid", wsjgIDStr))

	// 转换为整数
	wsjgID, err := strconv.Atoi(wsjgIDStr)
	if err != nil {
		logx.Error("卫生机构ID格式错误", zap.String("CurrentWsjgid", wsjgIDStr), zap.Error(err))
		return
	}

	logx.Info("开始同步卫生机构信息", zap.Int("wsjgID", wsjgID))

	// 获取卫生机构信息
	resp, err := s.client.GetHealthOrganization(ctx, wsjgID)
	if err != nil {
		logx.Error("获取卫生机构信息失败", zap.Int("wsjgID", wsjgID), zap.Error(err))
		return
	}

	// 类型断言，提取机构信息
	orgData, ok := resp.Data.(hangzhou.HealthOrganization)
	if !ok {
		logx.Error("卫生机构数据格式错误", zap.Any("data", resp.Data))
		return
	}

	// 根据机构名称查找门店
	store, err := s.storeRepo.FindByName(orgData.WsjgMC)
	if err == nil {
		// 如果已存在，记录日志后直接返回，不进行更新
		logx.Info("门店已存在，跳过同步",
			zap.String("name", orgData.WsjgMC),
			zap.Uint("storeID", store.ID))
		return
	}

	// 解析地址中的省市区信息
	provinceName, cityName, areaName := s.parseAddress(orgData.JgDz)
	logx.Infof("解析地址 '%s' 得到: 省=%s, 市=%s, 区=%s", orgData.JgDz, provinceName, cityName, areaName)

	// 根据省市区名称查询地区编码
	provinceCode, cityCode, areaCode, err := s.getRegionCodes(provinceName, cityName, areaName)
	if err != nil {
		logx.Errorf("查询地区编码失败: %v", err)
		// 继续处理，编码为空
	} else {
		logx.Infof("查询到地区编码: 省=%s, 市=%s, 区=%s", provinceCode, cityCode, areaCode)
	}

	// 如果不存在，创建新门店
	store = &storeModel.Store{
		Name:        orgData.WsjgMC,
		Phone:       orgData.DhHm,
		ProvinceID:  provinceCode,
		CityID:      cityCode,
		AreaID:      areaCode,
		Address:     orgData.JgDz,
		Description: fmt.Sprintf("同步自杭州HIS系统，机构ID: %d", orgData.WsjgID),
		WsjgID:      strconv.Itoa(wsjgID), // 将卫生机构ID保存为string
		Status:      1,                    // 正常状态
		CreatorID:   1,
	}

	err = s.storeRepo.Create(store)
	if err != nil {
		logx.Error("创建门店失败",
			zap.String("name", orgData.WsjgMC),
			zap.Error(err))
		return
	}

	logx.Info("成功创建门店",
		zap.String("name", orgData.WsjgMC),
		zap.Uint("storeID", store.ID),
		zap.String("provinceCode", provinceCode),
		zap.String("cityCode", cityCode),
		zap.String("areaCode", areaCode))

	logx.Info("卫生机构同步完成",
		zap.Int("wsjgID", orgData.WsjgID),
		zap.String("name", orgData.WsjgMC))
}

// parseAddress 解析地址中的省市区信息
func (s *OrgSyncService) parseAddress(address string) (province, city, area string) {
	if address == "" {
		return "", "", ""
	}

	// 常见的省级后缀
	provincePatterns := []string{
		`(.+?省)`, `(.+?自治区)`, `(.+?市)`, `(.+?特别行政区)`,
	}

	// 常见的市级后缀
	cityPatterns := []string{
		`(.+?市)`, `(.+?地区)`, `(.+?州)`, `(.+?盟)`,
	}

	// 常见的区县级后缀
	areaPatterns := []string{
		`(.+?区)`, `(.+?县)`, `(.+?市)`, `(.+?旗)`, `(.+?林区)`,
	}

	// 提取省份
	for _, pattern := range provincePatterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(address); len(matches) > 1 {
			province = matches[1]
			address = strings.Replace(address, province, "", 1)
			break
		}
	}

	// 提取城市
	for _, pattern := range cityPatterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(address); len(matches) > 1 {
			city = matches[1]
			address = strings.Replace(address, city, "", 1)
			break
		}
	}

	// 提取区县
	for _, pattern := range areaPatterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(address); len(matches) > 1 {
			area = matches[1]
			break
		}
	}

	return province, city, area
}

// getRegionCodes 根据省市区名称查询地区编码
func (s *OrgSyncService) getRegionCodes(provinceName, cityName, areaName string) (string, string, string, error) {
	var provinceCode, cityCode, areaCode string

	// 查询省份编码
	if provinceName != "" {
		provinces, err := s.regionRepo.GetProvinces()
		if err == nil {
			for _, province := range provinces {
				if province.Name == provinceName {
					provinceCode = province.Code
					break
				}
			}
		}
	}

	// 查询城市编码
	if cityName != "" && provinceCode != "" {
		cities, err := s.regionRepo.GetCities(provinceCode)
		if err == nil {
			for _, city := range cities {
				if city.Name == cityName {
					cityCode = city.Code
					break
				}
			}
		}
	}

	// 查询区县编码
	if areaName != "" && cityCode != "" {
		areas, err := s.regionRepo.GetAreas(cityCode)
		if err == nil {
			for _, area := range areas {
				if area.Name == areaName {
					areaCode = area.Code
					break
				}
			}
		}
	}

	return provinceCode, cityCode, areaCode, nil
}
