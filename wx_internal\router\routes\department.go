package routes

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest"

	"yekaitai/wx_internal/modules/department/handler"
	"yekaitai/wx_internal/svc"
)

// RegisterDepartmentHandlers 注册科室相关接口
func RegisterDepartmentHandlers(server RestServer, serverCtx *svc.WxServiceContext) {
	// 获取科室列表接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/department/list",
			Handler: handler.GetDepartmentList,
		},
	)

	// 获取科室详情接口
	server.AddRoute(
		rest.Route{
			Method:  http.MethodGet,
			Path:    "/api/wx/department/:id",
			Handler: handler.GetDepartmentDetail,
		},
	)
}
