package handler

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"

	jst "yekaitai/pkg/adapters/jushuitan"
	"yekaitai/wx_internal/svc"
)

// AfterSaleHandler 聚水潭售后处理器
type AfterSaleHandler struct {
	ctx    *svc.WxServiceContext
	client *jst.Client
}

// NewAfterSaleHandler 创建聚水潭售后处理器
func NewAfterSaleHandler(ctx *svc.WxServiceContext) *AfterSaleHandler {
	return &AfterSaleHandler{
		ctx:    ctx,
		client: ctx.JushuitanClient,
	}
}

// UploadAfterSale 售后上传处理
func (h *AfterSaleHandler) UploadAfterSale(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 售后上传原始请求: %s", reqStr)

	// 直接解析请求为售后上传请求列表
	var reqs []jst.AfterSaleRequest
	if err := jsoniter.Unmarshal(bodyBytes, &reqs); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 请求参数校验
	if len(reqs) == 0 {
		err := fmt.Errorf("售后单不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 校验每一个售后单信息
	for i, req := range reqs {
		// 校验必填字段
		if req.ShopID <= 0 {
			err := fmt.Errorf("第%d个售后单店铺编号不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if req.OuterAsID == "" {
			err := fmt.Errorf("第%d个售后单外部单号不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if req.SoID == "" {
			err := fmt.Errorf("第%d个售后单平台订单号不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if req.Type == "" {
			err := fmt.Errorf("第%d个售后单类型不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if req.GoodStatus == "" {
			err := fmt.Errorf("第%d个售后单货物状态不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if len(req.Items) == 0 {
			err := fmt.Errorf("第%d个售后单商品明细不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}

		// 校验每个商品明细
		for j, item := range req.Items {
			if item.SkuID == "" {
				err := fmt.Errorf("第%d个售后单第%d个商品编码不能为空", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
			if item.Qty <= 0 {
				err := fmt.Errorf("第%d个售后单第%d个商品数量必须大于0", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
			if item.Type == "" {
				err := fmt.Errorf("第%d个售后单第%d个商品类型不能为空", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
		}
	}

	LogRequest(r, reqs)

	// 调用聚水潭API
	resp, err := h.client.UploadAfterSale(r.Context(), reqs)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 售后上传原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "售后上传失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// UploadAfterSaleNoInfo 无信息件售后上传处理
func (h *AfterSaleHandler) UploadAfterSaleNoInfo(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 无信息件售后上传原始请求: %s", reqStr)

	// 解析请求
	var req struct {
		Data []jst.AfterSaleNoInfoRequest `json:"data"`
	}
	if err := jsoniter.Unmarshal(bodyBytes, &req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 请求参数校验
	if len(req.Data) == 0 {
		err := fmt.Errorf("售后单不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}
	if len(req.Data) > 100 {
		err := fmt.Errorf("售后单最多支持100个")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 校验每一个售后单信息
	for i, item := range req.Data {
		// 校验类型
		if item.Type == "" {
			err := fmt.Errorf("第%d个售后单类型不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if item.Type != "普通退货" {
			err := fmt.Errorf("第%d个售后单类型目前仅支持\"普通退货\"", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		// 校验店铺ID
		if item.ShopID == 0 {
			err := fmt.Errorf("第%d个售后单店铺编号不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}

		// 校验商品明细
		for j, goods := range item.Items {
			if goods.SkuID == "" {
				err := fmt.Errorf("第%d个售后单第%d个商品编码不能为空", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
			if goods.Qty <= 0 {
				err := fmt.Errorf("第%d个售后单第%d个商品数量必须大于0", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
			if goods.Type == "" {
				err := fmt.Errorf("第%d个售后单第%d个商品类型不能为空", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
			if goods.Type != "退货" {
				err := fmt.Errorf("第%d个售后单第%d个商品类型目前仅支持\"退货\"", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
		}
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.UploadAfterSaleNoInfo(r.Context(), req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 无信息件售后上传原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "无信息件售后上传失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryRefundSingle 售后退货退款查询处理
func (h *AfterSaleHandler) QueryRefundSingle(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 售后退货退款查询原始请求: %s", reqStr)

	// 解析请求
	var req jst.RefundSingleQueryRequest
	if err := jsoniter.Unmarshal(bodyBytes, &req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 时间参数必须成对出现
	if (req.ModifiedBegin == "" && req.ModifiedEnd != "") || (req.ModifiedBegin != "" && req.ModifiedEnd == "") {
		err := fmt.Errorf("修改开始时间和结束时间必须同时存在")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 如果使用时间条件查询，需要与其他条件做互斥校验
	hasTimeCondition := req.ModifiedBegin != "" && req.ModifiedEnd != ""
	hasOrderCondition := len(req.SoIDs) > 0 || len(req.OIDs) > 0 || len(req.AsIDs) > 0

	if !hasTimeCondition && !hasOrderCondition {
		err := fmt.Errorf("必须提供时间范围或订单号条件")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 如果指定了买家账号，数量不能超过50
	if len(req.ShopBuyerIDs) > 50 {
		err := fmt.Errorf("买家账号最多支持50个")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.QueryRefundSingle(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 售后退货退款查询原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "售后退货退款查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// ConfirmAfterSaleGoods 售后确认收到货物处理
func (h *AfterSaleHandler) ConfirmAfterSaleGoods(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 售后确认收到货物原始请求: %s", reqStr)

	// 解析请求
	var reqs []jst.AfterSaleConfirmGoodsRequest
	if err := jsoniter.Unmarshal(bodyBytes, &reqs); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 请求参数校验
	if len(reqs) == 0 {
		err := fmt.Errorf("确认收货请求不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 校验每个请求参数
	for i, req := range reqs {
		// 校验必填参数
		if req.AsID <= 0 {
			err := fmt.Errorf("第%d个请求的售后单号不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if req.LogisticsCompany == "" {
			err := fmt.Errorf("第%d个请求的快递公司不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if req.LID == "" {
			err := fmt.Errorf("第%d个请求的快递单号不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if req.ReturnID == "" {
			err := fmt.Errorf("第%d个请求的唯一单号不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
		if len(req.Items) == 0 {
			err := fmt.Errorf("第%d个请求的收货明细不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}

		// 校验收货明细
		for j, item := range req.Items {
			if item.SkuID == "" {
				err := fmt.Errorf("第%d个请求的第%d个收货明细商品编码不能为空", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
			if item.Qty <= 0 {
				err := fmt.Errorf("第%d个请求的第%d个收货明细数量必须大于0", i+1, j+1)
				HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
				return
			}
		}
	}

	LogRequest(r, reqs)

	// 调用聚水潭API
	resp, err := h.client.ConfirmAfterSaleGoods(r.Context(), reqs)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 售后确认收到货物原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "售后确认收到货物失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryAfterSaleReceived 实际收货查询处理
func (h *AfterSaleHandler) QueryAfterSaleReceived(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 实际收货查询原始请求: %s", reqStr)

	// 解析请求
	var req jst.AfterSaleReceivedQueryRequest
	if err := jsoniter.Unmarshal(bodyBytes, &req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 时间参数校验
	hasTimeCondition := req.ModifiedBegin != "" && req.ModifiedEnd != ""
	if (req.ModifiedBegin == "" && req.ModifiedEnd != "") || (req.ModifiedBegin != "" && req.ModifiedEnd == "") {
		err := fmt.Errorf("修改开始时间和结束时间必须同时存在")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 查询条件校验
	hasOrderCondition := len(req.SoIDs) > 0 || len(req.OIDs) > 0 || len(req.AsIDs) > 0 || len(req.IoIDs) > 0
	if !hasTimeCondition && !hasOrderCondition && req.StartTs <= 0 && req.ShopID == "" {
		err := fmt.Errorf("至少需要一项查询条件")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 如果使用售后单号列表，不能超过50个
	if len(req.AsIDs) > 50 {
		err := fmt.Errorf("售后单号列表最多支持50个")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 如果使用退仓单号列表，不能超过50个
	if len(req.IoIDs) > 50 {
		err := fmt.Errorf("退仓单号列表最多支持50个")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.QueryAfterSaleReceived(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 实际收货查询原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "实际收货查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// ConfirmAfterSale 售后单确认处理
func (h *AfterSaleHandler) ConfirmAfterSale(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 售后单确认原始请求: %s", reqStr)

	// 解析请求
	var req jst.AfterSaleConfirmRequest
	if err := jsoniter.Unmarshal(bodyBytes, &req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 参数校验
	if len(req.AsIDs) == 0 {
		err := fmt.Errorf("售后单id数组不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.ConfirmAfterSale(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 售后单确认原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "售后单确认失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// UnconfirmAfterSale 售后单反确认处理
func (h *AfterSaleHandler) UnconfirmAfterSale(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 售后单反确认原始请求: %s", reqStr)

	// 解析请求
	var req jst.AfterSaleUnconfirmRequest
	if err := jsoniter.Unmarshal(bodyBytes, &req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 参数校验
	if len(req.AsIDs) == 0 {
		err := fmt.Errorf("售后单id数组不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.UnconfirmAfterSale(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 售后单反确认原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "售后单反确认失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// ConfirmAfterSaleBySns 唯一码批量确认收货处理
func (h *AfterSaleHandler) ConfirmAfterSaleBySns(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 唯一码批量确认收货原始请求: %s", reqStr)

	// 解析请求
	var items []jst.AfterSaleConfirmBySnItem
	if err := jsoniter.Unmarshal(bodyBytes, &items); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 参数校验
	if len(items) == 0 {
		err := fmt.Errorf("唯一码列表不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 校验唯一码
	for i, item := range items {
		if item.SkuSn == "" {
			err := fmt.Errorf("第%d个唯一码不能为空", i+1)
			HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
			return
		}
	}

	LogRequest(r, items)

	// 调用聚水潭API
	resp, err := h.client.ConfirmAfterSaleBySns(r.Context(), items)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 唯一码批量确认收货原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "唯一码批量确认收货失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryRefund 退款单查询处理
func (h *AfterSaleHandler) QueryRefund(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 退款单查询原始请求: %s", reqStr)

	// 解析请求
	var req jst.RefundQueryRequest
	if err := jsoniter.Unmarshal(bodyBytes, &req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 参数校验
	if req.ModifiedBegin == "" || req.ModifiedEnd == "" {
		err := fmt.Errorf("修改开始时间和结束时间不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 页码处理
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageIndex > 800 {
		req.PageIndex = 800
	}
	if req.PageSize <= 0 {
		req.PageSize = 1
	}
	if req.PageSize > 1000 {
		req.PageSize = 1000
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.QueryRefund(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 退款单查询原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "退款单查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}
