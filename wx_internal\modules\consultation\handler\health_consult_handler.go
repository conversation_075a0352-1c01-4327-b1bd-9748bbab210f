package handler

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"

	aiModel "yekaitai/internal/modules/ai_data_upload/model"
	"yekaitai/pkg/adapters/medlinker"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/response"
	"yekaitai/wx_internal/modules/consultation/model"
	consultationTypes "yekaitai/wx_internal/modules/consultation/types"
	userModel "yekaitai/wx_internal/modules/user/model"
	"yekaitai/wx_internal/svc"
)

// getUserIDFromContext 从上下文获取用户ID
func getUserIDFromContext(ctx context.Context) (uint, bool) {
	// 首先尝试从types.UserIDKey获取（这是认证中间件设置的）
	if userIDValue := ctx.Value("userId"); userIDValue != nil {
		if userID, ok := userIDValue.(uint); ok {
			return userID, true
		}
		// 如果是string类型的openID，需要通过openID查询用户ID
		if openID, ok := userIDValue.(string); ok {
			// 通过openID查询用户ID
			if userID := getUserIDByOpenID(openID); userID > 0 {
				return userID, true
			}
		}
	}

	// 尝试从其他可能的key获取
	if id, exists := ctx.Value("user_id").(uint); exists {
		return id, true
	}
	if id, exists := ctx.Value("wx_user_id").(uint); exists {
		return id, true
	}
	return 0, false
}

// getUserIDByOpenID 通过OpenID查询用户ID
func getUserIDByOpenID(openID string) uint {
	// 导入必要的包
	db := mysql.GetDB()
	var wxUser userModel.WxUser

	// 查询用户
	if err := db.Where("open_id = ?", openID).First(&wxUser).Error; err != nil {
		logx.Errorf("通过OpenID查询用户失败: %v, openID: %s", err, openID)
		return 0
	}

	logx.Infof("通过OpenID查询到用户ID: %d, openID: %s", wxUser.UserID, openID)
	return wxUser.UserID
}

// getUserPhoneByUserID 通过用户ID获取用户电话号码
func getUserPhoneByUserID(userID uint) string {
	db := mysql.GetDB()
	var wxUser userModel.WxUser

	// 查询用户电话
	if err := db.Where("user_id = ?", userID).First(&wxUser).Error; err != nil {
		logx.Errorf("通过用户ID查询电话失败: %v, userID: %d", err, userID)
		return ""
	}

	if wxUser.Mobile == "" {
		logx.Infof("用户未绑定电话号码: userID=%d", userID)
		return ""
	}

	logx.Infof("获取到用户电话: userID=%d, phone=%s", userID, maskPhone(wxUser.Mobile))
	return wxUser.Mobile
}

// maskPhone 脱敏显示电话号码
func maskPhone(phone string) string {
	if len(phone) < 7 {
		return phone
	}
	return phone[:3] + "****" + phone[len(phone)-4:]
}

// getTokenFromContext 从上下文获取token
func getTokenFromContext(ctx context.Context) string {
	if token, ok := ctx.Value("token").(string); ok {
		return token
	}
	return ""
}

// HealthConsultHandler 健康咨询接口处理器
type HealthConsultHandler struct {
	medlinkerClient *medlinker.MedlinkerClient
	kafkaProducer   medlinker.KafkaProducer
	upgrader        websocket.Upgrader
	phone           string                 // 医联认证电话号码
	svcCtx          *svc.WxServiceContext  // 服务上下文
	sessionModel    model.ChatSessionModel // 会话模型
}

// NewHealthConsultHandler 创建健康咨询处理器
func NewHealthConsultHandler(svcCtx *svc.WxServiceContext, medlinkerClient *medlinker.MedlinkerClient, kafkaProducer medlinker.KafkaProducer) *HealthConsultHandler {
	logx.Info("创建健康咨询处理器")

	// 设置Redis客户端用于调用次数统计
	if svcCtx.RedisClient != nil {
		medlinkerClient.SetRedisClient(svcCtx.RedisClient)
		logx.Info("医联客户端已设置Redis客户端用于调用次数统计")
	} else {
		logx.Infof("Redis客户端未配置，调用次数统计将使用内存模式")
	}

	return &HealthConsultHandler{
		medlinkerClient: medlinkerClient,
		kafkaProducer:   kafkaProducer,
		sessionModel:    model.NewChatSessionModel(),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				logx.Infof("WebSocket CheckOrigin: Origin=%s, Path=%s",
					r.Header.Get("Origin"), r.URL.Path)
				return true
			},
			HandshakeTimeout: 15 * time.Second,
			ReadBufferSize:   4096, // 增加到4KB，与预问诊保持一致
			WriteBufferSize:  4096, // 增加到4KB，与预问诊保持一致
		},
		phone:  svcCtx.Config.Medlinker.Phone, // 从配置文件获取医联认证电话号码
		svcCtx: svcCtx,
	}
}

// InitHealthConsult 初始化健康咨询会话
func (h *HealthConsultHandler) InitHealthConsult(w http.ResponseWriter, r *http.Request) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("InitHealthConsult panic: %v\n%s", err, debug.Stack())
			response.Error(w, http.StatusInternalServerError, "服务器内部错误")
		}
	}()

	// 解析请求参数
	var req consultationTypes.InitConsultationRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析请求参数失败: %v", err)
		response.Error(w, http.StatusBadRequest, "请求参数错误")
		return
	}

	// 验证模型ID
	if !isValidModelID(req.ModelID) {
		logx.Errorf("无效的模型ID: %d", req.ModelID)
		response.Error(w, http.StatusBadRequest, "无效的模型ID")
		return
	}

	// 从中间件获取用户信息
	userID, ok := getUserIDFromContext(r.Context())
	if !ok {
		logx.Error("获取用户信息失败")
		response.Error(w, http.StatusUnauthorized, "用户认证失败")
		return
	}

	// 获取token
	token := getTokenFromContext(r.Context())
	if token == "" {
		// 从Authorization头获取token
		authHeader := r.Header.Get("Authorization")
		if authHeader != "" {
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) == 2 {
				token = parts[1]
			}
		}
	}

	// 生成会话ID
	sessionID := generateSessionID(req.ModelID)
	logx.Infof("生成会话ID: %s, 用户ID: %d, 模型ID: %d", sessionID, userID, req.ModelID)

	// 创建会话记录
	session := &model.ChatSession{
		SessionID:     sessionID,
		UserID:        int64(userID),
		ModelID:       req.ModelID,
		ModelType:     getModelType(req.ModelID),
		Title:         generateSessionTitle(req.ModelID),
		FirstQuestion: "",
		LastMessage:   "",
		MessageCount:  0,
		FullContent:   "",
		Status:        1, // 进行中
		StartTime:     time.Now(),
	}

	if err := h.sessionModel.Insert(r.Context(), session); err != nil {
		logx.Errorf("创建会话记录失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "创建会话失败")
		return
	}

	// 生成WebSocket连接URL（包含路由前缀）
	wsURL := fmt.Sprintf("ws://%s/yekaitai-mini-api/api/wx/consultation/ws/health?token=%s&sessionid=%s&modelid=%d",
		r.Host, token, sessionID, req.ModelID)

	// 返回响应
	resp := consultationTypes.InitConsultationResponse{
		SessionID:    sessionID,
		WebSocketURL: wsURL,
		ModelID:      req.ModelID,
		ModelType:    getModelType(req.ModelID),
	}

	response.Success(w, resp)
	logx.Infof("健康咨询会话初始化成功: %s", sessionID)
}

// GetSessionHistory 获取会话历史记录
func (h *HealthConsultHandler) GetSessionHistory(w http.ResponseWriter, r *http.Request) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("GetSessionHistory panic: %v\n%s", err, debug.Stack())
			response.Error(w, http.StatusInternalServerError, "服务器内部错误")
		}
	}()

	// 从中间件获取用户信息
	userID, ok := getUserIDFromContext(r.Context())
	if !ok {
		logx.Error("获取用户信息失败")
		response.Error(w, http.StatusUnauthorized, "用户认证失败")
		return
	}

	// 获取查询参数
	modelTypeStr := r.URL.Query().Get("model_type")
	pageStr := r.URL.Query().Get("page")
	pageSizeStr := r.URL.Query().Get("page_size")

	// 设置默认值
	page := 1
	pageSize := 20

	if pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	offset := (page - 1) * pageSize

	var sessions []*model.ChatSession
	var err error

	// 根据模型类型查询
	if modelTypeStr != "" {
		// 暂时使用FindByUserID，然后在应用层过滤
		allSessions, err := h.sessionModel.FindByUserID(r.Context(), int64(userID), pageSize*2, offset) // 获取更多数据用于过滤
		if err == nil {
			// 过滤指定模型类型的会话
			for _, session := range allSessions {
				if session.ModelType == modelTypeStr {
					sessions = append(sessions, session)
					if len(sessions) >= pageSize {
						break
					}
				}
			}
		}
	} else {
		sessions, err = h.sessionModel.FindByUserID(r.Context(), int64(userID), pageSize, offset)
	}

	if err != nil {
		logx.Errorf("查询会话历史失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "查询会话历史失败")
		return
	}

	// 转换为响应格式
	var historyList []consultationTypes.SessionHistoryItem
	for _, session := range sessions {
		item := consultationTypes.SessionHistoryItem{
			SessionID:     session.SessionID,
			ModelID:       session.ModelID,
			ModelType:     session.ModelType,
			Title:         session.Title,
			FirstQuestion: session.FirstQuestion,
			LastMessage:   session.LastMessage,
			MessageCount:  session.MessageCount,
			Status:        session.Status,
			StartTime:     session.StartTime.Format("2006-01-02 15:04:05"),
			UpdatedAt:     session.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		historyList = append(historyList, item)
	}

	resp := consultationTypes.SessionHistoryResponse{
		List:     historyList,
		Page:     page,
		PageSize: pageSize,
		Total:    len(historyList),
	}

	response.Success(w, resp)
	logx.Infof("获取用户 %d 会话历史成功，返回 %d 条记录", userID, len(historyList))
}

// GetSessionDetail 获取会话详情
func (h *HealthConsultHandler) GetSessionDetail(w http.ResponseWriter, r *http.Request) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("GetSessionDetail panic: %v\n%s", err, debug.Stack())
			response.Error(w, http.StatusInternalServerError, "服务器内部错误")
		}
	}()

	// 从中间件获取用户信息
	userID, ok := getUserIDFromContext(r.Context())
	if !ok {
		logx.Error("获取用户信息失败")
		response.Error(w, http.StatusUnauthorized, "用户认证失败")
		return
	}

	// 获取会话ID参数
	sessionID := r.URL.Query().Get("session_id")
	if sessionID == "" {
		response.Error(w, http.StatusBadRequest, "会话ID不能为空")
		return
	}

	// 查询会话详情
	session, err := h.sessionModel.FindBySessionID(r.Context(), sessionID)
	if err != nil {
		logx.Errorf("查询会话详情失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "查询会话详情失败")
		return
	}

	if session == nil {
		response.Error(w, http.StatusNotFound, "会话不存在")
		return
	}

	// 验证会话所有权
	if session.UserID != int64(userID) {
		logx.Errorf("会话所有权验证失败: sessionID=%s, userID=%d, sessionUserID=%d",
			sessionID, userID, session.UserID)
		response.Error(w, http.StatusForbidden, "无权访问此会话")
		return
	}

	// 构造响应
	resp := consultationTypes.SessionDetailResponse{
		SessionID:   session.SessionID,
		ModelID:     session.ModelID,
		ModelType:   session.ModelType,
		Title:       session.Title,
		FullContent: session.FullContent,
		Status:      session.Status,
		StartTime:   session.StartTime.Format("2006-01-02 15:04:05"),
		EndTime:     session.EndTime.Format("2006-01-02 15:04:05"),
	}

	response.Success(w, resp)
	logx.Infof("获取会话详情成功: sessionID=%s, userID=%d", sessionID, userID)
}

// 辅助函数
func isValidModelID(modelID int) bool {
	validModels := map[int]bool{
		medlinker.ModelHealthConsult:   true, // 399 健康咨询
		medlinker.ModelPreDiagnosis:    true, // 400 预问诊
		medlinker.ModelReportAnalysis:  true, // 401 报告解读
		medlinker.ModelSmartTriage:     true, // 403 智能分导诊
		medlinker.ModelCustomerService: true, // 405 智能客服
	}
	return validModels[modelID]
}

func getModelType(modelID int) string {
	modelTypes := map[int]string{
		medlinker.ModelHealthConsult:   "health_consult",
		medlinker.ModelPreDiagnosis:    "prediagnosis",
		medlinker.ModelReportAnalysis:  "report_interpret",
		medlinker.ModelSmartTriage:     "smart_triage",
		medlinker.ModelCustomerService: "smart_service",
	}
	return modelTypes[modelID]
}

func generateSessionTitle(modelID int) string {
	titles := map[int]string{
		medlinker.ModelHealthConsult:   "健康咨询",
		medlinker.ModelPreDiagnosis:    "预问诊",
		medlinker.ModelReportAnalysis:  "报告解读",
		medlinker.ModelSmartTriage:     "智能分导诊",
		medlinker.ModelCustomerService: "智能客服",
	}
	return titles[modelID]
}

func generateSessionID(modelID int) string {
	prefixes := map[int]string{
		medlinker.ModelHealthConsult:   "hc",
		medlinker.ModelPreDiagnosis:    "pd",
		medlinker.ModelReportAnalysis:  "ri",
		medlinker.ModelSmartTriage:     "st",
		medlinker.ModelCustomerService: "ss",
	}
	prefix := prefixes[modelID]
	if prefix == "" {
		prefix = "unk"
	}
	return fmt.Sprintf("%s_%s_%d", prefix, time.Now().Format("20060102_150405"), time.Now().UnixNano()%1000)
}

// HandleWebSocket 处理WebSocket连接
func (h *HealthConsultHandler) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("HandleWebSocket panic: %v\n%s", err, debug.Stack())
		}
	}()

	// 获取URL参数
	sessionID := r.URL.Query().Get("sessionid")
	modelIDStr := r.URL.Query().Get("modelid")

	if sessionID == "" || modelIDStr == "" {
		logx.Error("WebSocket连接缺少必要参数")
		http.Error(w, "缺少必要参数", http.StatusBadRequest)
		return
	}

	modelID, err := strconv.Atoi(modelIDStr)
	if err != nil || !isValidModelID(modelID) {
		logx.Errorf("无效的模型ID: %s", modelIDStr)
		http.Error(w, "无效的模型ID", http.StatusBadRequest)
		return
	}

	// 从中间件获取用户信息
	userID, ok := getUserIDFromContext(r.Context())
	if !ok {
		logx.Error("WebSocket认证失败")
		http.Error(w, "认证失败", http.StatusUnauthorized)
		return
	}

	// 验证会话是否存在
	session, err := h.sessionModel.FindBySessionID(r.Context(), sessionID)
	if err != nil {
		logx.Errorf("查询会话失败: %v", err)
		http.Error(w, "会话不存在", http.StatusNotFound)
		return
	}
	if session == nil {
		logx.Errorf("会话不存在: %s", sessionID)
		http.Error(w, "会话不存在", http.StatusNotFound)
		return
	}

	// 验证会话所有权
	if session.UserID != int64(userID) {
		logx.Errorf("会话所有权验证失败: sessionID=%s, userID=%d, sessionUserID=%d",
			sessionID, userID, session.UserID)
		http.Error(w, "无权访问此会话", http.StatusForbidden)
		return
	}

	logx.Infof("开始升级WebSocket连接: sessionID=%s, userID=%d, modelID=%d",
		sessionID, userID, modelID)

	// 升级为WebSocket连接
	conn, err := h.upgrader.Upgrade(w, r, nil)
	if err != nil {
		logx.Errorf("升级WebSocket连接失败: %v", err)
		return
	}

	logx.Infof("WebSocket连接建立成功: sessionID=%s", sessionID)

	// 处理WebSocket连接
	h.handleWebSocketConnection(conn, sessionID, modelID, userID)
}

// handleWebSocketConnection 处理WebSocket连接的核心逻辑
func (h *HealthConsultHandler) handleWebSocketConnection(conn *websocket.Conn, sessionID string, modelID int, userID uint) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("handleWebSocketConnection panic: %v\n%s", err, debug.Stack())
		}
		h.gracefulClose(conn, sessionID)
	}()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置连接参数
	conn.SetReadLimit(32768) // 设置为32KB，足够处理长消息
	conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
		return nil
	})

	// 创建消息发送通道
	sendChan := make(chan consultationTypes.WebSocketMessage, 10)
	done := make(chan struct{})

	// 启动消息发送协程
	go h.handleMessageSending(conn, sendChan, done, sessionID)

	// 启动心跳协程
	go h.handleHeartbeat(conn, done, sessionID)

	// 发送欢迎消息
	welcomeMsg := h.getWelcomeMessage(modelID)
	select {
	case sendChan <- welcomeMsg:
	case <-time.After(5 * time.Second):
		logx.Errorf("发送欢迎消息超时: sessionID=%s", sessionID)
	}

	// 主消息处理循环
	for {
		var msg consultationTypes.WebSocketMessage
		err := conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logx.Errorf("WebSocket读取消息错误: %v", err)
			} else {
				logx.Infof("WebSocket连接正常关闭: sessionID=%s", sessionID)
			}
			break
		}

		logx.Infof("收到WebSocket消息: sessionID=%s, type=%s, content=%s",
			sessionID, msg.Type, msg.Content)

		// 重置读超时
		conn.SetReadDeadline(time.Now().Add(5 * time.Minute))

		// 处理消息
		if err := h.processMessage(ctx, &msg, sessionID, modelID, userID, sendChan); err != nil {
			logx.Errorf("处理消息失败: sessionID=%s, modelID=%d, error=%v", sessionID, modelID, err)

			// 根据错误类型提供更具体的错误信息
			errorContent := "处理消息失败，请重试"
			if strings.Contains(err.Error(), "知识库未同步") {
				errorContent = "智能客服知识库未同步，请联系管理员"
				logx.Errorf("智能客服知识库未同步错误: %v", err)
			} else if strings.Contains(err.Error(), "知识库文档未完整同步") {
				errorContent = "智能客服知识库数据不完整，请联系管理员"
				logx.Errorf("智能客服知识库文档同步错误: %v", err)
			} else if strings.Contains(err.Error(), "获取知识库失败") {
				errorContent = "智能客服知识库配置错误，请联系管理员"
				logx.Errorf("智能客服获取知识库错误: %v", err)
			} else if strings.Contains(err.Error(), "推荐医生功能需要提供位置信息") {
				errorContent = "推荐医生功能需要提供位置信息"
				logx.Errorf("智能导诊位置信息错误: %v", err)
			} else if strings.Contains(err.Error(), "消息内容不能超过2000个字符") {
				errorContent = "消息内容过长，请控制在2000字符以内"
				logx.Errorf("消息内容长度错误: %v", err)
			} else if strings.Contains(err.Error(), "智能客服API返回错误") {
				errorContent = "智能客服服务异常，请稍后重试"
				logx.Errorf("智能客服API错误: %v", err)
			} else {
				logx.Errorf("未知错误类型: %v", err)
			}

			errorMsg := consultationTypes.WebSocketMessage{
				Type:      "error",
				Content:   errorContent,
				SessionID: sessionID,
				IsFinal:   true,
			}
			select {
			case sendChan <- errorMsg:
			case <-time.After(5 * time.Second):
				logx.Errorf("发送错误消息超时: sessionID=%s", sessionID)
			}
		}
	}

	// 通知协程退出
	close(done)
	close(sendChan)

	logx.Infof("WebSocket连接处理结束: sessionID=%s", sessionID)
}

// handleMessageSending 处理消息发送
func (h *HealthConsultHandler) handleMessageSending(conn *websocket.Conn, sendChan <-chan consultationTypes.WebSocketMessage, done <-chan struct{}, sessionID string) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("handleMessageSending panic: %v\n%s", err, debug.Stack())
		}
	}()

	for {
		select {
		case msg, ok := <-sendChan:
			if !ok {
				logx.Infof("发送通道已关闭: sessionID=%s", sessionID)
				return
			}

			conn.SetWriteDeadline(time.Now().Add(2 * time.Minute))
			if err := conn.WriteJSON(msg); err != nil {
				logx.Errorf("发送消息失败: %v, sessionID=%s", err, sessionID)
				return
			}

			logx.Infof("发送消息成功: type=%s, sessionID=%s", msg.Type, sessionID)

		case <-done:
			logx.Infof("消息发送协程退出: sessionID=%s", sessionID)
			return
		}
	}
}

// handleHeartbeat 处理心跳
func (h *HealthConsultHandler) handleHeartbeat(conn *websocket.Conn, done <-chan struct{}, sessionID string) {
	defer func() {
		if err := recover(); err != nil {
			logx.Errorf("handleHeartbeat panic: %v\n%s", err, debug.Stack())
		}
	}()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			conn.SetWriteDeadline(time.Now().Add(5 * time.Second))
			if err := conn.WriteControl(websocket.PingMessage, []byte("ping"), time.Now().Add(5*time.Second)); err != nil {
				logx.Errorf("发送心跳失败: %v, sessionID=%s", err, sessionID)
				return
			}
			logx.Infof("发送心跳成功: sessionID=%s", sessionID)

		case <-done:
			logx.Infof("心跳协程退出: sessionID=%s", sessionID)
			return
		}
	}
}

// getWelcomeMessage 获取欢迎消息
func (h *HealthConsultHandler) getWelcomeMessage(modelID int) consultationTypes.WebSocketMessage {
	welcomeTexts := map[int]string{
		medlinker.ModelHealthConsult:   "您好！我是您的健康咨询助手，有什么健康问题可以咨询我。",
		medlinker.ModelPreDiagnosis:    "您好！我是您的预问诊助手，请详细描述您的症状，我会为您提供初步的健康建议。",
		medlinker.ModelReportAnalysis:  "您好！我是您的报告解读助手，请上传您的检查报告，我会为您详细解读。",
		medlinker.ModelSmartTriage:     "您好！我是您的智能分导诊助手，请描述您的症状，我会为您推荐合适的科室。",
		medlinker.ModelCustomerService: "您好！我是您的智能客服，有什么问题可以咨询我。",
	}

	welcomeText := welcomeTexts[modelID]
	if welcomeText == "" {
		welcomeText = "您好！欢迎使用智能咨询服务，有什么问题可以咨询我。"
	}

	return consultationTypes.WebSocketMessage{
		Type:      "welcome",
		Content:   welcomeText,
		SessionID: "",
		IsFinal:   false,
		ModelID:   modelID,
	}
}

// gracefulClose 优雅关闭WebSocket连接
func (h *HealthConsultHandler) gracefulClose(conn *websocket.Conn, sessionID string) {
	if conn == nil {
		return
	}

	logx.Infof("开始优雅关闭WebSocket连接: sessionID=%s", sessionID)

	// 设置较短的写超时
	conn.SetWriteDeadline(time.Now().Add(3 * time.Second))

	// 发送关闭消息
	closeMsg := websocket.FormatCloseMessage(
		websocket.CloseNormalClosure,
		"会话结束")

	err := conn.WriteMessage(websocket.CloseMessage, closeMsg)
	if err != nil {
		logx.Errorf("发送关闭消息失败: %v, sessionID=%s", err, sessionID)
	} else {
		logx.Infof("成功发送WebSocket关闭消息: sessionID=%s", sessionID)

		// 等待客户端响应的Close帧
		conn.SetReadDeadline(time.Now().Add(3 * time.Second))
		messageType, closePayload, readErr := conn.ReadMessage()
		if readErr != nil {
			logx.Infof("等待客户端关闭响应时发生错误(这通常是正常的): %v, sessionID=%s", readErr, sessionID)
		} else if messageType == websocket.CloseMessage {
			logx.Infof("收到客户端关闭帧: %v, sessionID=%s", closePayload, sessionID)
		}
	}

	// 关闭底层连接
	conn.Close()
	logx.Infof("WebSocket连接已关闭: sessionID=%s", sessionID)
}

// processMessage 处理用户消息
func (h *HealthConsultHandler) processMessage(ctx context.Context, msg *consultationTypes.WebSocketMessage, sessionID string, modelID int, userID uint, sendChan chan<- consultationTypes.WebSocketMessage) error {
	if msg.Content == "" && (msg.ImageData == nil || len(msg.ImageData.Images) == 0) {
		return fmt.Errorf("消息内容不能为空")
	}

	// 检查内容长度限制（2000个字符）
	if len([]rune(msg.Content)) > 2000 {
		return fmt.Errorf("消息内容不能超过2000个字符，当前长度：%d", len([]rune(msg.Content)))
	}

	logx.Infof("开始处理消息: sessionID=%s, modelID=%d, msgType=%d", sessionID, modelID, msg.MsgType)

	// 更新会话信息 - 保存用户提问
	session, err := h.sessionModel.FindBySessionID(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("查询会话失败: %v", err)
	}

	if session != nil {
		// 构建用户提问内容
		var userQuestion string
		if msg.MsgType == 407 && msg.ImageData != nil {
			// 图片消息
			userQuestion = fmt.Sprintf("\n\n**用户：**\n%s\n图片数量：%d张",
				msg.ImageData.Content, len(msg.ImageData.Images))
		} else {
			// 普通文本消息
			userQuestion = fmt.Sprintf("\n\n**用户：**\n%s", msg.Content)
		}

		if session.FirstQuestion == "" {
			// 这是第一条消息，设置首次提问
			if msg.MsgType == 407 && msg.ImageData != nil {
				session.FirstQuestion = msg.ImageData.Content
			} else {
				session.FirstQuestion = msg.Content
			}
			session.MessageCount = 1
			session.FullContent = userQuestion
		} else {
			// 追加到现有对话记录
			session.MessageCount++
			session.FullContent += userQuestion
		}

		// 更新会话
		if err := h.sessionModel.Update(ctx, session); err != nil {
			logx.Errorf("更新会话用户提问失败: %v", err)
		}

		logx.Infof("保存用户提问成功: sessionID=%s, messageCount=%d", sessionID, session.MessageCount)
	}

	// 调用医联API
	return h.callMedlinkerAPIWithImages(ctx, msg, sessionID, modelID, sendChan)
}

// callMedlinkerAPIWithImages 调用医联API（支持图片）
func (h *HealthConsultHandler) callMedlinkerAPIWithImages(ctx context.Context, msg *consultationTypes.WebSocketMessage, sessionID string, modelID int, sendChan chan<- consultationTypes.WebSocketMessage) error {
	// 获取用户电话号码
	userID, exists := getUserIDFromContext(ctx)
	var phone string
	if exists {
		phone = getUserPhoneByUserID(userID)
	}

	// 如果用户未绑定电话，使用配置中的默认电话（用于开发测试）
	if phone == "" {
		phone = h.phone
		logx.Infof("用户未绑定电话，使用默认电话进行医联认证: %s", maskPhone(phone))
	}

	// 先进行医联登录认证
	err := h.medlinkerClient.Login(phone)
	if err != nil {
		logx.Errorf("医联登录失败: %v", err)
		return fmt.Errorf("医联登录失败: %v", err)
	}

	logx.Infof("医联登录成功，开始调用聊天接口: sessionID=%s, modelID=%d, msgType=%d", sessionID, modelID, msg.MsgType)

	var responseStream io.ReadCloser

	// 智能客服（ModelID: 405）需要特殊处理
	if modelID == 405 {
		logx.Infof("开始调用智能客服API: sessionID=%s, content=%s", sessionID, msg.Content)
		// 智能客服需要知识库ID
		responseStream, err = h.callSmartServiceAPI(ctx, msg, sessionID)
		if err != nil {
			logx.Errorf("调用智能客服接口失败: sessionID=%s, error=%v", sessionID, err)
			return fmt.Errorf("调用智能客服接口失败: %v", err)
		}
		logx.Infof("智能客服API调用成功: sessionID=%s", sessionID)
	} else if modelID == 403 {
		// 智能导诊（ModelID: 403）需要特殊处理
		responseStream, err = h.callSmartDiagnosisAPI(ctx, msg, sessionID)
		if err != nil {
			logx.Errorf("调用智能导诊接口失败: %v", err)
			return fmt.Errorf("调用智能导诊接口失败: %v", err)
		}
	} else {
		// 其他AI模型（健康咨询、预问诊、报告解读等）
		// 根据消息类型选择不同的API调用方式
		if msg.MsgType == 407 && msg.ImageData != nil {
			// 图片消息，使用ChatWithImages方法
			responseStream, err = h.medlinkerClient.ChatWithImages(modelID, msg.ImageData.Content, msg.ImageData.Images, sessionID)
			if err != nil {
				logx.Errorf("调用医联图片聊天接口失败: %v", err)
				return fmt.Errorf("调用医联图片聊天接口失败: %v", err)
			}
		} else {
			// 普通文本消息，使用原有的Chat方法
			responseStream, err = h.medlinkerClient.Chat(modelID, msg.Content, sessionID)
			if err != nil {
				logx.Errorf("调用医联聊天接口失败: %v", err)
				return fmt.Errorf("调用医联聊天接口失败: %v", err)
			}
		}
	}
	defer responseStream.Close()

	// 处理流式响应
	return h.processStreamResponse(ctx, responseStream, sessionID, modelID, sendChan)
}

// processStreamResponse 处理流式响应
func (h *HealthConsultHandler) processStreamResponse(ctx context.Context, responseStream io.ReadCloser, sessionID string, modelID int, sendChan chan<- consultationTypes.WebSocketMessage) error {
	// 处理流式响应
	var fullContent strings.Builder
	messageCount := 0

	// 读取流式数据
	buffer := make([]byte, 4096)
	for {
		n, err := responseStream.Read(buffer)
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			logx.Errorf("读取响应流失败: %v", err)
			break
		}

		chunk := string(buffer[:n])
		// 按|||分割多个响应
		responses := strings.Split(chunk, "|||")

		for _, response := range responses {
			response = strings.TrimSpace(response)
			if response == "" {
				continue
			}

			messageCount++

			// 解析响应数据
			var responseData map[string]interface{}
			if err := jsoniter.Unmarshal([]byte(response), &responseData); err != nil {
				logx.Errorf("解析医联响应失败: %v, response: %s", err, response)
				continue
			}

			// 提取消息内容和类型
			content := ""
			msgType := 0
			var cardData *consultationTypes.MedicalCard
			var imageData *consultationTypes.ReportImages

			if data, ok := responseData["data"].(map[string]interface{}); ok {
				if answer, ok := data["answer"].([]interface{}); ok && len(answer) > 0 {
					if answerItem, ok := answer[0].(map[string]interface{}); ok {
						// 获取msgType
						if msgTypeFloat, ok := answerItem["msgType"].(float64); ok {
							msgType = int(msgTypeFloat)
						}

						// 根据msgType处理不同类型的内容
						switch msgType {
						case 366:
							// 病历卡片数据
							if contentObj, ok := answerItem["content"].(map[string]interface{}); ok {
								cardData = h.parseMedicalCard(contentObj)
								if cardData != nil {
									content = "收到病历卡片数据"
								}
							}
						case 367:
							// 智能导诊推荐医生
							if contentStr, ok := answerItem["content"].(string); ok {
								content = contentStr
								logx.Infof("智能导诊推荐医生: %s", contentStr)
							}
						case 369:
							// 智能客服院内功能跳转
							if contentStr, ok := answerItem["content"].(string); ok {
								content = contentStr
								logx.Infof("智能客服院内功能跳转: %s", contentStr)
							}
						case 407:
							// 图片消息响应
							if contentStr, ok := answerItem["content"].(string); ok {
								content = contentStr
								// 可以在这里解析图片相关的响应数据
							}
						default:
							// 普通文本消息
							if contentStr, ok := answerItem["content"].(string); ok {
								content = contentStr
							}
						}
					}
				}
			}

			if content != "" || cardData != nil {
				// 保留原始格式，不做任何清理
				if content != "" {
					fullContent.WriteString(content)
				}
			}

			// 判断是否为最后一条消息
			isFinal := false
			if typeStr, ok := responseData["type"].(string); ok && typeStr == "full" {
				isFinal = true
			}

			// 确定消息类型
			messageType := "chunk"
			if msgType == 366 {
				messageType = "medical_card"
			} else if msgType == 367 {
				messageType = "doctor_recommendation" // 智能导诊推荐医生
			} else if msgType == 369 {
				messageType = "intent" // 智能客服院内功能跳转
			} else if msgType == 407 {
				messageType = "report_image"
			}

			// 发送消息到前端
			wsMsg := consultationTypes.WebSocketMessage{
				Type:      messageType,
				Content:   content,
				SessionID: sessionID,
				IsFinal:   isFinal,
				ModelID:   modelID,
				MsgType:   msgType,
				CardData:  cardData,
				ImageData: imageData,
			}

			select {
			case sendChan <- wsMsg:
			case <-time.After(5 * time.Second):
				logx.Errorf("发送消息到前端超时: sessionID=%s", sessionID)
			}

			// 异步保存到Kafka
			go func(chunkData string) {
				key := []byte(sessionID)
				value := []byte(chunkData)
				_, _, err := h.kafkaProducer.SendMessage("medical_chat_stream", key, value)
				if err != nil {
					logx.Errorf("发送消息到Kafka失败: %v", err)
				}
			}(response)

			// 如果是最后一条消息，更新会话状态
			if isFinal {
				logx.Infof("收到最后一条消息，更新会话状态: sessionID=%s", sessionID)

				// 更新会话信息
				session, err := h.sessionModel.FindBySessionID(ctx, sessionID)
				if err == nil && session != nil {
					// 保留原始格式，追加AI回答到对话记录
					aiAnswer := fmt.Sprintf("\n\n**AI：**\n%s", fullContent.String())

					// 追加到现有对话记录，而不是覆盖
					session.FullContent += aiAnswer
					session.LastMessage = content
					session.MessageCount = messageCount
					session.Status = 2 // 已结束
					session.EndTime = time.Now()

					if err := h.sessionModel.Update(ctx, session); err != nil {
						logx.Errorf("更新会话状态失败: %v", err)
					} else {
						logx.Infof("保存AI回答成功: sessionID=%s, 内容长度=%d", sessionID, len(session.FullContent))
					}
				}

				logx.Infof("医联API调用完成: sessionID=%s, 总消息数=%d", sessionID, messageCount)
				return nil
			}
		}
	}

	logx.Infof("医联API调用完成: sessionID=%s, 总消息数=%d", sessionID, messageCount)
	return nil
}

// callMedlinkerAPI 调用医联API（兼容性方法）
func (h *HealthConsultHandler) callMedlinkerAPI(ctx context.Context, content, sessionID string, modelID int, sendChan chan<- consultationTypes.WebSocketMessage) error {
	// 获取用户电话号码
	userID, exists := getUserIDFromContext(ctx)
	var phone string
	if exists {
		phone = getUserPhoneByUserID(userID)
	}

	// 如果用户未绑定电话，使用配置中的默认电话（用于开发测试）
	if phone == "" {
		phone = h.phone
		logx.Infof("用户未绑定电话，使用默认电话进行医联认证: %s", maskPhone(phone))
	}

	// 先进行医联登录认证
	err := h.medlinkerClient.Login(phone)
	if err != nil {
		logx.Errorf("医联登录失败: %v", err)
		return fmt.Errorf("医联登录失败: %v", err)
	}

	logx.Infof("医联登录成功，开始调用聊天接口: sessionID=%s, modelID=%d", sessionID, modelID)

	// 调用医联聊天接口
	responseStream, err := h.medlinkerClient.Chat(modelID, content, sessionID)
	if err != nil {
		logx.Errorf("调用医联聊天接口失败: %v", err)
		return fmt.Errorf("调用医联聊天接口失败: %v", err)
	}
	defer responseStream.Close()

	// 处理流式响应
	return h.processStreamResponse(ctx, responseStream, sessionID, modelID, sendChan)
}

// parseMedicalCard 解析病历卡片数据
func (h *HealthConsultHandler) parseMedicalCard(contentObj map[string]interface{}) *consultationTypes.MedicalCard {
	card := &consultationTypes.MedicalCard{}

	// 解析prompt_title
	if promptTitle, ok := contentObj["prompt_title"].(string); ok {
		card.PromptTitle = promptTitle
	}

	// 解析card_msg数组
	if cardMsgArray, ok := contentObj["card_msg"].([]interface{}); ok {
		for _, item := range cardMsgArray {
			if itemMap, ok := item.(map[string]interface{}); ok {
				cardItem := consultationTypes.MedicalCardItem{}

				// 解析type字段
				if typeFloat, ok := itemMap["type"].(float64); ok {
					cardItem.Type = int(typeFloat)
				}

				// 根据type字段解析不同的内容
				if cardItem.Type == 2 {
					// 标题类型
					if subType, ok := itemMap["sub_type"].(float64); ok {
						cardItem.SubType = int(subType)
					}
					if subName, ok := itemMap["sub_name"].(string); ok {
						cardItem.SubName = subName
					}
					if subContent, ok := itemMap["sub_content"].(string); ok {
						cardItem.SubContent = subContent
					}
				} else if cardItem.Type == 3 {
					// 总结语类型
					if summaryType, ok := itemMap["summary_type"].(float64); ok {
						cardItem.SummaryType = int(summaryType)
					}
					if summaryContent, ok := itemMap["summary_content"].(string); ok {
						cardItem.SummaryContent = summaryContent
					}
				}

				card.CardMsg = append(card.CardMsg, cardItem)
			}
		}
	}

	// 如果解析到了有效数据，返回卡片对象
	if card.PromptTitle != "" || len(card.CardMsg) > 0 {
		logx.Infof("成功解析病历卡片: title=%s, items=%d", card.PromptTitle, len(card.CardMsg))
		return card
	}

	return nil
}

// callSmartServiceAPI 调用智能客服API
func (h *HealthConsultHandler) callSmartServiceAPI(ctx context.Context, msg *consultationTypes.WebSocketMessage, sessionID string) (io.ReadCloser, error) {
	logx.Infof("智能客服API调用开始: sessionID=%s, content=%s", sessionID, msg.Content)

	// 获取知识库信息
	kbRepo := aiModel.NewAIKnowledgeBaseRepository(mysql.GetDB())
	kb, err := kbRepo.GetActiveKnowledgeBase()
	if err != nil {
		logx.Errorf("获取知识库失败: %v", err)
		return nil, fmt.Errorf("获取知识库失败: %v", err)
	}

	logx.Infof("获取到知识库信息: ID=%d, FileName=%s, SyncStatus=%s, MedlinkerKnowledgeID=%s, MedlinkerDocumentID=%s",
		kb.ID, kb.FileName, kb.SyncStatus, kb.MedlinkerKnowledgeID, kb.MedlinkerDocumentID)

	if kb.MedlinkerKnowledgeID == "" {
		logx.Errorf("知识库未同步到医联: MedlinkerKnowledgeID为空")
		return nil, fmt.Errorf("知识库未同步到医联")
	}

	// 检查知识库数据完整性
	if kb.MedlinkerDocumentID == "" {
		logx.Errorf("知识库文档未完整同步到医联: MedlinkerDocumentID为空")
		return nil, fmt.Errorf("知识库文档未完整同步到医联")
	}

	// 使用知识库记录的ID作为hospital_id
	hospitalID := fmt.Sprintf("hospital_%d", kb.ID)

	logx.Infof("智能客服使用知识库: ID=%d, KnowledgeID=%s, DocumentID=%s, HospitalID=%s",
		kb.ID, kb.MedlinkerKnowledgeID, kb.MedlinkerDocumentID, hospitalID)

	// 构建智能客服请求 - 使用特殊的参数格式
	reqData := map[string]interface{}{
		"sessionType": 72, // 智能客服固定值
		"messages": []map[string]interface{}{
			{
				"role":    "user",
				"content": msg.Content,
			},
		},
		"sessionid":       sessionID,
		"third_sessionid": fmt.Sprintf("smart_service_%d_%s", time.Now().Unix(), sessionID),
		"hospital_id":     hospitalID, // 使用知识库记录的ID作为hospital_id
		"knowledge_id":    kb.MedlinkerKnowledgeID,
		"intent_arr": []map[string]interface{}{
			{
				"name":    "院内指引",
				"content": "帮助患者和访客更好的了解医院环境，可以查询到医院每个楼层的科室部署情况，了解每个科室所在的具体位置。",
			},
			{
				"name":    "来院导航",
				"content": "为来医院的患者提供地图导航，可以通过地图软件通过公交、地铁、出租车、步行方式的路线。",
			},
			{
				"name":    "智能导诊",
				"content": "患者不清楚当前的病症应该挂哪个科室，可以通过智能导诊功能，为患者推荐适合当前病症的科室。",
			},
			{
				"name":    "排队叫号",
				"content": "已经预约挂号之后，患者可以通过排队叫号功能，查看当前科室门诊的叫号屏上的叫号情况。",
			},
			{
				"name":    "诊间支付",
				"content": "患者可以通过诊间支付完成线上支付医院开具的收费单。",
			},
		},
	}

	// 序列化请求数据
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	logx.Infof("智能客服请求数据: %s", string(reqBody))

	// 创建HTTP请求
	url := fmt.Sprintf("%s/api/med/chat", h.medlinkerClient.Config.BaseURL)
	logx.Infof("智能客服请求URL: %s", url)

	req, err := http.NewRequest("POST", url, strings.NewReader(string(reqBody)))
	if err != nil {
		logx.Errorf("创建HTTP请求失败: %v", err)
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头 - 使用与其他AI模型相同的token
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", h.medlinkerClient.GetToken())

	logx.Infof("智能客服请求头: Content-Type=%s, Token=%s",
		req.Header.Get("Content-Type"), h.medlinkerClient.GetToken()[:10]+"...")

	// 发送请求
	client := &http.Client{Timeout: 120 * time.Second}
	logx.Infof("开始发送智能客服请求...")

	resp, err := client.Do(req)
	if err != nil {
		logx.Errorf("请求医联服务器失败: %v", err)
		return nil, fmt.Errorf("请求医联服务器失败: %w", err)
	}

	logx.Infof("智能客服响应状态码: %d, Content-Type: %s",
		resp.StatusCode, resp.Header.Get("Content-Type"))

	// 如果状态码不是200，读取错误响应
	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()
		logx.Errorf("智能客服API返回错误: 状态码=%d, 响应内容=%s", resp.StatusCode, string(body))
		return nil, fmt.Errorf("智能客服API返回错误: 状态码=%d, 响应=%s", resp.StatusCode, string(body))
	}

	logx.Infof("智能客服请求成功，开始处理流式响应")
	return resp.Body, nil
}

// callSmartDiagnosisAPI 调用智能导诊API
func (h *HealthConsultHandler) callSmartDiagnosisAPI(ctx context.Context, msg *consultationTypes.WebSocketMessage, sessionID string) (io.ReadCloser, error) {
	logx.Infof("智能导诊处理用户消息: %s, 推荐医生: %v", msg.Content, msg.RecommendDoctor)

	// 获取用户位置信息
	var longitude, latitude string

	// 如果前端要求推荐医生，则必须提供位置信息
	if msg.RecommendDoctor {
		if msg.LocationData == nil || msg.LocationData.Longitude == "" || msg.LocationData.Latitude == "" {
			return nil, fmt.Errorf("推荐医生功能需要提供位置信息")
		}
		longitude = msg.LocationData.Longitude
		latitude = msg.LocationData.Latitude
		logx.Infof("智能导诊推荐医生模式，使用用户位置: 经度=%s, 纬度=%s", longitude, latitude)
	} else {
		// 普通导诊模式，使用默认位置
		longitude = "116.397128" // 默认经度（北京）
		latitude = "39.916527"   // 默认纬度（北京）
		logx.Infof("智能导诊普通模式，使用默认位置: 经度=%s, 纬度=%s", longitude, latitude)
	}

	// 构建智能导诊请求 - 使用特殊的参数格式
	reqData := map[string]interface{}{
		"session_type": 18, // 智能导诊固定值
		"messages": []map[string]interface{}{
			{
				"role":    "user",
				"content": msg.Content,
			},
		},
		"sessionid":       sessionID,
		"third_sessionid": fmt.Sprintf("smart_diagnosis_%d_%s", time.Now().Unix(), sessionID),
		"longitude":       longitude,
		"latitude":        latitude,
	}

	// hospital_id是可选参数，可以不传

	// 序列化请求数据
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	logx.Infof("智能导诊请求数据: %s", string(reqBody))

	// 创建HTTP请求
	url := fmt.Sprintf("%s/api/med/chat", h.medlinkerClient.Config.BaseURL)
	req, err := http.NewRequest("POST", url, strings.NewReader(string(reqBody)))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头 - 使用与其他AI模型相同的token
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("token", h.medlinkerClient.GetToken())

	logx.Infof("智能导诊请求URL: %s, Token: %s", url, h.medlinkerClient.GetToken()[:10]+"...")

	// 发送请求
	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求医联服务器失败: %w", err)
	}

	logx.Infof("智能导诊响应状态码: %d", resp.StatusCode)

	return resp.Body, nil
}
