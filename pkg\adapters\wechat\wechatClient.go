package wechat

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// Config 微信配置
type Config struct {
	AppID     string `json:"appID"`
	AppSecret string `json:"appSecret"`
}

// Client 是 wechatClient 的公开类型别名
type Client = wechatClient

// wechatClient 微信客户端
type wechatClient struct {
	Config Config
}

// Code2SessionResponse 微信登录返回数据
type Code2SessionResponse struct {
	OpenID     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionID    string `json:"unionid,omitempty"`
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
}

// NewClient 创建微信客户端
func NewClient(config Config) *wechatClient {
	return &wechatClient{
		Config: config,
	}
}

// Code2Session 通过小程序code获取用户openId和会话密钥
func (c *wechatClient) Code2Session(code string) (*Code2SessionResponse, error) {
	url := fmt.Sprintf(
		"https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		c.Config.AppID,
		c.Config.AppSecret,
		code,
	)

	// 使用HTTP客户端请求微信API
	httpClient := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := httpClient.Get(url)
	if err != nil {
		logx.Errorf("微信Code2Session请求失败: %v", err)
		return nil, fmt.Errorf("请求微信服务器失败: %w", err)
	}
	defer resp.Body.Close()

	var result Code2SessionResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		logx.Errorf("解析微信Code2Session响应失败: %v", err)
		return nil, fmt.Errorf("解析微信响应失败: %w", err)
	}

	// 检查返回错误码
	if result.ErrCode != 0 {
		logx.Errorf("微信Code2Session返回错误: code=%d, msg=%s", result.ErrCode, result.ErrMsg)
		return &result, fmt.Errorf("微信服务错误: %s (错误码: %d)", result.ErrMsg, result.ErrCode)
	}

	return &result, nil
}

// 全局默认客户端实例
var DefaultClient *wechatClient

// Init 初始化默认客户端
func Init(config Config) {
	DefaultClient = NewClient(config)
}
