package handler

import (
	"encoding/json"
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"

	adminModel "yekaitai/internal/modules/admin/model"
	storeModel "yekaitai/internal/modules/store/model"
	"yekaitai/pkg/infra/mysql"
)

// StoreListRequest 门店列表请求
type StoreListRequest struct {
	Page       int    `form:"page,default=1"`       // 页码，默认1
	PageSize   int    `form:"page_size,default=10"` // 每页数量，默认10
	Query      string `form:"query,optional"`       // 搜索关键词
	ProvinceID string `form:"province_id,optional"` // 省份编码
	CityID     string `form:"city_id,optional"`     // 城市编码
	AreaID     string `form:"area_id,optional"`     // 区县编码
}

// StoreDetailRequest 门店详情请求
type StoreDetailRequest struct {
	StoreID uint `path:"id"` // 门店ID
}

// BaseResponse 基础响应
type BaseResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// PageData 分页数据
type PageData struct {
	List  interface{} `json:"list"`
	Total int64       `json:"total"`
	Page  int         `json:"page"`
	Size  int         `json:"size"`
}

// ListStores 获取门店列表
func ListStores(w http.ResponseWriter, r *http.Request) {
	var req StoreListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 1,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 创建门店仓库
	storeRepo := storeModel.NewStoreRepository(mysql.Master())

	// 构建查询条件
	query := req.Query
	if req.ProvinceID != "" || req.CityID != "" || req.AreaID != "" {
		// 增加地区筛选条件
		db := mysql.Master().Model(&storeModel.Store{})

		if req.ProvinceID != "" {
			db = db.Where("province_id = ?", req.ProvinceID)
		}
		if req.CityID != "" {
			db = db.Where("city_id = ?", req.CityID)
		}
		if req.AreaID != "" {
			db = db.Where("area_id = ?", req.AreaID)
		}

		// 只查询状态正常的门店
		db = db.Where("status = ?", 1)
	}

	// 查询门店列表
	stores, total, err := storeRepo.List(req.Page, req.PageSize, query)
	if err != nil {
		logx.Errorf("获取门店列表失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 1,
			Msg:  "获取门店列表失败",
			Data: nil,
		})
		return
	}

	// 处理门店数据
	result := make([]map[string]interface{}, len(stores))
	for i, store := range stores {
		storeMap := map[string]interface{}{
			"id":          store.ID,
			"name":        store.Name,
			"phone":       store.Phone,
			"province_id": store.ProvinceID,
			"city_id":     store.CityID,
			"area_id":     store.AreaID,
			"address":     store.Address,
			"latitude":    store.Latitude,
			"longitude":   store.Longitude,
			"description": store.Description,
			"wsjg_id":     store.WsjgID,
		}

		// 解析门店图片
		var images []string
		if store.Images != "" {
			if err := json.Unmarshal([]byte(store.Images), &images); err != nil {
				logx.Errorf("解析门店图片失败: %v", err)
				images = []string{}
			}
		}
		storeMap["images"] = images

		// 获取地区名称
		regionRepo := adminModel.NewRegionRepository(mysql.Slave())
		if store.ProvinceID != "" {
			province, err := regionRepo.FindByCode(store.ProvinceID)
			if err == nil {
				storeMap["province_name"] = province.Name
			}
		}
		if store.CityID != "" {
			city, err := regionRepo.FindByCode(store.CityID)
			if err == nil {
				storeMap["city_name"] = city.Name
			}
		}
		if store.AreaID != "" {
			area, err := regionRepo.FindByCode(store.AreaID)
			if err == nil {
				storeMap["area_name"] = area.Name
			}
		}

		result[i] = storeMap
	}

	// 返回响应
	httpx.OkJson(w, &BaseResponse{
		Code: 0,
		Msg:  "success",
		Data: &PageData{
			List:  result,
			Total: total,
			Page:  req.Page,
			Size:  req.PageSize,
		},
	})
}

// GetStore 获取门店详情
func GetStore(w http.ResponseWriter, r *http.Request) {
	var req StoreDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.OkJson(w, &BaseResponse{
			Code: 1,
			Msg:  "参数错误",
			Data: nil,
		})
		return
	}

	// 创建门店仓库
	storeRepo := storeModel.NewStoreRepository(mysql.Master())

	// 查询门店详情
	store, err := storeRepo.FindByID(req.StoreID)
	if err != nil {
		logx.Errorf("获取门店详情失败: %v", err)
		httpx.OkJson(w, &BaseResponse{
			Code: 1,
			Msg:  "门店不存在",
			Data: nil,
		})
		return
	}

	// 构建结果
	result := map[string]interface{}{
		"id":          store.ID,
		"name":        store.Name,
		"phone":       store.Phone,
		"province_id": store.ProvinceID,
		"city_id":     store.CityID,
		"area_id":     store.AreaID,
		"address":     store.Address,
		"latitude":    store.Latitude,
		"longitude":   store.Longitude,
		"description": store.Description,
		"wsjg_id":     store.WsjgID,
	}

	// 解析门店图片
	var images []string
	if store.Images != "" {
		if err := json.Unmarshal([]byte(store.Images), &images); err != nil {
			logx.Errorf("解析门店图片失败: %v", err)
			images = []string{}
		}
	}
	result["images"] = images

	// 获取地区名称
	regionRepo := adminModel.NewRegionRepository(mysql.Slave())
	if store.ProvinceID != "" {
		province, err := regionRepo.FindByCode(store.ProvinceID)
		if err == nil {
			result["province_name"] = province.Name
		}
	}
	if store.CityID != "" {
		city, err := regionRepo.FindByCode(store.CityID)
		if err == nil {
			result["city_name"] = city.Name
		}
	}
	if store.AreaID != "" {
		area, err := regionRepo.FindByCode(store.AreaID)
		if err == nil {
			result["area_name"] = area.Name
		}
	}

	// 返回响应
	httpx.OkJson(w, &BaseResponse{
		Code: 0,
		Msg:  "success",
		Data: result,
	})
}
