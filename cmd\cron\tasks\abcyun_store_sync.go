package tasks

import (
	"fmt"

	"yekaitai/internal/modules/admin/model"
	storeModel "yekaitai/internal/modules/store/model"
	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// Store 门店信息结构体
type Store struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	Phone        string `json:"phone"`
	Address      string `json:"address"`
	ProvinceName string `json:"provinceName"`
	CityName     string `json:"cityName"`
	DistrictName string `json:"districtName"`
	ParentID     string `json:"parentId"`
}

// StoreListResponse 门店列表响应
type StoreListResponse struct {
	Stores []Store `json:"stores"`
}

// AbcYunStoreSyncService ABC云门店同步服务
type AbcYunStoreSyncService struct {
	client     *abcyun.AbcYunClient
	db         *gorm.DB
	storeRepo  storeModel.StoreRepository
	regionRepo model.RegionRepository
}

// NewAbcYunStoreSyncService 创建ABC云门店同步服务
func NewAbcYunStoreSyncService(client *abcyun.AbcYunClient) *AbcYunStoreSyncService {
	return &AbcYunStoreSyncService{
		client:     client,
		db:         mysql.Master(),
		storeRepo:  storeModel.NewStoreRepository(mysql.Master()),
		regionRepo: model.NewRegionRepository(mysql.Master()),
	}
}

// SyncStores 同步门店信息
func (s *AbcYunStoreSyncService) SyncStores() error {
	logx.Info("开始同步ABC云门店信息")

	// 调用获取门店信息接口
	stores, err := s.getStoreList()
	if err != nil {
		return fmt.Errorf("获取门店列表失败: %w", err)
	}

	var syncCount, createCount, updateCount int

	// 遍历门店列表
	for _, store := range stores {
		// 根据省市区名称查询地区编码
		provinceCode, cityCode, areaCode, err := s.getRegionCodes(store.ProvinceName, store.CityName, store.DistrictName)
		if err != nil {
			logx.Errorf("查询地区编码失败: %v", err)
			// 继续处理其他门店
		}

		// 拼接完整地址：省市区名称 + 详细地址
		fullAddress := store.Address
		if store.ProvinceName != "" || store.CityName != "" || store.DistrictName != "" {
			addressPrefix := ""
			if store.ProvinceName != "" {
				addressPrefix += store.ProvinceName
			}
			if store.CityName != "" {
				addressPrefix += store.CityName
			}
			if store.DistrictName != "" {
				addressPrefix += store.DistrictName
			}
			fullAddress = addressPrefix + store.Address
		}

		// 检查门店是否已存在（通过wsjg_id字段）
		var existingStore storeModel.Store
		err = s.db.Where("wsjg_id = ?", store.ID).First(&existingStore).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			logx.Errorf("查询门店失败: %v", err)
			continue
		}

		storeData := &storeModel.Store{
			Name:       store.Name,
			Phone:      store.Phone,
			ProvinceID: provinceCode,
			CityID:     cityCode,
			AreaID:     areaCode,
			Address:    fullAddress, // 使用拼接后的完整地址
			WsjgID:     store.ID,
			ParentID:   store.ParentID,
			Status:     1, // 正常状态
			CreatorID:  1, // 系统创建
		}

		if err == nil { // 门店已存在
			// 更新现有门店，保留原有的创建时间
			storeData.ID = existingStore.ID
			storeData.CreatedAt = existingStore.CreatedAt // 保留原有创建时间
			err = s.storeRepo.Update(storeData)
			if err != nil {
				logx.Errorf("更新门店失败: %v", err)
				continue
			}
			updateCount++
			logx.Infof("更新门店成功: %s (ID: %d)", storeData.Name, storeData.ID)
		} else {
			// 创建新门店
			err = s.storeRepo.Create(storeData)
			if err != nil {
				logx.Errorf("创建门店失败: %v", err)
				continue
			}
			createCount++
			logx.Infof("创建门店成功: %s (ID: %d)", storeData.Name, storeData.ID)
		}
		syncCount++
	}

	logx.Infof("ABC云门店信息同步完成，总计同步: %d, 新建: %d, 更新: %d",
		syncCount, createCount, updateCount)
	return nil
}

// getStoreList 获取门店列表
func (s *AbcYunStoreSyncService) getStoreList() ([]Store, error) {
	// 使用已有的API接口获取诊所信息
	clinicInfo, err := s.client.GetClinicInfo()
	if err != nil {
		return nil, fmt.Errorf("调用获取诊所信息接口失败: %w", err)
	}

	// 转换为Store结构体
	store := Store{
		ID:           clinicInfo.ID,
		Name:         clinicInfo.Name,
		Phone:        clinicInfo.Phone,
		Address:      clinicInfo.AddressDetail,
		ProvinceName: clinicInfo.ProvinceName,
		CityName:     clinicInfo.CityName,
		DistrictName: clinicInfo.DistrictName,
		ParentID:     clinicInfo.ParentID,
	}

	return []Store{store}, nil
}

// getRegionCodes 根据省市区名称查询地区编码
func (s *AbcYunStoreSyncService) getRegionCodes(provinceName, cityName, districtName string) (string, string, string, error) {
	var provinceCode, cityCode, areaCode string

	// 查询省份编码
	if provinceName != "" {
		provinces, err := s.regionRepo.GetProvinces()
		if err == nil {
			for _, province := range provinces {
				if province.Name == provinceName {
					provinceCode = province.Code
					break
				}
			}
		}
	}

	// 查询城市编码
	if cityName != "" && provinceCode != "" {
		cities, err := s.regionRepo.GetCities(provinceCode)
		if err == nil {
			for _, city := range cities {
				if city.Name == cityName {
					cityCode = city.Code
					break
				}
			}
		}
	}

	// 查询区县编码
	if districtName != "" && cityCode != "" {
		areas, err := s.regionRepo.GetAreas(cityCode)
		if err == nil {
			for _, area := range areas {
				if area.Name == districtName {
					areaCode = area.Code
					break
				}
			}
		}
	}

	return provinceCode, cityCode, areaCode, nil
}
