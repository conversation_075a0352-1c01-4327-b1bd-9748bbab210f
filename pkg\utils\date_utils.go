package utils

import (
	"regexp"
	"time"
)

// TruncateToDay 将时间截断到日期（零点）
func TruncateToDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// IsValidTimeFormat 检查时间格式是否为HH:MM
func IsValidTimeFormat(timeStr string) bool {
	r := regexp.MustCompile(`^([01]?[0-9]|2[0-3]):[0-5][0-9]$`)
	return r.MatchString(timeStr)
}

// ParseDate 解析日期字符串为time.Time
func ParseDate(dateStr string) (time.Time, error) {
	return time.Parse("2006-01-02", dateStr)
}

// FormatDate 格式化时间为日期字符串
func FormatDate(t time.Time) string {
	return t.Format("2006-01-02")
}
