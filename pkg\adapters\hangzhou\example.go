// 此文件仅作为示例，展示如何在应用中使用杭州HIS适配器
package hangzhou

import (
	"context"
	"fmt"
	"time"
)

// ExampleUsage 展示如何使用杭州HIS适配器
func ExampleUsage() {
	// 方法1: 创建服务实例（从配置文件加载）
	service, err := NewService("etc/yekaitai-dev.yaml")
	if err != nil {
		fmt.Printf("创建HIS服务失败: %v\n", err)
		return
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 示例1: 获取卫生机构信息
	orgID := 5000 // 假设机构ID为5000
	orgResp, err := service.GetHealthOrganization(ctx, orgID)
	if err != nil {
		fmt.Printf("获取卫生机构信息失败: %v\n", err)
		return
	}
	fmt.Printf("卫生机构信息: %+v\n", orgResp)

	// 示例2: 获取机构科室列表
	deptResp, err := service.GetDepartments(ctx, orgID, 1) // 未作废科室
	if err != nil {
		fmt.Printf("获取科室列表失败: %v\n", err)
		return
	}
	fmt.Printf("科室列表: %+v\n", deptResp)

	// 示例3: 获取用户列表
	userResp, err := service.GetUsers(ctx, "1", "0") // 启用且未作废的用户
	if err != nil {
		fmt.Printf("获取用户列表失败: %v\n", err)
		return
	}
	fmt.Printf("用户列表: %+v\n", userResp)

	// 方法2: 使用全局默认客户端（适用于应用启动时已初始化的情况）
	/*
		// 应用启动时在main.go或初始化阶段调用
		var config struct {
			HangzhouHIS hangzhou.Config
		}
		// 从配置文件加载配置
		if err := conf.Load("etc/config.yaml", &config); err != nil {
			panic(err)
		}
		// 初始化全局客户端
		hangzhou.Init(config.HangzhouHIS)

		// 然后在应用的任何地方可以直接使用全局方法
		resp, err := hangzhou.GetHealthOrganization(ctx, 5000)
	*/
}

// 在wx_internal或internal模块中的用法示例
/*
package handler

import (
	"context"
	"net/http"

	"yekaitai/pkg/hangzhou"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// HisHandler HIS系统相关处理器
type HisHandler struct {
	hisService *hangzhou.Service
}

// NewHisHandler 创建HIS处理器
func NewHisHandler(configFile string) *HisHandler {
	service, err := hangzhou.NewService(configFile)
	if err != nil {
		panic(err)
	}

	return &HisHandler{
		hisService: service,
	}
}

// GetHealthOrganization 获取卫生机构信息
func (h *HisHandler) GetHealthOrganization(w http.ResponseWriter, r *http.Request) {
	// 从请求中获取机构ID
	var req struct {
		OrgID int `path:"orgId"`
	}
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 调用HIS服务
	resp, err := h.hisService.GetHealthOrganization(r.Context(), req.OrgID)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取卫生机构信息失败"))
		return
	}

	// 返回结果
	httpx.OkJson(w, types.NewSuccessResponse(resp.Data, "获取卫生机构信息成功"))
}
*/
