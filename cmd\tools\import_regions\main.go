package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	adminModel "yekaitai/internal/modules/admin/model"

	_ "github.com/go-sql-driver/mysql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	_ "modernc.org/sqlite"
)

// go run main.go --keep=true > 123
// --keep=true：保留表中现有数据，通过code字段匹配进行更新
func main() {
	// 解析命令行参数
	mysqlDSN := flag.String("mysql", "root:123456@tcp(127.0.0.1:3306)/yekaitai?charset=utf8mb4&parseTime=True&loc=Local", "MySQL连接字符串")
	sqlitePath := flag.String("sqlite", "../../../data/data.sqlite", "SQLite数据库文件路径")
	keepData := flag.Bool("keep", false, "清空表并重新导入（默认行为）")
	flag.Parse()

	// 打印启动信息
	fmt.Println("五级行政区划数据导入工具启动中...")
	fmt.Printf("MySQL DSN: %s\n", *mysqlDSN)
	fmt.Printf("SQLite路径: %s\n", *sqlitePath)
	fmt.Printf("保留现有数据: %v\n", *keepData)

	// 检查SQLite文件是否存在
	if _, err := os.Stat(*sqlitePath); os.IsNotExist(err) {
		log.Fatalf("SQLite文件不存在: %s", *sqlitePath)
	}

	// 连接MySQL数据库
	fmt.Println("连接MySQL数据库...")
	db, err := gorm.Open(mysql.Open(*mysqlDSN), &gorm.Config{})
	if err != nil {
		log.Fatalf("连接MySQL数据库失败: %v", err)
	}

	// 自动迁移表结构
	fmt.Println("创建/更新表结构...")
	err = db.AutoMigrate(&adminModel.Region{})
	if err != nil {
		log.Fatalf("自动迁移表结构失败: %v", err)
	}

	// 连接SQLite数据库
	fmt.Printf("连接SQLite数据库(%s)...\n", *sqlitePath)
	sqliteDB, err := sql.Open("sqlite", *sqlitePath)
	if err != nil {
		log.Fatalf("连接SQLite数据库失败: %v", err)
	}
	defer sqliteDB.Close()

	// 打印SQLite数据库中的所有表
	fmt.Println("SQLite数据库中的所有表:")
	tables, err := getAllTables(sqliteDB)
	if err != nil {
		log.Printf("获取表列表失败: %v", err)
	} else {
		for i, table := range tables {
			fmt.Printf("%d. %s\n", i+1, table)
		}
		fmt.Printf("总共 %d 个表\n", len(tables))
	}

	// 验证SQLite数据库表结构
	fmt.Println("验证SQLite数据库表结构...")
	requiredTables := []string{"province", "city", "area", "street", "village"}
	missingTables := []string{}

	for _, table := range requiredTables {
		if !checkTableExists(sqliteDB, table) {
			missingTables = append(missingTables, table)
		} else {
			// 打印表结构
			columns := getTableColumns(sqliteDB, table)
			fmt.Printf("表 %s 的字段: %v\n", table, columns)
		}
	}

	if len(missingTables) > 0 {
		log.Fatalf("SQLite数据库缺少以下表: %s", strings.Join(missingTables, ", "))
	}

	// 开始事务
	fmt.Println("开始事务...")
	tx := db.Begin()
	if tx.Error != nil {
		log.Fatalf("开始事务失败: %v", tx.Error)
	}

	// 根据参数决定是否清空现有数据
	if !*keepData {
		fmt.Println("清空现有数据...")
		if err := tx.Exec("TRUNCATE TABLE " + (&adminModel.Region{}).TableName()).Error; err != nil {
			tx.Rollback()
			log.Fatalf("清空表数据失败: %v", err)
		}
	} else {
		fmt.Println("保留现有数据，将执行更新/覆盖操作...")
	}

	// 导入省份数据
	fmt.Println("导入省份数据...")
	provinces, err := queryRegions(sqliteDB, 1, "")
	if err != nil {
		tx.Rollback()
		log.Fatalf("查询省份数据失败: %v", err)
	}

	if *keepData {
		// 使用覆盖模式导入
		if err := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "code"}},
			UpdateAll: true,
		}).CreateInBatches(provinces, 100).Error; err != nil {
			tx.Rollback()
			log.Fatalf("更新省份数据失败: %v", err)
		}
	} else {
		// 直接批量创建
		if err := tx.CreateInBatches(provinces, 100).Error; err != nil {
			tx.Rollback()
			log.Fatalf("保存省份数据失败: %v", err)
		}
	}
	fmt.Printf("成功导入 %d 个省份\n", len(provinces))

	// 导入市级数据
	fmt.Println("导入市级数据...")
	var cities []*adminModel.Region
	var provinceCodes []string
	for _, province := range provinces {
		provinceCodes = append(provinceCodes, province.Code)
	}

	cities, err = queryRegionsBatch(sqliteDB, 2, provinceCodes)
	if err != nil {
		tx.Rollback()
		log.Fatalf("批量查询城市数据失败: %v", err)
	}

	if len(cities) > 0 {
		if *keepData {
			// 使用覆盖模式导入
			if err := tx.Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "code"}},
				UpdateAll: true,
			}).CreateInBatches(cities, 500).Error; err != nil {
				tx.Rollback()
				log.Fatalf("更新城市数据失败: %v", err)
			}
		} else {
			// 直接批量创建
			if err := tx.CreateInBatches(cities, 500).Error; err != nil {
				tx.Rollback()
				log.Fatalf("保存城市数据失败: %v", err)
			}
		}
		fmt.Printf("成功导入 %d 个城市\n", len(cities))
	} else {
		fmt.Println("未找到市级数据")
	}

	// 导入区县级数据
	fmt.Println("导入区县级数据...")
	var areas []*adminModel.Region
	var cityCodes []string
	for _, city := range cities {
		cityCodes = append(cityCodes, city.Code)
	}

	areas, err = queryRegionsBatch(sqliteDB, 3, cityCodes)
	if err != nil {
		tx.Rollback()
		log.Fatalf("批量查询区县数据失败: %v", err)
	}

	if len(areas) > 0 {
		if *keepData {
			// 使用覆盖模式导入
			if err := tx.Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "code"}},
				UpdateAll: true,
			}).CreateInBatches(areas, 1000).Error; err != nil {
				tx.Rollback()
				log.Fatalf("更新区县数据失败: %v", err)
			}
		} else {
			// 直接批量创建
			if err := tx.CreateInBatches(areas, 1000).Error; err != nil {
				tx.Rollback()
				log.Fatalf("保存区县数据失败: %v", err)
			}
		}
		fmt.Printf("成功导入 %d 个区县\n", len(areas))
	} else {
		fmt.Println("未找到区县级数据")
	}

	// 导入乡镇级数据
	fmt.Println("导入乡镇级数据...")
	var towns []*adminModel.Region
	var areaCodes []string
	for _, area := range areas {
		areaCodes = append(areaCodes, area.Code)
	}

	towns, err = queryRegionsBatch(sqliteDB, 4, areaCodes)
	if err != nil {
		tx.Rollback()
		log.Fatalf("批量查询乡镇数据失败: %v", err)
	}

	if len(towns) > 0 {
		if *keepData {
			// 使用覆盖模式导入
			if err := tx.Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "code"}},
				UpdateAll: true,
			}).CreateInBatches(towns, 2000).Error; err != nil {
				tx.Rollback()
				log.Fatalf("更新乡镇数据失败: %v", err)
			}
		} else {
			// 直接批量创建
			if err := tx.CreateInBatches(towns, 2000).Error; err != nil {
				tx.Rollback()
				log.Fatalf("保存乡镇数据失败: %v", err)
			}
		}
		fmt.Printf("成功导入 %d 个乡镇\n", len(towns))
	} else {
		fmt.Println("未找到乡镇级数据")
	}

	// 导入村街道级数据
	fmt.Println("导入村街道级数据...")
	var townCodes []string
	for _, town := range towns {
		townCodes = append(townCodes, town.Code)
	}

	// 对于大量村级数据，分批次处理
	chunkSize := 1000
	totalVillages := 0

	for i := 0; i < len(townCodes); i += chunkSize {
		end := i + chunkSize
		if end > len(townCodes) {
			end = len(townCodes)
		}

		fmt.Printf("处理村级数据批次 %d/%d...\n", i/chunkSize+1, (len(townCodes)+chunkSize-1)/chunkSize)
		chunkCodes := townCodes[i:end]

		villageChunk, err := queryRegionsBatch(sqliteDB, 5, chunkCodes)
		if err != nil {
			tx.Rollback()
			log.Fatalf("批量查询村街道数据失败: %v", err)
		}

		if len(villageChunk) > 0 {
			if *keepData {
				// 使用覆盖模式导入
				if err := tx.Clauses(clause.OnConflict{
					Columns:   []clause.Column{{Name: "code"}},
					UpdateAll: true,
				}).CreateInBatches(villageChunk, 5000).Error; err != nil {
					tx.Rollback()
					log.Fatalf("更新村街道数据失败: %v", err)
				}
			} else {
				// 直接批量创建
				if err := tx.CreateInBatches(villageChunk, 5000).Error; err != nil {
					tx.Rollback()
					log.Fatalf("保存村街道数据失败: %v", err)
				}
			}
			totalVillages += len(villageChunk)
			fmt.Printf("当前批次成功导入 %d 个村街道，累计 %d 个\n", len(villageChunk), totalVillages)
		}
	}

	if totalVillages > 0 {
		fmt.Printf("总共成功导入 %d 个村街道\n", totalVillages)
	} else {
		fmt.Println("未找到村街道级数据")
	}

	// 提交事务
	fmt.Println("提交事务...")
	if err := tx.Commit().Error; err != nil {
		log.Fatalf("提交事务失败: %v", err)
	}

	// 打印统计信息
	total := len(provinces) + len(cities) + len(areas) + len(towns) + totalVillages
	fmt.Printf("导入完成! 共导入 %d 条记录 (省份: %d, 城市: %d, 区县: %d, 乡镇: %d, 村街道: %d)\n",
		total, len(provinces), len(cities), len(areas), len(towns), totalVillages)
}

// 检查表是否存在
func checkTableExists(db *sql.DB, tableName string) bool {
	query := fmt.Sprintf("SELECT name FROM main.sqlite_master WHERE type='table' AND name='%s'", tableName)
	rows, err := db.Query(query)
	if err != nil {
		log.Printf("检查表%s是否存在失败: %v", tableName, err)
		return false
	}
	defer rows.Close()

	return rows.Next()
}

// 查询表字段
func getTableColumns(db *sql.DB, tableName string) []string {
	query := fmt.Sprintf("PRAGMA table_info(%s)", tableName)
	rows, err := db.Query(query)
	if err != nil {
		log.Printf("查询表%s结构失败: %v", tableName, err)
		return nil
	}
	defer rows.Close()

	var columns []string
	for rows.Next() {
		var cid int
		var name, typeName string
		var notnull, dfltValue, pk interface{}
		if err := rows.Scan(&cid, &name, &typeName, &notnull, &dfltValue, &pk); err != nil {
			log.Printf("扫描表结构失败: %v", err)
			return nil
		}
		columns = append(columns, name)
	}

	return columns
}

// 获取所有表
func getAllTables(db *sql.DB) ([]string, error) {
	query := "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return nil, err
		}
		tables = append(tables, name)
	}

	return tables, nil
}

// 从SQLite查询区域数据
func queryRegions(db *sql.DB, level int, parentCode string) ([]*adminModel.Region, error) {
	var table, parentField string

	// 根据不同级别选择不同的表和父级字段
	switch level {
	case 1: // 省级
		table = "province"
		parentField = ""
	case 2: // 市级
		table = "city"
		parentField = "provinceCode"
	case 3: // 县级
		table = "area"
		parentField = "cityCode"
	case 4: // 乡级
		table = "street"
		parentField = "areaCode"
	case 5: // 村级
		table = "village"
		parentField = "streetCode"
	default:
		return nil, fmt.Errorf("不支持的级别: %d", level)
	}

	// 检查表结构
	columns := getTableColumns(db, strings.Replace(table, "main.", "", 1))
	if len(columns) == 0 {
		return nil, fmt.Errorf("无法获取%s表的结构", table)
	}

	// 确定查询字段（code/id）
	codeField := "code"
	if !contains(columns, "code") && contains(columns, "id") {
		codeField = "id"
	}

	// 确定名称字段
	nameField := "name"
	if !contains(columns, "name") && contains(columns, "title") {
		nameField = "title"
	}

	// 构建查询
	var query string
	var args []interface{}

	if level == 1 {
		// 省级查询
		query = fmt.Sprintf("SELECT %s, %s FROM %s ORDER BY %s", codeField, nameField, table, codeField)
		args = []interface{}{}
	} else {
		// 下级查询，附带父级条件
		query = fmt.Sprintf("SELECT %s, %s FROM %s WHERE %s = ? ORDER BY %s",
			codeField, nameField, table, parentField, codeField)
		args = []interface{}{parentCode}
	}

	fmt.Printf("执行查询: %s, 参数: %v\n", query, args)
	rows, err := db.Query(query, args...)
	if err != nil {
		fmt.Printf("查询失败: %v\n", err)
		return nil, err
	}
	defer rows.Close()

	var regions []*adminModel.Region
	for rows.Next() {
		var code, name string
		if err := rows.Scan(&code, &name); err != nil {
			fmt.Printf("扫描行失败: %v\n", err)
			return nil, err
		}

		// 去除名称中的空格和特殊字符
		name = strings.TrimSpace(name)

		regions = append(regions, &adminModel.Region{
			Code:       code,
			Name:       name,
			ParentCode: parentCode,
			Level:      level,
		})
	}

	if err := rows.Err(); err != nil {
		fmt.Printf("查询结果错误: %v\n", err)
		return nil, err
	}

	fmt.Printf("查询到 %d 条记录\n", len(regions))
	return regions, nil
}

// 判断字符串是否在字符串数组中
func contains(arr []string, str string) bool {
	for _, a := range arr {
		if a == str {
			return true
		}
	}
	return false
}

// 批量查询区域数据
func queryRegionsBatch(db *sql.DB, level int, parentCodes []string) ([]*adminModel.Region, error) {
	if len(parentCodes) == 0 {
		return []*adminModel.Region{}, nil
	}

	var table, parentField string

	// 根据不同级别选择不同的表和父级字段
	switch level {
	case 1: // 省级
		table = "province"
		parentField = ""
	case 2: // 市级
		table = "city"
		parentField = "provinceCode"
	case 3: // 县级
		table = "area"
		parentField = "cityCode"
	case 4: // 乡级
		table = "street"
		parentField = "areaCode"
	case 5: // 村级
		table = "village"
		parentField = "streetCode"
	default:
		return nil, fmt.Errorf("不支持的级别: %d", level)
	}

	// 检查表结构
	columns := getTableColumns(db, strings.Replace(table, "main.", "", 1))
	if len(columns) == 0 {
		return nil, fmt.Errorf("无法获取%s表的结构", table)
	}

	// 确定查询字段（code/id）
	codeField := "code"
	if !contains(columns, "code") && contains(columns, "id") {
		codeField = "id"
	}

	// 确定名称字段
	nameField := "name"
	if !contains(columns, "name") && contains(columns, "title") {
		nameField = "title"
	}

	// 构建查询参数占位符
	placeholders := make([]string, len(parentCodes))
	args := make([]interface{}, len(parentCodes))
	for i, code := range parentCodes {
		placeholders[i] = "?"
		args[i] = code
	}

	// 构建查询
	var query string

	if level == 1 {
		// 省级查询
		query = fmt.Sprintf("SELECT %s, %s FROM %s ORDER BY %s", codeField, nameField, table, codeField)
	} else {
		// 下级批量查询，附带父级条件
		query = fmt.Sprintf("SELECT %s, %s, %s FROM %s WHERE %s IN (%s) ORDER BY %s",
			codeField, nameField, parentField, table, parentField, strings.Join(placeholders, ","), codeField)
	}

	fmt.Printf("执行批量查询: %s\n", query)
	rows, err := db.Query(query, args...)
	if err != nil {
		fmt.Printf("查询失败: %v\n", err)
		return nil, err
	}
	defer rows.Close()

	var regions []*adminModel.Region
	for rows.Next() {
		var code, name string
		var parentCode string

		var scanErr error
		if level == 1 {
			scanErr = rows.Scan(&code, &name)
			parentCode = ""
		} else {
			scanErr = rows.Scan(&code, &name, &parentCode)
		}

		if scanErr != nil {
			fmt.Printf("扫描行失败: %v\n", scanErr)
			return nil, scanErr
		}

		// 去除名称中的空格和特殊字符
		name = strings.TrimSpace(name)

		regions = append(regions, &adminModel.Region{
			Code:       code,
			Name:       name,
			ParentCode: parentCode,
			Level:      level,
		})
	}

	if err := rows.Err(); err != nil {
		fmt.Printf("查询结果错误: %v\n", err)
		return nil, err
	}

	fmt.Printf("查询到 %d 条记录\n", len(regions))
	return regions, nil
}
