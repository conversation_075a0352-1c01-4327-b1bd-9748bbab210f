package handler

import (
	"net/http"

	"yekaitai/internal/modules/base_setup/model"
	"yekaitai/internal/modules/base_setup/service"
	"yekaitai/internal/types"
	"yekaitai/pkg/upload"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type StoreConfigGoZeroHandler struct {
	storeConfigService *service.StoreConfigService
}

func NewStoreConfigGoZeroHandler() *StoreConfigGoZeroHandler {
	return &StoreConfigGoZeroHandler{
		storeConfigService: service.NewStoreConfigService(),
	}
}

// CreateStoreConfig 创建店铺配置
func (h *StoreConfigGoZeroHandler) CreateStoreConfig(w http.ResponseWriter, r *http.Request) {
	var req model.StoreConfigCreateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	config, err := h.storeConfigService.CreateStoreConfig(r.Context(), &req)
	if err != nil {
		logx.Errorf("创建店铺配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(config))
}

// UpdateStoreConfig 更新店铺配置
func (h *StoreConfigGoZeroHandler) UpdateStoreConfig(w http.ResponseWriter, r *http.Request) {
	var req model.StoreConfigUpdateRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	config, err := h.storeConfigService.UpdateStoreConfig(r.Context(), &req)
	if err != nil {
		logx.Errorf("更新店铺配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(config))
}

// GetStoreConfig 获取店铺配置
func (h *StoreConfigGoZeroHandler) GetStoreConfig(w http.ResponseWriter, r *http.Request) {
	config, err := h.storeConfigService.GetStoreConfigResponse(r.Context())
	if err != nil {
		logx.Errorf("获取店铺配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(config))
}

// DeleteStoreConfig 删除店铺配置
func (h *StoreConfigGoZeroHandler) DeleteStoreConfig(w http.ResponseWriter, r *http.Request) {
	err := h.storeConfigService.DeleteStoreConfig(r.Context())
	if err != nil {
		logx.Errorf("删除店铺配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// IsStoreConfigured 检查店铺是否已配置
func (h *StoreConfigGoZeroHandler) IsStoreConfigured(w http.ResponseWriter, r *http.Request) {
	configured, err := h.storeConfigService.IsStoreConfigured(r.Context())
	if err != nil {
		logx.Errorf("检查店铺配置状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	result := map[string]interface{}{
		"configured": configured,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// UploadStoreLicense 上传店铺证件
func (h *StoreConfigGoZeroHandler) UploadStoreLicense(w http.ResponseWriter, r *http.Request) {
	// 确保上传服务已初始化
	if upload.DefaultService == nil {
		upload.InitDefaultService()
	}

	// 获取上传的文件
	file, header, err := r.FormFile("file")
	if err != nil {
		logx.Errorf("获取上传文件失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无法获取上传文件"))
		return
	}
	defer file.Close()

	// 获取证件类型
	licenseType := r.FormValue("type")
	if licenseType == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请指定证件类型"))
		return
	}

	// 设置上传路径
	uploadPath := "store/licenses/" + licenseType

	// 上传图片
	result, err := upload.DefaultService.UploadImage(r.Context(), header, uploadPath)
	if err != nil {
		logx.Errorf("上传证件图片失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "上传失败: "+err.Error()))
		return
	}

	logx.Infof("店铺证件上传成功: %s -> %s", header.Filename, result.URL)
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// GetStoreConfigForWx 小程序端获取店铺配置
func (h *StoreConfigGoZeroHandler) GetStoreConfigForWx(w http.ResponseWriter, r *http.Request) {
	config, err := h.storeConfigService.GetStoreConfigResponse(r.Context())
	if err != nil {
		logx.Errorf("获取店铺配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 小程序端返回包含证件信息的完整配置
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(config))
}

// GetStoreLicenses 小程序端获取店铺证件信息
func (h *StoreConfigGoZeroHandler) GetStoreLicenses(w http.ResponseWriter, r *http.Request) {
	config, err := h.storeConfigService.GetStoreConfigResponse(r.Context())
	if err != nil {
		logx.Errorf("获取店铺配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 只返回证件相关信息
	certificates := map[string]interface{}{
		"business_license":       config.BusinessLicense,
		"medical_license":        config.MedicalLicense,
		"internet_license":       config.InternetLicense,
		"second_class_license":   config.SecondClassLicense,
		"third_class_license":    config.ThirdClassLicense,
		"internet_sales_license": config.InternetSalesLicense,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(certificates))
}

// CheckStoreStatus 检查店铺状态
func (h *StoreConfigGoZeroHandler) CheckStoreStatus(w http.ResponseWriter, r *http.Request) {
	config, err := h.storeConfigService.GetStoreConfigResponse(r.Context())
	if err != nil {
		logx.Errorf("获取店铺配置失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	status := map[string]interface{}{
		"status":     config.Status,
		"is_open":    config.Status == 1, // 1表示开启
		"store_name": config.StoreName,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(status))
}
