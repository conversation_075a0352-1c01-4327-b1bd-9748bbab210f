package wechatpay

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/zeromicro/go-zero/core/logx"
)

// PaymentService 微信支付服务
type PaymentService struct {
	client *core.Client
	config *WechatPayConfig
}

// NewPaymentService 创建支付服务
func NewPaymentService(client *core.Client, config *WechatPayConfig) *PaymentService {
	return &PaymentService{
		client: client,
		config: config,
	}
}

// CreateJSAPIPaymentRequest JSAPI支付请求
type CreateJSAPIPaymentRequest struct {
	OrderNo     string    `json:"out_trade_no"`
	Description string    `json:"description"`
	Amount      int64     `json:"amount"` // 金额，单位：分
	OpenID      string    `json:"openid"`
	Attach      string    `json:"attach,omitempty"` // 附加数据
	TimeExpire  time.Time `json:"time_expire"`      // 订单失效时间
}

// CreateJSAPIPaymentResponse JSAPI支付响应
type CreateJSAPIPaymentResponse struct {
	PrepayID      string            `json:"prepay_id"`
	PaymentParams map[string]string `json:"payment_params"` // 小程序调起支付所需参数
}

// CreateJSAPIPayment 创建JSAPI支付
func (s *PaymentService) CreateJSAPIPayment(ctx context.Context, req *CreateJSAPIPaymentRequest) (*CreateJSAPIPaymentResponse, error) {
	// 创建JSAPI支付API服务
	svc := jsapi.JsapiApiService{Client: s.client}

	// 构建支付请求
	prepayReq := jsapi.PrepayRequest{
		Appid:       core.String(s.config.AppID),
		Mchid:       core.String(s.config.MchID),
		Description: core.String(req.Description),
		OutTradeNo:  core.String(req.OrderNo),
		TimeExpire:  core.Time(req.TimeExpire),
		Attach:      core.String(req.Attach),
		NotifyUrl:   core.String(s.config.NotifyURL), // 从配置获取回调URL
		Amount: &jsapi.Amount{
			Total: core.Int64(req.Amount),
		},
		Payer: &jsapi.Payer{
			Openid: core.String(req.OpenID),
		},
	}

	// 网络连接测试
	logx.Infof("===== 微信支付网络连接测试 =====")
	if err := s.testNetworkConnection(); err != nil {
		logx.Errorf("[微信支付] 网络连接测试失败: %v", err)
		return nil, fmt.Errorf("网络连接失败，请检查网络设置: %w", err)
	}
	logx.Infof("[微信支付] 网络连接测试通过")

	// 详细日志：请求参数
	logx.Infof("===== 微信支付创建订单请求 =====")
	logx.Infof("[微信支付] 请求类型: 创建JSAPI支付订单（小程序支付）")
	logx.Infof("[微信支付] 商户订单号: %s", req.OrderNo)
	logx.Infof("[微信支付] 订单描述: %s", req.Description)
	logx.Infof("[微信支付] 订单金额: %d分 (%.2f元)", req.Amount, float64(req.Amount)/100)
	logx.Infof("[微信支付] 买家OpenID: %s", req.OpenID)
	logx.Infof("[微信支付] 商户ID: %s", s.config.MchID)
	logx.Infof("[微信支付] 应用ID: %s", s.config.AppID)
	logx.Infof("[微信支付] 订单失效时间: %s", req.TimeExpire.Format("2006-01-02 15:04:05"))
	logx.Infof("[微信支付] 附加数据: %s", req.Attach)
	logx.Infof("[微信支付] 回调URL: %s", s.config.NotifyURL)

	// 调用微信支付API创建订单并获取调起支付的参数
	resp, result, err := svc.PrepayWithRequestPayment(ctx, prepayReq)

	// 详细日志：响应结果
	logx.Infof("===== 微信支付创建订单响应 =====")
	if err != nil {
		logx.Errorf("[微信支付] 请求失败: %v", err)
		logx.Errorf("[微信支付] 订单号: %s", req.OrderNo)
		return nil, fmt.Errorf("创建微信支付订单失败: %w", err)
	}

	logx.Infof("[微信支付] 请求成功")
	logx.Infof("[微信支付] HTTP状态码: %d", result.Response.StatusCode)
	logx.Infof("[微信支付] 商户订单号: %s", req.OrderNo)
	logx.Infof("[微信支付] 预支付交易会话ID: %s", *resp.PrepayId)
	logx.Infof("[微信支付] 响应头: %+v", result.Response.Header)

	// 构建小程序调起支付的参数
	paymentParams := map[string]string{
		"appId":     *resp.Appid,
		"timeStamp": *resp.TimeStamp,
		"nonceStr":  *resp.NonceStr,
		"package":   *resp.Package,
		"signType":  *resp.SignType,
		"paySign":   *resp.PaySign,
	}

	return &CreateJSAPIPaymentResponse{
		PrepayID:      *resp.PrepayId,
		PaymentParams: paymentParams,
	}, nil
}

// QueryOrderRequest 查询订单请求
type QueryOrderRequest struct {
	OutTradeNo    string `json:"out_trade_no,omitempty"`   // 商户订单号
	TransactionID string `json:"transaction_id,omitempty"` // 微信支付订单号
}

// QueryOrderResponse 查询订单响应
type QueryOrderResponse struct {
	TransactionID  string     `json:"transaction_id"`         // 微信支付订单号
	OutTradeNo     string     `json:"out_trade_no"`           // 商户订单号
	TradeState     string     `json:"trade_state"`            // 交易状态
	TradeStateDesc string     `json:"trade_state_desc"`       // 交易状态描述
	Amount         int64      `json:"amount"`                 // 订单金额，单位分
	PayerOpenID    string     `json:"payer_openid"`           // 支付者openid
	SuccessTime    *time.Time `json:"success_time,omitempty"` // 支付完成时间
}

// QueryOrder 查询订单
func (s *PaymentService) QueryOrder(ctx context.Context, req *QueryOrderRequest) (*QueryOrderResponse, error) {
	svc := jsapi.JsapiApiService{Client: s.client}

	// 详细日志：请求参数
	logx.Infof("===== 微信支付查询订单请求 =====")
	logx.Infof("[微信支付] 请求类型: 查询订单状态")
	logx.Infof("[微信支付] 商户订单号: %s", req.OutTradeNo)
	logx.Infof("[微信支付] 微信支付订单号: %s", req.TransactionID)
	logx.Infof("[微信支付] 商户ID: %s", s.config.MchID)

	var resp *payments.Transaction
	var err error

	// 根据不同参数调用不同的查询接口
	if req.OutTradeNo != "" {
		// 通过商户订单号查询
		queryReq := jsapi.QueryOrderByOutTradeNoRequest{
			OutTradeNo: core.String(req.OutTradeNo),
			Mchid:      core.String(s.config.MchID),
		}
		resp, _, err = svc.QueryOrderByOutTradeNo(ctx, queryReq)
	} else if req.TransactionID != "" {
		// 通过微信支付订单号查询
		queryReq := jsapi.QueryOrderByIdRequest{
			TransactionId: core.String(req.TransactionID),
			Mchid:         core.String(s.config.MchID),
		}
		resp, _, err = svc.QueryOrderById(ctx, queryReq)
	} else {
		return nil, fmt.Errorf("商户订单号和微信支付订单号至少需要提供一个")
	}

	// 详细日志：响应结果
	logx.Infof("===== 微信支付查询订单响应 =====")
	if err != nil {
		logx.Errorf("[微信支付] 查询失败: %v", err)
		logx.Errorf("[微信支付] 商户订单号: %s", req.OutTradeNo)
		logx.Errorf("[微信支付] 微信支付订单号: %s", req.TransactionID)
		return nil, fmt.Errorf("查询微信支付订单失败: %w", err)
	}

	result := &QueryOrderResponse{
		TransactionID:  *resp.TransactionId,
		OutTradeNo:     *resp.OutTradeNo,
		TradeState:     string(*resp.TradeState),
		TradeStateDesc: *resp.TradeStateDesc,
		Amount:         *resp.Amount.Total,
		PayerOpenID:    *resp.Payer.Openid,
	}

	// 如果有支付成功时间，转换时间格式
	if resp.SuccessTime != nil {
		if successTime, err := time.Parse(time.RFC3339, *resp.SuccessTime); err == nil {
			result.SuccessTime = &successTime
		}
	}

	// 详细日志：响应内容
	logx.Infof("[微信支付] 查询成功")
	logx.Infof("[微信支付] 微信支付订单号: %s", result.TransactionID)
	logx.Infof("[微信支付] 商户订单号: %s", result.OutTradeNo)
	logx.Infof("[微信支付] 交易状态: %s", result.TradeState)
	logx.Infof("[微信支付] 状态描述: %s", result.TradeStateDesc)
	logx.Infof("[微信支付] 订单金额: %d分 (%.2f元)", result.Amount, float64(result.Amount)/100)
	logx.Infof("[微信支付] 支付者OpenID: %s", result.PayerOpenID)
	if result.SuccessTime != nil {
		logx.Infof("[微信支付] 支付完成时间: %s", result.SuccessTime.Format("2006-01-02 15:04:05"))
	}

	return result, nil
}

// CloseOrderRequest 关闭订单请求
type CloseOrderRequest struct {
	OutTradeNo string `json:"out_trade_no"` // 商户订单号
}

// CloseOrder 关闭订单
func (s *PaymentService) CloseOrder(ctx context.Context, req *CloseOrderRequest) error {
	svc := jsapi.JsapiApiService{Client: s.client}

	closeReq := jsapi.CloseOrderRequest{
		OutTradeNo: core.String(req.OutTradeNo),
		Mchid:      core.String(s.config.MchID),
	}

	_, err := svc.CloseOrder(ctx, closeReq)
	if err != nil {
		logx.Errorf("关闭微信支付订单失败: %v", err)
		return fmt.Errorf("关闭微信支付订单失败: %w", err)
	}

	logx.Infof("关闭微信支付订单成功: 订单号=%s", req.OutTradeNo)
	return nil
}

// 全局支付服务实例
var globalPaymentService *PaymentService

// InitGlobalPaymentService 初始化全局支付服务
func InitGlobalPaymentService(client *core.Client, config *WechatPayConfig) {
	globalPaymentService = NewPaymentService(client, config)
}

// GetGlobalPaymentService 获取全局支付服务实例
func GetGlobalPaymentService() *PaymentService {
	return globalPaymentService
}

// testNetworkConnection 测试网络连接
func (s *PaymentService) testNetworkConnection() error {
	// 测试DNS解析
	_, err := net.LookupHost("api.mch.weixin.qq.com")
	if err != nil {
		logx.Errorf("[微信支付] DNS解析失败: %v", err)
		return fmt.Errorf("DNS解析失败: %w", err)
	}

	// 测试TCP连接
	conn, err := net.DialTimeout("tcp", "api.mch.weixin.qq.com:443", 10*time.Second)
	if err != nil {
		logx.Errorf("[微信支付] TCP连接失败: %v", err)
		return fmt.Errorf("TCP连接失败: %w", err)
	}
	conn.Close()

	logx.Infof("[微信支付] 网络连接正常")
	return nil
}
