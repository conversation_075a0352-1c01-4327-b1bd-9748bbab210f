package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"
	"unicode/utf8"

	serviceModel "yekaitai/internal/modules/service_setup/model"
	storeModel "yekaitai/internal/modules/store/model"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// CustomTime 自定义时间类型，支持多种格式解析
type CustomTime struct {
	time.Time
}

// UnmarshalJSON 自定义JSON解析
func (ct *CustomTime) UnmarshalJSON(data []byte) error {
	// 处理null值
	if string(data) == "null" {
		return nil
	}

	// 移除JSON字符串的引号
	str := string(data)
	if len(str) >= 2 && str[0] == '"' && str[len(str)-1] == '"' {
		str = str[1 : len(str)-1]
	}

	if str == "" {
		return nil
	}

	// 支持的时间格式
	formats := []string{
		"2006-01-02",                // 日期格式 YYYY-MM-DD (最常用)
		"2006-01-02 15:04",          // 自定义格式 YYYY-MM-DD HH:MM
		"2006-01-02 15:04:05",       // MySQL datetime
		"2006-01-02T15:04:05Z",      // RFC3339 UTC
		"2006-01-02T15:04:05",       // ISO 8601 without timezone
		"2006-01-02T15:04:05Z07:00", // RFC3339
	}

	for _, format := range formats {
		if t, err := time.Parse(format, str); err == nil {
			ct.Time = t
			return nil
		}
	}

	return fmt.Errorf("无法解析时间格式: %s", str)
}

// MarshalJSON 自定义JSON序列化
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	if ct.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte(fmt.Sprintf("\"%s\"", ct.Time.Format("2006-01-02T15:04:05Z"))), nil
}

// ToTimePtr 转换为 *time.Time
func (ct *CustomTime) ToTimePtr() *time.Time {
	if ct == nil {
		return nil
	}
	return &ct.Time
}

// parseTimeString 解析时间字符串
func parseTimeString(timeStr string) (*time.Time, error) {
	if timeStr == "" {
		return nil, nil
	}

	// 支持的时间格式
	formats := []string{
		"2006-01-02",                // 日期格式 YYYY-MM-DD (最常用)
		"2006-01-02 15:04",          // 自定义格式 YYYY-MM-DD HH:MM
		"2006-01-02 15:04:05",       // MySQL datetime
		"2006-01-02T15:04:05Z",      // RFC3339 UTC
		"2006-01-02T15:04:05",       // ISO 8601 without timezone
		"2006-01-02T15:04:05Z07:00", // RFC3339
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return &t, nil
		}
	}

	return nil, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// 套餐列表请求
type PackageListRequest struct {
	types.PageRequest
	TagID     uint   `form:"tagId,optional"`     // 标签ID
	Status    string `form:"status,optional"`    // 状态
	StartDate string `form:"startDate,optional"` // 开始日期
	EndDate   string `form:"endDate,optional"`   // 结束日期
}

// 套餐详情请求
type PackageDetailRequest struct {
	PackageID uint `path:"packageId"` // 套餐ID
}

// 创建套餐请求
type CreatePackageRequest struct {
	Name                 string   `json:"name"`                    // 套餐名称
	Images               []string `json:"images"`                  // 套餐图片
	TagID                uint     `json:"tag_id"`                  // 标签ID
	Sort                 int      `json:"sort"`                    // 推荐排序值
	BodyPart             string   `json:"body_part"`               // 服务部位
	Process              []string `json:"process"`                 // 服务流程
	Duration             int      `json:"duration"`                // 服务时长(分钟)
	Times                int      `json:"times"`                   // 服务次数
	SuitableFor          string   `json:"suitable_for"`            // 适应人群
	Price                float64  `json:"price"`                   // 价格
	PointPrice           int      `json:"point_price"`             // 积分价格
	OriginalPrice        float64  `json:"original_price"`          // 划线价格
	ExtraInfo            string   `json:"extra_info"`              // 补充信息
	IsAllStores          bool     `json:"is_all_stores"`           // 是否适用所有门店
	StoreIDs             []uint   `json:"store_ids"`               // 适用门店ID
	DiscountInfo         string   `json:"discount_info"`           // 优惠同享信息
	GiftInfo             string   `json:"gift_info"`               // 免费赠送信息
	ValidityType         string   `json:"validity_type"`           // 有效期类型
	ValidityStart        string   `json:"validity_start"`          // 有效期开始时间
	ValidityEnd          string   `json:"validity_end"`            // 有效期结束时间
	UnavailableDates     []string `json:"unavailable_dates"`       // 不可用日期
	SaleStartType        string   `json:"sale_start_type"`         // 售卖开始类型
	SaleStartAt          string   `json:"sale_start_at"`           // 售卖开始时间
	SaleEndAt            string   `json:"sale_end_at"`             // 售卖结束时间
	AppointmentRule      string   `json:"appointment_rule"`        // 预约规则
	AdvanceHours         int      `json:"advance_hours"`           // 提前预约小时数
	AppointmentTimeType  string   `json:"appointment_time_type"`   // 预约时间类型
	AppointmentStartTime string   `json:"appointment_start_time"`  // 预约开始时间
	AppointmentEndTime   string   `json:"appointment_end_time"`    // 预约结束时间
	MaxAppointments      int      `json:"max_appointments"`        // 最大预约人数
	MaxPurchases         int      `json:"max_purchases"`           // 每人最大购买数
	SingleMaxPurchases   int      `json:"single_max_purchases"`    // 单次最大购买数
	WarmTips             string   `json:"warm_tips"`               // 温馨提示
	SupportRefundAnyTime bool     `json:"support_refund_any_time"` // 是否支持随时退
	RefundAnyTimeDesc    string   `json:"refund_any_time_desc"`    // 随时退款说明
	SupportAutoRefund    bool     `json:"support_auto_refund"`     // 是否支持过期自动退
	AutoRefundDesc       string   `json:"auto_refund_desc"`        // 过期自动退款说明
	Description          string   `json:"description"`             // 图文详情
	Status               string   `json:"status"`                  // 状态
}

// 更新套餐请求
type UpdatePackageRequest struct {
	PackageID            uint     `path:"packageId"`               // 套餐ID
	Name                 string   `json:"name"`                    // 套餐名称
	Images               []string `json:"images"`                  // 套餐图片
	TagID                uint     `json:"tag_id"`                  // 标签ID
	Sort                 int      `json:"sort"`                    // 推荐排序值
	BodyPart             string   `json:"body_part"`               // 服务部位
	Process              []string `json:"process"`                 // 服务流程
	Duration             int      `json:"duration"`                // 服务时长(分钟)
	Times                int      `json:"times"`                   // 服务次数
	SuitableFor          string   `json:"suitable_for"`            // 适应人群
	Price                float64  `json:"price"`                   // 价格
	PointPrice           int      `json:"point_price"`             // 积分价格
	OriginalPrice        float64  `json:"original_price"`          // 划线价格
	ExtraInfo            string   `json:"extra_info"`              // 补充信息
	IsAllStores          bool     `json:"is_all_stores"`           // 是否适用所有门店
	StoreIDs             []uint   `json:"store_ids"`               // 适用门店ID
	DiscountInfo         string   `json:"discount_info"`           // 优惠同享信息
	GiftInfo             string   `json:"gift_info"`               // 免费赠送信息
	ValidityType         string   `json:"validity_type"`           // 有效期类型
	ValidityStart        string   `json:"validity_start"`          // 有效期开始时间
	ValidityEnd          string   `json:"validity_end"`            // 有效期结束时间
	UnavailableDates     []string `json:"unavailable_dates"`       // 不可用日期
	SaleStartType        string   `json:"sale_start_type"`         // 售卖开始类型
	SaleStartAt          string   `json:"sale_start_at"`           // 售卖开始时间
	SaleEndAt            string   `json:"sale_end_at"`             // 售卖结束时间
	AppointmentRule      string   `json:"appointment_rule"`        // 预约规则
	AdvanceHours         int      `json:"advance_hours"`           // 提前预约小时数
	AppointmentTimeType  string   `json:"appointment_time_type"`   // 预约时间类型
	AppointmentStartTime string   `json:"appointment_start_time"`  // 预约开始时间
	AppointmentEndTime   string   `json:"appointment_end_time"`    // 预约结束时间
	MaxAppointments      int      `json:"max_appointments"`        // 最大预约人数
	MaxPurchases         int      `json:"max_purchases"`           // 每人最大购买数
	SingleMaxPurchases   int      `json:"single_max_purchases"`    // 单次最大购买数
	WarmTips             string   `json:"warm_tips"`               // 温馨提示
	SupportRefundAnyTime bool     `json:"support_refund_any_time"` // 是否支持随时退
	RefundAnyTimeDesc    string   `json:"refund_any_time_desc"`    // 随时退款说明
	SupportAutoRefund    bool     `json:"support_auto_refund"`     // 是否支持过期自动退
	AutoRefundDesc       string   `json:"auto_refund_desc"`        // 过期自动退款说明
	Description          string   `json:"description"`             // 图文详情
	Status               string   `json:"status"`                  // 状态
}

// 更新套餐状态请求
type UpdatePackageStatusRequest struct {
	PackageID uint   `path:"packageId"` // 套餐ID
	Status    string `json:"status"`    // 状态
}

// 删除套餐请求
type DeletePackageRequest struct {
	PackageID uint `path:"packageId"` // 套餐ID
}

// ServicePackageHandler 服务套餐处理器
type ServicePackageHandler struct {
	svcCtx *svc.ServiceContext
}

// NewServicePackageHandler 创建服务套餐处理器
func NewServicePackageHandler(svcCtx *svc.ServiceContext) *ServicePackageHandler {
	return &ServicePackageHandler{
		svcCtx: svcCtx,
	}
}

// ListPackages 获取套餐列表
func (h *ServicePackageHandler) ListPackages(w http.ResponseWriter, r *http.Request) {
	var req PackageListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取套餐列表: page=%d, size=%d, query=%s, tagId=%d, status=%s, startDate=%s, endDate=%s",
		req.Page, req.Size, req.Query, req.TagID, req.Status, req.StartDate, req.EndDate)

	packageRepo := h.svcCtx.ServicePackageRepo
	packages, total, err := packageRepo.List(req.Page, req.Size, req.Query, req.TagID, req.Status, req.StartDate, req.EndDate)
	if err != nil {
		logx.Errorf("获取套餐列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取套餐列表失败"))
		return
	}

	// 处理数据
	result := make([]map[string]interface{}, len(packages))
	for i, pkg := range packages {
		// 获取标签信息
		tagName := ""
		if pkg.TagID > 0 {
			tagRepo := h.svcCtx.ServiceTagRepo
			tag, err := tagRepo.FindByID(pkg.TagID)
			if err == nil && tag != nil {
				tagName = tag.Name
			}
		}

		// 获取套餐绑定的门店
		var storeNames []string
		if pkg.IsAllStores {
			storeNames = append(storeNames, "全部门店")
		} else {
			storeIDs, _ := packageRepo.GetPackageStores(pkg.ID)
			if len(storeIDs) > 0 {
				storeRepo := storeModel.NewStoreRepository(mysql.Slave())
				for _, storeID := range storeIDs {
					store, err := storeRepo.FindByID(storeID)
					if err == nil && store != nil {
						storeNames = append(storeNames, store.Name)
					}
				}
			}
		}

		// 格式化有效期
		validityStr := "永久有效"
		if pkg.ValidityType == "fixed" && pkg.ValidityStart != nil && pkg.ValidityEnd != nil {
			validityStr = pkg.ValidityStart.Format("2006-01-02") + " 至 " + pkg.ValidityEnd.Format("2006-01-02")
		}

		result[i] = map[string]interface{}{
			"id":             pkg.ID,
			"name":           pkg.Name,
			"tag_id":         pkg.TagID,
			"tag_name":       tagName,
			"price":          pkg.Price,
			"point_price":    pkg.PointPrice,
			"original_price": pkg.OriginalPrice,
			"times":          pkg.Times,
			"stores":         storeNames,
			"status":         pkg.Status,
			"validity":       validityStr,
			"sales_count":    pkg.SalesCount,
			"extra_info":     pkg.ExtraInfo,
			"created_at":     pkg.CreatedAt,
			"updated_at":     pkg.UpdatedAt,
		}
	}

	logx.Infof("获取套餐列表成功: 共%d条记录", total)

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取套餐列表成功"))
}

// GetPackage 获取套餐详情
func (h *ServicePackageHandler) GetPackage(w http.ResponseWriter, r *http.Request) {
	var req PackageDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取套餐详情: packageId=%d", req.PackageID)

	packageRepo := h.svcCtx.ServicePackageRepo
	pkg, err := packageRepo.FindByID(req.PackageID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "套餐不存在"))
		} else {
			logx.Errorf("获取套餐详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取套餐详情失败"))
		}
		return
	}

	// 获取标签信息
	tagName := ""
	if pkg.TagID > 0 {
		tagRepo := h.svcCtx.ServiceTagRepo
		tag, err := tagRepo.FindByID(pkg.TagID)
		if err == nil && tag != nil {
			tagName = tag.Name
		}
	}

	// 获取套餐绑定的门店
	storeIDs, _ := packageRepo.GetPackageStores(pkg.ID)
	var stores []map[string]interface{}

	if len(storeIDs) > 0 {
		storeRepo := storeModel.NewStoreRepository(mysql.Slave())
		for _, storeID := range storeIDs {
			store, err := storeRepo.FindByID(storeID)
			if err == nil && store != nil {
				stores = append(stores, map[string]interface{}{
					"id":   store.ID,
					"name": store.Name,
				})
			}
		}
	}

	// 解析JSON字段
	var images []string
	if pkg.Images != "" {
		json.Unmarshal([]byte(pkg.Images), &images)
	}

	var process []string
	if pkg.Process != "" {
		json.Unmarshal([]byte(pkg.Process), &process)
	}

	var unavailableDates []string
	if pkg.UnavailableDates != "" {
		json.Unmarshal([]byte(pkg.UnavailableDates), &unavailableDates)
	}

	// 构建结果
	result := map[string]interface{}{
		"id":                      pkg.ID,
		"name":                    pkg.Name,
		"images":                  images,
		"tag_id":                  pkg.TagID,
		"tag_name":                tagName,
		"sort":                    pkg.Sort,
		"body_part":               pkg.BodyPart,
		"process":                 process,
		"duration":                pkg.Duration,
		"times":                   pkg.Times,
		"suitable_for":            pkg.SuitableFor,
		"price":                   pkg.Price,
		"point_price":             pkg.PointPrice,
		"original_price":          pkg.OriginalPrice,
		"extra_info":              pkg.ExtraInfo,
		"is_all_stores":           pkg.IsAllStores,
		"stores":                  stores,
		"store_ids":               storeIDs,
		"discount_info":           pkg.DiscountInfo,
		"gift_info":               pkg.GiftInfo,
		"validity_type":           pkg.ValidityType,
		"validity_start":          pkg.ValidityStart,
		"validity_end":            pkg.ValidityEnd,
		"unavailable_dates":       unavailableDates,
		"sale_start_type":         pkg.SaleStartType,
		"sale_start_at":           pkg.SaleStartAt,
		"sale_end_at":             pkg.SaleEndAt,
		"appointment_rule":        pkg.AppointmentRule,
		"advance_hours":           pkg.AdvanceHours,
		"appointment_time_type":   pkg.AppointmentTimeType,
		"appointment_start_time":  pkg.AppointmentStartTime,
		"appointment_end_time":    pkg.AppointmentEndTime,
		"max_appointments":        pkg.MaxAppointments,
		"max_purchases":           pkg.MaxPurchases,
		"single_max_purchases":    pkg.SingleMaxPurchases,
		"warm_tips":               pkg.WarmTips,
		"support_refund_any_time": pkg.SupportRefundAnyTime,
		"refund_any_time_desc":    pkg.RefundAnyTimeDesc,
		"support_auto_refund":     pkg.SupportAutoRefund,
		"auto_refund_desc":        pkg.AutoRefundDesc,
		"description":             pkg.Description,
		"status":                  pkg.Status,
		"sales_count":             pkg.SalesCount,
		"created_at":              pkg.CreatedAt,
		"updated_at":              pkg.UpdatedAt,
	}

	httpx.OkJson(w, types.NewSuccessResponse(result, "获取套餐详情成功"))
}

// CreatePackage 创建套餐
func (h *ServicePackageHandler) CreatePackage(w http.ResponseWriter, r *http.Request) {
	var req CreatePackageRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析创建套餐请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("无效的请求参数: %v", err)))
		return
	}

	// 检查必填字段
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐名称不能为空"))
		return
	}

	// 检查名称长度（使用字符数而不是字节数）
	nameLen := utf8.RuneCountInString(req.Name)
	if nameLen < 1 || nameLen > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐名称长度必须在1-10个字符之间"))
		return
	}

	// 检查图片
	if len(req.Images) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐图片不能为空"))
		return
	}

	if len(req.Images) > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐图片不能超过10张"))
		return
	}

	// 检查价格
	if req.Price <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "价格必须大于0"))
		return
	}

	// 检查服务时长和次数
	if req.Duration <= 0 || req.Duration > 1000 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务时长必须在1-1000分钟之间"))
		return
	}

	if req.Times <= 0 || req.Times > 999 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务次数必须在1-999次之间"))
		return
	}

	// 检查字符串字段长度
	if bodyPartLen := utf8.RuneCountInString(req.BodyPart); bodyPartLen > 50 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务部位不能超过50个字符"))
		return
	}

	if suitableForLen := utf8.RuneCountInString(req.SuitableFor); suitableForLen > 100 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "适应人群不能超过100个字符"))
		return
	}

	if extraInfoLen := utf8.RuneCountInString(req.ExtraInfo); extraInfoLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "补充信息不能超过500个字符"))
		return
	}

	if discountInfoLen := utf8.RuneCountInString(req.DiscountInfo); discountInfoLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "优惠同享信息不能超过500个字符"))
		return
	}

	if giftInfoLen := utf8.RuneCountInString(req.GiftInfo); giftInfoLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "免费赠送信息不能超过500个字符"))
		return
	}

	if warmTipsLen := utf8.RuneCountInString(req.WarmTips); warmTipsLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "温馨提示不能超过500个字符"))
		return
	}

	if refundAnyTimeDescLen := utf8.RuneCountInString(req.RefundAnyTimeDesc); refundAnyTimeDescLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "随时退款说明不能超过500个字符"))
		return
	}

	if autoRefundDescLen := utf8.RuneCountInString(req.AutoRefundDesc); autoRefundDescLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "过期自动退款说明不能超过500个字符"))
		return
	}

	// 检查门店配置
	if !req.IsAllStores && len(req.StoreIDs) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请选择门店"))
		return
	}

	// 检查预约规则
	if req.AppointmentRule != "no_need" && req.AppointmentRule != "advance" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的预约规则"))
		return
	}

	// 如果需要提前预约，检查提前小时数
	if req.AppointmentRule == "advance" && req.AdvanceHours <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请设置提前预约小时数"))
		return
	}

	// 检查有效期
	if req.ValidityType != "permanent" && req.ValidityType != "fixed" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的有效期类型"))
		return
	}

	if req.ValidityType == "fixed" {
		if req.ValidityStart == "" || req.ValidityEnd == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "固定有效期必须设置开始和结束时间"))
			return
		}

		// 解析时间进行比较
		startTime, err := parseTimeString(req.ValidityStart)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("有效期开始时间格式错误: %v", err)))
			return
		}
		endTime, err := parseTimeString(req.ValidityEnd)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("有效期结束时间格式错误: %v", err)))
			return
		}

		if endTime.Before(*startTime) {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "有效期结束时间不能早于开始时间"))
			return
		}
	}

	// 检查售卖时间
	if req.SaleStartType != "after_release" && req.SaleStartType != "fixed" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的售卖开始类型"))
		return
	}

	if req.SaleStartType == "fixed" && req.SaleStartAt == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请设置售卖开始时间"))
		return
	}

	if req.SaleEndAt == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请设置售卖结束时间"))
		return
	}

	// 检查流程
	if len(req.Process) > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务流程不能超过10条"))
		return
	}

	packageRepo := h.svcCtx.ServicePackageRepo

	// 检查套餐名称是否已存在
	exists, err := packageRepo.CheckNameExists(req.Name, 0)
	if err != nil {
		logx.Errorf("检查套餐名称失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建套餐失败"))
		return
	}

	if exists {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐名称已存在"))
		return
	}

	// 转换 JSON 字段
	images, _ := json.Marshal(req.Images)
	process, _ := json.Marshal(req.Process)
	unavailableDates, _ := json.Marshal(req.UnavailableDates)

	// 从上下文中获取管理员ID
	adminIDStr, _ := r.Context().Value("admin_id").(string)
	adminID := utils.StringToUint(adminIDStr)

	// 解析时间字段
	validityStart, _ := parseTimeString(req.ValidityStart)
	validityEnd, _ := parseTimeString(req.ValidityEnd)
	saleStartAt, _ := parseTimeString(req.SaleStartAt)
	saleEndAt, _ := parseTimeString(req.SaleEndAt)

	// 创建套餐
	pkg := &serviceModel.ServicePackage{
		Name:                 req.Name,
		Images:               string(images),
		TagID:                req.TagID,
		Sort:                 req.Sort,
		BodyPart:             req.BodyPart,
		Process:              string(process),
		Duration:             req.Duration,
		Times:                req.Times,
		SuitableFor:          req.SuitableFor,
		Price:                req.Price,
		PointPrice:           req.PointPrice,
		OriginalPrice:        req.OriginalPrice,
		ExtraInfo:            req.ExtraInfo,
		IsAllStores:          req.IsAllStores,
		DiscountInfo:         req.DiscountInfo,
		GiftInfo:             req.GiftInfo,
		ValidityType:         req.ValidityType,
		ValidityStart:        validityStart,
		ValidityEnd:          validityEnd,
		UnavailableDates:     string(unavailableDates),
		SaleStartType:        req.SaleStartType,
		SaleStartAt:          saleStartAt,
		SaleEndAt:            saleEndAt,
		AppointmentRule:      req.AppointmentRule,
		AdvanceHours:         req.AdvanceHours,
		AppointmentTimeType:  req.AppointmentTimeType,
		AppointmentStartTime: req.AppointmentStartTime,
		AppointmentEndTime:   req.AppointmentEndTime,
		MaxAppointments:      req.MaxAppointments,
		MaxPurchases:         req.MaxPurchases,
		SingleMaxPurchases:   req.SingleMaxPurchases,
		WarmTips:             req.WarmTips,
		SupportRefundAnyTime: req.SupportRefundAnyTime,
		RefundAnyTimeDesc:    req.RefundAnyTimeDesc,
		SupportAutoRefund:    req.SupportAutoRefund,
		AutoRefundDesc:       req.AutoRefundDesc,
		Description:          req.Description,
		Status:               req.Status,
		CreatedBy:            adminID,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	if err := packageRepo.Create(pkg); err != nil {
		logx.Errorf("创建套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建套餐失败"))
		return
	}

	// 绑定门店
	if !req.IsAllStores && len(req.StoreIDs) > 0 {
		if err := packageRepo.BindStores(pkg.ID, req.StoreIDs, req.IsAllStores); err != nil {
			logx.Errorf("绑定门店失败: %v", err)
			// 不返回错误，继续处理
		}
	}

	// 记录操作日志
	go h.logAdminOperation(r, "套餐管理", "创建", pkg.ID, "ServicePackage", fmt.Sprintf("创建套餐: %s", pkg.Name))

	httpx.OkJson(w, types.NewSuccessResponse(pkg, "创建套餐成功"))
}

// UpdatePackage 更新套餐
func (h *ServicePackageHandler) UpdatePackage(w http.ResponseWriter, r *http.Request) {
	var req UpdatePackageRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析更新套餐请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("无效的请求参数: %v", err)))
		return
	}

	packageRepo := h.svcCtx.ServicePackageRepo

	// 检查套餐是否存在
	existingPkg, err := packageRepo.FindByID(req.PackageID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "套餐不存在"))
		} else {
			logx.Errorf("查询套餐失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新套餐失败"))
		}
		return
	}

	// 检查必填字段
	if req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐名称不能为空"))
		return
	}

	// 检查名称长度（使用字符数而不是字节数）
	nameLen := utf8.RuneCountInString(req.Name)
	if nameLen < 1 || nameLen > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐名称长度必须在1-10个字符之间"))
		return
	}

	// 检查套餐名称是否已被其他套餐使用
	if req.Name != existingPkg.Name {
		exists, err := packageRepo.CheckNameExists(req.Name, req.PackageID)
		if err != nil {
			logx.Errorf("检查套餐名称失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新套餐失败"))
			return
		}
		if exists {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐名称已存在"))
			return
		}
	}

	// 检查图片
	if len(req.Images) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐图片不能为空"))
		return
	}

	if len(req.Images) > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "套餐图片不能超过10张"))
		return
	}

	// 检查价格
	if req.Price <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "价格必须大于0"))
		return
	}

	// 检查服务时长和次数
	if req.Duration <= 0 || req.Duration > 1000 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务时长必须在1-1000分钟之间"))
		return
	}

	if req.Times <= 0 || req.Times > 999 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务次数必须在1-999次之间"))
		return
	}

	// 检查字符串字段长度
	if bodyPartLen := utf8.RuneCountInString(req.BodyPart); bodyPartLen > 50 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务部位不能超过50个字符"))
		return
	}

	if suitableForLen := utf8.RuneCountInString(req.SuitableFor); suitableForLen > 100 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "适应人群不能超过100个字符"))
		return
	}

	if extraInfoLen := utf8.RuneCountInString(req.ExtraInfo); extraInfoLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "补充信息不能超过500个字符"))
		return
	}

	if discountInfoLen := utf8.RuneCountInString(req.DiscountInfo); discountInfoLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "优惠同享信息不能超过500个字符"))
		return
	}

	if giftInfoLen := utf8.RuneCountInString(req.GiftInfo); giftInfoLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "免费赠送信息不能超过500个字符"))
		return
	}

	if warmTipsLen := utf8.RuneCountInString(req.WarmTips); warmTipsLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "温馨提示不能超过500个字符"))
		return
	}

	if refundAnyTimeDescLen := utf8.RuneCountInString(req.RefundAnyTimeDesc); refundAnyTimeDescLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "随时退款说明不能超过500个字符"))
		return
	}

	if autoRefundDescLen := utf8.RuneCountInString(req.AutoRefundDesc); autoRefundDescLen > 500 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "过期自动退款说明不能超过500个字符"))
		return
	}

	// 检查门店配置
	if !req.IsAllStores && len(req.StoreIDs) == 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请选择门店"))
		return
	}

	// 检查预约规则
	if req.AppointmentRule != "no_need" && req.AppointmentRule != "advance" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的预约规则"))
		return
	}

	// 如果需要提前预约，检查提前小时数
	if req.AppointmentRule == "advance" && req.AdvanceHours <= 0 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请设置提前预约小时数"))
		return
	}

	// 检查有效期
	if req.ValidityType != "permanent" && req.ValidityType != "fixed" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的有效期类型"))
		return
	}

	if req.ValidityType == "fixed" {
		if req.ValidityStart == "" || req.ValidityEnd == "" {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "固定有效期必须设置开始和结束时间"))
			return
		}

		// 解析时间进行比较
		startTime, err := parseTimeString(req.ValidityStart)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("有效期开始时间格式错误: %v", err)))
			return
		}
		endTime, err := parseTimeString(req.ValidityEnd)
		if err != nil {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("有效期结束时间格式错误: %v", err)))
			return
		}

		if endTime.Before(*startTime) {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "有效期结束时间不能早于开始时间"))
			return
		}
	}

	// 检查售卖时间
	if req.SaleStartType != "after_release" && req.SaleStartType != "fixed" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的售卖开始类型"))
		return
	}

	if req.SaleStartType == "fixed" && req.SaleStartAt == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请设置售卖开始时间"))
		return
	}

	if req.SaleEndAt == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请设置售卖结束时间"))
		return
	}

	// 检查流程
	if len(req.Process) > 10 {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "服务流程不能超过10条"))
		return
	}

	// 转换 JSON 字段
	images, _ := json.Marshal(req.Images)
	process, _ := json.Marshal(req.Process)
	unavailableDates, _ := json.Marshal(req.UnavailableDates)

	// 解析时间字段
	validityStart, _ := parseTimeString(req.ValidityStart)
	validityEnd, _ := parseTimeString(req.ValidityEnd)
	saleStartAt, _ := parseTimeString(req.SaleStartAt)
	saleEndAt, _ := parseTimeString(req.SaleEndAt)

	// 更新套餐信息
	existingPkg.Name = req.Name
	existingPkg.Images = string(images)
	existingPkg.TagID = req.TagID
	existingPkg.Sort = req.Sort
	existingPkg.BodyPart = req.BodyPart
	existingPkg.Process = string(process)
	existingPkg.Duration = req.Duration
	existingPkg.Times = req.Times
	existingPkg.SuitableFor = req.SuitableFor
	existingPkg.Price = req.Price
	existingPkg.PointPrice = req.PointPrice
	existingPkg.OriginalPrice = req.OriginalPrice
	existingPkg.ExtraInfo = req.ExtraInfo
	existingPkg.IsAllStores = req.IsAllStores
	existingPkg.DiscountInfo = req.DiscountInfo
	existingPkg.GiftInfo = req.GiftInfo
	existingPkg.ValidityType = req.ValidityType
	existingPkg.ValidityStart = validityStart
	existingPkg.ValidityEnd = validityEnd
	existingPkg.UnavailableDates = string(unavailableDates)
	existingPkg.SaleStartType = req.SaleStartType
	existingPkg.SaleStartAt = saleStartAt
	existingPkg.SaleEndAt = saleEndAt
	existingPkg.AppointmentRule = req.AppointmentRule
	existingPkg.AdvanceHours = req.AdvanceHours
	existingPkg.AppointmentTimeType = req.AppointmentTimeType
	existingPkg.AppointmentStartTime = req.AppointmentStartTime
	existingPkg.AppointmentEndTime = req.AppointmentEndTime
	existingPkg.MaxAppointments = req.MaxAppointments
	existingPkg.MaxPurchases = req.MaxPurchases
	existingPkg.SingleMaxPurchases = req.SingleMaxPurchases
	existingPkg.WarmTips = req.WarmTips
	existingPkg.SupportRefundAnyTime = req.SupportRefundAnyTime
	existingPkg.RefundAnyTimeDesc = req.RefundAnyTimeDesc
	existingPkg.SupportAutoRefund = req.SupportAutoRefund
	existingPkg.AutoRefundDesc = req.AutoRefundDesc
	existingPkg.Description = req.Description
	existingPkg.Status = req.Status
	existingPkg.UpdatedAt = time.Now()

	if err := packageRepo.Update(existingPkg); err != nil {
		logx.Errorf("更新套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新套餐失败"))
		return
	}

	// 更新门店绑定
	if err := packageRepo.BindStores(existingPkg.ID, req.StoreIDs, req.IsAllStores); err != nil {
		logx.Errorf("更新门店绑定失败: %v", err)
		// 不返回错误，继续处理
	}

	// 记录操作日志
	go h.logAdminOperation(r, "套餐管理", "更新", existingPkg.ID, "ServicePackage", fmt.Sprintf("更新套餐: %s", existingPkg.Name))

	httpx.OkJson(w, types.NewSuccessResponse(existingPkg, "更新套餐成功"))
}

// UpdatePackageStatus 更新套餐状态
func (h *ServicePackageHandler) UpdatePackageStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdatePackageStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 验证状态值
	validStatus := map[string]bool{
		"active":   true,
		"disabled": true,
	}

	if !validStatus[req.Status] {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的状态值"))
		return
	}

	logx.Infof("更新套餐状态: packageId=%d, status=%s", req.PackageID, req.Status)

	packageRepo := h.svcCtx.ServicePackageRepo

	// 检查套餐是否存在
	pkg, err := packageRepo.FindByID(req.PackageID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "套餐不存在"))
		} else {
			logx.Errorf("获取套餐失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新套餐状态失败"))
		}
		return
	}

	// 检查是否是已过期状态
	if pkg.Status == "expired" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "套餐已过期，不能更新状态"))
		return
	}

	// 检查状态是否已经是目标状态
	if pkg.Status == req.Status {
		statusText := "启用"
		if req.Status == "disabled" {
			statusText = "禁用"
		}
		httpx.OkJson(w, types.NewSuccessResponse(nil, fmt.Sprintf("套餐已处于%s状态", statusText)))
		return
	}

	// 更新套餐状态
	if err := packageRepo.UpdateStatus(req.PackageID, req.Status); err != nil {
		logx.Errorf("更新套餐状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新套餐状态失败"))
		return
	}

	statusText := "启用"
	if req.Status == "disabled" {
		statusText = "禁用"
	}

	// 记录操作日志
	go h.logAdminOperation(r, "套餐管理", "更新状态", pkg.ID, "ServicePackage", fmt.Sprintf("%s套餐: %s", statusText, pkg.Name))

	httpx.OkJson(w, types.NewSuccessResponse(nil, fmt.Sprintf("套餐%s成功", statusText)))
}

// DeletePackage 删除套餐
func (h *ServicePackageHandler) DeletePackage(w http.ResponseWriter, r *http.Request) {
	var req DeletePackageRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("删除套餐: packageId=%d", req.PackageID)

	packageRepo := h.svcCtx.ServicePackageRepo

	// 检查套餐是否存在
	pkg, err := packageRepo.FindByID(req.PackageID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "套餐不存在"))
		} else {
			logx.Errorf("获取套餐失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除套餐失败"))
		}
		return
	}

	// 检查是否有未完成订单
	hasActiveOrders, err := packageRepo.HasActiveOrders(req.PackageID)
	if err != nil {
		logx.Errorf("检查未完成订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除套餐失败"))
		return
	}

	if hasActiveOrders {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeForbidden, "该套餐有未完成的订单，不能删除"))
		return
	}

	// 删除套餐
	if err := packageRepo.Delete(req.PackageID); err != nil {
		logx.Errorf("删除套餐失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除套餐失败"))
		return
	}

	// 记录操作日志
	go h.logAdminOperation(r, "套餐管理", "删除", pkg.ID, "ServicePackage", fmt.Sprintf("删除套餐: %s", pkg.Name))

	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除套餐成功"))
}

// CheckExpiredPackages 检查并更新过期套餐
func (h *ServicePackageHandler) CheckExpiredPackages() {
	packageRepo := h.svcCtx.ServicePackageRepo
	if err := packageRepo.CheckAndUpdateExpiredStatus(); err != nil {
		logx.Errorf("检查并更新过期套餐失败: %v", err)
	}
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *ServicePackageHandler) logAdminOperation(r *http.Request, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 模块=%s, 操作=%s", module, action)
}
