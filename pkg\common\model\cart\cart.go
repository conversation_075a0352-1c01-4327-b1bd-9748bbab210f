package cart

import (
	"database/sql"
	"time"

	goodsModel "yekaitai/internal/modules/goods/model"
	userModel "yekaitai/pkg/common/model/user"
)

// Cart 购物车表
type Cart struct {
	ID        uint         `json:"id" gorm:"primaryKey;autoIncrement;comment:购物车ID"`
	UserID    uint         `json:"user_id" gorm:"not null;index;comment:用户ID"`
	GoodsID   uint         `json:"goods_id" gorm:"not null;index;comment:商品ID"`
	SpecID    uint         `json:"spec_id" gorm:"comment:规格ID"`
	Quantity  int          `json:"quantity" gorm:"not null;default:1;comment:购买数量"`
	CreatedAt time.Time    `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time    `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt sql.NullTime `json:"-" gorm:"index;comment:删除时间"`

	// 关联字段
	User  *userModel.WxUser     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Goods *goodsModel.Goods     `json:"goods,omitempty" gorm:"foreignKey:GoodsID"`
	Spec  *goodsModel.GoodsSpec `json:"spec,omitempty" gorm:"foreignKey:SpecID"`
}

// CartQueryParams 购物车查询参数
type CartQueryParams struct {
	UserID   uint `form:"user_id"`
	GoodsID  uint `form:"goods_id"`
	Page     int  `form:"page"`
	PageSize int  `form:"page_size"`
}

// CartAddRequest 添加购物车请求
type CartAddRequest struct {
	GoodsID  uint `json:"goods_id" validate:"required"`
	SpecID   uint `json:"spec_id,optional"`
	Quantity int  `json:"quantity" validate:"required,min=1,max=99"`
}

// CartUpdateRequest 更新购物车请求
type CartUpdateRequest struct {
	Quantity int `json:"quantity" validate:"required,min=1,max=99"`
}

// CartBatchRequest 批量操作购物车请求
type CartBatchRequest struct {
	CartIDs []uint `json:"cart_ids" validate:"required"`
}

// TableName 表名
func (Cart) TableName() string {
	return "carts"
}

// CartResponse 购物车响应结构
type CartResponse struct {
	ID         uint                  `json:"id"`
	UserID     uint                  `json:"user_id"`
	GoodsID    uint                  `json:"goods_id"`
	SpecID     uint                  `json:"spec_id"`
	Quantity   int                   `json:"quantity"`
	Goods      *goodsModel.Goods     `json:"goods,omitempty"`
	Spec       *goodsModel.GoodsSpec `json:"spec,omitempty"`
	TotalPrice float64               `json:"total_price"` // 计算得出的总价
	CreatedAt  time.Time             `json:"created_at"`
	UpdatedAt  time.Time             `json:"updated_at"`
}
