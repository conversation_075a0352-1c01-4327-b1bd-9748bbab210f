package routes

import (
	"net/http"

	"yekaitai/wx_internal/modules/email/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterEmailRoutes 注册邮件相关路由
func RegisterEmailRoutes(server RestServer, svcCtx *svc.WxServiceContext) {
	// 创建邮件处理器
	emailHandler := handler.NewEmailHandler(svcCtx)

	// 注册邮件相关路由
	server.AddRoutes([]rest.Route{
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/email/appointment",
			Handler: emailHandler.SubmitAppointment,
		},
		{
			Method:  http.MethodPost,
			Path:    "/api/wx/email/test",
			Handler: emailHandler.TestEmail,
		},
	})
}
