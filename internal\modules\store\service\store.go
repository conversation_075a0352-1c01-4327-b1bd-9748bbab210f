package service

import (
	"context"
	"fmt"
	adminModel "yekaitai/internal/modules/admin/model"
	"yekaitai/internal/modules/store/model"
	"yekaitai/pkg/infra/mysql"
)

// StoreService 门店服务接口
type StoreService interface {
	// CreateStore 创建门店
	CreateStore(store *model.Store) error
	// UpdateStore 更新门店
	UpdateStore(store *model.Store) error
	// GetStore 获取门店详情
	GetStore(id uint) (*model.Store, error)
	// ListStores 获取门店列表
	ListStores(page, size int, query string) ([]*model.Store, int64, error)
	// DeleteStore 删除门店
	DeleteStore(id uint) error
	// ListNearbyStores 获取附近的门店
	ListNearbyStores(latitude, longitude float64, maxDistance float64, page, size int) ([]*model.Store, int64, error)
}

type storeService struct {
	repo     model.StoreRepository
	location LocationService
}

// NewStoreService 创建门店服务实例
func NewStoreService(repo model.StoreRepository, location LocationService) StoreService {
	return &storeService{
		repo:     repo,
		location: location,
	}
}

// CreateStore 创建门店
func (s *storeService) CreateStore(store *model.Store) error {
	// 如果没有提供经纬度，但提供了地址，则通过地理编码获取经纬度
	if store.Latitude == 0 && store.Longitude == 0 && store.Address != "" {
		fullAddress := fmt.Sprintf("%s%s%s%s", store.ProvinceID, store.CityID, store.AreaID, store.Address)
		lat, lng, err := s.location.Geocode(fullAddress)
		if err != nil {
			return fmt.Errorf("地理编码失败: %v", err)
		}
		store.Latitude = lat
		store.Longitude = lng
	}
	return s.repo.Create(store)
}

// UpdateStore 更新门店
func (s *storeService) UpdateStore(store *model.Store) error {
	// 如果地址发生变化，重新获取经纬度
	oldStore, err := s.repo.FindByID(store.ID)
	if err != nil {
		return err
	}

	if oldStore.Address != store.Address {
		fullAddress := fmt.Sprintf("%s%s%s%s", store.ProvinceID, store.CityID, store.AreaID, store.Address)
		lat, lng, err := s.location.Geocode(fullAddress)
		if err != nil {
			return fmt.Errorf("地理编码失败: %v", err)
		}
		store.Latitude = lat
		store.Longitude = lng
	}
	return s.repo.Update(store)
}

// GetStore 获取门店详情
func (s *storeService) GetStore(id uint) (*model.Store, error) {
	return s.repo.FindByID(id)
}

// ListStores 获取门店列表
func (s *storeService) ListStores(page, size int, query string) ([]*model.Store, int64, error) {
	return s.repo.List(page, size, query)
}

// DeleteStore 删除门店
func (s *storeService) DeleteStore(id uint) error {
	return s.repo.Delete(id)
}

// ListNearbyStores 获取附近的门店
func (s *storeService) ListNearbyStores(latitude, longitude float64, maxDistance float64, page, size int) ([]*model.Store, int64, error) {
	if maxDistance <= 0 {
		maxDistance = 5000 // 默认5公里
	}
	return s.repo.ListNearby(latitude, longitude, maxDistance, page, size)
}

func (s *storeService) Create(ctx context.Context, req *model.StoreCreateRequest, creatorID uint) error {
	store := &model.Store{
		Name:        req.Name,
		Phone:       req.Phone,
		ProvinceID:  req.ProvinceID,
		CityID:      req.CityID,
		AreaID:      req.AreaID,
		Address:     req.Address,
		Description: req.Description,
		Images:      req.Images,
		ManagerID:   req.ManagerID,
		CreatorID:   creatorID,
		Status:      1,
	}

	return mysql.Master().Create(store).Error
}

func (s *storeService) Update(ctx context.Context, req *model.StoreUpdateRequest) error {
	store := &model.Store{
		Name:        req.Name,
		Phone:       req.Phone,
		ProvinceID:  req.ProvinceID,
		CityID:      req.CityID,
		AreaID:      req.AreaID,
		Address:     req.Address,
		Description: req.Description,
		Images:      req.Images,
		ManagerID:   req.ManagerID,
		Status:      req.Status,
	}

	return mysql.Master().Model(&model.Store{}).Where("id = ?", req.ID).Updates(store).Error
}

func (s *storeService) Delete(ctx context.Context, id uint) error {
	return mysql.Master().Delete(&model.Store{}, id).Error
}

func (s *storeService) Get(ctx context.Context, id uint) (*model.StoreResponse, error) {
	var store model.Store
	if err := mysql.Master().First(&store, id).Error; err != nil {
		return nil, err
	}

	// 获取管理员和创建人信息
	var managerName, creatorName string
	if err := mysql.Master().Model(&adminModel.AdminUser{}).Where("admin_id = ?", store.ManagerID).Pluck("username", &managerName).Error; err != nil {
		return nil, err
	}
	if err := mysql.Master().Model(&adminModel.AdminUser{}).Where("admin_id = ?", store.CreatorID).Pluck("username", &creatorName).Error; err != nil {
		return nil, err
	}

	return &model.StoreResponse{
		ID:          store.ID,
		Name:        store.Name,
		Phone:       store.Phone,
		ProvinceID:  store.ProvinceID,
		CityID:      store.CityID,
		AreaID:      store.AreaID,
		Address:     store.Address,
		Description: store.Description,
		Images:      store.Images,
		ManagerID:   store.ManagerID,
		ManagerName: managerName,
		CreatorID:   store.CreatorID,
		CreatorName: creatorName,
		Status:      store.Status,
		CreatedAt:   store.CreatedAt,
		UpdatedAt:   store.UpdatedAt,
	}, nil
}

func (s *storeService) List(ctx context.Context, req *model.StoreListRequest) (*model.StoreListResponse, error) {
	var total int64
	var stores []model.Store
	db := mysql.Master().Model(&model.Store{})

	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Phone != "" {
		db = db.Where("phone LIKE ?", "%"+req.Phone+"%")
	}
	if req.ProvinceID != "" {
		db = db.Where("province_id = ?", req.ProvinceID)
	}
	if req.CityID != "" {
		db = db.Where("city_id = ?", req.CityID)
	}
	if req.AreaID != "" {
		db = db.Where("area_id = ?", req.AreaID)
	}
	if req.ManagerID > 0 {
		db = db.Where("manager_id = ?", req.ManagerID)
	}
	if req.Status > 0 {
		db = db.Where("status = ?", req.Status)
	}

	if err := db.Count(&total).Error; err != nil {
		return nil, err
	}

	if err := db.Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize).Find(&stores).Error; err != nil {
		return nil, err
	}

	// 获取所有管理员和创建人ID
	var managerIDs, creatorIDs []uint
	for _, store := range stores {
		managerIDs = append(managerIDs, store.ManagerID)
		creatorIDs = append(creatorIDs, store.CreatorID)
	}

	// 获取管理员和创建人信息
	managerMap := make(map[uint]string)
	creatorMap := make(map[uint]string)
	var users []adminModel.AdminUser
	if err := mysql.Master().Where("admin_id IN ?", append(managerIDs, creatorIDs...)).Find(&users).Error; err != nil {
		return nil, err
	}
	for _, user := range users {
		managerMap[user.AdminID] = user.Username
		creatorMap[user.AdminID] = user.Username
	}

	// 构建响应
	list := make([]model.StoreResponse, 0, len(stores))
	for _, store := range stores {
		list = append(list, model.StoreResponse{
			ID:          store.ID,
			Name:        store.Name,
			Phone:       store.Phone,
			ProvinceID:  store.ProvinceID,
			CityID:      store.CityID,
			AreaID:      store.AreaID,
			Address:     store.Address,
			Description: store.Description,
			Images:      store.Images,
			ManagerID:   store.ManagerID,
			ManagerName: managerMap[store.ManagerID],
			CreatorID:   store.CreatorID,
			CreatorName: creatorMap[store.CreatorID],
			Status:      store.Status,
			CreatedAt:   store.CreatedAt,
			UpdatedAt:   store.UpdatedAt,
		})
	}

	return &model.StoreListResponse{
		Total: total,
		List:  list,
	}, nil
}
