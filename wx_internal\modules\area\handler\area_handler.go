package handler

import (
	"net/http"

	"yekaitai/internal/modules/area/model"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// AreaHandler 小程序端区域处理器
type AreaHandler struct {
	db       *gorm.DB
	areaRepo model.EnabledAreaRepository
}

// NewAreaHandler 创建小程序端区域处理器
func NewAreaHandler(db *gorm.DB) *AreaHandler {
	// 如果传入的db为空，则使用mysql.Master()获取连接
	if db == nil {
		logx.Info("使用默认Master数据库连接初始化AreaHandler")
		db = mysql.Master()
	}

	return &AreaHandler{
		db:       db,
		areaRepo: model.NewEnabledAreaRepository(db),
	}
}

// GetEnabledProvinces 获取已开通省份
func (h *AreaHandler) GetEnabledProvinces(w http.ResponseWriter, r *http.Request) {
	logx.Infof("开始获取已开通省份列表")

	// 确保DB连接存在
	if h.db == nil {
		logx.Error("数据库连接为空，重新初始化")
		h.db = mysql.Master()
		h.areaRepo = model.NewEnabledAreaRepository(h.db)
	}

	provinces, err := h.areaRepo.GetProvinces()
	if err != nil {
		logx.Errorf("获取已开通省份列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取已开通省份列表失败"))
		return
	}

	logx.Infof("成功获取已开通省份列表，共 %d 条数据", len(provinces))
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(provinces, "获取已开通省份列表成功"))
}

// GetEnabledCities 获取指定省份的已开通城市
func (h *AreaHandler) GetEnabledCities(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取provinceCode
	vars := r.URL.Query()
	provinceCode := vars.Get("provinceCode")
	if provinceCode == "" {
		logx.Error("获取已开通城市列表失败: 省份编码不能为空")
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "省份编码不能为空"))
		return
	}

	logx.Infof("开始获取省份 %s 的已开通城市列表", provinceCode)

	// 确保DB连接存在
	if h.db == nil {
		logx.Error("数据库连接为空，重新初始化")
		h.db = mysql.Master()
		h.areaRepo = model.NewEnabledAreaRepository(h.db)
	}

	cities, err := h.areaRepo.GetCities(provinceCode)
	if err != nil {
		logx.Errorf("获取省份 %s 的已开通城市列表失败: %v", provinceCode, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取已开通城市列表失败"))
		return
	}

	logx.Infof("成功获取省份 %s a的已开通城市列表，共 %d 条数据", provinceCode, len(cities))
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(cities, "获取已开通城市列表成功"))
}

// GetEnabledAreas 获取指定城市的已开通区县
func (h *AreaHandler) GetEnabledAreas(w http.ResponseWriter, r *http.Request) {
	// 解析URL参数以获取cityCode
	vars := r.URL.Query()
	cityCode := vars.Get("cityCode")
	if cityCode == "" {
		logx.Error("获取已开通区县列表失败: 城市编码不能为空")
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "城市编码不能为空"))
		return
	}

	logx.Infof("开始获取城市 %s 的已开通区县列表", cityCode)

	// 确保DB连接存在
	if h.db == nil {
		logx.Error("数据库连接为空，重新初始化")
		h.db = mysql.Master()
		h.areaRepo = model.NewEnabledAreaRepository(h.db)
	}

	areas, err := h.areaRepo.GetAreas(cityCode)
	if err != nil {
		logx.Errorf("获取城市 %s 的已开通区县列表失败: %v", cityCode, err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取已开通区县列表失败"))
		return
	}

	logx.Infof("成功获取城市 %s 的已开通区县列表，共 %d 条数据", cityCode, len(areas))
	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(areas, "获取已开通区县列表成功"))
}
