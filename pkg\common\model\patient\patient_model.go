package patient

import (
	"time"

	"gorm.io/gorm"
)

// HealthRecord 患者健康档案表
type HealthRecord struct {
	ID              uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:健康档案ID"`
	PatientID       uint           `json:"patient_id" gorm:"uniqueIndex;not null;comment:患者ID"`
	Height          float64        `json:"height" gorm:"type:decimal(5,2);comment:身高(cm)"`
	Weight          float64        `json:"weight" gorm:"type:decimal(5,2);comment:体重(kg)"`
	BloodType       string         `json:"blood_type" gorm:"type:varchar(10);comment:血型"`
	BloodPressure   string         `json:"blood_pressure" gorm:"type:varchar(20);comment:血压"`
	HeartRate       int            `json:"heart_rate" gorm:"comment:心率(次/分)"`
	BloodSugar      float64        `json:"blood_sugar" gorm:"type:decimal(5,2);comment:血糖(mmol/L)"`
	MedicalHistory  string         `json:"medical_history" gorm:"type:text;comment:病史"`
	AllergyHistory  string         `json:"allergy_history" gorm:"type:text;comment:过敏史"`
	FamilyHistory   string         `json:"family_history" gorm:"type:text;comment:家族病史"`
	ChronicDiseases string         `json:"chronic_diseases" gorm:"type:text;comment:慢性病"`
	Notes           string         `json:"notes" gorm:"type:text;comment:备注信息"`
	LastCheckupDate time.Time      `json:"last_checkup_date" gorm:"comment:最近体检日期"`
	CreatedAt       time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt       time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 设置HealthRecord表名
func (HealthRecord) TableName() string {
	return "patient_health_record"
}

// PatientEmergencyContact 患者紧急联系人信息表
type PatientEmergencyContact struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:联系人ID"`
	PatientID    uint           `json:"patient_id" gorm:"index;not null;comment:患者ID"`
	Name         string         `json:"name" gorm:"type:varchar(50);not null;comment:联系人姓名"`
	Relationship string         `json:"relationship" gorm:"type:varchar(20);comment:与患者关系"`
	Phone        string         `json:"phone" gorm:"type:varchar(20);not null;comment:联系电话"`
	Address      string         `json:"address" gorm:"type:varchar(255);comment:联系地址"`
	IsDefault    bool           `json:"is_default" gorm:"default:false;comment:是否默认联系人"`
	CreatedAt    time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt    time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 设置PatientEmergencyContact表名
func (PatientEmergencyContact) TableName() string {
	return "patient_emergency_contact"
}
