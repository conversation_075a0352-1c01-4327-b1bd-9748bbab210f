package model

import "time"

type StoreResponse struct {
	ID          uint      `json:"id" comment:"门店ID"`
	Name        string    `json:"name" comment:"门店名称"`
	Phone       string    `json:"phone" comment:"联系电话"`
	ProvinceID  string    `json:"province_id" comment:"省份编码"`
	CityID      string    `json:"city_id" comment:"城市编码"`
	AreaID      string    `json:"area_id" comment:"区县编码"`
	Address     string    `json:"address" comment:"详细地址"`
	Description string    `json:"description" comment:"门店介绍"`
	Images      string    `json:"images" comment:"门店图片,JSON格式"`
	ManagerID   uint      `json:"manager_id" comment:"门店管理员ID"`
	ManagerName string    `json:"manager_name" comment:"门店管理员姓名"`
	CreatorID   uint      `json:"creator_id" comment:"创建人ID"`
	CreatorName string    `json:"creator_name" comment:"创建人姓名"`
	Status      int       `json:"status" comment:"状态：1-正常，0-禁用"`
	CreatedAt   time.Time `json:"created_at" comment:"创建时间"`
	UpdatedAt   time.Time `json:"updated_at" comment:"更新时间"`
}

type StoreListResponse struct {
	Total int64           `json:"total" comment:"总数"`
	List  []StoreResponse `json:"list" comment:"列表"`
}
