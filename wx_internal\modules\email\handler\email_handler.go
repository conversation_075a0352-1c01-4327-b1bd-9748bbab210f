package handler

import (
	"net/http"
	"strings"
	"time"

	"yekaitai/internal/types"
	"yekaitai/pkg/email"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// EmailHandler 邮件处理器
type EmailHandler struct {
	svcCtx       *svc.WxServiceContext
	emailService *email.EmailService
}

// NewEmailHandler 创建邮件处理器
func NewEmailHandler(svcCtx *svc.WxServiceContext) *EmailHandler {
	// 从配置中获取邮件配置
	emailConfig := &email.EmailConfig{
		Host:     svcCtx.WxConfig.Email.Host,
		Username: svcCtx.WxConfig.Email.Username,
		Password: svcCtx.WxConfig.Email.Password,
		Port:     svcCtx.WxConfig.Email.EmailPort,
	}

	// 记录邮件配置信息（不记录密码）
	logx.Infof("邮件服务初始化 - Host: %s, Username: %s, EmailPort: %d, RecipientEmail: %s",
		svcCtx.WxConfig.Email.Host, svcCtx.WxConfig.Email.Username, svcCtx.WxConfig.Email.EmailPort, svcCtx.WxConfig.Email.RecipientEmail)

	// 测试邮件服务连接
	if err := email.NewEmailService(emailConfig).TestConnection(); err != nil {
		logx.Errorf("邮件服务连接测试失败: %v", err)
	} else {
		logx.Infof("邮件服务连接测试成功")
	}

	return &EmailHandler{
		svcCtx:       svcCtx,
		emailService: email.NewEmailService(emailConfig),
	}
}

// AppointmentRequest 预约请求
type AppointmentRequest struct {
	Name      string `json:"name" validate:"required"`       // 姓名
	Gender    string `json:"gender" validate:"required"`     // 性别 (1-男, 2-女)
	BirthDate string `json:"birth_date" validate:"required"` // 出生年月日 (YYYY-MM-DD)
	Phone     string `json:"phone" validate:"required"`      // 电话号码
	Email     string `json:"email" validate:"required"`      // 邮箱
}

// SubmitAppointment 提交预约信息
func (h *EmailHandler) SubmitAppointment(w http.ResponseWriter, r *http.Request) {
	var req AppointmentRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析预约请求参数失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "请求参数错误"))
		return
	}

	// 获取用户OpenID（小程序端使用OpenID作为用户标识）
	openIDValue := r.Context().Value(types.UserIDKey)
	if openIDValue == nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	openID, ok := openIDValue.(string)
	if !ok {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "无效的用户标识"))
		return
	}

	// 验证请求参数
	if errMsg := h.validateAppointmentRequest(&req); errMsg != "" {
		logx.Errorf("预约请求参数验证失败: %s", errMsg)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, errMsg))
		return
	}

	// 构建预约信息
	appointmentInfo := &email.AppointmentInfo{
		Name:       strings.TrimSpace(req.Name),
		Gender:     req.Gender,
		BirthDate:  req.BirthDate,
		Phone:      strings.TrimSpace(req.Phone),
		Email:      strings.TrimSpace(req.Email),
		SubmitTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 获取收件人邮箱配置
	recipientEmail := h.svcCtx.WxConfig.Email.RecipientEmail
	if recipientEmail == "" {
		// 如果没有配置专门的收件人邮箱，使用发件人邮箱
		recipientEmail = h.svcCtx.WxConfig.Email.Username
	}

	if recipientEmail == "" {
		logx.Errorf("邮件配置错误: 收件人邮箱地址为空，请检查配置文件中的Email.RecipientEmail或Email.Username字段")
		logx.Errorf("当前配置值 - RecipientEmail: [%s], Username: [%s]", h.svcCtx.WxConfig.Email.RecipientEmail, h.svcCtx.WxConfig.Email.Username)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "邮件服务配置错误"))
		return
	}

	// 记录用户提交的详细信息
	logx.Infof("用户 %s 提交预约信息 - 姓名: %s, 性别: %s, 生日: %s, 电话: %s, 邮箱: %s",
		openID, appointmentInfo.Name, appointmentInfo.Gender, appointmentInfo.BirthDate,
		appointmentInfo.Phone, appointmentInfo.Email)

	// 使用重试机制发送邮件（最多重试3次）
	err := h.sendEmailWithRetry(appointmentInfo, recipientEmail, 3)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "发送预约信息失败，请稍后重试"))
		return
	}

	logx.Infof("用户 %s 提交预约信息成功，邮件已发送到 %s", openID, recipientEmail)

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(map[string]interface{}{
		"message":     "预约信息提交成功",
		"submit_time": appointmentInfo.SubmitTime,
	}, "预约信息已提交，我们会尽快与您联系"))
}

// validateAppointmentRequest 验证预约请求参数
func (h *EmailHandler) validateAppointmentRequest(req *AppointmentRequest) string {
	// 验证姓名
	if strings.TrimSpace(req.Name) == "" {
		return "姓名不能为空"
	}
	if len([]rune(req.Name)) > 50 {
		return "姓名长度不能超过50个字符"
	}

	// 验证性别
	if req.Gender != "1" && req.Gender != "2" {
		return "性别参数无效，请选择男(1)或女(2)"
	}

	// 验证出生日期
	if req.BirthDate == "" {
		return "出生年月日不能为空"
	}
	if _, err := time.Parse("2006-01-02", req.BirthDate); err != nil {
		return "出生年月日格式错误，请使用YYYY-MM-DD格式"
	}

	// 验证电话号码
	phone := strings.TrimSpace(req.Phone)
	if phone == "" {
		return "电话号码不能为空"
	}
	if len(phone) < 7 || len(phone) > 20 {
		return "电话号码长度应在7-20位之间"
	}

	// 验证邮箱
	emailAddr := strings.TrimSpace(req.Email)
	if emailAddr == "" {
		return "邮箱不能为空"
	}
	if !email.IsValidEmail(emailAddr) {
		return "邮箱格式不正确"
	}

	return ""
}

// sendEmailWithRetry 带重试机制的邮件发送
func (h *EmailHandler) sendEmailWithRetry(appointmentInfo *email.AppointmentInfo, recipientEmail string, maxRetries int) error {
	var lastErr error

	// 记录详细的发送信息
	logx.Infof("开始发送预约邮件 - 收件人: %s, 发送人: %s, 电话: %s, 邮箱: %s",
		recipientEmail, appointmentInfo.Name, appointmentInfo.Phone, appointmentInfo.Email)

	for attempt := 1; attempt <= maxRetries; attempt++ {
		logx.Infof("邮件发送尝试 %d/%d", attempt, maxRetries)

		err := h.emailService.SendAppointmentEmail(appointmentInfo, recipientEmail)
		if err == nil {
			logx.Infof("邮件发送成功 - 尝试次数: %d", attempt)
			return nil
		}

		lastErr = err
		logx.Errorf("邮件发送失败 (尝试 %d/%d): %v", attempt, maxRetries, err)

		// 如果不是最后一次尝试，等待一段时间再重试
		if attempt < maxRetries {
			waitTime := time.Duration(attempt) * time.Second // 递增等待时间：1s, 2s, 3s...
			logx.Infof("等待 %v 后进行第 %d 次重试", waitTime, attempt+1)
			time.Sleep(waitTime)
		}
	}

	// 记录最终失败的详细信息
	logx.Errorf("邮件发送最终失败 - 已尝试 %d 次, 最后错误: %v", maxRetries, lastErr)
	logx.Errorf("失败的预约信息详情 - 姓名: %s, 性别: %s, 生日: %s, 电话: %s, 邮箱: %s, 提交时间: %s",
		appointmentInfo.Name, appointmentInfo.Gender, appointmentInfo.BirthDate,
		appointmentInfo.Phone, appointmentInfo.Email, appointmentInfo.SubmitTime)

	return lastErr
}

// sendTestEmailWithRetry 带重试机制的测试邮件发送
func (h *EmailHandler) sendTestEmailWithRetry(recipientEmail string, maxRetries int) error {
	var lastErr error

	logx.Infof("开始发送测试邮件 - 收件人: %s", recipientEmail)

	for attempt := 1; attempt <= maxRetries; attempt++ {
		logx.Infof("测试邮件发送尝试 %d/%d", attempt, maxRetries)

		err := h.emailService.SendTestEmail(recipientEmail)
		if err == nil {
			logx.Infof("测试邮件发送成功 - 尝试次数: %d", attempt)
			return nil
		}

		lastErr = err
		logx.Errorf("测试邮件发送失败 (尝试 %d/%d): %v", attempt, maxRetries, err)

		// 如果不是最后一次尝试，等待一段时间再重试
		if attempt < maxRetries {
			waitTime := time.Duration(attempt) * time.Second
			logx.Infof("等待 %v 后进行第 %d 次重试", waitTime, attempt+1)
			time.Sleep(waitTime)
		}
	}

	logx.Errorf("测试邮件发送最终失败 - 已尝试 %d 次, 最后错误: %v", maxRetries, lastErr)
	return lastErr
}

// TestEmail 测试邮件发送（仅用于开发测试）
func (h *EmailHandler) TestEmail(w http.ResponseWriter, r *http.Request) {
	// 获取用户OpenID
	openIDValue := r.Context().Value(types.UserIDKey)
	if openIDValue == nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "用户未登录"))
		return
	}

	openID, ok := openIDValue.(string)
	if !ok {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "无效的用户标识"))
		return
	}

	// 获取收件人邮箱配置
	recipientEmail := h.svcCtx.WxConfig.Email.RecipientEmail
	if recipientEmail == "" {
		// 如果没有配置专门的收件人邮箱，使用发件人邮箱
		recipientEmail = h.svcCtx.WxConfig.Email.Username
	}

	if recipientEmail == "" {
		logx.Errorf("邮件配置错误: 收件人邮箱地址为空，请检查配置文件中的Email.RecipientEmail或Email.Username字段")
		logx.Errorf("当前配置值 - RecipientEmail: [%s], Username: [%s]", h.svcCtx.WxConfig.Email.RecipientEmail, h.svcCtx.WxConfig.Email.Username)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "邮件服务配置错误"))
		return
	}

	// 使用重试机制发送测试邮件（最多重试3次）
	err := h.sendTestEmailWithRetry(recipientEmail, 3)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "发送测试邮件失败"))
		return
	}

	logx.Infof("用户 %s 发送测试邮件成功", openID)

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(map[string]interface{}{
		"message":   "测试邮件发送成功",
		"send_time": time.Now().Format("2006-01-02 15:04:05"),
		"recipient": recipientEmail,
	}, "测试邮件已发送"))
}
