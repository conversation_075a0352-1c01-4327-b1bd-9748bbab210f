package upload

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"yekaitai/pkg/response"

	"github.com/zeromicro/go-zero/core/logx"
)

// UploadHandler 上传处理器
type UploadHandler struct {
	service        Service
	allowedOrigins map[string]bool
}

// NewUploadHandler 创建上传处理器
func NewUploadHandler(service Service) *UploadHandler {
	config := GetUploadConfig()
	allowedOrigins := make(map[string]bool)

	// 处理允许的域名
	for _, origin := range config.AllowedDomains {
		allowedOrigins[origin] = true
	}

	logx.Info("创建上传处理器")

	return &UploadHandler{
		service:        service,
		allowedOrigins: allowedOrigins,
	}
}

// DefaultUploadHandler 默认上传处理器
var DefaultUploadHandler *UploadHandler

// InitDefaultUploadHandler 初始化默认上传处理器
func InitDefaultUploadHandler() {
	logx.Info("初始化默认上传处理器")
	if DefaultService == nil {
		InitDefaultService()
	}
	DefaultUploadHandler = NewUploadHandler(DefaultService)
}

// handleCORS 处理跨域请求
func (h *UploadHandler) handleCORS(w http.ResponseWriter, r *http.Request) bool {
	origin := r.Header.Get("Origin")
	if origin == "" {
		return true
	}

	// 检查是否允许所有源
	if h.allowedOrigins["*"] {
		w.Header().Set("Access-Control-Allow-Origin", origin)
		w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		return true
	}

	// 检查特定源是否允许
	if h.allowedOrigins[origin] {
		w.Header().Set("Access-Control-Allow-Origin", origin)
		w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		return true
	}

	// 不允许的源
	logx.Errorf("跨域请求被拒绝，来源: %s", origin)
	http.Error(w, "不允许的源", http.StatusForbidden)
	return false
}

// SendErrorResponse 发送错误响应
func SendErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	logx.Errorf("上传错误: %s, 状态码: %d", message, statusCode)
	response.Error(w, statusCode, message)
}

// SendSuccessResponse 发送成功响应
func SendSuccessResponse(w http.ResponseWriter, data interface{}) {
	response.Success(w, data)
}

// ServeHTTP 处理HTTP请求
func (h *UploadHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// 处理预检请求
	if r.Method == http.MethodOptions {
		if h.handleCORS(w, r) {
			return
		}
	}

	// 只允许POST请求
	if r.Method != http.MethodPost {
		SendErrorResponse(w, http.StatusMethodNotAllowed, "只允许POST请求")
		return
	}

	// 处理跨域
	if !h.handleCORS(w, r) {
		return
	}

	// 解析多部分表单
	if err := r.ParseMultipartForm(32 << 20); err != nil {
		logx.Errorf("解析表单数据失败: %v", err)
		SendErrorResponse(w, http.StatusBadRequest, "无法解析表单数据: "+err.Error())
		return
	}

	// 获取文件
	file, header, err := r.FormFile("file")
	if err != nil {
		logx.Errorf("获取上传文件失败: %v", err)
		SendErrorResponse(w, http.StatusBadRequest, "无法获取上传文件: "+err.Error())
		return
	}
	defer file.Close()

	logx.Infof("接收到文件上传请求: %s, 大小: %d bytes", header.Filename, header.Size)

	// 获取路径参数
	path := r.FormValue("path")

	// 获取上传类型
	uploadType := r.FormValue("type")
	if uploadType == "" {
		uploadType = "general"
	}

	var result *UploadResult
	switch strings.ToLower(uploadType) {
	case "image", "img":
		logx.Infof("处理图片上传: %s", header.Filename)
		result, err = h.service.UploadImage(r.Context(), header, path)
	case "document", "doc":
		logx.Infof("处理文档上传: %s", header.Filename)
		result, err = h.service.UploadDocument(r.Context(), header, path)
	case "video", "vid":
		logx.Infof("处理视频上传: %s", header.Filename)
		result, err = h.service.UploadVideo(r.Context(), header, path)
	default:
		logx.Infof("处理通用文件上传: %s", header.Filename)
		result, err = h.service.UploadFile(r.Context(), header, UploadTypeGeneral, path)
	}

	if err != nil {
		var statusCode int
		var errMsg string

		if errors.Is(err, ErrFileTooLarge) {
			statusCode = http.StatusBadRequest
			errMsg = "文件太大"
		} else if errors.Is(err, ErrUnsupportedFileType) {
			statusCode = http.StatusBadRequest
			errMsg = "不支持的文件类型"
		} else {
			statusCode = http.StatusInternalServerError
			errMsg = fmt.Sprintf("上传失败: %s", err.Error())
		}

		logx.Errorf("文件上传失败: %v", err)
		SendErrorResponse(w, statusCode, errMsg)
		return
	}

	logx.Infof("文件上传成功: %s -> %s", header.Filename, result.URL)
	SendSuccessResponse(w, result)
}

// UploadFileHandler 上传文件处理器
func UploadFileHandler(w http.ResponseWriter, r *http.Request) {
	if DefaultUploadHandler == nil {
		InitDefaultUploadHandler()
	}
	DefaultUploadHandler.ServeHTTP(w, r)
}

// UploadImageHandler 上传图片处理器
func UploadImageHandler(w http.ResponseWriter, r *http.Request) {
	// 确保默认处理器初始化
	if DefaultUploadHandler == nil {
		InitDefaultUploadHandler()
	}

	// 处理预检请求
	if r.Method == http.MethodOptions {
		if DefaultUploadHandler.handleCORS(w, r) {
			return
		}
	}

	// 只允许POST请求
	if r.Method != http.MethodPost {
		SendErrorResponse(w, http.StatusMethodNotAllowed, "只允许POST请求")
		return
	}

	// 处理跨域
	if !DefaultUploadHandler.handleCORS(w, r) {
		return
	}

	// 解析多部分表单
	if err := r.ParseMultipartForm(32 << 20); err != nil {
		logx.Errorf("解析表单数据失败: %v", err)
		SendErrorResponse(w, http.StatusBadRequest, "无法解析表单数据: "+err.Error())
		return
	}

	// 获取文件
	file, header, err := r.FormFile("file")
	if err != nil {
		logx.Errorf("获取上传文件失败: %v", err)
		SendErrorResponse(w, http.StatusBadRequest, "无法获取上传文件: "+err.Error())
		return
	}
	defer file.Close()

	logx.Infof("接收到图片上传请求: %s, 大小: %d bytes", header.Filename, header.Size)

	// 获取路径参数
	path := r.FormValue("path")

	// 上传图片
	result, err := DefaultUploadHandler.service.UploadImage(r.Context(), header, path)
	if err != nil {
		var statusCode int
		var errMsg string

		if errors.Is(err, ErrFileTooLarge) {
			statusCode = http.StatusBadRequest
			errMsg = "文件太大"
		} else if errors.Is(err, ErrUnsupportedFileType) {
			statusCode = http.StatusBadRequest
			errMsg = "不支持的文件类型"
		} else {
			statusCode = http.StatusInternalServerError
			errMsg = fmt.Sprintf("上传失败: %s", err.Error())
		}

		logx.Errorf("图片上传失败: %v", err)
		SendErrorResponse(w, statusCode, errMsg)
		return
	}

	logx.Infof("图片上传成功: %s -> %s", header.Filename, result.URL)
	SendSuccessResponse(w, result)
}

// UploadDocumentHandler 上传文档处理器
func UploadDocumentHandler(w http.ResponseWriter, r *http.Request) {
	// 确保默认处理器初始化
	if DefaultUploadHandler == nil {
		InitDefaultUploadHandler()
	}

	// 处理预检请求
	if r.Method == http.MethodOptions {
		if DefaultUploadHandler.handleCORS(w, r) {
			return
		}
	}

	// 只允许POST请求
	if r.Method != http.MethodPost {
		SendErrorResponse(w, http.StatusMethodNotAllowed, "只允许POST请求")
		return
	}

	// 处理跨域
	if !DefaultUploadHandler.handleCORS(w, r) {
		return
	}

	// 解析多部分表单
	if err := r.ParseMultipartForm(32 << 20); err != nil {
		SendErrorResponse(w, http.StatusBadRequest, "无法解析表单数据: "+err.Error())
		return
	}

	// 获取文件
	file, header, err := r.FormFile("file")
	if err != nil {
		SendErrorResponse(w, http.StatusBadRequest, "无法获取上传文件: "+err.Error())
		return
	}
	defer file.Close()

	// 获取路径参数
	path := r.FormValue("path")

	// 上传文档
	result, err := DefaultUploadHandler.service.UploadDocument(r.Context(), header, path)
	if err != nil {
		var statusCode int
		var errMsg string

		if errors.Is(err, ErrFileTooLarge) {
			statusCode = http.StatusBadRequest
			errMsg = "文件太大"
		} else if errors.Is(err, ErrUnsupportedFileType) {
			statusCode = http.StatusBadRequest
			errMsg = "不支持的文件类型"
		} else {
			statusCode = http.StatusInternalServerError
			errMsg = fmt.Sprintf("上传失败: %s", err.Error())
		}

		SendErrorResponse(w, statusCode, errMsg)
		return
	}

	SendSuccessResponse(w, result)
}

// UploadVideoHandler 上传视频处理器
func UploadVideoHandler(w http.ResponseWriter, r *http.Request) {
	// 确保默认处理器初始化
	if DefaultUploadHandler == nil {
		InitDefaultUploadHandler()
	}

	// 处理预检请求
	if r.Method == http.MethodOptions {
		if DefaultUploadHandler.handleCORS(w, r) {
			return
		}
	}

	// 只允许POST请求
	if r.Method != http.MethodPost {
		SendErrorResponse(w, http.StatusMethodNotAllowed, "只允许POST请求")
		return
	}

	// 处理跨域
	if !DefaultUploadHandler.handleCORS(w, r) {
		return
	}

	// 解析多部分表单
	if err := r.ParseMultipartForm(32 << 20); err != nil {
		SendErrorResponse(w, http.StatusBadRequest, "无法解析表单数据: "+err.Error())
		return
	}

	// 获取文件
	file, header, err := r.FormFile("file")
	if err != nil {
		SendErrorResponse(w, http.StatusBadRequest, "无法获取上传文件: "+err.Error())
		return
	}
	defer file.Close()

	// 获取路径参数
	path := r.FormValue("path")

	// 上传视频
	result, err := DefaultUploadHandler.service.UploadVideo(r.Context(), header, path)
	if err != nil {
		var statusCode int
		var errMsg string

		if errors.Is(err, ErrFileTooLarge) {
			statusCode = http.StatusBadRequest
			errMsg = "文件太大"
		} else if errors.Is(err, ErrUnsupportedFileType) {
			statusCode = http.StatusBadRequest
			errMsg = "不支持的文件类型"
		} else {
			statusCode = http.StatusInternalServerError
			errMsg = fmt.Sprintf("上传失败: %s", err.Error())
		}

		SendErrorResponse(w, statusCode, errMsg)
		return
	}

	SendSuccessResponse(w, result)
}

// 初始化
func init() {
	Init()
	InitDefaultUploadHandler()
}
