package wanliniu

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

// Client 万里牛ERP API客户端
type Client struct {
	BaseURL   string
	AppKey    string
	AppSecret string
	ShopNick  string // 万里牛ERP中B2C店铺昵称
	ShopType  int    // 店铺类型，B2C平台：100
	client    *http.Client
}

// NewClient 创建新的万里牛客户端
func NewClient(config *Config) *Client {
	return &Client{
		BaseURL:   config.BaseURL,
		AppKey:    config.AppKey,
		AppSecret: config.AppSecret,
		ShopNick:  config.ShopNick,
		ShopType:  config.ShopType,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// LogisticsTraceRequest 物流轨迹查询请求
type LogisticsTraceRequest struct {
	StartTime  string `json:"start_time"`  // 物流轨迹开始时间
	EndTime    string `json:"end_time"`    // 物流轨迹结束时间
	ExpressNos string `json:"express_nos"` // 快递单号，支持多个，使用,分割
	Page       int    `json:"page"`        // 当前页码
	Limit      int    `json:"limit"`       // 数量
}

// LogisticsTrace 物流轨迹
type LogisticsTrace struct {
	AcceptStation string `json:"accept_station"` // 轨迹站点
	AcceptTime    string `json:"accept_time"`    // 轨迹时间
	Remark        string `json:"remark"`         // 备注
}

// LogisticsTraceData 物流轨迹数据
type LogisticsTraceData struct {
	ExpressNo string           `json:"express_no"` // 快递单号
	Traces    []LogisticsTrace `json:"traces"`     // 轨迹列表
}

// LogisticsTraceResponse 物流轨迹查询响应
type LogisticsTraceResponse struct {
	Code int                  `json:"code"` // 响应代码，0表示成功
	Data []LogisticsTraceData `json:"data"` // 响应结果数据
}

// QueryLogisticsTrace 查询物流轨迹
func (c *Client) QueryLogisticsTrace(ctx context.Context, req *LogisticsTraceRequest) (*LogisticsTraceResponse, error) {
	// 构建请求参数
	params := make(map[string]string)
	params["start_time"] = req.StartTime
	params["end_time"] = req.EndTime
	params["express_nos"] = req.ExpressNos
	params["page"] = strconv.Itoa(req.Page)
	params["limit"] = strconv.Itoa(req.Limit)

	// 发送请求
	body, err := c.Request(ctx, "/erp/logistic/trace/list", params)
	if err != nil {
		return nil, fmt.Errorf("查询物流轨迹失败: %w", err)
	}

	// 解析响应
	var response LogisticsTraceResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析物流轨迹查询响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 物流轨迹查询业务失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("查询物流轨迹业务失败: code=%d", response.Code)
	}

	logx.Infof("[WanLiNiu] 物流轨迹查询成功")
	return &response, nil
}

// GetDefaultConfig 获取默认配置（从环境变量或配置文件读取）
func GetDefaultConfig() *Config {
	return &Config{
		BaseURL:   "https://open-api.hupun.com/api", // 万里牛开放平台地址
		AppKey:    "",                               // 需要从配置中获取
		AppSecret: "",                               // 需要从配置中获取
	}
}

// GetConfig 获取客户端配置
func (c *Client) GetConfig() Config {
	return Config{
		BaseURL:   c.BaseURL,
		AppKey:    c.AppKey,
		AppSecret: c.AppSecret,
		ShopNick:  c.ShopNick,
		ShopType:  c.ShopType,
	}
}

// Request 发送API请求
func (c *Client) Request(ctx context.Context, path string, params map[string]string) ([]byte, error) {
	// 打印原始请求参数
	logx.Infof("[WanLiNiu] ===== 开始万里牛API请求 =====")
	logx.Infof("[WanLiNiu] 请求路径: %s", path)
	logx.Infof("[WanLiNiu] 基础URL: %s", c.BaseURL)
	logx.Infof("[WanLiNiu] AppKey: %s", c.AppKey)
	logx.Infof("[WanLiNiu] AppSecret: %s***", c.AppSecret[:4]) // 只显示前4位
	logx.Infof("[WanLiNiu] 原始业务参数: %+v", params)

	// 对参数进行签名
	signedParams := SignParameters(params, c.AppKey, c.AppSecret)

	// 打印签名后的完整参数
	logx.Infof("[WanLiNiu] 签名后完整参数: %+v", signedParams)

	// 构建请求URL
	fullURL := c.BaseURL + path
	logx.Infof("[WanLiNiu] 完整请求URL: %s", fullURL)

	// 构建表单数据
	formData := url.Values{}
	for k, v := range signedParams {
		formData.Set(k, v)
	}

	// 打印表单数据
	logx.Infof("[WanLiNiu] 表单数据: %s", formData.Encode())

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", fullURL, strings.NewReader(formData.Encode()))
	if err != nil {
		logx.Errorf("[WanLiNiu] 创建请求失败: %v", err)
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	logx.Infof("[WanLiNiu] 请求头: Content-Type=application/x-www-form-urlencoded")

	// 发送请求
	startTime := time.Now()
	resp, err := c.client.Do(req)
	requestDuration := time.Since(startTime)

	if err != nil {
		logx.Errorf("[WanLiNiu] 请求失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logx.Errorf("[WanLiNiu] 读取响应失败: %s, 耗时: %v, 错误: %v", fullURL, requestDuration, err)
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 记录响应信息
	logx.Infof("[WanLiNiu] 收到响应: %s, 状态码: %d, 耗时: %v", fullURL, resp.StatusCode, requestDuration)
	logx.Infof("[WanLiNiu] ===== HTTP响应详情 =====")
	logx.Infof("[WanLiNiu] 响应状态码: %d", resp.StatusCode)
	logx.Infof("[WanLiNiu] 响应头: %+v", resp.Header)
	logx.Infof("[WanLiNiu] 响应体长度: %d bytes", len(body))
	logx.Infof("[WanLiNiu] 响应体内容: %s", string(body))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logx.Errorf("[WanLiNiu] HTTP请求失败: %s, 状态码: %d, 响应: %s", fullURL, resp.StatusCode, string(body))
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return body, nil
}

// QueryGoods 查询商品
func (c *Client) QueryGoods(ctx context.Context, queryParams GoodsQueryParams) (*GoodsListResponse, error) {
	// 构建查询参数
	params := make(map[string]string)

	// 必填参数
	params["page"] = strconv.Itoa(queryParams.Page)
	params["limit"] = strconv.Itoa(queryParams.Limit)

	// 可选参数
	if queryParams.SpecCode != "" {
		params["spec_code"] = queryParams.SpecCode
	}
	if queryParams.ItemCode != "" {
		params["item_code"] = queryParams.ItemCode
	}
	if queryParams.BarCode != "" {
		params["bar_code"] = queryParams.BarCode
	}
	if queryParams.ModifyTime != nil {
		params["modify_time"] = queryParams.ModifyTime.Format("2006-01-02 15:04:05")
	}
	if queryParams.EndTime != nil {
		params["end_time"] = queryParams.EndTime.Format("2006-01-02 15:04:05")
	}
	if queryParams.AllStatus {
		params["all_status"] = "true"
	}
	if queryParams.NeedProperties {
		params["need_properties"] = "true"
	}

	// 发送请求
	body, err := c.Request(ctx, "/erp/goods/spec/open/query/goodswithspeclist", params)
	if err != nil {
		return nil, fmt.Errorf("查询商品失败: %w", err)
	}

	// 解析响应
	var response GoodsListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析商品查询响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 商品查询业务失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("查询商品业务失败: code=%d", response.Code)
	}

	logx.Infof("[WanLiNiu] 查询商品成功，返回 %d 条数据", len(response.Data))
	return &response, nil
}

// TestConnection 测试连接
func (c *Client) TestConnection(ctx context.Context) error {
	// 使用商品查询接口测试连接，查询最近修改的商品
	// 设置一个较早的时间作为修改时间参数，确保能查到数据
	modifyTime := time.Now().AddDate(0, 0, -30) // 30天前
	params := GoodsQueryParams{
		Page:       1,
		Limit:      1,
		ModifyTime: &modifyTime, // 提供modify_time参数以满足API要求
		AllStatus:  true,        // 查询所有状态的商品
	}

	_, err := c.QueryGoods(ctx, params)
	if err != nil {
		return fmt.Errorf("测试连接失败: %w", err)
	}

	logx.Info("[WanLiNiu] 连接测试成功")
	return nil
}

// ===================== 类目推送接口 =====================

// PushCategories 推送类目到万里牛ERP
func (c *Client) PushCategories(ctx context.Context, request CategoryPushRequest) (*BaseResponse, error) {
	// 构建请求参数
	params := make(map[string]string)

	// 将Categories转为JSON字符串
	categoriesJSON, err := json.Marshal(request.Categories)
	if err != nil {
		return nil, fmt.Errorf("序列化类目数据失败: %w", err)
	}
	params["categories"] = string(categoriesJSON)

	// 发送请求
	body, err := c.Request(ctx, "/erp/b2c/categories/open", params)
	if err != nil {
		return nil, fmt.Errorf("推送类目失败: %w", err)
	}

	// 解析响应
	var response BaseResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析类目推送响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 类目推送业务失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("类目推送业务失败: code=%d", response.Code)
	}

	logx.Infof("[WanLiNiu] 类目推送成功，推送 %d 个类目", len(request.Categories))
	return &response, nil
}

// ===================== 订单推送接口 =====================

// PushTrades 推送订单到万里牛ERP
func (c *Client) PushTrades(ctx context.Context, request TradesPushRequest) (*TradesPushResponse, error) {
	// 构建请求参数
	params := make(map[string]string)

	// 将Trades转为JSON字符串
	tradesJSON, err := json.Marshal(request.Trades)
	if err != nil {
		return nil, fmt.Errorf("序列化订单数据失败: %w", err)
	}
	params["trades"] = string(tradesJSON)

	// 打印调试信息
	logx.Infof("[WanLiNiu] 订单推送 JSON 数据: %s", string(tradesJSON))

	// 发送请求
	body, err := c.Request(ctx, "/erp/b2c/trades/open", params)
	if err != nil {
		return nil, fmt.Errorf("推送订单失败: %w", err)
	}

	// 解析响应
	var response TradesPushResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析订单推送响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查HTTP层状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 订单推送HTTP失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("订单推送HTTP失败: code=%d", response.Code)
	}

	// 检查业务层状态码
	if !response.Data.Success {
		logx.Errorf("[WanLiNiu] 订单推送业务失败: success=%v, error_code=%s, error_msg=%s",
			response.Data.Success, response.Data.ErrorCode, response.Data.ErrorMsg)
		return nil, fmt.Errorf("订单推送业务失败: %s (错误代码: %s)", response.Data.ErrorMsg, response.Data.ErrorCode)
	}

	logx.Infof("[WanLiNiu] 订单推送成功，推送 %d 个订单", len(request.Trades))
	return &response, nil
}

// ===================== 库存查询接口 =====================

// QueryInventoryBatch 批量查询ERP库存
func (c *Client) QueryInventoryBatch(ctx context.Context, request InventoryBatchRequest) (*InventoryResponse, error) {
	// 构建查询参数
	params := make(map[string]string)
	params["page"] = strconv.Itoa(request.Page)
	params["limit"] = strconv.Itoa(request.Limit)

	if request.Start != nil {
		params["start"] = request.Start.Format("2006-01-02 15:04:05")
	}
	if request.End != nil {
		params["end"] = request.End.Format("2006-01-02 15:04:05")
	}
	if request.StorageCode != "" {
		params["storage_code"] = request.StorageCode
	}

	// 发送请求
	body, err := c.Request(ctx, "/erp/b2c/inventories/erp", params)
	if err != nil {
		return nil, fmt.Errorf("批量查询库存失败: %w", err)
	}

	// 解析响应
	var response InventoryResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析批量库存查询响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 批量库存查询业务失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("批量库存查询业务失败: code=%d", response.Code)
	}

	logx.Infof("[WanLiNiu] 批量库存查询成功")
	return &response, nil
}

// QueryInventorySingle 查询单个商品ERP库存
func (c *Client) QueryInventorySingle(ctx context.Context, request InventorySingleRequest) (*InventoryResponse, error) {
	// 构建查询参数
	params := make(map[string]string)
	params["shop_type"] = strconv.Itoa(request.ShopType)
	params["shop_nick"] = request.ShopNick
	params["item_id"] = request.ItemID

	if request.SkuID != "" {
		params["sku_id"] = request.SkuID
	}
	if request.StorageCode != "" {
		params["storage_code"] = request.StorageCode
	}

	// 发送请求
	body, err := c.Request(ctx, "/erp/b2c/inventories/erp/single", params)
	if err != nil {
		return nil, fmt.Errorf("单笔库存查询失败: %w", err)
	}

	// 解析响应
	var response InventoryResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析单笔库存查询响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 单笔库存查询业务失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("单笔库存查询业务失败: code=%d", response.Code)
	}

	logx.Infof("[WanLiNiu] 单笔库存查询成功，商品ID: %s", request.ItemID)
	return &response, nil
}

// ===================== 订单状态查询接口 =====================

// QueryTradeStatus 查询订单发货状态
func (c *Client) QueryTradeStatus(ctx context.Context, request TradeStatusRequest) (*BaseResponse, error) {
	// 构建请求参数
	params := map[string]string{
		"shop_type": strconv.Itoa(request.ShopType),
		"shop_nick": request.ShopNick,
		"trade_ids": request.TradeIDs,
	}

	logx.Infof("[WanLiNiu] 开始查询订单发货状态: 店铺=%s, 类型=%d, 订单数量=%d",
		request.ShopNick, request.ShopType, len(strings.Split(request.TradeIDs, ",")))
	logx.Infof("[WanLiNiu] 查询参数: %+v", params)

	// 调用API
	body, err := c.Request(ctx, "/erp/b2c/trades/erp/status", params)
	if err != nil {
		logx.Errorf("[WanLiNiu] 查询订单发货状态失败: %v", err)
		return nil, fmt.Errorf("查询订单发货状态失败: %w", err)
	}

	// 解析响应
	var response BaseResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析订单发货状态查询响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 订单发货状态查询业务失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("订单发货状态查询业务失败: code=%d", response.Code)
	}

	logx.Infof("[WanLiNiu] 查询订单发货状态成功，查询的订单: %s", request.TradeIDs)
	return &response, nil
}

// ===================== 售后单推送接口 =====================

// PushRefund 推送售后单到万里牛ERP
func (c *Client) PushRefund(ctx context.Context, request RefundPushRequest) (*BaseResponse, error) {
	// 构建请求参数
	params := make(map[string]string)

	// 将Refund转为JSON字符串
	refundJSON, err := json.Marshal(request.Refund)
	if err != nil {
		return nil, fmt.Errorf("序列化售后单数据失败: %w", err)
	}
	params["refund"] = string(refundJSON)

	// 打印调试信息
	logx.Infof("[WanLiNiu] 售后单推送 JSON 数据: %s", string(refundJSON))

	// 发送请求
	body, err := c.Request(ctx, "/erp/b2c/refunds/open", params)
	if err != nil {
		return nil, fmt.Errorf("推送售后单失败: %w", err)
	}

	// 解析响应
	var response BaseResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析售后单推送响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 售后单推送业务失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("售后单推送业务失败: code=%d", response.Code)
	}

	logx.Infof("[WanLiNiu] 售后单推送成功，售后单号: %s", request.Refund.RefundID)
	return &response, nil
}

// ===================== 商品分类查询接口 =====================

// QueryCategories 查询商品分类
func (c *Client) QueryCategories(ctx context.Context, queryParams CategoryQueryParams) (*CategoryListResponse, error) {
	// 构建查询参数
	params := make(map[string]string)

	// 必填参数
	params["page"] = strconv.Itoa(queryParams.Page)
	params["limit"] = strconv.Itoa(queryParams.Limit)

	// 发送请求
	body, err := c.Request(ctx, "/erp/goods/catagorypage/query/v2", params)
	if err != nil {
		return nil, fmt.Errorf("查询商品分类失败: %w", err)
	}

	// 解析响应
	var response CategoryListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		logx.Errorf("[WanLiNiu] 解析商品分类查询响应失败: %v, 响应内容: %s", err, string(body))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务状态码
	if response.Code != 0 {
		logx.Errorf("[WanLiNiu] 商品分类查询业务失败: code=%d, 响应: %s", response.Code, string(body))
		return nil, fmt.Errorf("查询商品分类业务失败: code=%d", response.Code)
	}

	logx.Infof("[WanLiNiu] 查询商品分类成功，返回 %d 条数据", len(response.Data))
	return &response, nil
}

// QueryInventoryV2 查询库存V2（分仓返回库存）
func (c *Client) QueryInventoryV2(ctx context.Context, request InventoryQueryV2Request) (*InventoryQueryV2Response, error) {
	// 验证必要参数
	if request.PageNo <= 0 {
		request.PageNo = 1
	}
	if request.PageSize <= 0 || request.PageSize > 200 {
		request.PageSize = 10
	}

	// 检查必要参数：规格编码、修改时间、货号和条码不能都为空
	if request.SkuCode == "" && request.ModifyTime == nil && request.ArticleNumber == "" && request.BarCode == "" {
		return nil, fmt.Errorf("规格编码、修改时间、货号和条码不能都为空")
	}

	// 构建请求参数 - 使用string map与老接口保持一致
	params := make(map[string]string)
	params["page_no"] = strconv.Itoa(request.PageNo)
	params["page_size"] = strconv.Itoa(request.PageSize)

	// 添加可选参数
	if request.ArticleNumber != "" {
		params["article_number"] = request.ArticleNumber
	}
	if request.BarCode != "" {
		params["bar_code"] = request.BarCode
	}
	if request.ModifyTime != nil {
		params["modify_time"] = *request.ModifyTime
	}
	if request.ModifyTimeEnd != nil {
		params["modify_time_end"] = *request.ModifyTimeEnd
	}
	if request.SkuCode != "" {
		params["sku_code"] = request.SkuCode
	}
	if request.Storage != "" {
		params["storage"] = request.Storage
	}

	// 使用老的Request方法来保持签名一致
	response, err := c.Request(ctx, "/erp/open/inventory/items/get/by/modifytimev2", params)
	if err != nil {
		return nil, fmt.Errorf("查询库存V2失败: %w", err)
	}

	logx.Infof("[WanLiNiu] 响应内容: %s", string(response))

	// 解析响应
	var result InventoryQueryV2Response
	if err := json.Unmarshal(response, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务响应码
	if result.Code != 0 {
		return nil, fmt.Errorf("万里牛库存查询V2业务失败: code=%d", result.Code)
	}

	logx.Infof("[WanLiNiu] 库存查询V2成功")
	return &result, nil
}
