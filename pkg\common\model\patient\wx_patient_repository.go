package patient

import (
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// WxPatientRepository 微信患者仓库接口
type WxPatientRepository interface {
	Create(patient *WxPatient) error
	Update(patient *WxPatient) error
	Delete(id uint) error
	FindByID(id uint) (*WxPatient, error)
	FindByUserID(userID uint) (*WxPatient, error)
	List(page, size int, query map[string]interface{}) ([]*WxPatient, int64, error)
	FindPatientWithUser(id uint) (*WxPatient, *user.WxUser, error)
	UpdateStatus(id uint, status int) error
}

// wxPatientRepository 微信患者仓库实现
type wxPatientRepository struct {
	db *gorm.DB // 保留db字段以兼容接口，但不在方法中使用
}

// NewWxPatientRepository 创建微信患者仓库
func NewWxPatientRepository(db *gorm.DB) WxPatientRepository {
	return &wxPatientRepository{
		db: db,
	}
}

// Create 创建微信患者
func (r *wxPatientRepository) Create(patient *WxPatient) error {
	return mysql.Master().Create(patient).Error
}

// Update 更新微信患者
func (r *wxPatientRepository) Update(patient *WxPatient) error {
	return mysql.Master().Save(patient).Error
}

// Delete 删除微信患者
func (r *wxPatientRepository) Delete(id uint) error {
	return mysql.Master().Delete(&WxPatient{}, id).Error
}

// FindByID 根据ID查找微信患者
func (r *wxPatientRepository) FindByID(id uint) (*WxPatient, error) {
	var patient WxPatient
	err := mysql.Slave().Where("patient_id = ?", id).First(&patient).Error
	if err != nil {
		return nil, err
	}
	return &patient, nil
}

// FindByUserID 根据用户ID查找微信患者
func (r *wxPatientRepository) FindByUserID(userID uint) (*WxPatient, error) {
	var patient WxPatient
	err := mysql.Slave().Where("user_id = ?", userID).First(&patient).Error
	if err != nil {
		return nil, err
	}
	return &patient, nil
}

// List 获取微信患者列表
func (r *wxPatientRepository) List(page, size int, query map[string]interface{}) ([]*WxPatient, int64, error) {
	var patients []*WxPatient
	var total int64

	db := mysql.Slave()

	// 添加查询条件
	if query != nil {
		for key, value := range query {
			if value != nil && value != "" {
				db = db.Where(key, value)
			}
		}
	}

	// 获取总数
	if err := db.Model(&WxPatient{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("patient_id DESC").Offset(offset).Limit(size).Find(&patients).Error; err != nil {
		return nil, 0, err
	}

	return patients, total, nil
}

// FindPatientWithUser 查找患者及其关联的用户信息
func (r *wxPatientRepository) FindPatientWithUser(id uint) (*WxPatient, *user.WxUser, error) {
	// 查询患者信息
	patient, err := r.FindByID(id)
	if err != nil {
		return nil, nil, err
	}

	// 查询患者关联的用户信息
	var wxUser user.WxUser
	err = mysql.Slave().Where("user_id = ?", patient.UserID).First(&wxUser).Error
	if err != nil {
		return patient, nil, err
	}

	return patient, &wxUser, nil
}

// UpdateStatus 更新微信患者状态
func (r *wxPatientRepository) UpdateStatus(id uint, status int) error {
	return mysql.Master().Model(&WxPatient{}).Where("patient_id = ?", id).Update("status", status).Error
}
