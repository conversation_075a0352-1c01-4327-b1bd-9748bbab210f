package model

import (
	"time"
)

// Create 创建短信验证码
func (r *smsCodeRepository) Create(smsCode *SmsCode) error {
	return r.db.Create(smsCode).Error
}

// FindLatestByMobile 根据手机号查找最新的短信验证码
func (r *smsCodeRepository) FindLatestByMobile(mobile string, smsType int) (*SmsCode, error) {
	var smsCode SmsCode
	err := r.db.Where("mobile = ? AND type = ? AND used = ? AND expire_at > ?",
		mobile, smsType, false, time.Now()).
		Order("id DESC").First(&smsCode).Error
	if err != nil {
		return nil, err
	}
	return &smsCode, nil
}

// MarkAsUsed 标记验证码为已使用
func (r *smsCodeRepository) MarkAsUsed(id uint) error {
	return r.db.Model(&SmsCode{}).Where("id = ?", id).Update("used", true).Error
}

// DeleteExpired 删除过期的验证码
func (r *smsCodeRepository) DeleteExpired() error {
	return r.db.Where("expire_at < ?", time.Now()).Delete(&SmsCode{}).Error
}
