package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"yekaitai/internal/modules/content/model"
	"yekaitai/pkg/hmac"
	"yekaitai/pkg/infra/mysql"
	qrcodeService "yekaitai/wx_internal/modules/qrcode-management/service"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// ActivityQRCodeService 活动二维码服务
type ActivityQRCodeService struct{}

// NewActivityQRCodeService 创建活动二维码服务
func NewActivityQRCodeService() *ActivityQRCodeService {
	return &ActivityQRCodeService{}
}

// GenerateActivityQRCode 生成活动二维码
func (s *ActivityQRCodeService) GenerateActivityQRCode(ctx context.Context, orderID uint) (string, error) {
	// 查询订单信息
	var order model.ContentSignUpOrder
	if err := mysql.Slave().Where("id = ?", orderID).First(&order).Error; err != nil {
		logx.Errorf("查询活动报名订单失败: %v", err)
		return "", fmt.Errorf("查询活动报名订单失败: %v", err)
	}

	// 如果订单已经有二维码，则直接返回
	if order.QRCodeURL != "" {
		return order.QRCodeURL, nil
	}

	// 检查是否已有核销码，如果没有则生成新的
	verificationCode := order.VerificationCode
	if verificationCode == "" {
		verificationCode = fmt.Sprintf("ACT%d%d", orderID, time.Now().Unix())
		// 更新订单核销码
		if err := mysql.Master().Model(&order).Update("verification_code", verificationCode).Error; err != nil {
			logx.Errorf("更新订单核销码失败: %v", err)
			return "", fmt.Errorf("更新订单核销码失败: %v", err)
		}
	}

	// 调用通用二维码生成服务
	qrCodeURL, err := s.generateQRCodeWithData(ctx, orderID, order.OrderNo, verificationCode)
	if err != nil {
		logx.Errorf("生成二维码失败: %v", err)
		return "", fmt.Errorf("生成二维码失败: %v", err)
	}

	// 更新订单二维码URL
	if err := mysql.Master().Model(&order).Update("qr_code_url", qrCodeURL).Error; err != nil {
		logx.Errorf("更新订单二维码URL失败: %v", err)
		return "", fmt.Errorf("更新订单二维码URL失败: %v", err)
	}

	logx.Infof("生成活动二维码成功: orderID=%d, orderNo=%s, verificationCode=%s",
		orderID, order.OrderNo, verificationCode)
	return qrCodeURL, nil
}

// GetActivityQRCode 获取活动二维码
func (s *ActivityQRCodeService) GetActivityQRCode(ctx context.Context, orderID uint) (string, error) {
	// 查询订单信息
	var order model.ContentSignUpOrder
	if err := mysql.Slave().Where("id = ?", orderID).First(&order).Error; err != nil {
		logx.Errorf("查询活动报名订单失败: %v", err)
		return "", fmt.Errorf("查询活动报名订单失败: %v", err)
	}

	// 如果订单还没有二维码，则生成二维码
	if order.QRCodeURL == "" {
		return s.GenerateActivityQRCode(ctx, orderID)
	}

	return order.QRCodeURL, nil
}

// generateQRCodeWithData 生成包含数据的二维码
func (s *ActivityQRCodeService) generateQRCodeWithData(ctx context.Context, orderID uint, orderNo, verificationCode string) (string, error) {
	// 构建二维码数据 - 仅包含必要信息
	data := map[string]interface{}{
		"type":              "activity",                                   // 标识为活动类型
		"order_id":          orderID,                                      // 订单ID
		"order_no":          orderNo,                                      // 订单号
		"verification_code": verificationCode,                             // 核销码
		"timestamp":         strconv.FormatInt(time.Now().UnixNano(), 10), // 时间戳
	}

	// 生成签名（基于order_no + verification_code）
	signatureData := fmt.Sprintf("%s%s", orderNo, verificationCode)
	sign := hmac.GenerateHMAC(signatureData)
	data["sign"] = sign

	// 生成并上传二维码
	return qrcodeService.GenerateQRCodeWithData(ctx, data)
}

// VerifyActivityCode 验证活动核销码
func (s *ActivityQRCodeService) VerifyActivityCode(ctx context.Context, orderNo, verificationCode string, verifierID uint, verifierName string) error {
	// 查询订单详情
	var order model.ContentSignUpOrder
	err := mysql.Slave().Where("order_no = ? AND verification_code = ?", orderNo, verificationCode).First(&order).Error
	if err != nil {
		logx.Errorf("查询活动报名订单失败: %v", err)
		return fmt.Errorf("订单不存在或核销码错误")
	}

	// 检查订单状态
	if order.Status != 1 {
		return fmt.Errorf("订单状态不允许核销")
	}

	// 检查是否已经核销
	if order.VerificationTime != nil {
		return fmt.Errorf("订单已经核销")
	}

	// 验证核销员身份和权限
	err = s.validateRedeemer(ctx, verifierID, order.ContentID)
	if err != nil {
		logx.Errorf("核销员验证失败: %v", err)
		return err
	}

	// 更新核销信息
	now := time.Now()
	updates := map[string]interface{}{
		"status":            4,            // 已核销
		"verification_time": &now,         // 核销时间
		"verifier_id":       verifierID,   // 核销人ID
		"verifier_name":     verifierName, // 核销人姓名
		"updated_at":        now,          // 更新时间
	}

	if err := mysql.Master().Model(&order).Updates(updates).Error; err != nil {
		logx.Errorf("更新核销信息失败: %v", err)
		return fmt.Errorf("核销失败")
	}

	logx.Infof("活动核销成功: orderNo=%s, verificationCode=%s, verifierID=%d, verifierName=%s",
		orderNo, verificationCode, verifierID, verifierName)
	return nil
}

// ValidateQRCodeData 验证二维码数据
func (s *ActivityQRCodeService) ValidateQRCodeData(data map[string]interface{}) error {
	// 检查必要字段
	orderNo, ok := data["order_no"].(string)
	if !ok || orderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}

	verificationCode, ok := data["verification_code"].(string)
	if !ok || verificationCode == "" {
		return fmt.Errorf("核销码不能为空")
	}

	sign, ok := data["sign"].(string)
	if !ok || sign == "" {
		return fmt.Errorf("签名不能为空")
	}

	// 验证签名
	expectedSign := hmac.GenerateHMAC(orderNo + verificationCode)
	if sign != expectedSign {
		return fmt.Errorf("签名验证失败")
	}

	return nil
}

// GetActivityOrderByQRCode 根据二维码数据获取活动订单
func (s *ActivityQRCodeService) GetActivityOrderByQRCode(ctx context.Context, data map[string]interface{}) (*model.ContentSignUpOrder, error) {
	// 验证二维码数据
	if err := s.ValidateQRCodeData(data); err != nil {
		return nil, err
	}

	orderNo := data["order_no"].(string)
	verificationCode := data["verification_code"].(string)

	// 查询订单
	var order model.ContentSignUpOrder
	err := mysql.Slave().Where("order_no = ? AND verification_code = ?", orderNo, verificationCode).First(&order).Error
	if err != nil {
		logx.Errorf("查询活动报名订单失败: %v", err)
		return nil, fmt.Errorf("订单不存在或核销码错误")
	}

	return &order, nil
}

// GenerateVerificationCode 生成核销码
func (s *ActivityQRCodeService) GenerateVerificationCode(orderID uint) string {
	return fmt.Sprintf("ACT%d%d", orderID, time.Now().Unix())
}

// IsValidVerificationCode 验证核销码格式
func (s *ActivityQRCodeService) IsValidVerificationCode(code string) bool {
	if len(code) < 10 {
		return false
	}

	// 检查是否以ACT开头
	if len(code) >= 3 && code[:3] == "ACT" {
		return true
	}

	return false
}

// validateRedeemer 验证核销员身份和权限
func (s *ActivityQRCodeService) validateRedeemer(ctx context.Context, redeemerID uint, contentID uint) error {
	// 查询核销员信息
	var redeemer struct {
		RedeemerID uint `gorm:"column:redeemer_id"`
		UserID     uint `gorm:"column:user_id"`
		StoreID    uint `gorm:"column:store_id"`
		Status     int  `gorm:"column:status"`
	}

	err := mysql.Slave().Table("wx_redeemer").
		Where("user_id = ? AND deleted_at IS NULL", redeemerID).
		First(&redeemer).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("核销员身份不存在")
		}
		return fmt.Errorf("查询核销员信息失败: %w", err)
	}

	// 检查核销员状态
	if redeemer.Status != 1 {
		return fmt.Errorf("核销员已被禁用")
	}

	// 查询活动绑定的门店
	var storeIDs []uint
	err = mysql.Slave().Table("t_content_store_relations").
		Where("content_id = ?", contentID).
		Pluck("store_id", &storeIDs).Error
	if err != nil {
		return fmt.Errorf("查询活动门店信息失败: %w", err)
	}

	// 检查核销员是否有权限核销该活动
	hasPermission := false
	for _, storeID := range storeIDs {
		if storeID == redeemer.StoreID {
			hasPermission = true
			break
		}
	}

	if !hasPermission {
		return fmt.Errorf("核销员无权限核销该活动")
	}

	return nil
}
