package routes

import (
	"net/http"
	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/member/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterMemberRoutes 注册会员管理相关路由
func RegisterMemberRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建会员管理处理器
	memberHandler := handler.NewMemberHandler(serverCtx)

	// 创建管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 添加会员管理路由
	server.AddRoutes(
		[]rest.Route{
			// 会员列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/members",
				Handler: adminAuthWrapper(memberHandler.ListMembers),
			},
			// 会员详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/members/:id",
				Handler: admin<PERSON>uthWrapper(memberHandler.GetMember),
			},
			// 更新会员状态（启用/禁用）
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/members/:id/status",
				Handler: adminAuthWrapper(memberHandler.UpdateMemberStatus),
			},
			// 更新会员标签
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/members/:id/tags",
				Handler: adminAuthWrapper(memberHandler.UpdateMemberTags),
			},
		},
	)
}
