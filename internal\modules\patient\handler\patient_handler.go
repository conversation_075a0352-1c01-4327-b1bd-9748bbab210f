package handler

import (
	"fmt"
	"net/http"
	"time"

	"yekaitai/internal/models/enum"
	"yekaitai/internal/svc"
	"yekaitai/internal/types"

	doctorModel "yekaitai/pkg/common/model/doctor"
	patientModel "yekaitai/pkg/common/model/patient"
	userModel "yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"gorm.io/gorm"
)

// 患者列表请求
type PatientListRequest struct {
	types.PageRequest
	Query     string `form:"query,optional"`     // 搜索关键词（可以是姓名或手机号）
	Name      string `form:"name,optional"`      // 按姓名筛选
	Mobile    string `form:"mobile,optional"`    // 按手机号筛选
	IdCard    string `form:"idCard,optional"`    // 按身份证号筛选
	UserID    uint   `form:"user_id,optional"`   // 用户ID，查询某个用户的就诊人列表
	StartDate string `form:"startDate,optional"` // 创建开始日期
	EndDate   string `form:"endDate,optional"`   // 创建结束日期
}

// 患者详情请求
type PatientDetailRequest struct {
	PatientID uint `path:"patientId"` // 患者ID
}

// 更新患者状态请求
type UpdatePatientStatusRequest struct {
	PatientID uint `path:"patientId"` // 患者ID
	Status    int  `json:"status"`    // 状态：1-正常，0-禁用
}

// 更新患者请求
type UpdatePatientRequest struct {
	PatientID            uint   `path:"patientId"`                     // 患者ID
	Name                 string `json:"name,optional"`                 // 姓名
	Gender               int    `json:"gender,optional"`               // 性别：0-未知，1-男，2-女
	BirthDate            string `json:"birthDate,optional"`            // 出生年月日
	IdCard               string `json:"idCard,optional"`               // 身份证号
	Ethnicity            string `json:"ethnicity,optional"`            // 民族
	RelationshipWithUser string `json:"relationshipWithUser,optional"` // 与本人关系
	Mobile               string `json:"mobile,optional"`               // 手机号码
	MaritalStatus        int    `json:"maritalStatus,optional"`        // 婚姻状态：0-未知，1-已婚，2-未婚
	MedicalHistory       string `json:"medicalHistory,optional"`       // 病史
	Allergies            string `json:"allergies,optional"`            // 过敏史
	Status               int    `json:"status,optional"`               // 状态：1-正常，0-禁用
	// 新增平台患者ID字段
	HangzhouHisID   string `json:"hangzhouHisId,optional"`   // 杭州HIS患者ID
	AbcyunPatientID string `json:"abcyunPatientId,optional"` // ABC云患者ID
}

// 创建患者请求
type CreatePatientRequest struct {
	UserID               uint   `json:"userId"`                        // 关联的微信用户ID
	Name                 string `json:"name"`                          // 姓名
	Gender               int    `json:"gender"`                        // 性别：0-未知，1-男，2-女
	BirthDate            string `json:"birthDate,optional"`            // 出生年月日
	IdCard               string `json:"idCard,optional"`               // 身份证号
	Ethnicity            string `json:"ethnicity,optional"`            // 民族
	RelationshipWithUser string `json:"relationshipWithUser,optional"` // 与本人关系
	Mobile               string `json:"mobile,optional"`               // 手机号码
	MaritalStatus        int    `json:"maritalStatus,optional"`        // 婚姻状态：0-未知，1-已婚，2-未婚
	MedicalHistory       string `json:"medicalHistory,optional"`       // 病史
	Allergies            string `json:"allergies,optional"`            // 过敏史
	Status               int    `json:"status,optional"`               // 状态：1-正常，0-禁用
	// 新增平台患者ID字段
	HangzhouHisID   string `json:"hangzhouHisId,optional"`   // 杭州HIS患者ID
	AbcyunPatientID string `json:"abcyunPatientId,optional"` // ABC云患者ID
}

// 删除患者请求
type DeletePatientRequest struct {
	PatientID uint `path:"patientId"` // 患者ID
}

// 获取患者就诊记录请求
type GetPatientMedicalRecordsRequest struct {
	PatientID uint   `path:"patientId"`          // 患者ID
	Page      int    `form:"page,default=1"`     // 页码
	Size      int    `form:"size,default=10"`    // 每页记录数
	StartDate string `form:"startDate,optional"` // 开始日期
	EndDate   string `form:"endDate,optional"`   // 结束日期
}

// 获取患者处方记录请求
type GetPatientPrescriptionsRequest struct {
	PatientID uint   `path:"patientId"`          // 患者ID
	Page      int    `form:"page,default=1"`     // 页码
	Size      int    `form:"size,default=10"`    // 每页记录数
	StartDate string `form:"startDate,optional"` // 开始日期
	EndDate   string `form:"endDate,optional"`   // 结束日期
}

// 添加患者健康档案请求
type AddPatientHealthRecordRequest struct {
	PatientID   uint     `path:"patientId"`            // 患者ID
	RecordType  string   `json:"recordType"`           // 档案类型
	RecordDate  string   `json:"recordDate"`           // 档案日期
	Content     string   `json:"content"`              // 档案内容
	DoctorID    uint     `json:"doctorId,optional"`    // 关联医生ID
	Attachments []string `json:"attachments,optional"` // 附件列表（图片或文件的URL）
}

// 获取患者预问诊记录列表请求
type GetPatientPreConsultationsRequest struct {
	PatientID uint   `path:"patientId"`          // 患者ID
	Page      int    `form:"page,default=1"`     // 页码
	Size      int    `form:"size,default=10"`    // 每页记录数
	StartDate string `form:"startDate,optional"` // 开始日期
	EndDate   string `form:"endDate,optional"`   // 结束日期
}

// 将PageRequest字段添加到预问诊请求中以兼容分页响应
func (r *GetPatientPreConsultationsRequest) PageRequest() *types.PageRequest {
	return &types.PageRequest{
		Page: r.Page,
		Size: r.Size,
	}
}

// 获取预问诊详情请求
type GetPreConsultationDetailRequest struct {
	PatientID uint   `path:"patientId"` // 患者ID
	SessionID string `path:"sessionId"` // 会话ID
}

// 获取患者报告列表请求
type GetPatientReportsRequest struct {
	PatientID uint   `path:"patientId"`          // 患者ID
	Page      int    `form:"page,default=1"`     // 页码
	Size      int    `form:"size,default=10"`    // 每页记录数
	StartDate string `form:"startDate,optional"` // 开始日期
	EndDate   string `form:"endDate,optional"`   // 结束日期
	Type      string `form:"type,optional"`      // 报告类型
}

// 将PageRequest字段添加到报告请求中以兼容分页响应
func (r *GetPatientReportsRequest) PageRequest() *types.PageRequest {
	return &types.PageRequest{
		Page: r.Page,
		Size: r.Size,
	}
}

// 获取报告详情请求
type GetReportDetailRequest struct {
	PatientID uint `path:"patientId"` // 患者ID
	ReportID  uint `path:"reportId"`  // 报告ID
}

// 获取患者病历详情请求
type GetPatientMedicalRecordDetailRequest struct {
	PatientID uint `path:"patientId"` // 患者ID
	RecordID  uint `path:"recordId"`  // 病历ID
}

// PatientHandler 患者处理器
type PatientHandler struct {
	svcCtx *svc.ServiceContext
}

// NewPatientHandler 创建患者处理器
func NewPatientHandler(svcCtx *svc.ServiceContext) *PatientHandler {
	return &PatientHandler{
		svcCtx: svcCtx,
	}
}

// ListPatients 获取患者列表
func (h *PatientHandler) ListPatients(w http.ResponseWriter, r *http.Request) {
	var req PatientListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取患者列表: page=%d, size=%d, query=%s, name=%s, mobile=%s, idCard=%s, userId=%d, startDate=%s, endDate=%s",
		req.Page, req.Size, req.Query, req.Name, req.Mobile, req.IdCard, req.UserID, req.StartDate, req.EndDate)

	// 构建查询条件
	db := mysql.Slave().Model(&patientModel.WxPatient{})

	// 如果有查询关键词，添加条件
	if req.Query != "" {
		db = db.Where("name LIKE ? OR mobile LIKE ? OR id_card LIKE ?",
			"%"+req.Query+"%", "%"+req.Query+"%", "%"+req.Query+"%")
	}

	// 按姓名筛选
	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}

	// 按手机号筛选
	if req.Mobile != "" {
		db = db.Where("mobile LIKE ?", "%"+req.Mobile+"%")
	}

	// 按身份证号筛选
	if req.IdCard != "" {
		db = db.Where("id_card LIKE ?", "%"+req.IdCard+"%")
	}

	// 按用户ID筛选
	if req.UserID > 0 {
		db = db.Where("user_id = ?", req.UserID)
	}

	// 按创建时间范围筛选
	if req.StartDate != "" && req.EndDate != "" {
		startDate := req.StartDate + " 00:00:00"
		endDate := req.EndDate + " 23:59:59"
		db = db.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	} else if req.StartDate != "" {
		startDate := req.StartDate + " 00:00:00"
		db = db.Where("created_at >= ?", startDate)
	} else if req.EndDate != "" {
		endDate := req.EndDate + " 23:59:59"
		db = db.Where("created_at <= ?", endDate)
	}

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logx.Errorf("统计患者数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取患者列表失败"))
		return
	}

	// 分页查询，默认按创建时间倒序排序
	var patients []patientModel.WxPatient
	offset := (req.Page - 1) * req.Size
	if err := db.Order("created_at DESC").Offset(offset).Limit(req.Size).Find(&patients).Error; err != nil {
		logx.Errorf("查询患者列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取患者列表失败"))
		return
	}

	// 处理患者数据，准备返回结果
	result := make([]map[string]interface{}, len(patients))
	for i, patient := range patients {
		// 转换为Map
		patientMap := map[string]interface{}{
			"patientId":            patient.PatientID,
			"userId":               patient.UserID,
			"name":                 patient.Name,
			"gender":               patient.Gender,
			"birthDate":            patient.BirthDate,
			"idCard":               patient.IdCard,
			"ethnicity":            patient.Ethnicity,
			"relationshipWithUser": patient.RelationshipWithUser,
			"mobile":               patient.Mobile,
			"maritalStatus":        patient.MaritalStatus,
			"medicalHistory":       patient.MedicalHistory,
			"allergies":            patient.Allergies,
			"status":               patient.Status,
			"hangzhouHisId":        patient.HangzhouHisID,
			"abcyunPatientId":      patient.AbcyunPatientID,
			"createdAt":            patient.CreatedAt,
			"updatedAt":            patient.UpdatedAt,
		}

		result[i] = patientMap
	}

	logx.Infof("获取患者列表成功: 共%d条记录", total)

	pageResponse := types.NewPageResponse(result, total, &req.PageRequest)
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取患者列表成功"))
}

// GetPatient 获取患者详情
func (h *PatientHandler) GetPatient(w http.ResponseWriter, r *http.Request) {
	var req PatientDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取患者详情: patientId=%d", req.PatientID)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 获取患者信息
	patient, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取患者详情失败"))
		}
		return
	}

	// 准备返回数据
	result := map[string]interface{}{
		"patientId":            patient.PatientID,
		"userId":               patient.UserID,
		"name":                 patient.Name,
		"gender":               patient.Gender,
		"birthDate":            patient.BirthDate,
		"idCard":               patient.IdCard,
		"ethnicity":            patient.Ethnicity,
		"relationshipWithUser": patient.RelationshipWithUser,
		"mobile":               patient.Mobile,
		"maritalStatus":        patient.MaritalStatus,
		"medicalHistory":       patient.MedicalHistory,
		"allergies":            patient.Allergies,
		"status":               patient.Status,
		"isDefault":            patient.RelationshipWithUser == "本人",
		"createdAt":            patient.CreatedAt,
		"updatedAt":            patient.UpdatedAt,
		// 新增平台患者ID字段
		"hangzhouHisId":   patient.HangzhouHisID,
		"abcyunPatientId": patient.AbcyunPatientID,
	}

	logx.Infof("获取患者详情成功: patientId=%d", req.PatientID)
	httpx.OkJson(w, types.NewSuccessResponse(result, "获取患者详情成功"))
}

// UpdatePatient 更新患者信息
func (h *PatientHandler) UpdatePatient(w http.ResponseWriter, r *http.Request) {
	var req UpdatePatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("更新患者信息: patientId=%d", req.PatientID)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 获取当前患者信息
	patient, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新患者信息失败"))
		}
		return
	}

	// 验证手机号码格式
	if req.Mobile != "" && !utils.IsValidPhoneNumber(req.Mobile) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的手机号码格式"))
		return
	}

	// 验证身份证号码格式
	if req.IdCard != "" && !utils.IsValidIDCard(req.IdCard) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的身份证号码格式"))
		return
	}

	// 验证民族
	if req.Ethnicity != "" {
		if !enum.IsValidEthnicity(req.Ethnicity) {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的民族"))
			return
		}
	}

	// 验证与本人关系
	if req.RelationshipWithUser != "" {
		if !enum.IsValidRelationship(req.RelationshipWithUser) {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的与本人关系"))
			return
		}
	}

	// 更新字段
	if req.Name != "" {
		patient.Name = req.Name
	}
	if req.Gender != 0 {
		patient.Gender = req.Gender
	}
	if req.BirthDate != "" {
		patient.BirthDate = req.BirthDate
	}
	if req.IdCard != "" {
		patient.IdCard = req.IdCard
	}
	if req.Ethnicity != "" {
		patient.Ethnicity = req.Ethnicity
	}
	if req.RelationshipWithUser != "" {
		patient.RelationshipWithUser = req.RelationshipWithUser
	}
	if req.Mobile != "" {
		patient.Mobile = req.Mobile
	}
	if req.MaritalStatus != 0 {
		patient.MaritalStatus = req.MaritalStatus
	}
	if req.MedicalHistory != "" {
		patient.MedicalHistory = req.MedicalHistory
	}
	if req.Allergies != "" {
		patient.Allergies = req.Allergies
	}
	if req.Status != 0 {
		patient.Status = req.Status
	}
	// 更新平台患者ID字段
	if req.HangzhouHisID != "" {
		patient.HangzhouHisID = req.HangzhouHisID
	}
	if req.AbcyunPatientID != "" {
		patient.AbcyunPatientID = req.AbcyunPatientID
	}

	// 更新患者信息
	err = mysql.Master().Save(patient).Error
	if err != nil {
		logx.Errorf("更新患者信息失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新患者信息失败"))
		return
	}

	// 记录操作日志
	adminID := utils.GetAdminIDFromContext(r.Context())
	h.logAdminOperation(r, adminID, "患者管理", "更新", uint(patient.PatientID), "患者", fmt.Sprintf("更新患者信息: %s", patient.Name))

	logx.Infof("更新患者信息成功: patientId=%d", req.PatientID)
	httpx.OkJson(w, types.NewSuccessResponse(nil, "更新患者信息成功"))
}

// UpdatePatientStatus 更新患者状态
func (h *PatientHandler) UpdatePatientStatus(w http.ResponseWriter, r *http.Request) {
	var req UpdatePatientStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("更新患者状态: patientId=%d, status=%d", req.PatientID, req.Status)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 获取当前患者信息
	patient, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新患者状态失败"))
		}
		return
	}

	// 更新状态
	patient.Status = req.Status

	// 更新患者信息
	err = mysql.Master().Save(patient).Error
	if err != nil {
		logx.Errorf("更新患者状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "更新患者状态失败"))
		return
	}

	// 记录操作日志
	adminID := utils.GetAdminIDFromContext(r.Context())
	statusText := "禁用"
	if req.Status == 1 {
		statusText = "启用"
	}
	h.logAdminOperation(r, adminID, "患者管理", statusText, uint(patient.PatientID), "患者", fmt.Sprintf("%s患者: %s", statusText, patient.Name))

	logx.Infof("更新患者状态成功: patientId=%d, status=%d", req.PatientID, req.Status)
	httpx.OkJson(w, types.NewSuccessResponse(nil, "更新患者状态成功"))
}

// CreatePatient 创建患者
func (h *PatientHandler) CreatePatient(w http.ResponseWriter, r *http.Request) {
	var req CreatePatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("创建患者: 姓名=%s, 手机号=%s", req.Name, req.Mobile)

	// 验证必填字段
	if req.UserID == 0 || req.Name == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "用户ID和姓名不能为空"))
		return
	}

	// 验证手机号码格式
	if req.Mobile != "" && !utils.IsValidPhoneNumber(req.Mobile) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的手机号码格式"))
		return
	}

	// 验证身份证号码格式
	if req.IdCard != "" && !utils.IsValidIDCard(req.IdCard) {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的身份证号码格式"))
		return
	}

	// 验证民族
	if req.Ethnicity != "" {
		if !enum.IsValidEthnicity(req.Ethnicity) {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的民族"))
			return
		}
	} else {
		// 设置默认民族为汉族
		req.Ethnicity = enum.GetDefaultEthnicity()
	}

	// 验证与本人关系
	if req.RelationshipWithUser != "" {
		if !enum.IsValidRelationship(req.RelationshipWithUser) {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的与本人关系"))
			return
		}
	} else {
		// 设置默认关系为本人
		req.RelationshipWithUser = enum.GetDefaultRelationship()
	}

	// 验证用户ID是否存在
	userRepo := userModel.NewWxUserRepository(mysql.Slave())
	_, err := userRepo.FindByID(req.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "关联的微信用户不存在"))
		} else {
			logx.Errorf("查询用户信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建患者失败"))
		}
		return
	}

	// 创建患者对象
	patient := &patientModel.WxPatient{
		UserID:               req.UserID,
		Name:                 req.Name,
		Gender:               req.Gender,
		BirthDate:            req.BirthDate,
		IdCard:               req.IdCard,
		Ethnicity:            req.Ethnicity,
		RelationshipWithUser: req.RelationshipWithUser,
		Mobile:               req.Mobile,
		MaritalStatus:        req.MaritalStatus,
		MedicalHistory:       req.MedicalHistory,
		Allergies:            req.Allergies,
		Status:               1, // 默认状态为正常
		// 新增平台患者ID字段
		HangzhouHisID:   req.HangzhouHisID,
		AbcyunPatientID: req.AbcyunPatientID,
	}

	// 如果传入了状态，则使用传入的值
	if req.Status != 0 {
		patient.Status = req.Status
	}

	// 保存患者信息
	err = mysql.Master().Create(patient).Error
	if err != nil {
		logx.Errorf("创建患者失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "创建患者失败"))
		return
	}

	// 记录操作日志
	adminID := utils.GetAdminIDFromContext(r.Context())
	h.logAdminOperation(r, adminID, "患者管理", "创建", uint(patient.PatientID), "患者", fmt.Sprintf("创建患者: %s", patient.Name))

	logx.Infof("创建患者成功: patientId=%d", patient.PatientID)
	httpx.OkJson(w, types.NewSuccessResponse(map[string]interface{}{
		"patientId": patient.PatientID,
	}, "创建患者成功"))
}

// DeletePatient 删除患者
func (h *PatientHandler) DeletePatient(w http.ResponseWriter, r *http.Request) {
	var req DeletePatientRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("删除患者: patientId=%d", req.PatientID)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 获取当前患者信息
	patient, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除患者失败"))
		}
		return
	}

	// 删除患者信息（软删除）
	err = mysql.Master().Delete(patient).Error
	if err != nil {
		logx.Errorf("删除患者失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "删除患者失败"))
		return
	}

	// 记录操作日志
	adminID := utils.GetAdminIDFromContext(r.Context())
	h.logAdminOperation(r, adminID, "患者管理", "删除", uint(patient.PatientID), "患者", fmt.Sprintf("删除患者: %s", patient.Name))

	logx.Infof("删除患者成功: patientId=%d", req.PatientID)
	httpx.OkJson(w, types.NewSuccessResponse(nil, "删除患者成功"))
}

// GetPatientMedicalRecords 获取患者就诊记录
func (h *PatientHandler) GetPatientMedicalRecords(w http.ResponseWriter, r *http.Request) {
	var req GetPatientMedicalRecordsRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("获取患者就诊记录失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取患者就诊记录请求: patientId=%d, page=%d, size=%d", req.PatientID, req.Page, req.Size)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 检查患者是否存在
	_, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logx.Errorf("获取患者就诊记录失败: 患者ID %d 不存在", req.PatientID)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者就诊记录失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取患者就诊记录失败"))
		}
		return
	}

	// 构建查询条件
	query := make(map[string]interface{})
	query["patient_id"] = req.PatientID

	// 处理日期范围筛选
	if req.StartDate != "" && req.EndDate != "" {
		// 假设有一个就诊时间字段 visit_time
		query["visit_time BETWEEN ? AND ?"] = []string{req.StartDate + " 00:00:00", req.EndDate + " 23:59:59"}
	} else if req.StartDate != "" {
		query["visit_time >= ?"] = req.StartDate + " 00:00:00"
	} else if req.EndDate != "" {
		query["visit_time <= ?"] = req.EndDate + " 23:59:59"
	}

	// TODO: 从数据库获取患者就诊记录
	// 由于没有具体的就诊记录模型定义，这里使用模拟数据
	// 实际开发中应该创建对应的仓库和模型

	// 模拟数据，实际项目中应该从数据库查询
	records := []map[string]interface{}{
		{
			"recordId":    1,
			"patientId":   req.PatientID,
			"doctorName":  "张医生",
			"doctorId":    101,
			"department":  "内科",
			"visitTime":   "2023-06-01 10:30:00",
			"description": "感冒，发热",
			"diagnosis":   "上呼吸道感染",
			"treatment":   "口服药物治疗",
		},
		{
			"recordId":    2,
			"patientId":   req.PatientID,
			"doctorName":  "李医生",
			"doctorId":    102,
			"department":  "内科",
			"visitTime":   "2023-07-15 14:20:00",
			"description": "头痛，乏力",
			"diagnosis":   "偏头痛",
			"treatment":   "口服药物治疗，建议休息",
		},
	}

	// 模拟分页和总数
	total := int64(len(records))

	logx.Infof("获取患者就诊记录成功: patientId=%d, 共%d条记录", req.PatientID, total)

	// 返回分页结果
	pageResponse := types.NewPageResponse(records, total, &types.PageRequest{
		Page: req.Page,
		Size: req.Size,
	})

	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取患者就诊记录成功"))
}

// GetPatientPrescriptions 获取患者处方记录
func (h *PatientHandler) GetPatientPrescriptions(w http.ResponseWriter, r *http.Request) {
	var req GetPatientPrescriptionsRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("获取患者处方记录失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取患者处方记录请求: patientId=%d, page=%d, size=%d", req.PatientID, req.Page, req.Size)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 检查患者是否存在
	_, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logx.Errorf("获取患者处方记录失败: 患者ID %d 不存在", req.PatientID)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("获取患者处方记录失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取患者处方记录失败"))
		}
		return
	}

	// 构建查询条件
	query := make(map[string]interface{})
	query["patient_id"] = req.PatientID

	// 处理日期范围筛选
	if req.StartDate != "" && req.EndDate != "" {
		// 假设有一个处方时间字段 prescription_time
		query["prescription_time BETWEEN ? AND ?"] = []string{req.StartDate + " 00:00:00", req.EndDate + " 23:59:59"}
	} else if req.StartDate != "" {
		query["prescription_time >= ?"] = req.StartDate + " 00:00:00"
	} else if req.EndDate != "" {
		query["prescription_time <= ?"] = req.EndDate + " 23:59:59"
	}

	// TODO: 从数据库获取患者处方记录
	// 由于没有具体的处方记录模型定义，这里使用模拟数据
	// 实际开发中应该创建对应的仓库和模型

	// 模拟数据，实际项目中应该从数据库查询
	prescriptions := []map[string]interface{}{
		{
			"prescriptionId":   1,
			"patientId":        req.PatientID,
			"doctorName":       "张医生",
			"doctorId":         101,
			"department":       "内科",
			"prescriptionTime": "2023-06-01 11:00:00",
			"diagnosis":        "上呼吸道感染",
			"medicines": []map[string]interface{}{
				{
					"name":      "布洛芬",
					"dosage":    "0.2g",
					"frequency": "每日3次",
					"duration":  "3天",
					"note":      "饭后服用",
				},
				{
					"name":      "感冒灵颗粒",
					"dosage":    "1袋",
					"frequency": "每日3次",
					"duration":  "3天",
					"note":      "温水冲服",
				},
			},
			"totalAmount": 156.50,
			"status":      1, // 1-已发药, 0-未发药
		},
		{
			"prescriptionId":   2,
			"patientId":        req.PatientID,
			"doctorName":       "李医生",
			"doctorId":         102,
			"department":       "内科",
			"prescriptionTime": "2023-07-15 15:00:00",
			"diagnosis":        "偏头痛",
			"medicines": []map[string]interface{}{
				{
					"name":      "布洛芬缓释胶囊",
					"dosage":    "0.3g",
					"frequency": "每日2次",
					"duration":  "5天",
					"note":      "饭后服用",
				},
			},
			"totalAmount": 78.00,
			"status":      1, // 1-已发药, 0-未发药
		},
	}

	// 模拟分页和总数
	total := int64(len(prescriptions))

	logx.Infof("获取患者处方记录成功: patientId=%d, 共%d条记录", req.PatientID, total)

	// 返回分页结果
	pageResponse := types.NewPageResponse(prescriptions, total, &types.PageRequest{
		Page: req.Page,
		Size: req.Size,
	})

	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取患者处方记录成功"))
}

// AddPatientHealthRecord 添加患者健康档案
func (h *PatientHandler) AddPatientHealthRecord(w http.ResponseWriter, r *http.Request) {
	var req AddPatientHealthRecordRequest
	if err := httpx.ParseJsonBody(r, &req); err != nil {
		logx.Errorf("添加患者健康档案失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("添加患者健康档案请求: patientId=%d, recordType=%s", req.PatientID, req.RecordType)

	// 创建患者仓库
	patientRepo := patientModel.NewWxPatientRepository(mysql.Slave())

	// 检查患者是否存在
	_, err := patientRepo.FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logx.Errorf("添加患者健康档案失败: 患者ID %d 不存在", req.PatientID)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("添加患者健康档案失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "添加患者健康档案失败"))
		}
		return
	}

	// 验证日期格式
	_, err = time.Parse("2006-01-02", req.RecordDate)
	if err != nil {
		logx.Errorf("添加患者健康档案失败: 日期格式错误 %s", req.RecordDate)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "日期格式错误，应为yyyy-MM-dd"))
		return
	}

	// 如果指定了医生ID，验证医生是否存在
	if req.DoctorID > 0 {
		// 创建医生仓库，这里假设有doctorModel包
		doctorRepo := doctorModel.NewWxDoctorRepository(mysql.Slave())
		_, err := doctorRepo.FindByID(req.DoctorID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				logx.Errorf("添加患者健康档案失败: 医生ID %d 不存在", req.DoctorID)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "关联的医生不存在"))
			} else {
				logx.Errorf("添加患者健康档案失败: 查询医生信息错误 %v", err)
				httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "添加患者健康档案失败"))
			}
			return
		}
	}

	// TODO: 创建健康档案记录
	// 由于没有具体的健康档案模型定义，这里使用模拟数据
	// 实际开发中应该创建对应的仓库和模型

	// 模拟创建记录，返回ID
	recordID := uint(time.Now().Unix())

	// 模拟数据，实际项目中应该保存到数据库
	result := map[string]interface{}{
		"recordId":    recordID,
		"patientId":   req.PatientID,
		"recordType":  req.RecordType,
		"recordDate":  req.RecordDate,
		"content":     req.Content,
		"doctorId":    req.DoctorID,
		"attachments": req.Attachments,
		"createdAt":   time.Now().Format("2006-01-02 15:04:05"),
	}

	logx.Infof("患者健康档案添加成功: patientId=%d, recordId=%d", req.PatientID, recordID)

	// 记录操作日志 - 已由中间件统一处理

	httpx.OkJson(w, types.NewSuccessResponse(result, "添加健康档案成功"))
}

// GetPreConsultationList 获取患者预问诊记录列表
func (h *PatientHandler) GetPreConsultationList(w http.ResponseWriter, r *http.Request) {
	var req GetPatientPreConsultationsRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取患者预问诊记录列表: patientId=%d, page=%d, size=%d, startDate=%s, endDate=%s",
		req.PatientID, req.Page, req.Size, req.StartDate, req.EndDate)

	// 验证患者是否存在
	patient, err := patientModel.NewWxPatientRepository(mysql.Slave()).FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("查询患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询患者信息失败"))
		}
		return
	}

	// 构建查询条件
	db := mysql.Slave().Table("med_chat_session").Where("user_id = ?", patient.UserID)

	// 按日期范围筛选
	if req.StartDate != "" && req.EndDate != "" {
		startDate := req.StartDate + " 00:00:00"
		endDate := req.EndDate + " 23:59:59"
		db = db.Where("start_time BETWEEN ? AND ?", startDate, endDate)
	} else if req.StartDate != "" {
		startDate := req.StartDate + " 00:00:00"
		db = db.Where("start_time >= ?", startDate)
	} else if req.EndDate != "" {
		endDate := req.EndDate + " 23:59:59"
		db = db.Where("start_time <= ?", endDate)
	}

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logx.Errorf("统计预问诊记录数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取预问诊记录列表失败"))
		return
	}

	// 查询会话列表
	type ChatSessionResult struct {
		SessionID   string    `json:"session_id" db:"session_id"`
		UserID      int64     `json:"user_id" db:"user_id"`
		FullContent string    `json:"full_content" db:"full_content"`
		StartTime   time.Time `json:"start_time" db:"start_time"`
		EndTime     time.Time `json:"end_time" db:"end_time"`
		CreatedAt   time.Time `json:"created_at" db:"created_at"`
		UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	}

	var sessions []ChatSessionResult
	offset := (req.Page - 1) * req.Size
	if err := db.Order("start_time DESC").Offset(offset).Limit(req.Size).Find(&sessions).Error; err != nil {
		logx.Errorf("查询预问诊记录列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取预问诊记录列表失败"))
		return
	}

	// 处理会话数据
	result := make([]map[string]interface{}, len(sessions))
	for i, session := range sessions {
		// 截取前100个字符作为预览
		preview := session.FullContent
		if len(preview) > 100 {
			preview = preview[:100] + "..."
		}

		// 转换为Map
		sessionMap := map[string]interface{}{
			"sessionId": session.SessionID,
			"userId":    session.UserID,
			"preview":   preview,
			"startTime": session.StartTime,
			"endTime":   session.EndTime,
			"createdAt": session.CreatedAt,
			"updatedAt": session.UpdatedAt,
		}

		result[i] = sessionMap
	}

	logx.Infof("获取患者预问诊记录列表成功: 共%d条记录", total)

	pageResponse := types.NewPageResponse(result, total, req.PageRequest())
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取患者预问诊记录列表成功"))
}

// GetPreConsultationDetail 获取预问诊详情
func (h *PatientHandler) GetPreConsultationDetail(w http.ResponseWriter, r *http.Request) {
	var req GetPreConsultationDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取预问诊详情: patientId=%d, sessionId=%s", req.PatientID, req.SessionID)

	// 验证患者是否存在
	patient, err := patientModel.NewWxPatientRepository(mysql.Slave()).FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("查询患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询患者信息失败"))
		}
		return
	}

	// 查询会话详情
	type ChatSessionDetail struct {
		SessionID   string    `json:"session_id" db:"session_id"`
		UserID      int64     `json:"user_id" db:"user_id"`
		FullContent string    `json:"full_content" db:"full_content"`
		StartTime   time.Time `json:"start_time" db:"start_time"`
		EndTime     time.Time `json:"end_time" db:"end_time"`
		CreatedAt   time.Time `json:"created_at" db:"created_at"`
		UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	}

	var session ChatSessionDetail
	if err := mysql.Slave().Table("med_chat_session").
		Where("session_id = ? AND user_id = ?", req.SessionID, patient.UserID).
		First(&session).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "预问诊记录不存在"))
		} else {
			logx.Errorf("查询预问诊详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取预问诊详情失败"))
		}
		return
	}

	// 查询对话流
	type ChatStreamDetail struct {
		ID         int64     `json:"id" db:"id"`
		ChunkIndex int       `json:"chunk_index" db:"chunk_index"`
		Content    string    `json:"content" db:"content"`
		IsFinal    bool      `json:"is_final" db:"is_final"`
		CreatedAt  time.Time `json:"created_at" db:"created_at"`
	}

	var streams []ChatStreamDetail
	if err := mysql.Slave().Table("med_chat_stream").
		Where("session_id = ?", req.SessionID).
		Order("chunk_index ASC").
		Find(&streams).Error; err != nil {
		logx.Errorf("查询对话流失败: %v", err)
		// 不返回错误，继续处理
	}

	// 构建详情响应
	result := map[string]interface{}{
		"sessionId":   session.SessionID,
		"userId":      session.UserID,
		"patientId":   req.PatientID,
		"fullContent": session.FullContent,
		"startTime":   session.StartTime,
		"endTime":     session.EndTime,
		"createdAt":   session.CreatedAt,
		"updatedAt":   session.UpdatedAt,
		"streams":     streams,
	}

	logx.Infof("获取预问诊详情成功: sessionId=%s", req.SessionID)
	httpx.OkJson(w, types.NewSuccessResponse(result, "获取预问诊详情成功"))
}

// GetPatientReports 获取患者报告列表
func (h *PatientHandler) GetPatientReports(w http.ResponseWriter, r *http.Request) {
	var req GetPatientReportsRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取患者报告列表: patientId=%d, page=%d, size=%d, startDate=%s, endDate=%s, type=%s",
		req.PatientID, req.Page, req.Size, req.StartDate, req.EndDate, req.Type)

	// 验证患者是否存在
	_, err := patientModel.NewWxPatientRepository(mysql.Slave()).FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("查询患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询患者信息失败"))
		}
		return
	}

	// 构建查询条件
	db := mysql.Slave().Table("medical_report").Where("patient_id = ?", req.PatientID)

	// 按日期范围筛选
	if req.StartDate != "" && req.EndDate != "" {
		startDate := req.StartDate + " 00:00:00"
		endDate := req.EndDate + " 23:59:59"
		db = db.Where("created_at BETWEEN ? AND ?", startDate, endDate)
	} else if req.StartDate != "" {
		startDate := req.StartDate + " 00:00:00"
		db = db.Where("created_at >= ?", startDate)
	} else if req.EndDate != "" {
		endDate := req.EndDate + " 23:59:59"
		db = db.Where("created_at <= ?", endDate)
	}

	// 按报告类型筛选
	if req.Type != "" {
		db = db.Where("report_type = ?", req.Type)
	}

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logx.Errorf("统计报告数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取报告列表失败"))
		return
	}

	// 查询报告列表
	type MedicalReport struct {
		ReportID    uint      `json:"report_id" db:"report_id"`
		PatientID   uint      `json:"patient_id" db:"patient_id"`
		ReportType  string    `json:"report_type" db:"report_type"`
		ReportName  string    `json:"report_name" db:"report_name"`
		ReportDate  time.Time `json:"report_date" db:"report_date"`
		Description string    `json:"description" db:"description"`
		FileURL     string    `json:"file_url" db:"file_url"`
		Status      int       `json:"status" db:"status"`
		CreatedAt   time.Time `json:"created_at" db:"created_at"`
		UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	}

	var reports []MedicalReport
	offset := (req.Page - 1) * req.Size
	if err := db.Order("created_at DESC").Offset(offset).Limit(req.Size).Find(&reports).Error; err != nil {
		logx.Errorf("查询报告列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取报告列表失败"))
		return
	}

	// 处理报告数据
	result := make([]map[string]interface{}, len(reports))
	for i, report := range reports {
		// 转换为Map
		reportMap := map[string]interface{}{
			"reportId":    report.ReportID,
			"patientId":   report.PatientID,
			"reportType":  report.ReportType,
			"reportName":  report.ReportName,
			"reportDate":  report.ReportDate,
			"description": report.Description,
			"fileUrl":     report.FileURL,
			"status":      report.Status,
			"createdAt":   report.CreatedAt,
			"updatedAt":   report.UpdatedAt,
		}

		result[i] = reportMap
	}

	logx.Infof("获取患者报告列表成功: 共%d条记录", total)

	pageResponse := types.NewPageResponse(result, total, req.PageRequest())
	httpx.OkJson(w, types.NewSuccessResponse(pageResponse, "获取患者报告列表成功"))
}

// GetReportDetail 获取报告详情
func (h *PatientHandler) GetReportDetail(w http.ResponseWriter, r *http.Request) {
	var req GetReportDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取报告详情: patientId=%d, reportId=%d", req.PatientID, req.ReportID)

	// 验证患者是否存在
	_, err := patientModel.NewWxPatientRepository(mysql.Slave()).FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("查询患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询患者信息失败"))
		}
		return
	}

	// 查询报告详情
	type MedicalReportDetail struct {
		ReportID       uint      `json:"report_id" db:"report_id"`
		PatientID      uint      `json:"patient_id" db:"patient_id"`
		ReportType     string    `json:"report_type" db:"report_type"`
		ReportName     string    `json:"report_name" db:"report_name"`
		ReportDate     time.Time `json:"report_date" db:"report_date"`
		Description    string    `json:"description" db:"description"`
		FileURL        string    `json:"file_url" db:"file_url"`
		Content        string    `json:"content" db:"content"`
		Interpretation string    `json:"interpretation" db:"interpretation"`
		DoctorID       uint      `json:"doctor_id" db:"doctor_id"`
		DoctorName     string    `json:"doctor_name" db:"doctor_name"`
		Status         int       `json:"status" db:"status"`
		CreatedAt      time.Time `json:"created_at" db:"created_at"`
		UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
	}

	var report MedicalReportDetail
	if err := mysql.Slave().Table("medical_report").
		Where("report_id = ? AND patient_id = ?", req.ReportID, req.PatientID).
		First(&report).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "报告不存在"))
		} else {
			logx.Errorf("查询报告详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取报告详情失败"))
		}
		return
	}

	// 查询附件
	type ReportAttachment struct {
		AttachmentID uint      `json:"attachment_id" db:"attachment_id"`
		ReportID     uint      `json:"report_id" db:"report_id"`
		FileName     string    `json:"file_name" db:"file_name"`
		FileURL      string    `json:"file_url" db:"file_url"`
		FileType     string    `json:"file_type" db:"file_type"`
		FileSize     int64     `json:"file_size" db:"file_size"`
		CreatedAt    time.Time `json:"created_at" db:"created_at"`
	}

	var attachments []ReportAttachment
	if err := mysql.Slave().Table("report_attachment").
		Where("report_id = ?", req.ReportID).
		Find(&attachments).Error; err != nil {
		logx.Errorf("查询报告附件失败: %v", err)
		// 不返回错误，继续处理
	}

	// 构建详情响应
	result := map[string]interface{}{
		"reportId":       report.ReportID,
		"patientId":      report.PatientID,
		"reportType":     report.ReportType,
		"reportName":     report.ReportName,
		"reportDate":     report.ReportDate,
		"description":    report.Description,
		"fileUrl":        report.FileURL,
		"content":        report.Content,
		"interpretation": report.Interpretation,
		"doctorId":       report.DoctorID,
		"doctorName":     report.DoctorName,
		"status":         report.Status,
		"createdAt":      report.CreatedAt,
		"updatedAt":      report.UpdatedAt,
		"attachments":    attachments,
	}

	logx.Infof("获取报告详情成功: reportId=%d", req.ReportID)
	httpx.OkJson(w, types.NewSuccessResponse(result, "获取报告详情成功"))
}

// GetPatientMedicalRecordDetail 获取患者病历详情
func (h *PatientHandler) GetPatientMedicalRecordDetail(w http.ResponseWriter, r *http.Request) {
	var req GetPatientMedicalRecordDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	logx.Infof("获取患者病历详情: patientId=%d, recordId=%d", req.PatientID, req.RecordID)

	// 验证患者是否存在
	_, err := patientModel.NewWxPatientRepository(mysql.Slave()).FindByID(req.PatientID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "患者不存在"))
		} else {
			logx.Errorf("查询患者信息失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询患者信息失败"))
		}
		return
	}

	// 查询病历详情
	type MedicalRecordDetail struct {
		RecordID       uint      `json:"record_id" db:"record_id"`
		PatientID      uint      `json:"patient_id" db:"patient_id"`
		DoctorID       uint      `json:"doctor_id" db:"doctor_id"`
		DoctorName     string    `json:"doctor_name" db:"doctor_name"`
		VisitDate      time.Time `json:"visit_date" db:"visit_date"`
		DiagnosisType  string    `json:"diagnosis_type" db:"diagnosis_type"`
		Diagnosis      string    `json:"diagnosis" db:"diagnosis"`
		ChiefComplaint string    `json:"chief_complaint" db:"chief_complaint"`
		PresentIllness string    `json:"present_illness" db:"present_illness"`
		PastHistory    string    `json:"past_history" db:"past_history"`
		PhysicalExam   string    `json:"physical_exam" db:"physical_exam"`
		AuxiliaryExam  string    `json:"auxiliary_exam" db:"auxiliary_exam"`
		Treatment      string    `json:"treatment" db:"treatment"`
		Remark         string    `json:"remark" db:"remark"`
		Status         int       `json:"status" db:"status"`
		CreatedAt      time.Time `json:"created_at" db:"created_at"`
		UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
	}

	var record MedicalRecordDetail
	if err := mysql.Slave().Table("medical_record").
		Where("record_id = ? AND patient_id = ?", req.RecordID, req.PatientID).
		First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "病历不存在"))
		} else {
			logx.Errorf("查询病历详情失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取病历详情失败"))
		}
		return
	}

	// 查询处方
	type Prescription struct {
		PrescriptionID uint      `json:"prescription_id" db:"prescription_id"`
		RecordID       uint      `json:"record_id" db:"record_id"`
		Name           string    `json:"name" db:"name"`
		Status         int       `json:"status" db:"status"`
		CreatedAt      time.Time `json:"created_at" db:"created_at"`
	}

	var prescriptions []Prescription
	if err := mysql.Slave().Table("prescription").
		Where("record_id = ?", req.RecordID).
		Find(&prescriptions).Error; err != nil {
		logx.Errorf("查询处方列表失败: %v", err)
		// 不返回错误，继续处理
	}

	// 查询处方详情（药品列表）
	type PrescriptionDetail struct {
		Items []struct {
			ItemID         uint    `json:"item_id" db:"item_id"`
			PrescriptionID uint    `json:"prescription_id" db:"prescription_id"`
			MedicineID     uint    `json:"medicine_id" db:"medicine_id"`
			MedicineName   string  `json:"medicine_name" db:"medicine_name"`
			Specification  string  `json:"specification" db:"specification"`
			Dosage         string  `json:"dosage" db:"dosage"`
			Usage          string  `json:"usage" db:"usage"`
			Frequency      string  `json:"frequency" db:"frequency"`
			Days           int     `json:"days" db:"days"`
			Quantity       int     `json:"quantity" db:"quantity"`
			Unit           string  `json:"unit" db:"unit"`
			UnitPrice      float64 `json:"unit_price" db:"unit_price"`
			Amount         float64 `json:"amount" db:"amount"`
		} `json:"items"`
	}

	// 构建详情响应
	result := map[string]interface{}{
		"recordId":       record.RecordID,
		"patientId":      record.PatientID,
		"doctorId":       record.DoctorID,
		"doctorName":     record.DoctorName,
		"visitDate":      record.VisitDate,
		"diagnosisType":  record.DiagnosisType,
		"diagnosis":      record.Diagnosis,
		"chiefComplaint": record.ChiefComplaint,
		"presentIllness": record.PresentIllness,
		"pastHistory":    record.PastHistory,
		"physicalExam":   record.PhysicalExam,
		"auxiliaryExam":  record.AuxiliaryExam,
		"treatment":      record.Treatment,
		"remark":         record.Remark,
		"status":         record.Status,
		"createdAt":      record.CreatedAt,
		"updatedAt":      record.UpdatedAt,
		"prescriptions":  prescriptions,
	}

	// 对于每个处方获取处方明细
	if len(prescriptions) > 0 {
		prescriptionDetails := make([]PrescriptionDetail, len(prescriptions))

		for i, prescription := range prescriptions {
			var items []struct {
				ItemID         uint    `json:"item_id" db:"item_id"`
				PrescriptionID uint    `json:"prescription_id" db:"prescription_id"`
				MedicineID     uint    `json:"medicine_id" db:"medicine_id"`
				MedicineName   string  `json:"medicine_name" db:"medicine_name"`
				Specification  string  `json:"specification" db:"specification"`
				Dosage         string  `json:"dosage" db:"dosage"`
				Usage          string  `json:"usage" db:"usage"`
				Frequency      string  `json:"frequency" db:"frequency"`
				Days           int     `json:"days" db:"days"`
				Quantity       int     `json:"quantity" db:"quantity"`
				Unit           string  `json:"unit" db:"unit"`
				UnitPrice      float64 `json:"unit_price" db:"unit_price"`
				Amount         float64 `json:"amount" db:"amount"`
			}

			if err := mysql.Slave().Table("prescription_item").
				Where("prescription_id = ?", prescription.PrescriptionID).
				Find(&items).Error; err != nil {
				logx.Errorf("查询处方明细失败: prescriptionId=%d, error=%v", prescription.PrescriptionID, err)
				// 不返回错误，继续处理
			}

			prescriptionDetails[i].Items = items
		}

		result["prescriptionDetails"] = prescriptionDetails
	}

	logx.Infof("获取患者病历详情成功: recordId=%d", req.RecordID)
	httpx.OkJson(w, types.NewSuccessResponse(result, "获取患者病历详情成功"))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *PatientHandler) logAdminOperation(r *http.Request, adminID, module, action string, targetID uint, targetType, content string) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理: 管理员ID=%s, 模块=%s, 操作=%s", adminID, module, action)
}
