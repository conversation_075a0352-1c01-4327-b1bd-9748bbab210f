package model

import (
	"time"
	"yekaitai/pkg/infra/mysql"
)

// 支付方式
const (
	PaymentMethodAlipay  = "alipay"  // 支付宝
	PaymentMethodWechat  = "wechat"  // 微信支付
	PaymentMethodApple   = "apple"   // 苹果支付
	PaymentMethodOffline = "offline" // 线下支付
)

// 支付状态
const (
	PaymentStatusPending   = 1 // 待支付
	PaymentStatusSuccess   = 2 // 支付成功
	PaymentStatusFailed    = 3 // 支付失败
	PaymentStatusRefunding = 4 // 退款中
	PaymentStatusRefunded  = 5 // 已退款
)

// Payment 支付记录
type Payment struct {
	ID            uint       `gorm:"primaryKey;column:id" json:"id"`
	PaymentNo     string     `gorm:"column:payment_no;type:varchar(64);not null;uniqueIndex" json:"payment_no"` // 支付单号
	OrderNo       string     `gorm:"column:order_no;type:varchar(64);not null;index" json:"order_no"`           // 订单编号
	UserID        uint       `gorm:"column:user_id;not null;index" json:"user_id"`                              // 用户ID
	PaymentType   int        `gorm:"column:payment_type;not null;default:1" json:"payment_type"`                // 支付类型：1-微信，2-支付宝，3-余额
	Amount        float64    `gorm:"column:amount;not null;type:decimal(10,2)" json:"amount"`                   // 支付金额
	Status        int        `gorm:"column:status;not null;default:1" json:"status"`                            // 支付状态
	TransactionID string     `gorm:"column:transaction_id;type:varchar(128)" json:"transaction_id"`             // 第三方交易ID
	PaymentTime   *time.Time `gorm:"column:payment_time" json:"payment_time"`                                   // 支付时间
	RefundAmount  float64    `gorm:"column:refund_amount;type:decimal(10,2);default:0" json:"refund_amount"`    // 退款金额
	RefundTime    *time.Time `gorm:"column:refund_time" json:"refund_time"`                                     // 退款时间
	RefundReason  string     `gorm:"column:refund_reason;type:varchar(255)" json:"refund_reason"`               // 退款原因
	CallbackData  string     `gorm:"column:callback_data;type:text" json:"callback_data"`                       // 回调数据
	CreatedAt     time.Time  `gorm:"column:created_at;not null" json:"created_at"`                              // 创建时间
	UpdatedAt     time.Time  `gorm:"column:updated_at;not null" json:"updated_at"`                              // 更新时间
}

// TableName 表名
func (Payment) TableName() string {
	return "payments"
}

// PaymentRepository 支付仓库接口
type PaymentRepository interface {
	// 创建支付记录
	CreatePayment(payment *Payment) error
	// 根据支付单号查询支付记录
	FindPaymentByNo(paymentNo string) (*Payment, error)
	// 根据订单号查询支付记录
	FindPaymentsByOrderNo(orderNo string) ([]*Payment, error)
	// 更新支付状态
	UpdatePaymentStatus(paymentNo string, status int, transactionID string) error
	// 更新支付回调数据
	UpdateCallbackData(paymentNo string, callbackData string) error
	// 处理退款
	ProcessRefund(paymentNo string, refundAmount float64, refundReason string) error
}

// paymentRepository 支付仓库实现
type paymentRepository struct{}

// NewPaymentRepository 创建支付仓库
func NewPaymentRepository() PaymentRepository {
	return &paymentRepository{}
}

// CreatePayment 创建支付记录
func (r *paymentRepository) CreatePayment(payment *Payment) error {
	return mysql.Master().Create(payment).Error
}

// FindPaymentByNo 根据支付单号查询支付记录
func (r *paymentRepository) FindPaymentByNo(paymentNo string) (*Payment, error) {
	var payment Payment
	err := mysql.Slave().Where("payment_no = ?", paymentNo).First(&payment).Error
	if err != nil {
		return nil, err
	}
	return &payment, nil
}

// FindPaymentsByOrderNo 根据订单号查询支付记录
func (r *paymentRepository) FindPaymentsByOrderNo(orderNo string) ([]*Payment, error) {
	var payments []*Payment
	err := mysql.Slave().Where("order_no = ?", orderNo).Find(&payments).Error
	return payments, err
}

// UpdatePaymentStatus 更新支付状态
func (r *paymentRepository) UpdatePaymentStatus(paymentNo string, status int, transactionID string) error {
	updates := map[string]interface{}{
		"status":         status,
		"transaction_id": transactionID,
		"updated_at":     time.Now(),
	}

	if status == PaymentStatusSuccess {
		updates["payment_time"] = time.Now()
	}

	return mysql.Master().Model(&Payment{}).Where("payment_no = ?", paymentNo).Updates(updates).Error
}

// UpdateCallbackData 更新支付回调数据
func (r *paymentRepository) UpdateCallbackData(paymentNo string, callbackData string) error {
	return mysql.Master().Model(&Payment{}).Where("payment_no = ?", paymentNo).Updates(map[string]interface{}{
		"callback_data": callbackData,
		"updated_at":    time.Now(),
	}).Error
}

// ProcessRefund 处理退款
func (r *paymentRepository) ProcessRefund(paymentNo string, refundAmount float64, refundReason string) error {
	return mysql.Master().Model(&Payment{}).Where("payment_no = ?", paymentNo).Updates(map[string]interface{}{
		"status":        PaymentStatusRefunded,
		"refund_amount": refundAmount,
		"refund_time":   time.Now(),
		"refund_reason": refundReason,
		"updated_at":    time.Now(),
	}).Error
}
