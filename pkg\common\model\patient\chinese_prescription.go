package patient

import (
	"time"

	"gorm.io/gorm"
)

// AbcYunChinesePrescription ABC云中药处方表
type AbcYunChinesePrescription struct {
	ID                uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	PatientID         uint           `gorm:"column:patient_id;index;comment:患者ID(关联wx_patient表)" json:"patient_id"`
	ExternalID        string         `gorm:"column:external_id;index;comment:外部处方ID" json:"external_id"`
	OutpatientSheetID string         `gorm:"column:outpatient_sheet_id;index;comment:门诊单ID" json:"outpatient_sheet_id"`
	Type              int            `gorm:"column:type;comment:处方类型(1:西药,2:输液,3:中药)" json:"type"`
	Specification     string         `gorm:"column:specification;comment:类型(饮片、颗粒)" json:"specification"`
	DoseCount         int            `gorm:"column:dose_count;comment:剂数" json:"dose_count"`
	DailyDosage       string         `gorm:"column:daily_dosage;comment:每日剂量" json:"daily_dosage"`
	Usage             string         `gorm:"column:usage;comment:用法" json:"usage"`
	Freq              string         `gorm:"column:freq;comment:用药频次" json:"freq"`
	Requirement       string         `gorm:"column:requirement;comment:服用要求" json:"requirement"`
	UsageLevel        string         `gorm:"column:usage_level;comment:单次服用用量" json:"usage_level"`
	CreatedAt         time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (AbcYunChinesePrescription) TableName() string {
	return "abcyun_chinese_prescriptions"
}

// AbcYunChinesePrescriptionItem ABC云中药处方单项表
type AbcYunChinesePrescriptionItem struct {
	ID                 uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	PatientID          uint           `gorm:"column:patient_id;index;comment:患者ID(关联wx_patient表)" json:"patient_id"`
	PrescriptionID     uint           `gorm:"column:prescription_id;index;comment:处方ID" json:"prescription_id"`
	ExternalID         string         `gorm:"column:external_id;index;comment:外部处方单项ID" json:"external_id"`
	PrescriptionFormID string         `gorm:"column:prescription_form_id;comment:外部处方单ID" json:"prescription_form_id"`
	ProductID          string         `gorm:"column:product_id;comment:药品ID" json:"product_id"`
	Type               int            `gorm:"column:type;comment:类型(1:药品,2:物资)" json:"type"`
	SubType            int            `gorm:"column:sub_type;comment:子类型" json:"sub_type"`
	Name               string         `gorm:"column:name;comment:药品商用名" json:"name"`
	Specification      string         `gorm:"column:specification;comment:药品规格" json:"specification"`
	Manufacturer       string         `gorm:"column:manufacturer;comment:生产厂家" json:"manufacturer"`
	Usage              string         `gorm:"column:usage;comment:用法" json:"usage"`
	Ivgtt              float64        `gorm:"column:ivgtt;comment:输液滴速" json:"ivgtt"`
	IvgttUnit          string         `gorm:"column:ivgtt_unit;comment:输液滴速单位" json:"ivgtt_unit"`
	Freq               string         `gorm:"column:freq;comment:用药频次" json:"freq"`
	Dosage             string         `gorm:"column:dosage;comment:单次剂量" json:"dosage"`
	DosageUnit         string         `gorm:"column:dosage_unit;comment:剂量单位" json:"dosage_unit"`
	Days               int            `gorm:"column:days;comment:用药天数" json:"days"`
	SpecialRequirement string         `gorm:"column:special_requirement;comment:特殊要求" json:"special_requirement"`
	IsDismounting      int            `gorm:"column:is_dismounting;comment:是否使用拆零" json:"is_dismounting"`
	UnitCount          float64        `gorm:"column:unit_count;comment:数量" json:"unit_count"`
	Unit               string         `gorm:"column:unit;comment:单位" json:"unit"`
	UnitPrice          float64        `gorm:"column:unit_price;comment:单位价格" json:"unit_price"`
	GroupID            int            `gorm:"column:group_id;comment:分组编号" json:"group_id"`
	IsAst              int            `gorm:"column:is_ast;comment:是否需要皮试(0:不需要,1:需要)" json:"is_ast"`
	AstResult          string         `gorm:"column:ast_result;type:text;comment:皮试结果(JSON)" json:"ast_result"`
	ProductInfo        string         `gorm:"column:product_info;type:text;comment:药品信息(JSON)" json:"product_info"`
	CreatedAt          time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (AbcYunChinesePrescriptionItem) TableName() string {
	return "abcyun_chinese_prescription_items"
}
