package model

import (
	"encoding/json"
	"time"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

/*
ServicePackage 枚举类型说明：

1. ValidityType 有效期类型：
   - permanent: 永久有效
   - fixed: 固定时间

2. SaleStartType 售卖开始类型：
   - after_release: 发布后立即开始售卖
   - fixed: 固定时间开始售卖

3. AppointmentRule 预约规则：
   - no_need: 无需预约
   - advance: 提前预约

4. AppointmentTimeType 预约时间类型：
   - all_day: 全天可预约
   - fixed: 固定时间段预约

5. Status 套餐状态：
   - active: 生效中(已上架)
   - disabled: 已禁用(已下架)
   - expired: 已过期

6. 时间字段格式说明：
   - ValidityStart/ValidityEnd: 有效期时间，格式YYYY-MM-DD
   - SaleStartAt: 售卖开始时间，格式YYYY-MM-DD HH:MM
   - SaleEndAt: 售卖结束时间，格式YYYY-MM-DD

7. JSON字段格式说明：
   - Images: ["url1", "url2", ...] 图片URL数组
   - Process: ["步骤1", "步骤2", ...] 服务流程数组
   - StoreIDs: [1, 2, 3, ...] 门店ID数组
   - UnavailableDates: ["2024-01-01", "2024-01-02", ...] 不可用日期数组

8. 退款说明字段：
   - RefundAnyTimeDesc: 随时退款说明(0-500字)
   - AutoRefundDesc: 过期自动退款说明(0-500字)
*/

// ServicePackage 服务套餐模型
type ServicePackage struct {
	ID                   uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:套餐ID"`
	Name                 string         `json:"name" gorm:"type:varchar(60);not null;comment:套餐名称(1-10字)"`
	Images               string         `json:"images" gorm:"type:text;comment:套餐图片(JSON数组，最多10张)"`
	TagID                uint           `json:"tag_id" gorm:"index;comment:所属标签ID"`
	Sort                 int            `json:"sort" gorm:"default:0;index;comment:推荐排序值(0表示不推荐，值越小排序越靠前)"`
	BodyPart             string         `json:"body_part" gorm:"type:varchar(255);comment:服务部位(0-50字)"`
	Process              string         `json:"process" gorm:"type:text;comment:服务流程(JSON数组，最多10条)"`
	Duration             int            `json:"duration" gorm:"default:0;comment:服务时长(分钟，1-1000分钟)"`
	Times                int            `json:"times" gorm:"default:1;comment:服务次数(1-999次)"`
	SuitableFor          string         `json:"suitable_for" gorm:"type:varchar(1000);comment:适应人群(0-100字)"`
	Price                float64        `json:"price" gorm:"type:decimal(10,2);not null;comment:价格(1-9999元)"`
	PointPrice           int            `json:"point_price" gorm:"default:0;comment:积分价格(积分兑换)"`
	OriginalPrice        float64        `json:"original_price" gorm:"type:decimal(10,2);comment:划线价格(1-9999元)"`
	ExtraInfo            string         `json:"extra_info" gorm:"type:text;comment:补充信息(0-500字)"`
	StoreIDs             string         `json:"store_ids" gorm:"type:text;comment:适用门店ID(JSON数组)"`
	IsAllStores          bool           `json:"is_all_stores" gorm:"default:false;comment:是否适用所有门店"`
	DiscountInfo         string         `json:"discount_info" gorm:"type:text;comment:优惠同享信息(0-500字)"`
	GiftInfo             string         `json:"gift_info" gorm:"type:text;comment:免费赠送信息(0-500字)"`
	ValidityType         string         `json:"validity_type" gorm:"type:varchar(20);default:'permanent';comment:有效期类型(permanent:永久有效,fixed:固定时间)"`
	ValidityStart        *time.Time     `json:"validity_start" gorm:"comment:有效期开始时间(格式YYYY-MM-DD)"`
	ValidityEnd          *time.Time     `json:"validity_end" gorm:"comment:有效期结束时间(格式YYYY-MM-DD)"`
	UnavailableDates     string         `json:"unavailable_dates" gorm:"type:text;comment:不可用日期(JSON数组，格式YYYY-MM-DD)"`
	SaleStartType        string         `json:"sale_start_type" gorm:"type:varchar(20);default:'after_release';comment:售卖开始类型(after_release:发布后,fixed:固定时间)"`
	SaleStartAt          *time.Time     `json:"sale_start_at" gorm:"comment:售卖开始时间(格式YYYY-MM-DD HH:MM)"`
	SaleEndAt            *time.Time     `json:"sale_end_at" gorm:"comment:售卖结束时间(格式YYYY-MM-DD)"`
	AppointmentRule      string         `json:"appointment_rule" gorm:"type:varchar(20);default:'no_need';comment:预约规则(no_need:无需预约,advance:提前预约)"`
	AdvanceHours         int            `json:"advance_hours" gorm:"default:0;comment:提前预约小时数(1-9999小时)"`
	AppointmentTimeType  string         `json:"appointment_time_type" gorm:"type:varchar(20);default:'all_day';comment:预约时间类型(all_day:全天,fixed:固定时间)"`
	AppointmentStartTime string         `json:"appointment_start_time" gorm:"type:varchar(5);comment:预约开始时间(HH:MM格式)"`
	AppointmentEndTime   string         `json:"appointment_end_time" gorm:"type:varchar(5);comment:预约结束时间(HH:MM格式)"`
	MaxAppointments      int            `json:"max_appointments" gorm:"default:1;comment:最大预约人数(1-9999人)"`
	MaxPurchases         int            `json:"max_purchases" gorm:"default:1;comment:每人最大购买数(1-9999张)"`
	SingleMaxPurchases   int            `json:"single_max_purchases" gorm:"default:1;comment:单次最大购买数(1-9999张)"`
	WarmTips             string         `json:"warm_tips" gorm:"type:text;comment:温馨提示(0-500字)"`
	SupportRefundAnyTime bool           `json:"support_refund_any_time" gorm:"default:false;comment:是否支持随时退"`
	RefundAnyTimeDesc    string         `json:"refund_any_time_desc" gorm:"type:text;comment:随时退款说明(0-500字)"`
	SupportAutoRefund    bool           `json:"support_auto_refund" gorm:"default:false;comment:是否支持过期自动退"`
	AutoRefundDesc       string         `json:"auto_refund_desc" gorm:"type:text;comment:过期自动退款说明(0-500字)"`
	Description          string         `json:"description" gorm:"type:text;comment:图文详情"`
	Status               string         `json:"status" gorm:"type:varchar(20);default:'active';index;comment:状态(active:生效中(已上架),disabled:已禁用(已下架),expired:已过期)"`
	SalesCount           int            `json:"sales_count" gorm:"default:0;comment:已售出数量"`
	CreatedBy            uint           `json:"created_by" gorm:"comment:创建人ID"`
	CreatedAt            time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt            time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt            gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (s *ServicePackage) TableName() string {
	return "service_packages"
}

// ServicePackageStoreRelation 套餐与门店关联表
type ServicePackageStoreRelation struct {
	ID               uint      `json:"id" gorm:"primaryKey;autoIncrement;comment:关联ID"`
	ServicePackageID uint      `json:"service_package_id" gorm:"column:service_package_id;index;not null;comment:套餐ID"`
	StoreID          uint      `json:"store_id" gorm:"column:store_id;index;not null;comment:门店ID"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
}

// TableName 返回表名
func (s *ServicePackageStoreRelation) TableName() string {
	return "service_package_store_relations"
}

// ServicePackageRepository 套餐仓库接口
type ServicePackageRepository interface {
	// 基础CRUD
	Create(pkg *ServicePackage) error
	Update(pkg *ServicePackage) error
	Delete(id uint) error
	FindByID(id uint) (*ServicePackage, error)
	List(page, size int, query string, tagID uint, status string, startDate, endDate string) ([]*ServicePackage, int64, error)

	// 套餐状态管理
	UpdateStatus(id uint, status string) error

	// 绑定门店
	BindStores(packageID uint, storeIDs []uint, isAllStores bool) error

	// 获取套餐绑定的门店
	GetPackageStores(packageID uint) ([]uint, error)

	// 获取门店的套餐列表
	GetStorePackages(storeID uint) ([]*ServicePackage, error)

	// 检查套餐名称是否存在
	CheckNameExists(name string, excludeID uint) (bool, error)

	// 更新已售出数量
	UpdateSalesCount(id uint, count int) error

	// 检查是否有未完成的订单
	HasActiveOrders(id uint) (bool, error)

	// 检查有效期并更新状态
	CheckAndUpdateExpiredStatus() error
}

// servicePackageRepository 套餐仓库实现
type servicePackageRepository struct {
	db *gorm.DB
}

// NewServicePackageRepository 创建套餐仓库
func NewServicePackageRepository() ServicePackageRepository {
	return &servicePackageRepository{
		db: mysql.Master(),
	}
}

// Create 创建套餐
func (r *servicePackageRepository) Create(pkg *ServicePackage) error {
	return r.db.Create(pkg).Error
}

// Update 更新套餐
func (r *servicePackageRepository) Update(pkg *ServicePackage) error {
	return r.db.Save(pkg).Error
}

// Delete 删除套餐
func (r *servicePackageRepository) Delete(id uint) error {
	// 开启事务
	tx := r.db.Begin()

	// 使用GORM软删除机制删除套餐
	if err := tx.Delete(&ServicePackage{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除套餐与门店的关联
	if err := tx.Where("service_package_id = ?", id).Delete(&ServicePackageStoreRelation{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// FindByID 根据ID查找套餐
func (r *servicePackageRepository) FindByID(id uint) (*ServicePackage, error) {
	var pkg ServicePackage
	err := r.db.First(&pkg, id).Error
	if err != nil {
		return nil, err
	}
	return &pkg, nil
}

// List 获取套餐列表
func (r *servicePackageRepository) List(page, size int, query string, tagID uint, status string, startDate, endDate string) ([]*ServicePackage, int64, error) {
	var packages []*ServicePackage
	var total int64

	db := r.db.Model(&ServicePackage{})

	// 条件筛选
	if query != "" {
		db = db.Where("name LIKE ?", "%"+query+"%")
	}

	if tagID > 0 {
		db = db.Where("tag_id = ?", tagID)
	}

	if status != "" {
		db = db.Where("status = ?", status)
	}

	if startDate != "" {
		db = db.Where("created_at >= ?", startDate)
	}

	if endDate != "" {
		db = db.Where("created_at <= ?", endDate+" 23:59:59")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := db.Order("sort ASC, created_at DESC").Offset(offset).Limit(size).Find(&packages).Error; err != nil {
		return nil, 0, err
	}

	return packages, total, nil
}

// UpdateStatus 更新套餐状态
func (r *servicePackageRepository) UpdateStatus(id uint, status string) error {
	return r.db.Model(&ServicePackage{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error
}

// BindStores 绑定门店
func (r *servicePackageRepository) BindStores(packageID uint, storeIDs []uint, isAllStores bool) error {
	tx := r.db.Begin()

	// 更新套餐的全部门店标志
	if err := tx.Model(&ServicePackage{}).Where("id = ?", packageID).
		Updates(map[string]interface{}{
			"is_all_stores": isAllStores,
			"updated_at":    time.Now(),
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除原有关联
	if err := tx.Where("service_package_id = ?", packageID).Delete(&ServicePackageStoreRelation{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 如果不是全部门店且有指定门店，创建新关联
	if !isAllStores && len(storeIDs) > 0 {
		relations := make([]ServicePackageStoreRelation, 0, len(storeIDs))
		for _, storeID := range storeIDs {
			relations = append(relations, ServicePackageStoreRelation{
				ServicePackageID: packageID,
				StoreID:          storeID,
			})
		}

		if err := tx.Create(&relations).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// GetPackageStores 获取套餐绑定的门店
func (r *servicePackageRepository) GetPackageStores(packageID uint) ([]uint, error) {
	// 先检查套餐是否存在
	var pkg ServicePackage
	if err := r.db.Select("id, is_all_stores").Where("id = ?", packageID).First(&pkg).Error; err != nil {
		return nil, err
	}

	// 如果适用所有门店，返回所有门店ID
	if pkg.IsAllStores {
		var storeIDs []uint
		if err := r.db.Model(&struct {
			ID uint `gorm:"column:id"`
		}{}).
			Table("t_stores").
			Where("is_enabled = ?", true).
			Pluck("id", &storeIDs).Error; err != nil {
			return nil, err
		}
		return storeIDs, nil
	}

	// 否则返回绑定的门店ID
	var relations []ServicePackageStoreRelation
	var storeIDs []uint

	err := r.db.Where("service_package_id = ?", packageID).Find(&relations).Error
	if err != nil {
		return nil, err
	}

	for _, relation := range relations {
		storeIDs = append(storeIDs, relation.StoreID)
	}

	return storeIDs, nil
}

// GetStorePackages 获取门店的套餐列表
func (r *servicePackageRepository) GetStorePackages(storeID uint) ([]*ServicePackage, error) {
	var packages []*ServicePackage

	// 查询适用所有门店的套餐
	err := r.db.Where("is_all_stores = ? AND status = ?", true, "active").Find(&packages).Error
	if err != nil {
		return nil, err
	}

	// 查询特定绑定此门店的套餐
	var specificPackages []*ServicePackage
	err = r.db.Joins("JOIN t_service_package_store_relations r ON t_service_packages.id = r.service_package_id").
		Where("r.store_id = ? AND t_service_packages.status = ? AND t_service_packages.is_all_stores = ?",
			storeID, "active", false).
		Find(&specificPackages).Error

	if err != nil {
		return packages, nil // 至少返回全门店套餐
	}

	// 合并两种套餐
	packages = append(packages, specificPackages...)

	return packages, nil
}

// CheckNameExists 检查套餐名称是否存在
func (r *servicePackageRepository) CheckNameExists(name string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.Model(&ServicePackage{}).Where("name = ?", name)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// UpdateSalesCount 更新已售出数量
func (r *servicePackageRepository) UpdateSalesCount(id uint, count int) error {
	return r.db.Model(&ServicePackage{}).Where("id = ?", id).
		UpdateColumn("sales_count", gorm.Expr("sales_count + ?", count)).Error
}

// HasActiveOrders 检查是否有未完成的订单
// 实际实现需要查询订单表，这里只是示例
func (r *servicePackageRepository) HasActiveOrders(id uint) (bool, error) {
	// TODO: 实现实际的订单检查逻辑
	// 例如：
	// var count int64
	// err := r.db.Model(&Order{}).Where("service_package_id = ? AND status IN ?", id, []string{"pending", "paid", "processing"}).Count(&count).Error
	// return count > 0, err

	// 临时实现
	return false, nil
}

// CheckAndUpdateExpiredStatus 检查有效期并更新状态
func (r *servicePackageRepository) CheckAndUpdateExpiredStatus() error {
	now := time.Now()

	// 更新已过期套餐状态
	return r.db.Model(&ServicePackage{}).
		Where("status = ? AND validity_type = ? AND validity_end < ?", "active", "fixed", now).
		Updates(map[string]interface{}{
			"status":     "expired",
			"updated_at": now,
		}).Error
}

// 工具方法：将JSON字符串转换为字符串数组
func StringToStringArray(jsonStr string) ([]string, error) {
	if jsonStr == "" {
		return []string{}, nil
	}

	var result []string
	err := json.Unmarshal([]byte(jsonStr), &result)
	return result, err
}

// 工具方法：将JSON字符串转换为整型数组
func StringToUintArray(jsonStr string) ([]uint, error) {
	if jsonStr == "" {
		return []uint{}, nil
	}

	var result []uint
	err := json.Unmarshal([]byte(jsonStr), &result)
	return result, err
}

// ServicePackage 枚举常量定义
const (
	// ValidityType 有效期类型
	ValidityTypePermanent = "permanent" // 永久有效
	ValidityTypeFixed     = "fixed"     // 固定时间

	// SaleStartType 售卖开始类型
	SaleStartTypeAfterRelease = "after_release" // 发布后立即开始售卖
	SaleStartTypeFixed        = "fixed"         // 固定时间开始售卖

	// AppointmentRule 预约规则
	AppointmentRuleNoNeed  = "no_need" // 无需预约
	AppointmentRuleAdvance = "advance" // 提前预约

	// AppointmentTimeType 预约时间类型
	AppointmentTimeTypeAllDay = "all_day" // 全天可预约
	AppointmentTimeTypeFixed  = "fixed"   // 固定时间段预约

	// Status 套餐状态
	StatusActive   = "active"   // 生效中(已上架)
	StatusDisabled = "disabled" // 已禁用(已下架)
	StatusExpired  = "expired"  // 已过期
)

// ServiceOrder 服务订单模型
type ServiceOrder struct {
	ID               uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:订单ID"`
	OrderNo          string         `json:"order_no" gorm:"type:varchar(32);uniqueIndex;not null;comment:订单号"`
	UserID           uint           `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ServicePackageID uint           `json:"service_package_id" gorm:"not null;index;comment:服务套餐ID"`
	ServiceName      string         `json:"service_name" gorm:"type:varchar(60);not null;comment:服务名称"`
	Quantity         int            `json:"quantity" gorm:"default:1;comment:购买数量"`
	OriginalAmount   float64        `json:"original_amount" gorm:"type:decimal(10,2);not null;comment:原始金额"`
	CouponAmount     float64        `json:"coupon_amount" gorm:"type:decimal(10,2);default:0;comment:优惠券抵扣金额"`
	PointAmount      float64        `json:"point_amount" gorm:"type:decimal(10,2);default:0;comment:积分抵扣金额"`
	PayAmount        float64        `json:"pay_amount" gorm:"type:decimal(10,2);not null;comment:实付金额"`
	UsedPoints       int64          `json:"used_points" gorm:"default:0;comment:使用积分数"`
	CouponID         *uint          `json:"coupon_id" gorm:"comment:使用的优惠券ID"`
	Status           string         `json:"status" gorm:"type:varchar(20);default:'pending';index;comment:订单状态:pending-待支付,paid-已支付,completed-已完成,cancelled-已取消,refunded-已退款"`
	PayStatus        string         `json:"pay_status" gorm:"type:varchar(20);default:'unpaid';index;comment:支付状态:unpaid-未支付,paid-已支付"`
	PayMethod        string         `json:"pay_method" gorm:"type:varchar(20);comment:支付方式"`
	PayTime          *time.Time     `json:"pay_time" gorm:"type:datetime(3);comment:支付时间"`
	ExpireTime       *time.Time     `json:"expire_time" gorm:"type:datetime(3);index;comment:订单过期时间"`
	RemainingTimes   int            `json:"remaining_times" gorm:"default:0;comment:剩余使用次数"`
	UsedTimes        int            `json:"used_times" gorm:"default:0;comment:已使用次数"`
	ValidityStart    *time.Time     `json:"validity_start" gorm:"type:datetime(3);comment:有效期开始时间"`
	ValidityEnd      *time.Time     `json:"validity_end" gorm:"type:datetime(3);comment:有效期结束时间"`
	QRCode           string         `json:"qr_code" gorm:"type:varchar(255);comment:核销二维码"`
	QRCodeURL        string         `json:"qr_code_url" gorm:"type:varchar(255);comment:二维码URL"`
	VerificationCode string         `json:"verification_code" gorm:"type:varchar(50);index;comment:核销码"`
	VerificationTime *time.Time     `json:"verification_time" gorm:"type:datetime(3);index;comment:核销时间"`
	VerifierID       uint           `json:"verifier_id" gorm:"comment:核销人ID"`
	VerifierName     string         `json:"verifier_name" gorm:"type:varchar(50);comment:核销人姓名"`
	RefundAmount     float64        `json:"refund_amount" gorm:"type:decimal(10,2);default:0;comment:退款金额"`
	RefundTime       *time.Time     `json:"refund_time" gorm:"type:datetime(3);comment:退款时间"`
	RefundReason     string         `json:"refund_reason" gorm:"type:varchar(255);comment:退款原因"`
	CreatedAt        time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt        time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 指定表名
func (ServiceOrder) TableName() string {
	return "service_orders"
}

// ServiceAppointment 服务预约模型
type ServiceAppointment struct {
	ID               uint           `json:"id" gorm:"primaryKey;autoIncrement;comment:预约ID"`
	OrderID          uint           `json:"order_id" gorm:"not null;index;comment:订单ID"`
	UserID           uint           `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ServicePackageID uint           `json:"service_package_id" gorm:"not null;index;comment:服务套餐ID"`
	StoreID          uint           `json:"store_id" gorm:"not null;index;comment:门店ID"`
	AppointmentDate  time.Time      `json:"appointment_date" gorm:"type:date;not null;index;comment:预约日期"`
	AppointmentTime  *string        `json:"appointment_time" gorm:"type:varchar(11);comment:预约时间段(HH:MM-HH:MM)"`
	Status           string         `json:"status" gorm:"type:varchar(20);default:'booked';index;comment:预约状态"`
	ModifyCount      int            `json:"modify_count" gorm:"default:0;comment:修改次数"`
	CancelTime       *time.Time     `json:"cancel_time" gorm:"type:datetime(3);comment:取消时间"`
	CompletedTime    *time.Time     `json:"completed_time" gorm:"type:datetime(3);comment:完成时间"`
	CreatedAt        time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt        time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 指定表名
func (ServiceAppointment) TableName() string {
	return "service_appointments"
}

// 服务订单状态常量
const (
	// 订单状态 (service_orders.status)
	OrderStatusPending   = "pending"   // 待支付 - 订单已创建，等待用户支付
	OrderStatusPaid      = "paid"      // 已支付 - 用户已支付，可以使用服务或预约
	OrderStatusCompleted = "completed" // 已完成 - 服务已全部使用完毕
	OrderStatusCancelled = "cancelled" // 已取消 - 订单被取消（未支付状态下）
	OrderStatusRefunded  = "refunded"  // 已退款 - 订单已退款

	// 支付状态 (service_orders.pay_status)
	PayStatusUnpaid = "unpaid" // 未支付 - 订单未支付
	PayStatusPaid   = "paid"   // 已支付 - 订单已支付成功

	// 预约状态 (service_appointments.status)
	AppointmentStatusBooked    = "booked"    // 已预约 - 预约已确认
	AppointmentStatusCancelled = "cancelled" // 已取消 - 预约被取消
	AppointmentStatusCompleted = "completed" // 已完成 - 预约已完成（已核销）
	AppointmentStatusExpired   = "expired"   // 已过期 - 预约已过期
)

// 服务订单状态转换说明：
// pending -> paid (支付成功)
// pending -> cancelled (取消订单)
// paid -> completed (服务使用完毕)
// paid -> refunded (申请退款)
// cancelled -> 无法转换到其他状态
// completed -> 无法转换到其他状态
// refunded -> 无法转换到其他状态

// GetOrderStatusText 获取订单状态文本描述
func GetOrderStatusText(status string) string {
	switch status {
	case OrderStatusPending:
		return "待支付"
	case OrderStatusPaid:
		return "已支付"
	case OrderStatusCompleted:
		return "已完成"
	case OrderStatusCancelled:
		return "已取消"
	case OrderStatusRefunded:
		return "已退款"
	default:
		return "未知状态"
	}
}

// GetPayStatusText 获取支付状态文本描述
func GetPayStatusText(payStatus string) string {
	switch payStatus {
	case PayStatusUnpaid:
		return "未支付"
	case PayStatusPaid:
		return "已支付"
	default:
		return "未知状态"
	}
}

// GetAppointmentStatusText 获取预约状态文本描述
func GetAppointmentStatusText(status string) string {
	switch status {
	case AppointmentStatusBooked:
		return "已预约"
	case AppointmentStatusCancelled:
		return "已取消"
	case AppointmentStatusCompleted:
		return "已完成"
	case AppointmentStatusExpired:
		return "已过期"
	default:
		return "未知状态"
	}
}

// IsValidOrderStatus 检查订单状态是否有效
func IsValidOrderStatus(status string) bool {
	validStatuses := []string{
		OrderStatusPending,
		OrderStatusPaid,
		OrderStatusCompleted,
		OrderStatusCancelled,
		OrderStatusRefunded,
	}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// CanCancelOrder 检查订单是否可以取消
func CanCancelOrder(status string, payStatus string) bool {
	return status == OrderStatusPending && payStatus == PayStatusUnpaid
}

// CanRefundOrder 检查订单是否可以退款
func CanRefundOrder(status string, payStatus string) bool {
	return status == OrderStatusPaid && payStatus == PayStatusPaid
}

// CanVerifyOrder 检查订单是否可以核销
func CanVerifyOrder(status string, payStatus string) bool {
	return status == OrderStatusPaid && payStatus == PayStatusPaid
}
