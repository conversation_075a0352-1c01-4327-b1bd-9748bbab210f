package routes

import (
	"net/http"
	"yekaitai/wx_internal/handler"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterUploadRoutes 注册小程序上传路由
func RegisterUploadRoutes(server RestServer, serverCtx *svc.WxServiceContext) {
	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 注册需要认证的上传路由
	server.AddRoutes(
		[]rest.Route{
			// 通用文件上传
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/upload/file",
				Handler: wxAuthWrapper(handler.UploadFileHandler(serverCtx)),
			},
			// 图片上传
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/upload/image",
				Handler: wxAuthWrapper(handler.UploadImageHandler(serverCtx)),
			},
			// 文档上传
			{
				Method:  http.MethodPost,
				Path:    "/api/wx/upload/document",
				Handler: wxAuthWrapper(handler.UploadDocumentHandler(serverCtx)),
			},
			// 视频上传(大文件由前端直接分片上传)
			// {
			// 	Method:  http.MethodPost,
			// 	Path:    "/api/wx/upload/video",
			// 	Handler: wxAuthWrapper(handler.UploadVideoHandler(serverCtx)),
			// },
		},
	)
}
