package handler

import (
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/modules/service_setup/model"
	"yekaitai/internal/types"
	"yekaitai/pkg/infra/mysql"
	wxSvc "yekaitai/wx_internal/svc"
	wxUtils "yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// ServiceOrderHandler 服务订单处理器
type ServiceOrderHandler struct {
	svcCtx *wxSvc.WxServiceContext
}

// NewServiceOrderHandler 创建服务订单处理器
func NewServiceOrderHandler(svcCtx *wxSvc.WxServiceContext) *ServiceOrderHandler {
	return &ServiceOrderHandler{
		svcCtx: svcCtx,
	}
}

// GetOrderList 获取服务订单列表
func (h *ServiceOrderHandler) GetOrderList(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	status := r.URL.Query().Get("status") // 状态筛选：pending(待支付), paid(待使用), completed(已完成), cancelled(已取消), refunded(已退款)
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	pageSize, _ := strconv.Atoi(r.URL.Query().Get("page_size"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 构建查询条件
	query := mysql.Slave().Table("service_orders").
		Where("user_id = ? AND deleted_at IS NULL", userID)

	// 状态筛选
	switch status {
	case "pending":
		query = query.Where("pay_status = 'unpaid'")
	case "paid":
		query = query.Where("pay_status = 'paid' AND remaining_times > 0")
	case "completed":
		query = query.Where("pay_status = 'paid' AND remaining_times = 0")
	case "cancelled":
		query = query.Where("status = 'cancelled'")
	case "refunded":
		query = query.Where("status = 'refunded'")
	}

	// 分页
	offset := (page - 1) * pageSize
	query = query.Order("created_at DESC").Offset(offset).Limit(pageSize)

	// 查询订单列表
	var orders []model.ServiceOrder
	if err := query.Find(&orders).Error; err != nil {
		logx.Errorf("查询服务订单列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "查询订单列表失败"))
		return
	}

	// 格式化订单数据
	result := make([]map[string]interface{}, 0, len(orders))
	for _, order := range orders {
		item := h.formatOrderItem(order)
		result = append(result, item)
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取订单列表成功"))
}

// GetOrderDetail 获取服务订单详情
func (h *ServiceOrderHandler) GetOrderDetail(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	orderIDStr := r.URL.Query().Get("order_id")
	if orderIDStr == "" {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID不能为空"))
		return
	}

	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单ID格式错误"))
		return
	}

	// 查询订单详情
	var order model.ServiceOrder
	err = mysql.Slave().Table("service_orders").
		Where("id = ? AND user_id = ? AND deleted_at IS NULL", orderID, userID).
		First(&order).Error

	if err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在"))
		return
	}

	// 查询服务套餐信息
	var service model.ServicePackage
	mysql.Slave().Table("service_packages").
		Where("id = ?", order.ServicePackageID).
		First(&service)

	// 查询预约信息
	var appointment *model.ServiceAppointment
	mysql.Slave().Table("service_appointments").
		Where("order_id = ? AND status != 'cancelled'", order.ID).
		First(&appointment)

	// 格式化订单详情
	result := h.formatOrderDetail(order, service, appointment)

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取订单详情成功"))
}

// CancelOrder 取消订单
func (h *ServiceOrderHandler) CancelOrder(w http.ResponseWriter, r *http.Request) {
	userID, ok := wxUtils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	var req struct {
		OrderID uint   `json:"order_id"` // 订单ID
		Reason  string `json:"reason"`   // 取消原因
	}

	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "参数解析失败"))
		return
	}

	// 开始事务
	tx := mysql.Master().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询订单
	var order model.ServiceOrder
	err := tx.Table("service_orders").
		Where("id = ? AND user_id = ? AND deleted_at IS NULL", req.OrderID, userID).
		First(&order).Error

	if err != nil {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeNotFound, "订单不存在"))
		return
	}

	// 检查订单状态
	if order.PayStatus == "paid" {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "已支付订单不能取消"))
		return
	}

	if order.Status == "cancelled" {
		tx.Rollback()
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "订单已取消"))
		return
	}

	// 更新订单状态
	err = tx.Table("service_orders").
		Where("id = ?", req.OrderID).
		Updates(map[string]interface{}{
			"status":        "cancelled",
			"refund_reason": req.Reason,
			"updated_at":    time.Now(),
		}).Error

	if err != nil {
		tx.Rollback()
		logx.Errorf("取消订单失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "取消订单失败"))
		return
	}

	// TODO: 退还优惠券和积分

	tx.Commit()

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "取消订单成功"))
}

// formatOrderItem 格式化订单列表项
func (h *ServiceOrderHandler) formatOrderItem(order model.ServiceOrder) map[string]interface{} {
	// 计算订单状态显示
	var displayStatus string
	var canCancel bool
	var canPay bool
	var canUse bool

	switch {
	case order.PayStatus == "unpaid":
		displayStatus = "待支付"
		canCancel = true
		canPay = true
		// 检查是否过期
		if order.ExpireTime != nil && order.ExpireTime.Before(time.Now()) {
			displayStatus = "已取消"
			canCancel = false
			canPay = false
		}
	case order.PayStatus == "paid" && order.RemainingTimes > 0:
		displayStatus = "待使用"
		canUse = true
	case order.PayStatus == "paid" && order.RemainingTimes == 0:
		displayStatus = "已完成"
	case order.Status == "cancelled":
		displayStatus = "已取消"
	case order.Status == "refunded":
		displayStatus = "已退款"
	default:
		displayStatus = "未知状态"
	}

	return map[string]interface{}{
		"order_id":        order.ID,
		"order_no":        order.OrderNo,
		"service_id":      order.ServicePackageID,
		"service_name":    order.ServiceName,
		"quantity":        order.Quantity,
		"original_amount": order.OriginalAmount,
		"coupon_amount":   order.CouponAmount,
		"point_amount":    order.PointAmount,
		"pay_amount":      order.PayAmount,
		"used_points":     order.UsedPoints,
		"remaining_times": order.RemainingTimes,
		"used_times":      order.UsedTimes,
		"status":          order.Status,
		"pay_status":      order.PayStatus,
		"display_status":  displayStatus,
		"can_cancel":      canCancel,
		"can_pay":         canPay,
		"can_use":         canUse,
		"pay_time":        order.PayTime,
		"expire_time":     order.ExpireTime,
		"validity_start":  order.ValidityStart,
		"validity_end":    order.ValidityEnd,
		"created_at":      order.CreatedAt,
	}
}

// formatOrderDetail 格式化订单详情
func (h *ServiceOrderHandler) formatOrderDetail(order model.ServiceOrder, service model.ServicePackage, appointment *model.ServiceAppointment) map[string]interface{} {
	result := h.formatOrderItem(order)

	// 添加服务套餐信息
	result["service_info"] = map[string]interface{}{
		"id":               service.ID,
		"name":             service.Name,
		"images":           service.Images,
		"body_part":        service.BodyPart,
		"duration":         service.Duration,
		"times":            service.Times,
		"appointment_rule": service.AppointmentRule,
		"advance_hours":    service.AdvanceHours,
		"warm_tips":        service.WarmTips,
	}

	// 添加预约信息
	if appointment != nil {
		result["appointment"] = map[string]interface{}{
			"id":               appointment.ID,
			"appointment_date": appointment.AppointmentDate,
			"appointment_time": appointment.AppointmentTime,
			"store_id":         appointment.StoreID,
			"status":           appointment.Status,
			"modify_count":     appointment.ModifyCount,
		}
		result["has_appointment"] = true
	} else {
		result["has_appointment"] = false
	}

	// 生成二维码（如果已支付）
	if order.PayStatus == "paid" {
		result["qr_code"] = h.generateQRCode(order.OrderNo)
	}

	return result
}

// generateQRCode 生成核销二维码
func (h *ServiceOrderHandler) generateQRCode(orderNo string) string {
	// 生成二维码内容（包含订单号和时间戳的加密字符串）
	timestamp := time.Now().Unix()
	content := fmt.Sprintf("%s_%d", orderNo, timestamp)

	// 使用MD5生成签名（实际项目中应该使用更安全的加密方式）
	hash := md5.Sum([]byte(content + "service_qr_secret"))
	signature := fmt.Sprintf("%x", hash)

	// 二维码数据格式：订单号|时间戳|签名
	qrData := fmt.Sprintf("%s|%d|%s", orderNo, timestamp, signature)

	// 这里简化处理，实际应该调用二维码生成库
	// 例如使用 github.com/skip2/go-qrcode
	qrCodeBase64 := base64.StdEncoding.EncodeToString([]byte(qrData))

	return fmt.Sprintf("data:text/plain;base64,%s", qrCodeBase64)
}
