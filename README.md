# 叶开泰医疗系统

## 项目介绍

叶开泰医疗预约系统是一个基于Go-Zero框架开发的医疗预约管理平台，支持多种HIS系统的集成，提供统一的预约、挂号、患者管理等功能。

## 系统架构

系统采用了适配器模式和工厂模式，通过统一的接口抽象不同HIS系统的操作，实现了对多种HIS系统的无缝集成。

### HIS客户端适配器

系统支持多种HIS系统的集成，每种HIS系统都有对应的客户端适配器：

- **YeKaiTaiClient** - 叶开泰HIS客户端适配器
- **ABCClient** - ABC云诊所管家客户端适配器
- **ChongQingClient** - 重庆HIS客户端适配器

### 接口和工厂类

- **Client** - 定义了所有HIS客户端的通用接口，包括获取科室、医生、排班、预约等操作
- **Factory** 和 **ClientFactory** - 根据提供商类型创建对应客户端的工厂，支持动态注册和创建不同的HIS客户端

### 服务层

- **HISService** - 集成各种HIS系统的服务，提供统一的业务逻辑处理

## 设计原则

当前的代码结构符合以下设计原则：

1. **接口隔离原则**：通过Client接口抽象了不同HIS系统的共同操作
2. **依赖倒置原则**：高层模块依赖于抽象接口，而不是具体实现
3. **适配器模式**：通过适配器实现了对不同HIS系统的统一访问
4. **工厂模式**：使用工厂类创建不同的客户端实现
5. **模块化和分层架构**：系统采用了模块化设计和清晰的分层架构，方便维护和扩展

这种设计使得系统可以灵活地支持新的HIS系统集成，只需要实现Client接口并注册到工厂即可，不会影响现有功能。

## 技术栈

- **框架**：Go-Zero
- **数据库**：MySQL (使用Gorm ORM)
- **缓存**：Redis
- **消息队列**：Kafka
- **对象存储**：七牛云
- **日志**：Zap

## 项目结构

```
├── api         # API定义和处理器
├── cmd         # 命令行工具和初始化代码
│   └── bootstrap  # 系统启动和初始化代码
├── config      # 配置文件和配置结构
├── internal    # 内部代码
│   ├── config     # 配置管理
│   ├── handler    # HTTP处理器
│   ├── middleware # 中间件
│   ├── modules    # 按功能模块组织的代码
│   │   ├── admin        # 管理员模块
│   │   ├── appointment  # 预约/挂号模块
│   │   ├── auth         # 认证模块
│   │   ├── consultation # 咨询模块
│   │   ├── pharmacy     # 药房模块
│   │   ├── user         # 用户模块
│   │   └── utils        # 工具类
│   ├── router     # 路由定义
│   │   └── routes    # 按模块组织的路由
│   ├── svc        # 服务上下文
│   ├── types      # 类型定义
│   └── util       # 工具函数
├── pkg         # 公共包
    └── common/
        ├── models/          # 公共模型
        │   ├── user/        # 用户模型
        │   └── patient/     # 患者模型
│   ├── adapters    # 第三方系统适配器
│   │   ├── abcyun     # ABC云对接
│   │   └── medlinker  # 医联对接
│   ├── infra       # 基础设施组件
│   │   ├── logger     # 日志工具
│   │   ├── kafka      # kafka工具
│   │   ├── mysql      # MySQL工具
│   │   └── redis      # Redis工具
│   └── utils       # 通用工具函数
└── etc         # 配置文件
```

## 环境设置

### 开发环境

```
go version go1.21
```

### 配置文件

项目使用YAML格式的配置文件，位于`etc`目录：

- `yekaitai-dev.yaml` - 开发环境配置
- `yekaitai-test.yaml` - 测试环境配置
- `yekaitai-prod.yaml` - 生产环境配置