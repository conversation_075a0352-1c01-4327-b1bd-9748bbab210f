package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	baseSetupHandler "yekaitai/internal/modules/base_setup/handler"
	goodsHandler "yekaitai/internal/modules/goods/handler"
	orderHandler "yekaitai/internal/modules/order/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterGoodsRoutes 注册商城管理相关路由
func RegisterGoodsRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 创建go-zero格式的handler实例
	categoryHandler := goodsHandler.NewCategoryGoZeroHandler()
	goodsHandlerInst := goodsHandler.NewGoodsGoZeroHandler()
	shippingHandler := goodsHandler.NewShippingGoZeroHandler()
	orderHandlerInst := orderHandler.NewOrderGoZeroHandler()
	storeHandler := baseSetupHandler.NewStoreConfigGoZeroHandler()

	// 商品分类管理路由
	server.AddRoutes(
		[]rest.Route{
			// 获取商品分类
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/categories",
				Handler: adminAuthWrapper(categoryHandler.ListCategories),
			},
			// 创建商品分类
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/categories",
				Handler: adminAuthWrapper(categoryHandler.CreateCategory),
			},
			// 获取分类详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/categories/:id",
				Handler: adminAuthWrapper(categoryHandler.GetCategory),
			},
			// 更新商品分类
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/categories/:id",
				Handler: adminAuthWrapper(categoryHandler.UpdateCategory),
			},
			// 删除商品分类
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/categories/:id",
				Handler: adminAuthWrapper(categoryHandler.DeleteCategory),
			},
			// 获取分类树
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/categories/tree",
				Handler: adminAuthWrapper(categoryHandler.GetCategoryTree),
			},
		},
	)

	// 商品管理路由
	server.AddRoutes(
		[]rest.Route{
			// 获取商品列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/goods",
				Handler: adminAuthWrapper(goodsHandlerInst.ListGoods),
			},
			// 创建商品
			// {
			// 	Method:  http.MethodPost,
			// 	Path:    "/api/admin/goods",
			// 	Handler: adminAuthWrapper(goodsHandlerInst.CreateGoods),
			// },
			// 获取商品详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/goods/:id",
				Handler: adminAuthWrapper(goodsHandlerInst.GetGoods),
			},
			// 更新商品
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/goods/:id",
				Handler: adminAuthWrapper(goodsHandlerInst.UpdateGoods),
			},
			// 删除商品
			// {
			// 	Method:  http.MethodDelete,
			// 	Path:    "/api/admin/goods/:id",
			// 	Handler: adminAuthWrapper(goodsHandlerInst.DeleteGoods),
			// },
		},
	)

	// 订单管理路由
	server.AddRoutes(
		[]rest.Route{
			// 获取订单列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/orders",
				Handler: adminAuthWrapper(orderHandlerInst.ListOrders),
			},
			// 获取订单详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/orders/:id",
				Handler: adminAuthWrapper(orderHandlerInst.GetOrder),
			},
			// 更新订单状态
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/orders/:id/status",
				Handler: adminAuthWrapper(orderHandlerInst.UpdateOrderStatus),
			},
			// 订单统计
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/orders/statistics",
				Handler: adminAuthWrapper(orderHandlerInst.GetOrderStatistics),
			},
		},
	)

	// 运费配置路由
	server.AddRoutes(
		[]rest.Route{
			// 设置运费配置
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/shipping/config",
				Handler: adminAuthWrapper(shippingHandler.SetShippingConfig),
			},
			// 获取运费配置
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/shipping/config",
				Handler: adminAuthWrapper(shippingHandler.GetShippingConfig),
			},
		},
	)

	// 店铺配置路由
	server.AddRoutes(
		[]rest.Route{
			// 创建/更新店铺配置
			{
				Method:  http.MethodPost,
				Path:    "/api/admin/store/config",
				Handler: adminAuthWrapper(storeHandler.CreateStoreConfig),
			},
			// 获取店铺配置
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/store/config",
				Handler: adminAuthWrapper(storeHandler.GetStoreConfig),
			},
			// 更新店铺配置
			{
				Method:  http.MethodPut,
				Path:    "/api/admin/store/config",
				Handler: adminAuthWrapper(storeHandler.UpdateStoreConfig),
			},
			// 上传证件
			// {
			// 	Method:  http.MethodPost,
			// 	Path:    "/api/admin/store/upload",
			// 	Handler: adminAuthWrapper(storeHandler.UploadStoreLicense),
			// },
		},
	)
}
