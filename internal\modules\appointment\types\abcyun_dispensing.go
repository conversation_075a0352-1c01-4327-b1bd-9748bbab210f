package types

// PatientInfo 结构已在其他文件中定义，这里使用

// DispensingDetailRequest 根据发药单ID查询发药单详情请求
type DispensingDetailRequest struct {
	ID string `path:"id"` // 发药单ID
}

// DispensingByDateRequest 按天查询发药单请求
type DispensingByDateRequest struct {
	Date     string `form:"date"`              // 日期
	DateType int    `form:"dateType,optional"` // 日期类型 0:发药单创建时间 1:发药单操作时间
	Limit    int    `form:"limit,optional"`    // 每页显示条数
	Offset   int    `form:"offset,optional"`   // 分页起始下标
	ClinicID string `form:"clinicId,optional"` // 诊所ID
}

// DispensingDispenseAllRequest 发药请求
type DispensingDispenseAllRequest struct {
	DispensingSheetID string `path:"dispenseSheetId"`     // 发药单ID
	OperatorID        string `form:"operatorId,optional"` // 操作员ID
}

// DispensingUndispenseAllRequest 退药请求
type DispensingUndispenseAllRequest struct {
	DispensingSheetID string `path:"dispenseSheetId"`     // 发药单ID
	OperatorID        string `form:"operatorId,optional"` // 操作员ID
}

// ExternalTraceItem 外部追踪信息项
type ExternalTraceItem struct {
	Content string `json:"content"` // 内容
	Time    string `json:"time"`    // 时间，格式 yyyy-MM-dd HH:mm:ss
}

// DispensingProcessingStatusRequest 修改加工状态请求
type DispensingProcessingStatusRequest struct {
	ExternalTrace        []ExternalTraceItem `json:"externalTrace,omitempty"` // 外部追踪信息
	DispensingSheetID    string              `json:"dispensingSheetId"`       // 发药单ID
	DispensingFormID     string              `json:"dispensingFormId"`        // 发药单处方ID
	ProcessGeneralStatus int                 `json:"processGeneralStatus"`    // 加工状态
}

// DispensingDeliveryRequest 修改快递信息请求
type DispensingDeliveryRequest struct {
	ExternalTrace     []ExternalTraceItem `json:"externalTrace,omitempty"` // 外部追踪信息
	DispensingSheetID string              `json:"dispensingSheetId"`       // 发药单ID
	DeliveryStatus    int                 `json:"deliveryStatus"`          // 配送状态
	DeliveryOrderNo   string              `json:"deliveryOrderNo"`         // 快递单号
}

// DeliveryTraceDataItem 快递物流跟踪信息项
type DeliveryTraceDataItem struct {
	Context string `json:"context"` // 内容描述
	Ftime   string `json:"ftime"`   // 发生时间
}

// DispensingDeliveryTraceRequest 更新快递物流信息请求
type DispensingDeliveryTraceRequest struct {
	DeliveryOrderNo   string                  `path:"deliveryOrderNo"`   // 快递单号
	DeliveryTraceData []DeliveryTraceDataItem `json:"deliveryTraceData"` // 物流跟踪数据
}

// DispensingByChargeSheetIDRequest 根据收费单ID查询发药单详情列表请求
type DispensingByChargeSheetIDRequest struct {
	ID string `path:"id"` // 收费单ID
}

// DispensingByPatientRequest 查询患者发药单请求
type DispensingByPatientRequest struct {
	PatientID string `path:"patientId"`          // 患者ID
	BeginDate string `form:"beginDate,optional"` // 开始日期
	EndDate   string `form:"endDate,optional"`   // 结束日期
	Limit     int    `form:"limit,optional"`     // 分页大小
	Offset    int    `form:"offset,optional"`    // 分页偏移
	Status    int    `form:"status,optional"`    // 发药单状态
	ClinicID  string `form:"clinicId,optional"`  // 诊所ID
}

// Pharmacy 药房信息
type Pharmacy struct {
	No int `json:"no"` // 药房编号
}

// Doctor 医生信息
type Doctor struct {
	ID   string `json:"id"`   // 医生ID
	Name string `json:"name"` // 医生姓名
}

// DeliveryCompany 快递公司信息
type DeliveryCompany struct {
	ID   string `json:"id"`   // 快递公司ID
	Name string `json:"name"` // 快递公司名称
}

// DispensingDeliveryInfo 配送信息
type DispensingDeliveryInfo struct {
	AddressProvinceName string          `json:"addressProvinceName"` // 省份名称
	AddressCityName     string          `json:"addressCityName"`     // 城市名称
	AddressDistrictName string          `json:"addressDistrictName"` // 区县名称
	AddressDetail       string          `json:"addressDetail"`       // 详细地址
	DeliveryName        string          `json:"deliveryName"`        // 收件人姓名
	DeliveryMobile      string          `json:"deliveryMobile"`      // 收件人手机号
	DeliveryCompany     DeliveryCompany `json:"deliveryCompany"`     // 快递公司
	DeliveryOrderNo     string          `json:"deliveryOrderNo"`     // 快递单号
	DeliveryPayType     int             `json:"deliveryPayType"`     // 支付方式
	DeliveryFee         float64         `json:"deliveryFee"`         // 配送费
}

// UsageInfo 用法信息
type UsageInfo struct {
	Usage               string  `json:"usage"`               // 用法
	Freq                string  `json:"freq"`                // 频次
	MedicineRequirement string  `json:"medicineRequirement"` // 药物要求
	DoseCount           int     `json:"doseCount"`           // 剂数
	DailyDosage         string  `json:"dailyDosage"`         // 日剂量
	Requirement         string  `json:"requirement"`         // 要求
	UsageLevel          string  `json:"usageLevel"`          // 使用级别
	ProcessUsage        string  `json:"processUsage"`        // 加工用法
	ProcessRemark       string  `json:"processRemark"`       // 加工备注
	UsageDays           string  `json:"usageDays"`           // 使用天数
	ProcessBagUnitCount float64 `json:"processBagUnitCount"` // 加工袋单位数量
	TotalProcessCount   float64 `json:"totalProcessCount"`   // 总加工数量
}

// DispensingBatch 发药批次信息
type DispensingBatch struct {
	Action             string   `json:"action"`             // 操作类型
	StockID            int      `json:"stockId"`            // 库存ID
	Supplier           Supplier `json:"supplier"`           // 供应商
	BatchID            int      `json:"batchId"`            // 批次ID
	BatchNo            string   `json:"batchNo"`            // 批次号
	ProductionDate     string   `json:"productionDate"`     // 生产日期
	ExpiryDate         string   `json:"expiryDate"`         // 有效期
	PackageCount       float64  `json:"packageCount"`       // 包装数量
	PiecePrice         float64  `json:"piecePrice"`         // 单价
	PackagePrice       float64  `json:"packagePrice"`       // 包装价格
	PieceCount         float64  `json:"pieceCount"`         // 单位数量
	PackageCostPrice   float64  `json:"packageCostPrice"`   // 包装成本价
	CostPrice          float64  `json:"costPrice"`          // 成本价
	SalePrice          float64  `json:"salePrice"`          // 销售价格
	StockInOutDetailID string   `json:"stockInOutDetailId"` // 入库出库明细ID
	Created            string   `json:"created"`            // 创建时间
}

// CompoundInfo 调配信息
type CompoundInfo struct {
	CompoundBy         string `json:"compoundBy"`         // 调配人ID
	CompoundByName     string `json:"compoundByName"`     // 调配人姓名
	CompoundByHandSign string `json:"compoundByHandSign"` // 调配人签名URL
	CompoundTime       string `json:"compoundTime"`       // 调配时间
}

// AuditInfo 审核信息
type AuditInfo struct {
	AuditBy         string `json:"auditBy"`         // 审核人ID
	AuditByName     string `json:"auditByName"`     // 审核人姓名
	AuditByHandSign string `json:"auditByHandSign"` // 审核人签名URL
	AuditTime       string `json:"auditTime"`       // 审核时间
}

// DispensingFormItem 发药单表单项目
type DispensingFormItem struct {
	ID                string            `json:"id"`                // 表单项ID
	SourceFormItemID  string            `json:"sourceFormItemId"`  // 源表单项ID
	Status            int               `json:"status"`            // 状态
	ProductID         string            `json:"productId"`         // 产品ID
	ProductName       string            `json:"productName"`       // 产品名称
	Unit              string            `json:"unit"`              // 单位
	UnitCount         string            `json:"unitCount"`         // 单位数量
	DoseCount         string            `json:"doseCount"`         // 剂数
	TotalCount        string            `json:"totalCount"`        // 总数量
	UsageInfo         UsageInfo         `json:"usageInfo"`         // 用法信息
	ProductInfo       interface{}       `json:"productInfo"`       // 产品信息
	TotalPrice        float64           `json:"totalPrice"`        // 总价
	DispensingBatches []DispensingBatch `json:"dispensingBatches"` // 发药批次
}

// DispensingForm 发药单表单
type DispensingForm struct {
	ID                  string               `json:"id"`                  // 表单ID
	DispseneFormType    int                  `json:"dispseneFormType"`    // 表单类型
	NumberOfHerbs       int                  `json:"numberOfHerbs"`       // 草药数量
	UsageInfo           UsageInfo            `json:"usageInfo"`           // 用法信息
	TotalPrice          float64              `json:"totalPrice"`          // 总价
	DispensingFormItems []DispensingFormItem `json:"dispensingFormItems"` // 表单项目
	CompoundInfo        CompoundInfo         `json:"compoundInfo"`        // 调配信息
	AuditInfo           AuditInfo            `json:"auditInfo"`           // 审核信息
}

// DispensingDetail 发药单详情
type DispensingDetail struct {
	ClinicID        string                 `json:"clinicId"`        // 诊所ID
	ID              string                 `json:"id"`              // 发药单ID
	SourceSheetID   string                 `json:"sourceSheetId"`   // 源单据ID
	DoctorID        string                 `json:"doctorId"`        // 医生ID
	PatientOrderID  string                 `json:"patientOrderId"`  // 就诊单ID
	PatientOrderNo  string                 `json:"patientOrderNo"`  // 就诊单号
	DiagnosedDate   string                 `json:"diagnosedDate"`   // 诊断日期
	MedicalRecord   interface{}            `json:"medicalRecord"`   // 病历信息
	PatientInfo     PatientInfo            `json:"patientInfo"`     // 患者信息
	DeliveryInfo    DispensingDeliveryInfo `json:"deliveryInfo"`    // 配送信息
	DeliveredStatus int                    `json:"deliveredStatus"` // 已配送状态
	DeliveryType    int                    `json:"deliveryType"`    // 配送类型
	Status          int                    `json:"status"`          // 状态
	StatusName      string                 `json:"statusName"`      // 状态名称
	DispensedBy     string                 `json:"dispensedBy"`     // 发药人ID
	DispensedByName string                 `json:"dispensedByName"` // 发药人姓名
	DispensedTime   string                 `json:"dispensedTime"`   // 发药时间
	Created         string                 `json:"created"`         // 创建时间
	Pharmacy        Pharmacy               `json:"pharmacy"`        // 药房
	NetIncomeFee    float64                `json:"netIncomeFee"`    // 净收入
	Doctor          Doctor                 `json:"doctor"`          // 医生
	Department      Department             `json:"department"`      // 科室
	IsShebaoPay     int                    `json:"isShebaoPay"`     // 是否社保支付
	DispensingForms []DispensingForm       `json:"dispensingForms"` // 发药单表单
}

// DispensingDetailResponse 发药单详情响应
type DispensingDetailResponse struct {
	Data DispensingDetail `json:"data"` // 发药单详情
}

// DispensingByChargeSheetIDResponse 根据收费单ID查询发药单详情列表响应
type DispensingByChargeSheetIDResponse struct {
	Data struct {
		DispensingSheets []DispensingDetail `json:"dispensingSheets"` // 发药单详情列表
	} `json:"data"`
}

// DispensingOperationResponse 发药操作响应
type DispensingOperationResponse struct {
	Data OperationResult `json:"data"` // 操作结果
}

// DispensingSummary 发药单摘要信息
type DispensingSummary struct {
	ID             string      `json:"id"`             // 发药单ID
	PatientOrderID string      `json:"patientOrderId"` // 就诊单ID
	PatientOrderNo string      `json:"patientOrderNo"` // 就诊单号
	ChargeSheetID  string      `json:"chargeSheetId"`  // 收费单ID
	PharmacyNo     int         `json:"pharmacyNo"`     // 药房编号
	PharmacyType   int         `json:"pharmacyType"`   // 药房类型
	Status         int         `json:"status"`         // 状态
	Patient        PatientInfo `json:"patient"`        // 患者信息
}

// DispensingPatientSummary 患者发药单摘要信息
type DispensingPatientSummary struct {
	ID             string `json:"id"`             // 发药单ID
	PatientOrderID string `json:"patientOrderId"` // 就诊单ID
	ChargeSheetID  string `json:"chargeSheetId"`  // 收费单ID
	PharmacyNo     int    `json:"pharmacyNo"`     // 药房编号
	PharmacyType   int    `json:"pharmacyType"`   // 药房类型
	Status         int    `json:"status"`         // 状态
	Created        string `json:"created"`        // 创建时间
	SellerID       string `json:"sellerId"`       // 销售人ID
	SellerName     string `json:"sellerName"`     // 销售人姓名
}

// DispensingByDateResponse 按天查询发药单响应
type DispensingByDateResponse struct {
	Data struct {
		Rows   []DispensingSummary `json:"rows"`   // 发药单摘要信息列表
		Total  int                 `json:"total"`  // 总数
		Offset int                 `json:"offset"` // 偏移
		Limit  int                 `json:"limit"`  // 限制
	} `json:"data"`
}

// DispensingByPatientResponse 查询患者发药单响应
type DispensingByPatientResponse struct {
	Data struct {
		Rows   []DispensingPatientSummary `json:"rows"`   // 患者发药单摘要信息列表
		Total  int                        `json:"total"`  // 总数
		Offset int                        `json:"offset"` // 偏移
		Limit  int                        `json:"limit"`  // 限制
	} `json:"data"`
}
