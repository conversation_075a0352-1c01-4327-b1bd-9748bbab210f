package types

// StatBaseQueryRequest 统计基础查询请求
type StatBaseQueryRequest struct {
	Date     string `form:"date"`              // 日期（格式yyyy-MM-dd）
	Limit    int    `form:"limit,optional"`    // 每页显示条数，默认20
	Offset   int    `form:"offset,optional"`   // 分页起始下标 默认0
	ClinicID string `form:"clinicId,optional"` // 诊所ID
}

// StatRevenueDetailItemsRequest 收费明细-明细查询请求
type StatRevenueDetailItemsRequest struct {
	Date      string `form:"date"`               // 日期（格式yyyy-MM-dd）
	Dimension int    `form:"dimension,optional"` // 维度 0:药品维度 1:批次维度，默认为药品维度
	Limit     int    `form:"limit,optional"`     // 每页显示条数，最大不能超过 100 默认20
	Offset    int    `form:"offset,optional"`    // 分页起始下标 默认0
	ClinicID  string `form:"clinicId,optional"`  // 诊所ID
}

// StatCardRechargeRequest 开卡充值业绩-明细查询请求
type StatCardRechargeRequest struct {
	Date     string `form:"date"`              // 日期（格式yyyy-MM-dd）
	Limit    int    `form:"limit"`             // 每页显示条数，最大不能超过 100
	Offset   int    `form:"offset"`            // 分页起始下标
	ClinicID string `form:"clinicId,optional"` // 诊所ID
}

// StatMemberRechargeRequest 会员充值业绩-明细查询请求
type StatMemberRechargeRequest struct {
	Date     string `form:"date"`              // 日期（格式yyyy-MM-dd）
	Limit    int    `form:"limit"`             // 每页显示条数，最大不能超过 100
	Offset   int    `form:"offset"`            // 分页起始下标
	ClinicID string `form:"clinicId,optional"` // 诊所ID
}

// VisitSource 就诊来源信息
type VisitSource struct {
	ParentID       string `json:"parentId"`       // 父级ID
	ParentName     string `json:"parentName"`     // 父级名称
	ID             string `json:"id"`             // ID
	Name           string `json:"name"`           // 名称
	SourceFrom     string `json:"sourceFrom"`     // 来源
	SourceFromName string `json:"sourceFromName"` // 来源名称
	RelatedType    int    `json:"relatedType"`    // 关联类型
	RelatedID      string `json:"relatedId"`      // 关联ID
	RelateName     string `json:"relateName"`     // 关联名称
}

// StatRevenueDetailTransaction 收费明细-单据信息
type StatRevenueDetailTransaction struct {
	ClinicName                 string      `json:"clinicName"`                 // 诊所名称
	TransactionID              string      `json:"transactionId"`              // 交易ID
	ClinicID                   string      `json:"clinicId"`                   // 诊所ID
	PatientOrderID             string      `json:"patientOrderId"`             // 就诊单ID
	DepartmentName             string      `json:"departmentName"`             // 部门名称
	ReceivableFee              float64     `json:"receivableFee"`              // 应收费用
	ChargeSheetID              string      `json:"chargeSheetId"`              // 收费单ID
	DepartmentID               string      `json:"departmentId"`               // 部门ID
	EmployeeName               string      `json:"employeeName"`               // 员工名称
	ThirdPartyPayTransactionID string      `json:"thirdPartyPayTransactionId"` // 第三方支付交易ID
	EmployeeID                 string      `json:"employeeId"`                 // 员工ID
	VisitSource                VisitSource `json:"visitSource"`                // 就诊来源
	ChargeName                 string      `json:"chargeName"`                 // 收费人名称
	ChargeID                   string      `json:"chargeId"`                   // 收费人ID
	TotalPrice                 float64     `json:"totalPrice"`                 // 总价
	AdjustmentPrice            float64     `json:"adjustmentPrice"`            // 调整价格
	DiscountPrice              float64     `json:"discountPrice"`              // 折扣价格
	DeductPrice                float64     `json:"deductPrice"`                // 扣除价格
	Amount                     float64     `json:"amount"`                     // 金额
	Created                    string      `json:"created"`                    // 创建时间
	SourceType                 string      `json:"sourceType"`                 // 来源类型
	Action                     string      `json:"action"`                     // 操作
	PatientID                  string      `json:"patientId"`                  // 患者ID
	PatientName                string      `json:"patientName"`                // 患者名称
	No                         int         `json:"no"`                         // 编号
	PayMode                    string      `json:"payMode"`                    // 支付方式
	PayModeID                  int         `json:"payModeId"`                  // 支付方式ID
	PaySubModeID               int         `json:"paySubModeId"`               // 支付子方式ID
	Comment                    string      `json:"comment"`                    // 备注
	MemberPatientID            string      `json:"memberPatientId"`            // 会员患者ID
	MemberPatientName          string      `json:"memberPatientName"`          // 会员患者名称
	MemberPatientMobile        string      `json:"memberPatientMobile"`        // 会员患者手机号
}

// StatRevenueDetailClassify 收费明细-分类信息
type StatRevenueDetailClassify struct {
	ClinicName          string  `json:"clinicName"`          // 诊所名称
	FeeType             string  `json:"feeType"`             // 费用类型
	ClinicID            string  `json:"clinicId"`            // 诊所ID
	DepartmentName      string  `json:"departmentName"`      // 部门名称
	DepartmentID        string  `json:"departmentId"`        // 部门ID
	EmployeeName        string  `json:"employeeName"`        // 员工名称
	EmployeeID          string  `json:"employeeId"`          // 员工ID
	ChargeName          string  `json:"chargeName"`          // 收费人名称
	ChargeID            string  `json:"chargeId"`            // 收费人ID
	TotalPrice          float64 `json:"totalPrice"`          // 总价
	AdjustmentPrice     float64 `json:"adjustmentPrice"`     // 调整价格
	DiscountPrice       float64 `json:"discountPrice"`       // 折扣价格
	DeductPrice         float64 `json:"deductPrice"`         // 扣除价格
	Amount              float64 `json:"amount"`              // 金额
	Created             string  `json:"created"`             // 创建时间
	SourceType          string  `json:"sourceType"`          // 来源类型
	Action              string  `json:"action"`              // 操作
	PatientID           string  `json:"patientId"`           // 患者ID
	PatientName         string  `json:"patientName"`         // 患者名称
	No                  int     `json:"no"`                  // 编号
	PayMode             string  `json:"payMode"`             // 支付方式
	PayModeID           int     `json:"payModeId"`           // 支付方式ID
	PaySubModeID        int     `json:"paySubModeId"`        // 支付子方式ID
	Comment             string  `json:"comment"`             // 备注
	MemberPatientID     string  `json:"memberPatientId"`     // 会员患者ID
	MemberPatientName   string  `json:"memberPatientName"`   // 会员患者名称
	MemberPatientMobile string  `json:"memberPatientMobile"` // 会员患者手机号
}

// StatRevenueDetailItem 收费明细-明细信息
type StatRevenueDetailItem struct {
	ClinicName           string  `json:"clinicName"`           // 诊所名称
	PatientMobile        string  `json:"patientMobile"`        // 患者手机号
	ClinicID             string  `json:"clinicId"`             // 诊所ID
	ID                   string  `json:"id"`                   // ID
	DepartmentName       string  `json:"departmentName"`       // 部门名称
	Name                 string  `json:"name"`                 // 名称
	DepartmentID         string  `json:"departmentId"`         // 部门ID
	ProductID            string  `json:"productId"`            // 产品ID
	EmployeeName         string  `json:"employeeName"`         // 员工名称
	ProductShortID       string  `json:"productShortId"`       // 产品短ID
	Count                float64 `json:"count"`                // 数量
	EmployeeID           string  `json:"employeeId"`           // 员工ID
	ChargeName           string  `json:"chargeName"`           // 收费人名称
	Price                float64 `json:"price"`                // 价格
	ChargeID             string  `json:"chargeId"`             // 收费人ID
	FeeType1             string  `json:"feeType1"`             // 费用类型1
	FeeType2             string  `json:"feeType2"`             // 费用类型2
	TotalPrice           float64 `json:"totalPrice"`           // 总价
	AdjustmentPrice      float64 `json:"adjustmentPrice"`      // 调整价格
	ProductType          int     `json:"productType"`          // 产品类型
	DiscountPrice        float64 `json:"discountPrice"`        // 折扣价格
	ProductSubType       int     `json:"productSubType"`       // 产品子类型
	DeductPrice          float64 `json:"deductPrice"`          // 扣除价格
	ProductCustomType    int     `json:"productCustomType"`    // 产品自定义类型
	Amount               float64 `json:"amount"`               // 金额
	BatchID              int     `json:"batchId"`              // 批次ID
	BatchNo              string  `json:"batchNo"`              // 批次号
	Created              string  `json:"created"`              // 创建时间
	ExpiryDate           string  `json:"expiryDate"`           // 过期日期
	SourceType           string  `json:"sourceType"`           // 来源类型
	Action               string  `json:"action"`               // 操作
	ProductionDate       string  `json:"productionDate"`       // 生产日期
	ChargeSheetID        string  `json:"chargeSheetId"`        // 收费单ID
	PatientID            string  `json:"patientId"`            // 患者ID
	ChargeFormItemID     string  `json:"chargeFormItemId"`     // 收费表单项ID
	PatientName          string  `json:"patientName"`          // 患者名称
	ChargeTransactionID  string  `json:"chargeTransactionId"`  // 收费交易ID
	No                   int     `json:"no"`                   // 编号
	ItemAdjustmentAmount float64 `json:"itemAdjustmentAmount"` // 项目调整金额
	PayMode              string  `json:"payMode"`              // 支付方式
	PayModeID            int     `json:"payModeId"`            // 支付方式ID
	PaySubModeID         int     `json:"paySubModeId"`         // 支付子方式ID
	Comment              string  `json:"comment"`              // 备注
	MemberPatientID      string  `json:"memberPatientId"`      // 会员患者ID
	MemberPatientName    string  `json:"memberPatientName"`    // 会员患者名称
	MemberPatientMobile  string  `json:"memberPatientMobile"`  // 会员患者手机号
}

// StatExecuteDetailItem 执行业绩-明细
type StatExecuteDetailItem struct {
	ID                        string  `json:"id"`                        // ID
	PatientID                 string  `json:"patientId"`                 // 患者ID
	PatientName               string  `json:"patientName"`               // 患者名称
	PatientMobile             string  `json:"patientMobile"`             // 患者手机号
	ExecuteClinicID           string  `json:"executeClinicId"`           // 执行诊所ID
	ExecuteClinicName         string  `json:"executeClinicName"`         // 执行诊所名称
	ExecutorIDs               string  `json:"executorIds"`               // 执行人ID集合
	ExecutorNames             string  `json:"executorNames"`             // 执行人名称集合
	Status                    int     `json:"status"`                    // 状态
	ExecuteDate               string  `json:"executeDate"`               // 执行日期
	ProductID                 string  `json:"productId"`                 // 产品ID
	ProductShortID            string  `json:"productShortId"`            // 产品短ID
	ProductName               string  `json:"productName"`               // 产品名称
	ProductType               int     `json:"productType"`               // 产品类型
	ProductSubType            int     `json:"productSubType"`            // 产品子类型
	ExecuteCount              float64 `json:"executeCount"`              // 执行数量
	TotalPrice                float64 `json:"totalPrice"`                // 总价
	Amount                    float64 `json:"amount"`                    // 金额
	Comment                   string  `json:"comment"`                   // 备注
	RecordEffectInfo          string  `json:"recordEffectInfo"`          // 记录效果信息
	SheetCreateClinicID       string  `json:"sheetCreateClinicId"`       // 单据创建诊所ID
	SheetCreateClinicName     string  `json:"sheetCreateClinicName"`     // 单据创建诊所名称
	SheetCreatedBy            string  `json:"sheetCreatedBy"`            // 单据创建人
	SheetCreatedByName        string  `json:"sheetCreatedByName"`        // 单据创建人名称
	SheetCreateDepartmentID   string  `json:"sheetCreateDepartmentId"`   // 单据创建部门ID
	SheetCreateDepartmentName string  `json:"sheetCreateDepartmentName"` // 单据创建部门名称
	SheetCreated              string  `json:"sheetCreated"`              // 单据创建时间
	CreatedBy                 string  `json:"createdBy"`                 // 创建人
	CreatedByName             string  `json:"createdByName"`             // 创建人名称
	ChargeFormItemID          string  `json:"chargeFormItemId"`          // 收费表单项ID
	ChargeSheetID             string  `json:"chargeSheetId"`             // 收费单ID
}

// StatCardRechargeDetailItem 开卡充值业绩-明细
type StatCardRechargeDetailItem struct {
	ID                string  `json:"id"`                // ID
	ClinicID          string  `json:"clinicId"`          // 诊所ID
	RechargeTime      string  `json:"rechargeTime"`      // 充值时间
	PatientID         string  `json:"patientId"`         // 患者ID
	PatientMobile     string  `json:"patientMobile"`     // 患者手机号
	CardID            string  `json:"cardId"`            // 卡ID
	CardName          string  `json:"cardName"`          // 卡名称
	OperationType     int     `json:"operationType"`     // 操作类型
	ActualAmount      float64 `json:"actualAmount"`      // 实际金额
	RechargePrincipal float64 `json:"rechargePrincipal"` // 充值本金
	RechargePresent   float64 `json:"rechargePresent"`   // 充值赠送
	PrincipalBalance  float64 `json:"principalBalance"`  // 本金余额
	PresentBalance    float64 `json:"presentBalance"`    // 赠送余额
	SellerID          string  `json:"sellerId"`          // 销售ID
	SellerName        string  `json:"sellerName"`        // 销售名称
	CreatedByName     string  `json:"createdByName"`     // 创建人名称
	CreatedBy         string  `json:"createdBy"`         // 创建人
	OriginPrice       float64 `json:"originPrice"`       // 原价
}

// StatMemberRechargeDetailItem 会员充值业绩-明细
type StatMemberRechargeDetailItem struct {
	ID              string  `json:"id"`              // ID
	ClinicID        string  `json:"clinicId"`        // 诊所ID
	ClinicName      string  `json:"clinicName"`      // 诊所名称
	SellerID        string  `json:"sellerId"`        // 销售ID
	SellerName      string  `json:"sellerName"`      // 销售名称
	ChargeTime      string  `json:"chargeTime"`      // 充值时间
	ChargePrincipal float64 `json:"chargePrincipal"` // 充值本金
	ChargePresent   float64 `json:"chargePresent"`   // 充值赠送
	PatientID       string  `json:"patientId"`       // 患者ID
	PatientName     string  `json:"patientName"`     // 患者名称
	PatientMobile   string  `json:"patientMobile"`   // 患者手机号
	ChargerID       string  `json:"chargerId"`       // 充值人ID
	ChargerName     string  `json:"chargerName"`     // 充值人名称
	Remark          string  `json:"remark"`          // 备注
}

// PageDataResponse 分页查询结果
type PageDataResponse struct {
	Rows   interface{} `json:"rows"`   // 行
	Total  int         `json:"total"`  // 总数
	Offset int         `json:"offset"` // 偏移
	Limit  int         `json:"limit"`  // 限制
}

// StatResponse 统计接口通用响应
type StatResponse struct {
	Code    int         `json:"code"`    // 状态码
	Message string      `json:"message"` // 消息
	Data    interface{} `json:"data"`    // 数据
}
