package handler

import (
	"net/http"
	"yekaitai/wx_internal/modules/user/logic"
	"yekaitai/wx_internal/modules/user/types"
	"yekaitai/wx_internal/svc"
	wxTypes "yekaitai/wx_internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// GetPhoneNumberHandler 获取手机号
func GetPhoneNumberHandler(ctx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetPhoneNumberReq
		if err := httpx.Parse(r, &req); err != nil {
			logx.Errorf("解析获取手机号请求参数失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, wxTypes.NewErrorResponse(wxTypes.CodeInvalidParams, "无效的请求参数"))
			return
		}

		// 验证请求参数
		if req.Code == "" {
			logx.Error("获取手机号失败: code为空")
			httpx.WriteJson(w, http.StatusOK, wxTypes.NewErrorResponse(wxTypes.CodeInvalidParams, "手机号获取凭证不能为空"))
			return
		}

		l := logic.NewGetPhoneNumberLogic(r.Context(), ctx)
		resp, err := l.GetPhoneNumber(&req)
		if err != nil {
			logx.Errorf("获取手机号失败: %v", err)
			httpx.WriteJson(w, http.StatusOK, wxTypes.NewErrorResponse(wxTypes.CodeInternalError, err.Error()))
			return
		}

		httpx.WriteJson(w, http.StatusOK, wxTypes.NewSuccessResponse(resp))
	}
}
