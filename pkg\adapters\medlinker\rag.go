package medlinker

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"sort"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"
)

// RAGConfig RAG相关配置
type RAGConfig struct {
	BaseURL    string `json:"baseURL"`
	AppID      string `json:"appID"`
	AppSecret  string `json:"appSecret"`
	HospitalID string `json:"hospitalID"`
}

// KnowledgeBaseRequest 知识库请求
type KnowledgeBaseRequest struct {
	AppID       string `json:"app_id"`
	Token       string `json:"token"`
	Name        string `json:"name"`
	Description string `json:"description"`
	HospitalID  string `json:"hospital_id"`
}

// UpdateKnowledgeBaseRequest 更新知识库请求
type UpdateKnowledgeBaseRequest struct {
	AppID       string `json:"app_id"`
	Token       string `json:"token"`
	KnowledgeID string `json:"knowledge_id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	HospitalID  string `json:"hospital_id"`
}

// DocumentRequest 文档请求
type DocumentRequest struct {
	AppID       string `json:"app_id"`
	Token       string `json:"token"`
	KnowledgeID string `json:"knowledge_id"`
	FilePath    string `json:"file_path"`
	FileName    string `json:"file_name"`
}

// KnowledgeBaseResponse 知识库响应
type KnowledgeBaseResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		KnowledgeID string `json:"knowledge_id"`
	} `json:"data"`
}

// DocumentResponse 文档响应
type DocumentResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		DocumentID string `json:"document_id"`
	} `json:"data"`
}

// GenerateToken 生成医联鉴权token
func GenerateToken(data map[string]interface{}, appSecret string) string {
	// 获取所有参数名（除了token）
	keys := make([]string, 0)
	for k := range data {
		if k == "token" {
			continue
		}
		keys = append(keys, k)
	}

	// 按ASCII码排序
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})

	// 构建签名字符串
	list := make([]string, 0)
	for _, key := range keys {
		list = append(list, fmt.Sprintf("%s=%v", key, data[key]))
	}
	list = append(list, fmt.Sprintf("key=%s", appSecret))

	tmpSignString := strings.Join(list, "&")
	logx.Infof("医联签名字符串: %s", tmpSignString)

	// MD5加密并转大写
	d := md5.Sum([]byte(tmpSignString))
	token := strings.ToUpper(hex.EncodeToString(d[:]))

	return token
}

// CreateKnowledgeBase 创建知识库
func (c *medlinkerClient) CreateKnowledgeBase(config RAGConfig, name, description string) (string, error) {
	url := fmt.Sprintf("%s/api/med/rag/add_knowledge", config.BaseURL)

	// 准备请求数据
	reqData := map[string]interface{}{
		"app_id":      config.AppID,
		"name":        name,
		"description": description,
		"hospital_id": config.HospitalID,
	}

	// 生成token
	token := GenerateToken(reqData, config.AppSecret)
	reqData["token"] = token

	// 序列化请求数据
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		return "", fmt.Errorf("序列化请求数据失败: %w", err)
	}

	logx.Infof("医联创建知识库请求: %s", string(reqBody))

	// 发送HTTP请求
	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Post(url, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("请求医联服务器失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	logx.Infof("医联创建知识库响应: %s", string(respBody))

	// 解析响应
	var result KnowledgeBaseResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return "", fmt.Errorf("解析响应失败: %w", err)
	}

	if result.Code != 200 {
		return "", fmt.Errorf("医联服务错误: %s (错误码: %d)", result.Msg, result.Code)
	}

	return result.Data.KnowledgeID, nil
}

// UpdateKnowledgeBase 更新知识库
func (c *medlinkerClient) UpdateKnowledgeBase(config RAGConfig, knowledgeID, name, description string) error {
	url := fmt.Sprintf("%s/api/med/rag/update_knowledge", config.BaseURL)

	// 准备请求数据
	reqData := map[string]interface{}{
		"app_id":       config.AppID,
		"knowledge_id": knowledgeID,
		"name":         name,
		"description":  description,
		"hospital_id":  config.HospitalID,
	}

	// 生成token
	token := GenerateToken(reqData, config.AppSecret)
	reqData["token"] = token

	// 序列化请求数据
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 发送HTTP请求
	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Post(url, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		return fmt.Errorf("请求医联服务器失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	logx.Infof("医联更新知识库响应: %s", string(respBody))

	// 解析响应
	var result KnowledgeBaseResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if result.Code != 200 {
		return fmt.Errorf("医联服务错误: %s (错误码: %d)", result.Msg, result.Code)
	}

	return nil
}

// AddDocument 添加文档到知识库
func (c *medlinkerClient) AddDocument(config RAGConfig, knowledgeID, filePath, fileName string) (string, error) {
	url := fmt.Sprintf("%s/api/med/rag/add_document", config.BaseURL)

	logx.Infof("医联添加文档开始: knowledgeID=%s, filePath=%s, fileName=%s", knowledgeID, filePath, fileName)

	// 检查文件格式，医联可能只支持特定格式
	supportedFormats := []string{".pdf", ".doc", ".docx", ".md"}
	fileExt := strings.ToLower(filepath.Ext(fileName))
	if fileExt == ".txt" {
		// 将txt文件名改为md格式，可能医联更支持markdown
		fileName = strings.TrimSuffix(fileName, ".txt") + ".md"
		logx.Infof("将txt文件格式转换为md: %s", fileName)
	}

	logx.Infof("文件格式检查: 原始=%s, 最终文件名=%s, 支持格式=%v", fileExt, fileName, supportedFormats)

	// 准备请求数据
	reqData := map[string]interface{}{
		"app_id":       config.AppID,
		"knowledge_id": knowledgeID,
		"file_path":    filePath,
		"file_name":    fileName,
	}

	// 检查文件URL是否可访问
	if err := c.checkFileAccessibility(filePath); err != nil {
		logx.Errorf("文件URL不可访问: %v", err)
		return "", fmt.Errorf("文件URL不可访问: %w", err)
	}

	// 生成token
	token := GenerateToken(reqData, config.AppSecret)
	reqData["token"] = token

	// 序列化请求数据
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		return "", fmt.Errorf("序列化请求数据失败: %w", err)
	}

	logx.Infof("医联添加文档请求: %s", string(reqBody))

	// 发送HTTP请求
	httpClient := &http.Client{Timeout: 60 * time.Second} // 文档上传可能需要更长时间
	resp, err := httpClient.Post(url, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("请求医联服务器失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	logx.Infof("医联添加文档响应: %s", string(respBody))

	// 解析响应
	var result DocumentResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return "", fmt.Errorf("解析响应失败: %w", err)
	}

	if result.Code != 200 {
		return "", fmt.Errorf("医联服务错误: %s (错误码: %d)", result.Msg, result.Code)
	}

	return result.Data.DocumentID, nil
}

// DeleteDocument 删除文档
func (c *medlinkerClient) DeleteDocument(config RAGConfig, documentID string) error {
	url := fmt.Sprintf("%s/api/med/rag/delete_document", config.BaseURL)

	// 准备请求数据
	reqData := map[string]interface{}{
		"app_id":      config.AppID,
		"document_id": documentID,
	}

	// 生成token
	token := GenerateToken(reqData, config.AppSecret)
	reqData["token"] = token

	// 序列化请求数据
	reqBody, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(reqData)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 发送HTTP请求
	httpClient := &http.Client{Timeout: 30 * time.Second}
	resp, err := httpClient.Post(url, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		return fmt.Errorf("请求医联服务器失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	logx.Infof("医联删除文档响应: %s", string(respBody))

	// 解析响应
	var result map[string]interface{}
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(respBody, &result); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if code, ok := result["code"].(float64); !ok || int(code) != 200 {
		msg := "未知错误"
		if msgStr, ok := result["msg"].(string); ok {
			msg = msgStr
		}
		return fmt.Errorf("医联服务错误: %s", msg)
	}

	return nil
}

// checkFileAccessibility 检查文件URL是否可访问
func (c *medlinkerClient) checkFileAccessibility(fileURL string) error {
	logx.Infof("检查文件URL可访问性: %s", fileURL)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 发送HEAD请求检查文件是否存在
	resp, err := client.Head(fileURL)
	if err != nil {
		return fmt.Errorf("无法访问文件URL: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("文件URL返回错误状态码: %d", resp.StatusCode)
	}

	// 检查Content-Type
	contentType := resp.Header.Get("Content-Type")
	logx.Infof("文件URL可访问，Content-Type: %s, Content-Length: %s",
		contentType, resp.Header.Get("Content-Length"))

	return nil
}
