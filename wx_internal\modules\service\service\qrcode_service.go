package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"yekaitai/internal/modules/service_setup/model"
	"yekaitai/pkg/hmac"
	"yekaitai/pkg/infra/mysql"
	qrcodeService "yekaitai/wx_internal/modules/qrcode-management/service"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// ServiceQRCodeService 服务二维码服务
type ServiceQRCodeService struct{}

// NewServiceQRCodeService 创建服务二维码服务实例
func NewServiceQRCodeService() *ServiceQRCodeService {
	return &ServiceQRCodeService{}
}

// GenerateServiceQRCode 生成服务订单二维码
func (s *ServiceQRCodeService) GenerateServiceQRCode(ctx context.Context, orderID uint) (string, error) {
	// 查询订单信息
	var order struct {
		ID               uint   `gorm:"column:id"`
		OrderNo          string `gorm:"column:order_no"`
		VerificationCode string `gorm:"column:verification_code"`
		Status           string `gorm:"column:status"`
	}

	err := mysql.Slave().Table("service_orders").
		Where("id = ?", orderID).
		First(&order).Error
	if err != nil {
		logx.Errorf("查询服务订单失败: %v", err)
		return "", fmt.Errorf("订单不存在")
	}

	// 检查订单状态
	if order.Status != "paid" { // paid-已支付
		return "", fmt.Errorf("订单状态不允许生成二维码")
	}

	// 如果没有核销码，生成一个
	if order.VerificationCode == "" {
		order.VerificationCode = s.GenerateVerificationCode(orderID)

		// 更新订单的核销码
		err = mysql.Master().Table("service_orders").
			Where("id = ?", orderID).
			Update("verification_code", order.VerificationCode).Error
		if err != nil {
			logx.Errorf("更新订单核销码失败: %v", err)
			return "", fmt.Errorf("生成核销码失败")
		}
	}

	// 构建二维码数据 - 包含完整参数
	qrData := map[string]interface{}{
		"type":              "service_order",                              // 标识为服务订单类型
		"order_id":          orderID,                                      // 订单ID
		"order_no":          order.OrderNo,                                // 订单号
		"verification_code": order.VerificationCode,                       // 核销码
		"timestamp":         strconv.FormatInt(time.Now().UnixNano(), 10), // 时间戳
	}

	// 生成签名（基于order_no + verification_code）
	signatureData := fmt.Sprintf("%s%s", order.OrderNo, order.VerificationCode)
	sign := hmac.GenerateHMAC(signatureData)
	qrData["sign"] = sign

	// 生成二维码
	qrCodeURL, err := qrcodeService.GenerateQRCodeWithData(ctx, qrData)
	if err != nil {
		logx.Errorf("生成二维码失败: %v", err)
		return "", fmt.Errorf("生成二维码失败")
	}

	// 更新订单的二维码URL
	err = mysql.Master().Table("service_orders").
		Where("id = ?", orderID).
		Update("qr_code_url", qrCodeURL).Error
	if err != nil {
		logx.Errorf("更新订单二维码URL失败: %v", err)
		// 这里不返回错误，因为二维码已经生成成功
	}

	return qrCodeURL, nil
}

// VerifyServiceCode 验证服务核销码
func (s *ServiceQRCodeService) VerifyServiceCode(ctx context.Context, orderNo, verificationCode string, verifierID uint, verifierName string) error {
	// 查询订单详情
	var order struct {
		ID               uint       `gorm:"column:id"`
		OrderNo          string     `gorm:"column:order_no"`
		ServicePackageID uint       `gorm:"column:service_package_id"`
		Status           string     `gorm:"column:status"`
		VerificationTime *time.Time `gorm:"column:verification_time"`
	}

	err := mysql.Slave().Table("service_orders").
		Where("order_no = ? AND verification_code = ?", orderNo, verificationCode).
		First(&order).Error
	if err != nil {
		logx.Errorf("查询服务订单失败: %v", err)
		return fmt.Errorf("订单不存在或核销码错误")
	}

	// 检查订单状态
	if order.Status != "paid" { // paid-已支付
		return fmt.Errorf("订单状态不允许核销")
	}

	// 检查是否已经核销
	if order.VerificationTime != nil {
		return fmt.Errorf("订单已经核销")
	}

	// 验证核销员身份和权限
	err = s.validateRedeemer(ctx, verifierID, order.ServicePackageID)
	if err != nil {
		logx.Errorf("核销员验证失败: %v", err)
		return err
	}

	// 执行核销
	now := time.Now()
	err = mysql.Master().Table("service_orders").
		Where("id = ?", order.ID).
		Updates(map[string]interface{}{
			"status":            "completed", // completed-已完成（已核销）
			"verification_time": &now,
			"verifier_id":       verifierID,
			"verifier_name":     verifierName,
			"updated_at":        now,
		}).Error

	if err != nil {
		logx.Errorf("更新订单核销状态失败: %v", err)
		return fmt.Errorf("核销失败")
	}

	logx.Infof("服务订单核销成功: order_no=%s, verifier_id=%d", orderNo, verifierID)
	return nil
}

// validateRedeemer 验证核销员身份和权限
func (s *ServiceQRCodeService) validateRedeemer(ctx context.Context, redeemerID uint, servicePackageID uint) error {
	// 查询核销员信息
	var redeemer struct {
		RedeemerID uint `gorm:"column:redeemer_id"`
		UserID     uint `gorm:"column:user_id"`
		StoreID    uint `gorm:"column:store_id"`
		Status     int  `gorm:"column:status"`
	}

	err := mysql.Slave().Table("wx_redeemer").
		Where("user_id = ? AND deleted_at IS NULL", redeemerID).
		First(&redeemer).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("核销员身份不存在")
		}
		return fmt.Errorf("查询核销员信息失败: %w", err)
	}

	// 检查核销员状态
	if redeemer.Status != 1 {
		return fmt.Errorf("核销员已被禁用")
	}

	// 查询服务套餐绑定的门店
	var storeIDs []uint
	err = mysql.Slave().Table("service_package_store_relations").
		Where("service_package_id = ?", servicePackageID).
		Pluck("store_id", &storeIDs).Error
	if err != nil {
		return fmt.Errorf("查询服务套餐门店信息失败: %w", err)
	}

	// 检查核销员是否有权限核销该服务
	hasPermission := false
	for _, storeID := range storeIDs {
		if storeID == redeemer.StoreID {
			hasPermission = true
			break
		}
	}

	if !hasPermission {
		return fmt.Errorf("核销员无权限核销该服务")
	}

	return nil
}

// ValidateQRCodeData 验证二维码数据
func (s *ServiceQRCodeService) ValidateQRCodeData(data map[string]interface{}) error {
	// 检查必要字段
	orderNo, ok := data["order_no"].(string)
	if !ok || orderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}

	verificationCode, ok := data["verification_code"].(string)
	if !ok || verificationCode == "" {
		return fmt.Errorf("核销码不能为空")
	}

	sign, ok := data["sign"].(string)
	if !ok || sign == "" {
		return fmt.Errorf("签名不能为空")
	}

	// 验证签名
	expectedSign := hmac.GenerateHMAC(orderNo + verificationCode)
	if sign != expectedSign {
		return fmt.Errorf("签名验证失败")
	}

	return nil
}

// GetServiceOrderByQRCode 根据二维码数据获取服务订单
func (s *ServiceQRCodeService) GetServiceOrderByQRCode(ctx context.Context, data map[string]interface{}) (*model.ServiceOrder, error) {
	// 验证二维码数据
	if err := s.ValidateQRCodeData(data); err != nil {
		return nil, err
	}

	orderNo := data["order_no"].(string)
	verificationCode := data["verification_code"].(string)

	// 查询订单
	var order model.ServiceOrder
	err := mysql.Slave().Where("order_no = ? AND verification_code = ?", orderNo, verificationCode).First(&order).Error
	if err != nil {
		logx.Errorf("查询服务订单失败: %v", err)
		return nil, fmt.Errorf("订单不存在或核销码错误")
	}

	return &order, nil
}

// GenerateVerificationCode 生成核销码
func (s *ServiceQRCodeService) GenerateVerificationCode(orderID uint) string {
	return fmt.Sprintf("SVC%d%d", orderID, time.Now().Unix())
}

// IsValidVerificationCode 验证核销码格式
func (s *ServiceQRCodeService) IsValidVerificationCode(code string) bool {
	if len(code) < 10 {
		return false
	}

	// 检查是否以SVC开头
	if len(code) >= 3 && code[:3] == "SVC" {
		return true
	}

	return false
}
