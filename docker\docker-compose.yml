version: '3.8'

services:
  # 测试环境
  yekaitai-test:
    image: yekaitai:latest
    container_name: yekaitai-test
    environment:
      - APP_NAME=yekaitai
      - ENV=test
      - RUN_ENV=test
      - LOG_ROOT=/var/log/medlinker
      - LOG_PATH=/var/log/medlinker/yekaitai
    volumes:
      - /var/log/medlinker:/var/log/medlinker
      - ./etc:/var/www/yekaitai/etc
    ports:
      - "8888:8888"
      - "8889:8889"
    command: ["/var/www/yekaitai/yekaitai_admin", "-f", "/var/www/yekaitai/etc/yekaitai-test.yaml", "-service", "all"]
    restart: unless-stopped
    networks:
      - yekaitai-network

  # 生产环境
  yekaitai-prod:
    image: yekaitai:latest
    container_name: yekaitai-prod
    environment:
      - APP_NAME=yekaitai
      - ENV=prod
      - RUN_ENV=prod
      - LOG_ROOT=/var/log/medlinker
      - LOG_PATH=/var/log/medlinker/yekaitai
    volumes:
      - /var/log/medlinker:/var/log/medlinker
      - ./etc:/var/www/yekaitai/etc
    ports:
      - "8888:8888"
      - "8889:8889"
    command: ["/var/www/yekaitai/yekaitai_admin", "-f", "/var/www/yekaitai/etc/yekaitai-prod.yaml", "-service", "all"]
    restart: unless-stopped
    networks:
      - yekaitai-network

  # 开发环境
  yekaitai-dev:
    image: yekaitai:latest
    container_name: yekaitai-dev
    environment:
      - APP_NAME=yekaitai
      - ENV=dev
      - RUN_ENV=dev
      - LOG_PATH=logs  # 开发环境使用相对路径
    volumes:
      - ./logs:/var/www/yekaitai/logs
      - ./etc:/var/www/yekaitai/etc
    ports:
      - "8888:8888"
      - "8889:8889"
    command: ["/var/www/yekaitai/yekaitai_admin", "-f", "/var/www/yekaitai/etc/yekaitai-dev.yaml", "-service", "all"]
    restart: unless-stopped
    networks:
      - yekaitai-network

networks:
  yekaitai-network:
    driver: bridge
