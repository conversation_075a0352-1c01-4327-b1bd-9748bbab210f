package order

import (
	"time"

	"gorm.io/gorm"
)

// Order 订单表
type Order struct {
	ID      uint   `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	OrderNo string `gorm:"column:order_no;type:varchar(32);unique;comment:订单号" json:"order_no"`
	UserID  uint   `gorm:"column:user_id;index;comment:用户ID" json:"user_id"`
	Status  int    `gorm:"column:status;default:1;comment:订单状态(1待支付,2已支付,3已发货,4已完成,5已取消,6已退款)" json:"status"`

	// 支付状态字段（兼容管理后台）
	PaymentStatus  int `gorm:"column:payment_status;default:0;comment:支付状态(0未付款,1已付款,2已退款)" json:"payment_status"`
	ShippingStatus int `gorm:"column:shipping_status;default:0;comment:配送状态(0未发货,1已发货,2已送达)" json:"shipping_status"`

	// 金额相关字段
	TotalAmount    float64 `gorm:"column:total_amount;comment:订单总金额" json:"total_amount"`
	GoodsAmount    float64 `gorm:"column:goods_amount;comment:商品金额" json:"goods_amount"`
	ShippingAmount float64 `gorm:"column:shipping_amount;comment:运费" json:"shipping_amount"`
	ShippingFee    float64 `gorm:"column:shipping_fee;type:decimal(10,2);default:0;comment:运费(别名)" json:"shipping_fee"` // 兼容管理后台
	DiscountAmount float64 `gorm:"column:discount_amount;type:decimal(10,2);default:0;comment:优惠金额" json:"discount_amount"`
	CouponAmount   float64 `gorm:"column:coupon_amount;default:0;comment:优惠券抵扣金额" json:"coupon_amount"`
	PointsAmount   float64 `gorm:"column:points_amount;default:0;comment:积分抵扣金额" json:"points_amount"`
	PayAmount      float64 `gorm:"column:pay_amount;comment:实际支付金额" json:"pay_amount"`

	// 支付相关字段
	PayMethod           string     `gorm:"column:pay_method;type:varchar(20);comment:支付方式" json:"pay_method"`
	PaymentMethod       int        `gorm:"column:payment_method;default:1;comment:支付方式(1微信支付,2支付宝,3现金)" json:"payment_method"` // 兼容管理后台
	PayTime             *time.Time `gorm:"column:pay_time;comment:支付时间" json:"pay_time"`
	PaymentTime         *time.Time `gorm:"column:payment_time;comment:支付时间(别名)" json:"payment_time"` // 兼容管理后台
	WechatPayOrderID    string     `gorm:"column:wechat_pay_order_id;type:varchar(64);comment:微信支付订单号" json:"wechat_pay_order_id"`
	WechatTransactionID string     `gorm:"column:wechat_transaction_id;type:varchar(64);comment:微信交易号" json:"wechat_transaction_id"` // 微信交易号
	WechatPrepayID      string     `gorm:"column:wechat_prepay_id;type:varchar(64);comment:微信预支付ID" json:"wechat_prepay_id"`

	// 地址相关字段
	AddressID   uint   `gorm:"column:address_id;comment:收货地址ID" json:"address_id"`
	AddressInfo string `gorm:"column:address_info;type:text;comment:收货地址信息快照" json:"address_info"`

	// 优惠相关字段
	CouponID  uint `gorm:"column:coupon_id;default:0;comment:使用的优惠券ID" json:"coupon_id"`
	UsePoints int  `gorm:"column:use_points;default:0;comment:使用的积分数量" json:"use_points"`

	// 备注和取消字段
	Remark       string     `gorm:"column:remark;type:varchar(500);comment:订单备注" json:"remark"`
	CancelReason string     `gorm:"column:cancel_reason;type:varchar(200);comment:取消原因" json:"cancel_reason"`
	CancelTime   *time.Time `gorm:"column:cancel_time;comment:取消时间" json:"cancel_time"`

	// 时间字段
	ShipTime     *time.Time `gorm:"column:ship_time;comment:发货时间" json:"ship_time"`
	ShippingTime *time.Time `gorm:"column:shipping_time;comment:发货时间(别名)" json:"shipping_time"` // 兼容管理后台
	FinishTime   *time.Time `gorm:"column:finish_time;comment:完成时间" json:"finish_time"`
	ReceiveTime  *time.Time `gorm:"column:receive_time;comment:收货时间" json:"receive_time"`

	// 退款相关字段
	RefundStatus int        `gorm:"column:refund_status;default:0;comment:退款状态(0无退款,1申请退款,2退款中,3已退款)" json:"refund_status"`
	RefundAmount float64    `gorm:"column:refund_amount;default:0;comment:退款金额" json:"refund_amount"`
	RefundReason string     `gorm:"column:refund_reason;type:varchar(200);comment:退款原因" json:"refund_reason"`
	RefundTime   *time.Time `gorm:"column:refund_time;comment:退款时间" json:"refund_time"`
	RefundNo     string     `gorm:"column:refund_no;type:varchar(64);comment:退款单号" json:"refund_no"`

	// 物流相关字段
	ExpressCompany string `gorm:"column:express_company;type:varchar(50);comment:快递公司" json:"express_company"`
	ExpressNo      string `gorm:"column:express_no;type:varchar(50);comment:快递单号" json:"express_no"`

	// 万里牛推送相关字段
	WanLiNiuOrderPushStatus  int        `gorm:"column:wanliniu_order_push_status;default:0;comment:万里牛订单推送状态(0未推送,1推送成功,2推送失败)" json:"wanliniu_order_push_status"`
	WanLiNiuRefundPushStatus int        `gorm:"column:wanliniu_refund_push_status;default:0;comment:万里牛售后单推送状态(0未推送,1推送成功,2推送失败)" json:"wanliniu_refund_push_status"`
	WanLiNiuOrderPushCount   int        `gorm:"column:wanliniu_order_push_count;default:0;comment:万里牛订单推送失败次数" json:"wanliniu_order_push_count"`
	WanLiNiuRefundPushCount  int        `gorm:"column:wanliniu_refund_push_count;default:0;comment:万里牛售后单推送失败次数" json:"wanliniu_refund_push_count"`
	WanLiNiuLastPushTime     *time.Time `gorm:"column:wanliniu_last_push_time;comment:万里牛最后推送时间" json:"wanliniu_last_push_time"`
	WanLiNiuPushError        string     `gorm:"column:wanliniu_push_error;type:text;comment:万里牛推送错误信息" json:"wanliniu_push_error"`

	// 时间戳字段
	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`

	// 关联关系
	Items      []OrderItem `gorm:"foreignKey:OrderID" json:"items,omitempty"`
	OrderItems []OrderItem `gorm:"foreignKey:OrderID" json:"order_items,omitempty"` // 兼容管理后台
}

// TableName 设置表名
func (Order) TableName() string {
	return "orders"
}

// OrderItem 订单商品表
type OrderItem struct {
	ID         uint           `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	OrderID    uint           `gorm:"column:order_id;index;comment:订单ID" json:"order_id"`
	GoodsID    uint           `gorm:"column:goods_id;comment:商品ID" json:"goods_id"`
	SpecID     uint           `gorm:"column:spec_id;default:0;comment:规格ID" json:"spec_id"`
	GoodsName  string         `gorm:"column:goods_name;type:varchar(255);comment:商品名称" json:"goods_name"`
	SpecName   string         `gorm:"column:spec_name;type:varchar(100);comment:规格名称" json:"spec_name"`
	GoodsPic   string         `gorm:"column:goods_pic;type:varchar(500);comment:商品图片" json:"goods_pic"`
	Price      float64        `gorm:"column:price;comment:商品单价" json:"price"`
	Quantity   int            `gorm:"column:quantity;comment:购买数量" json:"quantity"`
	TotalPrice float64        `gorm:"column:total_price;comment:小计金额" json:"total_price"`
	CreatedAt  time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt  time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (OrderItem) TableName() string {
	return "order_items"
}

// OrderStatus 订单状态常量
const (
	OrderStatusPending   = 1 // 待支付
	OrderStatusPaid      = 2 // 已支付
	OrderStatusShipped   = 3 // 已发货
	OrderStatusCompleted = 4 // 已完成
	OrderStatusCancelled = 5 // 已取消
	OrderStatusRefunded  = 6 // 已退款
)

// RefundStatus 退款状态常量
const (
	RefundStatusNone       = 0 // 无退款
	RefundStatusApplying   = 1 // 申请退款
	RefundStatusProcessing = 2 // 退款中
	RefundStatusCompleted  = 3 // 已退款
)

// WanLiNiuPushStatus 万里牛推送状态常量
const (
	WanLiNiuPushStatusNone    = 0 // 未推送
	WanLiNiuPushStatusSuccess = 1 // 推送成功
	WanLiNiuPushStatusFailed  = 2 // 推送失败
)

// GetOrderStatusText 获取订单状态文本
func GetOrderStatusText(status int) string {
	switch status {
	case OrderStatusPending:
		return "待支付"
	case OrderStatusPaid:
		return "已支付"
	case OrderStatusShipped:
		return "已发货"
	case OrderStatusCompleted:
		return "已完成"
	case OrderStatusCancelled:
		return "已取消"
	case OrderStatusRefunded:
		return "已退款"
	default:
		return "未知状态"
	}
}

// GetRefundStatusText 获取退款状态文本
func GetRefundStatusText(status int) string {
	switch status {
	case RefundStatusNone:
		return "无退款"
	case RefundStatusApplying:
		return "申请退款"
	case RefundStatusProcessing:
		return "退款中"
	case RefundStatusCompleted:
		return "已退款"
	default:
		return "未知状态"
	}
}

// GetRefundButtonStatus 获取退款按钮状态
func (o *Order) GetRefundButtonStatus(isShipped bool) RefundButtonStatus {
	// 如果订单状态不是已支付，不能退款
	if o.Status != OrderStatusPaid {
		var reason string
		switch o.Status {
		case OrderStatusPending:
			reason = "订单未支付，无法退款"
		case OrderStatusCancelled:
			reason = "订单已取消"
		case OrderStatusRefunded:
			reason = "订单已退款"
		case OrderStatusCompleted:
			reason = "订单已完成"
		default:
			reason = "订单状态异常"
		}
		return RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "不可退款",
			DisableReason: reason,
			RefundStatus:  o.RefundStatus,
		}
	}

	// 如果已发货，不能退款
	if isShipped {
		return RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "不可退款",
			DisableReason: "商品已发货，无法退款",
			RefundStatus:  o.RefundStatus,
		}
	}

	// 根据退款状态返回按钮状态
	switch o.RefundStatus {
	case RefundStatusNone:
		return RefundButtonStatus{
			CanRefund:     true,
			ButtonText:    "申请退款",
			DisableReason: "",
			RefundStatus:  o.RefundStatus,
		}
	case RefundStatusApplying:
		return RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "退款申请中",
			DisableReason: "退款申请正在处理中",
			RefundStatus:  o.RefundStatus,
		}
	case RefundStatusProcessing:
		return RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "退款中",
			DisableReason: "退款正在处理中",
			RefundStatus:  o.RefundStatus,
		}
	case RefundStatusCompleted:
		return RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "已退款",
			DisableReason: "已完成退款",
			RefundStatus:  o.RefundStatus,
		}
	default:
		return RefundButtonStatus{
			CanRefund:     false,
			ButtonText:    "不可退款",
			DisableReason: "退款状态异常",
			RefundStatus:  o.RefundStatus,
		}
	}
}

// CanApplyRefund 判断是否可以申请退款
func (o *Order) CanApplyRefund(isShipped bool) (bool, string) {
	// 只有已支付且未发货的订单可以申请退款
	if o.Status != OrderStatusPaid {
		return false, "订单状态不支持退款"
	}

	if isShipped {
		return false, "商品已发货，无法退款"
	}

	if o.RefundStatus != RefundStatusNone {
		return false, "退款申请已存在"
	}

	return true, ""
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	CartIDs   []uint   `json:"cart_ids" validate:"required,min=1"`   // 购物车商品ID列表
	AddressID uint     `json:"address_id" validate:"required,min=1"` // 收货地址ID
	CouponIDs []string `json:"coupon_ids,omitempty"`                 // 优惠券ID列表（支持多张）
	UsePoints bool     `json:"use_points,omitempty"`                 // 是否使用积分
	Remark    string   `json:"remark,omitempty"`                     // 订单备注（可选）
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	OrderID       uint              `json:"order_id"`
	OrderNo       string            `json:"order_no"`
	TotalAmount   float64           `json:"total_amount"`
	PayAmount     float64           `json:"pay_amount"`
	PaymentParams map[string]string `json:"payment_params"` // 微信支付参数
	NeedPayment   bool              `json:"need_payment"`   // 是否需要支付
}

// OrderDetailResponse 订单详情响应
type OrderDetailResponse struct {
	Order
	Items              []OrderItem         `json:"items"`                // 订单商品列表
	StatusText         string              `json:"status_text"`          // 订单状态文本
	RefundStatusText   string              `json:"refund_status_text"`   // 退款状态文本
	CanRefund          bool                `json:"can_refund"`           // 是否可以退款
	CanCancel          bool                `json:"can_cancel"`           // 是否可以取消
	CanViewLogistics   bool                `json:"can_view_logistics"`   // 是否可以查看物流
	CanConfirmReceive  bool                `json:"can_confirm_receive"`  // 是否可以确认收货
	RefundButtonStatus *RefundButtonStatus `json:"refund_button_status"` // 退款按钮状态
}

// RefundOrderRequest 申请订单退款请求
type RefundOrderRequest struct {
	OrderID      uint    `json:"order_id" validate:"required"`
	RefundAmount float64 `json:"refund_amount" validate:"required,min=0.01"`
	RefundReason string  `json:"refund_reason" validate:"required"`
}

// RefundOrderResponse 申请订单退款响应
type RefundOrderResponse struct {
	RefundNo string `json:"refund_no"`
	Status   string `json:"status"`
	Message  string `json:"message"`
}

// RefundStatusResponse 退款状态响应
type RefundStatusResponse struct {
	OrderID          uint       `json:"order_id"`
	OrderNo          string     `json:"order_no"`
	RefundStatus     int        `json:"refund_status"`
	RefundStatusText string     `json:"refund_status_text"`
	RefundAmount     float64    `json:"refund_amount"`
	RefundReason     string     `json:"refund_reason"`
	RefundNo         string     `json:"refund_no"`
	RefundTime       *time.Time `json:"refund_time"`
}

// RefundButtonStatus 退款按钮状态
type RefundButtonStatus struct {
	CanRefund     bool   `json:"can_refund"`     // 是否可以退款
	ButtonText    string `json:"button_text"`    // 按钮文本
	DisableReason string `json:"disable_reason"` // 禁用原因（当不可退款时）
	RefundStatus  int    `json:"refund_status"`  // 当前退款状态
}

// ShippingStatusResponse 发货状态响应
type ShippingStatusResponse struct {
	IsShipped      bool   `json:"is_shipped"`      // 是否已发货
	ShipTime       string `json:"ship_time"`       // 发货时间
	ExpressCompany string `json:"express_company"` // 快递公司
	ExpressNo      string `json:"express_no"`      // 快递单号
	CanRefund      bool   `json:"can_refund"`      // 是否可以退款
}

// ShippingAddress 收货地址结构（兼容管理后台）
type ShippingAddress struct {
	Name      string `json:"name"`
	Phone     string `json:"phone"`
	Province  string `json:"province"`
	City      string `json:"city"`
	District  string `json:"district"`
	Address   string `json:"address"`
	IsDefault bool   `json:"is_default"`
}

// OrderCreateRequest 创建订单请求（兼容管理后台）
type OrderCreateRequest struct {
	CartIDs       []uint `json:"cart_ids" validate:"required"`
	AddressID     uint   `json:"address_id" validate:"required"`
	PaymentMethod int    `json:"payment_method,optional" validate:"oneof=1 2 3"`
	Remark        string `json:"remark,optional"`
}

// OrderUpdateRequest 更新订单请求（兼容管理后台）
type OrderUpdateRequest struct {
	Status       int    `json:"status,optional" validate:"oneof=1 2 3 4 5 6"`
	CancelReason string `json:"cancel_reason,optional"`
	Remark       string `json:"remark,optional"`
	TrackingCode string `json:"tracking_code,optional"`
}

// OrderQueryParams 订单查询参数（兼容管理后台）
type OrderQueryParams struct {
	UserID        *uint  `form:"user_id"`
	Status        *int   `form:"status"`
	PaymentStatus *int   `form:"payment_status"`
	OrderNo       string `form:"order_no"`
	StartDate     string `form:"start_date"`
	EndDate       string `form:"end_date"`
	Page          int    `form:"page"`
	PageSize      int    `form:"page_size"`
}

// OrderResponse 订单响应（兼容管理后台）
type OrderResponse struct {
	ID                       uint             `json:"id"`
	OrderNo                  string           `json:"order_no"`
	UserID                   uint             `json:"user_id"`
	Status                   int              `json:"status"`
	StatusText               string           `json:"status_text"`
	PaymentStatus            int              `json:"payment_status"`
	PaymentStatusText        string           `json:"payment_status_text"`
	ShippingStatus           int              `json:"shipping_status"`
	ShippingStatusText       string           `json:"shipping_status_text"`
	TotalAmount              float64          `json:"total_amount"`
	GoodsAmount              float64          `json:"goods_amount"`
	ShippingAmount           float64          `json:"shipping_amount"`
	ShippingFee              float64          `json:"shipping_fee"`
	DiscountAmount           float64          `json:"discount_amount"`
	CouponAmount             float64          `json:"coupon_amount"`
	PointsAmount             float64          `json:"points_amount"`
	PayAmount                float64          `json:"pay_amount"`
	PayMethod                string           `json:"pay_method"`
	PaymentMethod            int              `json:"payment_method"`
	PaymentMethodText        string           `json:"payment_method_text"`
	PayTime                  *time.Time       `json:"pay_time"`
	PaymentTime              *time.Time       `json:"payment_time"`
	WechatPayOrderID         string           `json:"wechat_pay_order_id"`
	WechatPrepayID           string           `json:"wechat_prepay_id"`
	AddressID                uint             `json:"address_id"`
	AddressInfo              string           `json:"address_info"`
	CouponID                 uint             `json:"coupon_id"`
	UsePoints                int              `json:"use_points"`
	Remark                   string           `json:"remark"`
	CancelReason             string           `json:"cancel_reason"`
	CancelTime               *time.Time       `json:"cancel_time"`
	ShipTime                 *time.Time       `json:"ship_time"`
	ShippingTime             *time.Time       `json:"shipping_time"`
	FinishTime               *time.Time       `json:"finish_time"`
	ReceiveTime              *time.Time       `json:"receive_time"`
	RefundStatus             int              `json:"refund_status"`
	RefundAmount             float64          `json:"refund_amount"`
	RefundReason             string           `json:"refund_reason"`
	RefundTime               *time.Time       `json:"refund_time"`
	RefundNo                 string           `json:"refund_no"`
	ExpressCompany           string           `json:"express_company"`
	ExpressNo                string           `json:"express_no"`
	WanLiNiuOrderPushStatus  int              `json:"wanliniu_order_push_status"`
	WanLiNiuRefundPushStatus int              `json:"wanliniu_refund_push_status"`
	WanLiNiuOrderPushCount   int              `json:"wanliniu_order_push_count"`
	WanLiNiuRefundPushCount  int              `json:"wanliniu_refund_push_count"`
	WanLiNiuLastPushTime     *time.Time       `json:"wanliniu_last_push_time"`
	WanLiNiuPushError        string           `json:"wanliniu_push_error"`
	CreatedAt                time.Time        `json:"created_at"`
	UpdatedAt                time.Time        `json:"updated_at"`
	DeletedAt                gorm.DeletedAt   `json:"-"`
	Items                    []OrderItem      `json:"items,omitempty"`
	OrderItems               []OrderItem      `json:"order_items,omitempty"`
	ShippingAddress          *ShippingAddress `json:"shipping_address"`
}

// OrderStatistics 订单统计（兼容管理后台）
type OrderStatistics struct {
	TotalOrders     int64   `json:"total_orders"`
	PendingOrders   int64   `json:"pending_orders"`
	PaidOrders      int64   `json:"paid_orders"`
	ShippedOrders   int64   `json:"shipped_orders"`
	CompletedOrders int64   `json:"completed_orders"`
	CancelledOrders int64   `json:"cancelled_orders"`
	TotalSales      float64 `json:"total_sales"`
	TodaySales      float64 `json:"today_sales"`
}
