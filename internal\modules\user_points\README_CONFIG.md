# 叶小币配置管理功能说明

## 功能概述

叶小币配置管理是一个完整的用户积分系统后台管理功能，支持全局配置、规则管理、等级关联等核心功能。

## 主要功能

### 1. 全局配置管理

#### 功能说明
- **启用叶小币开关**：全局开关，默认关闭
  - 关闭时：禁用所有叶小币设置项（包含输入、选择、开关、使用），数据不清空
  - 开启时：启用所有叶小币设置项，原数据生效
- **退款归还开关**：开启后，退款/取消订单将收回订单所产生的叶小币
- **期限设置**：支持永久、逐年（当年）、逐月（当月）三种模式
- **抵现规则**：1币1元的兑换比例

#### API接口
```
GET  /api/admin/user-points/global-config     # 获取全局配置
PUT  /api/admin/user-points/global-config/:id # 更新全局配置
```

### 2. 规则管理

#### 支持的规则类型
1. **完善信息**：完善档案>=1的获得设置的对应叶小币，仅限一次，获得后删除不可再次获得
2. **活动奖励**：根据活动类型设置对应的叶小币，默认0；后台发布活动时勾选专项活动后，根据类型获取对应叶小币
3. **打卡且分享**：打卡且分享成功后，获取对应叶小币；发送成功视为分享成功，勾选生效
4. **推荐奖励**：推荐成功且新人注册、消费每满多少钱的，推荐者获得设置的叶小币
5. **消费奖励**：消费后叶小币增加，订单核销完成后用户端才会增加叶小币
6. **注册奖励**：用户注册成功后获得叶小币
7. **消费累计奖励**：消费累计达到一定金额后获得叶小币
8. **推荐人消费奖励**：推荐的用户消费时，推荐人获得叶小币

#### 规则配置字段
- **用户等级ID**：关联的用户等级
- **规则类型**：上述支持的规则类型之一
- **规则名称**：自定义规则名称
- **规则描述**：规则的详细说明
- **是否启用**：规则开关
- **奖励叶小币数量**：满足条件时奖励的叶小币数量（>=0的正整数）
- **最小金额**：消费类规则的最小金额要求（分为单位）
- **金额阈值**：消费类规则的金额阈值（分为单位）
- **是否一次性奖励**：是否只能获得一次
- **是否需要分享**：是否需要分享才能获得奖励
- **活动类型**：活动规则的具体类型

#### API接口
```
GET    /api/admin/user-points/rules        # 获取规则列表
POST   /api/admin/user-points/rules        # 创建规则
GET    /api/admin/user-points/rules/:id    # 获取规则详情
PUT    /api/admin/user-points/rules/:id    # 更新规则
DELETE /api/admin/user-points/rules/:id    # 删除规则
```

### 3. 等级同步功能

#### 功能说明
- **同步至所有等级**：点击后，当前等级编辑内容同步至所有等级，且选择内容页同步
- 仅限等级栏目内容，叶小币获得项与等级关联

#### API接口
```
POST /api/admin/user-points/rules/sync    # 同步规则到所有等级
```

### 4. 辅助接口

#### 获取基础数据
```
GET /api/admin/user-points/user-levels     # 获取所有用户等级
GET /api/admin/user-points/rule-types      # 获取所有规则类型
GET /api/admin/user-points/expiry-types    # 获取所有期限类型
GET /api/admin/user-points/activity-types  # 获取所有活动类型
```

## 数据库表结构

### 1. coin_global_config（全局配置表）
- `id`：配置ID
- `enabled`：启用叶小币开关
- `refund_enabled`：退款归还开关
- `expiry_policy_type`：期限类型（PERMANENT/YEARLY/MONTHLY）
- `exchange_rate`：抵现规则（1币1元）

### 2. coin_rules（规则表）
- `id`：规则ID
- `user_level_id`：用户等级ID
- `rule_type`：规则类型
- `rule_name`：规则名称
- `description`：规则描述
- `enabled`：是否启用
- `coins_awarded`：奖励叶小币数量
- `min_amount`：最小金额（分）
- `amount_threshold`：金额阈值（分）
- `is_one_time`：是否一次性奖励
- `require_share`：是否需要分享
- `activity_type`：活动类型

### 3. user_coins（用户叶小币余额表）
- `id`：记录ID
- `user_id`：用户ID
- `total_coins`：总叶小币数量
- `available_coins`：可用叶小币数量
- `frozen_coins`：冻结叶小币数量
- `used_coins`：已使用叶小币数量

### 4. coin_transactions（叶小币交易记录表）
- `id`：交易ID
- `user_id`：用户ID
- `transaction_type`：交易类型（EARN/SPEND/REFUND/EXPIRE）
- `amount`：交易金额（正数为收入，负数为支出）
- `balance`：交易后余额
- `rule_id`：关联规则ID
- `order_id`：关联订单ID
- `description`：交易描述
- `expires_at`：过期时间

### 5. coin_one_time_rewards（一次性奖励记录表）
- `id`：记录ID
- `user_id`：用户ID
- `rule_id`：规则ID
- `rule_type`：规则类型
- `awarded`：是否已奖励

### 6. coin_checkin_records（打卡记录表）
- `id`：记录ID
- `user_id`：用户ID
- `checkin_date`：打卡日期
- `shared`：是否已分享
- `coins_awarded`：奖励叶小币数量

## 业务规则说明

### 1. 数据验证
- 所有金额、叶小币为>=0的正整数
- 规则类型必须是预定义的类型之一
- 期限类型必须是PERMANENT、YEARLY、MONTHLY之一

### 2. 业务逻辑
- 打卡+分享朋友圈+积分：单次
- 打卡不加积分
- 分享朋友圈不加积分
- 每日可打卡签到一次，分享不限制，只根据首次分享赠与叶小币
- 开启时，订单取消后，收回此笔订单消费赠送的叶小币
- 开启时，订单取消后，退还此笔订单扣减的叶小币给用户

### 3. 与现有系统集成
- 会员等级对应的权益存储到了`user_level_rules`表的`extra_coins`字段
- 叶小币系统与用户等级系统紧密集成

## 使用示例

### 1. 创建消费奖励规则
```json
{
  "user_level_id": 1,
  "rule_type": "CONSUME",
  "rule_name": "消费满100元奖励10叶小币",
  "description": "每消费满100元奖励10叶小币",
  "enabled": true,
  "coins_awarded": 10,
  "min_amount": 10000,
  "amount_threshold": 10000,
  "is_one_time": false,
  "require_share": false
}
```

### 2. 创建打卡分享规则
```json
{
  "user_level_id": 1,
  "rule_type": "CHECKIN_SHARE",
  "rule_name": "每日打卡分享奖励5叶小币",
  "description": "每日打卡并分享朋友圈奖励5叶小币",
  "enabled": true,
  "coins_awarded": 5,
  "is_one_time": false,
  "require_share": true
}
```

### 3. 更新全局配置
```json
{
  "id": 1,
  "enabled": true,
  "refund_enabled": true,
  "expiry_policy_type": "YEARLY",
  "exchange_rate": 1
}
```

## 注意事项

1. **权限控制**：所有API接口都需要管理员认证
2. **数据一致性**：规则修改会影响用户积分计算，需谨慎操作
3. **性能考虑**：大量用户的积分计算可能影响性能，建议异步处理
4. **日志记录**：所有配置变更都会记录操作日志
5. **数据备份**：重要配置变更前建议备份数据

## 开发说明

### 文件结构
```
internal/modules/user_points/
├── model/
│   └── user_points_config.go    # 数据模型定义
├── service/
│   └── user_points_service.go   # 业务逻辑层
├── handler/
│   └── user_points_handler.go   # HTTP处理层
└── README_CONFIG.md             # 功能说明文档
```

### 路由配置
- 路由文件：`internal/router/routes/user_points.go`
- 路由前缀：`/api/admin/user-points`
- 认证方式：管理员认证中间件

### 数据库迁移
- 迁移文件：`cmd/bootstrap/coin_rules_migration.go`
- 注册位置：`cmd/bootstrap/bootstrap.go`
- 自动创建默认全局配置