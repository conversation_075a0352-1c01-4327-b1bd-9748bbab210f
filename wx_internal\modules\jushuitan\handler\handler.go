package handler

import (
	"net/http"
	"strconv"
	"strings"
	"time"
	jst "yekaitai/pkg/adapters/jushuitan"
	"yekaitai/pkg/response"
	"yekaitai/wx_internal/svc"

	jsoniter "github.com/json-iterator/go"

	"github.com/zeromicro/go-zero/core/logx"
)

// 创建json-iterator实例
var json = jsoniter.ConfigCompatibleWithStandardLibrary

// JushuitanHandler 聚水潭模块处理器
type JushuitanHandler struct {
	ctx    *svc.WxServiceContext
	client *jst.Client
}

// NewJushuitanHandler 创建聚水潭处理器
func NewJushuitanHandler(ctx *svc.WxServiceContext) *JushuitanHandler {
	return &JushuitanHandler{
		ctx:    ctx,
		client: jst.DefaultClient,
	}
}

// LogRequest 记录请求信息
func LogRequest(r *http.Request, params interface{}) {
	data, _ := json.Marshal(params)
	logx.Infof("[JST] 请求 %s: %s，参数: %s", r.Method, r.URL.Path, string(data))
}

// LogResponse 记录响应信息
func LogResponse(r *http.Request, data interface{}) {
	respData, _ := json.Marshal(data)
	logx.Infof("[JST] 响应 %s: %s, 数据: %s", r.Method, r.URL.Path, string(respData))
}

// LogError 记录错误
func LogError(r *http.Request, err error) {
	logx.Errorf("[JST] 错误 %s: %s, 错误信息: %s", r.Method, r.URL.Path, err.Error())
}

// HandleError 处理错误，将API错误作为data返回
func HandleError(w http.ResponseWriter, r *http.Request, err error, statusCode int, message string) {
	LogError(r, err)

	// 构造错误响应
	errResp := &response.Response{
		Code:    statusCode,
		Message: message,
	}

	// 尝试解析错误信息JSON
	errMsg := err.Error()
	if strings.HasPrefix(errMsg, "{") && strings.HasSuffix(errMsg, "}") {
		// 可能是JSON格式的错误消息，尝试解析
		var jsonData interface{}
		if jsonErr := jsoniter.Unmarshal([]byte(errMsg), &jsonData); jsonErr == nil {
			// 成功解析为JSON，将其作为data返回
			errResp.Data = jsonData
		} else {
			// JSON解析失败，使用原始错误消息
			errResp.Data = map[string]interface{}{
				"error": errMsg,
			}
		}
	} else {
		// 非JSON格式，直接使用原始错误消息
		errResp.Data = map[string]interface{}{
			"error": errMsg,
		}
	}

	// 设置响应头并返回
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	jsoniter.NewEncoder(w).Encode(errResp)
}

// QueryShops 查询店铺列表
func (h *JushuitanHandler) QueryShops(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageIndex, _ := strconv.Atoi(r.FormValue("page_index"))
	if pageIndex <= 0 {
		pageIndex = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 100
	}

	// 处理店铺ID
	var shopIDs []int
	if shopIDsStr := r.FormValue("shop_ids"); shopIDsStr != "" {
		idStrings := strings.Split(shopIDsStr, ",")
		for _, idStr := range idStrings {
			if id, err := strconv.Atoi(idStr); err == nil {
				shopIDs = append(shopIDs, id)
			}
		}
	}

	// 记录请求参数
	req := &jst.ShopQueryRequest{
		PageIndex: pageIndex,
		PageSize:  pageSize,
		ShopIDs:   shopIDs,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QueryShops(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询店铺列表失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		http.Error(w, "写入响应失败", http.StatusInternalServerError)
	}
}

// QueryLogisticsCompanies 查询物流公司列表
func (h *JushuitanHandler) QueryLogisticsCompanies(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageIndex, _ := strconv.Atoi(r.FormValue("page_index"))
	if pageIndex <= 0 {
		pageIndex = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	modifiedBegin := r.FormValue("modified_begin")
	modifiedEnd := r.FormValue("modified_end")
	wmsCoID, _ := strconv.Atoi(r.FormValue("wms_co_id"))

	// 记录请求参数
	req := &jst.LogisticsCompanyQueryRequest{
		PageIndex:     pageIndex,
		PageSize:      pageSize,
		ModifiedBegin: modifiedBegin,
		ModifiedEnd:   modifiedEnd,
		WmsCoID:       wmsCoID,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QueryLogisticsCompanies(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询物流公司列表失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		http.Error(w, "写入响应失败", http.StatusInternalServerError)
	}
}

// QueryWarehouses 查询仓库列表
func (h *JushuitanHandler) QueryWarehouses(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageIndex, _ := strconv.Atoi(r.FormValue("page_index"))
	if pageIndex <= 0 {
		pageIndex = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	// 记录请求参数
	req := &jst.WarehouseQueryRequest{
		PageIndex: pageIndex,
		PageSize:  pageSize,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QueryWarehouses(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询仓库列表失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		http.Error(w, "写入响应失败", http.StatusInternalServerError)
	}
}

// QueryUsers 查询用户列表
func (h *JushuitanHandler) QueryUsers(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	currentPage, _ := strconv.Atoi(r.FormValue("current_page"))
	if currentPage <= 0 {
		currentPage = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	loginID := r.FormValue("loginId")
	enabledStr := r.FormValue("enabled")
	var enabled *bool
	if enabledStr != "" {
		enabledVal := enabledStr == "true" || enabledStr == "1"
		enabled = &enabledVal
	}

	// 记录请求参数
	req := &jst.UserQueryRequest{
		CurrentPage: currentPage,
		PageSize:    pageSize,
		LoginID:     loginID,
		Enabled:     enabled,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QueryUsers(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询用户列表失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		http.Error(w, "写入响应失败", http.StatusInternalServerError)
	}
}

// QuerySuppliers 查询供应商列表
func (h *JushuitanHandler) QuerySuppliers(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageNum, _ := strconv.Atoi(r.FormValue("page_num"))
	if pageNum <= 0 {
		pageNum = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	status, _ := strconv.Atoi(r.FormValue("status"))
	coName := r.FormValue("co_name")
	supplierCoID := r.FormValue("supplier_co_id")

	// 记录请求参数
	req := &jst.SupplierQueryRequest{
		Status:       status,
		CoName:       coName,
		SupplierCoID: supplierCoID,
		PageNum:      pageNum,
		PageSize:     pageSize,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QuerySuppliers(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询供应商列表失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		http.Error(w, "写入响应失败", http.StatusInternalServerError)
	}
}

// QueryDistributors 查询分销商列表
func (h *JushuitanHandler) QueryDistributors(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	pageNum, _ := strconv.Atoi(r.FormValue("page_num"))
	if pageNum <= 0 {
		pageNum = 1
	}

	pageSize, _ := strconv.Atoi(r.FormValue("page_size"))
	if pageSize <= 0 {
		pageSize = 30
	}

	status, _ := strconv.Atoi(r.FormValue("status"))
	updatedStart := r.FormValue("updated_start")
	updatedEnd := r.FormValue("updated_end")

	// 记录请求参数
	req := &jst.DistributorQueryRequest{
		Status:       status,
		PageNum:      pageNum,
		PageSize:     pageSize,
		UpdatedStart: updatedStart,
		UpdatedEnd:   updatedEnd,
	}
	LogRequest(r, req)

	// 调用聚水潭API
	jstResp, err := h.client.QueryDistributors(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询分销商列表失败")
		return
	}

	// 直接返回原始响应
	LogResponse(r, jstResp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// 直接将原始响应写入响应体
	if err := json.NewEncoder(w).Encode(jstResp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		http.Error(w, "写入响应失败", http.StatusInternalServerError)
	}
}
