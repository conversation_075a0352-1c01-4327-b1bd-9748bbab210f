package handler

import (
	"net/http"

	"yekaitai/internal/types"
	cartModel "yekaitai/pkg/common/model/cart"
	"yekaitai/wx_internal/modules/goods/service"
	"yekaitai/wx_internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

type CartGoZeroHandler struct {
	cartService *service.CartService
}

func NewCartGoZeroHandler() *CartGoZeroHandler {
	return &CartGoZeroHandler{
		cartService: service.NewCartService(),
	}
}

// 购物车项更新请求
type CartUpdateItemRequest struct {
	CartID uint `path:"id"` // 购物车ID
	cartModel.CartUpdateRequest
}

// 购物车项删除请求
type CartDeleteItemRequest struct {
	CartID uint `path:"id"` // 购物车ID
}

// 购物车批量删除请求
type CartBatchDeleteRequest struct {
	CartIDs []uint `json:"cart_ids" validate:"required,min=1"` // 购物车ID列表
}

// 更新购物车规格请求
type UpdateCartSpecRequest struct {
	CartID uint `path:"id"`                          // 购物车ID
	SpecID uint `json:"spec_id" validate:"required"` // 新规格ID
}

// AddToCart 添加商品到购物车
func (h *CartGoZeroHandler) AddToCart(w http.ResponseWriter, r *http.Request) {
	var req cartModel.CartAddRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	cart, err := h.cartService.AddToCart(r.Context(), userID, &req)
	if err != nil {
		logx.Errorf("添加购物车失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(cart))
}

// GetCartList 获取购物车列表
func (h *CartGoZeroHandler) GetCartList(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	cartList, err := h.cartService.GetCartList(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取购物车列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 计算购物车统计信息
	cartSummary, err := h.cartService.GetCartSummary(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取购物车统计失败: %v", err)
	}

	// 返回用户的全部购物车数据和统计信息
	result := map[string]interface{}{
		"list":    cartList,
		"summary": cartSummary,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// UpdateCartSpec 更新购物车商品规格
func (h *CartGoZeroHandler) UpdateCartSpec(w http.ResponseWriter, r *http.Request) {
	var req UpdateCartSpecRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 更新购物车规格
	if err := h.cartService.UpdateCartSpec(r.Context(), req.CartID, userID, req.SpecID); err != nil {
		logx.Errorf("更新购物车规格失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(map[string]string{
		"message": "规格更新成功",
	}))
}

// UpdateCartQuantity 更新购物车商品数量
func (h *CartGoZeroHandler) UpdateCartQuantity(w http.ResponseWriter, r *http.Request) {
	var req CartUpdateItemRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	err := h.cartService.UpdateCartQuantity(r.Context(), req.CartID, userID, req.Quantity)
	if err != nil {
		logx.Errorf("更新购物车数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// RemoveFromCart 从购物车删除商品（支持批量删除）
func (h *CartGoZeroHandler) RemoveFromCart(w http.ResponseWriter, r *http.Request) {
	// 检查请求方法，支持单个删除和批量删除
	if r.Method == http.MethodDelete && r.Header.Get("Content-Type") == "application/json" {
		// 批量删除
		h.batchRemoveFromCart(w, r)
		return
	}

	// 单个删除
	var req CartDeleteItemRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	err := h.cartService.RemoveFromCart(r.Context(), req.CartID, userID)
	if err != nil {
		logx.Errorf("删除购物车商品失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse("删除成功"))
}

// batchRemoveFromCart 批量删除购物车商品
func (h *CartGoZeroHandler) batchRemoveFromCart(w http.ResponseWriter, r *http.Request) {
	var req CartBatchDeleteRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, err.Error()))
		return
	}

	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return
	}

	// 批量删除
	err := h.cartService.BatchRemoveFromCart(r.Context(), req.CartIDs, userID)
	if err != nil {
		logx.Errorf("批量删除购物车商品失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	result := map[string]any{
		"total_count": len(req.CartIDs),
		"message":     "批量删除成功",
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}

// ClearCart 清空购物车
func (h *CartGoZeroHandler) ClearCart(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	err := h.cartService.ClearCart(r.Context(), userID)
	if err != nil {
		logx.Errorf("清空购物车失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil))
}

// GetCartCount 获取购物车商品数量
func (h *CartGoZeroHandler) GetCartCount(w http.ResponseWriter, r *http.Request) {
	// 获取用户ID
	userID, ok := utils.GetUserIDFromRequest(w, r)
	if !ok {
		return // 错误已在GetUserIDFromRequest中处理
	}

	count, err := h.cartService.GetCartCount(r.Context(), userID)
	if err != nil {
		logx.Errorf("获取购物车数量失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	result := map[string]interface{}{
		"count": count,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result))
}
