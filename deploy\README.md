# YeKaiTai 分别部署指南

## 🚀 部署方式

### 1. 命令行参数启动

#### 启动后台管理服务（端口 8080）
```bash
./main -env prod -service admin
```

#### 启动小程序服务（端口 8081）
```bash
./main -env prod -service mini
```

#### 启动所有服务（默认模式）
```bash
./main -env prod -service all
# 或者
./main -env prod
```

### 2. 使用部署脚本

#### 后台管理服务
```bash
# 启动
./deploy/admin-service.sh start

# 停止
./deploy/admin-service.sh stop

# 重启
./deploy/admin-service.sh restart

# 查看状态
./deploy/admin-service.sh status
```

#### 小程序服务
```bash
# 启动
./deploy/mini-service.sh start

# 停止
./deploy/mini-service.sh stop

# 重启
./deploy/mini-service.sh restart

# 查看状态
./deploy/mini-service.sh status
```

### 3. Docker Compose 部署

#### 分别启动服务
```bash
# 只启动后台管理服务
docker-compose up -d yekaitai-admin

# 只启动小程序服务
docker-compose up -d yekaitai-mini

# 启动所有服务
docker-compose up -d
```

#### 直接使用 Docker 命令
```bash
# 启动后台管理服务
docker run -d \
  -p 8889:8889 \
  -e ENV_FLAG=prod \
  yekaitai-admin-api:latest start_admin.sh

# 启动小程序服务
docker run -d \
  -p 8888:8888 \
  -e ENV_FLAG=prod \
  yekaitai-admin-api:latest start_mini.sh
```

#### 管理服务
```bash
# 查看日志
docker-compose logs -f yekaitai-admin
docker-compose logs -f yekaitai-mini

# 停止服务
docker-compose stop yekaitai-admin
docker-compose stop yekaitai-mini

# 删除服务
docker-compose down
```

## 📋 启动脚本说明

### 可用的启动脚本

| 脚本名称 | 功能 | 端口 | 用途 |
|---------|------|------|------|
| `start_admin.sh` | 后台管理服务 | 8889 | 生产环境，后台管理 + 定时任务 |
| `start_mini.sh` | 小程序服务 | 8888 | 生产环境，小程序API |

### Docker 环境变量

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `ENV_FLAG` | `prod` | 运行环境（dev/test/prod） |

### 端口配置

在 `etc/yekaitai-prod.yaml` 中：

```yaml
# 后台管理服务端口
backend:
  Host: 0.0.0.0
  Port: 8889

# 小程序服务端口
mini:
  Host: 0.0.0.0
  Port: 8888
```

### 服务访问地址

- **后台管理服务**: `http://your-domain:8889`
- **小程序服务**: `http://your-domain:8888`

## 🔧 Nginx 反向代理配置

```nginx
# 后台管理服务
server {
    listen 80;
    server_name admin.yekaitai.com;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 小程序服务
server {
    listen 80;
    server_name api.yekaitai.com;
    
    location / {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控和日志

### 日志文件位置
- **后台管理服务**: `/var/log/medlinker/yekaitai-admin-api/admin.log`
- **小程序服务**: `/var/log/medlinker/yekaitai-admin-api/mini.log`

### 健康检查
```bash
# 检查后台管理服务
curl http://localhost:8080/health

# 检查小程序服务
curl http://localhost:8081/health
```

## ⚠️ 注意事项

1. **数据库连接**: 两个服务共享同一个数据库，确保数据库连接数足够
2. **Redis连接**: 两个服务共享同一个Redis实例
3. **定时任务**: 只在 `service=all` 模式下启动定时任务，避免重复执行
4. **日志分离**: 不同服务使用不同的日志文件，便于问题排查
5. **端口冲突**: 确保配置的端口没有被其他服务占用

## 🔄 升级部署

```bash
# 1. 停止服务
./deploy/admin-service.sh stop
./deploy/mini-service.sh stop

# 2. 备份当前版本
cp main main.backup.$(date +%Y%m%d_%H%M%S)

# 3. 部署新版本
cp new_main main
chmod +x main

# 4. 启动服务
./deploy/admin-service.sh start
./deploy/mini-service.sh start

# 5. 验证服务
./deploy/admin-service.sh status
./deploy/mini-service.sh status
```
