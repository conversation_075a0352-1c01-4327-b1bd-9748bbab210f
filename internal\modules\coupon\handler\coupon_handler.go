package handler

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"yekaitai/internal/modules/coupon/service"
	"yekaitai/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// CouponDetailRequest 获取优惠券详情请求
type CouponDetailRequest struct {
	ID uint `path:"id"` // 优惠券ID
}

// UpdateCouponRequest 更新优惠券请求（handler层使用）
type UpdateCouponRequest struct {
	ID          uint    `path:"id"`                                        // 优惠券ID
	Name        string  `json:"name" validate:"required,max=30"`           // 优惠券名称
	Type        int     `json:"type" validate:"required,oneof=1 2 3"`      // 优惠券类型
	Description string  `json:"description,optional"`                      // 使用说明
	Amount      float64 `json:"amount,optional" validate:"min=0"`          // 优惠金额
	Discount    float64 `json:"discount,optional" validate:"min=0,max=10"` // 折扣比例
	MinAmount   float64 `json:"min_amount,optional" validate:"min=0"`      // 最低使用金额

	TotalQuantity int `json:"total_quantity" validate:"required,min=1,max=10000"` // 发放数量

	Scope int `json:"scope" validate:"required,oneof=1 2 3"` // 可用范围

	ValidityType int        `json:"validity_type" validate:"required,oneof=1 2 3"` // 有效期类型
	ValidDays    int        `json:"valid_days,optional" validate:"min=0,max=9999"` // 有效天数
	ValidFrom    *time.Time `json:"valid_from,optional"`                           // 生效开始时间
	ValidUntil   *time.Time `json:"valid_until,optional"`                          // 生效结束时间

	UsageRestriction int `json:"usage_restriction" validate:"required,oneof=1 2"` // 使用限制

	UserLevel    int    `json:"user_level" validate:"required,oneof=1 2"` // 用户可见范围
	UserLevelIDs []uint `json:"user_level_ids,optional"`                  // 可见用户等级ID

	// 适用商品/服务
	ProductIDs []uint `json:"product_ids,optional"` // 适用商品
	ServiceIDs []uint `json:"service_ids,optional"` // 适用服务
}

// UpdateCouponStatusRequest 更新优惠券状态请求
type UpdateCouponStatusRequest struct {
	ID     uint `path:"id"`                                     // 优惠券ID
	Status int  `json:"status" validate:"required,oneof=0 1 2"` // 状态：0-禁用，1-启用，2-已过期
}

// DeleteCouponRequest 删除优惠券请求
type DeleteCouponRequest struct {
	ID uint `path:"id"` // 优惠券ID
}

// IssueCouponsRequest 批量发放优惠券请求
type IssueCouponsRequest struct {
	ID          uint   `path:"id"`                                    // 优惠券ID
	UserLevels  []int  `json:"user_levels" validate:"required"`       // 用户等级列表：1-LV1，2-LV2，等等
	IssueCount  int    `json:"issue_count" validate:"required,min=1"` // 发放数量
	IssueReason string `json:"issue_reason,optional"`                 // 发放原因
}

// CreateIssueTaskRequest 创建发放任务请求
type CreateIssueTaskRequest struct {
	ID          uint   `path:"id"`                                    // 优惠券ID
	UserLevels  []int  `json:"user_levels" validate:"required"`       // 用户等级列表
	IssueCount  int    `json:"issue_count" validate:"required,min=1"` // 发放数量
	IssueReason string `json:"issue_reason,optional"`                 // 发放原因
	TaskName    string `json:"task_name,optional"`                    // 任务名称
}

// CouponGoZeroHandler 优惠券Go-Zero处理器
type CouponGoZeroHandler struct {
	couponService *service.CouponService
}

// NewCouponGoZeroHandler 创建优惠券处理器
func NewCouponGoZeroHandler() *CouponGoZeroHandler {
	return &CouponGoZeroHandler{
		couponService: service.NewCouponService(),
	}
}

// GetCouponList 获取优惠券列表
func (h *CouponGoZeroHandler) GetCouponList(w http.ResponseWriter, r *http.Request) {
	var req service.CouponListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	coupons, total, err := h.couponService.GetCouponList(context.Background(), &req)
	if err != nil {
		logx.Errorf("获取优惠券列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取优惠券列表失败"))
		return
	}

	response := map[string]interface{}{
		"list":  coupons,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(response, "获取优惠券列表成功"))
}

// CreateCoupon 创建优惠券
func (h *CouponGoZeroHandler) CreateCoupon(w http.ResponseWriter, r *http.Request) {
	var req service.CreateCouponRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	coupon, err := h.couponService.CreateCoupon(context.Background(), &req)
	if err != nil {
		logx.Errorf("创建优惠券失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(coupon, "创建优惠券成功"))
}

// GetCouponDetail 获取优惠券详情
func (h *CouponGoZeroHandler) GetCouponDetail(w http.ResponseWriter, r *http.Request) {
	var req CouponDetailRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	coupon, err := h.couponService.GetCouponDetail(context.Background(), req.ID)
	if err != nil {
		logx.Errorf("获取优惠券详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(coupon, "获取优惠券详情成功"))
}

// UpdateCoupon 更新优惠券
func (h *CouponGoZeroHandler) UpdateCoupon(w http.ResponseWriter, r *http.Request) {
	var req UpdateCouponRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 转换为service层的请求结构体
	serviceReq := &service.UpdateCouponRequest{
		ID:               req.ID,
		Name:             req.Name,
		Type:             req.Type,
		Description:      req.Description,
		Amount:           req.Amount,
		Discount:         req.Discount,
		MinAmount:        req.MinAmount,
		TotalQuantity:    req.TotalQuantity,
		Scope:            req.Scope,
		ValidityType:     req.ValidityType,
		ValidDays:        req.ValidDays,
		ValidFrom:        req.ValidFrom,
		ValidUntil:       req.ValidUntil,
		UsageRestriction: req.UsageRestriction,
		UserLevel:        req.UserLevel,
		UserLevelIDs:     req.UserLevelIDs,
		ProductIDs:       req.ProductIDs,
		ServiceIDs:       req.ServiceIDs,
	}

	coupon, err := h.couponService.UpdateCoupon(context.Background(), serviceReq)
	if err != nil {
		logx.Errorf("更新优惠券失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(coupon, "更新优惠券成功"))
}

// DeleteCoupon 删除优惠券
func (h *CouponGoZeroHandler) DeleteCoupon(w http.ResponseWriter, r *http.Request) {
	var req DeleteCouponRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 删除优惠券
	err := h.couponService.DeleteCoupon(context.Background(), req.ID)
	if err != nil {
		logx.Errorf("删除优惠券失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "删除优惠券成功"))
}

// UpdateCouponStatus 更新优惠券状态
func (h *CouponGoZeroHandler) UpdateCouponStatus(w http.ResponseWriter, r *http.Request) {
	// 添加调试日志
	logx.Infof("更新优惠券状态请求: URL=%s, Method=%s", r.URL.Path, r.Method)

	// 读取请求体进行调试
	if r.Body != nil {
		bodyBytes, _ := io.ReadAll(r.Body)
		logx.Infof("请求体内容: %s", string(bodyBytes))
		// 重新设置请求体，因为ReadAll会消耗掉
		r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	var req UpdateCouponStatusRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析更新优惠券状态请求失败: %v", err)
		logx.Errorf("请求路径: %s", r.URL.Path)
		logx.Errorf("请求方法: %s", r.Method)
		logx.Errorf("Content-Type: %s", r.Header.Get("Content-Type"))
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("解析请求失败: %v", err)))
		return
	}

	logx.Infof("解析成功 - 优惠券ID=%d, 状态=%d", req.ID, req.Status)

	// 更新优惠券状态
	err := h.couponService.UpdateCouponStatus(context.Background(), req.ID, req.Status)
	if err != nil {
		logx.Errorf("更新优惠券状态失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "更新优惠券状态成功"))
}

// GetProductList 获取商品列表
func (h *CouponGoZeroHandler) GetProductList(w http.ResponseWriter, r *http.Request) {
	name := r.URL.Query().Get("name")
	pageStr := r.URL.Query().Get("page")
	sizeStr := r.URL.Query().Get("size")

	page, _ := strconv.Atoi(pageStr)
	size, _ := strconv.Atoi(sizeStr)

	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	result, err := h.couponService.GetProductList(context.Background(), name, page, size)
	if err != nil {
		logx.Errorf("获取商品列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取商品列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取商品列表成功"))
}

// GetServiceList 获取服务列表
func (h *CouponGoZeroHandler) GetServiceList(w http.ResponseWriter, r *http.Request) {
	name := r.URL.Query().Get("name")
	pageStr := r.URL.Query().Get("page")
	sizeStr := r.URL.Query().Get("size")

	page, _ := strconv.Atoi(pageStr)
	size, _ := strconv.Atoi(sizeStr)

	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	result, err := h.couponService.GetServiceList(context.Background(), name, page, size)
	if err != nil {
		logx.Errorf("获取服务列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取服务列表失败"))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "获取服务列表成功"))
}

// IssueCoupons 批量发放优惠券
func (h *CouponGoZeroHandler) IssueCoupons(w http.ResponseWriter, r *http.Request) {
	// 添加调试日志
	logx.Infof("批量发放优惠券请求: URL=%s, Method=%s", r.URL.Path, r.Method)

	// 读取请求体进行调试
	if r.Body != nil {
		bodyBytes, _ := io.ReadAll(r.Body)
		logx.Infof("请求体内容: %s", string(bodyBytes))
		// 重新设置请求体，因为ReadAll会消耗掉
		r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	var req IssueCouponsRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析批量发放优惠券请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("解析请求失败: %v", err)))
		return
	}

	logx.Infof("解析成功 - 优惠券ID=%d, 用户等级=%v, 发放数量=%d", req.ID, req.UserLevels, req.IssueCount)

	// 调用service层的批量发放方法
	result, err := h.couponService.IssueCoupons(context.Background(), &service.IssueCouponsRequest{
		CouponID:    req.ID,
		UserLevels:  req.UserLevels,
		IssueCount:  req.IssueCount,
		IssueReason: req.IssueReason,
	})
	if err != nil {
		logx.Errorf("批量发放优惠券失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "批量发放优惠券成功"))
}

// CreateIssueTask 创建发放任务（异步）
func (h *CouponGoZeroHandler) CreateIssueTask(w http.ResponseWriter, r *http.Request) {
	// 添加调试日志
	logx.Infof("创建发放任务请求: URL=%s, Method=%s", r.URL.Path, r.Method)

	var req CreateIssueTaskRequest
	if err := httpx.Parse(r, &req); err != nil {
		logx.Errorf("解析创建发放任务请求失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, fmt.Sprintf("解析请求失败: %v", err)))
		return
	}

	logx.Infof("解析成功 - 优惠券ID=%d, 用户等级=%v, 发放数量=%d", req.ID, req.UserLevels, req.IssueCount)

	// 获取管理员ID
	var adminID uint = 0
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if id, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			adminID = uint(id)
		}
	}

	// 构建服务层请求
	serviceReq := &service.CreateIssueTaskRequest{
		CouponID:    req.ID,
		UserLevels:  req.UserLevels,
		IssueCount:  req.IssueCount,
		IssueReason: req.IssueReason,
		TaskName:    req.TaskName,
	}

	// 创建任务
	result, err := h.couponService.CreateIssueTask(context.Background(), serviceReq, adminID)
	if err != nil {
		logx.Errorf("创建发放任务失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(result, "发放任务创建成功"))
}

// GetIssueTaskList 获取发放任务列表
func (h *CouponGoZeroHandler) GetIssueTaskList(w http.ResponseWriter, r *http.Request) {
	var req service.IssueTaskListRequest
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	tasks, total, err := h.couponService.GetIssueTaskList(context.Background(), &req)
	if err != nil {
		logx.Errorf("获取发放任务列表失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, "获取任务列表失败"))
		return
	}

	response := map[string]interface{}{
		"list":  tasks,
		"total": total,
		"page":  req.Page,
		"size":  req.Size,
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(response, "获取发放任务列表成功"))
}

// GetIssueTaskDetail 获取发放任务详情
func (h *CouponGoZeroHandler) GetIssueTaskDetail(w http.ResponseWriter, r *http.Request) {
	var req struct {
		ID uint `path:"id"` // 任务ID
	}
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	task, err := h.couponService.GetIssueTaskDetail(context.Background(), req.ID)
	if err != nil {
		logx.Errorf("获取发放任务详情失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(task, "获取发放任务详情成功"))
}

// ExecuteIssueTask 手动执行发放任务
func (h *CouponGoZeroHandler) ExecuteIssueTask(w http.ResponseWriter, r *http.Request) {
	var req struct {
		ID uint `path:"id"` // 任务ID
	}
	if err := httpx.Parse(r, &req); err != nil {
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInvalidParams, "无效的请求参数"))
		return
	}

	// 获取执行者ID（管理员ID）
	var executorID uint = 0
	if adminIDStr, ok := r.Context().Value("admin_id").(string); ok {
		if id, err := strconv.ParseUint(adminIDStr, 10, 32); err == nil {
			executorID = uint(id)
		}
	}

	logx.Infof("手动执行发放任务请求: TaskID=%d, ExecutorID=%d", req.ID, executorID)

	// 执行任务
	if err := h.couponService.ExecuteIssueTask(context.Background(), req.ID, executorID); err != nil {
		logx.Errorf("手动执行发放任务失败: %v", err)
		httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeInternalError, err.Error()))
		return
	}

	// 记录操作日志 - 已由中间件统一处理

	httpx.WriteJson(w, http.StatusOK, types.NewSuccessResponse(nil, "任务执行成功"))
}

// logAdminOperation 记录管理员操作日志 - 已由中间件统一处理，保留方法以避免编译错误
func (h *CouponGoZeroHandler) logAdminOperation(r *http.Request, args ...interface{}) {
	// 操作日志已由中间件统一处理，此方法已废弃
	logx.Infof("操作日志已由中间件统一处理")
}
