package handler

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/zeromicro/go-zero/core/logx"

	jst "yekaitai/pkg/adapters/jushuitan"
	"yekaitai/pkg/response"
	"yekaitai/wx_internal/svc"
)

// OrderHandler 聚水潭订单处理器
type OrderHandler struct {
	ctx    *svc.WxServiceContext
	client *jst.Client
}

// NewOrderHandler 创建聚水潭订单处理器
func NewOrderHandler(ctx *svc.WxServiceContext) *OrderHandler {
	return &OrderHandler{
		ctx:    ctx,
		client: ctx.JushuitanClient,
	}
}

// UploadOrder 上传订单处理
func (h *OrderHandler) UploadOrder(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.<PERSON>, r.URL.Path)

	var req []*jst.OrderUploadRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.UploadOrders(r.Context(), req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "上传订单失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryOrder 查询订单处理
func (h *OrderHandler) QueryOrder(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 解析请求参数
	var req jst.OrderQueryRequest
	var reqBody []byte
	var err error

	// 仅支持POST请求
	if r.Method != http.MethodPost {
		err = fmt.Errorf("不支持的请求方法: %s，仅支持POST请求", r.Method)
		HandleError(w, r, err, http.StatusMethodNotAllowed, "请求方法不支持")
		return
	}

	// 处理POST请求的JSON体
	reqBody, err = io.ReadAll(r.Body)
	if err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "读取请求体失败")
		return
	}
	// 保存原始请求体用于日志记录
	logx.Infof("[JST] 订单查询POST请求体: %s", string(reqBody))

	// 重新设置请求体以便解析
	r.Body = io.NopCloser(bytes.NewReader(reqBody))

	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 如果请求URL中也有参数，合并到请求中
	if r.URL.RawQuery != "" {
		if err := r.ParseForm(); err == nil {
			// 处理URL中的o_ids参数
			if oIDsStr := r.FormValue("o_ids"); oIDsStr != "" && len(req.OIDs) == 0 {
				if strings.HasPrefix(oIDsStr, "[") && strings.HasSuffix(oIDsStr, "]") {
					oIDsStr = strings.Trim(oIDsStr, "[]")
				}

				if oIDsStr != "" {
					oIDStrs := strings.Split(oIDsStr, ",")
					for _, idStr := range oIDStrs {
						idStr = strings.TrimSpace(idStr)
						id, err := strconv.Atoi(idStr)
						if err == nil {
							req.OIDs = append(req.OIDs, id)
						}
					}
				}
			}

			// 处理URL中的is_get_cbfinance参数
			if isGetCBFinanceStr := r.FormValue("is_get_cbfinance"); isGetCBFinanceStr != "" {
				req.IsGetCBFinance = isGetCBFinanceStr == "true"
			}
		}
	}

	// 参数校验
	if len(req.OIDs) == 0 && len(req.SoIDs) == 0 &&
		(req.ModifiedBegin == "" || req.ModifiedEnd == "") && req.StartTS == 0 {
		err = fmt.Errorf("必须提供至少一个查询条件: 线上单号列表、内部订单号列表、时间范围或时间戳")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 默认值设置
	if req.PageSize <= 0 {
		req.PageSize = 20 // 默认每页20条
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 最大一次查询100条
	}
	if req.PageIndex <= 0 {
		req.PageIndex = 1 // 默认第1页
	}

	// 记录最终的请求参数
	reqJSON, _ := jsoniter.MarshalToString(req)
	logx.Infof("[JST] 订单查询最终请求参数: %s", reqJSON)

	// 调用聚水潭API
	resp, err := h.client.QueryOrder(r.Context(), &req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询订单失败")
		return
	}

	// 记录完整的响应结果
	respJSON, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 订单查询响应结果: %s", respJSON)

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// CancelOrder 取消订单处理
func (h *OrderHandler) CancelOrder(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderCancelRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.CancelOrder(r.Context(), &req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "取消订单失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// SplitOrder 订单拆分处理
func (h *OrderHandler) SplitOrder(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 订单拆分原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.OrderSplitRequest{}

	// 处理o_id
	if oID, ok := rawReq["o_id"]; ok && oID != nil {
		req.OID = fmt.Sprintf("%v", oID)
	}

	// 处理split_infos
	if splitInfosRaw, ok := rawReq["split_infos"]; ok && splitInfosRaw != nil {
		if splitInfosArr, ok := splitInfosRaw.([]interface{}); ok {
			for _, groupRaw := range splitInfosArr {
				if groupArr, ok := groupRaw.([]interface{}); ok {
					var group []jst.SplitItemInfo
					for _, itemRaw := range groupArr {
						if itemMap, ok := itemRaw.(map[string]interface{}); ok {
							item := jst.SplitItemInfo{}

							// 处理oi_id
							if oiID, ok := itemMap["oi_id"]; ok && oiID != nil {
								item.OIID = fmt.Sprintf("%v", oiID)
							}

							// 处理qty
							if qty, ok := itemMap["qty"]; ok && qty != nil {
								switch v := qty.(type) {
								case float64:
									item.Qty = int(v)
								case string:
									if qtyInt, err := strconv.Atoi(v); err == nil {
										item.Qty = qtyInt
									}
								case int:
									item.Qty = v
								}
							}

							group = append(group, item)
						}
					}
					req.SplitInfos = append(req.SplitInfos, group)
				}
			}
		}
	}

	LogRequest(r, req)

	// 参数验证
	if req.OID == "" {
		err := fmt.Errorf("内部订单号不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}
	if len(req.SplitInfos) == 0 {
		err := fmt.Errorf("拆分信息不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.SplitOrder(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 订单拆分原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "订单拆分失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// ModifyOrderWMS 指定发货仓处理
func (h *OrderHandler) ModifyOrderWMS(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req []*jst.OrderModifyWMSRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		LogError(r, err)
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)

	// 调用聚水潭API
	resp, err := h.client.ModifyOrderWMS(r.Context(), req)
	if err != nil {
		LogError(r, err)
		HandleError(w, r, err, http.StatusInternalServerError, "指定发货仓失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QuestionOrder 订单转异常处理
func (h *OrderHandler) QuestionOrder(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 订单转异常原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.OrderQuestionRequest{
		QuestionType: fmt.Sprintf("%v", rawReq["question_type"]),
	}

	// 处理可能是字符串数组的o_ids
	if oIDsRaw, ok := rawReq["o_ids"]; ok && oIDsRaw != nil {
		switch oIDs := oIDsRaw.(type) {
		case []interface{}:
			for _, id := range oIDs {
				switch v := id.(type) {
				case float64:
					req.OIDs = append(req.OIDs, int(v))
				case string:
					if intID, err := strconv.Atoi(v); err == nil {
						req.OIDs = append(req.OIDs, intID)
					}
				case int:
					req.OIDs = append(req.OIDs, v)
				}
			}
		}
	}

	// 处理可能是字符串数组的so_ids
	if soIDsRaw, ok := rawReq["so_ids"]; ok && soIDsRaw != nil {
		switch soIDs := soIDsRaw.(type) {
		case []interface{}:
			for _, id := range soIDs {
				switch v := id.(type) {
				case string:
					req.SoIDs = append(req.SoIDs, v)
				case float64:
					req.SoIDs = append(req.SoIDs, fmt.Sprintf("%v", v))
				}
			}
		}
	}

	// 处理其他可选字段
	if desc, ok := rawReq["question_desc"]; ok && desc != nil {
		req.QuestionDesc = fmt.Sprintf("%v", desc)
	}

	if channel, ok := rawReq["channel"]; ok && channel != nil {
		req.Channel = fmt.Sprintf("%v", channel)
	}

	LogRequest(r, req)

	// 参数验证
	if len(req.OIDs) == 0 && len(req.SoIDs) == 0 {
		err := fmt.Errorf("内部订单号列表和线上单号列表至少需要提供一项")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	if req.QuestionType == "" {
		err := fmt.Errorf("异常类型不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.SetOrderQuestion(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 订单转异常原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "订单转异常失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// UploadOrderRemark 修改卖家备注处理
func (h *OrderHandler) UploadOrderRemark(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	// 先读取原始请求体
	var bodyBytes []byte
	bodyBytes, _ = io.ReadAll(r.Body)
	r.Body.Close()
	// 重新创建请求体以供后续使用
	r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 记录原始请求
	reqStr := string(bodyBytes)
	logx.Infof("[JST] 修改订单备注原始请求: %s", reqStr)

	// 使用map先解析以处理可能的类型转换
	var rawReq map[string]interface{}
	if err := jsoniter.Unmarshal(bodyBytes, &rawReq); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 准备最终请求对象
	req := jst.OrderRemarkSaveRequest{}

	// 处理so_id
	if soID, ok := rawReq["so_id"]; ok && soID != nil {
		req.SoID = fmt.Sprintf("%v", soID)
	}

	// 处理remark
	if remark, ok := rawReq["remark"]; ok && remark != nil {
		req.Remark = fmt.Sprintf("%v", remark)
	}

	// 处理is_append
	if isAppend, ok := rawReq["is_append"]; ok && isAppend != nil {
		switch v := isAppend.(type) {
		case bool:
			req.IsAppend = v
		case string:
			req.IsAppend = v == "true" || v == "1"
		case float64:
			req.IsAppend = v > 0
		}
	}

	LogRequest(r, req)

	// 参数验证
	if req.SoID == "" {
		err := fmt.Errorf("线上单号不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	if req.Remark == "" {
		err := fmt.Errorf("备注不能为空")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 调用聚水潭API
	resp, err := h.client.SaveOrderRemark(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 修改订单备注原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "修改订单备注失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(start))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// SetOrderNode 修改订单线下备注
func (h *OrderHandler) SetOrderNode(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderNodeRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		LogError(r, err)
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)
	resp, err := h.client.SetOrderNode(r.Context(), &req)
	if err != nil {
		LogError(r, err)
		HandleError(w, r, err, http.StatusInternalServerError, "修改订单线下备注失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// UploadOrderLabel 修改订单标签
func (h *OrderHandler) UploadOrderLabel(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderLabelRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		LogError(r, err)
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)
	resp, err := h.client.UploadOrderLabel(r.Context(), &req)
	if err != nil {
		LogError(r, err)
		HandleError(w, r, err, http.StatusInternalServerError, "修改订单标签失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// UploadOrderSent 订单发货处理
func (h *OrderHandler) UploadOrderSent(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderSentRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		LogError(r, err)
		response.Error(w, http.StatusBadRequest, "解析请求失败: "+err.Error())
		return
	}

	LogRequest(r, req)
	resp, err := h.client.UploadOrderSent(r.Context(), &req)

	// 无论成功失败，先记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 订单发货原始响应数据: %s", respJson)

	// 处理错误情况
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "订单发货失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		logx.Errorf("写入响应失败: %v", err)
		response.Error(w, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryOrderAction 订单操作日志查询处理
func (h *OrderHandler) QueryOrderAction(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderActionQueryRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)
	resp, err := h.client.QueryOrderAction(r.Context(), &req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "订单操作日志查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryOrders 查询订单列表
func (h *OrderHandler) QueryOrders(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderQueryRequest
	var err error

	// 仅支持POST请求
	if r.Method != http.MethodPost {
		err = fmt.Errorf("不支持的请求方法: %s，仅支持POST请求", r.Method)
		HandleError(w, r, err, http.StatusMethodNotAllowed, "请求方法不支持")
		return
	}

	// 处理POST请求的JSON体
	var reqBody []byte
	reqBody, err = io.ReadAll(r.Body)
	if err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "读取请求体失败")
		return
	}

	// 记录请求内容
	logx.Infof("[JST] 订单查询POST请求体: %s", string(reqBody))

	// 重新设置请求体以便解析
	r.Body = io.NopCloser(bytes.NewReader(reqBody))

	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	// 如果请求URL中也有参数，合并到请求中
	if r.URL.RawQuery != "" {
		if err := r.ParseForm(); err == nil {
			// 处理URL中的o_ids参数
			if oIDsStr := r.FormValue("o_ids"); oIDsStr != "" && len(req.OIDs) == 0 {
				if strings.HasPrefix(oIDsStr, "[") && strings.HasSuffix(oIDsStr, "]") {
					oIDsStr = strings.Trim(oIDsStr, "[]")
				}

				if oIDsStr != "" {
					oIDStrs := strings.Split(oIDsStr, ",")
					for _, idStr := range oIDStrs {
						idStr = strings.TrimSpace(idStr)
						id, err := strconv.Atoi(idStr)
						if err == nil {
							req.OIDs = append(req.OIDs, id)
						}
					}
				}
			}

			// 处理URL中的is_get_cbfinance参数
			if isGetCBFinanceStr := r.FormValue("is_get_cbfinance"); isGetCBFinanceStr != "" {
				req.IsGetCBFinance = isGetCBFinanceStr == "true"
			}
		}
	}

	// 参数校验
	if len(req.OIDs) == 0 && len(req.SoIDs) == 0 &&
		(req.ModifiedBegin == "" || req.ModifiedEnd == "") && req.StartTS == 0 {
		err = fmt.Errorf("必须提供至少一个查询条件: 线上单号列表、内部订单号列表、时间范围或时间戳")
		HandleError(w, r, err, http.StatusBadRequest, "参数校验失败")
		return
	}

	// 默认值设置
	if req.PageSize <= 0 {
		req.PageSize = 20 // 默认每页20条
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 最大一次查询100条
	}
	if req.PageIndex <= 0 {
		req.PageIndex = 1 // 默认第1页
	}

	// 记录最终的请求参数
	reqJSON, _ := jsoniter.MarshalToString(req)
	logx.Infof("[JST] 订单查询最终请求参数: %s", reqJSON)

	// 调用聚水潭API
	resp, err := h.client.QueryOrder(r.Context(), &req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "查询订单失败")
		return
	}

	// 记录响应结果
	respJSON, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 订单查询响应结果: %s", respJSON)

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// SetOrderException 设置订单异常
func (h *OrderHandler) SetOrderException(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderExceptionRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)
	resp, err := h.client.SetOrderException(r.Context(), &req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "设置订单异常失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// UpdateOrderRemark 更新订单备注
func (h *OrderHandler) UpdateOrderRemark(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderRemarkRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)
	resp, err := h.client.UpdateOrderRemark(r.Context(), &req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "更新订单备注失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// OrderSent 订单发货处理
func (h *OrderHandler) OrderSent(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderSentRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)
	resp, err := h.client.UploadOrderSent(r.Context(), &req)

	// 记录原始响应
	respJson, _ := jsoniter.MarshalToString(resp)
	logx.Infof("[JST] 订单发货原始响应数据: %s", respJson)

	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "订单发货失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}

// QueryOrderActions 订单操作日志查询处理
func (h *OrderHandler) QueryOrderActions(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	logx.Infof("[JST] 开始处理请求: %s %s", r.Method, r.URL.Path)

	var req jst.OrderActionQueryRequest
	if err := jsoniter.NewDecoder(r.Body).Decode(&req); err != nil {
		HandleError(w, r, err, http.StatusBadRequest, "解析请求失败")
		return
	}

	LogRequest(r, req)
	resp, err := h.client.QueryOrderAction(r.Context(), &req)
	if err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "订单操作日志查询失败")
		return
	}

	LogResponse(r, resp)
	logx.Infof("[JST] 请求处理完成: %s %s, 耗时: %v", r.Method, r.URL.Path, time.Since(startTime))

	// 直接返回原始响应
	w.Header().Set("Content-Type", "application/json")
	if err := jsoniter.NewEncoder(w).Encode(resp); err != nil {
		HandleError(w, r, err, http.StatusInternalServerError, "写入响应失败")
	}
}
