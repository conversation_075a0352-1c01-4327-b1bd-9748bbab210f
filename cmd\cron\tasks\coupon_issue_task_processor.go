package tasks

import (
	"context"
	"time"

	couponModel "yekaitai/internal/modules/coupon/model"
	couponService "yekaitai/internal/modules/coupon/service"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// CouponIssueTaskProcessor 优惠券发放任务处理器
type CouponIssueTaskProcessor struct {
	couponService *couponService.CouponService
	taskRepo      couponModel.CouponIssueTaskRepository
}

// NewCouponIssueTaskProcessor 创建任务处理器
func NewCouponIssueTaskProcessor() *CouponIssueTaskProcessor {
	db := mysql.GetDB()
	return &CouponIssueTaskProcessor{
		couponService: couponService.NewCouponService(),
		taskRepo:      couponModel.NewCouponIssueTaskRepository(db),
	}
}

// ProcessTasks 处理待执行的任务
func (p *CouponIssueTaskProcessor) ProcessTasks() {
	logx.Info("开始处理优惠券发放任务")

	// 获取待处理的任务，每次处理5个
	tasks, err := p.taskRepo.FindPendingTasks(5)
	if err != nil {
		logx.Errorf("获取待处理任务失败: %v", err)
		return
	}

	if len(tasks) == 0 {
		logx.Info("没有待处理的优惠券发放任务")
		return
	}

	logx.Infof("找到 %d 个待处理的优惠券发放任务", len(tasks))

	// 并发处理任务，但限制并发数
	semaphore := make(chan struct{}, 3) // 最多同时处理3个任务

	for _, task := range tasks {
		go func(t *couponModel.CouponIssueTask) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			p.processTask(t)
		}(task)
	}

	// 等待所有任务完成（最多等待5分钟）
	timeout := time.After(5 * time.Minute)
	completed := 0

	for completed < len(tasks) {
		select {
		case <-timeout:
			logx.Errorf("任务处理超时，强制退出")
			return
		case <-time.After(100 * time.Millisecond):
			// 检查是否还有正在处理的任务
			if len(semaphore) == 0 {
				completed = len(tasks)
			}
		}
	}

	logx.Info("优惠券发放任务处理完成")
}

// processTask 处理单个任务
func (p *CouponIssueTaskProcessor) processTask(task *couponModel.CouponIssueTask) {
	startTime := time.Now()
	logx.Infof("开始处理任务 ID=%d, 名称=%s", task.ID, task.TaskName)

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Minute) // 单个任务最多3分钟
	defer cancel()

	// 处理任务
	err := p.couponService.ProcessIssueTask(ctx, task)

	duration := time.Since(startTime)

	if err != nil {
		logx.Errorf("处理任务失败 ID=%d, 耗时=%v, 错误=%v", task.ID, duration, err)
	} else {
		logx.Infof("处理任务成功 ID=%d, 耗时=%v", task.ID, duration)
	}
}

// CleanupExpiredTasks 清理过期的失败任务（可选）
func (p *CouponIssueTaskProcessor) CleanupExpiredTasks() {
	logx.Info("开始清理过期的失败任务")

	// TODO: 实现清理逻辑，比如删除7天前的失败任务
	// 这里可以根据业务需求决定是否需要

	logx.Info("过期任务清理完成")
}
