package middleware

import (
	"net/http"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
)

// WxCorsMiddleware 微信小程序端跨域中间件
func WxCorsMiddleware() rest.Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 获取请求的Origin
			origin := r.Header.Get("Origin")
			if origin == "" {
				origin = "*"
			}

			logx.Debugf("设置跨域响应头: %s %s, Origin=%s", r.Method, r.URL.Path, origin)

			// 根据请求来源设置CORS响应头
			w.Header().Set("Access-Control-Allow-Origin", origin)
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-CSRF-Token, X-Token, X-Api-Key, X-Auth-Token")
			w.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
			w.Header().Set("Access-Control-Allow-Credentials", "true")
			w.Header().Set("Access-Control-Max-Age", "86400") // 24小时

			next(w, r)
		}
	}
}

// NotAllowedHandler 处理405错误的处理器，主要用于处理OPTIONS预检请求
func NotAllowedHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 专门处理OPTIONS预检请求
		if r.Method == http.MethodOptions {
			logx.Infof("NotAllowedHandler处理OPTIONS预检请求: %s", r.URL.Path)

			// 获取请求的Origin
			origin := r.Header.Get("Origin")
			if origin == "" {
				origin = "*"
			}

			// 设置CORS头
			w.Header().Set("Access-Control-Allow-Origin", origin)
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-CSRF-Token, X-Token, X-Api-Key, X-Auth-Token")
			w.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
			w.Header().Set("Access-Control-Allow-Credentials", "true")
			w.Header().Set("Access-Control-Max-Age", "86400") // 24小时

			// 覆盖Allow头，避免框架自动生成的只含已注册方法的Allow头
			w.Header().Set("Allow", "GET, POST, PUT, DELETE, OPTIONS, PATCH")

			// 返回成功状态
			w.WriteHeader(http.StatusOK)
			return
		}

		// 非OPTIONS请求，返回普通的405错误
		logx.Infof("方法不允许: %s %s", r.Method, r.URL.Path)
		w.WriteHeader(http.StatusMethodNotAllowed)
	}
}

// WxLogMiddleware 微信小程序端日志中间件
func WxLogMiddleware() rest.Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		// 这里可以添加日志记录逻辑
		return func(w http.ResponseWriter, r *http.Request) {
			next(w, r)
		}
	}
}

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	svcCtx *svc.ServiceContext
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(svcCtx *svc.ServiceContext) *AuthMiddleware {
	return &AuthMiddleware{
		svcCtx: svcCtx,
	}
}

// Handle 处理认证
func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 这里简单实现，实际项目中应当验证token
		next(w, r)
	}
}
