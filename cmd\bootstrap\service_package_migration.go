package bootstrap

import (
	"yekaitai/internal/modules/service_setup/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateServicePackageTables 执行服务套餐管理模块表结构迁移
func MigrateServicePackageTables() error {
	db := mysql.Master()

	// 自动迁移服务套餐标签表
	if err := db.AutoMigrate(&model.ServiceTag{}); err != nil {
		logx.Errorf("迁移服务套餐标签表失败: %v", err)
		return err
	}
	logx.Info("服务套餐标签表迁移成功")

	// 自动迁移服务套餐表
	if err := db.AutoMigrate(&model.ServicePackage{}); err != nil {
		logx.Errorf("迁移服务套餐表失败: %v", err)
		return err
	}
	logx.Info("服务套餐表迁移成功")

	// 自动迁移服务套餐与门店关联表
	if err := db.AutoMigrate(&model.ServicePackageStoreRelation{}); err != nil {
		logx.Errorf("迁移服务套餐与门店关联表失败: %v", err)
		return err
	}
	logx.Info("服务套餐与门店关联表迁移成功")

	return nil
} 