package handler

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"

	storeModel "yekaitai/internal/modules/store/model"
	"yekaitai/pkg/adapters/hangzhou"
	"yekaitai/pkg/common/model/doctor"
	"yekaitai/pkg/common/model/patient"
	registration "yekaitai/pkg/common/model/registration"
	repo_registration "yekaitai/pkg/common/repository/registration"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/pkg/response"
	departmentModel "yekaitai/wx_internal/modules/department/model"
	regModel "yekaitai/wx_internal/modules/registration/model"
)

// AppointmentHandler 预约挂号处理器
type AppointmentHandler struct {
	appointmentRepo regModel.AppointmentRepository
}

// NewAppointmentHandler 创建预约挂号处理器
func NewAppointmentHandler() *AppointmentHandler {
	return &AppointmentHandler{
		appointmentRepo: regModel.NewAppointmentRepository(),
	}
}

// buildErrorResponse 构建带有示例的错误响应
func buildErrorResponse(w http.ResponseWriter, message string, errorDetail string, example interface{}) {
	errorResponse := struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Error   string      `json:"error"`
		Example interface{} `json:"example"`
	}{
		Code:    response.CodeInvalidParams,
		Message: message,
		Error:   errorDetail,
		Example: example,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK) // 使用200状态码但包含错误信息
	json.NewEncoder(w).Encode(errorResponse)
}

// Create 创建预约挂号
func (h *AppointmentHandler) Create(w http.ResponseWriter, r *http.Request) {
	// 读取并记录原始请求体
	var requestBody []byte
	if r.Body != nil {
		requestBody, _ = io.ReadAll(r.Body)
		// 重新设置请求体，否则后续无法读取
		r.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		// 记录原始请求内容
		logx.Infof("[预约挂号创建] 预约挂号请求体: %s", string(requestBody))
	}

	// 获取当前用户信息
	userInfo, err := hangzhou.GetCurrentUserInfo(r.Context())
	if err != nil {
		errMsg := fmt.Sprintf("[预约挂号创建] 获取当前用户信息失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("获取用户信息失败: %v", err))
		}
		return
	}

	var currentUser *hangzhou.CurrentUser
	if userData, ok := userInfo.Data.(*hangzhou.CurrentUser); ok && userData != nil {
		currentUser = userData
	} else {
		logx.Errorf("用户信息类型断言失败")
		response.Error(w, response.CodeInternalError, "用户信息格式错误")
		return
	}

	// 手动解析JSON，以避免字段缺失问题
	var requestMap map[string]interface{}
	if err := json.Unmarshal(requestBody, &requestMap); err != nil {
		logx.Errorf("解析请求JSON失败: %v, 请求体: %s", err, string(requestBody))
		buildErrorResponse(w, "请求参数解析失败", fmt.Sprintf("无效的JSON格式: %v", err), nil)
		return
	}

	// 首先获取HIS类型
	hisType := 1 // 默认为杭州HIS
	if v, ok := requestMap["his_type"].(float64); ok {
		hisType = int(v)
	}

	// 根据HIS类型构建不同的请求结构
	var req regModel.AppointmentRequest
	req.HisType = hisType

	if hisType == 2 { // ABC云
		// 构建ABC云请求
		abcReq := &regModel.AbcYunAppointmentRequest{}

		// 设置默认值
		abcReq.YyLx = 5 // 默认5-微信小程序预约
		abcReq.BzXx = "微信小程序预约"
		abcReq.CzrID = currentUser.YhID // 使用当前用户ID
		abcReq.Czr = currentUser.YhMC   // 使用当前用户名称

		// 解析本地系统必填字段
		if v, ok := requestMap["wx_doctor_id"].(float64); ok {
			abcReq.WxDoctorID = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "医生ID不能为空", map[string]interface{}{
				"wx_doctor_id": "必须提供医生ID",
			})
			return
		}

		if v, ok := requestMap["wx_patient_id"].(float64); ok {
			abcReq.WxPatientID = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "患者ID不能为空", map[string]interface{}{
				"wx_patient_id": "必须提供患者ID",
			})
			return
		}

		if v, ok := requestMap["store_id"].(float64); ok {
			abcReq.StoreID = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "门店ID不能为空", map[string]interface{}{
				"store_id": "必须提供门店ID",
			})
			return
		}

		if v, ok := requestMap["appoint_date"].(string); ok && v != "" {
			abcReq.AppointDate = v
		} else {
			buildErrorResponse(w, "参数验证失败", "预约日期不能为空", map[string]interface{}{
				"appoint_date": "必须提供预约日期",
			})
			return
		}

		if v, ok := requestMap["appoint_period"].(float64); ok {
			abcReq.AppointPeriod = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "预约时段不能为空", map[string]interface{}{
				"appoint_period": "必须提供预约时段",
			})
			return
		}

		// ABC云特有字段
		if v, ok := requestMap["order_no"].(float64); ok {
			abcReq.OrderNo = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "排队号不能为空", map[string]interface{}{
				"order_no": "必须提供排队号",
			})
			return
		}

		if v, ok := requestMap["reserve_start"].(string); ok && v != "" {
			abcReq.ReserveStart = v
		} else {
			buildErrorResponse(w, "参数验证失败", "预约开始时间不能为空", map[string]interface{}{
				"reserve_start": "必须提供预约开始时间",
			})
			return
		}

		if v, ok := requestMap["reserve_end"].(string); ok && v != "" {
			abcReq.ReserveEnd = v
		} else {
			buildErrorResponse(w, "参数验证失败", "预约结束时间不能为空", map[string]interface{}{
				"reserve_end": "必须提供预约结束时间",
			})
			return
		}

		if v, ok := requestMap["registration_type"].(float64); ok {
			abcReq.RegistrationType = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "预约类型不能为空", map[string]interface{}{
				"registration_type": "必须提供预约类型",
			})
			return
		}

		// 可选字段
		if v, ok := requestMap["source_id"].(string); ok {
			abcReq.SourceID = v
		}
		if v, ok := requestMap["source_from_id"].(string); ok {
			abcReq.SourceFromID = v
		}
		if v, ok := requestMap["source_remark"].(string); ok {
			abcReq.SourceRemark = v
		}
		if v, ok := requestMap["yylx"].(float64); ok {
			abcReq.YyLx = int(v)
		}
		if v, ok := requestMap["bzxx"].(string); ok {
			abcReq.BzXx = v
		}

		// 查询门店信息设置WsjgIDStr
		storeRepo := storeModel.NewStoreRepository(mysql.Slave())
		store, err := storeRepo.FindByID(uint(abcReq.StoreID))
		if err != nil {
			logx.Errorf("查询门店信息失败: %v, 门店ID=%d", err, abcReq.StoreID)
			response.Error(w, response.CodeInternalError, "查询门店信息失败")
			return
		}
		abcReq.WsjgIDStr = store.WsjgID

		// 查询患者信息获取ABC云患者ID
		patientRepo := patient.NewWxPatientRepository(mysql.Slave())
		patientInfo, err := patientRepo.FindByID(uint(abcReq.WxPatientID))
		if err != nil {
			logx.Errorf("查询患者信息失败: %v, 患者ID=%d", err, abcReq.WxPatientID)
			response.Error(w, response.CodeInternalError, "查询患者信息失败")
			return
		}
		if patientInfo.AbcyunPatientID == "" {
			logx.Errorf("ABC云预约失败: 患者的ABC云ID为空, 患者ID=%d", abcReq.WxPatientID)
			response.Error(w, response.CodeInvalidParams, "患者未绑定ABC云ID，无法使用ABC云预约")
			return
		}
		abcReq.AbcYunPatientID = patientInfo.AbcyunPatientID

		// 查询医生信息获取ABC云医生ID
		doctorRepo := doctor.NewWxDoctorRepository(mysql.Slave())
		doctorInfo, err := doctorRepo.FindByID(uint(abcReq.WxDoctorID))
		if err != nil {
			logx.Errorf("查询医生信息失败: %v, 医生ID=%d", err, abcReq.WxDoctorID)
			response.Error(w, response.CodeInternalError, "查询医生信息失败")
			return
		}
		if doctorInfo.YsID == "" {
			logx.Errorf("ABC云预约失败: 医生的ABC云ID为空, 医生ID=%d", abcReq.WxDoctorID)
			response.Error(w, response.CodeInvalidParams, "医生未绑定ABC云ID，无法使用ABC云预约")
			return
		}
		abcReq.AbcYunDoctorID = doctorInfo.YsID

		// 查询科室信息获取ABC云科室ID
		// 这里需要根据前端传递的科室参数确定，暂时从请求中获取
		if v, ok := requestMap["abcyun_department_id"].(string); ok && v != "" {
			abcReq.AbcYunDepartmentID = v
		} else {
			buildErrorResponse(w, "参数验证失败", "ABC云科室ID不能为空", map[string]interface{}{
				"abcyun_department_id": "必须提供ABC云科室ID",
			})
			return
		}

		req.AbcYunRequest = abcReq

	} else { // 杭州HIS (默认)
		// 构建杭州HIS请求
		hzReq := &regModel.HangzhouAppointmentRequest{}

		// 设置默认值
		hzReq.YyLx = 5 // 默认5-微信小程序预约
		hzReq.BzXx = "微信小程序预约"
		hzReq.CzrID = currentUser.YhID // 使用当前用户ID
		hzReq.Czr = currentUser.YhMC   // 使用当前用户名称

		// 解析杭州HIS必填字段
		if v, ok := requestMap["hyjlid"].(float64); ok {
			hzReq.HyjlID = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "号源记录ID不能为空", map[string]interface{}{
				"hyjlid": "必须提供号源记录ID",
			})
			return
		}

		if v, ok := requestMap["jgksid"].(float64); ok {
			hzReq.JgksID = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "机构科室ID不能为空", map[string]interface{}{
				"jgksid": "必须提供机构科室ID",
			})
			return
		}

		// 本地系统字段
		if v, ok := requestMap["wx_doctor_id"].(float64); ok {
			hzReq.WxDoctorID = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "医生ID不能为空", map[string]interface{}{
				"wx_doctor_id": "必须提供医生ID",
			})
			return
		}

		if v, ok := requestMap["wx_patient_id"].(float64); ok {
			hzReq.WxPatientID = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "患者ID不能为空", map[string]interface{}{
				"wx_patient_id": "必须提供患者ID",
			})
			return
		}

		if v, ok := requestMap["store_id"].(float64); ok {
			hzReq.StoreID = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "门店ID不能为空", map[string]interface{}{
				"store_id": "必须提供门店ID",
			})
			return
		}

		if v, ok := requestMap["appoint_date"].(string); ok && v != "" {
			hzReq.AppointDate = v
		} else {
			buildErrorResponse(w, "参数验证失败", "预约日期不能为空", map[string]interface{}{
				"appoint_date": "必须提供预约日期",
			})
			return
		}

		if v, ok := requestMap["appoint_period"].(float64); ok {
			hzReq.AppointPeriod = int(v)
		} else {
			buildErrorResponse(w, "参数验证失败", "预约时段不能为空", map[string]interface{}{
				"appoint_period": "必须提供预约时段",
			})
			return
		}

		// 可选字段
		if v, ok := requestMap["grxxid"].(float64); ok {
			hzReq.GrxxID = int(v)
		}
		if v, ok := requestMap["ysmc"].(string); ok {
			hzReq.YsMC = v
		}
		if v, ok := requestMap["jgksmc"].(string); ok {
			hzReq.JgksMC = v
		}
		if v, ok := requestMap["wsjgmc"].(string); ok {
			hzReq.WsjgMC = v
		}
		if v, ok := requestMap["yylx"].(float64); ok {
			hzReq.YyLx = int(v)
		}
		if v, ok := requestMap["bzxx"].(string); ok {
			hzReq.BzXx = v
		}

		// 查询门店信息设置WsjgID
		storeRepo := storeModel.NewStoreRepository(mysql.Slave())
		store, err := storeRepo.FindByID(uint(hzReq.StoreID))
		if err != nil {
			logx.Errorf("查询门店信息失败: %v, 门店ID=%d", err, hzReq.StoreID)
			response.Error(w, response.CodeInternalError, "查询门店信息失败")
			return
		}
		wsjgID, err := strconv.Atoi(store.WsjgID)
		if err != nil {
			logx.Errorf("门店卫生机构ID转换失败: %v, 门店ID=%d", err, hzReq.StoreID)
			response.Error(w, response.CodeInternalError, "门店卫生机构ID转换失败")
			return
		}
		hzReq.WsjgID = wsjgID
		hzReq.WsjgMC = store.Name

		// 查询患者信息获取GrxxID
		patientRepo := patient.NewWxPatientRepository(mysql.Slave())
		patientInfo, err := patientRepo.FindByID(uint(hzReq.WxPatientID))
		if err != nil {
			logx.Errorf("查询患者信息失败: %v, 患者ID=%d", err, hzReq.WxPatientID)
			response.Error(w, response.CodeInternalError, "查询患者信息失败")
			return
		}
		if patientInfo.HangzhouHisID != "" {
			grxxID, err := strconv.Atoi(patientInfo.HangzhouHisID)
			if err != nil {
				logx.Errorf("转换GrxxID失败: %v, 患者ID=%d", err, hzReq.WxPatientID)
				hzReq.GrxxID = 0
			} else {
				hzReq.GrxxID = grxxID
			}
		}

		// 查询医生信息
		if hzReq.WxDoctorID > 0 {
			doctorRepo := doctor.NewWxDoctorRepository(mysql.Slave())
			doctorInfo, err := doctorRepo.FindByID(uint(hzReq.WxDoctorID))
			if err != nil {
				logx.Errorf("查询医生信息失败: %v, 医生ID=%d", err, hzReq.WxDoctorID)
				response.Error(w, response.CodeInternalError, "查询医生信息失败")
				return
			}
			hzReq.YsID = doctorInfo.YsID
			hzReq.YsMC = doctorInfo.Name
		} else {
			hzReq.YsID = "0"
			hzReq.YsMC = "普通医生"
		}

		// 查询科室信息
		if hzReq.JgksID > 0 {
			deptRepo := departmentModel.NewDepartmentRepository()
			dept, err := deptRepo.FindByJgksID(hzReq.JgksID)
			if err == nil && dept != nil {
				hzReq.JgksMC = dept.JgksMC
			}
		}

		req.HangzhouRequest = hzReq
	}

	// 打印解析后的请求参数，辅助调试
	logx.Infof("预约挂号请求参数，HIS类型=%d: %+v", hisType, req)

	// 创建预约挂号
	resp, err := h.appointmentRepo.CreateAppointment(r.Context(), &req)
	if err != nil {
		// 详细记录错误信息
		errMsg := fmt.Sprintf("[预约挂号创建] 创建预约挂号失败: %v", err)
		logx.Errorf(errMsg)

		// 尝试解析错误信息，如果是JSON格式的，则提取message字段
		var hisErrorMsg string
		errStr := err.Error()

		// 检查是否包含JSON格式的错误消息
		if strings.Contains(errStr, "{\"message\":") {
			// 提取JSON部分
			jsonStartIndex := strings.Index(errStr, "{")
			jsonEndIndex := strings.LastIndex(errStr, "}")
			if jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex {
				jsonStr := errStr[jsonStartIndex : jsonEndIndex+1]
				var errMap map[string]interface{}
				if jsonErr := json.Unmarshal([]byte(jsonStr), &errMap); jsonErr == nil {
					if msg, ok := errMap["message"].(string); ok {
						hisErrorMsg = msg
					}
				}
			}
		}

		if hisErrorMsg != "" {
			// 使用HIS系统返回的原始错误信息
			logx.Errorf("[预约挂号创建] 使用HIS系统原始错误: %s", hisErrorMsg)
			response.Error(w, response.CodeInternalError, hisErrorMsg)
		} else {
			// 检查具体错误类型并返回详细信息给前端
			if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
			} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
			} else if strings.Contains(errStr, "timeout") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
			} else {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("创建预约挂号失败: %v", err))
			}
		}
		return
	}

	// 记录成功结果
	logx.Infof("[预约挂号创建] 创建成功，预约ID: %v", resp)
	response.Success(w, resp)
}

// Cancel 取消预约挂号
func (h *AppointmentHandler) Cancel(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[预约挂号取消] 开始处理请求: %s %s", r.Method, r.URL.String())

	// 解析请求体中的JSON数据
	var cancelReq struct {
		ID    uint   `json:"id"`    // 本地挂号订单记录表ID
		ZfrID int    `json:"zfrid"` // 作废人ID
		ZfrMC string `json:"zfrmc"` // 作废人名称
		ZfYy  string `json:"zfyy"`  // 作废原因
	}

	if err := json.NewDecoder(r.Body).Decode(&cancelReq); err != nil {
		errMsg := fmt.Sprintf("[预约挂号取消] 解析取消预约请求参数失败: %v", err)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("请求参数解析失败: %v", err))
		return
	}

	// 记录请求参数
	logx.Infof("[预约挂号取消] 请求参数: ID=%d, ZfrID=%d, ZfrMC=%s, ZfYy=%s", cancelReq.ID, cancelReq.ZfrID, cancelReq.ZfrMC, cancelReq.ZfYy)

	if cancelReq.ID <= 0 {
		errMsg := fmt.Sprintf("[预约挂号取消] 本地挂号订单记录表ID无效: %d", cancelReq.ID)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("挂号订单ID不能为空: %d", cancelReq.ID))
		return
	}

	// 查询本地订单信息
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	existingOrder, err := orderRepo.GetAppointmentOrderByID(r.Context(), cancelReq.ID)
	if err != nil {
		errMsg := fmt.Sprintf("[预约挂号取消] 查询预约挂号订单失败: %v, ID=%d", err, cancelReq.ID)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInternalError, fmt.Sprintf("未找到预约订单信息: %v", err))
		return
	}

	// 验证1：检查核销状态，如果已核销则不能取消
	if existingOrder.VerificationStatus == int(registration.VerificationStatusVerified) {
		errMsg := fmt.Sprintf("[预约挂号取消] 订单已核销，不能取消: orderID=%d, verificationStatus=%d", cancelReq.ID, existingOrder.VerificationStatus)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("订单已核销，不能取消: 订单ID=%d", cancelReq.ID))
		return
	}

	// 验证2：检查订单状态，只有1(待就诊)和2(待支付)可以取消
	if existingOrder.OrderStatus != registration.OrderStatusWaitDiagnosis && existingOrder.OrderStatus != registration.OrderStatusWaitPay {
		errMsg := fmt.Sprintf("[预约挂号取消] 订单状态不允许取消: orderID=%d, orderStatus=%d", cancelReq.ID, int(existingOrder.OrderStatus))
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("当前订单状态不允许取消，状态：%s, 订单ID=%d", regModel.GetOrderStatusText(existingOrder.OrderStatus), cancelReq.ID))
		return
	}

	// 检查作废人信息 - 统一获取当前用户信息
	// 获取当前用户信息
	userInfo, err := hangzhou.GetCurrentUserInfo(r.Context())
	if err != nil {
		logx.Errorf("获取当前用户信息失败: %v", err)
		response.Error(w, response.CodeInternalError, "获取用户信息失败")
		return
	}

	var currentUser *hangzhou.CurrentUser
	if userData, ok := userInfo.Data.(*hangzhou.CurrentUser); ok && userData != nil {
		currentUser = userData
	} else {
		logx.Errorf("用户信息类型断言失败")
		response.Error(w, response.CodeInternalError, "用户信息格式错误")
		return
	}

	// 如果请求中没有提供作废人信息，使用当前用户信息
	if cancelReq.ZfrID == 0 || cancelReq.ZfrMC == "" {
		cancelReq.ZfrID = currentUser.YhID
		cancelReq.ZfrMC = currentUser.YhMC
	}

	// 设置默认作废原因
	if cancelReq.ZfYy == "" {
		cancelReq.ZfYy = "用户取消预约"
	}

	// 根据订单的HIS类型构建取消请求
	var req regModel.CancelAppointmentRequest
	req.HisType = int(existingOrder.HisType)
	req.ZfrID = cancelReq.ZfrID
	req.ZfrMC = cancelReq.ZfrMC
	req.ZfYy = cancelReq.ZfYy

	if existingOrder.HisType == 2 { // ABC云
		// 设置ABC云字段
		req.RegistrationSheetID = existingOrder.RegistrationSheetID
		req.PatientOrderID = existingOrder.PatientOrderID
	} else { // 杭州HIS
		// 设置杭州HIS字段
		req.YyghID = existingOrder.YyghID
		req.GrxxID = existingOrder.GrxxID
		req.HyjlID = existingOrder.HyjlID
		req.YyLx = existingOrder.YylX
	}

	// 记录请求参数，用于调试
	if existingOrder.HisType == 2 {
		logx.Infof("取消ABC云预约挂号请求参数: registrationSheetID=%s, patientOrderID=%s, zfrID=%d, zfrMC=%s, zfYy=%s",
			req.RegistrationSheetID, req.PatientOrderID, req.ZfrID, req.ZfrMC, req.ZfYy)
	} else {
		logx.Infof("取消杭州HIS预约挂号请求参数: yyghID=%d, grxxID=%d, hyjlID=%d, yylx=%d, zfrID=%d, zfrMC=%s, zfYy=%s",
			req.YyghID, req.GrxxID, req.HyjlID, req.YyLx, req.ZfrID, req.ZfrMC, req.ZfYy)
	}

	// 取消预约挂号
	resp, err := h.appointmentRepo.CancelAppointment(r.Context(), &req)
	if err != nil {
		// 详细记录错误信息
		errMsg := fmt.Sprintf("[预约挂号取消] 取消预约挂号失败: %v", err)
		logx.Errorf(errMsg)

		// 尝试解析错误信息，如果是JSON格式的，则提取message字段
		var hisErrorMsg string
		errStr := err.Error()

		// 检查是否包含JSON格式的错误消息
		if strings.Contains(errStr, "{\"message\":") {
			// 提取JSON部分
			jsonStartIndex := strings.Index(errStr, "{")
			jsonEndIndex := strings.LastIndex(errStr, "}")
			if jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex {
				jsonStr := errStr[jsonStartIndex : jsonEndIndex+1]
				var errMap map[string]interface{}
				if jsonErr := json.Unmarshal([]byte(jsonStr), &errMap); jsonErr == nil {
					if msg, ok := errMap["message"].(string); ok {
						hisErrorMsg = msg
					}
				}
			}
		}

		if hisErrorMsg != "" {
			// 使用HIS系统返回的原始错误信息
			logx.Errorf("[预约挂号取消] 使用HIS系统原始错误: %s", hisErrorMsg)
			response.Error(w, response.CodeInternalError, hisErrorMsg)
		} else {
			// 检查具体错误类型并返回详细信息给前端
			if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
			} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
			} else if strings.Contains(errStr, "timeout") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
			} else {
				// 直接使用完整的错误信息，而不是通用错误消息
				response.Error(w, response.CodeInternalError, fmt.Sprintf("取消预约挂号失败: %v", err))
			}
		}
		return
	}

	// 记录成功结果
	logx.Infof("[预约挂号取消] 取消成功，订单ID: %d", cancelReq.ID)
	response.Success(w, resp)
}

// GetAppointmentList 获取挂号订单列表
func (h *AppointmentHandler) GetAppointmentList(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[预约订单列表] 开始处理请求: %s %s", r.Method, r.URL.String())

	// 从查询参数中获取筛选条件
	patientID := r.URL.Query().Get("patient_id")
	status := r.URL.Query().Get("status") // 可选：all(全部)、pending(待就诊)、wait_pay(待支付)、completed(已完成)、canceled(已取消)

	// 记录查询参数
	logx.Infof("[预约订单列表] 查询参数: patient_id=%s, status=%s", patientID, status)

	// 转换患者ID为整数
	var wxPatientID int
	var err error
	if patientID != "" {
		wxPatientID, err = strconv.Atoi(patientID)
		if err != nil {
			errMsg := fmt.Sprintf("[预约订单列表] 患者ID解析失败: %v", err)
			logx.Errorf(errMsg)
			response.Error(w, response.CodeInvalidParams, fmt.Sprintf("患者ID无效: %v", err))
			return
		}
	}

	// 查询订单列表
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	orders, err := orderRepo.GetAppointmentOrdersByPatientID(r.Context(), wxPatientID, status)
	if err != nil {
		errMsg := fmt.Sprintf("[预约订单列表] 查询预约挂号订单列表失败: %v", err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("获取挂号订单列表失败: %v", err))
		}
		return
	}

	// 记录成功结果
	logx.Infof("[预约订单列表] 查询成功，返回订单数量: %d", len(orders))

	// 构建返回数据
	result := struct {
		Total  int                                      `json:"total"`
		Orders []*regModel.AppointmentOrderListResponse `json:"orders"`
	}{
		Total:  len(orders),
		Orders: make([]*regModel.AppointmentOrderListResponse, 0, len(orders)),
	}

	// 格式化订单数据
	for _, order := range orders {
		// 判断是否可以取消（待就诊订单进入就诊日当天 00:00 的不可取消）
		canCancel := true
		if order.OrderStatus == 1 { // 待就诊状态
			// 解析预约日期
			appointDate, err := time.Parse("2006-01-02", order.AppointDate)
			if err == nil {
				// 获取预约日期的零点时间
				appointZeroTime := time.Date(appointDate.Year(), appointDate.Month(), appointDate.Day(), 0, 0, 0, 0, time.Local)
				// 如果当前时间大于预约日期零点，则不可取消
				if time.Now().After(appointZeroTime) || time.Now().Equal(appointZeroTime) {
					canCancel = false
				}
			}
		} else if order.OrderStatus != 2 { // 非待就诊和待支付状态不可取消
			canCancel = false
		}

		// 查询关联的患者、医生、门店名称
		patientName := ""
		doctorName := ""
		storeName := ""

		// 查询患者姓名
		if order.WxPatientID > 0 {
			patientRepo := patient.NewWxPatientRepository(nil)
			if patientInfo, err := patientRepo.FindByID(uint(order.WxPatientID)); err == nil && patientInfo != nil {
				patientName = patientInfo.Name
			}
		}

		// 查询医生姓名
		if order.WxDoctorID > 0 {
			doctorRepo := doctor.NewWxDoctorRepository(nil)
			if doctorInfo, err := doctorRepo.FindByID(uint(order.WxDoctorID)); err == nil && doctorInfo != nil {
				doctorName = doctorInfo.Name
			}
		}

		// 查询门店名称
		if order.StoreID > 0 {
			storeRepo := storeModel.NewStoreRepository(nil)
			if storeInfo, err := storeRepo.FindByID(uint(order.StoreID)); err == nil && storeInfo != nil {
				storeName = storeInfo.Name
			}
		}

		// 准备订单信息，返回全部表字段
		orderItem := &regModel.AppointmentOrderListResponse{
			ID:                     order.ID,
			OrderNo:                order.OrderNo,
			AppointDate:            order.AppointDate,
			AppointPeriod:          int(order.AppointPeriod),
			AppointPeriodText:      registration.GetAppointPeriodText(order.AppointPeriod),
			SyncStatus:             int(order.SyncStatus),
			SyncStatusText:         regModel.GetSyncStatusText(int(order.SyncStatus)),
			OrderStatus:            int(order.OrderStatus),
			StatusText:             regModel.GetOrderStatusText(int(order.OrderStatus)),
			HisType:                int(order.HisType),
			YyghID:                 order.YyghID,
			GrxxID:                 order.GrxxID,
			HyjlID:                 order.HyjlID,
			YylX:                   order.YylX,
			WxDoctorID:             order.WxDoctorID,
			WxPatientID:            order.WxPatientID,
			StoreID:                order.StoreID,
			ZfrID:                  order.ZfrID,
			ZfrMC:                  order.ZfrMC,
			ZfYY:                   order.ZfYY,
			ZfSJ:                   order.ZfSJ,
			CzrID:                  order.CzrID,
			Czr:                    order.Czr,
			QRCodeURL:              order.QRCodeURL,
			VerificationCode:       order.VerificationCode,
			VerificationStatus:     order.VerificationStatus,
			VerificationStatusText: regModel.GetVerificationStatusText(order.VerificationStatus),
			VerifiedAt:             order.VerifiedAt,
			VerifierID:             order.VerifierID,
			VerifierName:           order.VerifierName,
			JgksID:                 order.JgksID,
			DepartmentID:           order.DepartmentID,
			CanCancel:              canCancel,
			CreatedAt:              order.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:              order.UpdatedAt.Format("2006-01-02 15:04:05"),
			// 新增全部表字段
			RequestData:         order.RequestData,
			ResponseData:        order.ResponseData,
			RegistrationSheetID: order.RegistrationSheetID,
			PatientOrderID:      order.PatientOrderID,
			// 新增名字字段
			PatientName: patientName,
			DoctorName:  doctorName,
			StoreName:   storeName,
		}
		result.Orders = append(result.Orders, orderItem)
	}

	response.Success(w, result)
}

// GetAppointmentDetail 获取挂号订单详情
func (h *AppointmentHandler) GetAppointmentDetail(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[预约订单详情] 开始处理请求: %s %s", r.Method, r.URL.String())

	// 从URL路径中获取预约订单ID
	parts := strings.Split(r.URL.Path, "/")
	if len(parts) == 0 {
		errMsg := fmt.Sprintf("[预约订单详情] 无法解析URL路径: %s", r.URL.Path)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("预约订单ID不能为空，URL路径: %s", r.URL.Path))
		return
	}

	// 获取路径中的最后一个部分作为订单ID
	orderIDStr := parts[len(parts)-1]
	logx.Infof("[预约订单详情] URL路径: %s, 解析到orderID: %s", r.URL.Path, orderIDStr)

	if orderIDStr == "" {
		errMsg := "[预约订单详情] 未找到预约订单ID参数"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("预约订单ID不能为空，URL路径: %s", r.URL.Path))
		return
	}

	// 转换为整数
	orderID, err := strconv.Atoi(orderIDStr)
	if err != nil || orderID <= 0 {
		errMsg := fmt.Sprintf("[预约订单详情] 预约订单ID无效: %s, 错误: %v", orderIDStr, err)
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, fmt.Sprintf("预约订单ID无效: %s, 错误: %v", orderIDStr, err))
		return
	}

	// 查询订单详情
	orderRepo := repo_registration.NewAppointmentOrderRepository()
	order, err := orderRepo.GetAppointmentOrderByID(r.Context(), uint(orderID))
	if err != nil {
		errMsg := fmt.Sprintf("[预约订单详情] 查询预约挂号订单详情失败: %v, ID=%d", err, orderID)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		errStr := err.Error()
		if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
		} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "timeout") {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
		} else if strings.Contains(errStr, "record not found") {
			response.Error(w, response.CodeNotFound, fmt.Sprintf("未找到订单信息，订单ID: %d", orderID))
		} else {
			response.Error(w, response.CodeInternalError, fmt.Sprintf("获取挂号订单详情失败: %v, 订单ID: %d", err, orderID))
		}
		return
	}

	// 记录成功结果
	logx.Infof("[预约订单详情] 查询成功，订单ID: %d", orderID)

	// 判断是否可以取消（待就诊订单进入就诊日当天 00:00 的不可取消）
	canCancel := true
	if order.OrderStatus == 1 { // 待就诊状态
		// 解析预约日期
		appointDate, err := time.Parse("2006-01-02", order.AppointDate)
		if err == nil {
			// 获取预约日期的零点时间
			appointZeroTime := time.Date(appointDate.Year(), appointDate.Month(), appointDate.Day(), 0, 0, 0, 0, time.Local)
			// 如果当前时间大于预约日期零点，则不可取消
			if time.Now().After(appointZeroTime) || time.Now().Equal(appointZeroTime) {
				canCancel = false
			}
		}
	} else if order.OrderStatus != 2 { // 非待就诊和待支付状态不可取消
		canCancel = false
	}

	// 使用真实数据替代示例数据
	// 解析请求和响应数据获取更多信息
	var requestData map[string]interface{}
	var responseData map[string]interface{}

	// 尝试解析请求数据JSON
	requestInfo := order.RequestData
	if requestInfo != "" {
		if err := json.Unmarshal([]byte(requestInfo), &requestData); err != nil {
			logx.Errorf("解析请求数据失败: %v", err)
		}
	}

	// 尝试解析响应数据JSON
	responseInfo := order.ResponseData
	if responseInfo != "" {
		if err := json.Unmarshal([]byte(responseInfo), &responseData); err != nil {
			logx.Errorf("解析响应数据失败: %v", err)
		}
	}

	// 从解析的数据中提取更多信息
	storeAddress := ""
	storePhone := ""
	departmentName := ""

	// 查询关联的患者、医生、门店信息
	patientName := ""
	doctorName := ""
	storeName := ""

	// 查询患者姓名
	if order.WxPatientID > 0 {
		patientRepo := patient.NewWxPatientRepository(nil)
		if patientInfo, err := patientRepo.FindByID(uint(order.WxPatientID)); err == nil && patientInfo != nil {
			patientName = patientInfo.Name
		}
	}

	// 查询医生姓名
	if order.WxDoctorID > 0 {
		doctorRepo := doctor.NewWxDoctorRepository(nil)
		if doctorInfo, err := doctorRepo.FindByID(uint(order.WxDoctorID)); err == nil && doctorInfo != nil {
			doctorName = doctorInfo.Name
		}
	}

	// 查询门店信息
	if order.StoreID > 0 {
		storeRepo := storeModel.NewStoreRepository(nil)
		if storeInfo, err := storeRepo.FindByID(uint(order.StoreID)); err == nil && storeInfo != nil {
			storeName = storeInfo.Name
			storeAddress = storeInfo.Address
			storePhone = storeInfo.Phone
		}
	}

	// 从响应数据中提取科室名称
	if responseData != nil {
		if deptName, ok := responseData["jgksmc"].(string); ok && deptName != "" {
			departmentName = deptName
		}
	}

	// 尝试通过请求数据或在线API获取门店信息
	// 这里可以添加调用门店API的代码

	// 初始化支付相关信息
	var originalFee float64 = 0
	var couponAmount float64 = 0
	var pointsAmount float64 = 0
	var payAmount float64 = 0
	var payTime string = ""

	// 从响应数据中提取费用信息
	if responseData != nil {
		// 挂号费
		if ghf, ok := responseData["ghf"].(float64); ok {
			originalFee += ghf
		}

		// 诊疗费
		if zlf, ok := responseData["zlf"].(float64); ok {
			originalFee += zlf
		}

		// 设置支付金额
		payAmount = originalFee - couponAmount - pointsAmount
	}

	// 构建详细响应
	detail := &regModel.AppointmentOrderDetailResponse{
		ID:                order.ID,
		OrderNo:           order.OrderNo,
		StoreAddress:      storeAddress,
		StorePhone:        storePhone,
		AppointDate:       order.AppointDate,
		AppointPeriod:     int(order.AppointPeriod),
		AppointPeriodText: registration.GetAppointPeriodText(order.AppointPeriod),
		Department:        departmentName,
		OrderStatus:       int(order.OrderStatus),
		StatusText:        regModel.GetOrderStatusText(int(order.OrderStatus)),
		CanCancel:         canCancel,
		OriginalFee:       originalFee,
		CouponAmount:      couponAmount,
		PointsAmount:      pointsAmount,
		PayAmount:         payAmount,
		CreatedAt:         order.CreatedAt.Format("2006-01-02 15:04:05"),
		PayTime:           payTime,
		QRCodeURL:         order.QRCodeURL,
		CancelReason:      order.ZfYY,
		CancelTime:        order.ZfSJ,
		// 新增名字字段
		PatientName: patientName,
		DoctorName:  doctorName,
		StoreName:   storeName,
	}

	response.Success(w, detail)
}

// GetAppointmentDetailByParams 通过参数获取挂号订单详情（支持yyghid和registration_sheet_id查询）
func (h *AppointmentHandler) GetAppointmentDetailByParams(w http.ResponseWriter, r *http.Request) {
	// 记录请求开始
	logx.Infof("[预约订单详情查询] 开始处理请求: %s %s", r.Method, r.URL.String())

	// 从查询参数中获取查询条件
	yyghidStr := r.URL.Query().Get("yyghid")
	registrationSheetID := r.URL.Query().Get("registration_sheet_id")

	// 记录查询参数
	logx.Infof("[预约订单详情查询] 查询参数: yyghid=%s, registration_sheet_id=%s", yyghidStr, registrationSheetID)

	// 验证参数：至少提供一个查询条件
	if yyghidStr == "" && registrationSheetID == "" {
		errMsg := "[预约订单详情查询] 查询参数不能为空"
		logx.Errorf(errMsg)
		response.Error(w, response.CodeInvalidParams, "查询参数不能为空，请提供yyghid或registration_sheet_id")
		return
	}

	var order *registration.AppointmentOrder
	var err error
	orderRepo := repo_registration.NewAppointmentOrderRepository()

	// 根据提供的参数进行查询
	if yyghidStr != "" {
		// 通过yyghid查询
		yyghid, parseErr := strconv.Atoi(yyghidStr)
		if parseErr != nil || yyghid <= 0 {
			errMsg := fmt.Sprintf("[预约订单详情查询] yyghid参数无效: %s, 错误: %v", yyghidStr, parseErr)
			logx.Errorf(errMsg)
			response.Error(w, response.CodeInvalidParams, fmt.Sprintf("yyghid参数无效: %s", yyghidStr))
			return
		}

		logx.Infof("[预约订单详情查询] 通过yyghid查询: %d", yyghid)
		order, err = orderRepo.GetAppointmentOrderByYyghID(r.Context(), yyghid)
		if err != nil {
			logx.Errorf("[预约订单详情查询] 通过yyghid查询失败: %v, yyghid=%d", err, yyghid)
		}
	}

	// 如果通过yyghid没有找到，且提供了registration_sheet_id，则通过registration_sheet_id查询
	if order == nil && registrationSheetID != "" {
		logx.Infof("[预约订单详情查询] 通过registration_sheet_id查询: %s", registrationSheetID)
		order, err = orderRepo.GetAppointmentOrderByRegistrationSheetID(r.Context(), registrationSheetID)
		if err != nil {
			logx.Errorf("[预约订单详情查询] 通过registration_sheet_id查询失败: %v, registration_sheet_id=%s", err, registrationSheetID)
		}
	}

	// 检查是否找到订单
	if order == nil || err != nil {
		errMsg := fmt.Sprintf("[预约订单详情查询] 未找到订单: yyghid=%s, registration_sheet_id=%s, 错误: %v", yyghidStr, registrationSheetID, err)
		logx.Errorf(errMsg)

		// 检查具体错误类型并返回详细信息给前端
		if err != nil {
			errStr := err.Error()
			if strings.Contains(errStr, "record not found") {
				response.Error(w, response.CodeNotFound, fmt.Sprintf("未找到订单信息，查询参数: yyghid=%s, registration_sheet_id=%s", yyghidStr, registrationSheetID))
			} else if strings.Contains(errStr, "认证请求失败") || strings.Contains(errStr, "获取访问令牌失败") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("认证失败，请检查网络连接: %v", err))
			} else if strings.Contains(errStr, "EOF") || strings.Contains(errStr, "connection") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("网络连接异常，请稍后重试: %v", err))
			} else if strings.Contains(errStr, "timeout") {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("请求超时，请稍后重试: %v", err))
			} else {
				response.Error(w, response.CodeInternalError, fmt.Sprintf("获取挂号订单详情失败: %v", err))
			}
		} else {
			response.Error(w, response.CodeNotFound, fmt.Sprintf("未找到订单信息，查询参数: yyghid=%s, registration_sheet_id=%s", yyghidStr, registrationSheetID))
		}
		return
	}

	// 记录成功结果
	logx.Infof("[预约订单详情查询] 查询成功，订单ID: %d", order.ID)

	// 判断是否可以取消（待就诊订单进入就诊日当天 00:00 的不可取消）
	canCancel := true
	if order.OrderStatus == 1 { // 待就诊状态
		// 解析预约日期
		appointDate, err := time.Parse("2006-01-02", order.AppointDate)
		if err == nil {
			// 获取预约日期的零点时间
			appointZeroTime := time.Date(appointDate.Year(), appointDate.Month(), appointDate.Day(), 0, 0, 0, 0, time.Local)
			// 如果当前时间大于预约日期零点，则不可取消
			if time.Now().After(appointZeroTime) || time.Now().Equal(appointZeroTime) {
				canCancel = false
			}
		}
	} else if order.OrderStatus != 2 { // 非待就诊和待支付状态不可取消
		canCancel = false
	}

	// 使用真实数据替代示例数据
	// 解析请求和响应数据获取更多信息
	var requestData map[string]interface{}
	var responseData map[string]interface{}

	// 尝试解析请求数据JSON
	requestInfo := order.RequestData
	if requestInfo != "" {
		if err := json.Unmarshal([]byte(requestInfo), &requestData); err != nil {
			logx.Errorf("解析请求数据失败: %v", err)
		}
	}

	// 尝试解析响应数据JSON
	responseInfo := order.ResponseData
	if responseInfo != "" {
		if err := json.Unmarshal([]byte(responseInfo), &responseData); err != nil {
			logx.Errorf("解析响应数据失败: %v", err)
		}
	}

	// 从解析的数据中提取更多信息
	storeAddress := ""
	storePhone := ""
	departmentName := ""

	// 查询关联的患者、医生、门店信息
	patientName := ""
	doctorName := ""
	storeName := ""

	// 查询患者姓名
	if order.WxPatientID > 0 {
		patientRepo := patient.NewWxPatientRepository(nil)
		if patientInfo, err := patientRepo.FindByID(uint(order.WxPatientID)); err == nil && patientInfo != nil {
			patientName = patientInfo.Name
		}
	}

	// 查询医生姓名
	if order.WxDoctorID > 0 {
		doctorRepo := doctor.NewWxDoctorRepository(nil)
		if doctorInfo, err := doctorRepo.FindByID(uint(order.WxDoctorID)); err == nil && doctorInfo != nil {
			doctorName = doctorInfo.Name
		}
	}

	// 查询门店信息
	if order.StoreID > 0 {
		storeRepo := storeModel.NewStoreRepository(nil)
		if storeInfo, err := storeRepo.FindByID(uint(order.StoreID)); err == nil && storeInfo != nil {
			storeName = storeInfo.Name
			storeAddress = storeInfo.Address
			storePhone = storeInfo.Phone
		}
	}

	// 从响应数据中提取科室名称
	if responseData != nil {
		if deptName, ok := responseData["jgksmc"].(string); ok && deptName != "" {
			departmentName = deptName
		}
	}

	// 初始化支付相关信息
	var originalFee float64 = 0
	var couponAmount float64 = 0
	var pointsAmount float64 = 0
	var payAmount float64 = 0
	var payTime string = ""

	// 从响应数据中提取费用信息
	if responseData != nil {
		// 挂号费
		if ghf, ok := responseData["ghf"].(float64); ok {
			originalFee += ghf
		}

		// 诊疗费
		if zlf, ok := responseData["zlf"].(float64); ok {
			originalFee += zlf
		}

		// 设置支付金额
		payAmount = originalFee - couponAmount - pointsAmount
	}

	// 构建详细响应
	detail := &regModel.AppointmentOrderDetailResponse{
		ID:                order.ID,
		OrderNo:           order.OrderNo,
		StoreAddress:      storeAddress,
		StorePhone:        storePhone,
		AppointDate:       order.AppointDate,
		AppointPeriod:     int(order.AppointPeriod),
		AppointPeriodText: registration.GetAppointPeriodText(order.AppointPeriod),
		Department:        departmentName,
		OrderStatus:       int(order.OrderStatus),
		StatusText:        regModel.GetOrderStatusText(int(order.OrderStatus)),
		CanCancel:         canCancel,
		OriginalFee:       originalFee,
		CouponAmount:      couponAmount,
		PointsAmount:      pointsAmount,
		PayAmount:         payAmount,
		CreatedAt:         order.CreatedAt.Format("2006-01-02 15:04:05"),
		PayTime:           payTime,
		QRCodeURL:         order.QRCodeURL,
		CancelReason:      order.ZfYY,
		CancelTime:        order.ZfSJ,
		// 新增名字字段
		PatientName: patientName,
		DoctorName:  doctorName,
		StoreName:   storeName,
	}

	response.Success(w, detail)
}
