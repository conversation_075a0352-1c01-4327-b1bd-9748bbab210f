package routes

import (
	"net/http"

	evaluationHandler "yekaitai/internal/modules/evaluation/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterEvaluationRoutes 注册服务评价管理路由
func RegisterEvaluationRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 创建服务评价处理器实例
	evaluationHandlerInst := evaluationHandler.NewServiceEvaluationHandler()

	// 服务评价管理路由
	server.AddRoutes(
		[]rest.Route{
			// 获取服务评价列表
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/evaluations",
				Handler: evaluationHandlerInst.ListEvaluations,
			},
			// 获取服务评价详情
			{
				Method:  http.MethodGet,
				Path:    "/api/admin/evaluations/:id",
				Handler: evaluationHandlerInst.GetEvaluationDetail,
			},
			// 删除服务评价
			{
				Method:  http.MethodDelete,
				Path:    "/api/admin/evaluations/:id",
				Handler: evaluationHandlerInst.DeleteEvaluation,
			},
		},
	)
}
