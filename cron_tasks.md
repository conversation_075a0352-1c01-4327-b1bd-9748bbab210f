# 定时任务说明文档

## 配置说明

定时任务的配置位于 `etc/config.yaml` 文件中，示例配置如下：

```yaml
cron:
  enable: false  # 是否启用定时任务
  amap:
    key: "your_amap_key_here"  # 高德地图API密钥
  storeGeoSync:
    enable: false  # 是否启用门店地理编码同步
    cron: "0 0 3 * * ?"  # 每天凌晨3点执行
    batchSize: 100  # 每批处理数量

# 万里牛ERP配置
wanliniu:
  baseUrl: "http://open.wanliniu.com"
  appKey: "your_app_key"
  appSecret: "your_app_secret"
```

## 可用任务列表

### 1. 门店地理编码同步 (store_geo)

功能：同步门店表中的地址信息到经纬度坐标。

- 定时执行：每天凌晨3点自动执行（可配置）
- 手动执行：
  ```bash
  go run cmd/tools/main.go -f etc/config.yaml -task store_geo
  ```

### 2. 万里牛商品同步 (wanliniu_goods_sync)

功能：从万里牛ERP系统同步商品数据到本地数据库。

- **定时执行**：每小时自动执行一次
  ```bash
  # 启动定时任务服务
  go run cmd/cron/main.go etc/config.yaml
  ```

- **手动执行**：
  ```bash
  go run cmd/tools/main.go -f etc/config.yaml -task wanliniu_goods_sync
  ```

- **功能特性**：
  - 自动增量同步：每小时检查并同步修改的商品
  - 自动分类创建：如果商品包含分类信息，会自动添加到分类表
  - 智能更新：保持本地设置（推荐状态、上架状态、销售价等）
  - 同步进度跟踪：记录每次同步的结果和状态

### 3. 医生数据同步 (doctor_sync)

功能：同步医生相关数据（待实现）

- 手动执行：
  ```bash
  go run cmd/tools/main.go -f etc/config.yaml -task doctor_sync
  ```

### 4. 患者数据同步 (patient_sync)

功能：同步患者相关数据（待实现）

- 手动执行：
  ```bash
  go run cmd/tools/main.go -f etc/config.yaml -task patient_sync
  ```

## 定时任务调度

### 万里牛商品同步调度规则

- **执行频率**：每小时的整点执行（如：13:00:00, 14:00:00）
- **首次同步**：如果没有同步记录，会进行全量同步
- **增量同步**：距离上次成功同步超过1小时，进行增量同步
- **跳过条件**：距离上次同步不足1小时时，跳过本次同步

### Cron表达式说明

```
"0 0 * * * *"  # 每小时的0分0秒执行
秒 分 时 日 月 周
```

## 注意事项

1. **万里牛API限制**
   - 请合理控制请求频率
   - 建议在业务低峰期运行大量同步

2. **数据一致性**
   - 同步过程中会保持本地设置不被覆盖
   - 分类会自动创建，避免数据关联错误

3. **执行建议**
   - 首次运行建议先手动执行测试
   - 确认无误后启动定时任务服务
   - 定时任务会有详细日志输出

4. **错误处理**
   - 同步失败的记录会记录在日志中
   - 可以根据日志进行问题排查和重试

## 启动定时任务服务

```bash
# 启动定时任务服务（前台运行）
go run cmd/cron/main.go etc/config.yaml

# 或者编译后运行
go build -o yekaitai_cron cmd/cron/main.go
./yekaitai_cron etc/config.yaml
```

## 开发说明

如需添加新的定时任务，请参考以下步骤：

1. 在 `cmd/cron/tasks/` 目录下创建新的同步服务
2. 在 `cmd/cron/main.go` 中添加新的定时任务
3. 在 `cmd/tools/main.go` 中添加手动执行支持
4. 更新本文档，添加新任务的使用说明 