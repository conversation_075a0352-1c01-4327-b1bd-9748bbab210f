package upload

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"math/big"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// 默认文件大小常量
const (
	// 默认最大文件大小 - 这些将由配置覆盖
	DefaultMaxImageSize    = 10 << 20   // 10MB
	DefaultMaxDocumentSize = 50 << 20   // 50MB
	DefaultMaxVideoSize    = 1536 << 20 // 1.5GB
	DefaultMaxGeneralSize  = 100 << 20  // 100MB
	MaxFileSize            = 1536 << 20 // 整体最大上传大小 1.5GB
)

// 允许的图片类型
var allowedImageTypes = map[string]bool{
	"image/jpeg":    true,
	"image/png":     true,
	"image/gif":     true,
	"image/webp":    true,
	"image/bmp":     true,
	"image/tiff":    true,
	"image/svg+xml": true,
}

// 允许的文档类型
var allowedDocTypes = map[string]bool{
	"application/pdf":    true,
	"application/msword": true,
	"application/vnd.openxmlformats-officedocument.wordprocessingml.document": true,
	"application/vnd.ms-excel": true,
	"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":         true,
	"application/vnd.ms-powerpoint":                                             true,
	"application/vnd.openxmlformats-officedocument.presentationml.presentation": true,
	"text/plain":                   true,
	"application/rtf":              true,
	"application/x-rar-compressed": true,
	"application/zip":              true,
	"application/x-7z-compressed":  true,
}

// 允许的视频类型
var allowedVideoTypes = map[string]bool{
	"video/mp4":             true,
	"video/mpeg":            true,
	"video/quicktime":       true,
	"video/x-msvideo":       true, // AVI
	"video/x-ms-wmv":        true, // WMV
	"video/x-flv":           true, // FLV
	"video/webm":            true, // WebM
	"video/3gpp":            true, // 3GP
	"video/3gpp2":           true, // 3GP2
	"application/x-mpegURL": true, // M3U8
	"video/MP2T":            true, // TS
	"video/ogg":             true, // OGG
}

// 视频文件扩展名
var videoExtensions = map[string]bool{
	".mp4":  true,
	".avi":  true,
	".mov":  true,
	".wmv":  true,
	".flv":  true,
	".webm": true,
	".mkv":  true,
	".3gp":  true,
	".mpeg": true,
	".mpg":  true,
	".m4v":  true,
	".ts":   true,
	".ogg":  true,
}

// CheckFileSize 检查文件大小
func CheckFileSize(file *multipart.FileHeader, maxSize int64) error {
	if file.Size > maxSize {
		logx.Errorf("文件过大: %s, 大小: %d, 最大限制: %d", file.Filename, file.Size, maxSize)
		return fmt.Errorf("文件大小超过限制: %d > %d", file.Size, maxSize)
	}
	return nil
}

// CheckImageType 检查是否为允许的图片类型
func CheckImageType(file *multipart.FileHeader) error {
	// 先检查扩展名
	ext := strings.ToLower(filepath.Ext(file.Filename))
	switch ext {
	case ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".tiff", ".tif", ".svg":
		// 扩展名合法, 继续检查内容类型
	default:
		logx.Errorf("不支持的图片扩展名: %s, 文件: %s", ext, file.Filename)
		return fmt.Errorf("不支持的图片扩展名: %s", ext)
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		logx.Errorf("打开文件失败: %v, 文件: %s", err, file.Filename)
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer src.Close()

	// 读取文件头信息
	buffer := make([]byte, 512)
	_, err = src.Read(buffer)
	if err != nil {
		logx.Errorf("读取文件信息失败: %v, 文件: %s", err, file.Filename)
		return fmt.Errorf("读取文件信息失败: %w", err)
	}

	// 检测文件类型
	contentType := http.DetectContentType(buffer)
	if !allowedImageTypes[contentType] {
		logx.Errorf("不支持的图片类型: %s, 文件: %s", contentType, file.Filename)
		return fmt.Errorf("不支持的图片类型: %s", contentType)
	}

	return nil
}

// CheckDocumentType 检查是否为允许的文档类型
func CheckDocumentType(file *multipart.FileHeader) error {
	// 根据文件扩展名检查
	ext := strings.ToLower(filepath.Ext(file.Filename))
	switch ext {
	case ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf", ".rar", ".zip", ".7z":
		return nil
	default:
		logx.Errorf("不支持的文档类型: %s, 文件: %s", ext, file.Filename)
		return fmt.Errorf("不支持的文档类型: %s", ext)
	}
}

// CheckVideoType 检查是否为允许的视频类型
func CheckVideoType(file *multipart.FileHeader) error {
	// 根据文件扩展名检查
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if !videoExtensions[ext] {
		logx.Errorf("不支持的视频扩展名: %s, 文件: %s", ext, file.Filename)
		return fmt.Errorf("不支持的视频扩展名: %s", ext)
	}

	// 对于小视频文件 (<10MB), 检查内容类型
	if file.Size < 10<<20 {
		// 打开文件
		src, err := file.Open()
		if err != nil {
			logx.Errorf("打开文件失败: %v, 文件: %s", err, file.Filename)
			return fmt.Errorf("打开文件失败: %w", err)
		}
		defer src.Close()

		// 读取文件头信息
		buffer := make([]byte, 512)
		_, err = src.Read(buffer)
		if err != nil {
			logx.Errorf("读取文件信息失败: %v, 文件: %s", err, file.Filename)
			return fmt.Errorf("读取文件信息失败: %w", err)
		}

		// 检测文件类型
		contentType := http.DetectContentType(buffer)
		// 检查是否为视频类型 或 是否包含 "video/" 前缀
		if !allowedVideoTypes[contentType] && !strings.HasPrefix(contentType, "video/") {
			logx.Errorf("文件内容不是视频类型: %s, 文件: %s", contentType, file.Filename)
			return fmt.Errorf("文件内容不是视频类型: %s", contentType)
		}
	}

	return nil
}

// GenerateFileMD5 生成文件的MD5值
func GenerateFileMD5(file *multipart.FileHeader) (string, error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		logx.Errorf("打开文件失败: %v, 文件: %s", err, file.Filename)
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer src.Close()

	// 计算MD5
	hash := md5.New()
	if _, err := io.Copy(hash, src); err != nil {
		logx.Errorf("计算MD5失败: %v, 文件: %s", err, file.Filename)
		return "", fmt.Errorf("计算MD5失败: %w", err)
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// RandomString 生成指定长度的随机字符串
func RandomString(length int) string {
	const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := 0; i < length; i++ {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		result[i] = letters[num.Int64()]
	}
	return string(result)
}

// GenerateUniqueFilename 生成唯一的文件名
func GenerateUniqueFilename(originalFilename string) string {
	ext := filepath.Ext(originalFilename)
	filename := fmt.Sprintf("%d-%s%s", time.Now().UnixNano(), RandomString(8), ext)
	return filename
}

// GenerateBase64Key 生成Base64编码的唯一key
func GenerateBase64Key(prefix string) string {
	now := time.Now().UnixNano()
	random := RandomString(8)
	data := fmt.Sprintf("%s-%d-%s", prefix, now, random)

	hash := md5.Sum([]byte(data))
	return base64.URLEncoding.EncodeToString(hash[:])
}
