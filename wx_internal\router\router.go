package router

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"yekaitai/internal/types"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/consultation"
	"yekaitai/wx_internal/router/routes"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// RegisterHandlers 注册API路由
func RegisterHandlers(server *rest.Server, serverCtx *svc.WxServiceContext) {
	// 设置统一的错误处理器
	types.SetupErrorHandler()

	// 全局中间件 - 确保覆盖所有请求
	server.Use(middleware.WxCorsMiddleware())
	server.Use(middleware.WxLogMiddleware())
	logx.Info("注册全局中间件完成")

	// 创建带基础路径前缀的路由组
	apiGroup := &routeGroup{
		server: server,
		prefix: "/yekaitai-mini-api",
	}

	// 注册各模块路由
	routes.RegisterCommonRoutes(apiGroup, serverCtx)
	routes.RegisterAuthRoutes(apiGroup, serverCtx)
	routes.RegisterJushuitanRoutes(apiGroup, serverCtx)
	routes.RegisterAreaRoutes(apiGroup, serverCtx)

	logx.Info("已注册无需身份认证的路由")

	// 以下路由需要使用微信认证
	registerWithWxAuth(apiGroup, serverCtx, func(customServer routes.RestServer, ctx *svc.WxServiceContext) {
		logx.Info("开始注册需要微信认证的路由...")

		// 注册医疗咨询路由
		routes.RegisterConsultationRoutes(customServer, ctx)
		logx.Info("已注册预问诊路由：/api/wx/consultation/prediagnosis, /api/wx/consultation/ws/prediagnosis")

		// 注册健康咨询路由（包含智能客服）
		consultation.RegisterHealthConsultRoutes(customServer, ctx)
		logx.Info("已注册健康咨询路由：/api/wx/consultation/health/*, /api/wx/consultation/ws/health")
		logx.Info("智能客服已集成到健康咨询模块，通过ModelID: 405区分")

		// 注册上传相关路由
		routes.RegisterUploadRoutes(customServer, ctx)
		logx.Info("已注册上传路由：/api/wx/upload/file, /api/wx/upload/image, /api/wx/upload/document, /api/wx/upload/video")

		// 注册枚举相关路由
		routes.RegisterEnumRoutes(customServer, ctx)
		logx.Info("已注册枚举路由：/api/enum/ethnicities, /api/enum/relationships")

		// 注册其他路由
		routes.RegisterAbcYunRoutes(customServer, ctx)
		routes.RegisterUserRoutes(customServer, ctx)

		// 注册挂号相关路由
		routes.RegisterRegistrationRoutes(customServer, ctx)
		logx.Info("已注册挂号相关路由")

		// 注册患者相关路由
		routes.RegisterPatientRoutes(customServer, ctx)
		logx.Info("已注册患者相关路由")

		// 注册医生相关路由
		routes.RegisterDoctorRoutes(customServer, ctx)
		logx.Info("已注册医生相关路由")

		// 注册二维码相关路由
		routes.RegisterQrcodeHandlers(customServer, ctx)
		logx.Info("已注册二维码相关路由")

		// 注册门店相关路由
		routes.RegisterStoreHandlers(customServer, ctx)
		logx.Info("已注册门店相关路由")

		// 注册科室相关路由
		routes.RegisterDepartmentHandlers(customServer, ctx)
		logx.Info("已注册科室相关路由")

		// 注册商城相关路由
		routes.RegisterGoodsRoutes(customServer, ctx)
		logx.Info("已注册小程序商城相关路由")

		// 注册优惠券相关路由
		routes.RegisterCouponRoutes(customServer, ctx)
		logx.Info("已注册小程序优惠券相关路由")

		// 注册服务评价相关路由
		routes.RegisterEvaluationRoutes(customServer, ctx)
		logx.Info("已注册小程序服务评价相关路由")

		// 注册邮件相关路由
		routes.RegisterEmailRoutes(customServer, ctx)
		logx.Info("已注册小程序邮件相关路由")

		// 注册签到相关路由
		routes.RegisterCheckinRoutes(customServer, ctx)
		logx.Info("已注册小程序签到相关路由")

		// 注册邀请相关路由
		routes.RegisterInvitationRoutes(customServer, ctx)
		logx.Info("已注册小程序邀请相关路由")

		// 注册活动相关路由
		routes.RegisterActivityRoutes(customServer, ctx)
		logx.Info("已注册小程序活动相关路由")

		// 注册服务相关路由
		routes.RegisterServiceRoutes(customServer, ctx)
		logx.Info("已注册小程序服务相关路由")

		logx.Info("微信认证路由注册完成")
	})
}

// registerWithWxAuth 注册支持微信小程序认证的路由
func registerWithWxAuth(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.WxServiceContext, registerFunc func(routes.RestServer, *svc.WxServiceContext)) {
	// 创建一个自定义服务器，拦截所有路由注册
	wxAuthServer := &wxAuthServer{
		server:    server,
		serverCtx: serverCtx,
	}

	// 调用注册函数，但路由会被拦截
	registerFunc(wxAuthServer, serverCtx)
	logx.Info("已完成微信认证路由包装")
}

// wxAuthServer 微信认证服务器，用于拦截路由注册
type wxAuthServer struct {
	server interface {
		AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
		AddRoute(r rest.Route, opts ...rest.RouteOption)
		Use(middleware rest.Middleware)
	}
	serverCtx *svc.WxServiceContext
}

// 实现AddRoutes方法，为每个路由添加微信认证中间件
func (s *wxAuthServer) AddRoutes(rs []rest.Route, opts ...rest.RouteOption) {
	// 创建带微信认证的路由
	wrappedRoutes := make([]rest.Route, len(rs))
	for i, r := range rs {
		route := r
		logx.Infof("包装微信认证路由: %s %s", route.Method, route.Path)
		originalHandler := route.Handler

		// 包装处理函数，添加微信认证
		route.Handler = func(w http.ResponseWriter, r *http.Request) {
			// 检查是否是WebSocket请求
			isWebSocket := strings.Contains(strings.ToLower(r.Header.Get("Upgrade")), "websocket") &&
				strings.Contains(strings.ToLower(r.Header.Get("Connection")), "upgrade")

			logx.Infof("处理请求: %s %s, 是否WebSocket: %v", r.Method, r.URL.Path, isWebSocket)

			var authHeader string

			// 如果是WebSocket请求，从URL参数获取token
			if isWebSocket {
				logx.Info("检测到WebSocket握手请求")
				// 从URL参数中获取token
				tokenParam := r.URL.Query().Get("token")
				if tokenParam != "" {
					// 移除可能存在的转义字符
					tokenParam = strings.ReplaceAll(tokenParam, "\\u0026", "&")
					authHeader = tokenParam
					logx.Infof("从URL参数获取token: %s", authHeader)
				}
			} else {
				// 否则从请求头获取Token
				authHeader = r.Header.Get("Authorization")
			}

			if authHeader == "" {
				logx.Errorf("未提供认证信息: %s %s", r.Method, r.URL.Path)
				if isWebSocket {
					// WebSocket请求使用401状态码和纯文本错误
					http.Error(w, "Unauthorized", http.StatusUnauthorized)
				} else {
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "未授权，请提供认证信息"))
				}
				return
			}

			// 记录认证请求
			logx.Infof("处理认证: %s %s", r.Method, r.URL.Path)

			// 加上Bearer前缀（如果从URL获取的token没有）
			if !strings.HasPrefix(strings.ToLower(authHeader), "bearer ") {
				authHeader = "Bearer " + authHeader
			}

			// 解析Token
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) != 2 {
				logx.Errorf("认证格式错误: %s", authHeader)
				if isWebSocket {
					http.Error(w, "Authentication Format Error", http.StatusUnauthorized)
				} else {
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "认证格式错误"))
				}
				return
			}

			tokenType := strings.ToLower(parts[0])
			tokenString := parts[1]

			// 验证认证类型
			if tokenType != "bearer" {
				logx.Errorf("不支持的认证类型: %s", tokenType)
				if isWebSocket {
					http.Error(w, "Unsupported Authentication Type", http.StatusUnauthorized)
				} else {
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(types.CodeUnauthorized, "不支持的认证类型，请使用Bearer认证"))
				}
				return
			}

			// 解析微信Token
			wxClaims, wxErr := middleware.ParseWxToken(s.serverCtx, tokenString)
			if wxErr != nil {
				logx.Errorf("Bearer Token解析失败: %v", wxErr)
				if isWebSocket {
					http.Error(w, "Authentication Failed", http.StatusUnauthorized)
				} else {
					httpx.WriteJson(w, http.StatusOK, types.NewErrorResponse(
						types.CodeUnauthorized,
						fmt.Sprintf("认证失败: %v", wxErr),
					))
				}
				return
			}

			// 设置用户信息到上下文
			ctx := r.Context()
			openID := wxClaims.OpenID

			// 设置用户信息到上下文
			ctx = context.WithValue(ctx, types.UserIDKey, openID)
			ctx = context.WithValue(ctx, middleware.OpenIDKey, openID)
			if len(wxClaims.Roles) > 0 {
				ctx = context.WithValue(ctx, types.RoleKey, strings.Join(wxClaims.Roles, ","))
				ctx = context.WithValue(ctx, "wx_roles", strings.Join(wxClaims.Roles, ","))
			}

			if isWebSocket {
				logx.Infof("WebSocket认证成功: OpenID=%s, 准备升级连接", openID)
			} else {
				logx.Infof("HTTP认证成功: OpenID=%s", openID)
			}

			// 使用包含认证信息的新请求上下文调用原始处理函数
			originalHandler(w, r.WithContext(ctx))
		}

		wrappedRoutes[i] = route
	}

	// 将包装后的路由添加到服务器
	s.server.AddRoutes(wrappedRoutes, opts...)
}

// 实现AddRoute方法，添加单个路由并加上认证中间件
func (s *wxAuthServer) AddRoute(r rest.Route, opts ...rest.RouteOption) {
	// 将单个路由转换为切片，复用AddRoutes的逻辑
	s.AddRoutes([]rest.Route{r}, opts...)
}

// 实现必要的其他方法
func (s *wxAuthServer) Use(middleware rest.Middleware) {
	s.server.Use(middleware)
}

// routeGroup 路由组，支持路径前缀
type routeGroup struct {
	server *rest.Server
	prefix string
}

// AddRoutes 实现路由注册，添加路径前缀
func (rg *routeGroup) AddRoutes(rs []rest.Route, opts ...rest.RouteOption) {
	// 为每个路由添加前缀
	prefixedRoutes := make([]rest.Route, len(rs))
	for i, r := range rs {
		route := r
		route.Path = rg.prefix + route.Path
		prefixedRoutes[i] = route
	}
	rg.server.AddRoutes(prefixedRoutes, opts...)
}

// AddRoute 添加单个路由，添加路径前缀
func (rg *routeGroup) AddRoute(r rest.Route, opts ...rest.RouteOption) {
	r.Path = rg.prefix + r.Path
	rg.server.AddRoute(r, opts...)
}

// Use 添加中间件
func (rg *routeGroup) Use(middleware rest.Middleware) {
	rg.server.Use(middleware)
}
