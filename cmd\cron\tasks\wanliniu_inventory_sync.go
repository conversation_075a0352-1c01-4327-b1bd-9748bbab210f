package tasks

import (
	"context"
	"fmt"
	"time"

	"yekaitai/internal/config"
	"yekaitai/internal/modules/goods/model"
	"yekaitai/pkg/adapters/wanliniu"
	"yekaitai/pkg/common/model/patient"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// WanLiNiuInventorySyncService 万里牛库存同步服务
type WanLiNiuInventorySyncService struct {
	wanliniuService *wanliniu.Service
	db              *gorm.DB
	config          *config.WanLiNiuConfig
}

// NewWanLiNiuInventorySyncService 创建万里牛库存同步服务
func NewWanLiNiuInventorySyncService(cfg ...*config.WanLiNiuConfig) *WanLiNiuInventorySyncService {
	var conf *config.WanLiNiuConfig
	if len(cfg) > 0 {
		conf = cfg[0]
	}
	return &WanLiNiuInventorySyncService{
		wanliniuService: wanliniu.GetService(),
		db:              mysql.Master(),
		config:          conf,
	}
}

// SyncInventory 定时同步库存
func (s *WanLiNiuInventorySyncService) SyncInventory() error {
	logx.Info("[WanLiNiu] 开始定时同步库存...")

	// 每次都查询3天内的数据，不使用增量同步
	now := time.Now()
	startTime := now.AddDate(0, 0, -3) // 往前推3天
	logx.Infof("[WanLiNiu] 定时库存同步，查询最近3天数据: %s 到 %s",
		startTime.Format("2006-01-02 15:04:05"),
		now.Format("2006-01-02 15:04:05"))

	return s.syncInventoryByTimeRange(&startTime, &now)
}

// SyncInventoryManual 手动同步库存
func (s *WanLiNiuInventorySyncService) SyncInventoryManual() error {
	logx.Info("[WanLiNiu] 开始手动同步库存...")

	// 每次都查询3天内的数据，不使用增量同步
	now := time.Now()
	startTime := now.AddDate(0, 0, -3) // 往前推3天
	logx.Infof("[WanLiNiu] 库存同步，查询最近3天数据: %s 到 %s",
		startTime.Format("2006-01-02 15:04:05"),
		now.Format("2006-01-02 15:04:05"))

	return s.syncInventoryByTimeRange(&startTime, &now)
}

// syncInventoryIncremental 增量同步库存
func (s *WanLiNiuInventorySyncService) syncInventoryIncremental(lastSyncTime *time.Time) error {
	currentTime := time.Now()

	// 如果是首次同步，自动设置为当前时间往前推3天
	if lastSyncTime == nil {
		startTime := currentTime.AddDate(0, 0, -3) // 往前推3天
		logx.Infof("[WanLiNiu] 首次增量同步，自动设置起始时间为当前时间往前推3天: %s", startTime.Format("2006-01-02 15:04:05"))
		lastSyncTime = &startTime
	}

	return s.syncInventoryByTimeRange(lastSyncTime, &currentTime)
}

// syncInventoryByTimeRange 根据时间范围同步库存
func (s *WanLiNiuInventorySyncService) syncInventoryByTimeRange(startTime, endTime *time.Time) error {
	if endTime == nil {
		now := time.Now()
		endTime = &now
	}

	// 检查时间跨度不能超过7天
	maxDuration := 7 * 24 * time.Hour
	currentStart := *startTime

	totalRecords := 0
	syncStartTime := time.Now()

	for currentStart.Before(*endTime) {
		// 计算当前批次的结束时间
		currentEnd := currentStart.Add(maxDuration)
		if currentEnd.After(*endTime) {
			currentEnd = *endTime
		}

		logx.Infof("[WanLiNiu] 同步库存时间段: %s 到 %s",
			currentStart.Format("2006-01-02 15:04:05"),
			currentEnd.Format("2006-01-02 15:04:05"))

		// 同步当前时间段的库存
		records, err := s.syncInventoryBatch(&currentStart, &currentEnd)
		if err != nil {
			logx.Errorf("[WanLiNiu] 同步库存失败: %v", err)
			s.saveSyncProgress(syncStartTime, 0, totalRecords, fmt.Sprintf("同步库存失败: %v", err))
			return err
		}

		totalRecords += records
		logx.Infof("[WanLiNiu] 当前时间段同步完成，处理 %d 条库存记录", records)

		// 移动到下一个时间段
		currentStart = currentEnd

		// 避免请求过于频繁
		time.Sleep(200 * time.Millisecond)
	}

	s.saveSyncProgress(syncStartTime, 1, totalRecords, fmt.Sprintf("库存同步成功，共处理 %d 条记录", totalRecords))
	logx.Infof("[WanLiNiu] 库存同步完成，共处理 %d 条记录", totalRecords)
	return nil
}

// syncInventoryBatch 批量同步库存
func (s *WanLiNiuInventorySyncService) syncInventoryBatch(startTime, endTime *time.Time) (int, error) {
	ctx := context.Background()
	totalRecords := 0
	page := 1
	limit := 200 // 每页200条数据，API支持的最大值

	for {
		// 使用V2接口查询万里牛库存
		response, err := s.wanliniuService.QueryInventoryV2ByModifyTime(ctx, page, limit,
			startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), "")
		if err != nil {
			return totalRecords, fmt.Errorf("查询万里牛库存V2失败: %w", err)
		}

		// 检查是否有数据
		if len(response.Data) == 0 {
			logx.Infof("[WanLiNiu] 第 %d 页无数据，批次同步结束", page)
			break
		}

		// 处理库存数据
		processedCount := 0
		for _, inventory := range response.Data {
			if err := s.processInventoryItemV2(ctx, inventory); err != nil {
				logx.Errorf("[WanLiNiu] 处理库存记录失败: %v", err)
				// 不中断流程，继续处理下一条记录
			} else {
				processedCount++
			}
		}

		totalRecords += processedCount
		logx.Infof("[WanLiNiu] 已处理第 %d 页库存数据，本页处理 %d/%d 条", page, processedCount, len(response.Data))

		// 如果返回的数据少于limit，说明已经是最后一页
		if len(response.Data) < limit {
			break
		}

		page++
		time.Sleep(100 * time.Millisecond)
	}

	return totalRecords, nil
}

// processInventoryItemV2 处理单个库存记录V2
func (s *WanLiNiuInventorySyncService) processInventoryItemV2(ctx context.Context, inventory wanliniu.InventoryItemV2) error {
	// 解析货号、条码和规格编码
	articleNumber := inventory.ArticleNumber
	barCode := inventory.BarCode
	skuCode := inventory.SkuCode

	if articleNumber == "" && barCode == "" && skuCode == "" {
		return fmt.Errorf("货号、条码和规格编码都为空")
	}

	// 计算可用库存 = 实际库存 - 锁定库存
	availableStock := inventory.Quantity - inventory.LockSize
	originalStock := availableStock
	if availableStock < 0 {
		logx.Errorf("[WanLiNiu] 检测到负数库存: 规格编码=%s, 实际库存=%.2f, 锁定库存=%.2f, 计算结果=%.2f, 自动调整为0",
			skuCode, inventory.Quantity, inventory.LockSize, originalStock)
		availableStock = 0
	}

	logx.Infof("[WanLiNiu] 处理库存记录: 货号=%s, 条码=%s, 规格编码=%s, 实际库存=%.2f, 锁定库存=%.2f, 可用库存=%.2f",
		articleNumber, barCode, skuCode, inventory.Quantity, inventory.LockSize, availableStock)

	// 更新本地库存
	return s.updateLocalInventory(ctx, articleNumber, barCode, skuCode, int(availableStock))
}

// updateLocalInventory 更新本地库存
func (s *WanLiNiuInventorySyncService) updateLocalInventory(ctx context.Context, articleNumber, barCode, skuCode string, stock int) error {
	// 根据万里牛返回的sku_code直接对应goods_spec表的spec_code字段更新库存
	if skuCode != "" {
		// 根据spec_code查找规格记录
		var spec model.GoodsSpec
		err := s.db.Where("spec_code = ?", skuCode).First(&spec).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				logx.Infof("[WanLiNiu] 规格不存在，跳过: spec_code=%s", skuCode)
				return nil
			}
			return fmt.Errorf("查询商品规格失败: %w", err)
		}

		// 更新规格库存
		err = s.db.Model(&spec).Update("stock", stock).Error
		if err != nil {
			return fmt.Errorf("更新规格库存失败: %w", err)
		}

		logx.Infof("[WanLiNiu] 更新规格库存成功: spec_code=%s, 规格ID=%d, 新库存=%d", skuCode, spec.ID, stock)
		return nil
	}

	// 如果没有sku_code，尝试通过article_number或bar_code查找规格
	if articleNumber != "" {
		// 通过货号查找规格（假设货号在某个字段中存储）
		var spec model.GoodsSpec
		err := s.db.Where("spec_code = ? OR bar_code = ?", articleNumber, articleNumber).First(&spec).Error
		if err == nil {
			// 更新规格库存
			err = s.db.Model(&spec).Update("stock", stock).Error
			if err != nil {
				return fmt.Errorf("更新规格库存失败: %w", err)
			}
			logx.Infof("[WanLiNiu] 通过货号更新规格库存成功: article_number=%s, 规格ID=%d, 新库存=%d", articleNumber, spec.ID, stock)
			return nil
		}
	}

	if barCode != "" {
		// 通过条码查找规格
		var spec model.GoodsSpec
		err := s.db.Where("bar_code = ?", barCode).First(&spec).Error
		if err == nil {
			// 更新规格库存
			err = s.db.Model(&spec).Update("stock", stock).Error
			if err != nil {
				return fmt.Errorf("更新规格库存失败: %w", err)
			}
			logx.Infof("[WanLiNiu] 通过条码更新规格库存成功: bar_code=%s, 规格ID=%d, 新库存=%d", barCode, spec.ID, stock)
			return nil
		}
	}

	// 如果都无法找到对应的规格，记录日志但不报错
	logx.Infof("[WanLiNiu] 无法找到对应的规格记录: 货号=%s, 条码=%s, 规格编码=%s", articleNumber, barCode, skuCode)
	return nil
}

// getLastSyncTime 获取最后成功同步的时间
func (s *WanLiNiuInventorySyncService) getLastSyncTime() *time.Time {
	var progress patient.SyncProgress
	err := s.db.Where("sync_type = ? AND status = ?", "wanliniu_inventory_sync", 1).
		Order("sync_date DESC").
		First(&progress).Error

	if err != nil {
		return nil // 没有找到记录，返回nil
	}

	// 解析同步时间
	if syncTime, parseErr := time.Parse("2006-01-02 15:04:05", progress.SyncDate); parseErr == nil {
		return &syncTime
	}

	// 如果解析失败，返回nil
	return nil
}

// saveSyncProgress 保存同步进度
func (s *WanLiNiuInventorySyncService) saveSyncProgress(syncTime time.Time, status int, records int, message string) {
	syncTimeStr := syncTime.Format("2006-01-02 15:04:05")

	progress := patient.SyncProgress{
		SyncType: "wanliniu_inventory_sync",
		SyncDate: syncTimeStr,
		Status:   status,
		Records:  records,
		Message:  message,
	}

	// 先查找是否已存在该时间的记录
	var existing patient.SyncProgress
	err := s.db.Where("sync_type = ? AND sync_date = ?", "wanliniu_inventory_sync", syncTimeStr).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// 不存在，创建新记录
		s.db.Create(&progress)
		logx.Infof("[WanLiNiu] 保存库存同步进度: 时间=%s, 状态=%d, 记录数=%d", syncTimeStr, status, records)
	} else {
		// 存在，更新记录
		s.db.Model(&existing).Updates(map[string]interface{}{
			"status":  status,
			"records": records,
			"message": message,
		})
		logx.Infof("[WanLiNiu] 更新库存同步进度: 时间=%s, 状态=%d, 记录数=%d", syncTimeStr, status, records)
	}
}

// UpdateLocalInventoryBySkuCode 根据sku_code更新本地库存（用于手动同步）
func (s *WanLiNiuInventorySyncService) UpdateLocalInventoryBySkuCode(skuCode string, stock int) error {
	if skuCode == "" {
		return fmt.Errorf("sku_code不能为空")
	}

	// 直接根据sku_code查找goods_spec记录
	var spec model.GoodsSpec
	err := s.db.Where("spec_code = ?", skuCode).First(&spec).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("未找到spec_code为%s的规格记录", skuCode)
		}
		return fmt.Errorf("查询规格记录失败: %v", err)
	}

	// 更新库存
	oldStock := spec.Stock
	if err := s.db.Model(&spec).Update("stock", stock).Error; err != nil {
		return fmt.Errorf("更新规格库存失败: %v", err)
	}

	fmt.Printf("规格ID: %d, 规格编码: %s, 库存: %d -> %d\n", spec.ID, spec.SpecCode, oldStock, stock)
	return nil
}
