package kafka

// MessageHandler 消息处理函数类型
type MessageHandler func([]byte) error

// Consumer Kafka消息消费者接口
type Consumer interface {
	// Subscribe 订阅主题
	Subscribe(topic string, handler MessageHandler) error
	// Start 启动消费者
	Start() error
	// Stop 停止消费者
	Stop() error
}

// MockConsumer 模拟的Kafka消费者，用于测试或开发环境
type MockConsumer struct {
	handlers map[string]MessageHandler
}

// NewMockConsumer 创建模拟Kafka消费者
func NewMockConsumer() Consumer {
	return &MockConsumer{
		handlers: make(map[string]MessageHandler),
	}
}

// Subscribe 订阅主题（模拟实现）
func (c *MockConsumer) Subscribe(topic string, handler MessageHandler) error {
	c.handlers[topic] = handler
	return nil
}

// Start 启动消费者（模拟实现）
func (c *MockConsumer) Start() error {
	// 在实际项目中，这里应该实现真正的Kafka消费者启动逻辑
	return nil
}

// Stop 停止消费者（模拟实现）
func (c *MockConsumer) Stop() error {
	// 在实际项目中，这里应该实现真正的Kafka消费者停止逻辑
	return nil
}

// ProcessMessage 手动处理消息，用于测试
func (c *MockConsumer) ProcessMessage(topic string, message []byte) error {
	handler, ok := c.handlers[topic]
	if !ok {
		return nil
	}
	return handler(message)
}
