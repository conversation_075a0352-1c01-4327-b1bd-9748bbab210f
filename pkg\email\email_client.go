package email

import (
	"crypto/tls"
	"fmt"
	"net/smtp"
	"strings"
)

// EmailConfig 邮件配置
type EmailConfig struct {
	Host     string `json:"host"`     // SMTP服务器地址
	Username string `json:"username"` // 邮箱用户名
	Password string `json:"password"` // 邮箱密码
	Port     int    `json:"port"`     // SMTP端口
}

// EmailClient 邮件客户端
type EmailClient struct {
	config *EmailConfig
}

// NewEmailClient 创建邮件客户端
func NewEmailClient(config *EmailConfig) *EmailClient {
	return &EmailClient{
		config: config,
	}
}

// EmailMessage 邮件消息
type EmailMessage struct {
	To      []string `json:"to"`      // 收件人列表
	Subject string   `json:"subject"` // 邮件主题
	Body    string   `json:"body"`    // 邮件内容
	IsHTML  bool     `json:"is_html"` // 是否为HTML格式
}

// SendEmail 发送邮件
func (c *EmailClient) SendEmail(message *EmailMessage) error {
	// 解析SMTP服务器地址
	host := c.config.Host
	if strings.HasPrefix(host, "ssl://") {
		host = strings.TrimPrefix(host, "ssl://")
	}

	// 构建邮件头
	headers := make(map[string]string)
	headers["From"] = c.config.Username
	headers["To"] = strings.Join(message.To, ",")
	headers["Subject"] = message.Subject
	headers["MIME-Version"] = "1.0"

	if message.IsHTML {
		headers["Content-Type"] = "text/html; charset=UTF-8"
	} else {
		headers["Content-Type"] = "text/plain; charset=UTF-8"
	}

	// 构建邮件内容
	var emailContent strings.Builder
	for key, value := range headers {
		emailContent.WriteString(fmt.Sprintf("%s: %s\r\n", key, value))
	}
	emailContent.WriteString("\r\n")
	emailContent.WriteString(message.Body)

	// 建立SMTP连接
	addr := fmt.Sprintf("%s:%d", host, c.config.Port)

	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: false,
		ServerName:         host,
	}

	// 建立TLS连接
	conn, err := tls.Dial("tcp", addr, tlsConfig)
	if err != nil {
		return fmt.Errorf("连接SMTP服务器失败: %v", err)
	}
	defer conn.Close()

	// 创建SMTP客户端
	client, err := smtp.NewClient(conn, host)
	if err != nil {
		return fmt.Errorf("创建SMTP客户端失败: %v", err)
	}
	defer client.Quit()

	// 身份验证
	auth := smtp.PlainAuth("", c.config.Username, c.config.Password, host)
	if err := client.Auth(auth); err != nil {
		return fmt.Errorf("SMTP身份验证失败: %v", err)
	}

	// 设置发件人
	if err := client.Mail(c.config.Username); err != nil {
		return fmt.Errorf("设置发件人失败: %v", err)
	}

	// 设置收件人
	for _, to := range message.To {
		if err := client.Rcpt(to); err != nil {
			return fmt.Errorf("设置收件人失败: %v", err)
		}
	}

	// 发送邮件内容
	writer, err := client.Data()
	if err != nil {
		return fmt.Errorf("获取邮件写入器失败: %v", err)
	}
	defer writer.Close()

	if _, err := writer.Write([]byte(emailContent.String())); err != nil {
		return fmt.Errorf("写入邮件内容失败: %v", err)
	}

	return nil
}

// ValidateConfig 验证邮件配置
func (c *EmailClient) ValidateConfig() error {
	if c.config.Host == "" {
		return fmt.Errorf("SMTP服务器地址不能为空")
	}
	if c.config.Username == "" {
		return fmt.Errorf("邮箱用户名不能为空")
	}
	if c.config.Password == "" {
		return fmt.Errorf("邮箱密码不能为空")
	}
	if c.config.Port <= 0 || c.config.Port > 65535 {
		return fmt.Errorf("SMTP端口号无效: %d", c.config.Port)
	}
	return nil
}

// TestConnection 测试邮件服务器连接
func (c *EmailClient) TestConnection() error {
	if err := c.ValidateConfig(); err != nil {
		return err
	}

	// 解析SMTP服务器地址
	host := c.config.Host
	if strings.HasPrefix(host, "ssl://") {
		host = strings.TrimPrefix(host, "ssl://")
	}

	// 建立连接测试
	addr := fmt.Sprintf("%s:%d", host, c.config.Port)

	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: false,
		ServerName:         host,
	}

	// 建立TLS连接
	conn, err := tls.Dial("tcp", addr, tlsConfig)
	if err != nil {
		return fmt.Errorf("连接SMTP服务器失败: %v", err)
	}
	defer conn.Close()

	// 创建SMTP客户端
	client, err := smtp.NewClient(conn, host)
	if err != nil {
		return fmt.Errorf("创建SMTP客户端失败: %v", err)
	}
	defer client.Quit()

	// 身份验证测试
	auth := smtp.PlainAuth("", c.config.Username, c.config.Password, host)
	if err := client.Auth(auth); err != nil {
		return fmt.Errorf("SMTP身份验证失败: %v", err)
	}

	return nil
}

// FormatEmailAddress 格式化邮箱地址
func FormatEmailAddress(name, email string) string {
	if name == "" {
		return email
	}
	return fmt.Sprintf("%s <%s>", name, email)
}

// IsValidEmail 简单的邮箱地址验证
func IsValidEmail(email string) bool {
	if email == "" {
		return false
	}

	// 简单的邮箱格式验证
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}

	if len(parts[0]) == 0 || len(parts[1]) == 0 {
		return false
	}

	if !strings.Contains(parts[1], ".") {
		return false
	}

	return true
}

// BuildHTMLTemplate 构建HTML邮件模板
func BuildHTMLTemplate(title, content string) string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>%s</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f4f4f4; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .footer { background-color: #f4f4f4; padding: 10px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>%s</h1>
        </div>
        <div class="content">
            %s
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
        </div>
    </div>
</body>
</html>`, title, title, content)
}
