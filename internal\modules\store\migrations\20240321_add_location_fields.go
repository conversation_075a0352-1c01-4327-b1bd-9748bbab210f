package migrations

import (
	"gorm.io/gorm"
)

// AddLocationFields 添加经纬度字段的迁移
func AddLocationFields(db *gorm.DB) error {
	// 添加经纬度字段和空间索引
	return db.Exec(`
		ALTER TABLE t_stores 
		ADD COLUMN latitude DECIMAL(10,7) DEFAULT NULL COMMENT '纬度' AFTER address,
		ADD COLUMN longitude DECIMAL(11,7) DEFAULT NULL COMMENT '经度' AFTER latitude,
		ADD INDEX idx_location (latitude, longitude),
		ADD SPATIAL INDEX idx_spatial_location (POINT(longitude, latitude));
	`).Error
}

// RollbackAddLocationFields 回滚经纬度字段的迁移
func RollbackAddLocationFields(db *gorm.DB) error {
	// 删除经纬度字段和索引
	return db.Exec(`
		ALTER TABLE t_stores 
		DROP INDEX idx_spatial_location,
		DROP INDEX idx_location,
		DROP COLUMN latitude,
		DROP COLUMN longitude;
	`).Error
}
