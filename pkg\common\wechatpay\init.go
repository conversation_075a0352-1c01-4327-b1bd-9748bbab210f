package wechatpay

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/zeromicro/go-zero/core/logx"
)

// Config 微信支付配置结构体（与 yekaitai-dev.yaml 对应）
type Config struct {
	AppID       string `json:"appID"`
	AppSecret   string `json:"appSecret"`
	MchID       string `json:"mchID"`
	ApiV3Key    string `json:"apiV3Key"`
	Certificate string `json:"certificate"` // 私钥内容
	SerialNo    string `json:"serialNo"`    // 证书序列号
}

// WechatPayManager 微信支付管理器
type WechatPayManager struct {
	config     *Config
	client     *core.Client
	privateKey *rsa.PrivateKey
	handler    *notify.Handler
}

var globalManager *WechatPayManager

// InitWechatPay 初始化微信支付（从配置文件）
func InitWechatPay(ctx context.Context, config *Config, notifyURL, refundNotifyURL string) error {
	// 解析商户私钥（从配置中的证书字符串）
	privateKey, err := parsePrivateKeyFromCertificate(config.Certificate)
	if err != nil {
		return fmt.Errorf("解析商户私钥失败: %w", err)
	}

	// 获取证书序列号
	serialNumber := config.SerialNo
	if serialNumber == "" {
		// 尝试从证书中提取序列号
		var err error
		serialNumber, err = getCertificateSerialNumber(config.Certificate)
		if err != nil {
			return fmt.Errorf("获取证书序列号失败: %w", err)
		}
	}
	logx.Infof("使用证书序列号: %s", serialNumber)

	// 初始化客户端选项
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(
			config.MchID,
			serialNumber,
			privateKey,
			config.ApiV3Key,
		),
	}

	// 创建客户端
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		return fmt.Errorf("创建微信支付客户端失败: %w", err)
	}

	// 初始化回调处理器
	// 注册下载器
	err = downloader.MgrInstance().RegisterDownloaderWithPrivateKey(
		ctx,
		privateKey,
		serialNumber,
		config.MchID,
		config.ApiV3Key,
	)
	if err != nil {
		logx.Errorf("注册证书下载器失败: %v", err)
		return fmt.Errorf("注册证书下载器失败: %w", err)
	}

	// 获取证书访问器
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(config.MchID)

	// 创建回调处理器
	handler := notify.NewNotifyHandler(
		config.ApiV3Key,
		verifiers.NewSHA256WithRSAVerifier(certificateVisitor),
	)

	// 创建全局管理器
	globalManager = &WechatPayManager{
		config:     config,
		client:     client,
		privateKey: privateKey,
		handler:    handler,
	}

	// 初始化各个服务
	wechatPayConfig := &WechatPayConfig{
		AppID:             config.AppID,
		MchID:             config.MchID,
		CertSerialNo:      serialNumber,
		APIv3Key:          config.ApiV3Key,
		PrivateKeyContent: config.Certificate, // 从配置中的私钥内容
		NotifyURL:         notifyURL,          // 支付回调URL
		RefundNotifyURL:   refundNotifyURL,    // 退款回调URL
	}

	InitGlobalPaymentService(client, wechatPayConfig)
	InitGlobalRefundService(client, wechatPayConfig)
	InitGlobalNotifyService(wechatPayConfig)

	logx.Info("微信支付初始化成功")
	return nil
}

// GetWechatPayManager 获取微信支付管理器
func GetWechatPayManager() *WechatPayManager {
	return globalManager
}

// GetClient 获取微信支付客户端
func GetClient() *core.Client {
	if globalManager == nil {
		return nil
	}
	return globalManager.client
}

// GetNotifyHandler 获取回调处理器
func GetNotifyHandler() *notify.Handler {
	if globalManager == nil {
		return nil
	}
	return globalManager.handler
}

// parsePrivateKeyFromCertificate 从配置字符串中解析私钥
// 配置中的 certificate 字段应该包含私钥的PEM格式字符串
func parsePrivateKeyFromCertificate(privateKeyPem string) (*rsa.PrivateKey, error) {
	if privateKeyPem == "" {
		return nil, fmt.Errorf("私钥配置为空")
	}

	// 直接从配置字符串解析私钥
	block, _ := pem.Decode([]byte(privateKeyPem))
	if block == nil {
		return nil, fmt.Errorf("无法解析PEM格式的私钥")
	}

	// 检查PEM块类型
	if block.Type != "PRIVATE KEY" && block.Type != "RSA PRIVATE KEY" {
		return nil, fmt.Errorf("PEM块类型错误，应该是PRIVATE KEY或RSA PRIVATE KEY，实际是: %s", block.Type)
	}

	// 解析私钥
	if block.Type == "PRIVATE KEY" {
		// PKCS#8格式
		key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("解析PKCS#8私钥失败: %w", err)
		}
		rsaKey, ok := key.(*rsa.PrivateKey)
		if !ok {
			return nil, fmt.Errorf("私钥不是RSA类型")
		}
		return rsaKey, nil
	} else {
		// PKCS#1格式
		return x509.ParsePKCS1PrivateKey(block.Bytes)
	}
}

// getCertificateSerialNumber 获取证书序列号
func getCertificateSerialNumber(certPem string) (string, error) {
	block, _ := pem.Decode([]byte(certPem))
	if block == nil {
		return "", fmt.Errorf("无法解析证书")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%X", cert.SerialNumber), nil
}
