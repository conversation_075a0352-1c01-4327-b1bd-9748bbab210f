package model

import (
	"context"
	"time"

	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// ChatSession 聊天会话数据模型
type ChatSession struct {
	SessionID     string         `db:"session_id" gorm:"primaryKey;type:varchar(64)"`
	UserID        int64          `db:"user_id" gorm:"index:idx_user_id"`
	ModelID       int            `db:"model_id" gorm:"index:idx_model_id;comment:模型ID"`
	ModelType     string         `db:"model_type" gorm:"type:varchar(50);index:idx_model_type;comment:模型类型"`
	Title         string         `db:"title" gorm:"type:varchar(200);comment:会话标题"`
	FirstQuestion string         `db:"first_question" gorm:"type:text;comment:首次提问内容"`
	LastMessage   string         `db:"last_message" gorm:"type:text;comment:最后一条消息"`
	MessageCount  int            `db:"message_count" gorm:"default:0;comment:消息数量"`
	FullContent   string         `db:"full_content" gorm:"type:longtext"`
	Status        int            `db:"status" gorm:"default:1;index:idx_status;comment:状态(1-进行中,2-已结束)"`
	StartTime     time.Time      `db:"start_time" gorm:"autoCreateTime"`
	EndTime       time.Time      `db:"end_time"`
	CreatedAt     time.Time      `gorm:"autoCreateTime"`
	UpdatedAt     time.Time      `gorm:"autoUpdateTime"`
	DeletedAt     gorm.DeletedAt `gorm:"index"`
}

// TableName 设置ChatSession的表名
func (ChatSession) TableName() string {
	return "med_chat_session"
}

// ChatSessionModel 聊天会话数据模型接口
type ChatSessionModel interface {
	Insert(ctx context.Context, data *ChatSession) error
	Update(ctx context.Context, data *ChatSession) error
	FindBySessionID(ctx context.Context, sessionID string) (*ChatSession, error)
	FindByUserID(ctx context.Context, userID int64, limit, offset int) ([]*ChatSession, error)
}

// defaultChatSessionModel 默认实现
type defaultChatSessionModel struct{}

// NewChatSessionModel 创建聊天会话数据模型
func NewChatSessionModel() ChatSessionModel {
	return &defaultChatSessionModel{}
}

// Insert 插入聊天会话数据
func (m *defaultChatSessionModel) Insert(ctx context.Context, data *ChatSession) error {
	if data.StartTime.IsZero() {
		data.StartTime = time.Now()
	}

	if data.EndTime.IsZero() {
		data.EndTime = time.Now()
	}

	return mysql.Master().WithContext(ctx).Create(data).Error
}

// Update 更新聊天会话数据
func (m *defaultChatSessionModel) Update(ctx context.Context, data *ChatSession) error {
	if data.EndTime.IsZero() {
		data.EndTime = time.Now()
	}

	// 构建更新字段
	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	// 只更新非空字段
	if data.FullContent != "" {
		updates["full_content"] = data.FullContent
	}
	if data.FirstQuestion != "" {
		updates["first_question"] = data.FirstQuestion
	}
	if data.LastMessage != "" {
		updates["last_message"] = data.LastMessage
	}
	if data.MessageCount > 0 {
		updates["message_count"] = data.MessageCount
	}
	if data.Status > 0 {
		updates["status"] = data.Status
	}
	if !data.EndTime.IsZero() {
		updates["end_time"] = data.EndTime
	}

	return mysql.Master().WithContext(ctx).Model(&ChatSession{}).
		Where("session_id = ? AND deleted_at IS NULL", data.SessionID).
		Updates(updates).Error
}

// FindBySessionID 根据会话ID查询聊天会话数据
func (m *defaultChatSessionModel) FindBySessionID(ctx context.Context, sessionID string) (*ChatSession, error) {
	var result ChatSession
	err := mysql.Slave().WithContext(ctx).
		Where("session_id = ? AND deleted_at IS NULL", sessionID).
		First(&result).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logx.Errorf("查询聊天会话数据失败: %v", err)
		return nil, err
	}

	return &result, nil
}

// FindByUserID 根据用户ID查询聊天会话数据
func (m *defaultChatSessionModel) FindByUserID(ctx context.Context, userID int64, limit, offset int) ([]*ChatSession, error) {
	var result []*ChatSession
	err := mysql.Slave().WithContext(ctx).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Order("start_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		logx.Errorf("查询用户聊天会话数据失败: %v", err)
		return nil, err
	}

	return result, nil
}

// FindByUserIDAndModelType 根据用户ID和模型类型查询聊天会话数据
func (m *defaultChatSessionModel) FindByUserIDAndModelType(ctx context.Context, userID int64, modelType string, limit, offset int) ([]*ChatSession, error) {
	var result []*ChatSession
	err := mysql.Slave().WithContext(ctx).
		Where("user_id = ? AND model_type = ? AND deleted_at IS NULL", userID, modelType).
		Order("start_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		logx.Errorf("查询用户特定类型聊天会话数据失败: %v", err)
		return nil, err
	}

	return result, nil
}

// UpdateSessionInfo 更新会话信息
func (m *defaultChatSessionModel) UpdateSessionInfo(ctx context.Context, sessionID string, updates map[string]interface{}) error {
	updates["updated_at"] = time.Now()

	return mysql.Master().WithContext(ctx).Model(&ChatSession{}).
		Where("session_id = ? AND deleted_at IS NULL", sessionID).
		Updates(updates).Error
}
