package tasks

import (
	"fmt"

	"yekaitai/pkg/adapters/abcyun"
	"yekaitai/pkg/infra/mysql"
	"yekaitai/wx_internal/modules/department/model"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// Department 科室信息结构体
type Department struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type int    `json:"type"`
}

// DepartmentListResponse 科室列表响应
type DepartmentListResponse struct {
	DepartmentList []Department `json:"departmentList"`
}

// AbcYunDepartmentSyncService ABC云科室同步服务
type AbcYunDepartmentSyncService struct {
	client         *abcyun.AbcYunClient
	db             *gorm.DB
	departmentRepo model.DepartmentRepository
}

// NewAbcYunDepartmentSyncService 创建ABC云科室同步服务
func NewAbcYunDepartmentSyncService(client *abcyun.AbcYunClient) *AbcYunDepartmentSyncService {
	return &AbcYunDepartmentSyncService{
		client:         client,
		db:             mysql.Master(),
		departmentRepo: model.NewDepartmentRepository(),
	}
}

// SyncDepartments 同步科室信息
func (s *AbcYunDepartmentSyncService) SyncDepartments() error {
	logx.Info("开始同步ABC云科室信息")

	// 首先获取门店信息
	clinicInfo, err := s.client.GetClinicInfo()
	if err != nil {
		return fmt.Errorf("获取门店信息失败: %w", err)
	}

	// 调用获取科室列表接口
	departments, err := s.getDepartmentList()
	if err != nil {
		return fmt.Errorf("获取科室列表失败: %w", err)
	}

	var syncCount, createCount, updateCount int

	// 遍历科室列表
	for _, dept := range departments {
		// 检查科室是否已存在（通过jgks_id字段）
		var existingDept model.Department
		err = s.db.Where("jgks_id = ?", dept.ID).First(&existingDept).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			logx.Errorf("查询科室失败: %v", err)
			continue
		}

		deptData := &model.Department{
			JgksID:   dept.ID, // 直接使用字符串ID
			JgksMC:   dept.Name,
			KsType:   dept.Type,
			ZfBz:     0,               // 未作废
			WsjgID:   clinicInfo.ID,   // 门店ID
			Hospital: clinicInfo.Name, // 门店名称
		}

		if err == nil { // 科室已存在
			// 更新现有科室，保留原有的创建时间
			deptData.ID = existingDept.ID
			deptData.CreatedAt = existingDept.CreatedAt // 保留原有创建时间
			err = s.departmentRepo.Update(deptData)
			if err != nil {
				logx.Errorf("更新科室失败: %v", err)
				continue
			}
			updateCount++
			logx.Infof("更新科室成功: %s (ID: %d)", deptData.JgksMC, deptData.ID)
		} else {
			// 创建新科室
			err = s.departmentRepo.Create(deptData)
			if err != nil {
				logx.Errorf("创建科室失败: %v", err)
				continue
			}
			createCount++
			logx.Infof("创建科室成功: %s (ID: %d)", deptData.JgksMC, deptData.ID)
		}
		syncCount++
	}

	logx.Infof("ABC云科室信息同步完成，总计同步: %d, 新建: %d, 更新: %d",
		syncCount, createCount, updateCount)
	return nil
}

// getDepartmentList 获取科室列表
func (s *AbcYunDepartmentSyncService) getDepartmentList() ([]Department, error) {
	// 使用已有的API接口获取科室列表
	departments, err := s.client.GetDepartmentList()
	if err != nil {
		return nil, fmt.Errorf("调用获取科室列表接口失败: %w", err)
	}

	// 转换为本地Department结构体
	var result []Department
	for _, dept := range departments {
		result = append(result, Department{
			ID:   dept.ID,
			Name: dept.Name,
			Type: dept.Type,
		})
	}

	return result, nil
}
