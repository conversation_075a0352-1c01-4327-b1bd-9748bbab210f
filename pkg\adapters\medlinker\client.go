package medlinker

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// Client 医联客户端
type Client struct {
	baseURL    string
	httpClient *http.Client
	timeout    time.Duration
}

// NewClient 创建医联客户端
func NewClient() *Client {
	return &Client{
		baseURL: "https://api.medlinker.com", // 医联API基础URL
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		timeout: 30 * time.Second,
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Phone string `json:"phone"`
	AppID string `json:"appid"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Token string `json:"token"`
	} `json:"data"`
}

// Login 医联登录接口
func (c *Client) Login(phone, appid string) (*LoginResponse, error) {
	logx.Infof("医联登录请求: phone=%s, appid=%s", phone, appid)

	req := LoginRequest{
		Phone: phone,
		AppID: appid,
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequest("POST", c.baseURL+"/api/med/login_v2", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var loginResp LoginResponse
	if err := json.Unmarshal(respBody, &loginResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	logx.Infof("医联登录响应: code=%d, msg=%s", loginResp.Code, loginResp.Msg)
	return &loginResp, nil
}

// ChatRequest 对话请求
type ChatRequest struct {
	Message string `json:"message"`
	Token   string `json:"token"`
}

// ChatResponse 对话响应
type ChatResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Reply string `json:"reply"`
	} `json:"data"`
}

// Chat 医联对话接口
func (c *Client) Chat(message, token string) (*ChatResponse, error) {
	logx.Infof("医联对话请求: message=%s", message)

	req := ChatRequest{
		Message: message,
		Token:   token,
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequest("POST", c.baseURL+"/api/med/chat", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("token", token)

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var chatResp ChatResponse
	if err := json.Unmarshal(respBody, &chatResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	logx.Infof("医联对话响应: code=%d, msg=%s", chatResp.Code, chatResp.Msg)
	return &chatResp, nil
}

// ValidateToken 验证token是否有效
func (c *Client) ValidateToken(token string) bool {
	logx.Infof("验证医联token: %s", token[:10]+"...")

	// 这里可以调用医联的token验证接口
	// 暂时简单验证token格式
	if len(token) < 20 {
		return false
	}

	// 可以添加更复杂的验证逻辑
	// 例如：调用医联API验证token有效性

	return true
}

// SetBaseURL 设置基础URL
func (c *Client) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

// SetTimeout 设置超时时间
func (c *Client) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
	c.httpClient.Timeout = timeout
}
