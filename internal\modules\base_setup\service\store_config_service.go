package service

import (
	"context"
	"fmt"

	"yekaitai/internal/modules/base_setup/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

type StoreConfigService struct {
	db *gorm.DB
}

func NewStoreConfigService() *StoreConfigService {
	return &StoreConfigService{
		db: mysql.GetDB(),
	}
}

// CreateStoreConfig 创建店铺配置（如果不存在）
func (s *StoreConfigService) CreateStoreConfig(ctx context.Context, req *model.StoreConfigCreateRequest) (*model.StoreConfig, error) {
	// 检查是否已存在配置
	var existingConfig model.StoreConfig
	err := s.db.WithContext(ctx).First(&existingConfig).Error
	if err == nil {
		return nil, fmt.Errorf("店铺配置已存在，请使用更新接口")
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询店铺配置失败: %w", err)
	}

	config := &model.StoreConfig{
		StoreName:            req.StoreName,
		StoreDescription:     req.StoreDescription,
		StoreAddress:         req.StoreAddress,
		StorePhone:           req.StorePhone,
		ProvinceID:           req.ProvinceID,
		CityID:               req.CityID,
		AreaID:               req.AreaID,
		BusinessLicense:      req.BusinessLicense,
		MedicalLicense:       req.MedicalLicense,
		InternetLicense:      req.InternetLicense,
		SecondClassLicense:   req.SecondClassLicense,
		ThirdClassLicense:    req.ThirdClassLicense,
		InternetSalesLicense: req.InternetSalesLicense,
		LogoImage:            req.LogoImage,
		Status:               req.Status,
	}

	if err := s.db.WithContext(ctx).Create(config).Error; err != nil {
		return nil, fmt.Errorf("创建店铺配置失败: %w", err)
	}

	return config, nil
}

// UpdateStoreConfig 更新店铺配置
func (s *StoreConfigService) UpdateStoreConfig(ctx context.Context, req *model.StoreConfigUpdateRequest) (*model.StoreConfig, error) {
	// 获取现有配置
	config, err := s.GetStoreConfig(ctx)
	if err != nil {
		// 如果不存在，创建一个新的
		createReq := &model.StoreConfigCreateRequest{
			StoreName:            req.StoreName,
			StoreDescription:     req.StoreDescription,
			StoreAddress:         req.StoreAddress,
			StorePhone:           req.StorePhone,
			ProvinceID:           req.ProvinceID,
			CityID:               req.CityID,
			AreaID:               req.AreaID,
			BusinessLicense:      req.BusinessLicense,
			MedicalLicense:       req.MedicalLicense,
			InternetLicense:      req.InternetLicense,
			SecondClassLicense:   req.SecondClassLicense,
			ThirdClassLicense:    req.ThirdClassLicense,
			InternetSalesLicense: req.InternetSalesLicense,
			LogoImage:            req.LogoImage,
			Status:               req.Status,
		}
		return s.CreateStoreConfig(ctx, createReq)
	}

	// 更新配置信息
	config.StoreName = req.StoreName
	config.StoreDescription = req.StoreDescription
	config.StoreAddress = req.StoreAddress
	config.StorePhone = req.StorePhone
	config.ProvinceID = req.ProvinceID
	config.CityID = req.CityID
	config.AreaID = req.AreaID
	config.BusinessLicense = req.BusinessLicense
	config.MedicalLicense = req.MedicalLicense
	config.InternetLicense = req.InternetLicense
	config.SecondClassLicense = req.SecondClassLicense
	config.ThirdClassLicense = req.ThirdClassLicense
	config.InternetSalesLicense = req.InternetSalesLicense
	config.LogoImage = req.LogoImage
	config.Status = req.Status

	if err := s.db.WithContext(ctx).Save(config).Error; err != nil {
		return nil, fmt.Errorf("更新店铺配置失败: %w", err)
	}

	return config, nil
}

// GetStoreConfig 获取店铺配置
func (s *StoreConfigService) GetStoreConfig(ctx context.Context) (*model.StoreConfig, error) {
	config := &model.StoreConfig{}
	if err := s.db.WithContext(ctx).First(config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("店铺配置不存在")
		}
		return nil, fmt.Errorf("查询店铺配置失败: %w", err)
	}
	return config, nil
}

// GetStoreConfigResponse 获取店铺配置响应格式
func (s *StoreConfigService) GetStoreConfigResponse(ctx context.Context) (*model.StoreConfigResponse, error) {
	config, err := s.GetStoreConfig(ctx)
	if err != nil {
		return nil, err
	}

	response := &model.StoreConfigResponse{
		ID:                   config.ID,
		StoreName:            config.StoreName,
		StoreDescription:     config.StoreDescription,
		StoreAddress:         config.StoreAddress,
		StorePhone:           config.StorePhone,
		ProvinceID:           config.ProvinceID,
		CityID:               config.CityID,
		AreaID:               config.AreaID,
		BusinessLicense:      config.BusinessLicense,
		MedicalLicense:       config.MedicalLicense,
		InternetLicense:      config.InternetLicense,
		SecondClassLicense:   config.SecondClassLicense,
		ThirdClassLicense:    config.ThirdClassLicense,
		InternetSalesLicense: config.InternetSalesLicense,
		LogoImage:            config.LogoImage,
		Status:               config.Status,
		CreatedAt:            config.CreatedAt,
		UpdatedAt:            config.UpdatedAt,
	}

	return response, nil
}

// DeleteStoreConfig 删除店铺配置
func (s *StoreConfigService) DeleteStoreConfig(ctx context.Context) error {
	result := s.db.WithContext(ctx).Delete(&model.StoreConfig{})
	if result.Error != nil {
		return fmt.Errorf("删除店铺配置失败: %w", result.Error)
	}
	return nil
}

// IsStoreConfigured 检查是否已配置店铺
func (s *StoreConfigService) IsStoreConfigured(ctx context.Context) (bool, error) {
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.StoreConfig{}).Count(&count).Error; err != nil {
		return false, fmt.Errorf("检查店铺配置失败: %w", err)
	}
	return count > 0, nil
}
