package jushuitan

import (
	"time"
)

// Config 聚水潭API客户端配置
type Config struct {
	BaseURL        string    // API基础URL
	AppKey         string    // 开发者应用Key
	AppSecret      string    // 开发者应用Secret
	AccessToken    string    // 商户授权token值
	RefreshToken   string    // 刷新token
	TokenExpiresAt time.Time // token过期时间
	IsTestEnv      bool      // 是否测试环境
}

// NewConfig 创建新的配置
func NewConfig(baseURL, appKey, appSecret, accessToken string) *Config {
	return &Config{
		BaseURL:     baseURL,
		AppKey:      appKey,
		AppSecret:   appSecret,
		AccessToken: accessToken,
	}
}
