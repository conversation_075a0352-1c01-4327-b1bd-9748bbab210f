package bootstrap

import (
	"log"
	"yekaitai/pkg/common/model/user"
	"yekaitai/pkg/infra/mysql"
)

// MigrateTagTables 执行标签表结构迁移
func MigrateTagTables() error {
	log.Println("开始执行标签表结构迁移...")

	// 执行标签表结构迁移
	db := mysql.Master()
	db.Set("gorm:table_options", "COMMENT='用户标签表'").AutoMigrate(&user.Tag{})
	db.Set("gorm:table_options", "COMMENT='用户标签关系表'").AutoMigrate(&user.UserTag{})
	log.Println("标签表结构迁移完成")
	return nil
}
