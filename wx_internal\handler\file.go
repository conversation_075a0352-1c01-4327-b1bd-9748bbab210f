package handler

import (
	"fmt"
	"net/http"

	"yekaitai/pkg/response"
	"yekaitai/pkg/upload"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/zap"
)

// UploadFileHandler 文件上传处理器
func UploadFileHandler(ctx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单
		err := r.ParseMultipartForm(10 << 20) // 限制上传文件大小为10MB
		if err != nil {
			logx.Error("解析表单失败", zap.Error(err))
			response.Error(w, http.StatusBadRequest, err.Error())
			return
		}

		// 获取文件
		file, header, err := r.FormFile("file")
		if err != nil {
			logx.Error("获取文件失败", zap.Error(err))
			response.Error(w, http.StatusBadRequest, err.Error())
			return
		}
		defer file.Close()

		// 获取上传类型和自定义路径
		uploadTypeStr := r.FormValue("type")
		customPath := r.FormValue("path")

		// 确定上传类型
		var uploadType upload.UploadType
		switch uploadTypeStr {
		case "image":
			uploadType = upload.UploadTypeImage
		case "document":
			uploadType = upload.UploadTypeDocument
		case "video":
			uploadType = upload.UploadTypeVideo
		default:
			uploadType = upload.UploadTypeGeneral
		}

		// 使用七牛云上传组件
		result, err := upload.DefaultService.UploadFile(r.Context(), header, uploadType, customPath)
		if err != nil {
			logx.Error("上传文件失败", zap.Error(err), zap.String("filename", header.Filename))
			response.Error(w, http.StatusInternalServerError, fmt.Sprintf("上传文件失败: %v", err))
			return
		}

		// 返回文件信息
		response.Success(w, result)
	}
}

// UploadImageHandler 图片上传处理器
func UploadImageHandler(ctx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单
		err := r.ParseMultipartForm(10 << 20) // 限制上传文件大小为10MB
		if err != nil {
			logx.Error("解析表单失败", zap.Error(err))
			response.Error(w, http.StatusBadRequest, err.Error())
			return
		}

		// 获取文件
		file, header, err := r.FormFile("file")
		if err != nil {
			logx.Error("获取文件失败", zap.Error(err))
			response.Error(w, http.StatusBadRequest, err.Error())
			return
		}
		defer file.Close()

		// 获取自定义路径
		customPath := r.FormValue("path")

		// 使用七牛云上传组件上传图片
		result, err := upload.DefaultService.UploadImage(r.Context(), header, customPath)
		if err != nil {
			logx.Error("上传图片失败", zap.Error(err), zap.String("filename", header.Filename))
			response.Error(w, http.StatusInternalServerError, fmt.Sprintf("上传图片失败: %v", err))
			return
		}

		// 返回图片信息
		response.Success(w, result)
	}
}

// UploadDocumentHandler 文档上传处理器
func UploadDocumentHandler(ctx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单
		err := r.ParseMultipartForm(10 << 20) // 限制上传文件大小为10MB
		if err != nil {
			logx.Error("解析表单失败", zap.Error(err))
			response.Error(w, http.StatusBadRequest, err.Error())
			return
		}

		// 获取文件
		file, header, err := r.FormFile("file")
		if err != nil {
			logx.Error("获取文件失败", zap.Error(err))
			response.Error(w, http.StatusBadRequest, err.Error())
			return
		}
		defer file.Close()

		// 获取自定义路径
		customPath := r.FormValue("path")

		// 使用七牛云上传组件上传文档
		result, err := upload.DefaultService.UploadDocument(r.Context(), header, customPath)
		if err != nil {
			logx.Error("上传文档失败", zap.Error(err), zap.String("filename", header.Filename))
			response.Error(w, http.StatusInternalServerError, fmt.Sprintf("上传文档失败: %v", err))
			return
		}

		// 返回文档信息
		response.Success(w, result)
	}
}

// UploadVideoHandler 视频上传处理器
func UploadVideoHandler(ctx *svc.WxServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 解析表单
		err := r.ParseMultipartForm(1536 << 20) // 限制上传文件大小为1.5GB
		if err != nil {
			logx.Error("解析表单失败", zap.Error(err))
			response.Error(w, http.StatusBadRequest, err.Error())
			return
		}

		// 获取文件
		file, header, err := r.FormFile("file")
		if err != nil {
			logx.Error("获取文件失败", zap.Error(err))
			response.Error(w, http.StatusBadRequest, err.Error())
			return
		}
		defer file.Close()

		// 获取自定义路径
		customPath := r.FormValue("path")

		// 使用七牛云上传组件上传视频
		result, err := upload.DefaultService.UploadVideo(r.Context(), header, customPath)
		if err != nil {
			logx.Error("上传视频失败", zap.Error(err), zap.String("filename", header.Filename))
			response.Error(w, http.StatusInternalServerError, fmt.Sprintf("上传视频失败: %v", err))
			return
		}

		// 返回视频信息
		response.Success(w, result)
	}
}
