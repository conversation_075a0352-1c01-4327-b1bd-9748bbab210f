package types

// AbcYunProductListRequest 获取商品列表请求
type AbcYunProductListRequest struct {
	AbcYunRequest
	Type           int    `form:"type"`                    // 类型: 1,药品; 2,物资; 3,检查; 4,治疗; 5,挂号; 7,商品; 11,套餐; 19,其他
	BeginDate      string `form:"beginDate,optional"`      // 开始日期 (格式yyyy-MM-dd)
	EndDate        string `form:"endDate,optional"`        // 结束日期 (格式yyyy-MM-dd)
	DateFieldType  int    `form:"dateFieldType,optional"`  // 查询日期类型: 1,创建时间; 2,最后修改时间
	IncludeDeleted int    `form:"includeDeleted,optional"` // 是否包含已删除商品, 0:不包含, 1:包含, 默认0
	Limit          int    `form:"limit,optional"`          // 每页显示条数，默认20，最大为200
	Offset         int    `form:"offset,optional"`         // 分页起始下标，默认0
}

// AbcYunProductDetailRequest 获取商品详情请求
type AbcYunProductDetailRequest struct {
	AbcYunRequest
	ProductID string `path:"id"` // 商品ID
}

// AbcYunProductOutOrderListRequest 获取出库单列表请求
type AbcYunProductOutOrderListRequest struct {
	AbcYunRequest
	BeginDate string `form:"beginDate"`       // 开始日期 (格式yyyy-MM-dd)
	EndDate   string `form:"endDate"`         // 结束日期 (格式yyyy-MM-dd)
	Limit     int    `form:"limit,optional"`  // 每页显示条数，默认20，最大为200
	Offset    int    `form:"offset,optional"` // 分页起始下标，默认0
}

// AbcYunProductOutOrderDetailRequest 获取出库单详情请求
type AbcYunProductOutOrderDetailRequest struct {
	AbcYunRequest
	OrderID string `path:"orderId"` // 出库单ID
}

// AbcYunProductInOrderListRequest 获取入库单列表请求
type AbcYunProductInOrderListRequest struct {
	AbcYunRequest
	BeginDate string `form:"beginDate"`       // 开始日期 (格式yyyy-MM-dd)
	EndDate   string `form:"endDate"`         // 结束日期 (格式yyyy-MM-dd)
	Type      int    `form:"type,optional"`   // 类型 0:采购入库 10:退货出库，默认0
	Limit     int    `form:"limit,optional"`  // 每页显示条数，默认20，最大为200
	Offset    int    `form:"offset,optional"` // 分页起始下标，默认0
}

// AbcYunProductInOrderDetailRequest 获取入库单详情请求
type AbcYunProductInOrderDetailRequest struct {
	AbcYunRequest
	OrderID string `path:"orderId"` // 入库单ID
}

// AbcYunProductCheckOrderListRequest 获取盘点单列表请求
type AbcYunProductCheckOrderListRequest struct {
	AbcYunRequest
	BeginDate string `form:"beginDate"`       // 开始日期 (格式yyyy-MM-dd)
	EndDate   string `form:"endDate"`         // 结束日期 (格式yyyy-MM-dd)
	Limit     int    `form:"limit,optional"`  // 每页显示条数，默认20，最大为200
	Offset    int    `form:"offset,optional"` // 分页起始下标，默认0
}

// AbcYunProductCheckOrderDetailRequest 获取盘点单详情请求
type AbcYunProductCheckOrderDetailRequest struct {
	AbcYunRequest
	OrderID string `path:"orderId"` // 盘点单ID
}

// ProductTypeTree 商品分类树节点
type ProductTypeTree struct {
	ID       int               `json:"id"`                 // ID
	Type     int               `json:"type"`               // 类型
	SubType  int               `json:"subType"`            // 子类型
	Category string            `json:"category"`           // 分类
	IsLeaf   int               `json:"isLeaf"`             // 是否叶子节点
	Name     string            `json:"name"`               // 名称
	Children []ProductTypeTree `json:"children,omitempty"` // 子分类
}

// ProductTypeData 商品分类响应数据
type ProductTypeData struct {
	TypeTree []ProductTypeTree `json:"typeTree"` // 分类树
}

// AbcYunProductTypesResponse 商品分类响应
type AbcYunProductTypesResponse struct {
	AbcYunResponse
	Data ProductTypeData `json:"data"` // 商品分类数据
}

// CustomProductType 自定义商品分类
type CustomProductType struct {
	ID     string `json:"id"`     // ID
	TypeID int    `json:"typeId"` // 类型ID
	Name   string `json:"name"`   // 名称
}

// CustomProductTypeData 自定义商品分类响应数据
type CustomProductTypeData struct {
	List []CustomProductType `json:"list"` // 分类列表
}

// AbcYunProductCustomTypesResponse 自定义商品分类响应
type AbcYunProductCustomTypesResponse struct {
	AbcYunResponse
	Data CustomProductTypeData `json:"data"` // 自定义商品分类数据
}

// ProductInfo 商品信息 (用于列表)
type ProductInfo struct {
	ID            string  `json:"id"`            // 商品ID
	ShortID       string  `json:"shortId"`       // 商品短ID
	Name          string  `json:"name"`          // 名称
	Specification string  `json:"specification"` // 规格
	TypeID        int     `json:"typeId"`        // 类型ID
	TypeName      string  `json:"typeName"`      // 类型名称
	CombineType   int     `json:"combineType"`   // 合并类型
	PieceNum      float64 `json:"pieceNum"`      // 转换比 (包装单位数量 / 基本单位数量)
	PieceUnit     string  `json:"pieceUnit"`     // 基本单位
	PackageUnit   string  `json:"packageUnit"`   // 包装单位
	LastModified  string  `json:"lastModified"`  // 最后修改时间
	Status        int     `json:"status"`        // 状态
	DisableSell   int     `json:"disableSell"`   // 是否禁售 (0: 否, 1: 是)
}

// ProductListData 商品列表响应数据
type ProductListData struct {
	Rows   []ProductInfo `json:"rows"`   // 商品列表
	Total  int           `json:"total"`  // 总数
	Offset int           `json:"offset"` // 偏移量
	Limit  int           `json:"limit"`  // 限制数量
}

// AbcYunProductListResponse 商品列表响应
type AbcYunProductListResponse struct {
	AbcYunResponse
	Data ProductListData `json:"data"` // 商品列表数据
}

// InsuranceType 保险类型
type InsuranceType struct {
	InsuranceType      string  `json:"insuranceType"`      // 保险类型
	ReimbursementRatio float64 `json:"reimbursementRatio"` // 报销比例
}

// ShebaoInfo 社保信息
type ShebaoInfo struct {
	NationalCode   string          `json:"nationalCode"`   // 国家编码
	InsuranceTypes []InsuranceType `json:"insuranceTypes"` // 保险类型列表
}

// GoodsSpu 商品SPU
type GoodsSpu struct {
	ID        string `json:"id"`        // ID
	Name      string `json:"name"`      // 名称
	BrandName string `json:"brandName"` // 品牌名称
	Material  string `json:"material"`  // 材料
}

// GoodsSpec 商品规格
type GoodsSpec struct {
	Color string `json:"color"` // 颜色
	Spec  string `json:"spec"`  // 规格
}

// ProductDetailInfo 商品详情信息
type ProductDetailInfo struct {
	ID             string              `json:"id"`                 // 商品ID
	Name           string              `json:"name"`               // 名称
	Shebao         ShebaoInfo          `json:"shebao"`             // 社保信息
	DisableSell    int                 `json:"disableSell"`        // 是否禁售
	TypeID         int                 `json:"typeId"`             // 类型ID
	TypeName       string              `json:"typeName"`           // 类型名称
	CustomTypeID   string              `json:"customTypeId"`       // 自定义类型ID
	CustomTypeName string              `json:"customTypeName"`     // 自定义类型名称
	Manufacturer   string              `json:"manufacturer"`       // 制造商
	MedicineNmpn   string              `json:"medicineNmpn"`       // 药品通用名 (NMPA)
	BarCode        string              `json:"barCode"`            // 条形码
	PieceUnit      string              `json:"pieceUnit"`          // 基本单位
	PieceNum       float64             `json:"pieceNum"`           // 转换比 (包装单位数量 / 基本单位数量)
	PiecePrice     float64             `json:"piecePrice"`         // 基本单位价格
	PackageUnit    string              `json:"packageUnit"`        // 包装单位
	PackagePrice   float64             `json:"packagePrice"`       // 包装单位价格
	ShortID        string              `json:"shortId"`            // 商品助记码/简码
	Specification  string              `json:"specification"`      // 规格
	OtcType        int                 `json:"otcType"`            // OTC类型
	Status         int                 `json:"status"`             // 状态
	LastModified   string              `json:"lastModified"`       // 最后修改时间
	GoodsSpu       GoodsSpu            `json:"goodsSpu"`           // 商品SPU
	GoodsSpec      GoodsSpec           `json:"goodsSpec"`          // 商品规格
	Children       []ProductDetailInfo `json:"children,omitempty"` // 子产品 (用于套餐)
}

// AbcYunProductDetailResponse 商品详情响应
type AbcYunProductDetailResponse struct {
	AbcYunResponse
	Data ProductDetailInfo `json:"data"` // 商品详情数据
}

// PharmacyInfo 药房信息 (部分)
type PharmacyInfo struct {
	No int `json:"no"` // 药房编号
}

// StockOutOrder 出库单信息 (用于列表)
type StockOutOrder struct {
	ID            int          `json:"id"`            // ID
	OrderNo       string       `json:"orderNo"`       // 订单号
	Status        int          `json:"status"`        // 状态
	OrganName     string       `json:"organName"`     // 机构名称
	Type          int          `json:"type"`          // 类型
	KindCount     int          `json:"kindCount"`     // 品种数
	Count         float64      `json:"count"`         // 总数量
	Amount        float64      `json:"amount"`        // 总金额
	Receiver      string       `json:"receiver"`      // 接收人
	Operator      string       `json:"operator"`      // 操作员
	EffectiveDate string       `json:"effectiveDate"` // 生效日期
	LastModified  string       `json:"lastModified"`  // 最后修改时间
	InOrderId     string       `json:"inOrderId"`     // 关联入库单ID (退货出库时)
	Pharmacy      PharmacyInfo `json:"pharmacy"`      // 药房信息
}

// StockOutOrderListData 出库单列表响应数据
type StockOutOrderListData struct {
	StockOutOrderList []StockOutOrder `json:"stockOutOrderList"` // 出库单列表
}

// AbcYunProductOutOrderListResponse 出库单列表响应
type AbcYunProductOutOrderListResponse struct {
	AbcYunResponse
	Data StockOutOrderListData `json:"data"` // 出库单列表数据
}

// Comment 评论/备注信息
type Comment struct {
	Time         string `json:"time"`         // 时间
	Content      string `json:"content"`      // 内容
	EmployeeID   string `json:"employeeId"`   // 员工ID
	EmployeeName string `json:"employeeName"` // 员工姓名
}

// StockOutBatch 出库批次信息
type StockOutBatch struct {
	StockID            int      `json:"stockId"`            // 库存ID
	StockInOutDetailID string   `json:"stockInOutDetailId"` // 出入库明细ID
	BatchID            int      `json:"batchId"`            // 批次ID
	Supplier           Supplier `json:"supplier"`           // 供应商
	BatchNo            string   `json:"batchNo"`            // 批号
	ProductionDate     string   `json:"productionDate"`     // 生产日期
	ExpiryDate         string   `json:"expiryDate"`         // 有效期
	PackageCount       float64  `json:"packageCount"`       // 包装数量
	PieceCount         float64  `json:"pieceCount"`         // 基本单位数量
	PackageCostPrice   float64  `json:"packageCostPrice"`   // 包装成本价
	PieceNum           int      `json:"pieceNum"`           // 转换比
	CostPrice          float64  `json:"costPrice"`          // 基本单位成本价
}

// StockOutOrderItem 出库单明细项
type StockOutOrderItem struct {
	ID               int             `json:"id"`               // ID
	ProductID        string          `json:"productId"`        // 商品ID
	ProductShortID   string          `json:"productShortId"`   // 商品助记码
	ProductName      string          `json:"productName"`      // 商品名称
	Specification    string          `json:"specification"`    // 规格
	PieceNum         float64         `json:"pieceNum"`         // 转换比
	PieceUnit        string          `json:"pieceUnit"`        // 基本单位
	PackageUnit      string          `json:"packageUnit"`      // 包装单位
	PackageCount     float64         `json:"packageCount"`     // 包装数量
	PieceCount       float64         `json:"pieceCount"`       // 基本单位数量
	PackageCostPrice float64         `json:"packageCostPrice"` // 包装成本价
	StockInID        int             `json:"stockInId"`        // 关联入库单ID (退货出库时)
	Amount           float64         `json:"amount"`           // 金额
	LastModified     string          `json:"lastModified"`     // 最后修改时间
	StockOutBatches  []StockOutBatch `json:"stockOutBatches"`  // 出库批次
}

// StockOutOrderDetail 出库单详情
type StockOutOrderDetail struct {
	StockOutOrder                        // 嵌入基础信息
	Comment          Comment             `json:"comment"`          // 备注
	OutOrderItemList []StockOutOrderItem `json:"outOrderItemList"` // 出库明细列表
}

// AbcYunProductOutOrderDetailResponse 出库单详情响应
type AbcYunProductOutOrderDetailResponse struct {
	AbcYunResponse
	Data StockOutOrderDetail `json:"data"` // 出库单详情数据
}

// StockInOrder 入库单信息 (用于列表)
type StockInOrder struct {
	ID            int          `json:"id"`            // ID
	InOrderID     int          `json:"inOrderId"`     // 入库单ID (与 id 相同?)
	OrderNo       string       `json:"orderNo"`       // 订单号
	Status        int          `json:"status"`        // 状态
	OrganName     string       `json:"organName"`     // 机构名称
	OrganID       string       `json:"organId"`       // 机构ID
	Amount        float64      `json:"amount"`        // 总金额
	Count         float64      `json:"count"`         // 总数量
	KindCount     int          `json:"kindCount"`     // 品种数
	Operator      string       `json:"operator"`      // 操作员
	OperatorID    string       `json:"operatorId"`    // 操作员ID
	Supplier      string       `json:"supplier"`      // 供应商名称
	SupplierID    string       `json:"supplierId"`    // 供应商ID
	EffectiveDate string       `json:"effectiveDate"` // 生效日期
	LastModified  string       `json:"lastModified"`  // 最后修改时间
	Pharmacy      PharmacyInfo `json:"pharmacy"`      // 药房信息
	Type          int          `json:"type"`          // 类型 (0:采购入库 10:退货出库)
}

// StockInOrderListData 入库单列表响应数据
type StockInOrderListData struct {
	StockInOrderList []StockInOrder `json:"stockInOrderList"` // 入库单列表
}

// AbcYunProductInOrderListResponse 入库单列表响应
type AbcYunProductInOrderListResponse struct {
	AbcYunResponse
	Data StockInOrderListData `json:"data"` // 入库单列表数据
}

// StockInOrderItem 入库单明细项
type StockInOrderItem struct {
	ID                 int     `json:"id"`                 // ID
	InID               int     `json:"inId"`               // 入库单ID (与StockInOrder.ID关联?)
	OriginalItemID     int     `json:"originalItemId"`     // 原始项目ID (退货入库时?)
	ProductID          string  `json:"productId"`          // 商品ID
	ProductShortID     string  `json:"productShortId"`     // 商品助记码
	ProductName        string  `json:"productName"`        // 商品名称
	Specification      string  `json:"specification"`      // 规格
	ExpiryDate         string  `json:"expiryDate"`         // 有效期 (格式yyyy-MM-dd)
	ProductionDate     string  `json:"productionDate"`     // 生产日期 (格式yyyy-MM-dd)
	PieceNum           float64 `json:"pieceNum"`           // 转换比
	PieceUnit          string  `json:"pieceUnit"`          // 基本单位
	PackageUnit        string  `json:"packageUnit"`        // 包装单位
	PackageCostPrice   float64 `json:"packageCostPrice"`   // 包装成本价
	PackageCount       float64 `json:"packageCount"`       // 包装数量
	PieceCount         float64 `json:"pieceCount"`         // 基本单位数量
	Amount             float64 `json:"amount"`             // 金额
	TotalCost          float64 `json:"totalCost"`          // 总成本
	BatchID            int     `json:"batchId"`            // 批次ID
	BatchNo            string  `json:"batchNo"`            // 批号
	OutDetailID        string  `json:"outDetailId"`        // 出库明细ID (退货入库时?)
	StockInOutDetailID string  `json:"stockInOutDetailId"` // 出入库明细ID
	Created            string  `json:"created"`            // 创建时间
	LastModified       string  `json:"lastModified"`       // 最后修改时间
}

// StockInOrderDetail 入库单详情
type StockInOrderDetail struct {
	StockInOrder                       // 嵌入基础信息
	Comment         Comment            `json:"comment"`         // 备注
	InOrderItemList []StockInOrderItem `json:"inOrderItemList"` // 入库明细列表
}

// AbcYunProductInOrderDetailResponse 入库单详情响应
type AbcYunProductInOrderDetailResponse struct {
	AbcYunResponse
	Data StockInOrderDetail `json:"data"` // 入库单详情数据
}

// CheckOrder 盘点单信息 (用于列表)
type CheckOrder struct {
	ID                   int     `json:"id"`                   // ID
	OrderNo              string  `json:"orderNo"`              // 订单号
	Status               int     `json:"status"`               // 状态
	OrganID              string  `json:"organId"`              // 机构ID
	OrganName            string  `json:"organName"`            // 机构名称
	Count                float64 `json:"count"`                // 总数量 (盘点后)
	KindCount            int     `json:"kindCount"`            // 品种数
	TotalCostPriceChange float64 `json:"totalCostPriceChange"` // 总成本变化
	TotalPriceChange     float64 `json:"totalPriceChange"`     // 总售价变化
	Operator             string  `json:"operator"`             // 操作员
	OperatorID           string  `json:"operatorId"`           // 操作员ID
	EffectiveDate        string  `json:"effectiveDate"`        // 生效日期
	LastModified         string  `json:"lastModified"`         // 最后修改时间
}

// CheckOrderListData 盘点单列表响应数据
type CheckOrderListData struct {
	CheckOrderList []CheckOrder `json:"checkOrderList"` // 盘点单列表
}

// AbcYunProductCheckOrderListResponse 盘点单列表响应
type AbcYunProductCheckOrderListResponse struct {
	AbcYunResponse
	Data CheckOrderListData `json:"data"` // 盘点单列表数据
}

// CheckBatch 盘点批次信息
type CheckBatch struct {
	StockID            int      `json:"stockId"`            // 库存ID
	StockInOutDetailID string   `json:"stockInOutDetailId"` // 出入库明细ID
	BatchID            int      `json:"batchId"`            // 批次ID
	Supplier           Supplier `json:"supplier"`           // 供应商
	BatchNo            string   `json:"batchNo"`            // 批号
	ProductionDate     string   `json:"productionDate"`     // 生产日期
	ExpiryDate         string   `json:"expiryDate"`         // 有效期
	PackageCount       float64  `json:"packageCount"`       // 包装数量 (盘点后)
	PieceCount         float64  `json:"pieceCount"`         // 基本单位数量 (盘点后)
	PackageCostPrice   float64  `json:"packageCostPrice"`   // 包装成本价
	PieceNum           int      `json:"pieceNum"`           // 转换比
	CostPrice          float64  `json:"costPrice"`          // 基本单位成本价
	// 注意：API文档中似乎没有明确盘点前后的批次数量，假设这里的数量是盘点后的
}

// CheckOrderItem 盘点单明细项
type CheckOrderItem struct {
	ID                 int          `json:"id"`                 // ID
	ProductID          string       `json:"productId"`          // 商品ID
	ProductShortID     string       `json:"productShortId"`     // 商品助记码
	ProductName        string       `json:"productName"`        // 商品名称
	Specification      string       `json:"specification"`      // 规格
	ExpiryDate         string       `json:"expiryDate"`         // 有效期 (格式yyyy-MM-dd)
	ProductionDate     string       `json:"productionDate"`     // 生产日期 (格式yyyy-MM-dd)
	PieceNum           float64      `json:"pieceNum"`           // 转换比
	PieceUnit          string       `json:"pieceUnit"`          // 基本单位
	PackageUnit        string       `json:"packageUnit"`        // 包装单位
	StockID            string       `json:"stockId"`            // 库存ID
	PackageCostPrice   float64      `json:"packageCostPrice"`   // 包装成本价
	BeforePieceCount   float64      `json:"beforePieceCount"`   // 盘点前基本单位数量
	BeforePackageCount float64      `json:"beforePackageCount"` // 盘点前包装数量
	AfterPieceCount    float64      `json:"afterPieceCount"`    // 盘点后基本单位数量
	AfterPackageCount  float64      `json:"afterPackageCount"`  // 盘点后包装数量
	ChangePieceCount   float64      `json:"changePieceCount"`   // 基本单位数量变化 (盈亏)
	ChangePackageCount float64      `json:"changePackageCount"` // 包装数量变化 (盈亏)
	Amount             float64      `json:"amount"`             // 金额 (成本变化?)
	LastModified       string       `json:"lastModified"`       // 最后修改时间
	CheckBatches       []CheckBatch `json:"checkBatches"`       // 盘点批次
}

// CheckOrderDetail 盘点单详情
type CheckOrderDetail struct {
	CheckOrder                          // 嵌入基础信息
	CheckOrderItemList []CheckOrderItem `json:"checkOrderItemList"` // 盘点明细列表
	Comment            Comment          `json:"comment"`            // 备注
}

// AbcYunProductCheckOrderDetailResponse 盘点单详情响应
type AbcYunProductCheckOrderDetailResponse struct {
	AbcYunResponse
	Data CheckOrderDetail `json:"data"` // 盘点单详情数据
}

// --- Settlement Order List (5.3.11) --- //

// AbcYunSettlementOrderListRequest 获取结算单列表请求
type AbcYunSettlementOrderListRequest struct {
	AbcYunRequest
	BeginDate     string `form:"beginDate,optional"`     // 开始时间（格式yyyy-MM-dd）
	EndDate       string `form:"endDate,optional"`       // 结束时间（格式yyyy-MM-dd）
	DateFieldType int    `form:"dateFieldType,optional"` // 查询日期类型: 1,创建时间; 2,最后修改时间; 3,入库时间; 4,出库时间; 5,结算盘点时间 (默认1)
}

// SettlementOrderInfo 结算单信息 (用于列表)
type SettlementOrderInfo struct {
	ID              int     `json:"id"`              // ID
	CreatedUser     string  `json:"createdUser"`     // 创建用户
	CreateDate      string  `json:"createDate"`      // 创建日期
	SupplierName    string  `json:"supplierName"`    // 供应商名称
	Status          int     `json:"status"`          // 状态
	IncludeTaxPrice float64 `json:"includeTaxPrice"` // 含税金额
	ExcludeTaxPrice float64 `json:"excludeTaxPrice"` // 不含税金额
	EffectiveDate   string  `json:"effectiveDate"`   // 生效日期
	LastModified    string  `json:"lastModified"`    // 最后修改时间
}

// SettlementOrderListData 结算单列表响应数据
type SettlementOrderListData struct {
	SettlementOrderList []SettlementOrderInfo `json:"settlementOrderList"` // 结算单列表
}

// AbcYunSettlementOrderListResponse 结算单列表响应
type AbcYunSettlementOrderListResponse struct {
	AbcYunResponse
	Data SettlementOrderListData `json:"data"` // 结算单列表数据
}

// --- Settlement Order Detail (5.3.12) --- //

// AbcYunSettlementOrderDetailRequest 获取结算单详情请求
type AbcYunSettlementOrderDetailRequest struct {
	AbcYunRequest
	OrderID string `path:"orderId"` // 结算单ID
}

// SettlementOrderItem 结算单项
type SettlementOrderItem struct {
	ID                int     `json:"id"`                // ID
	Name              string  `json:"name"`              // 名称
	Specification     string  `json:"specification"`     // 规格
	Unit              string  `json:"unit"`              // 单位
	TotalPackageCount int     `json:"totalPackageCount"` // 总包装数量
	IncludeTaxPrice   float64 `json:"includeTaxPrice"`   // 含税金额
	ExcludeTaxPrice   float64 `json:"excludeTaxPrice"`   // 不含税金额
}

// SettlementInvoice 结算单发票
type SettlementInvoice struct {
	ID           int    `json:"id"`           // ID
	InvoicedDate string `json:"invoicedDate"` // 开票日期
	InvoiceNo    string `json:"invoiceNo"`    // 发票号
}

// SettlementInOut 结算单出入库信息
type SettlementInOut struct {
	ID                   int     `json:"id"`                   // ID
	IncludeTaxPrice      float64 `json:"includeTaxPrice"`      // 含税金额
	ExcludeTaxPrice      float64 `json:"excludeTaxPrice"`      // 不含税金额
	TotalPackageCount    float64 `json:"totalPackageCount"`    // 总包装数量
	ClinicID             string  `json:"clinicId"`             // 诊所ID
	ClinicName           string  `json:"clinicName"`           // 诊所名称
	OrderInOutDate       string  `json:"orderInOutDate"`       // 出入库日期
	OrderCreatedUserName string  `json:"orderCreatedUserName"` // 订单创建用户名称
	Type                 int     `json:"type"`                 // 类型
}

// SettlementOrderDetail 结算单详情
type SettlementOrderDetail struct {
	ID              int                   `json:"id"`              // ID
	ItemsList       []SettlementOrderItem `json:"itemsList"`       // 项目列表
	CreatedUser     string                `json:"createdUser"`     // 创建用户
	InvoicesList    []SettlementInvoice   `json:"invoicesList"`    // 发票列表
	CreateDate      string                `json:"createDate"`      // 创建日期
	InOutList       []SettlementInOut     `json:"inOutList"`       // 出入库列表
	SupplierName    string                `json:"supplierName"`    // 供应商名称
	Status          int                   `json:"status"`          // 状态
	IncludeTaxPrice float64               `json:"includeTaxPrice"` // 含税总金额
	ExcludeTaxPrice float64               `json:"excludeTaxPrice"` // 不含税总金额
	EffectiveDate   string                `json:"effectiveDate"`   // 生效日期
	LastModified    string                `json:"lastModified"`    // 最后修改时间
}

// AbcYunSettlementOrderDetailResponse 结算单详情响应
type AbcYunSettlementOrderDetailResponse struct {
	AbcYunResponse
	Data SettlementOrderDetail `json:"data"` // 结算单详情数据
}

// --- Supplier List (5.3.13) --- //

// AbcYunSupplierListRequest 获取供应商列表请求
type AbcYunSupplierListRequest struct {
	AbcYunRequest
	// No specific query params mentioned in the doc
}

// SupplierInfo 供应商信息 (用于列表)
type SupplierInfo struct {
	ID        string `json:"id"`        // ID
	Name      string `json:"name"`      // 名称
	Status    int    `json:"status"`    // 状态
	LicenseID string `json:"licenseId"` // 许可证ID
	Contact   string `json:"contact"`   // 联系人
	Mobile    string `json:"mobile"`    // 手机号
	Mark      string `json:"mark"`      // 标记
}

// SupplierListData 供应商列表响应数据
type SupplierListData struct {
	SupplierInfoList []SupplierInfo `json:"supplierInfoList"` // 供应商信息列表
}

// AbcYunSupplierListResponse 供应商列表响应
type AbcYunSupplierListResponse struct {
	AbcYunResponse
	Data SupplierListData `json:"data"` // 供应商列表数据
}

// --- InOut Stock Details (5.3.14) --- //

// ChargeSheetIdentifier 收费单标识符
type ChargeSheetIdentifier struct {
	PatientOrderID string `json:"patientOrderId"` // 患者订单ID
	ChargeSheetID  string `json:"chargeSheetId"`  // 收费单ID
}

// AbcYunInOutStockDetailsRequest 获取进销存明细请求
type AbcYunInOutStockDetailsRequest struct {
	AbcYunRequest                           // Embed ClinicID if needed, though not explicitly in doc body
	Date            string                  `json:"date,omitempty"`            // 日期 (格式yyyy-MM-dd)
	OrderIDList     []string                `json:"orderIdList,omitempty"`     // 订单ID列表 (出入库/盘点/发药/调拨)
	ChargeSheetList []ChargeSheetIdentifier `json:"chargeSheetList,omitempty"` // 收费单列表
	Type            int                     `json:"type,omitempty"`            // 类型 (似乎文档未明确含义)
	Offset          int                     `json:"offset"`                    // 偏移量
	Limit           int                     `json:"limit"`                     // 限制数量
}

// InOutStockDetail 进销存明细
type InOutStockDetail struct {
	StockLogID             string  `json:"stockLogId"`             // 库存日志ID
	Date                   string  `json:"date"`                   // 日期时间
	ProductID              string  `json:"productId"`              // 商品ID
	ProductShortID         string  `json:"productShortId"`         // 商品助记码
	ProductName            string  `json:"productName"`            // 商品名称
	PieceCount             float64 `json:"pieceCount"`             // 基本单位数量
	PackageCount           float64 `json:"packageCount"`           // 包装数量
	Action                 string  `json:"action"`                 // 操作类型 (e.g., "发药")
	OrderID                string  `json:"orderId"`                // 关联订单ID
	OrderItemID            string  `json:"orderItemId"`            // 关联订单项ID
	PatientOrderID         string  `json:"patientOrderId"`         // 患者订单ID (发药时)
	SourceOrderID          string  `json:"sourceOrderId"`          // 源订单ID
	SourceOrderItemID      string  `json:"sourceOrderItemId"`      // 源订单项ID
	PackageCostPrice       float64 `json:"packageCostPrice"`       // 包装成本价
	BatchID                int     `json:"batchId"`                // 批次ID
	BatchNo                string  `json:"batchNo"`                // 批号
	ProductionDate         string  `json:"productionDate"`         // 生产日期
	ExpireDate             string  `json:"expireDate"`             // 过期日期
	InOrderID              int     `json:"inOrderId"`              // 入库单ID
	InOrderItemID          int     `json:"inOrderItemId"`          // 入库单项ID
	InOrderItemOutDetailID string  `json:"inOrderItemOutDetailId"` // 入库单项出库明细ID
	CostPrice              float64 `json:"costPrice"`              // 成本价 (可能是变化量)
	SalePrice              float64 `json:"salePrice"`              // 销售价
	PieceNum               int     `json:"pieceNum"`               // 转换比
}

// InOutStockDetailsData 进销存明细响应数据
type InOutStockDetailsData struct {
	DetailsList []InOutStockDetail `json:"detailsList"` // 明细列表
	Total       int                `json:"total"`       // 总数
}

// AbcYunInOutStockDetailsResponse 进销存明细响应
type AbcYunInOutStockDetailsResponse struct {
	AbcYunResponse
	Data InOutStockDetailsData `json:"data"` // 进销存明细数据
}

// --- Query Product Stock (5.3.15) --- //

// AbcYunQueryProductStockRequest 查询商品库存请求
type AbcYunQueryProductStockRequest struct {
	AbcYunRequest
	ProductIDs []string `json:"productIds"` // 商品ID列表
}

// PharmacyStock 药房库存
type PharmacyStock struct {
	PharmacyNo        int     `json:"pharmacyNo"`        // 药房编号
	PharmacyName      string  `json:"pharmacyName"`      // 药房名称
	StockPieceCount   float64 `json:"stockPieceCount"`   // 基本单位库存数量
	StockPackageCount float64 `json:"stockPackageCount"` // 包装单位库存数量
}

// ProductStock 商品库存信息
type ProductStock struct {
	ProductID         string          `json:"productId"`         // 商品ID
	ProductShortID    string          `json:"productShortId"`    // 商品助记码
	StockPieceCount   float64         `json:"stockPieceCount"`   // 总基本单位库存
	StockPackageCount float64         `json:"stockPackageCount"` // 总包装单位库存
	PharmacyStocks    []PharmacyStock `json:"pharmacyStocks"`    // 各药房库存
}

// QueryProductStockData 查询商品库存响应数据
type QueryProductStockData struct {
	GoodsStocks []ProductStock `json:"goodsStocks"` // 商品库存列表
}

// AbcYunQueryProductStockResponse 查询商品库存响应
type AbcYunQueryProductStockResponse struct {
	AbcYunResponse
	Data QueryProductStockData `json:"data"` // 商品库存数据
}

// --- Query Product Batches (5.3.16) --- //

// AbcYunQueryProductBatchesRequest 查询商品批次请求
type AbcYunQueryProductBatchesRequest struct {
	AbcYunRequest
	ProductIDs []string `json:"productIds"` // 商品ID列表
}

// ProductBatchInfo 商品批次信息
type ProductBatchInfo struct {
	BatchID            int     `json:"batchId"`            // 批次ID
	StockInOutDetailID string  `json:"stockInOutDetailId"` // 出入库明细ID
	BatchNo            string  `json:"batchNo"`            // 批号
	ProductionDate     string  `json:"productionDate"`     // 生产日期
	ExpiryDate         string  `json:"expiryDate"`         // 有效期
	StockPieceCount    float64 `json:"stockPieceCount"`    // 基本单位库存数量
	StockPackageCount  float64 `json:"stockPackageCount"`  // 包装单位库存数量
	PackageCostPrice   float64 `json:"packageCostPrice"`   // 包装成本价
	InDate             string  `json:"inDate"`             // 入库日期
}

// ProductWithBatches 带批次的商品信息
type ProductWithBatches struct {
	ProductID string             `json:"productId"` // 商品ID
	PieceNum  float64            `json:"pieceNum"`  // 转换比
	Batches   []ProductBatchInfo `json:"batches"`   // 批次列表 (仅本地0号药房)
}

// QueryProductBatchesData 查询商品批次响应数据
type QueryProductBatchesData struct {
	Products []ProductWithBatches `json:"products"` // 带批次的商品列表
}

// AbcYunQueryProductBatchesResponse 查询商品批次响应
type AbcYunQueryProductBatchesResponse struct {
	AbcYunResponse
	Data QueryProductBatchesData `json:"data"` // 商品批次数据
}

// --- Create Supplier (5.3.17) --- //

// AbcYunCreateSupplierRequest 新增供应商请求
type AbcYunCreateSupplierRequest struct {
	AbcYunRequest
	Name       string `json:"name"`       // 名称
	Status     int    `json:"status"`     // 状态 (文档示例为1)
	LicenseID  string `json:"licenseId"`  // 许可证ID
	Contact    string `json:"contact"`    // 联系人
	Mobile     string `json:"mobile"`     // 手机号
	Mark       string `json:"mark"`       // 标记
	OperatorID string `json:"operatorId"` // 操作员ID
}

// AbcYunCreateSupplierResponse 新增供应商响应
type AbcYunCreateSupplierResponse struct {
	AbcYunResponse
	Data SupplierInfo `json:"data"` // 供应商信息
}

// --- Update Supplier (5.3.18) --- //

// AbcYunUpdateSupplierRequest 修改供应商请求
type AbcYunUpdateSupplierRequest struct {
	AbcYunRequest
	ID         string `path:"id"`         // 供应商ID (from path)
	Name       string `json:"name"`       // 名称
	Status     int    `json:"status"`     // 状态 (文档示例为1)
	LicenseID  string `json:"licenseId"`  // 许可证ID
	Contact    string `json:"contact"`    // 联系人
	Mobile     string `json:"mobile"`     // 手机号
	Mark       string `json:"mark"`       // 标记
	OperatorID string `json:"operatorId"` // 操作员ID
}

// AbcYunUpdateSupplierResponse 修改供应商响应
type AbcYunUpdateSupplierResponse struct {
	AbcYunResponse
	Data SupplierInfo `json:"data"` // 供应商信息
}

// --- Create Product (5.3.19) --- //

// AbcYunCreateProductRequest 新增商品请求
type AbcYunCreateProductRequest struct {
	AbcYunRequest
	Type                 int     `json:"type"`                           // 类型
	SubType              int     `json:"subType"`                        // 子类型
	MedicineCadn         string  `json:"medicineCadn"`                   // 药品通用名 (CADN)
	Name                 string  `json:"name"`                           // 商品名称
	ShortID              string  `json:"shortId,omitempty"`              // 助记码
	CMSpec               string  `json:"cMSpec,omitempty"`               // 中药规格
	Dismounting          int     `json:"dismounting,omitempty"`          // 是否拆零
	ExtendSpec           string  `json:"extendSpec,omitempty"`           // 扩展规格
	MaterialSpec         string  `json:"materialSpec,omitempty"`         // 材料规格
	Manufacturer         string  `json:"manufacturer,omitempty"`         // 生产厂家
	MedicineNmpn         string  `json:"medicineNmpn,omitempty"`         // 批准文号 (NMPA)
	BarCode              string  `json:"barCode,omitempty"`              // 条形码
	PieceNum             float64 `json:"pieceNum"`                       // 转换比 (包/基)
	PieceUnit            string  `json:"pieceUnit"`                      // 基本单位
	PackageUnit          string  `json:"packageUnit"`                    // 包装单位
	ComponentContentUnit string  `json:"componentContentUnit,omitempty"` // 成分含量单位
	ComponentContentNum  float64 `json:"componentContentNum,omitempty"`  // 成分含量数量
	MedicineDosageNum    float64 `json:"medicineDosageNum,omitempty"`    // 药品剂量数量
	MedicineDosageUnit   string  `json:"medicineDosageUnit,omitempty"`   // 药品剂量单位
	PiecePrice           float64 `json:"piecePrice"`                     // 基本单位价格
	PackagePrice         float64 `json:"packagePrice"`                   // 包装单位价格
	OperatorID           string  `json:"operatorId"`                     // 操作员ID
}

// CreateProductResponseData 新增商品响应数据
type CreateProductResponseData struct {
	CMSpec         string  `json:"cmspec"`         // 中药规格
	ID             string  `json:"id"`             // 商品ID
	MedicineCadn   string  `json:"medicineCadn"`   // 药品通用名 (CADN)
	Name           string  `json:"name"`           // 商品名称
	Type           int     `json:"type"`           // 类型 (返回的是主类型?)
	SubType        int     `json:"subType"`        // 子类型
	TypeID         int     `json:"typeId"`         // 类型ID (与 Type/SubType 关系?)
	TypeName       string  `json:"typeName"`       // 类型名称
	CustomTypeID   string  `json:"customTypeId"`   // 自定义类型ID
	CustomTypeName string  `json:"customTypeName"` // 自定义类型名称
	Manufacturer   string  `json:"manufacturer"`   // 生产厂家
	MedicineNmpn   string  `json:"medicineNmpn"`   // 批准文号 (NMPA)
	BarCode        string  `json:"barCode"`        // 条形码
	ExtendSpec     string  `json:"extendSpec"`     // 扩展规格
	MaterialSpec   string  `json:"materialSpec"`   // 材料规格
	PieceUnit      string  `json:"pieceUnit"`      // 基本单位
	PieceNum       float64 `json:"pieceNum"`       // 转换比
	PiecePrice     float64 `json:"piecePrice"`     // 基本单位价格
	PackageUnit    string  `json:"packageUnit"`    // 包装单位
	PackagePrice   float64 `json:"packagePrice"`   // 包装单位价格
	ShortID        string  `json:"shortId"`        // 助记码
	DisplaySpec    string  `json:"displaySpec"`    // 显示规格
	DisplayName    string  `json:"displayName"`    // 显示名称
	Status         int     `json:"status"`         // 状态
}

// AbcYunCreateProductResponse 新增商品响应
type AbcYunCreateProductResponse struct {
	AbcYunResponse
	Data CreateProductResponseData `json:"data"` // 商品明细信息
}

// --- Update Product (5.3.20) --- //

// AbcYunUpdateProductRequest 修改商品请求
type AbcYunUpdateProductRequest struct {
	AbcYunCreateProductRequest        // Reuse create request fields for the body
	ID                         string `path:"id"` // 商品ID (from path)
}

// AbcYunUpdateProductResponse 修改商品响应
type AbcYunUpdateProductResponse struct {
	AbcYunResponse
	Data CreateProductResponseData `json:"data"` // 商品明细信息 (Same as create response data)
}

// --- Purchase Order List (5.3.21) --- //

// AbcYunPurchaseOrderListRequest 获取采购单列表请求
type AbcYunPurchaseOrderListRequest struct {
	AbcYunRequest
	BeginDate string `form:"beginDate"` // 开始日期 (格式yyyy-MM-dd)
	EndDate   string `form:"endDate"`   // 结束日期 (格式yyyy-MM-dd)
	// Note: limit/offset not mentioned, but likely exist based on other list APIs
	Limit  int `form:"limit,optional"`  // (推测) 每页数量
	Offset int `form:"offset,optional"` // (推测) 偏移量
}

// PurchaseOrderInfo 采购单信息 (用于列表)
type PurchaseOrderInfo struct {
	ID                int    `json:"id"`                // ID
	OrderNo           string `json:"orderNo"`           // 订单号
	Status            int    `json:"status"`            // 状态
	OrderType         int    `json:"orderType"`         // 订单类型
	PurchaseOrganName string `json:"purchaseOrganName"` // 采购机构名称
	PurchaseOrganID   string `json:"purchaseOrganId"`   // 采购机构ID
	ApplyEmployeeID   string `json:"applyEmployeeId"`   // 申请员工ID
	ApplyEmployeeName string `json:"applyEmployeeName"` // 申请员工姓名
	PurchaseOrderDate string `json:"purchaseOrderDate"` // 采购订单日期
	Created           string `json:"created"`           // 创建时间
}

// PurchaseOrderListData 采购单列表响应数据
type PurchaseOrderListData struct {
	PurchaseOrderList []PurchaseOrderInfo `json:"purchaseOrderList"` // 采购单列表
}

// AbcYunPurchaseOrderListResponse 采购单列表响应
type AbcYunPurchaseOrderListResponse struct {
	AbcYunResponse
	Data PurchaseOrderListData `json:"data"` // 采购单列表数据
}

// --- Purchase Order Detail (5.3.22) --- //

// AbcYunPurchaseOrderDetailRequest 获取采购单详情请求
type AbcYunPurchaseOrderDetailRequest struct {
	AbcYunRequest
	OrderID string `path:"orderId"` // 采购单ID
}

// PurchaseOrderItem 采购单项
type PurchaseOrderItem struct {
	ID             int     `json:"id"`             // ID
	ProductID      string  `json:"productId"`      // 商品ID
	ProductShortID string  `json:"productShortId"` // 商品助记码
	ProductName    string  `json:"productName"`    // 商品名称
	PieceCount     float64 `json:"pieceCount"`     // 基本单位数量
	PackageCount   string  `json:"packageCount"`   // 包装数量 (注意: 示例是字符串 "盒", 类型应为float64或string? 暂定string)
}

// PurchaseOrderDetail 采购单详情
type PurchaseOrderDetail struct {
	PurchaseOrderInfo                         // Embed basic list info
	Comment               Comment             `json:"comment"`               // 备注
	PurchaseOrderItemList []PurchaseOrderItem `json:"purchaseOrderItemList"` // 采购订单项列表
}

// AbcYunPurchaseOrderDetailResponse 采购单详情响应
type AbcYunPurchaseOrderDetailResponse struct {
	AbcYunResponse
	Data PurchaseOrderDetail `json:"data"` // 采购单详情数据
}

// --- Review Purchase Order (5.3.23) --- //

// AbcYunReviewPurchaseOrderRequest 审核采购单请求
type AbcYunReviewPurchaseOrderRequest struct {
	AbcYunRequest
	PurchaseOrderID string `path:"purchaseOrderId"` // 采购单ID (from path)
	Pass            int    `json:"pass"`            // 是否通过 (1: 通过, 0: 不通过?)
	Comment         string `json:"comment"`         // 审核意见
}

// AbcYunReviewPurchaseOrderResponse 审核采购单响应
type AbcYunReviewPurchaseOrderResponse struct {
	AbcYunResponse
	Data OperationResult `json:"data"` // 操作结果
}

// --- Review Fixed In Order (5.3.24) --- //

// AbcYunReviewFixedInOrderRequest 审核修正入库单请求
type AbcYunReviewFixedInOrderRequest struct {
	AbcYunRequest
	FixInOrderID string `path:"fixInOrderId"` // 修正入库单ID (from path)
	Pass         int    `json:"pass"`         // 是否通过 (1: 通过, 0: 不通过?)
	Comment      string `json:"comment"`      // 审核意见
}

// AbcYunReviewFixedInOrderResponse 审核修正入库单响应
type AbcYunReviewFixedInOrderResponse struct {
	AbcYunResponse
	Data OperationResult `json:"data"` // 操作结果
}

// --- Create In Order (5.3.25) --- //

// CreateInOrderItem 创建入库单项请求体
type CreateInOrderItem struct {
	GoodsID        string  `json:"goodsId"`        // 商品ID
	GoodsShortID   string  `json:"goodsShortId"`   // 商品助记码
	Unit           string  `json:"unit"`           // 单位 (包装单位?)
	UnitCount      float64 `json:"unitCount"`      // 单位数量 (包装数量?)
	UnitCostPrice  float64 `json:"unitCostPrice"`  // 单位成本价 (包装成本价?)
	TotalCostPrice float64 `json:"totalCostPrice"` // 总成本价
	BatchNo        string  `json:"batchNo"`        // 批号
	ExpireDate     string  `json:"expireDate"`     // 过期日期 (格式? yyyy-MM-dd?)
	ProductionDate string  `json:"productionDate"` // 生产日期 (格式? yyyy-MM-dd?)
	OutDetailID    string  `json:"outDetailId"`    // (退货入库时) 出库明细ID
}

// AbcYunCreateInOrderRequest 创建入库单请求
type AbcYunCreateInOrderRequest struct {
	AbcYunRequest
	SupplierID      string              `json:"supplierId"`      // 供应商ID
	OutOrderNo      string              `json:"outOrderNo"`      // (退货入库时) 外部订单号?
	PharmacyNo      int                 `json:"pharmacyNo"`      // 药房编号 (默认0?)
	ToClinicID      string              `json:"toClinicId"`      // 目标诊所ID (调拨入库时?)
	Comment         string              `json:"comment"`         // 备注
	InOrderItemList []CreateInOrderItem `json:"inOrderItemList"` // 入库项目列表
}

// AbcYunCreateInOrderResponse 创建入库单响应 (复用入库单详情结构)
type AbcYunCreateInOrderResponse struct {
	AbcYunResponse
	Data StockInOrderDetail `json:"data"` // 入库单详情数据
}

// --- Create Out Order (Return) (5.3.26) --- //

// CreateOutOrderItem 创建出库单项请求体 (退货)
type CreateOutOrderItem struct {
	GoodsID      string  `json:"goodsId"`      // 商品ID
	GoodsShortID string  `json:"goodsShortId"` // 商品助记码
	PieceCount   float64 `json:"pieceCount"`   // 基本单位数量
	PackageCount float64 `json:"packageCount"` // 包装数量
	BatchNo      string  `json:"batchNo"`      // 批号 (必填，指定退货批次)
}

// AbcYunCreateOutOrderRequest 创建出库单请求 (退货)
type AbcYunCreateOutOrderRequest struct {
	AbcYunRequest
	InOrderID        int                  `json:"inOrderId"`        // 关联的入库单ID
	PharmacyNo       int                  `json:"pharmacyNo"`       // 药房编号
	ToClinicID       string               `json:"toClinicId"`       // 目标诊所ID (退给谁?)
	Comment          string               `json:"comment"`          // 备注
	OutOrderItemList []CreateOutOrderItem `json:"outOrderItemList"` // 出库项目列表
}

// AbcYunCreateOutOrderResponse 创建出库单响应 (文档示例是入库单详情? 确认一下)
// Assuming it returns the created Out Order Detail
type AbcYunCreateOutOrderResponse struct {
	AbcYunResponse
	Data StockOutOrderDetail `json:"data"` // 出库单详情数据
}

// --- Review Out Apply Order (5.3.27) --- //

// AbcYunReviewOutApplyOrderRequest 审核退货出库申请单请求
type AbcYunReviewOutApplyOrderRequest struct {
	AbcYunRequest
	OutApplyOrderID int64  `path:"outApplyOrderId"` // 退货出库申请单ID (int64)
	Pass            int    `json:"pass"`            // 是否通过
	Comment         string `json:"comment"`         // 备注
}

// AbcYunReviewOutApplyOrderResponse 审核退货出库申请单响应
type AbcYunReviewOutApplyOrderResponse struct {
	AbcYunResponse
	Data OperationResult `json:"data"` // 操作结果
}

// --- Query Store Batches (5.3.28) --- //

// AbcYunQueryStoreBatchesRequest 查询门店库存(批次)请求
type AbcYunQueryStoreBatchesRequest struct {
	AbcYunRequest
	Limit  int `form:"limit,optional"`  // 每页数量 (默认20)
	Offset int `form:"offset,optional"` // 偏移量 (默认0)
}

// StoreBatchStockView 门店批次库存视图
type StoreBatchStockView struct {
	GoodsID            string  `json:"goodsId"`            // 商品ID
	GoodsShortID       string  `json:"goodsShortId"`       // 商品助记码
	BatchID            int     `json:"batchId"`            // 批次ID
	PieceNum           int     `json:"pieceNum"`           // 转换比
	PharmacyType       int     `json:"pharmacyType"`       // 药房类型
	PharmacyNo         int     `json:"pharmacyNo"`         // 药房编号
	ExpiryDate         string  `json:"expiryDate"`         // 过期日期
	ProductionDate     string  `json:"productionDate"`     // 生产日期
	InDate             string  `json:"inDate"`             // 入库日期
	BatchNo            string  `json:"batchNo"`            // 批号
	PackageCostPrice   float64 `json:"packageCostPrice"`   // 包装成本价
	LeftCost           float64 `json:"leftCost"`           // 剩余成本?
	PieceCount         float64 `json:"pieceCount"`         // 基本单位数量
	PackageCount       float64 `json:"packageCount"`       // 包装数量
	Status             int     `json:"status"`             // 状态
	StockInOutDetailID string  `json:"stockInOutDetailId"` // 出入库明细ID
}

// StoreBatchesPage Abc分页结构 for 批次库存
type StoreBatchesPage struct {
	Rows    []StoreBatchStockView `json:"rows"`    // 数据行
	Total   int                   `json:"total"`   // 总数
	Offset  int                   `json:"offset"`  // 偏移量
	Limit   int                   `json:"limit"`   // 限制数
	Keyword string                `json:"keyword"` // 关键字 (似乎在此API中未使用)
}

// AbcYunQueryStoreBatchesResponse 查询门店库存(批次)响应
type AbcYunQueryStoreBatchesResponse struct {
	AbcYunResponse
	Data StoreBatchesPage `json:"data"` // 分页批次库存数据
}

// --- Trans Order List (5.3.29) --- //

// AbcYunTransOrderListRequest 获取调拨单列表请求
type AbcYunTransOrderListRequest struct {
	AbcYunRequest
	BeginDate     string `form:"beginDate"`              // 开始时间 (格式yyyy-MM-dd)
	EndDate       string `form:"endDate"`                // 结束时间 (格式yyyy-MM-dd)
	Limit         int    `form:"limit"`                  // 每页数量
	Offset        int    `form:"offset"`                 // 偏移量
	DateFieldType int    `form:"dateFieldType,optional"` // 查询日期类型: 0-创建时间(默认) 1-调拨入库时间
	TransType     int    `form:"transType,optional"`     // 调拨类型: 0-店间调拨(默认) 1-店内调拨
}

// SimpleUser 简单用户信息
type SimpleUser struct {
	ID   string `json:"id"`   // 用户ID
	Name string `json:"name"` // 用户名称
}

// SimpleOrgan 简单机构信息
type SimpleOrgan struct {
	ID   string `json:"id"`   // 机构ID
	Name string `json:"name"` // 机构名称
}

// TransOrderListView 调拨单列表视图
type TransOrderListView struct {
	ID                 int          `json:"id"`                 // ID
	Amount             float64      `json:"amount"`             // 总金额
	Count              float64      `json:"count"`              // 总数量
	KindCount          int          `json:"kindCount"`          // 品种数
	OrderNo            string       `json:"orderNo"`            // 订单号
	TransType          int          `json:"transType"`          // 调拨类型
	TransCostPriceType int          `json:"transCostPriceType"` // 调拨成本价类型
	CreatedUser        SimpleUser   `json:"createdUser"`        // 创建用户
	Created            string       `json:"created"`            // 创建时间
	ReviewDate         string       `json:"reviewDate"`         // 审核日期
	ReviewUser         SimpleUser   `json:"reviewUser"`         // 审核用户
	InConfirmDate      string       `json:"inConfirmDate"`      // 入库确认日期
	InConfirmUser      SimpleUser   `json:"inConfirmUser"`      // 入库确认用户
	ToOrgan            SimpleOrgan  `json:"toOrgan"`            // 调入机构
	InPharmacy         PharmacyInfo `json:"inPharmacy"`         // 调入药房
	FromOrgan          SimpleOrgan  `json:"fromOrgan"`          // 调出机构
	OutPharmacy        PharmacyInfo `json:"outPharmacy"`        // 调出药房
	LastModified       string       `json:"lastModified"`       // 最后修改时间
	LastModifiedUser   SimpleUser   `json:"lastModifiedUser"`   // 最后修改用户
	Status             int          `json:"status"`             // 状态 (1: 待审核?)
}

// TransOrderListPage Abc分页结构 for 调拨单列表
type TransOrderListPage struct {
	Rows   []TransOrderListView `json:"rows"`   // 数据行
	Total  int                  `json:"total"`  // 总数
	Offset int                  `json:"offset"` // 偏移量
	Limit  int                  `json:"limit"`  // 限制数
}

// AbcYunTransOrderListResponse 获取调拨单列表响应
type AbcYunTransOrderListResponse struct {
	AbcYunResponse
	Data TransOrderListPage `json:"data"` // 分页调拨单数据
}

// --- Trans Order Detail (5.3.30) --- //

// AbcYunTransOrderDetailRequest 获取调拨单详情请求
type AbcYunTransOrderDetailRequest struct {
	AbcYunRequest
	OrderID int64 `path:"orderId"` // 调拨单ID (int64)
}

// TransInBatch 调拨入库批次信息 (复用 StockOutBatch? 字段可能不同)
type TransInBatch struct {
	StockID            int      `json:"stockId"`            // 库存ID
	StockInOutDetailID string   `json:"stockInOutDetailId"` // 出入库明细ID
	BatchID            int      `json:"batchId"`            // 批次ID
	Supplier           Supplier `json:"supplier"`           // 供应商
	BatchNo            string   `json:"batchNo"`            // 批号
	ProductionDate     string   `json:"productionDate"`     // 生产日期
	ExpiryDate         string   `json:"expiryDate"`         // 有效期
	PackageCount       float64  `json:"packageCount"`       // 包装数量
	PieceCount         float64  `json:"pieceCount"`         // 基本单位数量
	PackageCostPrice   float64  `json:"packageCostPrice"`   // 包装成本价
	PieceNum           int      `json:"pieceNum"`           // 转换比
	CostPrice          float64  `json:"costPrice"`          // 基本单位成本价
}

// TransOutBatch 调拨出库批次信息
type TransOutBatch struct {
	StockID             int     `json:"stockId"`             // 库存ID
	BatchNo             string  `json:"batchNo"`             // 批号
	ExpiryDate          string  `json:"expiryDate"`          // 过期日期
	PackageCount        float64 `json:"packageCount"`        // 包装数量
	PieceCount          float64 `json:"pieceCount"`          // 基本单位数量
	OutPackageCostPrice float64 `json:"outPackageCostPrice"` // 出库包装成本价
}

// TransOrderItem 调拨单项
type TransOrderItem struct {
	ApplicationPackageCount float64           `json:"applicationPackageCount"` // 申请包装数量
	ApplicationPieceCount   float64           `json:"applicationPieceCount"`   // 申请基本单位数量
	PackageCount            float64           `json:"packageCount"`            // 实际包装数量
	PieceCount              float64           `json:"pieceCount"`              // 实际基本单位数量
	Goods                   ProductDetailInfo `json:"goods"`                   // 商品详情
	OutTotalCostPrice       float64           `json:"outTotalCostPrice"`       // 出库总成本价
	OutPackageCostPrice     float64           `json:"outPackageCostPrice"`     // 出库包装成本价
	Batches                 []TransOutBatch   `json:"bathes"`                  // 出库批次 (注意 JSON key 是 'bathes')
	TransInBatches          []TransInBatch    `json:"transInBatches"`          // 入库批次
	LastModified            string            `json:"lastModified"`            // 最后修改时间
}

// TransOrderDetail 调拨单详情
type TransOrderDetail struct {
	TransOrderListView                  // Embed basic list info
	Items              []TransOrderItem `json:"items"` // 调拨项目列表
}

// AbcYunTransOrderDetailResponse 获取调拨单详情响应
type AbcYunTransOrderDetailResponse struct {
	AbcYunResponse
	Data TransOrderDetail `json:"data"` // 调拨单详情数据
}
