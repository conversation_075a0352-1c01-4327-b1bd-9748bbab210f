package utils

import (
	"errors"
	"net/http"
	"strconv"
	"strings"
)

// GetUserIDFromRequest 从请求中获取用户ID (已废弃，请使用 wx_internal/utils.GetUserIDFromRequest)
// Deprecated: 请使用 wx_internal/utils.GetUserIDFromRequest 方法
func GetUserIDFromRequest(r *http.Request) (uint, error) {
	return 0, errors.New("此方法已废弃，请使用 wx_internal/utils.GetUserIDFromRequest")
}

// GetLoginUserID 从请求中获取用户ID (int64格式)
func GetLoginUserID(r *http.Request) (int64, error) {
	// 从上下文中获取用户ID
	userID, ok := r.Context().Value("user_id").(int64)
	if !ok {
		// 尝试从Header中获取
		userIDStr := r.Header.Get("X-User-ID")
		if userIDStr == "" {
			return 0, errors.New("未找到用户ID")
		}

		// 转换为int64
		id, err := strconv.ParseInt(userIDStr, 10, 64)
		if err != nil {
			return 0, errors.New("无效的用户ID")
		}

		userID = id
	}

	return userID, nil
}

// GetClientIP 获取客户端IP地址
func GetClientIP(r *http.Request) string {
	// 尝试从X-Forwarded-For获取（支持代理链）
	xForwardedFor := r.Header.Get("X-Forwarded-For")
	if xForwardedFor != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if ip != "" {
				return ip
			}
		}
	}

	// 尝试从X-Real-IP获取
	xRealIP := r.Header.Get("X-Real-IP")
	if xRealIP != "" {
		return strings.TrimSpace(xRealIP)
	}

	// 尝试从X-Forwarded-For获取（Cloudflare等）
	cfConnectingIP := r.Header.Get("CF-Connecting-IP")
	if cfConnectingIP != "" {
		return strings.TrimSpace(cfConnectingIP)
	}

	// 尝试从X-Original-Forwarded-For获取
	xOriginalForwardedFor := r.Header.Get("X-Original-Forwarded-For")
	if xOriginalForwardedFor != "" {
		return strings.TrimSpace(xOriginalForwardedFor)
	}

	// 获取远程地址并处理端口
	remoteAddr := r.RemoteAddr
	if remoteAddr != "" {
		// 移除端口号
		if idx := strings.LastIndex(remoteAddr, ":"); idx != -1 {
			ip := remoteAddr[:idx]
			// 移除IPv6的方括号
			ip = strings.Trim(ip, "[]")
			if ip != "" {
				return ip
			}
		}
	}

	return "unknown"
}

// GetUserAgent 获取用户代理
func GetUserAgent(r *http.Request) string {
	return r.Header.Get("User-Agent")
}

// GetReferer 获取来源页面
func GetReferer(r *http.Request) string {
	return r.Header.Get("Referer")
}

// GenerateUUID 生成UUID
func GenerateUUID() string {
	// 这里使用简单的时间戳+随机数来模拟UUID
	// 实际项目中应该使用专业的UUID库
	return strconv.FormatInt(GetCurrentTime().UnixNano(), 10)
}
