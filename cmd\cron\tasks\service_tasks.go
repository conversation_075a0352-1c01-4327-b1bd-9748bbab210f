package tasks

import (
	"log"
	"time"

	"yekaitai/internal/modules/service_setup/model"
	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// ServiceTaskManager 服务模块定时任务管理器
type ServiceTaskManager struct{}

// NewServiceTaskManager 创建服务任务管理器
func NewServiceTaskManager() *ServiceTaskManager {
	return &ServiceTaskManager{}
}

// ProcessExpiredOrders 处理过期订单
func (t *ServiceTaskManager) ProcessExpiredOrders() {
	log.Println("开始处理过期服务订单...")

	now := time.Now()
	
	// 查询过期未支付的订单
	var expiredOrders []model.ServiceOrder
	err := mysql.Master().Table("service_orders").
		Where("pay_status = 'unpaid' AND expire_time < ? AND status != 'cancelled'", now).
		Find(&expiredOrders).Error
	
	if err != nil {
		log.Printf("查询过期订单失败: %v", err)
		return
	}

	if len(expiredOrders) == 0 {
		log.Println("没有过期订单需要处理")
		return
	}

	// 批量更新过期订单状态
	orderIDs := make([]uint, 0, len(expiredOrders))
	for _, order := range expiredOrders {
		orderIDs = append(orderIDs, order.ID)
	}

	err = mysql.Master().Table("service_orders").
		Where("id IN ?", orderIDs).
		Updates(map[string]interface{}{
			"status":     "cancelled",
			"updated_at": now,
		}).Error

	if err != nil {
		log.Printf("更新过期订单状态失败: %v", err)
		return
	}

	// 退还优惠券和积分
	for _, order := range expiredOrders {
		t.refundCouponAndPoints(order)
	}

	log.Printf("成功处理 %d 个过期订单", len(expiredOrders))
}

// ProcessExpiredAppointments 处理过期预约
func (t *ServiceTaskManager) ProcessExpiredAppointments() {
	log.Println("开始处理过期服务预约...")

	now := time.Now()
	
	// 查询过期未核销的预约（预约时间已过）
	var expiredAppointments []model.ServiceAppointment
	err := mysql.Master().Table("service_appointments").
		Where("status = 'booked' AND appointment_date < ?", now.Format("2006-01-02")).
		Find(&expiredAppointments).Error
	
	if err != nil {
		log.Printf("查询过期预约失败: %v", err)
		return
	}

	if len(expiredAppointments) == 0 {
		log.Println("没有过期预约需要处理")
		return
	}

	// 开始事务处理
	tx := mysql.Master().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, appointment := range expiredAppointments {
		// 更新预约状态为过期
		err = tx.Table("service_appointments").
			Where("id = ?", appointment.ID).
			Updates(map[string]interface{}{
				"status":     "expired",
				"updated_at": now,
			}).Error

		if err != nil {
			log.Printf("更新预约状态失败: %v", err)
			continue
		}

		// 扣减订单剩余次数
		err = tx.Table("service_orders").
			Where("id = ? AND remaining_times > 0", appointment.OrderID).
			Updates(map[string]interface{}{
				"remaining_times": gorm.Expr("remaining_times - 1"),
				"used_times":      gorm.Expr("used_times + 1"),
				"updated_at":      now,
			}).Error

		if err != nil {
			log.Printf("更新订单次数失败: %v", err)
			continue
		}

		log.Printf("处理过期预约: appointment_id=%d, order_id=%d", appointment.ID, appointment.OrderID)
	}

	tx.Commit()
	log.Printf("成功处理 %d 个过期预约", len(expiredAppointments))
}

// ProcessAutoRefund 处理自动退款
func (t *ServiceTaskManager) ProcessAutoRefund() {
	log.Println("开始处理服务自动退款...")

	now := time.Now()
	
	// 查询支持自动退款且已过有效期的订单
	var refundOrders []struct {
		model.ServiceOrder
		ServicePackage model.ServicePackage `gorm:"foreignKey:ServicePackageID"`
	}

	err := mysql.Master().Table("service_orders").
		Select("service_orders.*, service_packages.*").
		Joins("LEFT JOIN service_packages ON service_orders.service_package_id = service_packages.id").
		Where("service_orders.pay_status = 'paid'").
		Where("service_orders.remaining_times > 0").
		Where("service_orders.status != 'refunded'").
		Where("service_packages.support_auto_refund = 1").
		Where("service_orders.validity_end < ?", now).
		Find(&refundOrders).Error
	
	if err != nil {
		log.Printf("查询自动退款订单失败: %v", err)
		return
	}

	if len(refundOrders) == 0 {
		log.Println("没有需要自动退款的订单")
		return
	}

	for _, orderData := range refundOrders {
		order := orderData.ServiceOrder
		
		// 计算退款金额（按剩余次数比例）
		totalTimes := orderData.ServicePackage.Times * order.Quantity
		refundRatio := float64(order.RemainingTimes) / float64(totalTimes)
		refundAmount := order.PayAmount * refundRatio

		// 开始事务
		tx := mysql.Master().Begin()

		// 更新订单状态
		err = tx.Table("service_orders").
			Where("id = ?", order.ID).
			Updates(map[string]interface{}{
				"status":        "refunded",
				"refund_amount": refundAmount,
				"refund_time":   now,
				"refund_reason": "过期自动退款",
				"updated_at":    now,
			}).Error

		if err != nil {
			tx.Rollback()
			log.Printf("更新订单退款状态失败: order_id=%d, error=%v", order.ID, err)
			continue
		}

		// 调用退款接口
		success := t.processRefund(order.OrderNo, refundAmount)
		if !success {
			tx.Rollback()
			log.Printf("退款处理失败: order_id=%d", order.ID)
			continue
		}

		tx.Commit()
		log.Printf("自动退款成功: order_id=%d, amount=%.2f", order.ID, refundAmount)
	}

	log.Printf("自动退款处理完成")
}

// refundCouponAndPoints 退还优惠券和积分
func (t *ServiceTaskManager) refundCouponAndPoints(order model.ServiceOrder) {
	// 退还优惠券
	if order.CouponID != nil {
		log.Printf("退还优惠券: order_id=%d, coupon_id=%d", order.ID, *order.CouponID)
		// 恢复优惠券状态
		mysql.Master().Table("user_coupons").
			Where("id = ?", *order.CouponID).
			Updates(map[string]interface{}{
				"status":     "unused",
				"used_time":  nil,
				"order_no":   "",
				"updated_at": time.Now(),
			})
	}

	// 退还积分
	if order.UsedPoints > 0 {
		log.Printf("退还积分: order_id=%d, points=%d", order.ID, order.UsedPoints)
		// 恢复用户积分
		mysql.Master().Table("user_points").
			Where("user_id = ?", order.UserID).
			UpdateColumn("points", gorm.Expr("points + ?", order.UsedPoints))
		
		// 记录积分变动
		mysql.Master().Table("user_point_logs").Create(map[string]interface{}{
			"user_id":     order.UserID,
			"type":        "refund",
			"points":      order.UsedPoints,
			"description": "服务订单退款返还积分",
			"order_no":    order.OrderNo,
			"created_at":  time.Now(),
		})
	}
}

// processRefund 处理退款
func (t *ServiceTaskManager) processRefund(orderNo string, amount float64) bool {
	// 调用微信退款接口
	log.Printf("处理退款: order_no=%s, amount=%.2f", orderNo, amount)
	
	// TODO: 实际调用退款接口
	// 这里需要集成实际的退款接口
	
	return true
}

// RunAllTasks 运行所有定时任务
func (t *ServiceTaskManager) RunAllTasks() {
	log.Println("开始执行服务模块定时任务...")
	
	t.ProcessExpiredOrders()
	t.ProcessExpiredAppointments()
	t.ProcessAutoRefund()
	
	log.Println("服务模块定时任务执行完成")
}

// 手动执行任务的函数，可以在 main.go 中调用
func RunServiceTasks() {
	taskManager := NewServiceTaskManager()
	taskManager.RunAllTasks()
}

func RunExpiredOrdersTask() {
	taskManager := NewServiceTaskManager()
	taskManager.ProcessExpiredOrders()
}

func RunExpiredAppointmentsTask() {
	taskManager := NewServiceTaskManager()
	taskManager.ProcessExpiredAppointments()
}

func RunAutoRefundTask() {
	taskManager := NewServiceTaskManager()
	taskManager.ProcessAutoRefund()
}
