package utils

import (
	"fmt"

	"golang.org/x/crypto/bcrypt"
)

const (
	// DefaultCost 默认的bcrypt成本因子
	DefaultCost = bcrypt.DefaultCost
	// MinCost 最小的bcrypt成本因子，用于测试
	MinCost = bcrypt.MinCost
	// MaxCost 最大的bcrypt成本因子，CPU消耗较大
	MaxCost = bcrypt.MaxCost
)

// HashPassword 使用bcrypt对密码进行哈希加密
// 该函数自动生成随机盐并将其存储在哈希中
// cost参数可以控制哈希的复杂度，通常使用DefaultCost
func HashPassword(password string, cost ...int) (string, error) {
	actualCost := DefaultCost
	if len(cost) > 0 && cost[0] > 0 {
		actualCost = cost[0]
	}

	// 生成密码哈希
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), actualCost)
	if err != nil {
		return "", err
	}

	return string(bytes), nil
}

// VerifyPassword 验证密码是否匹配存储的哈希值
// 如果密码匹配，返回true；否则返回false
func VerifyPassword(hashedPassword, password string) bool {
	// 比较密码
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))

	// 添加详细的错误记录
	if err != nil {
		// 这里不使用logx包，因为这是一个工具包，应该保持独立性
		// 使用简单的fmt打印以便调试
		fmt.Printf("密码验证失败: %v, 哈希密码长度: %d, 明文密码长度: %d\n",
			err, len(hashedPassword), len(password))
		return false
	}

	return true
}

// NeedsRehash 检查密码哈希是否需要使用新的成本参数重新哈希
// 当计算能力变强时，可能需要增加成本因子
func NeedsRehash(hashedPassword string, cost ...int) bool {
	actualCost := DefaultCost
	if len(cost) > 0 && cost[0] > 0 {
		actualCost = cost[0]
	}

	// 获取当前哈希的成本
	hash, err := bcrypt.Cost([]byte(hashedPassword))
	if err != nil {
		return true // 出错时建议重新哈希
	}

	// 如果当前成本小于目标成本，则需要重新哈希
	return hash < actualCost
}
