package bootstrap

import (
	"fmt"
	"yekaitai/internal/modules/coupon/model"
	"yekaitai/pkg/infra/mysql"

	"github.com/zeromicro/go-zero/core/logx"
)

// MigrateCouponVisibleUserLevels 创建优惠券可见用户等级中间表
func MigrateCouponVisibleUserLevels() error {
	db := mysql.GetDB()

	logx.Info("开始执行优惠券可见用户等级表结构迁移...")

	// 自动迁移优惠券可见用户等级中间表
	if err := db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券可见用户等级中间表'").
		AutoMigrate(&model.CouponVisibleUserLevel{}); err != nil {
		logx.Errorf("创建优惠券可见用户等级表失败: %v", err)
		return fmt.Errorf("创建优惠券可见用户等级表失败: %w", err)
	}

	// 修改优惠券表的user_level字段注释
	if err := db.Exec("ALTER TABLE `coupons` MODIFY COLUMN `user_level` int NOT NULL DEFAULT 1 COMMENT '用户可见范围:1-全部,2-部分等级'").Error; err != nil {
		logx.Errorf("修改coupons表user_level字段注释失败: %v", err)
		return fmt.Errorf("修改字段注释失败: %w", err)
	}

	logx.Info("优惠券可见用户等级表创建成功")
	return nil
}
