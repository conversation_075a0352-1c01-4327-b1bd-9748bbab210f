package model

import (
	"database/sql"
	"time"

	"yekaitai/pkg/infra/mysql"

	"gorm.io/gorm"
)

// Store 门店模型
type Store struct {
	ID          uint         `json:"id" gorm:"primaryKey;autoIncrement;comment:门店ID"`
	Name        string       `json:"name" gorm:"type:varchar(100);not null;comment:门店名称"`
	Phone       string       `json:"phone" gorm:"type:varchar(20);comment:联系电话"`
	ProvinceID  string       `json:"province_id" gorm:"type:varchar(20);comment:省份编码"`
	CityID      string       `json:"city_id" gorm:"type:varchar(20);comment:城市编码"`
	AreaID      string       `json:"area_id" gorm:"type:varchar(20);comment:区县编码"`
	Address     string       `json:"address" gorm:"type:varchar(255);comment:详细地址"`
	Latitude    float64      `json:"latitude" gorm:"type:decimal(10,7);comment:纬度;index:idx_location,priority:1"`
	Longitude   float64      `json:"longitude" gorm:"type:decimal(11,7);comment:经度;index:idx_location,priority:2;index:idx_spatial_location,type:spatial"`
	Description string       `json:"description" gorm:"type:text;comment:门店介绍"`
	Images      string       `json:"images" gorm:"type:text;comment:门店图片,JSON格式"`
	ManagerID   uint         `json:"manager_id" gorm:"comment:门店管理员ID"`
	CreatorID   uint         `json:"creator_id" gorm:"comment:创建人ID"`
	WsjgID      string       `json:"wsjg_id" gorm:"type:varchar(50);comment:卫生机构ID"`
	ParentID    string       `json:"parent_id" gorm:"type:varchar(50);comment:连锁总部ID"`
	Status      int          `json:"status" gorm:"default:1;not null;comment:状态：1-正常，0-禁用"`
	CreatedAt   time.Time    `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt   time.Time    `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt   sql.NullTime `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 返回表名
func (s *Store) TableName() string {
	return "t_stores"
}

// StoreRepository 门店仓库接口
type StoreRepository interface {
	// 创建门店
	Create(store *Store) error
	// 更新门店
	Update(store *Store) error
	// 根据ID查找门店
	FindByID(id uint) (*Store, error)
	// 根据名称查找门店
	FindByName(name string) (*Store, error)
	// 获取门店列表
	List(page, size int, query string) ([]*Store, int64, error)
	// 删除门店
	Delete(id uint) error
	// 更新门店状态
	UpdateStatus(id uint, status int) error
	// 获取附近的门店
	ListNearby(latitude, longitude float64, maxDistance float64, page, size int) ([]*Store, int64, error)
}

// storeRepository 实现了StoreRepository接口
type storeRepository struct {
	db *gorm.DB
}

// NewStoreRepository 创建门店仓库实例
func NewStoreRepository(db *gorm.DB) StoreRepository {
	if db == nil {
		db = mysql.Slave()
	}
	return &storeRepository{db: db}
}

// Create 创建门店
func (r *storeRepository) Create(store *Store) error {
	return r.db.Create(store).Error
}

// Update 更新门店
func (r *storeRepository) Update(store *Store) error {
	return r.db.Save(store).Error
}

// FindByID 根据ID查找门店
func (r *storeRepository) FindByID(id uint) (*Store, error) {
	var store Store
	err := r.db.Where("id = ? AND deleted_at IS NULL", id).First(&store).Error
	if err != nil {
		return nil, err
	}
	return &store, nil
}

// FindByName 根据名称查找门店
func (r *storeRepository) FindByName(name string) (*Store, error) {
	var store Store
	err := r.db.Where("name = ? AND deleted_at IS NULL", name).First(&store).Error
	if err != nil {
		return nil, err
	}
	return &store, nil
}

// List 获取门店列表
func (r *storeRepository) List(page, size int, query string) ([]*Store, int64, error) {
	var stores []*Store
	var total int64

	db := r.db.Model(&Store{}).Where("deleted_at IS NULL")

	if query != "" {
		db = db.Where("name LIKE ? OR address LIKE ?", "%"+query+"%", "%"+query+"%")
	}

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if page > 0 && size > 0 {
		offset := (page - 1) * size
		db = db.Offset(offset).Limit(size)
	}

	err = db.Order("id DESC").Find(&stores).Error
	return stores, total, err
}

// Delete 删除门店（逻辑删除）
func (r *storeRepository) Delete(id uint) error {
	return r.db.Model(&Store{}).Where("id = ?", id).
		Update("deleted_at", time.Now()).Error
}

// UpdateStatus 更新门店状态
func (r *storeRepository) UpdateStatus(id uint, status int) error {
	return r.db.Model(&Store{}).Where("id = ? AND deleted_at IS NULL", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error
}

// ListNearby 获取附近的门店
func (r *storeRepository) ListNearby(latitude, longitude float64, maxDistance float64, page, size int) ([]*Store, int64, error) {
	var stores []*Store
	var total int64

	// 使用 MySQL 的 ST_Distance_Sphere 函数计算距离
	distanceQuery := "ST_Distance_Sphere(POINT(longitude, latitude), POINT(?, ?))"

	db := r.db.Model(&Store{}).
		Where("deleted_at IS NULL").
		Where(distanceQuery+" <= ?", longitude, latitude, maxDistance).
		Select("*, "+distanceQuery+" as distance", longitude, latitude)

	// 获取总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page > 0 && size > 0 {
		offset := (page - 1) * size
		db = db.Offset(offset).Limit(size)
	}

	// 按距离排序
	err = db.Order("distance ASC").Find(&stores).Error
	return stores, total, err
}
