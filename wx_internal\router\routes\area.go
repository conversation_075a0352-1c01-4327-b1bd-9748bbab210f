package routes

import (
	"net/http"
	"yekaitai/wx_internal/middleware"
	"yekaitai/wx_internal/modules/area/handler"
	"yekaitai/wx_internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
)

// RegisterAreaRoutes 注册区域相关路由
func RegisterAreaRoutes(server interface {
	AddRoutes(rs []rest.Route, opts ...rest.RouteOption)
	AddRoute(r rest.Route, opts ...rest.RouteOption)
	Use(middleware rest.Middleware)
}, serverCtx *svc.WxServiceContext) {
	logx.Info("开始注册区域相关路由")

	// 使用小程序认证中间件
	wxAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return http.HandlerFunc(middleware.WxAuthMiddleware(serverCtx)(next).ServeHTTP)
	}

	// 从serviceContext中获取DB实例
	db := serverCtx.DB
	if db == nil {
		logx.Info("serviceContext中DB为空，将在AreaHandler中自动初始化连接")
	} else {
		logx.Info("使用serviceContext中的DB实例初始化AreaHandler")
	}

	areaHandler := handler.NewAreaHandler(db)

	// 获取已开通的所有省份
	server.AddRoute(
		rest.Route{
			Method:  "GET",
			Path:    "/api/wx/areas/enabled/provinces",
			Handler: wxAuthWrapper(areaHandler.GetEnabledProvinces),
		},
	)

	//获取指定省份下已开通的所有城市
	server.AddRoute(
		rest.Route{
			Method:  "GET",
			Path:    "/api/wx/areas/enabled/cities",
			Handler: wxAuthWrapper(areaHandler.GetEnabledCities),
		},
	)

	//获取指定城市下已开通的所有区县
	server.AddRoute(
		rest.Route{
			Method:  "GET",
			Path:    "/api/wx/areas/enabled/districts",
			Handler: wxAuthWrapper(areaHandler.GetEnabledAreas),
		},
	)

	logx.Info("区域相关路由注册完成")
}
