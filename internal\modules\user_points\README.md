# 叶小币（用户积分）配置管理

## 功能概述

叶小币是系统的用户积分体系，支持多种获取方式和使用场景。本模块提供了完整的后台配置管理功能。

## 功能特性

叶小币积分系统设计
改进后的数据库设计
sql
-- 积分规则主表 (存储通用配置)
CREATE TABLE coin_rules (
  id INT PRIMARY KEY AUTO_INCREMENT,
  level_id INT NOT NULL DEFAULT 0, -- 0表示全局规则
  rule_key VARCHAR(50) NOT NULL, -- 规则标识符
  enabled BOOLEAN NOT NULL DEFAULT 0,
  condition_value INT DEFAULT 0, -- 触发条件值
  base_coin INT NOT NULL DEFAULT 0, -- 基础奖励
  extra_coin INT NOT NULL DEFAULT 0, -- 额外奖励
  UNIQUE KEY (level_id, rule_key)
);

-- 有效期策略表
CREATE TABLE coin_expiry_policy (
  level_id INT PRIMARY KEY,
  policy ENUM('permanent', 'yearly', 'monthly', 'custom') NOT NULL DEFAULT 'permanent',
  custom_years INT DEFAULT 0
);

-- 全局开关表
CREATE TABLE coin_global_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  enabled BOOLEAN NOT NULL DEFAULT 0, -- 叶小币启用开关
  refund_return BOOLEAN NOT NULL DEFAULT 0 -- 退款归还开关
);
规则类型定义（程序常量）
python
# 规则类型常量（程序内定义）
RULES = {
    # 基础规则
    'REGISTER': 'register',             # 注册奖励
    'COMPLETE_INFO': 'complete_info',   # 完善信息
    
    # 消费规则
    'CONSUME_ACCUMULATE': 'consume_accumulate',  # 累计消费
    'CONSUME_SINGLE': 'consume_single',          # 单笔消费满额
    
    # 活动规则
    'ACTIVITY_JOIN': 'activity_join',           # 普通活动
    'ACTIVITY_SPECIAL': 'activity_special',     # 专项活动
    
    # 打卡规则
    'CHECKIN_SHARE': 'checkin_share',           # 打卡+分享
    'CHECKIN_CONTINUOUS': 'checkin_continuous', # 连续打卡
    
    # 推荐规则
    'REFERRAL_REGISTER': 'referral_register',   # 推荐注册
    'REFERRAL_CONSUME': 'referral_consume'      # 推荐消费
}
"满多少送多少"规则实现方案
1. 累计消费规则

sql
INSERT INTO coin_rules (level_id, rule_key, enabled, condition_value, base_coin)
VALUES (1, 'consume_accumulate', 1, 100, 10);
-- 解释：每累计消费100元，获得10币
2. 单笔满额规则

sql
INSERT INTO coin_rules (level_id, rule_key, enabled, condition_value, base_coin)
VALUES (1, 'consume_single', 1, 500, 50);
-- 解释：单笔消费满500元，额外获得50币
3. 推荐消费规则

sql
INSERT INTO coin_rules (level_id, rule_key, enabled, condition_value, base_coin)
VALUES (0, 'referral_consume', 1, 200, 20);
-- 解释：被推荐人每消费满200元，推荐人获得20币
核心计算逻辑
python
def calculate_coins(rule_type, amount, level_id):
    """
    计算满足条件时应获得的叶小币
    :param rule_type: 规则类型
    :param amount: 消费金额/天数等
    :param level_id: 用户等级
    :return: 应得叶小币数
    """
    rule = get_rule(level_id, rule_type)
    
    if not rule or not rule['enabled']:
        return 0
    
    # "满多少送多少"计算逻辑
    if rule_type in [RULES['CONSUME_ACCUMULATE'], 
                     RULES['REFERRAL_CONSUME']]:
        # 计算满足条件的次数
        times = amount // rule['condition_value']
        return times * rule['base_coin']
    
    # 单次满足条件
    elif rule_type in [RULES['CONSUME_SINGLE']:
        return rule['base_coin'] if amount >= rule['condition_value'] else 0
    
    # 固定奖励
    else:
        return rule['base_coin'] + rule['extra_coin']

# 示例：计算累计消费奖励
amount = 350  # 消费金额
coins = calculate_coins(RULES['CONSUME_ACCUMULATE'], amount, 1)
print(f"消费{amount}元，获得{coins}币")  # 输出：消费350元，获得30币
补充表结构（支持完整业务）
sql
-- 用户积分账户
CREATE TABLE user_coins (
  user_id INT PRIMARY KEY,
  total_coins INT NOT NULL DEFAULT 0,   -- 总获得积分
  used_coins INT NOT NULL DEFAULT 0,    -- 已使用积分
  frozen_coins INT NOT NULL DEFAULT 0,  -- 冻结积分（未核销订单）
  expiry_date DATETIME DEFAULT NULL     -- 积分过期时间
);

-- 积分流水表
CREATE TABLE coin_transactions (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  amount INT NOT NULL,  -- 变动数量（正数为获得，负数为消耗）
  type ENUM('earn', 'use', 'refund') NOT NULL,
  rule_type VARCHAR(50) DEFAULT NULL, -- 关联规则类型
  order_id BIGINT DEFAULT NULL,       -- 关联订单
  created_at DATETIME NOT NULL
);

-- 一次性奖励记录表
CREATE TABLE coin_one_time_rewards (
  user_id INT NOT NULL,
  reward_type VARCHAR(50) NOT NULL,  -- 奖励类型（register/complete_info）
  received BOOLEAN NOT NULL DEFAULT 0,
  PRIMARY KEY (user_id, reward_type)
);

-- 打卡记录表
CREATE TABLE coin_checkin_records (
  user_id INT NOT NULL,
  checkin_date DATE NOT NULL,  -- 打卡日期
  shared BOOLEAN NOT NULL DEFAULT 0, -- 是否分享
  PRIMARY KEY (user_id, checkin_date)
);





# 叶小币积分系统 - Golang 实现

下面是一个完整的 Golang 设计实现，包括数据库模型、服务层和核心业务逻辑：

## 数据库模型设计 (models.go)

```go
package points

import (
	"database/sql"
	"time"
)

// 全局配置
type GlobalConfig struct {
	ID           int  `json:"id" db:"id"`
	Enabled      bool `json:"enabled" db:"enabled"`           // 叶小币启用开关
	RefundReturn bool `json:"refundReturn" db:"refund_return"` // 退款归还开关
}

// 积分规则
type PointsRule struct {
	ID             int    `json:"id" db:"id"`
	LevelID        int    `json:"levelId" db:"level_id"`               // 0表示全局规则
	RuleKey        string `json:"ruleKey" db:"rule_key"`               // 规则标识符
	Enabled        bool   `json:"enabled" db:"enabled"`                // 是否启用
	ConditionValue int    `json:"conditionValue" db:"condition_value"` // 触发条件值
	BasePoints     int    `json:"basePoints" db:"base_points"`         // 基础奖励积分
	ExtraPoints    int    `json:"extraPoints" db:"extra_points"`       // 额外奖励积分
}

// 有效期策略
type ExpiryPolicy struct {
	LevelID     int    `json:"levelId" db:"level_id"`
	Policy      string `json:"policy" db:"policy"`           // permanent, yearly, monthly, custom
	CustomYears int    `json:"customYears" db:"custom_years"` // 自定义过期年限
}

// 用户积分账户
type UserPoints struct {
	UserID      int        `json:"userId" db:"user_id"`
	TotalPoints int        `json:"totalPoints" db:"total_points"`   // 总获得积分
	UsedPoints  int        `json:"usedPoints" db:"used_points"`     // 已使用积分
	FrozenPoints int       `json:"frozenPoints" db:"frozen_points"` // 冻结积分
	ExpiryDate  *time.Time `json:"expiryDate" db:"expiry_date"`     // 积分过期时间
}

// 积分流水记录
type PointsTransaction struct {
	ID        int64      `json:"id" db:"id"`
	UserID    int        `json:"userId" db:"user_id"`
	Amount    int        `json:"amount" db:"amount"` // 变动数量（正数为获得，负数为消耗）
	Type      string     `json:"type" db:"type"`     // earn, use, refund
	RuleKey   *string    `json:"ruleKey" db:"rule_key"`
	OrderID   *int64     `json:"orderId" db:"order_id"`
	CreatedAt time.Time  `json:"createdAt" db:"created_at"`
}

// 一次性奖励记录
type OneTimeReward struct {
	UserID     int    `json:"userId" db:"user_id"`
	RewardType string `json:"rewardType" db:"reward_type"` // register, complete_info
	Received   bool   `json:"received" db:"received"`
}

// 打卡记录
type CheckinRecord struct {
	UserID      int       `json:"userId" db:"user_id"`
	CheckinDate time.Time `json:"checkinDate" db:"checkin_date"` // 打卡日期
	Shared      bool      `json:"shared" db:"shared"`           // 是否分享
}

// 规则类型常量
const (
	RuleRegister         = "register"          // 注册奖励
	RuleCompleteInfo     = "complete_info"     // 完善信息
	RuleConsumeAccumulate = "consume_accumulate" // 累计消费
	RuleConsumeSingle    = "consume_single"    // 单笔消费满额
	RuleActivityJoin     = "activity_join"     // 普通活动
	RuleActivitySpecial  = "activity_special"  // 专项活动
	RuleCheckinShare     = "checkin_share"     // 打卡+分享
	RuleCheckinContinuous = "checkin_continuous" // 连续打卡
	RuleReferralRegister = "referral_register" // 推荐注册
	RuleReferralConsume  = "referral_consume"  // 推荐消费
)
```

## 服务层实现 (service.go)

```go
package points

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"time"
)

var (
	ErrSystemDisabled = errors.New("points system is disabled")
	ErrAlreadyRedeemed = errors.New("reward already redeemed")
)

type PointsService struct {
	db *sql.DB
}

func NewPointsService(db *sql.DB) *PointsService {
	return &PointsService{db: db}
}

// 检查系统是否启用
func (s *PointsService) IsEnabled(ctx context.Context) (bool, error) {
	var config GlobalConfig
	query := "SELECT id, enabled, refund_return FROM global_config ORDER BY id DESC LIMIT 1"
	err := s.db.QueryRowContext(ctx, query).Scan(&config.ID, &config.Enabled, &config.RefundReturn)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return config.Enabled, nil
}

// 获取用户等级 (伪代码 - 实际需要实现)
func (s *PointsService) GetUserLevel(userID int) int {
	// 实际项目中需要从会员系统获取
	return 1 // 默认等级1
}

// 获取积分规则
func (s *PointsService) GetRule(ctx context.Context, levelID int, ruleKey string) (*PointsRule, error) {
	// 先查询特定等级规则
	var rule PointsRule
	query := "SELECT id, level_id, rule_key, enabled, condition_value, base_points, extra_points " +
		"FROM points_rules WHERE level_id = ? AND rule_key = ?"
	err := s.db.QueryRowContext(ctx, query, levelID, ruleKey).Scan(
		&rule.ID, &rule.LevelID, &rule.RuleKey, &rule.Enabled,
		&rule.ConditionValue, &rule.BasePoints, &rule.ExtraPoints,
	)

	if err == nil {
		return &rule, nil
	}

	if err != sql.ErrNoRows {
		return nil, err
	}

	// 如果特定等级规则不存在，查询全局规则
	query = "SELECT id, level_id, rule_key, enabled, condition_value, base_points, extra_points " +
		"FROM points_rules WHERE level_id = 0 AND rule_key = ?"
	err = s.db.QueryRowContext(ctx, query, ruleKey).Scan(
		&rule.ID, &rule.LevelID, &rule.RuleKey, &rule.Enabled,
		&rule.ConditionValue, &rule.BasePoints, &rule.ExtraPoints,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 规则不存在
		}
		return nil, err
	}

	return &rule, nil
}

// 计算应得积分
func (s *PointsService) CalculatePoints(ctx context.Context, ruleKey string, amount int, userID int) (int, error) {
	// 检查系统是否启用
	if enabled, err := s.IsEnabled(ctx); !enabled || err != nil {
		return 0, ErrSystemDisabled
	}

	levelID := s.GetUserLevel(userID)
	rule, err := s.GetRule(ctx, levelID, ruleKey)
	if err != nil {
		return 0, err
	}
	if rule == nil || !rule.Enabled {
		return 0, nil
	}

	switch ruleKey {
	case RuleConsumeAccumulate, RuleReferralConsume:
		// 每满X元获得Y积分
		times := amount / rule.ConditionValue
		return times * rule.BasePoints, nil
		
	case RuleConsumeSingle:
		// 单笔满X元额外获得Y积分
		if amount >= rule.ConditionValue {
			return rule.BasePoints, nil
		}
		return 0, nil
		
	case RuleCheckinContinuous:
		// 连续打卡奖励
		return rule.BasePoints + rule.ExtraPoints, nil
		
	default:
		// 固定奖励
		return rule.BasePoints, nil
	}
}

// 添加积分
func (s *PointsService) AddPoints(ctx context.Context, tx *sql.Tx, userID int, points int, ruleKey string, orderID *int64) error {
	if points <= 0 {
		return nil
	}

	// 1. 更新用户积分账户
	query := `INSERT INTO user_points (user_id, total_points, used_points, frozen_points) 
			  VALUES (?, ?, 0, 0)
			  ON DUPLICATE KEY UPDATE total_points = total_points + ?`
	_, err := tx.ExecContext(ctx, query, userID, points, points)
	if err != nil {
		return err
	}

	// 2. 添加积分流水记录
	query = `INSERT INTO points_transactions 
			(user_id, amount, type, rule_key, order_id, created_at) 
			VALUES (?, ?, 'earn', ?, ?, NOW())`
	_, err = tx.ExecContext(ctx, query, userID, points, ruleKey, orderID)
	return err
}

// 订单核销后处理积分
func (s *PointsService) HandleOrderCompletion(ctx context.Context, orderID int64, userID int, amount float64) error {
	// 检查系统是否启用
	if enabled, err := s.IsEnabled(ctx); !enabled || err != nil {
		return nil
	}

	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 1. 计算累计消费积分
	accumulatePoints, err := s.CalculatePoints(ctx, RuleConsumeAccumulate, int(amount), userID)
	if err != nil {
		return err
	}

	// 2. 计算单笔满额积分
	singlePoints, err := s.CalculatePoints(ctx, RuleConsumeSingle, int(amount), userID)
	if err != nil {
		return err
	}

	totalPoints := accumulatePoints + singlePoints

	// 3. 添加积分
	if totalPoints > 0 {
		orderIDPtr := &orderID
		if err := s.AddPoints(ctx, tx, userID, totalPoints, RuleConsumeAccumulate, orderIDPtr); err != nil {
			return err
		}
	}

	// 4. 处理推荐人奖励
	if err := s.HandleReferralReward(ctx, tx, userID, int(amount)); err != nil {
		return err
	}

	return tx.Commit()
}

// 处理推荐人奖励
func (s *PointsService) HandleReferralReward(ctx context.Context, tx *sql.Tx, newUserID int, amount int) error {
	// 获取推荐人ID (伪代码 - 实际需要实现)
	referrerID := getReferrerID(newUserID)
	if referrerID == 0 {
		return nil
	}

	// 计算推荐消费奖励
	points, err := s.CalculatePoints(ctx, RuleReferralConsume, amount, referrerID)
	if err != nil {
		return err
	}

	if points > 0 {
		// 添加积分到推荐人账户
		if err := s.AddPoints(ctx, tx, referrerID, points, RuleReferralConsume, nil); err != nil {
			return err
		}
	}
	return nil
}

// 注册奖励
func (s *PointsService) HandleRegister(ctx context.Context, userID int) error {
	// 检查是否已领取
	if received, err := s.checkRewardReceived(ctx, userID, RuleRegister); err != nil || received {
		return err
	}

	// 计算奖励积分
	points, err := s.CalculatePoints(ctx, RuleRegister, 0, userID)
	if err != nil {
		return err
	}

	if points > 0 {
		tx, err := s.db.BeginTx(ctx, nil)
		if err != nil {
			return err
		}
		defer tx.Rollback()

		// 添加积分
		if err := s.AddPoints(ctx, tx, userID, points, RuleRegister, nil); err != nil {
			return err
		}

		// 标记为已领取
		if err := s.markRewardReceived(ctx, tx, userID, RuleRegister); err != nil {
			return err
		}

		return tx.Commit()
	}
	return nil
}

// 完善信息奖励
func (s *PointsService) HandleCompleteProfile(ctx context.Context, userID int) error {
	// 检查是否已领取
	if received, err := s.checkRewardReceived(ctx, userID, RuleCompleteInfo); err != nil || received {
		return err
	}

	// 计算奖励积分
	points, err := s.CalculatePoints(ctx, RuleCompleteInfo, 0, userID)
	if err != nil {
		return err
	}

	if points > 0 {
		tx, err := s.db.BeginTx(ctx, nil)
		if err != nil {
			return err
		}
		defer tx.Rollback()

		// 添加积分
		if err := s.AddPoints(ctx, tx, userID, points, RuleCompleteInfo, nil); err != nil {
			return err
		}

		// 标记为已领取
		if err := s.markRewardReceived(ctx, tx, userID, RuleCompleteInfo); err != nil {
			return err
		}

		return tx.Commit()
	}
	return nil
}

// 处理打卡
func (s *PointsService) HandleCheckin(ctx context.Context, userID int, shared bool) error {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 检查今天是否已打卡
	var exists bool
	query := "SELECT 1 FROM checkin_records WHERE user_id = ? AND checkin_date = ?"
	err := s.db.QueryRowContext(ctx, query, userID, today).Scan(&exists)
	if err != nil && err != sql.ErrNoRows {
		return err
	}

	if exists {
		return nil // 今天已打卡
	}

	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 记录打卡
	query = "INSERT INTO checkin_records (user_id, checkin_date, shared) VALUES (?, ?, ?)"
	_, err = tx.ExecContext(ctx, query, userID, today, shared)
	if err != nil {
		return err
	}

	// 单次打卡+分享奖励
	if shared {
		points, err := s.CalculatePoints(ctx, RuleCheckinShare, 1, userID)
		if err != nil {
			return err
		}
		if points > 0 {
			if err := s.AddPoints(ctx, tx, userID, points, RuleCheckinShare, nil); err != nil {
				return err
			}
		}
	}

	// 检查连续打卡奖励
	if err := s.checkContinuousCheckin(ctx, tx, userID, today); err != nil {
		return err
	}

	return tx.Commit()
}

// 检查连续打卡奖励
func (s *PointsService) checkContinuousCheckin(ctx context.Context, tx *sql.Tx, userID int, today time.Time) error {
	// 获取最近30天的打卡记录
	startDate := today.AddDate(0, 0, -29)
	query := `SELECT COUNT(*) FROM checkin_records 
			  WHERE user_id = ? AND checkin_date BETWEEN ? AND ?`
	var count int
	err := tx.QueryRowContext(ctx, query, userID, startDate, today).Scan(&count)
	if err != nil {
		return err
	}

	// 检查是否连续30天打卡
	if count == 30 {
		points, err := s.CalculatePoints(ctx, RuleCheckinContinuous, 30, userID)
		if err != nil {
			return err
		}
		if points > 0 {
			return s.AddPoints(ctx, tx, userID, points, RuleCheckinContinuous, nil)
		}
	}
	return nil
}

// 检查奖励是否已领取
func (s *PointsService) checkRewardReceived(ctx context.Context, userID int, rewardType string) (bool, error) {
	var received bool
	query := "SELECT received FROM one_time_rewards WHERE user_id = ? AND reward_type = ?"
	err := s.db.QueryRowContext(ctx, query, userID, rewardType).Scan(&received)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil
		}
		return false, err
	}
	return received, nil
}

// 标记奖励已领取
func (s *PointsService) markRewardReceived(ctx context.Context, tx *sql.Tx, userID int, rewardType string) error {
	query := `INSERT INTO one_time_rewards (user_id, reward_type, received)
			  VALUES (?, ?, true)
			  ON DUPLICATE KEY UPDATE received = true`
	_, err := tx.ExecContext(ctx, query, userID, rewardType)
	return err
}

// 处理订单退款
func (s *PointsService) HandleOrderRefund(ctx context.Context, orderID int64, userID int) error {
	// 检查系统是否启用
	if enabled, err := s.IsEnabled(ctx); !enabled || err != nil {
		return nil
	}

	// 获取退款归还配置
	var refundReturn bool
	query := "SELECT refund_return FROM global_config ORDER BY id DESC LIMIT 1"
	if err := s.db.QueryRowContext(ctx, query).Scan(&refundReturn); err != nil {
		return err
	}
	
	if !refundReturn {
		return nil
	}

	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 1. 查询订单相关的积分获得记录
	query = `SELECT amount, id FROM points_transactions 
			 WHERE user_id = ? AND order_id = ? AND type = 'earn'`
	rows, err := tx.QueryContext(ctx, query, userID, orderID)
	if err != nil {
		return err
	}
	defer rows.Close()

	var earnedPoints int
	var transactionIDs []int64
	for rows.Next() {
		var amount int
		var id int64
		if err := rows.Scan(&amount, &id); err != nil {
			return err
		}
		earnedPoints += amount
		transactionIDs = append(transactionIDs, id)
	}

	// 2. 收回消费所得积分
	if earnedPoints > 0 {
		// 更新用户积分账户
		query = `UPDATE user_points 
				 SET total_points = total_points - ?
				 WHERE user_id = ?`
		if _, err := tx.ExecContext(ctx, query, earnedPoints, userID); err != nil {
			return err
		}

		// 添加退款流水记录
		query = `INSERT INTO points_transactions 
				(user_id, amount, type, rule_key, order_id, created_at) 
				VALUES (?, ?, 'refund', 'order_refund', ?, NOW())`
		if _, err := tx.ExecContext(ctx, query, userID, -earnedPoints, orderID); err != nil {
			return err
		}
	}

	// 3. 退还抵扣积分
	query = `SELECT amount FROM points_transactions 
			 WHERE user_id = ? AND order_id = ? AND type = 'use'`
	var usedPoints int
	if err := tx.QueryRowContext(ctx, query, userID, orderID).Scan(&usedPoints); err != nil {
		if err != sql.ErrNoRows {
			return err
		}
	}

	if usedPoints < 0 {
		usedPoints = -usedPoints // 取正值
	}

	if usedPoints > 0 {
		// 更新用户积分账户
		query = `UPDATE user_points 
				 SET total_points = total_points + ?, used_points = used_points - ?
				 WHERE user_id = ?`
		if _, err := tx.ExecContext(ctx, query, usedPoints, usedPoints, userID); err != nil {
			return err
		}

		// 添加退款流水记录
		query = `INSERT INTO points_transactions 
				(user_id, amount, type, rule_key, order_id, created_at) 
				VALUES (?, ?, 'refund', 'deduction_refund', ?, NOW())`
		if _, err := tx.ExecContext(ctx, query, userID, usedPoints, orderID); err != nil {
			return err
		}
	}

	return tx.Commit()
}

// 伪代码 - 实际需要实现
func getReferrerID(userID int) int {
	// 实际项目中从推荐关系表获取
	return 0
}
```

## 控制器示例 (controller.go)

```go
package api

import (
	"net/http"
	
	"your-app/points"
)

type PointsController struct {
	pointsService *points.PointsService
}

func NewPointsController(service *points.PointsService) *PointsController {
	return &PointsController{pointsService: service}
}

// 订单完成处理
func (c *PointsController) CompleteOrder(w http.ResponseWriter, r *http.Request) {
	// 解析请求参数
	var req struct {
		OrderID int64   `json:"orderId"`
		UserID  int     `json:"userId"`
		Amount  float64 `json:"amount"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}
	
	// 处理积分
	ctx := r.Context()
	if err := c.pointsService.HandleOrderCompletion(ctx, req.OrderID, req.UserID, req.Amount); err != nil {
		if err == points.ErrSystemDisabled {
			w.WriteHeader(http.StatusOK)
			return
		}
		http.Error(w, "Failed to process points", http.StatusInternalServerError)
		return
	}
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "success"})
}

// 用户打卡
func (c *PointsController) Checkin(w http.ResponseWriter, r *http.Request) {
	var req struct {
		UserID int  `json:"userId"`
		Shared bool `json:"shared"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}
	
	ctx := r.Context()
	if err := c.pointsService.HandleCheckin(ctx, req.UserID, req.Shared); err != nil {
		http.Error(w, "Failed to process checkin", http.StatusInternalServerError)
		return
	}
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "success"})
}

// 用户完善资料
func (c *PointsController) CompleteProfile(w http.ResponseWriter, r *http.Request) {
	var req struct {
		UserID int `json:"userId"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}
	
	ctx := r.Context()
	if err := c.pointsService.HandleCompleteProfile(ctx, req.UserID); err != nil {
		if err == points.ErrAlreadyRedeemed {
			http.Error(w, "Profile reward already redeemed", http.StatusBadRequest)
			return
		}
		http.Error(w, "Failed to process profile completion", http.StatusInternalServerError)
		return
	}
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "success"})
}
```

## 系统特点

1. **完整的积分生命周期管理**：
   - 积分获取（注册、消费、打卡、推荐等）
   - 积分使用（订单抵扣）
   - 积分退还（订单取消/退款）
   - 积分过期管理

2. **灵活的规则配置**：
   - 支持全局规则和等级专属规则
   - "满X元送Y积分"实现
   - 单笔满额额外奖励
   - 连续打卡奖励

3. **安全的事务处理**：
   - 所有积分操作在事务中进行
   - 防止重复领取一次性奖励
   - 每日打卡防重复机制

4. **模块化设计**：
   - 清晰的服务层和控制器分离
   - 易于扩展新规则类型
   - 独立处理各业务场景

5. **符合业务需求**：
   - 支持全局开关控制
   - 退款时积分回收机制
   - 订单核销后才发放积分
   - 一次性奖励防重复领取

## 使用示例

```go
// 初始化服务
db, _ := sql.Open("mysql", "user:password@tcp(127.0.0.1:3306)/dbname")
pointsService := points.NewPointsService(db)

// 用户注册
func onUserRegister(userID int) {
	ctx := context.Background()
	err := pointsService.HandleRegister(ctx, userID)
	if err != nil {
		log.Printf("Failed to handle register points: %v", err)
	}
}

// 用户消费
func onOrderComplete(orderID int64, userID int, amount float64) {
	ctx := context.Background()
	err := pointsService.HandleOrderCompletion(ctx, orderID, userID, amount)
	if err != nil {
		log.Printf("Failed to handle order points: %v", err)
	}
}

// 用户打卡
func onUserCheckin(userID int, shared bool) {
	ctx := context.Background()
	err := pointsService.HandleCheckin(ctx, userID, shared)
	if err != nil {
		log.Printf("Failed to handle checkin: %v", err)
	}
}
```

这个设计完全符合您的需求，实现了叶小币积分系统的所有核心功能，包括：
- 全局开关控制
- 多种积分获取方式（注册、完善信息、消费、活动、打卡、推荐）
- "满X元送Y积分"的实现
- 等级差异化规则
- 一次性奖励防重复
- 积分有效期管理
- 退款时积分回收机制
- 订单核销后才发放积分

系统采用模块化设计，易于维护和扩展，同时保证了数据的一致性和安全性。