package evaluation

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// ServiceEvaluationRepository 服务评价仓库
type ServiceEvaluationRepository struct {
	db *gorm.DB
}

// NewServiceEvaluationRepository 创建服务评价仓库
func NewServiceEvaluationRepository(db *gorm.DB) *ServiceEvaluationRepository {
	return &ServiceEvaluationRepository{db: db}
}

// Create 创建服务评价
func (r *ServiceEvaluationRepository) Create(ctx context.Context, evaluation *ServiceEvaluation) error {
	return r.db.WithContext(ctx).Create(evaluation).Error
}

// FindByID 根据ID查找服务评价
func (r *ServiceEvaluationRepository) FindByID(ctx context.Context, id uint) (*ServiceEvaluation, error) {
	var evaluation ServiceEvaluation
	err := r.db.WithContext(ctx).First(&evaluation, id).Error
	if err != nil {
		return nil, err
	}
	return &evaluation, nil
}

// FindByUserID 根据用户ID查找评价（检查是否已评价）
func (r *ServiceEvaluationRepository) FindByUserID(ctx context.Context, userID uint) (*ServiceEvaluation, error) {
	var evaluation ServiceEvaluation
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&evaluation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &evaluation, nil
}

// HasUserEvaluated 检查用户是否已评价
func (r *ServiceEvaluationRepository) HasUserEvaluated(ctx context.Context, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&ServiceEvaluation{}).Where("user_id = ?", userID).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// List 获取服务评价列表
func (r *ServiceEvaluationRepository) List(ctx context.Context, params *ServiceEvaluationQueryParams) ([]*ServiceEvaluationInfo, int64, error) {
	query := r.db.WithContext(ctx).Model(&ServiceEvaluation{})

	// 构建查询条件
	if params.StartDate != "" {
		startTime, err := time.Parse("2006-01-02", params.StartDate)
		if err != nil {
			return nil, 0, fmt.Errorf("开始日期格式错误: %v", err)
		}
		query = query.Where("DATE(created_at) >= ?", startTime.Format("2006-01-02"))
	}

	if params.EndDate != "" {
		endTime, err := time.Parse("2006-01-02", params.EndDate)
		if err != nil {
			return nil, 0, fmt.Errorf("结束日期格式错误: %v", err)
		}
		query = query.Where("DATE(created_at) <= ?", endTime.Format("2006-01-02"))
	}

	if params.Status != nil {
		query = query.Where("status = ?", *params.Status)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询，关联wx_user表
	offset := (params.Page - 1) * params.PageSize

	// 定义查询结果结构
	type EvaluationWithUser struct {
		ServiceEvaluation
		UserName  string `gorm:"column:user_name"`
		UserPhone string `gorm:"column:user_phone"`
	}

	var evaluationsWithUser []EvaluationWithUser
	err := r.db.WithContext(ctx).
		Table("service_evaluations").
		Select("service_evaluations.*, wx_users.name as user_name, wx_users.phone as user_phone").
		Joins("LEFT JOIN wx_users ON service_evaluations.user_id = wx_users.user_id").
		Where("service_evaluations.deleted_at IS NULL").
		Order("service_evaluations.created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Find(&evaluationsWithUser).Error

	if err != nil {
		return nil, 0, err
	}

	// 转换为展示信息
	var result []*ServiceEvaluationInfo
	for _, eval := range evaluationsWithUser {
		info := &ServiceEvaluationInfo{
			ID:          eval.ID,
			UserID:      eval.UserID,
			UserName:    GetDisplayUserName(eval.UserName, eval.IsAnonymous),
			UserPhone:   GetDisplayUserPhone(eval.UserPhone, eval.IsAnonymous),
			Content:     eval.Content,
			IsAnonymous: eval.IsAnonymous,
			Status:      eval.Status,
			CreatedAt:   eval.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   eval.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		result = append(result, info)
	}

	return result, total, nil
}

// UpdateStatus 更新评价状态
func (r *ServiceEvaluationRepository) UpdateStatus(ctx context.Context, id uint, status int) error {
	return r.db.WithContext(ctx).Model(&ServiceEvaluation{}).
		Where("id = ?", id).
		Update("status", status).Error
}

// SoftDelete 软删除评价（仅在后台隐藏）
func (r *ServiceEvaluationRepository) SoftDelete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&ServiceEvaluation{}).
		Where("id = ?", id).
		Update("status", 0).Error
}

// Delete 物理删除评价
func (r *ServiceEvaluationRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&ServiceEvaluation{}, id).Error
}

// FindByIDWithUser 根据ID查找服务评价并关联用户信息
func (r *ServiceEvaluationRepository) FindByIDWithUser(ctx context.Context, id uint) (*ServiceEvaluationInfo, error) {
	// 定义查询结果结构
	type EvaluationWithUser struct {
		ServiceEvaluation
		UserName  string `gorm:"column:user_name"`
		UserPhone string `gorm:"column:user_phone"`
	}

	var evalWithUser EvaluationWithUser
	err := r.db.WithContext(ctx).
		Table("service_evaluations").
		Select("service_evaluations.*, wx_users.name as user_name, wx_users.phone as user_phone").
		Joins("LEFT JOIN wx_users ON service_evaluations.user_id = wx_users.user_id").
		Where("service_evaluations.id = ?", id).
		Where("service_evaluations.deleted_at IS NULL").
		First(&evalWithUser).Error

	if err != nil {
		return nil, err
	}

	// 转换为展示信息
	info := &ServiceEvaluationInfo{
		ID:          evalWithUser.ID,
		UserID:      evalWithUser.UserID,
		UserName:    GetDisplayUserName(evalWithUser.UserName, evalWithUser.IsAnonymous),
		UserPhone:   GetDisplayUserPhone(evalWithUser.UserPhone, evalWithUser.IsAnonymous),
		Content:     evalWithUser.Content,
		IsAnonymous: evalWithUser.IsAnonymous,
		Status:      evalWithUser.Status,
		CreatedAt:   evalWithUser.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   evalWithUser.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	return info, nil
}
