package patient

import (
	"time"

	"gorm.io/gorm"
)

// DiagnosisRecord 诊断记录模型（基于门诊收费信息）
type DiagnosisRecord struct {
	ID uint `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`

	// 基础信息
	PYM   string `gorm:"column:pym;comment:拼音码" json:"pym"`
	CzrID int    `gorm:"column:czrid;comment:创建人ID" json:"czrid"`
	CzSJ  string `gorm:"column:czsj;comment:创建时间" json:"czsj"`
	CzrMC string `gorm:"column:czrmc;comment:创建人名称" json:"czrmc"`
	ZfBZ  int    `gorm:"column:zfbz;comment:作废标志" json:"zfbz"`
	BZ    string `gorm:"column:bz;comment:备注" json:"bz"`

	// 患者信息
	PatientID uint   `gorm:"column:patient_id;index;comment:患者ID(关联wx_patient表)" json:"patient_id"`
	BAH       string `gorm:"column:bah;comment:病案号" json:"bah"`
	XM        string `gorm:"column:xm;comment:姓名" json:"xm"`

	// 门诊收费信息
	MzsfID  int    `gorm:"column:mzsfid;index;unique;comment:门诊收费ID" json:"mzsfid"`
	WsjgID  int    `gorm:"column:wsjgid;index;comment:卫生机构ID" json:"wsjgid"`
	WsjgMC  string `gorm:"column:wsjgmc;comment:卫生机构名称" json:"wsjgmc"`
	MzsfdjH string `gorm:"column:mzsfdjh;comment:门诊收费登记号" json:"mzsfdjh"`
	MzghID  int    `gorm:"column:mzghid;comment:门诊挂号ID" json:"mzghid"`
	MzghdjH string `gorm:"column:mzghdjh;comment:门诊挂号登记号" json:"mzghdjh"`
	MzblID  int    `gorm:"column:mzblid;comment:门诊病历ID" json:"mzblid"`
	MzyzIDs string `gorm:"column:mzyzids;comment:门诊医嘱IDs" json:"mzyzids"`
	MzhjID  int    `gorm:"column:mzhjid;comment:门诊会诊ID" json:"mzhjid"`
	GrxxID  int    `gorm:"column:grxxid;index;comment:杭州HIS的个人信息ID" json:"grxxid"`

	// 费用信息
	FylbDM string `gorm:"column:fylbdm;comment:费用类别代码" json:"fylbdm"`
	FylbMC string `gorm:"column:fylbmc;comment:费用类别名称" json:"fylbmc"`

	// 科室医生信息
	JgksID int    `gorm:"column:jgksid;comment:机构科室ID" json:"jgksid"`
	JgksMC string `gorm:"column:jgksmc;comment:机构科室名称" json:"jgksmc"`
	YsID   int    `gorm:"column:ysid;comment:医生ID" json:"ysid"`
	YsMC   string `gorm:"column:ysmc;comment:医生名称" json:"ysmc"`

	// 金额信息
	YzJE float64 `gorm:"column:yzje;comment:应收金额" json:"yzje"`
	ZJE  float64 `gorm:"column:zje;comment:总金额" json:"zje"`

	// 票据信息
	PJH   string `gorm:"column:pjh;comment:票据号" json:"pjh"`
	PJLSH string `gorm:"column:pjlsh;comment:票据流水号" json:"pjlsh"`
	PJLx  string `gorm:"column:pjlx;comment:票据类型" json:"pjlx"`
	PJURL string `gorm:"column:pjurl;comment:票据URL" json:"pjurl"`
	KPRQ  string `gorm:"column:kprq;comment:开票日期" json:"kprq"`

	// 疾病信息
	JbDM string `gorm:"column:jbdm;comment:疾病代码" json:"jbdm"`
	JbMC string `gorm:"column:jbmc;comment:疾病名称" json:"jbmc"`

	// 医保信息
	YbmzlxDM string `gorm:"column:ybmzlxdm;comment:医保门诊类型代码" json:"ybmzlxdm"`
	YbksDM   string `gorm:"column:ybksdm;comment:医保科室代码" json:"ybksdm"`

	// 门诊类型
	MzlxDM string `gorm:"column:mzlxdm;comment:门诊类型代码" json:"mzlxdm"`
	MzlxMC string `gorm:"column:mzlxmc;comment:门诊类型名称" json:"mzlxmc"`

	// 系统字段
	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// TableName 设置表名
func (DiagnosisRecord) TableName() string {
	return "diagnosis_records"
}
