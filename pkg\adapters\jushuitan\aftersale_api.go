package jushuitan

import (
	"context"
	"fmt"

	jsoniter "github.com/json-iterator/go"
)

// AfterSaleItem 售后商品项
type AfterSaleItem struct {
	OuterOiID       string  `json:"outer_oi_id,omitempty"`      // 平台订单明细编号
	SkuID           string  `json:"sku_id"`                     // 商家商品编码
	Qty             int     `json:"qty"`                        // 退货数量
	Amount          float64 `json:"amount"`                     // SKU退款金额
	Type            string  `json:"type"`                       // 可选:退货，换货，其它，补发
	Name            string  `json:"name,omitempty"`             // 商品名称
	Pic             string  `json:"pic,omitempty"`              // 图片地址
	PropertiesValue string  `json:"properties_value,omitempty"` // 属性规格
	Des             string  `json:"des,omitempty"`              // 备注
	Freight         float64 `json:"freight,omitempty"`          // 卖家应退运费
	BatchID         string  `json:"batch_id,omitempty"`         // 生产批次号
	ProducedDate    string  `json:"produced_date,omitempty"`    // 生产日期
	ExpirationDate  string  `json:"expiration_date,omitempty"`  // 到期日
}

// AfterSaleRequest 售后上传请求
type AfterSaleRequest struct {
	ShopID           int             `json:"shop_id"`                      // 店铺编号
	OuterAsID        string          `json:"outer_as_id"`                  // 退货退款单号，平台唯一
	SoID             string          `json:"so_id"`                        // 平台订单号(订单页面线上单号)
	Type             string          `json:"type"`                         // 售后类型，普通退货，其它，拒收退货,仅退款,投诉,补发,维修,换货
	LogisticsCompany string          `json:"logistics_company,omitempty"`  // 快递公司
	LID              string          `json:"l_id,omitempty"`               // 物流单号
	ReceiverNameEn   string          `json:"receiver_name_en,omitempty"`   // 收货人：仅针对换货补发类型的售后单有效
	ReceiverMobileEn string          `json:"receiver_mobile_en,omitempty"` // 联系手机：仅针对换货补发类型的售后单有效
	ShopStatus       string          `json:"shop_status,omitempty"`        // 平台单据状态
	Remark           string          `json:"remark,omitempty"`             // 备注
	GoodStatus       string          `json:"good_status"`                  // 货物状态
	QuestionType     string          `json:"question_type"`                // 问题类型
	TotalAmount      float64         `json:"total_amount,omitempty"`       // 原单据总金额
	Refund           float64         `json:"refund"`                       // 卖家应退金额
	Payment          float64         `json:"payment"`                      // 买家应补偿金额
	WarehouseType    int             `json:"warehouse_type,omitempty"`     // 仓库类型
	WmsCoID          int             `json:"wms_co_id,omitempty"`          // 收货仓编码
	ReceiverState    string          `json:"receiver_state,omitempty"`     // 省份
	ReceiverCity     string          `json:"receiver_city,omitempty"`      // 城市
	ReceiverDistrict string          `json:"receiver_district,omitempty"`  // 县市
	ReceiverAddress  string          `json:"receiver_address,omitempty"`   // 收货地址
	SendLcID         string          `json:"send_lc_id,omitempty"`         // 寄出快递编码
	SendLcName       string          `json:"send_lc_name,omitempty"`       // 寄出快递名称
	Items            []AfterSaleItem `json:"items"`                        // 商品列表
}

// AfterSaleResponseData 售后上传响应数据
type AfterSaleResponseData struct {
	Msg       string `json:"msg"`         // 执行结果描述
	AsID      int    `json:"as_id"`       // 内部售后单号
	IsSuccess bool   `json:"issuccess"`   // 是否成功
	SoID      string `json:"so_id"`       // 线上单号
	OuterAsID string `json:"outer_as_id"` // 外部售后单号
	OID       int    `json:"o_id"`        // 内部单号
	ID        int    `json:"id"`          // ID
	OrderType string `json:"order_type"`  // 订单类型
	OaID      string `json:"oaid"`        // OaID
}

// AfterSaleResponse 售后上传响应
type AfterSaleResponse struct {
	Code int    `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data struct {
		Datas []AfterSaleResponseData `json:"datas"` // 数据集合
	} `json:"data"` // 响应数据
}

// UploadAfterSale 售后上传
func (c *Client) UploadAfterSale(ctx context.Context, req []AfterSaleRequest) (*AfterSaleResponse, error) {
	// 参数校验
	if len(req) == 0 {
		return nil, fmt.Errorf("售后单不能为空")
	}

	// 调用接口
	body, err := c.doRequest(ctx, AfterSaleUploadAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("售后上传请求失败: %w", err)
	}

	// 解析返回结果
	var resp AfterSaleResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析售后上传响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}

	return &resp, nil
}

// AfterSaleNoInfoItem 无信息件售后商品项
type AfterSaleNoInfoItem struct {
	SkuID           string `json:"sku_id"`                     // 商家商品编码
	Qty             int    `json:"qty"`                        // 退货数量
	Amount          string `json:"amount,omitempty"`           // SKU退款金额
	Name            string `json:"name,omitempty"`             // 商品名称
	Pic             string `json:"pic,omitempty"`              // 图片路径
	PropertiesValue string `json:"properties_value,omitempty"` // 属性规格
	BatchID         string `json:"batch_id,omitempty"`         // 生产批次号
	ProducedDate    string `json:"produced_date,omitempty"`    // 生产日期
	ExpirationDate  string `json:"expiration_date,omitempty"`  // 到期日
	Type            string `json:"type"`                       // 目前仅支持"退货"
	Des             string `json:"des,omitempty"`              // 备注
}

// AfterSaleNoInfoRequest 无信息件售后上传请求
type AfterSaleNoInfoRequest struct {
	AsID             int                   `json:"as_id"`                       // 聚水潭售后单号，修改时必须大于0，新增时为0
	AsDate           string                `json:"as_date,omitempty"`           // 售后申请日期，如果为空，则为当前时间
	OuterAsID        string                `json:"outer_as_id,omitempty"`       // 外部售后单号，如果为空，聚水潭随机生成
	Type             string                `json:"type"`                        // 售后类型，目前仅支持"普通退货"
	LogisticsCompany string                `json:"logistics_company,omitempty"` // 退货快递公司
	LID              string                `json:"l_id,omitempty"`              // 退货物流单号
	ShopID           int                   `json:"shop_id"`                     // 店铺编号，0代表线下，-1代表不知道店铺
	ShopStatus       string                `json:"shop_status,omitempty"`       // 平台单据状态
	QuestionType     string                `json:"question_type,omitempty"`     // 问题类型
	QuestionReason   string                `json:"question_reason,omitempty"`   // 退款原因
	Remark           string                `json:"remark,omitempty"`            // 备注
	Freight          float64               `json:"freight,omitempty"`           // 卖家应退运费
	Items            []AfterSaleNoInfoItem `json:"items,omitempty"`             // 商品项列表
}

// AfterSaleNoInfoData 无信息件售后上传响应数据项
type AfterSaleNoInfoData struct {
	AsID      string `json:"as_id"`       // 内部单号
	OuterAsID string `json:"outer_as_id"` // 外部单号
	Success   bool   `json:"success"`     // 结果： true:成功,false:失败
	Message   string `json:"message"`     // 提示信息
}

// AfterSaleNoInfoResponse 无信息件售后上传响应
type AfterSaleNoInfoResponse struct {
	Code      int                   `json:"code"`      // 状态码
	IsSuccess bool                  `json:"issuccess"` // 执行结果
	Msg       string                `json:"msg"`       // 提示信息
	Data      []AfterSaleNoInfoData `json:"data"`      // 响应数据
}

// UploadAfterSaleNoInfo 无信息件售后上传
func (c *Client) UploadAfterSaleNoInfo(ctx context.Context, req struct {
	Data []AfterSaleNoInfoRequest `json:"data"`
}) (*AfterSaleNoInfoResponse, error) {
	// 参数校验
	if len(req.Data) == 0 {
		return nil, fmt.Errorf("售后单不能为空")
	}
	if len(req.Data) > 100 {
		return nil, fmt.Errorf("售后单最多支持100个")
	}

	// 调用接口
	body, err := c.doRequest(ctx, AfterSaleNoInfoUploadAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("无信息件售后上传请求失败: %w", err)
	}

	// 解析返回结果
	var resp AfterSaleNoInfoResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析无信息件售后上传响应失败: %w", err)
	}
	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}
	return &resp, nil
}

// RefundSingleQueryRequest 售后退货退款查询请求
type RefundSingleQueryRequest struct {
	ShopID        int      `json:"shop_id,omitempty"`         // 店铺ID
	IsOfflineShop bool     `json:"is_offline_shop,omitempty"` // shop_id为0且is_offline_shop为true查询线下店铺单据
	ModifiedBegin string   `json:"modified_begin,omitempty"`  // 修改起始时间
	ModifiedEnd   string   `json:"modified_end,omitempty"`    // 修改结束时间
	SoIDs         []string `json:"so_ids,omitempty"`          // 指定线上订单号列表
	ShopBuyerIDs  []string `json:"shop_buyer_ids,omitempty"`  // 指定买家账号列表，最多50个
	PageIndex     int      `json:"page_index,omitempty"`      // 页码，从1开始，默认1
	PageSize      int      `json:"page_size,omitempty"`       // 每页条数，默认30，最大50
	OIDs          []int    `json:"o_ids,omitempty"`           // 指定内部单号列表
	AsIDs         []int    `json:"as_ids,omitempty"`          // 售后单号列表
	Status        string   `json:"status,omitempty"`          // 售后单状态
	StartTs       int      `json:"start_ts,omitempty"`        // 时间戳，sql server中的行版本号
	IsGetTotal    string   `json:"is_get_total,omitempty"`    // 是否查询总条数，默认true
	GoodStatus    string   `json:"good_status,omitempty"`     // 货物状态
	Type          string   `json:"type,omitempty"`            // 售后类型
	OwnerCoID     string   `json:"owner_co_id,omitempty"`     // 货主编码
}

// RefundSingleQueryResponse 售后退货退款查询响应
type RefundSingleQueryResponse struct {
	Code int    `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data struct {
		PageSize  int  `json:"page_size"`  // 每页条数
		PageIndex int  `json:"page_index"` // 页码
		HasNext   bool `json:"has_next"`   // 是否有下一页
		DataCount int  `json:"data_count"` // 数据总条数
		PageCount int  `json:"page_count"` // 总页数
		Datas     []struct {
			AsID             int     `json:"as_id"`             // 售后单号
			OuterAsID        string  `json:"outer_as_id"`       // 外部售后单号
			Type             string  `json:"type"`              // 售后类型
			Status           string  `json:"status"`            // 状态
			Modified         string  `json:"modified"`          // 修改时间
			ShopID           int     `json:"shop_id"`           // 店铺ID
			ShopName         string  `json:"shop_name"`         // 店铺名称
			ShopStatus       string  `json:"shop_status"`       // 平台状态
			Remark           string  `json:"remark"`            // 备注
			GoodStatus       string  `json:"good_status"`       // 货物状态
			QuestionType     string  `json:"question_type"`     // 问题类型
			Refund           float64 `json:"refund"`            // 卖家应退金额
			Payment          float64 `json:"payment"`           // 买家应补偿金额
			Freight          float64 `json:"freight"`           // 运费
			AsDate           string  `json:"as_date"`           // 售后日期
			OID              int     `json:"o_id"`              // 内部订单号
			SoID             string  `json:"so_id"`             // 线上订单号
			LogisticsCompany string  `json:"logistics_company"` // 物流公司
			LID              string  `json:"l_id"`              // 物流单号
			Items            []struct {
				SkuID           string  `json:"sku_id"`           // 商品编码
				Name            string  `json:"name"`             // 商品名称
				Pic             string  `json:"pic"`              // 图片
				Qty             int     `json:"qty"`              // 数量
				Amount          float64 `json:"amount"`           // 金额
				PropertiesValue string  `json:"properties_value"` // 规格
				Type            string  `json:"type"`             // 类型
				Des             string  `json:"des"`              // 备注
			} `json:"items"` // 商品列表
		} `json:"datas"` // 数据集合
	} `json:"data"` // 响应数据
}

// QueryRefundSingle 售后退货退款查询
func (c *Client) QueryRefundSingle(ctx context.Context, req *RefundSingleQueryRequest) (*RefundSingleQueryResponse, error) {
	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 时间参数必须成对出现，且间隔不超过7天
	if (req.ModifiedBegin == "" && req.ModifiedEnd != "") || (req.ModifiedBegin != "" && req.ModifiedEnd == "") {
		return nil, fmt.Errorf("修改开始时间和结束时间必须同时存在")
	}

	// 如果使用时间条件查询，需要与其他条件做互斥校验
	hasTimeCondition := req.ModifiedBegin != "" && req.ModifiedEnd != ""
	hasOrderCondition := len(req.SoIDs) > 0 || len(req.OIDs) > 0 || len(req.AsIDs) > 0

	if !hasTimeCondition && !hasOrderCondition {
		return nil, fmt.Errorf("必须提供时间范围或订单号条件")
	}

	// 如果指定了买家账号，数量不能超过50
	if len(req.ShopBuyerIDs) > 50 {
		return nil, fmt.Errorf("买家账号最多支持50个")
	}

	// 调用接口
	body, err := c.doRequest(ctx, RefundSingleQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("售后退货退款查询请求失败: %w", err)
	}

	// 解析返回结果
	var resp RefundSingleQueryResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析售后退货退款查询响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}
	return &resp, nil
}

// AfterSaleConfirmGoodsItem 售后确认收到货物商品项
type AfterSaleConfirmGoodsItem struct {
	SkuID          string `json:"sku_id"`                    // 商品编码
	QtyType        int    `json:"qty_type,omitempty"`        // 数量类型(0正品，4次品，6自定义1仓，7自定义2仓，8自定义3仓)不传默认是消退仓
	Qty            int    `json:"qty"`                       // 数量
	BatchID        string `json:"batch_id,omitempty"`        // 批次号
	ProducedDate   string `json:"produced_date,omitempty"`   // 生产日期，格式"2022-01-01"
	ExpirationDate string `json:"expiration_date,omitempty"` // 到期日，格式"2022-06-01"
}

// AfterSaleConfirmGoodsRequest 售后确认收到货物请求
type AfterSaleConfirmGoodsRequest struct {
	AsID             int                         `json:"as_id"`               // 售后单号
	LogisticsCompany string                      `json:"logistics_company"`   // 快递公司
	LID              string                      `json:"l_id"`                // 快递单号
	Remark           string                      `json:"remark,omitempty"`    // 备注
	WmsCoID          int                         `json:"wms_co_id,omitempty"` // 收货仓编码
	WhID             int                         `json:"wh_id,omitempty"`     // 收货仓位；主仓 = 1, 销退仓 = 2
	ReturnID         string                      `json:"return_id"`           // 唯一单号（防止重复调用而误增加库存）
	Items            []AfterSaleConfirmGoodsItem `json:"items,omitempty"`     // 收货明细
}

// AfterSaleConfirmGoodsResponse 售后确认收到货物响应
type AfterSaleConfirmGoodsResponse struct {
	Code string `json:"code"` // 返回代码
	Msg  string `json:"msg"`  // 错误描述
	Data struct {
		Success []struct {
			AsID      int    `json:"as_id"`       // 售后单号
			OuterAsID string `json:"outer_as_id"` // 平台退货退款单号
			Message   string `json:"message"`     // 消息
			Details   string `json:"details"`     // 消息明细
			IsSuccess bool   `json:"is_success"`  // 是否成功
		} `json:"success"` // 成功明细
		Fail []struct {
			AsID      string `json:"as_id"`       // 售后单号
			OuterAsID string `json:"outer_as_id"` // 平台退货退款单号
			Message   string `json:"message"`     // 消息
			Details   string `json:"details"`     // 消息明细
			IsSuccess string `json:"is_success"`  // 是否成功
		} `json:"fail"` // 失败明细
		SuccessCount int `json:"success_count"` // 成功数量
		FailCount    int `json:"fail_count"`    // 失败数量
	} `json:"data"` // 响应数据
}

// ConfirmAfterSaleGoods 售后确认收到货物
func (c *Client) ConfirmAfterSaleGoods(ctx context.Context, reqs []AfterSaleConfirmGoodsRequest) (*AfterSaleConfirmGoodsResponse, error) {
	// 参数校验
	if len(reqs) == 0 {
		return nil, fmt.Errorf("确认收货请求不能为空")
	}

	for i, req := range reqs {
		// 校验必填参数
		if req.AsID <= 0 {
			return nil, fmt.Errorf("第%d个请求的售后单号不能为空", i+1)
		}
		if req.LogisticsCompany == "" {
			return nil, fmt.Errorf("第%d个请求的快递公司不能为空", i+1)
		}
		if req.LID == "" {
			return nil, fmt.Errorf("第%d个请求的快递单号不能为空", i+1)
		}
		if req.ReturnID == "" {
			return nil, fmt.Errorf("第%d个请求的唯一单号不能为空", i+1)
		}
		if len(req.Items) == 0 {
			return nil, fmt.Errorf("第%d个请求的收货明细不能为空", i+1)
		}

		// 校验每个收货项
		for j, item := range req.Items {
			if item.SkuID == "" {
				return nil, fmt.Errorf("第%d个请求的第%d个收货明细商品编码不能为空", i+1, j+1)
			}
			if item.Qty <= 0 {
				return nil, fmt.Errorf("第%d个请求的第%d个收货明细数量必须大于0", i+1, j+1)
			}
		}
	}

	// 调用接口
	body, err := c.doRequest(ctx, AfterSaleConfirmGoodsAPIPath, reqs)
	if err != nil {
		return nil, fmt.Errorf("售后确认收到货物请求失败: %w", err)
	}

	// 解析返回结果
	var resp AfterSaleConfirmGoodsResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析售后确认收到货物响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == "0" {
		resp.Code = "200"
	}

	return &resp, nil
}

// AfterSaleReceivedQueryRequest 实际收货查询请求
type AfterSaleReceivedQueryRequest struct {
	PageSize      int      `json:"page_size,omitempty"`      // 每页条数，默认30，最大不超过50
	ModifiedBegin string   `json:"modified_begin,omitempty"` // 修改开始时间
	ModifiedEnd   string   `json:"modified_end,omitempty"`   // 修改结束时间
	SoIDs         []string `json:"so_ids,omitempty"`         // 外部订单号列表
	StartTs       int      `json:"start_ts,omitempty"`       // 时间戳
	OIDs          []string `json:"o_ids,omitempty"`          // ERP内部订单号
	PageIndex     int      `json:"page_index,omitempty"`     // 页码，从1开始
	ShopID        string   `json:"shop_id,omitempty"`        // 退货店铺
	DateType      int      `json:"date_type,omitempty"`      // 日期类型，0:修改时间，1:创建日期，4:实际出/入库时间
	AsIDs         []int    `json:"as_ids,omitempty"`         // 售后单号列表,最大50
	IoIDs         []int    `json:"io_ids,omitempty"`         // 退仓单号列表,最大50
}

// AfterSaleReceivedQueryResponse 实际收货查询响应
type AfterSaleReceivedQueryResponse struct {
	Code int    `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data struct {
		PageSize  int  `json:"page_size"`  // 每页条数
		PageIndex int  `json:"page_index"` // 页码
		HasNext   bool `json:"has_next"`   // 是否有下一页
		DataCount int  `json:"data_count"` // 数据总条数
		PageCount int  `json:"page_count"` // 总页数
		Datas     []struct {
			IoID             int    `json:"io_id"`                    // 退仓单号（唯一值）
			OID              int    `json:"o_id"`                     // 内部单号
			SoID             string `json:"so_id"`                    // 线上单号
			AsID             int    `json:"as_id"`                    // 售后订单号
			LID              string `json:"l_id"`                     // 物流单号
			LogisticsCompany string `json:"logistics_company"`        // 物流公司名称
			Creator          int    `json:"creator"`                  // 创建人编码
			CreatorName      string `json:"creator_name"`             // 创建人名称
			IoDate           string `json:"io_date"`                  // 入库日期
			Warehouse        string `json:"warehouse"`                // 仓库名称
			Modified         string `json:"modified"`                 // 修改时间
			LcID             string `json:"lc_id"`                    // 物流公司编码
			ShopID           int    `json:"shop_id"`                  // 店铺编号
			AftersaleRemark  string `json:"aftersale_remark"`         // 售后备注
			Ts               int    `json:"ts"`                       // 时间戳
			Status           string `json:"status"`                   // 单据状态
			WhID             int    `json:"wh_id"`                    // 仓库代码（1主仓，2销退仓，3进货仓，4次品仓）
			WmsCoID          int    `json:"wms_co_id"`                // 分仓编号
			DrpCoName        string `json:"drp_co_name"`              // 分销商名称
			DrpCoIDTo        int    `json:"drp_co_id_to"`             // 分销商编号
			Type             string `json:"type"`                     // 售后类型
			Currency         string `json:"currency,omitempty"`       // 币种
			DrpCoIDFrom      int    `json:"drp_co_id_from,omitempty"` // 分销商编号
			Labels           string `json:"labels,omitempty"`         // 标签
			Items            []struct {
				IoID            int     `json:"io_id"`                    // 退仓单号
				SkuID           string  `json:"sku_id"`                   // 商品编码
				IID             string  `json:"i_id"`                     // 款式编码
				Unit            string  `json:"unit"`                     // 单位
				Qty             int     `json:"qty"`                      // 商品数量
				Name            string  `json:"name"`                     // 商品名称
				PropertiesValue string  `json:"properties_value"`         // 属性值
				SalePrice       float64 `json:"sale_price"`               // 销售价格
				SaleAmount      float64 `json:"sale_amount"`              // 销售总金额
				IoiID           int     `json:"ioi_id"`                   // 退仓子单号
				CombineSkuID    string  `json:"combine_sku_id,omitempty"` // 组合商品编码
				RawSoID         string  `json:"raw_so_id,omitempty"`      // 原始线上单号
			} `json:"items"` // 商品列表
			Batchs []struct {
				BatchNo        string `json:"batch_no"`        // 批次号
				IoiID          int    `json:"ioi_id"`          // 退仓子单号
				SkuID          string `json:"sku_id"`          // 商品编码
				Qty            int    `json:"qty"`             // 商品数量
				ProductDate    string `json:"product_date"`    // 批次日期
				SupplierID     string `json:"supplier_id"`     // 供应商编号
				SupplierName   string `json:"supplier_name"`   // 供应商名称
				ExpirationDate string `json:"expiration_date"` // 有效期至
			} `json:"batchs"` // 批次信息集合
			Sns []struct {
				SkuID string `json:"sku_id"` // 商品编码
				Sn    string `json:"sn"`     // SN码
			} `json:"sns"` // SN码集合
		} `json:"datas"` // 数据集合
	} `json:"data"` // 响应数据
}

// QueryAfterSaleReceived 实际收货查询
func (c *Client) QueryAfterSaleReceived(ctx context.Context, req *AfterSaleReceivedQueryRequest) (*AfterSaleReceivedQueryResponse, error) {
	// 参数校验
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 30
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 时间参数校验
	hasTimeCondition := req.ModifiedBegin != "" && req.ModifiedEnd != ""
	if (req.ModifiedBegin == "" && req.ModifiedEnd != "") || (req.ModifiedBegin != "" && req.ModifiedEnd == "") {
		return nil, fmt.Errorf("修改开始时间和结束时间必须同时存在")
	}

	// 查询条件校验
	hasOrderCondition := len(req.SoIDs) > 0 || len(req.OIDs) > 0 || len(req.AsIDs) > 0 || len(req.IoIDs) > 0
	if !hasTimeCondition && !hasOrderCondition && req.StartTs <= 0 && req.ShopID == "" {
		return nil, fmt.Errorf("至少需要一项查询条件")
	}

	// 如果使用售后单号列表，不能超过50个
	if len(req.AsIDs) > 50 {
		return nil, fmt.Errorf("售后单号列表最多支持50个")
	}

	// 如果使用退仓单号列表，不能超过50个
	if len(req.IoIDs) > 50 {
		return nil, fmt.Errorf("退仓单号列表最多支持50个")
	}

	// 调用接口
	body, err := c.doRequest(ctx, AfterSaleReceivedQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("实际收货查询请求失败: %w", err)
	}

	// 解析返回结果
	var resp AfterSaleReceivedQueryResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析实际收货查询响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}
	return &resp, nil
}

// AfterSaleConfirmRequest 售后单确认请求
type AfterSaleConfirmRequest struct {
	AsIDs         []int `json:"as_ids"`             // 售后单id数组
	ExchangeForce bool  `json:"exchange_force"`     // 换货售后单是否强制确认
	ConfirmRefund bool  `json:"confirm_refund"`     // 是否同步确认退款单
	NoOrder       bool  `json:"no_order,omitempty"` // 无订单标识
}

// AfterSaleConfirmResponse 售后单确认响应
type AfterSaleConfirmResponse struct {
	Code int    `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data struct {
		Success []struct {
			AsID      int    `json:"as_id"`              // 售后单号
			OuterAsID string `json:"outer_as_id"`        // 外部售后单号
			Message   string `json:"message"`            // 消息
			Details   string `json:"details"`            // 消息明细
			IsSuccess bool   `json:"is_success"`         // 是否成功
			NewOID    string `json:"new_o_id,omitempty"` // 新生成的补发的内部订单号
		} `json:"success"` // 成功明细
		Fail []struct {
			AsID      int    `json:"as_id"`       // 售后单号
			OuterAsID string `json:"outer_as_id"` // 外部售后单号
			Message   string `json:"message"`     // 消息
			Details   string `json:"details"`     // 消息明细
			IsSuccess bool   `json:"is_success"`  // 是否成功
		} `json:"fail"` // 失败明细
		SuccessCount int `json:"success_count"` // 成功数量
		FailCount    int `json:"fail_count"`    // 失败数量
	} `json:"data"` // 响应数据
}

// ConfirmAfterSale 售后单确认
func (c *Client) ConfirmAfterSale(ctx context.Context, req *AfterSaleConfirmRequest) (*AfterSaleConfirmResponse, error) {
	// 参数校验
	if len(req.AsIDs) == 0 {
		return nil, fmt.Errorf("售后单id数组不能为空")
	}

	// 调用接口
	body, err := c.doRequest(ctx, AfterSaleConfirmAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("售后单确认请求失败: %w", err)
	}

	// 解析返回结果
	var resp AfterSaleConfirmResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析售后单确认响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}
	return &resp, nil
}

// AfterSaleUnconfirmRequest 售后单反确认请求
type AfterSaleUnconfirmRequest struct {
	AsIDs []int `json:"as_ids"` // 售后单id数组
}

// AfterSaleUnconfirmResponse 售后单反确认响应
type AfterSaleUnconfirmResponse struct {
	Code int    `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data struct {
		Success []struct {
			AsID      int    `json:"as_id"`       // 售后单号
			OuterAsID string `json:"outer_as_id"` // 外部售后单号
			Message   string `json:"message"`     // 消息
			Details   string `json:"details"`     // 消息明细
			IsSuccess bool   `json:"is_success"`  // 是否成功
		} `json:"success"` // 成功明细
		Fail []struct {
			AsID      int    `json:"as_id"`       // 售后单号
			OuterAsID string `json:"outer_as_id"` // 外部售后单号
			Message   string `json:"message"`     // 消息
			Details   string `json:"details"`     // 消息明细
			IsSuccess bool   `json:"is_success"`  // 是否成功
		} `json:"fail"` // 失败明细
		SuccessCount int `json:"success_count"` // 成功数量
		FailCount    int `json:"fail_count"`    // 失败数量
	} `json:"data"` // 响应数据
}

// UnconfirmAfterSale 售后单反确认
func (c *Client) UnconfirmAfterSale(ctx context.Context, req *AfterSaleUnconfirmRequest) (*AfterSaleUnconfirmResponse, error) {
	// 参数校验
	if len(req.AsIDs) == 0 {
		return nil, fmt.Errorf("售后单id数组不能为空")
	}

	// 调用接口
	body, err := c.doRequest(ctx, AfterSaleUnconfirmAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("售后单反确认请求失败: %w", err)
	}

	// 解析返回结果
	var resp AfterSaleUnconfirmResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析售后单反确认响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}
	return &resp, nil
}

// AfterSaleConfirmBySnItem 唯一码批量确认收货项
type AfterSaleConfirmBySnItem struct {
	SkuSn   string `json:"sku_sn"`              // 唯一码
	Remark  string `json:"remark,omitempty"`    // 追加备注
	WmsCoID int    `json:"wms_co_id,omitempty"` // 分仓编码
}

// AfterSaleConfirmBySnsResponse 唯一码批量确认收货响应
type AfterSaleConfirmBySnsResponse struct {
	Code int    `json:"code"` // 错误码，0表示成功
	Msg  string `json:"msg"`  // 执行返回信息
	Data struct {
		Success []struct {
			AsID      int    `json:"as_id"`       // 内部售后单号
			OuterAsID string `json:"outer_as_id"` // 外部售后单号
			SkuSns    string `json:"sku_sns"`     // 唯一码
			Code      int    `json:"code"`        // 返回码
			Message   string `json:"message"`     // 返回信息
			Details   string `json:"details"`     // 详细信息
			IsSuccess bool   `json:"is_success"`  // 是否成功
		} `json:"success"` // 成功详情
		Fail []struct {
			AsID      int    `json:"as_id"`       // 内部售后单号
			OuterAsID string `json:"outer_as_id"` // 外部售后单号
			SkuSns    string `json:"sku_sns"`     // 唯一码
			Code      int    `json:"code"`        // 返回码
			Message   string `json:"message"`     // 返回信息
			Details   string `json:"details"`     // 详细信息
			IsSuccess bool   `json:"is_success"`  // 是否成功
		} `json:"fail"` // 失败详情
		SuccessCount int `json:"success_count"` // 成功数量
		FailCount    int `json:"fail_count"`    // 失败数量
	} `json:"data"` // 响应数据
}

// ConfirmAfterSaleBySns 唯一码批量确认收货
func (c *Client) ConfirmAfterSaleBySns(ctx context.Context, items []AfterSaleConfirmBySnItem) (*AfterSaleConfirmBySnsResponse, error) {
	// 参数校验
	if len(items) == 0 {
		return nil, fmt.Errorf("唯一码列表不能为空")
	}

	// 校验唯一码
	for i, item := range items {
		if item.SkuSn == "" {
			return nil, fmt.Errorf("第%d个唯一码不能为空", i+1)
		}
	}

	// 调用接口
	body, err := c.doRequest(ctx, AfterSaleConfirmBySnsAPIPath, items)
	if err != nil {
		return nil, fmt.Errorf("唯一码批量确认收货请求失败: %w", err)
	}

	// 解析返回结果
	var resp AfterSaleConfirmBySnsResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析唯一码批量确认收货响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}
	return &resp, nil
}

// RefundQueryRequest 退款单查询请求
type RefundQueryRequest struct {
	ShopID        int    `json:"shop_id,omitempty"`    // 店铺编号
	IsArchive     bool   `json:"is_archive,omitempty"` // 是否归档
	ModifiedBegin string `json:"modified_begin"`       // 修改起始时间
	ModifiedEnd   string `json:"modified_end"`         // 修改结束时间（时间跨度不超过6个月）
	StartTs       int    `json:"start_ts,omitempty"`   // 开始ts,用于辅助分页
	PageIndex     int    `json:"page_index,omitempty"` // 第几页（默认1，最大800）
	PageSize      int    `json:"page_size,omitempty"`  // 页大小（默认1，最大1000）
}

// RefundQueryResponse 退款单查询响应
type RefundQueryResponse struct {
	Code int    `json:"code"` // 错误码
	Msg  string `json:"msg"`  // 错误描述
	Data struct {
		PageSize  int `json:"page_size"`  // 每页条数
		PageIndex int `json:"page_index"` // 页码
		PageCount int `json:"page_count"` // 总页数
		DataCount int `json:"data_count"` // 数据总条数
		Datas     []struct {
			ShopID      int     `json:"shop_id"`        // 店铺编码
			AsID        int     `json:"as_id"`          // 售后单
			OID         int     `json:"o_id"`           // 内部订单号
			PayID       int     `json:"pay_id"`         // 退款单号
			PayDate     string  `json:"pay_date"`       // 退款时间
			ConfirmDate string  `json:"confirm_date"`   // 审核时间
			Modified    string  `json:"modified"`       // 修改时间
			Amount      float64 `json:"amount"`         // 原订单已付金额
			Status      string  `json:"status"`         // 状态
			Payment     string  `json:"payment"`        // 退款方式
			DrpCoIDFrom int     `json:"drp_co_id_from"` // 分销商id
			Ts          int     `json:"ts"`             // 分页用时间戳
		} `json:"datas"` // 数据集合
	} `json:"data"` // 响应数据
}

// QueryRefund 退款单查询
func (c *Client) QueryRefund(ctx context.Context, req *RefundQueryRequest) (*RefundQueryResponse, error) {
	// 参数校验
	if req.ModifiedBegin == "" || req.ModifiedEnd == "" {
		return nil, fmt.Errorf("修改开始时间和结束时间不能为空")
	}

	// 页码参数处理
	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}
	if req.PageIndex > 800 {
		req.PageIndex = 800
	}
	if req.PageSize <= 0 {
		req.PageSize = 1
	}
	if req.PageSize > 1000 {
		req.PageSize = 1000
	}

	// 调用接口
	body, err := c.doRequest(ctx, RefundQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("退款单查询请求失败: %w", err)
	}

	// 解析返回结果
	var resp RefundQueryResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析退款单查询响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}
	return &resp, nil
}
