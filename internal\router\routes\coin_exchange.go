package routes

import (
	"net/http"

	"yekaitai/internal/middleware"
	"yekaitai/internal/modules/coin_exchange/handler"
	"yekaitai/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

// RegisterCoinExchangeRoutes 注册积分兑换相关路由
func RegisterCoinExchangeRoutes(server RestServer, serverCtx *svc.ServiceContext) {
	// 初始化兑换配置处理器
	exchangeHandler := handler.NewExchangeGoZeroHandler()

	// 使用管理员认证中间件
	adminAuthWrapper := func(next http.HandlerFunc) http.HandlerFunc {
		return middleware.AdminAuthMiddleware(serverCtx)(next).ServeHTTP
	}

	// 兑换配置路由
	server.AddRoutes(
		[]rest.Route{
			// 获取兑换配置列表
			{
				Method:  http.MethodGet,
				Path:    "/api/exchange/configs",
				Handler: adminAuthWrapper(exchangeHandler.ListConfigs),
			},
			// 创建兑换配置
			{
				Method:  http.MethodPost,
				Path:    "/api/exchange/configs",
				Handler: adminAuthWrapper(exchangeHandler.CreateConfig),
			},
			// 删除兑换配置
			{
				Method:  http.MethodDelete,
				Path:    "/api/exchange/configs/:id",
				Handler: adminAuthWrapper(exchangeHandler.DeleteConfig),
			},
			// 获取可用的服务套餐列表
			// {
			// 	Method:  http.MethodGet,
			// 	Path:    "/api/exchange/service-packages",
			// 	Handler: adminAuthWrapper(exchangeHandler.GetAvailableServicePackages),
			// },
		},
	)
}
