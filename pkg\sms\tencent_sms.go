package sms

import (
	"fmt"
	"strings"
	"yekaitai/internal/config"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	sms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sms/v20210111"
	"github.com/zeromicro/go-zero/core/logx"
)

// TencentSmsClient 腾讯云短信客户端
type TencentSmsClient struct {
	client     *sms.Client
	config     config.TencentSmsConfig
	sdkAppId   string
	signName   string
	templateId string
}

// NewTencentSmsClient 创建腾讯云短信客户端
func NewTencentSmsClient(config config.TencentSmsConfig) (*TencentSmsClient, error) {
	// 必要参数检查
	if config.SecretId == "" || config.SecretKey == "" || config.SdkAppId == "" {
		return nil, fmt.Errorf("腾讯云短信配置不完整")
	}

	// 设置默认地区
	if config.Region == "" {
		config.Region = "ap-guangzhou"
	}

	// 创建认证对象
	credential := common.NewCredential(
		config.SecretId,
		config.SecretKey,
	)

	// 创建客户端配置
	clientProfile := profile.NewClientProfile()
	clientProfile.HttpProfile.Endpoint = "sms.tencentcloudapi.com"

	// 创建客户端
	client, err := sms.NewClient(credential, config.Region, clientProfile)
	if err != nil {
		return nil, fmt.Errorf("创建腾讯云短信客户端失败: %v", err)
	}

	return &TencentSmsClient{
		client:     client,
		config:     config,
		sdkAppId:   config.SdkAppId,
		signName:   config.SignName,
		templateId: config.TemplateId,
	}, nil
}

// ValidateConfig 验证配置
func (c *TencentSmsClient) ValidateConfig() error {
	if c.config.SecretId == "" || c.config.SecretKey == "" || c.config.SdkAppId == "" {
		return fmt.Errorf("腾讯云短信配置不完整")
	}
	
	if c.config.SignName == "" {
		return fmt.Errorf("短信签名不能为空")
	}
	
	if c.config.TemplateId == "" {
		return fmt.Errorf("短信模板ID不能为空")
	}
	
	return nil
}

// SendSms 发送短信
func (c *TencentSmsClient) SendSms(phoneNumber, code string) error {
	// 创建请求对象
	request := sms.NewSendSmsRequest()
	
	// 设置短信应用ID
	request.SmsSdkAppId = common.StringPtr(c.sdkAppId)
	
	// 设置签名
	request.SignName = common.StringPtr(c.signName)
	
	// 设置模板ID
	request.TemplateId = common.StringPtr(c.templateId)
	
	// 设置模板参数（验证码）
	request.TemplateParamSet = common.StringPtrs([]string{code})
	
	// 处理手机号，确保符合E.164标准格式
	formattedPhone := formatPhoneNumber(phoneNumber)
	request.PhoneNumberSet = common.StringPtrs([]string{formattedPhone})
	
	// 发送短信
	response, err := c.client.SendSms(request)
	if err != nil {
		sdkErr, ok := err.(*errors.TencentCloudSDKError)
		if ok {
			logx.Errorf("腾讯云短信发送失败，错误码: %s，错误信息: %s", sdkErr.Code, sdkErr.Message)
			return fmt.Errorf("短信发送失败: %s", sdkErr.Message)
		}
		return fmt.Errorf("短信发送失败: %v", err)
	}
	
	// 检查发送状态
	if len(response.Response.SendStatusSet) > 0 {
		status := response.Response.SendStatusSet[0]
		if *status.Code != "Ok" {
			return fmt.Errorf("短信发送失败: %s", *status.Message)
		}
	} else {
		return fmt.Errorf("短信发送失败: 未收到发送状态")
	}
	
	return nil
}

// formatPhoneNumber 格式化手机号为E.164标准格式
func formatPhoneNumber(phoneNumber string) string {
	// 移除所有空格
	phoneNumber = strings.ReplaceAll(phoneNumber, " ", "")
	
	// 如果手机号不是以+开头，且是11位数字（中国大陆手机号），则添加+86前缀
	if !strings.HasPrefix(phoneNumber, "+") {
		if len(phoneNumber) == 11 && strings.HasPrefix(phoneNumber, "1") {
			return "+86" + phoneNumber
		}
	}
	
	return phoneNumber
}

// SendBatchSms 批量发送短信
func (t *TencentSmsClient) SendBatchSms(phoneNumbers []string, templateParams []string) error {
	// 实例化一个请求对象
	request := sms.NewSendSmsRequest()

	// 短信应用ID
	request.SmsSdkAppId = common.StringPtr(t.config.SdkAppId)

	// 短信签名内容
	request.SignName = common.StringPtr(t.config.SignName)

	// 模板ID
	request.TemplateId = common.StringPtr(t.config.TemplateId)

	// 格式化手机号
	formattedPhones := make([]string, len(phoneNumbers))
	for i, phone := range phoneNumbers {
		if len(phone) == 11 && phone[0] == '1' {
			formattedPhones[i] = "+86" + phone
		} else if phone[0] != '+' {
			formattedPhones[i] = "+86" + phone
		} else {
			formattedPhones[i] = phone
		}
	}

	request.PhoneNumberSet = common.StringPtrs(formattedPhones)

	// 模板参数
	request.TemplateParamSet = common.StringPtrs(templateParams)

	// 发送短信
	response, err := t.client.SendSms(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		return fmt.Errorf("腾讯云SDK错误: %v", err)
	}
	if err != nil {
		return fmt.Errorf("批量发送短信失败: %v", err)
	}

	// 检查发送状态
	for i, status := range response.Response.SendStatusSet {
		if *status.Code != "Ok" {
			return fmt.Errorf("手机号 %s 短信发送失败: %s - %s", 
				formattedPhones[i], *status.Code, *status.Message)
		}
	}

	return nil
}
