package abcyun

import (
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// AbcYunSyncService ABC云同步服务
type AbcYunSyncService struct {
	client    *AbcYunClient // ABC云客户端
	enabled   bool          // 是否启用同步
	interval  int           // 同步间隔（小时）
	ticker    *time.Ticker  // 定时器
	stopChan  chan struct{} // 停止信号通道
	isRunning bool          // 是否正在运行
	lock      sync.Mutex    // 互斥锁
}

// NewAbcYunSyncService 创建ABC云同步服务
func NewAbcYunSyncService(client *AbcYunClient, enabled bool, interval int) *AbcYunSyncService {
	// 默认1小时同步一次
	if interval <= 0 {
		interval = 1
	}

	return &AbcYunSyncService{
		client:    client,
		enabled:   enabled,
		interval:  interval,
		stopChan:  make(chan struct{}),
		isRunning: false,
	}
}

// Start 启动同步服务
func (s *AbcYunSyncService) Start() error {
	s.lock.Lock()
	defer s.lock.Unlock()

	if s.isRunning {
		logx.Info("ABC云同步服务已经在运行中")
		return nil
	}

	if !s.enabled {
		logx.Info("ABC云同步服务已禁用，不会同步数据")
		return nil
	}

	s.isRunning = true
	s.ticker = time.NewTicker(time.Duration(s.interval) * time.Hour)

	go func() {
		// 立即执行一次同步
		s.syncAll()

		for {
			select {
			case <-s.ticker.C:
				s.syncAll()
			case <-s.stopChan:
				s.ticker.Stop()
				return
			}
		}
	}()

	logx.Infof("ABC云同步服务已启动，每%d小时同步一次", s.interval)
	return nil
}

// Stop 停止同步服务
func (s *AbcYunSyncService) Stop() {
	s.lock.Lock()
	defer s.lock.Unlock()

	if !s.isRunning {
		return
	}

	close(s.stopChan)
	s.isRunning = false
	logx.Info("ABC云同步服务已停止")
}

// syncAll 同步所有数据
func (s *AbcYunSyncService) syncAll() {
	logx.Info("开始ABC云数据同步")

	// TODO: 同步门店信息
	// TODO: 同步科室信息
	// TODO: 同步医生信息

	logx.Info("ABC云数据同步完成")
}
