package jushuitan

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	jsoniter "github.com/json-iterator/go"
)

// API路径常量已在api_paths.go中定义

// UploadOrders 上传订单(商家自有商城)
func (c *Client) UploadOrders(ctx context.Context, orders []*OrderUploadRequest) (*OrderUploadResponse, error) {
	if len(orders) > 50 {
		return nil, fmt.Erro<PERSON>("一次最多上传50个订单")
	}

	// 准备API路径
	path := OrderUploadAPIPath

	// 数据预处理和验证
	for _, order := range orders {
		// 验证必填字段
		if order.ShopID == 0 {
			return nil, fmt.Errorf("店铺编号不能为空")
		}
		if order.SoID == "" {
			return nil, fmt.Errorf("自研商城系统订单号不能为空")
		}
		if order.OrderDate == "" {
			return nil, fmt.Errorf("订单日期不能为空")
		}
		if order.ShopStatus == "" {
			return nil, fmt.Errorf("自研商城系统订单状态不能为空")
		}
		if order.ShopBuyerID == "" {
			return nil, fmt.Errorf("买家帐号不能为空")
		}
		if order.ReceiverAddress == "" {
			return nil, fmt.Errorf("收货地址不能为空")
		}
		if order.ReceiverName == "" {
			return nil, fmt.Errorf("收件人不能为空")
		}
		if len(order.Items) == 0 {
			return nil, fmt.Errorf("商品明细不能为空")
		}

		// 格式化时间
		if order.OrderDate != "" {
			t, err := time.Parse("2006-01-02 15:04:05", order.OrderDate)
			if err == nil {
				order.OrderDate = t.Format("2006-01-02 15:04:05")
			}
		}
		if order.ShopModified != "" {
			t, err := time.Parse("2006-01-02 15:04:05", order.ShopModified)
			if err == nil {
				order.ShopModified = t.Format("2006-01-02 15:04:05")
			}
		}
		if order.PlanDeliveryDate != "" {
			t, err := time.Parse("2006-01-02 15:04:05", order.PlanDeliveryDate)
			if err == nil {
				order.PlanDeliveryDate = t.Format("2006-01-02 15:04:05")
			}
		}
		if order.SendDate != "" {
			t, err := time.Parse("2006-01-02 15:04:05", order.SendDate)
			if err == nil {
				order.SendDate = t.Format("2006-01-02 15:04:05")
			}
		}

		// 验证支付信息
		if order.ShopStatus != "WAIT_BUYER_PAY" {
			if order.Pay == nil {
				return nil, fmt.Errorf("订单状态非WAIT_BUYER_PAY时，支付信息必填")
			}
			if order.Pay.OuterPayID == "" {
				return nil, fmt.Errorf("外部支付单号不能为空")
			}
			if order.Pay.PayDate == "" {
				return nil, fmt.Errorf("支付日期不能为空")
			}
			if order.Pay.Payment == "" {
				return nil, fmt.Errorf("支付方式不能为空")
			}
			if order.Pay.SellerAccount == "" {
				return nil, fmt.Errorf("收款账户不能为空")
			}
			if order.Pay.BuyerAccount == "" {
				return nil, fmt.Errorf("买家支付账号不能为空")
			}

			// 格式化支付时间
			if order.Pay.PayDate != "" {
				t, err := time.Parse("2006-01-02 15:04:05", order.Pay.PayDate)
				if err == nil {
					order.Pay.PayDate = t.Format("2006-01-02 15:04:05")
				}
			}
		}

		// 验证商品信息
		for _, item := range order.Items {
			if item.SkuID == "" {
				return nil, fmt.Errorf("商品编码不能为空")
			}
			if item.ShopSkuID == "" {
				return nil, fmt.Errorf("店铺商品编码不能为空")
			}
			if item.Name == "" {
				return nil, fmt.Errorf("商品名称不能为空")
			}
			if item.OuterOiID == "" {
				return nil, fmt.Errorf("商家系统订单商品明细主键不能为空")
			}
		}

		// 验证身份证信息
		if order.Card != nil {
			if order.Card.Name == "" {
				return nil, fmt.Errorf("身份证姓名不能为空")
			}
			if order.Card.CardID == "" {
				return nil, fmt.Errorf("身份证号不能为空")
			}
		}
	}

	// 发送请求
	resp, err := c.Request(ctx, path, orders)
	if err != nil {
		return nil, fmt.Errorf("上传订单请求失败: %w", err)
	}

	// 解析响应
	var result OrderUploadResponse
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析上传订单响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// QueryOrder 查询订单
func (c *Client) QueryOrder(ctx context.Context, req *OrderQueryRequest) (*BaseResponse, error) {
	// 特殊处理订单号格式
	var oIDs []string
	for _, id := range req.OIDs {
		oIDs = append(oIDs, strconv.Itoa(id))
	}

	// 构造请求参数
	params := url.Values{}
	b, err := jsoniter.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化订单查询请求失败: %v", err)
	}

	var m map[string]interface{}
	if err := jsoniter.Unmarshal(b, &m); err != nil {
		return nil, fmt.Errorf("反序列化订单查询请求失败: %v", err)
	}

	for k, v := range m {
		if v == nil {
			continue
		}
		switch k {
		case "o_ids":
			continue // 特殊处理
		case "so_ids", "cb_ids":
			if arr, ok := v.([]interface{}); ok && len(arr) > 0 {
				var values []string
				for _, item := range arr {
					if str, ok := item.(string); ok {
						values = append(values, str)
					} else if num, ok := item.(float64); ok {
						values = append(values, strconv.FormatFloat(num, 'f', 0, 64))
					}
				}
				params.Set(k, "["+strings.Join(values, ",")+"]")
			}
		case "biz":
			if str, ok := v.(string); ok {
				params.Set(k, str)
			}
		default:
			// 根据值的类型进行处理
			switch val := v.(type) {
			case string:
				if val != "" {
					params.Set(k, val)
				}
			case int, int32, int64, float32, float64:
				if val != 0 {
					params.Set(k, fmt.Sprintf("%v", val))
				}
			case bool:
				params.Set(k, strconv.FormatBool(val))
			}
		}
	}

	// 特殊处理o_ids
	if len(oIDs) > 0 {
		params.Set("o_ids", "["+strings.Join(oIDs, ",")+"]")
	}

	// 发送请求
	resp, err := c.Request(ctx, OrderQueryAPIPath, req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}

	// 解析响应
	var result BaseResponse
	if err := jsoniter.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 标准化响应码: 0表示成功，且需要设置issuccess为true
	if result.Code == 0 {
		result.Code = 200
		// 处理特殊字段issuccess
		if resultData, ok := result.Data.(map[string]interface{}); ok {
			resultData["issuccess"] = true
			result.Data = resultData
		}
	}

	return &result, nil
}

// CancelOrder 取消订单
func (c *Client) CancelOrder(ctx context.Context, req *OrderCancelRequest) (*OrderCancelResponse, error) {
	// 准备API路径
	path := OrderCancelAPIPath

	// 验证参数
	if len(req.OIDs) == 0 {
		return nil, fmt.Errorf("订单号列表不能为空")
	}
	if len(req.OIDs) > 50 {
		return nil, fmt.Errorf("一次最多支持取消50个订单")
	}
	if req.CancelType == "" {
		return nil, fmt.Errorf("取消类型不能为空")
	}

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("取消订单请求失败: %w", err)
	}

	// 解析响应
	var result OrderCancelResponse
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析取消订单响应失败: %w", err)
	}

	// 将code为0的值转换为200
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// ModifyOrderWMS 订单指定发货仓
func (c *Client) ModifyOrderWMS(ctx context.Context, reqs []*OrderModifyWMSRequest) (*OrderModifyWMSResponse, error) {
	// 准备API路径
	path := OrderModifyWMSAPIPath

	// 验证参数
	if len(reqs) == 0 {
		return nil, fmt.Errorf("请求列表不能为空")
	}

	// 验证每个请求项的参数
	for _, req := range reqs {
		if req.OID == 0 {
			return nil, fmt.Errorf("订单号不能为空")
		}
		if req.WmsCoID == 0 {
			return nil, fmt.Errorf("仓库编码不能为空")
		}
	}

	// 发送请求
	resp, err := c.Request(ctx, path, reqs)
	if err != nil {
		return nil, fmt.Errorf("指定发货仓请求失败: %w", err)
	}

	// 解析响应
	var result OrderModifyWMSResponse
	if err := jsoniter_.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("解析指定发货仓响应失败: %w", err)
	}

	return &result, nil
}

// OrderSplitRequest 订单拆分请求结构
type OrderSplitRequest struct {
	OID        string            `json:"o_id"`        // 内部订单号
	SplitInfos [][]SplitItemInfo `json:"split_infos"` // 拆分信息数组
}

// SplitItemInfo 拆分项信息
type SplitItemInfo struct {
	OIID string `json:"oi_id"` // 订单明细号
	Qty  int    `json:"qty"`   // 拆分数量
}

// OrderSplitResponse 订单拆分响应
type OrderSplitResponse struct {
	Data      []int  `json:"data"`      // 拆分后的内部订单号
	Code      int    `json:"code"`      // 返回码，0表示成功
	IsSuccess bool   `json:"issuccess"` // 是否执行成功
	Msg       string `json:"msg"`       // 返回信息
}

// SplitOrder 订单拆分
func (c *Client) SplitOrder(ctx context.Context, req *OrderSplitRequest) (*OrderSplitResponse, error) {
	// 验证必填字段
	if req.OID == "" {
		return nil, errors.New("内部订单号不能为空")
	}
	if len(req.SplitInfos) == 0 {
		return nil, errors.New("拆分信息不能为空")
	}

	// 验证每个拆分项
	for i, group := range req.SplitInfos {
		if len(group) == 0 {
			return nil, fmt.Errorf("拆分组 %d 不能为空", i+1)
		}
		for j, item := range group {
			if item.OIID == "" {
				return nil, fmt.Errorf("拆分组 %d 项 %d 的订单明细号不能为空", i+1, j+1)
			}
			if item.Qty <= 0 {
				return nil, fmt.Errorf("拆分组 %d 项 %d 的拆分数量必须大于0", i+1, j+1)
			}
		}
	}

	// 准备API路径
	path := OrderSplitAPIPath

	// 调用接口
	body, err := c.doRequest(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("订单拆分请求失败: %w", err)
	}

	// 解析返回结果
	var resp OrderSplitResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析订单拆分响应失败: %w", err)
	}

	// 转换响应码
	if resp.Code == 0 {
		resp.Code = 200
	}

	return &resp, nil
}

// SetOrderNode 修改订单线下备注
func (c *Client) SetOrderNode(ctx context.Context, req *OrderNodeRequest) (*OrderNodeResponse, error) {
	// 验证必填字段
	if req.ShopID == 0 {
		return nil, errors.New("店铺编号不能为空")
	}
	if len(req.Items) == 0 {
		return nil, errors.New("订单列表不能为空")
	}
	if len(req.Items) > 10 {
		return nil, errors.New("一次最多支持10个订单")
	}

	for _, item := range req.Items {
		if item.OID == 0 && item.SoID == "" {
			return nil, errors.New("内部订单号和线上单号不能同时为空")
		}
		if item.Node == "" {
			return nil, errors.New("线下备注不能为空")
		}
	}

	// 准备API路径
	path := OrderNodeSetAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var result OrderNodeResponse
	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	// 转换响应码
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// UploadOrderLabel 修改订单标签
func (c *Client) UploadOrderLabel(ctx context.Context, req *OrderLabelRequest) (*OrderLabelResponse, error) {
	// 验证必填字段
	if req.OID == 0 && (req.ShopID == 0 || req.SoID == "") {
		return nil, errors.New("内部订单号和(店铺编号,线上单号)组合不能同时为空")
	}
	if len(req.Labels) == 0 || len(req.Labels) > 20 {
		return nil, errors.New("标签列表数量必须大于0且小于20")
	}
	if req.ActionType != 1 && req.ActionType != 2 {
		return nil, errors.New("操作类型不正确，1=新增，2=移除")
	}

	// 验证标签长度
	for _, label := range req.Labels {
		if len(label) > 10 {
			return nil, errors.New("单个标签长度不能超过10")
		}
	}

	// 验证标签是否重复
	labelMap := make(map[string]bool)
	for _, label := range req.Labels {
		if labelMap[label] {
			return nil, errors.New("标签不能重复")
		}
		labelMap[label] = true
	}

	// 准备API路径
	path := OrderLabelUploadAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var result OrderLabelResponse
	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	// 转换响应码
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// UploadOrderSent 订单发货
func (c *Client) UploadOrderSent(ctx context.Context, req *OrderSentRequest) (*OrderSentResponse, error) {
	// 验证必填字段
	if len(req.Items) == 0 {
		return nil, errors.New("订单列表不能为空")
	}
	if len(req.Items) > 50 {
		return nil, errors.New("一次最多支持上传50条")
	}

	for _, item := range req.Items {
		if item.OID == 0 {
			return nil, errors.New("内部订单号不能为空")
		}
		if item.ShopID == 0 {
			return nil, errors.New("店铺编号不能为空")
		}
		if item.SoID == "" {
			return nil, errors.New("线上单号不能为空")
		}
		if item.LcName == "" {
			return nil, errors.New("快递公司不能为空")
		}
		if item.LID == "" {
			return nil, errors.New("快递单号不能为空")
		}
		if item.LcID == "" {
			return nil, errors.New("快递公司编码不能为空")
		}
		if item.IsUnLid && (item.TrackingCode == "" || item.TrackingInfo == "") {
			return nil, errors.New("跨境物流时，渠道编码和渠道名称不能为空")
		}
	}

	// 准备API路径
	path := OrderSentUploadAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	// 无论成功失败都尝试解析响应
	var result OrderSentResponse
	if jsonErr := json.Unmarshal(resp, &result); jsonErr == nil {
		// 转换响应码
		if result.Code == 0 {
			result.Code = 200
		}

		// 如果有业务错误，返回解析后的响应和错误
		if err != nil {
			return &result, err
		}

		// 如果成功，返回解析后的响应
		return &result, nil
	}

	// 如果解析失败，并且有请求错误，直接返回请求错误
	if err != nil {
		return nil, err
	}

	// 如果解析失败，但请求成功，返回解析错误
	return nil, fmt.Errorf("解析响应失败: %v", json.Unmarshal(resp, &result))
}

// QueryOrderAction 订单操作日志查询
func (c *Client) QueryOrderAction(ctx context.Context, req *OrderActionQueryRequest) (*OrderActionQueryResponse, error) {
	// 验证必填字段
	if req.ActionName == "" {
		return nil, errors.New("操作类型不能为空")
	}

	// 验证时间范围
	if (req.ModifiedBegin != "" || req.ModifiedEnd != "") && (req.ModifiedBegin == "" || req.ModifiedEnd == "") {
		return nil, errors.New("起始时间和结束时间必须同时存在")
	}

	// 验证分页参数
	if req.PageSize > 500 {
		req.PageSize = 500 // 最大不超过500
	}
	if req.PageSize <= 0 {
		req.PageSize = 50 // 默认50
	}
	if req.PageIndex <= 0 {
		req.PageIndex = 1 // 默认第1页
	}

	// 准备API路径
	path := OrderActionQueryAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var result OrderActionQueryResponse
	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	// 转换响应码
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// SetOrderException 将订单转为异常状态
func (c *Client) SetOrderException(ctx context.Context, req *OrderExceptionRequest) (*OrderExceptionResponse, error) {
	// 验证必填字段
	if req.ShopID == 0 {
		return nil, errors.New("店铺编号不能为空")
	}
	if req.SoID == "" {
		return nil, errors.New("订单编号不能为空")
	}
	if req.Exception == "" {
		return nil, errors.New("异常原因不能为空")
	}
	if req.ExceptionType < 1 || req.ExceptionType > 4 {
		return nil, errors.New("异常类型不正确")
	}

	// 准备API路径
	path := OrderQuestionAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var result OrderExceptionResponse
	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	// 转换响应码
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// UpdateOrderRemark 修改订单卖家备注
func (c *Client) UpdateOrderRemark(ctx context.Context, req *OrderRemarkRequest) (*OrderRemarkResponse, error) {
	// 验证必填字段
	if req.ShopID == 0 {
		return nil, errors.New("店铺编号不能为空")
	}
	if req.SoID == "" {
		return nil, errors.New("订单编号不能为空")
	}
	if req.Remark == "" {
		return nil, errors.New("卖家备注不能为空")
	}

	// 准备API路径
	path := OrderRemarkUploadAPIPath

	// 发送请求
	resp, err := c.Request(ctx, path, req)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var result OrderRemarkResponse
	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	// 转换响应码
	if result.Code == 0 {
		result.Code = 200
	}

	return &result, nil
}

// OrderQuestionRequest 订单转异常请求
type OrderQuestionRequest struct {
	OIDs         []int    `json:"o_ids,omitempty"`         // 内部订单号列表（与so_ids二选一必填），最大50个
	SoIDs        []string `json:"so_ids,omitempty"`        // 线上单号列表（与o_ids二选一必填），最大50个
	QuestionType string   `json:"question_type"`           // 异常类型，必填
	QuestionDesc string   `json:"question_desc,omitempty"` // 异常描述，可选
	Channel      string   `json:"channel,omitempty"`       // 操作人，记录在订单详情日志上，不填写默认open
}

// OrderQuestionResponse 订单转异常响应
type OrderQuestionResponse struct {
	Code int                   `json:"code"`
	Msg  string                `json:"msg"`
	Data OrderQuestionRespData `json:"data"`
}

// OrderQuestionRespData 订单转异常响应数据
type OrderQuestionRespData struct {
	Success int                     `json:"success"` // Die成功数量
	Fail    int                     `json:"fail"`    // 失败数量
	Message []OrderQuestionRespItem `json:"message"` // 转异常失败信息
}

// OrderQuestionRespItem 订单转异常响应项
type OrderQuestionRespItem struct {
	ID  int    `json:"id"`  // 失败订单编号
	Msg string `json:"msg"` // 失败信息
}

// SetOrderQuestion 订单转异常
func (c *Client) SetOrderQuestion(ctx context.Context, req *OrderQuestionRequest) (*OrderQuestionResponse, error) {
	path := "/open/webapi/orderapi/questionorder/questions"

	// 调用接口
	body, err := c.doRequest(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("订单转异常请求失败: %w", err)
	}

	// 解析返回结果
	var resp OrderQuestionResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析订单转异常响应失败: %w", err)
	}

	return &resp, nil
}

// OrderRemarkSaveRequest 修改订单卖家备注请求
type OrderRemarkSaveRequest struct {
	SoID     string `json:"so_id"`     // 线上单号，必填
	Remark   string `json:"remark"`    // 备注，必填
	IsAppend bool   `json:"is_append"` // 是否追加，必填
}

// OrderRemarkSaveResponse 修改订单卖家备注响应
type OrderRemarkSaveResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// SaveOrderRemark 修改订单卖家备注
func (c *Client) SaveOrderRemark(ctx context.Context, req *OrderRemarkSaveRequest) (*OrderRemarkSaveResponse, error) {
	path := "/open/webapi/orderapi/modifyorder/saveremark"

	// 验证必填字段
	if req.SoID == "" {
		return nil, fmt.Errorf("线上单号不能为空")
	}
	if req.Remark == "" {
		return nil, fmt.Errorf("备注不能为空")
	}

	// 调用接口
	body, err := c.doRequest(ctx, path, req)
	if err != nil {
		return nil, fmt.Errorf("修改订单卖家备注请求失败: %w", err)
	}

	// 解析返回结果
	var resp OrderRemarkSaveResponse
	if err := jsoniter.ConfigCompatibleWithStandardLibrary.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("解析修改订单卖家备注响应失败: %w", err)
	}

	return &resp, nil
}
