# 设置 Go 编译器和工具
GOCMD=go
GOBUILD=$(GOCMD) build
GOTEST=$(GOCMD) test
GOCLEAN=$(GOCMD) clean
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=yekaitai_admin
DEPLOY_ENV=prod

# golangci-lint 配置
GOLANGCI_LINT_VERSION=v1.55.2
GOLANGCI_LINT_CMD=golangci-lint

# 设置环境变量
export GO111MODULE=on
export GOPROXY=https://goproxy.cn,direct

# 设置私有仓库
export GO_PRIVATE="git.medlinker.com"
export GOPRIVATE="git.medlinker.com"
export GONOPROXY="git.medlinker.com"
export GONOSUMDB="git.medlinker.com"


.PHONY: all build test clean lint deps help

# 默认目标
all: lint test build

# 安装依赖
deps:
	@echo "Installing dependencies..."
	$(GOMOD) download
	@if ! which $(GOLANGCI_LINT_CMD) > /dev/null; then \
		echo "Installing golangci-lint..." ;\
		curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin $(GOLANGCI_LINT_VERSION) ;\
	fi

# 运行 golangci-lint 检查
lint: deps
	@echo "Running golangci-lint..."
	$(GOLANGCI_LINT_CMD) run --timeout=5m
run:
	@echo "Running..."
	go run main.go -env $(DEPLOY_ENV)
# 运行测试
test:
	@echo "Running tests..."
	$(GOTEST) -v -race -cover ./...

# 运行测试并输出覆盖率报告
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -race -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# 构建应用
build:
	@echo "Building..."
	$(GOBUILD) -o $(BINARY_NAME) -v

# 清理构建文件
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f coverage.out coverage.html

# 更新依赖
update-deps:
	@echo "Updating dependencies..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

swag:
	swag init --parseVendor --parseInternal --parseDependency --parseDepth 1 -g main.go -o internal/swagger/

# 帮助信息
help:
	@echo "Make commands:"
	@echo "  make all          - Run lint, test and build"
	@echo "  make deps         - Install dependencies"
	@echo "  make lint         - Run golangci-lint"
	@echo "  make test         - Run tests"
	@echo "  make test-coverage- Run tests with coverage report"
	@echo "  make build        - Build the application"
	@echo "  make clean        - Clean build files"
	@echo "  make update-deps  - Update dependencies"

# 帮助信息
help:
	@echo "Make commands:"
	@echo "  make all          - Run lint, test and build"
	@echo "  make deps         - Install dependencies"
	@echo "  make lint         - Run golangci-lint"
	@echo "  make test         - Run tests"
	@echo "  make test-coverage- Run tests with coverage report"
	@echo "  make build        - Build the application"
	@echo "  make clean        - Clean build files"
	@echo "  make update-deps  - Update dependencies"