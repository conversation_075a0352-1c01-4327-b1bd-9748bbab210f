# 微信小程序端患者管理功能

## 功能概述

本模块实现了微信小程序端的患者管理（就诊人管理）功能，支持用户管理自己的就诊人信息，包括创建、查询、修改、删除等操作。

## 业务规则

### 数量限制
- 每个用户最多可创建10位就诊人
- 至少需要保留1位就诊人，不可全部删除

### 排序规则
- 默认就诊人（关系为"本人"）显示在第一位
- 其他就诊人按创建时间正序排列（最新创建的在最下方）

### 删除限制
- 有未结束订单的就诊人不可删除，提示"当前就诊人有未结束的订单，不可删除"
- 只有一位就诊人时不可删除
- 删除前需要弹框确认

### 表单验证顺序
按以下顺序依次验证：
1. 患者名称 → "请先填写患者名称"
2. 性别 → "请先选择性别"
3. 出生年月 → "请选择出生年月"
4. 身份证号 → "请先填写身份证号"
5. 手机号 → "请先填写手机号"

### 年龄计算
- 根据当前日期和出生日期精确计算年月
- 支持显示：X岁Y个月、X个月、X天、新生儿等格式

## API 接口

### 1. 获取患者列表
```
GET /api/wx/patients
```

**请求参数：**
```json
{
  "page": 1,     // 页码，默认1
  "size": 10     // 每页记录数，默认10
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取患者列表成功",
  "data": {
    "list": [
      {
        "patientId": 1,
        "userId": 10,
        "name": "张三",
        "gender": 1,
        "birthDate": "1990-01-01",
        "age": {
          "years": 34,
          "months": 0,
          "text": "34岁"
        },
        "idCard": "123456789012345678",
        "ethnicity": "汉族",
        "relationshipWithUser": "本人",
        "mobile": "13800138000",
        "maritalStatus": 1,
        "medicalHistory": "",
        "allergies": "",
        "status": 1,
        "isDefault": true,
        "createdAt": "2024-01-01T10:00:00Z",
        "updatedAt": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 3,
    "page": 1,
    "size": 10,
    "canAddMore": true
  }
}
```

### 2. 获取患者详情
```
GET /api/wx/patients/:patientId
```

**路径参数：**
- `patientId`: 患者ID

**响应示例：**
```json
{
  "code": 200,
  "msg": "获取患者详情成功", 
  "data": {
    "patientId": 1,
    "userId": 10,
    "name": "张三",
    "gender": 1,
    "birthDate": "1990-01-01",
    "age": {
      "years": 34,
      "months": 0,
      "text": "34岁"
    },
    "idCard": "123456789012345678",
    "ethnicity": "汉族",
    "relationshipWithUser": "本人",
    "mobile": "13800138000",
    "maritalStatus": 1,
    "medicalHistory": "",
    "allergies": "",
    "status": 1,
    "isDefault": true,
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-01T10:00:00Z"
  }
}
```

### 3. 创建患者
```
POST /api/wx/patients
```

**请求体：**
```json
{
  "name": "李四",                    // 必填：姓名
  "gender": 2,                      // 必填：性别，1-男，2-女
  "birthDate": "1985-06-15",        // 必填：出生年月日，YYYY-MM-DD格式
  "idCard": "123456789012345679",   // 必填：身份证号
  "mobile": "13900139000",          // 必填：手机号码
  "ethnicity": "汉族",              // 可选：民族，默认汉族
  "relationshipWithUser": "配偶",   // 必填：与本人关系
  "maritalStatus": 1,               // 可选：婚姻状态，1-已婚，2-未婚
  "medicalHistory": "高血压",       // 可选：病史
  "allergies": "青霉素过敏",        // 可选：过敏史
  "isDefault": false                // 可选：是否设为默认就诊人
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "创建患者成功",
  "data": {
    "patientId": 2
  }
}
```

### 4. 更新患者信息
```
PUT /api/wx/patients/:patientId
```

**路径参数：**
- `patientId`: 患者ID

**请求体：**（所有字段都是可选的）
```json
{
  "name": "李四四",
  "gender": 2,
  "birthDate": "1985-06-15",
  "idCard": "123456789012345679",
  "ethnicity": "汉族",
  "relationshipWithUser": "配偶",
  "mobile": "13900139000",
  "maritalStatus": 1,
  "medicalHistory": "高血压",
  "allergies": "青霉素过敏",
  "isDefault": true
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "更新患者信息成功",
  "data": null
}
```

### 5. 删除患者
```
DELETE /api/wx/patients/:patientId
```

**路径参数：**
- `patientId`: 患者ID

**响应示例：**
```json
{
  "code": 200,
  "msg": "删除患者成功",
  "data": null
}
```

### 6. 设置默认就诊人
```
PUT /api/wx/patients/:patientId/default
```

**路径参数：**
- `patientId`: 患者ID

**响应示例：**
```json
{
  "code": 200,
  "msg": "设置默认就诊人成功",
  "data": null
}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 用户未登录 |
| 403 | 无权限访问 |
| 404 | 患者不存在 |
| 500 | 服务器内部错误 |

### 业务错误提示

**创建患者时：**
- "请先填写患者名称"
- "请先选择性别"
- "请选择出生年月"
- "请先填写身份证号"
- "请先填写手机号"
- "无效的手机号码格式"
- "无效的身份证号码格式"
- "最多可创建10位就诊人"

**删除患者时：**
- "当前就诊人有未结束的订单，不可删除"
- "至少需要保留一位就诊人"

## 数据字典

### 性别 (gender)
- 0: 未知
- 1: 男
- 2: 女

### 婚姻状态 (maritalStatus)
- 0: 未知
- 1: 已婚
- 2: 未婚

### 与本人关系 (relationshipWithUser)
- "本人"
- "配偶"
- "子女"
- "父母"
- "其他"

### 状态 (status)
- 0: 禁用
- 1: 正常

## 认证说明

所有API都需要微信认证，请在请求头中携带Bearer Token：

```
Authorization: Bearer <token>
```

## 数据表结构

使用 `wx_patient` 表，主要字段：
- `patient_id`: 患者ID（主键）
- `user_id`: 关联的微信用户ID
- `name`: 姓名
- `gender`: 性别
- `birth_date`: 出生年月日
- `id_card`: 身份证号
- `ethnicity`: 民族
- `relationship_with_user`: 与本人关系
- `mobile`: 手机号码
- `marital_status`: 婚姻状态
- `medical_history`: 病史
- `allergies`: 过敏史
- `status`: 状态
- `created_at`: 创建时间
- `updated_at`: 更新时间 